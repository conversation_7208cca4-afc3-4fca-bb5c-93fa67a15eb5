import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    image: {
      width: Sizes.windowWidth * 0.41,
      height: Sizes.windowWidth * 0.41,
      alignSelf: 'center',
    },
    tagView: {
      paddingHorizontal: Sizes.s,
      borderRadius: Sizes.s,
    },
    tagLabel: {
      color: colors.text2,
    },
    deliveryOfferRow: {
      flexDirection: 'row',
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
      paddingHorizontal: Sizes.xm,
    },
    priceOfferContainer: {
      flex: Sizes.x,
      justifyContent: 'center',
      paddingHorizontal: Sizes.sx,
      paddingVertical: Sizes.s,
    },
    pricingRow: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.xm,
      paddingLeft: Sizes.xm,
    },
    freeTag: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.sx,
      paddingLeft: Sizes.xm,
    },
    rowCentered: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productDetailRatingRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.xm,
    },
    detailSection: {
      flex: 1,
    },
    bestSellerTag: {
      height: Sizes.xxl,
      width: Sizes.ex,
      marginLeft: -5,
    },
    productImageContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    imageSection: {
      flex: 1,
    },
    container: {
      width: '50%',
      paddingHorizontal: Sizes.sx,
    },
    innerContainer: {
      backgroundColor: '#fff',
      flex: 1,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 0.5},
      shadowOpacity: 0.3,
      shadowRadius: 3,
      elevation: 3,
    },
    PH8: {
      paddingHorizontal: Sizes.xm,
    },
    cartButton: {
      flex: 1,
      borderColor: colors.categoryTitle,
      borderWidth: 1,

      borderRadius: 6,
    },
    moreButton: {
      width: Sizes.x3l,
      height: Sizes.x3l,
      flex: 0,
    },
    catSep: {
      borderWidth: 0.3,
      width: '100%',
      borderColor: colors.text2,
    },
    footerView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: 10,
    },
    downloadButtonIcon: {
      transform: [{rotate: '180deg'}],
    },
    modelBtn: {
      height: Sizes.x7l,
      width: '100%',
      marginTop: Sizes.xms,
      alignSelf: 'center',
      borderRadius: Sizes.xms,
      backgroundColor: 'red',
    },
    continuerDirection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
  });

export default styles;

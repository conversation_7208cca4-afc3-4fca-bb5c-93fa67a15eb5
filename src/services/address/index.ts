import Api from 'services';
import {apiEndpoints, apiRoute, subEndpoint} from 'config/apiEndpoint';
import {mapPlaceKey} from 'config/environment';
const API = new Api();

export const createCostumerAddress = async (data: any) => {
  const url = `${apiRoute.customers}${apiEndpoints.customerAddress}`;
  const resp = await API.post(url, data);
  return resp;
};

export const updateCostumerAddress = async (data: any) => {
  const url = `${apiRoute.customers}${apiEndpoints.customerAddress}/${data.id}`;
  const resp = await API.put(url, data);
  return resp;
};

export const deleteAddress = async (id: string) => {
  const url = `${apiRoute.customers}${apiEndpoints.customerAddress}/${id}`;
  const resp = await API.delete(url);
  return resp;
};
export const getAddressValidationRule = async (id: string) => {
  const url = `${apiRoute.interface}${apiEndpoints.countries}/${id}/${subEndpoint.address.validations}`;
  const resp = await API.get(url);
  return resp;
};
export const getCountries = async () => {
  const url = `${apiRoute.interface}${apiEndpoints.countries}`;
  const resp = await API.get(url);
  return resp;
};

export const checkPinCodeValid = async (pinCode: string) => {
  const url = `${apiRoute.interface}${apiEndpoints.pinCode}/${pinCode}`;
  const resp = await API.get(url);
  return resp;
};
export const getMapLocationDetails = async (lat: number, lng: number) => {
  const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${mapPlaceKey}`;
  const resp = await API.get(url);
  return resp;
};
export const checkValidateGst = async (data: any) => {
  const url = `${apiRoute.customerAddress}${subEndpoint.address.validateGST}`;
  const resp = await API.post(url, data);
  return resp;
};

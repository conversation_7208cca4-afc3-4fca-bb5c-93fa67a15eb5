import React, {useEffect} from 'react';
import {TouchableOpacity, View, BackHandler} from 'react-native';
import {Label, Spacer, GradientText} from 'components/atoms';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {SafeAreaView} from 'react-native-safe-area-context';
import stylesWithOutColor from './style';
import {RouteProp, useTheme} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {Button} from 'components/molecules';
import {t} from 'i18next';
import {useMemo} from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@types/local';
import { AnalyticsEvents } from 'components/organisms';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList>;
};

const ThankYouPage = ({navigation, route}: Props) => {
  const {colors} = useTheme();
  const {order_id} = route.params;
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  useEffect(() => {
    const backAction = () => {
      navigation.navigate('HomePage');
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  useEffect(() => {
    AnalyticsEvents('THANK_YOU', 'Thank you page viewed', {}, userInfo, isLoggedIn);
  }, []);
  return (
    <SafeAreaView style={styles.mainView}>
      <View style={styles.view}>
        <FastImage source={Icons.thankYou} style={styles.image} />
        <Spacer size="xm" />
        <View style={styles.section}>
          <GradientText
            linearColor={[colors.cornflowerBlue1, colors.lavenderRose]}
            style={styles.titleStyle}>
            {t('orderListing.thankYouOrder')}
          </GradientText>
          <Spacer size="s" />

          <TouchableOpacity
            style={styles.labelView}
            onPress={() => navigation.navigate('OrderDetail', {order_id})}>
            <Label
              text={t('orderListing.orderId')}
              align="center"
              weight="500"
              size="mx"
              color="skyBlue17"
            />
            <Label
              align="center"
              fontFamily="Medium"
              size="mx"
              color="skyBlue17"
              text={`#${order_id}`}
            />
          </TouchableOpacity>
          <Spacer size="xm" />
          <Button
            onPress={() => navigation.reset({
              index: 0,
              routes: [
                {
                  name: 'Tab',
                  state: {
                    routes: [{ name: 'Home' }],
                  },
                },
              ],
            })} 
            style={styles.button}
            labelColor="categoryTitle"
            labelSize="mx"
            weight="500"
            text={t('buttons.continueShopping')}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ThankYouPage;

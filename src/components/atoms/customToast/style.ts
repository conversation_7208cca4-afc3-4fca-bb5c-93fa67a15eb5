import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      paddingLeft: Sizes.m,
      paddingRight: Sizes.s,
      maxWidth: '75%',
      width: '100%',
      alignItems: 'center',
      borderRadius: Sizes.x5l,
      marginBottom: Sizes.xm,
      paddingVertical: Sizes.sx,
    },
    subContainer: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.s,
      paddingVertical: Sizes.sx,
      // maxWidth: '90%',
      // width: '100%',
      alignItems: 'center',
      borderRadius: Sizes.x5l,
    },
    button: {paddingVertical: Sizes.xs},
    iconStyle: {width: Sizes.l, height: Sizes.l},
    imageStyle: {
      width: Sizes.xx4l,
      height: Sizes.xx4l,
      borderRadius: Sizes.x6l,
    },
    mainLabel: {maxWidth: '70%'},
    labelView: {
      flex: Sizes.x,
      marginHorizontal: Sizes.xm,
    },
    underPrizeView: {
      flexDirection: 'row',
    },
    underPrizeLastView: {
      height: Sizes.xx4l,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.x6l,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    image1: {
      zIndex: -Sizes.xs + Sizes.x,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.x6l,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      width: Sizes.xx4l,
      height: Sizes.xx4l,
    },
    image2: {
      zIndex: -Sizes.x,
      backgroundColor: colors.whiteColor,
      marginLeft: -Sizes.x4l,
      borderRadius: Sizes.x6l,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      width: Sizes.xx4l,
      height: Sizes.xx4l,
    },
    image3: {
      marginLeft: -Sizes.x4l,
      zIndex: -Sizes.x,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.x6l,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    labelStyle: {
      marginBottom: Sizes.s,
    },
    closeButton: {
      height: Sizes.xx4l,
      width: Sizes.xx4l,
      borderRadius: Sizes.x52,
      padding: Sizes.xms,
    },
    crossStyle: {
      width: Sizes.l,
      height: Sizes.l,
      tintColor: colors.whiteColor,
    },
    cartView: {
      marginHorizontal: Sizes.xm,
    },
  });

export default styles;

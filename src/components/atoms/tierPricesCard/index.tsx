import React from 'react';
import {View, TouchableOpacity, FlatList} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {useMemo} from 'react';

type Props = {
  transformedTierPrices: string[];
  totalTierAmount: number;
  tierPriceIndex: number;
  totalOriginalAmount: number;
  totalItemCount: number;
  selectTierPrice: (price: number) => void;
};

const TierPricesCard = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {
    transformedTierPrices,
    totalTierAmount,
    tierPriceIndex,
    totalOriginalAmount,
    totalItemCount,
    selectTierPrice,
  } = props;

  const findFirstActiveIndex = (object: any) => {
    let index = Object.keys(object).find(key =>
      object[key]?.rows?.some((i: {active: boolean}) => i.active === true),
    );
    return index;
  };

  return (
    <>
      {transformedTierPrices && transformedTierPrices?.length > 0 ? (
        <View style={styles.priceView}>
          <View style={styles.transformedTierPriceView}>
            <View style={styles.transformedTierView}>
              <Label
                text={t('PDP.subtotal')}
                color="text2"
                size="l"
                fontFamily="Medium">
                <Label
                  text={`₹${totalTierAmount.toFixed(2)}`}
                  color="text"
                  size="l"
                  fontFamily="SemiBold"
                />
              </Label>
            </View>
            <Spacer size="s" />
            <View style={styles.fRow}>
              <Label
                text={`${t('PDP.mrp')} - ₹${totalOriginalAmount}`}
                color="baliHaiLightGray"
                size="mx"
                fontFamily="Medium">
                <Label
                  text={`   ${
                    isNaN(
                      (
                        100 -
                        (totalTierAmount * 100) / totalOriginalAmount
                      ).toFixed(2),
                    )
                      ? `0% ${t('PDP.off1')}`
                      : (
                          100 -
                          (totalTierAmount * 100) / totalOriginalAmount
                        ).toFixed(2) + `% ${t('PDP.off1')}`
                  }`}
                  color="green2"
                  size="mx"
                  fontFamily="Medium"
                />
              </Label>
              <Spacer size="xm" type="Horizontal" />
              <TouchableOpacity>
                <ImageIcon size="xl" icon="infoCircle" />
              </TouchableOpacity>
            </View>
            <Spacer size="s" />
            <Label
              fontFamily="SemiBold"
              color="categoryTitle"
              size="mx"
              text={`${t('PDP.item')} - ${totalItemCount}`}
            />
            <Spacer size="s" />
            <View>
              {transformedTierPrices &&
              Object.keys(transformedTierPrices)?.length > 0 ? (
                <>
                  <Spacer size="s" />
                  <FlatList
                    data={Object.keys(transformedTierPrices)}
                    horizontal
                    ItemSeparatorComponent={() => (
                      <View style={styles.separator} />
                    )}
                    renderItem={({item}) => {
                      return transformedTierPrices?.[item]?.rows?.some(
                        j => j.active === true,
                      ) ? (
                        <TouchableOpacity
                          onPress={() => {
                            selectTierPrice(Number(item));
                          }}>
                          <View
                            style={[
                              styles.tierPrices,
                              tierPriceIndex === item ||
                              (findFirstActiveIndex(transformedTierPrices) ===
                                item &&
                                !tierPriceIndex)
                                ? {
                                    backgroundColor: colors.categoryTitle,
                                  }
                                : {},
                            ]}>
                            <Label
                              fontFamily="SemiBold"
                              size="mx"
                              color={
                                tierPriceIndex === item ||
                                (findFirstActiveIndex(transformedTierPrices) ===
                                  item &&
                                  !tierPriceIndex)
                                  ? 'whiteColor'
                                  : 'text'
                              }
                              text={item}
                            />
                          </View>
                        </TouchableOpacity>
                      ) : null;
                    }}
                  />
                </>
              ) : null}
              <View style={styles.listContainer}>
                <View style={styles.deliveryCont}>
                  <View style={styles.thLeft}>
                    <Label
                      color="whiteColor"
                      size="mx"
                      text={t('PDP.offer')}
                      fontFamily="SemiBold"
                    />
                  </View>
                  <View style={styles.thRight}>
                    <Label
                      color="whiteColor"
                      size="mx"
                      text={t('PDP.addSavings')}
                      fontFamily="SemiBold"
                    />
                  </View>
                </View>
                <View>
                  {transformedTierPrices &&
                    Object.keys(transformedTierPrices)?.length > 0 &&
                    transformedTierPrices?.[
                      tierPriceIndex ||
                        findFirstActiveIndex(transformedTierPrices)
                    ]?.rows?.map((item: any, index: number) => {
                      return (
                        <View style={styles.fRow} key={index}>
                          <View
                            style={[
                              styles.tableItemLeft,
                              item.qty >= item.qtyLimit
                                ? {backgroundColor: colors.caramel}
                                : {},
                            ]}>
                            <Label
                              color="blackColor"
                              size="mx"
                              fontFamily="Medium"
                              text={item.text}
                            />
                          </View>
                          <View
                            style={[
                              styles.tableItemRight,
                              item?.qty >= item?.qtyLimit
                                ? {backgroundColor: colors.caramel}
                                : {},
                            ]}>
                            <Label
                              color="blackColor"
                              size="mx"
                              fontFamily="Medium"
                              text={`${item.savings}%`}
                            />
                          </View>
                        </View>
                      );
                    })}
                </View>
              </View>
            </View>
          </View>
        </View>
      ) : null}
    </>
  );
};

export default TierPricesCard;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    body: {
      width: '100%',
      padding: Sizes.l,
      backgroundColor: colors.background,
      position: 'absolute',
      bottom: 0,
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
    },
    closeContainer: {
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      width: Sizes.xxl,
      height: Sizes.xxl,
      borderRadius: Sizes.m,
      position: 'absolute',
      zIndex: Sizes.m,
      alignSelf: 'center',
    },
  });

export default styles;

import React from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import FastImage from 'react-native-fast-image';
import {Button} from 'components/molecules';
import Spacer from '../spacer';
import Label from '../label';

export type Props = {
  title?: string;
  text?: string;
  bgImg?: string;
  imageName?: string;
  onPress?: () => void;
  btnShow?: boolean;
};

const EmptyView = ({title, text, bgImg, imageName, onPress}: Props) => {
  const {colors} = useTheme();
  const styles = stylesWithOutColor(colors);

  return (
    <View style={styles.wrapper}>
      <View style={styles.imageView}>
        <FastImage resizeMode="contain" style={styles.bgImage} source={bgImg} />
        <FastImage
          resizeMode="contain"
          style={styles.emptyImage}
          source={imageName}
        />
      </View>
      {title && (
        <>
          <Spacer size="x6l" />
          <Label
            text={title}
            size="l"
            color="text"
            fontFamily="Medium"
            align="center"
            textTransform="capitalize"
            style={styles.titleStyle}
          />
        </>
      )}
      {text && (
        <>
          <Spacer size="l" />
          <Button
            onPress={() => onPress?.()}
            style={styles.btnStyle}
            radius="m"
            type="secondary"
            labelSize="mx"
            labelStyle={styles.btnTxt}
            labelColor="whiteColor"
            text={text}
          />
        </>
      )}
    </View>
  );
};

export default EmptyView;

import {
  View,
  TouchableOpacity,
  Modal,
  Dimensions,
  FlatList,
  ViewProps,
  ImageProps,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  ImageIcon,
  Label,
  ReanimatedCarouselPagination,
  Separator,
  Spacer,
} from 'components/atoms';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import useStyle from './style';
import {useTheme} from '@react-navigation/native';
import {FullSizeProductImagesModal} from 'components/organisms';
import {addToWishListThunk, removeFromWishlist} from 'app-redux-store/slice/appSlice';
import {useDispatch, useSelector} from 'react-redux';
import Icons from 'common/icons';
import {showInfoMessage} from 'utils/show_messages';
import YoutubePlayer from 'react-native-youtube-iframe';
import {t} from 'i18next';
import { debugLog } from 'utils/debugLog';
import { debounce } from 'utils/utils';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';
import { appsFlyerEvent } from 'components/organisms/analytics-Events/appsFlyerEvent';

type Props = {
  scrollToSpecificOffset: () => void;
  rating?: number;
  share: boolean;
  reviewsCount: number;
  openSimilar: () => void;
  productImages: Array<MediaGalleryEntry>;
  showSimilar: boolean;
  productData: string;
  navigation: string;
  tagData: string;
  openShare: () => void;
};
const ProductImageSlider = ({
  scrollToSpecificOffset,
  rating,
  share,
  openSimilar,
  reviewsCount,
  showSimilar,
  productImages,
  productData,
  navigation,
  tagData,
  openShare,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => useStyle(colors), [colors]);
  const dispatch = useDispatch();
  const [carouselIndex, setCarouselIndex] = useState(0);
  const [visibleFullScreenImg, setVisibleFullScreenImg] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const {width: screenWidth} = Dimensions.get('window');

  const {isLoggedIn, wishlists} = useSelector(
    (state: RootState) => state.app,
  );
  const isInWishlist = useMemo(() => {
    // Check if product is in any wishlist
    if(wishlists) {
      return Object.values(wishlists).some(wishlist => 
        wishlist?.productIds?.includes( productData?.product_id )
      );
    }
    return false;
  }, [
    wishlists, productData?.product_id
   ]);
  const [likedProductId, setLikeProductId] = useState('');

  const debouncedWishlistPress = useCallback(
    debounce((productId, productImages) => {
      setLikeProductId(productId);
      if (isLoggedIn) {
        if (isInWishlist) {
          dispatch(removeFromWishlist({productId}));
        } else {
          dispatch(addToWishListThunk({
            productId,
            image: productImages,
          }));
          trackEvent('ADD_TO_WISHLIST', {
            contentId: productId,
            contentType: 'product',
            productImages: productImages
            });
          // appsFlyer Add to Wishlist Event
          appsFlyerEvent('AddToWishlist', {
            price: productData?.pricing?.selling_price,
            productId: productId,
            category: productData?.name,
            currency: 'INR',
          });
        }
      } else {
        if (navigation) {
          showInfoMessage(t('toastMassages.wishlistLogin'));
          const prevRoutes = navigation.getState().routes;
          navigation.navigate('Login', {
            nextRouterState: {
              index: 0,
              routes: prevRoutes,
            },
          });
        } else {
          debugLog('please provide navigation.');
        }
      }
    }, 350),
    [isLoggedIn, navigation, dispatch, wishlists, isInWishlist],
  );
  
  // Then create the handler function that calls the debounced function
  const onPressWishlist = useCallback(
    (productId, productImages) => {
      debouncedWishlistPress(productId, productImages);
    },
    [debouncedWishlistPress],
  );
  const pID = productData?.product_id || likedProductId;
  // const isLiked = wishlistItems.filter(item => item?.productId == pID);
  // const isMatch = isLiked?.length > 0;

  const getYouTubeVideoId = useCallback((url: string) => {
    if (!url) {
      return null;
    }
    const pattern =
      /(?:youtu\.be\/|youtube\.com(?:\/embed\/|\/v\/|\/watch\?v=|\/\?v=|.*[?&]v=))([^?&"'>]+)/;
    const match = url.match(pattern);
    return match ? match[1] : null;
  }, []);

  const {width, height} = Dimensions.get('window');

  const [isFullScreen, setIsFullScreen] = useState(false);
  const [fullScreenVideoId, setFullScreenVideoId] = useState<string | null>(
    null,
  );
  const playerRef = useRef(null);
  const fullScreenPlayerRef = useRef(null);

  const openFullScreen = useCallback((videoId: string | null) => {
    setFullScreenVideoId(videoId);
    setIsFullScreen(true);
  }, []);

  const closeFullScreen = useCallback(() => {
    setIsFullScreen(false);
    setFullScreenVideoId(null);
  }, []);

  const handleCarouselItemPress = useCallback(
    (isVideo: boolean, videoId: string | null) => {
      if (isVideo) {
        openFullScreen(videoId);
      } else {
        setVisibleFullScreenImg(!visibleFullScreenImg);
      }
    },
    [openFullScreen, visibleFullScreenImg],
  );

  const handleVideoStateChange = useCallback(
    (event: string, videoId: string | null) => {
      if (event === 'playing') {
        openFullScreen(videoId);
      }
    },
    [openFullScreen],
  );

  const handleScroll = useCallback(
    (event: any) => {
      const contentOffsetX = event.nativeEvent.contentOffset.x;
      const newIndex = Math.round(contentOffsetX / screenWidth);
      if (newIndex !== carouselIndex) {
        debugLog(`Scrolled to index ${newIndex}`);
        setCarouselIndex(newIndex);
      }
    },
    [carouselIndex, screenWidth],
  );

  const scrollToIndex = useCallback((index: number) => {
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex({
        index,
        animated: true,
      });
      setCarouselIndex(index);
    }
  }, []);

  const productImageContainerViewStyle: ViewProps['style'] = useMemo(() => {
    return {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    };
  }, []);

  const keyExtractor = useCallback((item: MediaGalleryEntry, index: number) => {
    return `product-image-${index}`;
  }, []);

  const itemContainerStyle = useMemo(() => {
    return {
      width: screenWidth,
      height: screenWidth,
      backgroundColor: colors.background,
    };
  }, [colors.background, screenWidth]);

  const getItemLayout = useCallback(
    (data: any, index: number) => {
      return {
        length: screenWidth,
        offset: screenWidth * index,
        index,
      };
    },
    [screenWidth],
  );

  const flatListStyle = useMemo(() => {
    return {
      width: screenWidth,
      height: screenWidth,
    };
  }, [screenWidth]);

  const renderCustomCarousel = useCallback(() => {
    if (!productImages || productImages.length === 0) {
      return null;
    }

    const renderItem = ({
      item,
      index,
    }: {
      item: MediaGalleryEntry;
      index: number;
    }) => {
      const isVideo = item?.media_type === 'external-video';
      const videoId = getYouTubeVideoId(item?.video_content?.video_url);
      const imageUrl = getImageUrl(item?.file);

      return (
        <TouchableOpacity
          key={`carousel-item-${index}`}
          activeOpacity={0.8}
          style={itemContainerStyle}
          onPress={() => {
            handleCarouselItemPress(isVideo, videoId);
          }}>
          {isVideo ? (
            <View style={styles.videoContainer}>
              <YoutubePlayer
                ref={playerRef}
                height={height}
                play={false}
                videoId={videoId}
                onChangeState={event => handleVideoStateChange(event, videoId)}
                webViewStyle={{opacity: 0.99}}
              />
            </View>
          ) : (
            <View style={productImageContainerViewStyle}>
             {item?.file ? (
              <FastImage
                key={`image-${index}`}
                style={styles.visibleSubScreenView}
                resizeMode="cover"
                source={{
                  uri: getImageUrl(item.file),
                  priority: FastImage.priority.high,
                  cache: FastImage.cacheControl.immutable,
                }}
              />
            ) : (
              <View style={styles.placeholderWrapper}>
                <FastImage
                  key={`placeholder-${index}`}
                  style={styles.placeholderImage}
                  resizeMode="contain"
                  source={Icons.defaultImage}
                />
              </View>
            )}
            </View>
          )}
        </TouchableOpacity>
      );
    };

    return (
      <View>
        <FlatList
          ref={flatListRef}
          data={productImages}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          initialNumToRender={2}
          maxToRenderPerBatch={3}
          windowSize={5}
          getItemLayout={getItemLayout}
          style={flatListStyle}
          removeClippedSubviews={true}
          bounces={false}
        />

        <View style={styles.carouselContainer}>
          <Spacer size="xm" />
          <View style={styles.paginationContainer}>
            <ReanimatedCarouselPagination
              activeIndex={carouselIndex}
              dotsLength={productImages.length}
              paginationType={'normal'}
              progress={carouselIndex}
              pdpSliderStyle
              pdpImageSliderDots
            />
          </View>
        </View>
      </View>
    );
  }, [
    productImages,
    styles.carouselContainer,
    styles.paginationContainer,
    styles.videoContainer,
    styles.visibleSubScreenView,
    keyExtractor,
    handleScroll,
    getItemLayout,
    flatListStyle,
    getYouTubeVideoId,
    itemContainerStyle,
    height,
    productImageContainerViewStyle,
    handleCarouselItemPress,
    handleVideoStateChange,
    carouselIndex,
  ]);

  const tagImages = useMemo(() => {
    return (
      tagData?.tags?.map(tag => ({
        uri: tag.image,
      })) || []
    );
  }, [tagData]);

  useEffect(() => {
    if (!productImages) {
      return;
    }
    const firstTwoImages = productImages.slice(0, 2);
    FastImage.preload(
      firstTwoImages.map(item => ({
        uri: getImageUrl(item?.file),
        priority: FastImage.priority.high,
      })),
    );
  }, [productImages]);

  const handleWishlistPress = useCallback(() => {
    onPressWishlist(productData.product_id, productImages[carouselIndex]?.file);
  }, [onPressWishlist, productData, productImages, carouselIndex]);

  const onYoutubePlayerStateChange = useCallback(
    (event: string, _videoId: string | null) => {
      if (event === 'ended') {
        closeFullScreen();
      }
    },
    [closeFullScreen],
  );

  const webViewStyle = useMemo(() => {
    return {
      top: height / 3,
      opacity: 0.99,
    };
  }, [height]);

  const supportedOrientations = useMemo(() => {
    return ['landscape', 'portrait'];
  }, []);

  return (
    <>
      <View>
        <View style={styles.fullScreenImgView}>
          {tagImages.length > 0 ? (
            tagImages.map(data => {
              return (
                <FastImage
                  style={styles.bestSellerTag}
                  resizeMode="stretch"
                  source={{
                    uri: data?.image,
                  }}
                />
              );
            })
          ) : (
            <View />
          )}

          <View style={styles.iconView}>
            <TouchableOpacity
              style={styles.imageIcon}
              onPress={handleWishlistPress}>
              <ImageIcon
                icon={isInWishlist ? 'heartFilled' : 'heart'}
                size="xxxl"
                tintColor={isInWishlist ? 'red1' : 'text'}
              />
            </TouchableOpacity>
            {share && (
              <>
                <Spacer size="m" />
                <TouchableOpacity onPress={openShare} style={styles.imageIcon}>
                  <ImageIcon size="xxl" icon="newIconShare" />
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>

        <View style={styles.deliveryView}>
          {showSimilar && (
            <View style={styles.simillerProductView}>
              <TouchableOpacity
                onPress={openSimilar}
                style={styles.simillerProductSubView}>
                <FastImage
                  source={Icons.newSimilarGif}
                  style={styles.similartImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            </View>
          )}
          {rating !== undefined && rating > 0 && (
            <View style={styles.ratingMainView}>
              <TouchableOpacity
                onPress={scrollToSpecificOffset}
                style={styles.scrollToView}>
                <View style={styles.scrollToSubView}>
                  <View style={styles.scrollToMainView}>
                    <Label
                      text={Number(rating || 0).toFixed(1)}
                      weight="400"
                      size="mx"
                      color="text"
                    />
                    <Spacer size="xs" type="Horizontal" />
                    <FastImage
                      resizeMode="contain"
                      source={Icons.ratingGif}
                      style={styles.ratingIcon}
                    />
                  </View>
                  <Spacer size="sx" type="Horizontal" />
                  <Separator
                    thickness="x"
                    color="grey2"
                    Vertical={true}
                    height="xx"
                  />
                  <Spacer size="sx" type="Horizontal" />
                  <View>
                    <Label
                      text={reviewsCount}
                      weight="400"
                      size="mx"
                      color="text"
                    />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {renderCustomCarousel()}
      </View>
      <FullSizeProductImagesModal
        visible={visibleFullScreenImg}
        onClose={setVisibleFullScreenImg}
        productImages={productImages}
        carouselIndex={carouselIndex}
        setCarouselIndex={setCarouselIndex}
      />
      <Modal
        visible={isFullScreen}
        onRequestClose={closeFullScreen}
        animationType="slide"
        supportedOrientations={supportedOrientations}
        transparent={true}>
        <View style={styles.fullScreenModalContainer}>
          <YoutubePlayer
            height={height}
            width={width}
            webViewStyle={webViewStyle}
            play={true}
            videoId={fullScreenVideoId}
            ref={playerRef}
            onChangeState={onYoutubePlayerStateChange}
          />
          <TouchableOpacity
            onPress={closeFullScreen}
            style={styles.closeButton}>
            <ImageIcon size="xxxl" icon="closeWhiteRound" />
          </TouchableOpacity>
        </View>
      </Modal>
    </>
  );
};

export default React.memo(ProductImageSlider);

import {Link} from 'components/atoms';
import React, {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';

type Props = {
  resendOtpApi: () => Promise<boolean>;
};
const CountDown = ({resendOtpApi}: Props) => {
  const [min, setMin] = useState(2);
  const [sec, setSec] = useState(0);
  const [resend, setResend] = useState(false);
  const {t} = useTranslation();

  const resendOtp = useCallback(async () => {
    const data = await resendOtpApi();
    if (data) {
      setMin(2);
      setSec(0);
      setResend(false);
    }
  }, [resendOtpApi]);

  useEffect(() => {
    var interval: NodeJS.Timeout | string | number | undefined;
    if (!resend) {
      interval = setInterval(() => {
        if (sec > 0) {
          setSec(sec - 1);
        } else {
          setSec(59);
          setMin(min - 1);
        }
      }, 1000);
      if (min === 0 && sec === 0) {
        clearInterval(interval);
        setResend(true);
      }
    }
    return () => clearInterval(interval);
  }, [min, resend, sec]);
  return (
    <Link
      text={t('otp.didNotReceiveCode', {
        min: String(min).padStart(2, '0'),
        sec: String(sec).padStart(2, '0'),
      })}
      size="m"
      color="textSecondary"
      onPress={() => {
        return resend ? resendOtp() : () => {};
      }}
    />
  );
};
export default CountDown;

import { Settings } from 'react-native-fbsdk-next';
import {
    logAddToCart,
    logAddToWishlist,
    logCompleteRegistration,
    logInitiateCheckout,
    logPurchase,
    logSearch,
    logViewContent,
  } from './FacebookAnalytics'; // Update path if needed
  
  type EventParams = Record<string, any>;
  
  export type FacebookEventName =
    | 'ADD_TO_CART'
    | 'ADD_TO_WISHLIST'
    | 'COMPLETE_REGISTRATION'
    | 'INITIATE_CHECKOUT'
    | 'PURCHASE'
    | 'SEARCH'
    | 'VIEW_CONTENT';
  
  /**
   * Unified event tracker for Facebook App Events
   */
  export const trackEvent = (
    eventName: FacebookEventName,
    data: EventParams = {}
  ): void => {
    
    Settings.setAutoLogAppEventsEnabled(true);
    Settings.setAdvertiserIDCollectionEnabled(true);

    switch (eventName) {
      case 'ADD_TO_CART':
        logAddToCart(
          data?.contentId,
          data?.contentType,
          data?.currency,
          data?.value,
          data?.params
        );
        break;
  
      case 'ADD_TO_WISHLIST':
        logAddToWishlist(
          data?.contentId,
          data?.contentType,
          data?.currency,
          data?.value,
          data?.params
        );
        break;
  
      case 'COMPLETE_REGISTRATION':
        logCompleteRegistration(
            data?.registrationId, 
            data?.params
        );
        break;
  
      case 'INITIATE_CHECKOUT':
        logInitiateCheckout(
          // data?.contentIds,
          // data?.contentType,
          // data?.currency,
          // data?.value,
          data?.params
        );
        break;
  
      case 'PURCHASE':
        logPurchase(
          data?.amount, 
          data?.currency, 
          data?.params
        );
        break;
  
      case 'SEARCH':
        logSearch(
          data?.searchString, 
          data?.params
        );
        break;
  
      case 'VIEW_CONTENT':
        logViewContent(
          data?.contentId,
          data?.contentType,
          data?.currency,
          data?.value,
          data?.params
        );
        break;
  
      default:
        console.warn(`[trackEvent] Unrecognized event: ${eventName}`, data);
    }
  };
  
import React, {memo, useCallback, useMemo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Label} from 'components/atoms';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {AnalyticsEvents} from 'components/organisms';
import {useSelector} from 'react-redux';

type Props = {
  index?: number;
  item?: SectionElement | string;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  filter?: boolean;
  selectedCategory?: SectionElement;
  onSelect?: (item: SectionElement) => void;
  onClear?: () => void;
};

const CategoriesItem = (props: Props) => {
  const {index, item, navigation, filter, selectedCategory, onSelect, onClear} =
    props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);

  const handlePress = useCallback(() => {
    if (filter) {
      onSelect?.(item as SectionElement);
    } else {
      const categoryData = {
        'Category Id': item?.landing_page_entity?.category_id,
        'Category Name': item?.title,
      };
      AnalyticsEvents(
        'CATEGORY_VIEW',
        'Category viewed',
        categoryData,
        userInfo,
        isLoggedIn,
      );

      navigation?.navigate(item === 'All' ? 'Categories' : 'CategoryDetail', {
        categoryId: item?.landing_page_entity?.category_id,
      });
    }
  }, [filter, item, navigation, onSelect, userInfo, isLoggedIn]);

  const handleClearPress = () => {
    onClear?.();
  };

  return (
    <TouchableOpacity onPress={handlePress} style={styles.categorySubView}>
      {item === 'All' ? (
        <View style={styles.viewAllContainer}>
          <View style={styles.viewAllView}>
            <ImageIcon style={styles.allView} icon="viewAll" />
          </View>
          <Label
            color="categoryTitle"
            size="xms"
            fontFamily="SemiBold"
            text={t('homePage.viewAll')}
            align="center"
          />
        </View>
      ) : (
        <View style={styles.viewAllContainer}>
          <ImageIcon
            source={item?.media?.mobile_image}
            sourceType="url"
            style={styles.categoryTitleView}
            resizeMode="cover"
          />
          {filter && item?.title === selectedCategory?.title && (
            <TouchableOpacity onPress={handleClearPress}>
              <ImageIcon
                size="xxxl"
                icon="closeWhiteRound"
                style={styles.closeIcon}
              />
            </TouchableOpacity>
          )}
          <Label
            color="categoryTitle"
            size="xms"
            fontFamily="SemiBold"
            text={item?.mobile_title || item?.title}
            align="center"
            textTransform="capitalize"
          />
        </View>
      )}
    </TouchableOpacity>
  );
};

export default memo(CategoriesItem);

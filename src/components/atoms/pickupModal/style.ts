import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalView: {
      borderRadius: Sizes.xms,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x70,
    },
    removeModalView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xms,
      padding: Sizes.xm,
    },
    removeModalSubView: {
      paddingVertical: Sizes.xx,
      paddingHorizontal: Sizes.m,
      alignSelf: 'center',
    },
    input: {
      height: Sizes.x6l,
      borderColor: colors.background,
      borderWidth: Sizes.x,
      borderRadius: Sizes.s,
      paddingHorizontal: Sizes.xms,
      textAlign: 'center',
    },
    invalidInput: {
      borderColor: 'red',
    },
  });

export default styles;

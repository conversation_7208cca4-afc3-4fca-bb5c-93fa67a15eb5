import AsyncStorage from '@react-native-async-storage/async-storage';
import {getCountry} from './country';
import tokenClass from './token';
import {Platform} from 'react-native';
import {Version} from 'config/environment';

const getRequest = async (url, api_key = '') => {
  const token = await AsyncStorage.getItem('token');
  return fetch(url, {
    method: 'GET',
    credentials: 'include',
    headers: new Headers({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
      'x-api-key': api_key,
      version: Version,
    }),
  });
};
export const _getRequest = url => {
  return fetch(url);
};

export const postRequest = async (url, params, headers = {}) => {
  const country = await getCountry();
  let token = await tokenClass.getToken();
  return fetch(url, {
    method: 'POST',
    headers: new Headers({
      ...headers,
      'Content-Type': 'application/json',
      Authorization: `Bear<PERSON> ${token}`,
      platform: Platform.OS,
      version: Version,
      currency: country?.currency_code,
    }),
    credentials: 'include',
    body: JSON.stringify(params),
  });
};

export const getDeepLinkData = async (
  onelinkId: string,
  deeplinkId: string,
) => {
  return fetch(
    `https://onelink.appsflyer.com/shortlink/v1/${onelinkId}?id=${deeplinkId}`,
    {
      method: 'GET',
      credentials: 'include',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: '1b3u1l4h00169000034RFlrAAG1s6h3a2t',
      }),
    },
  );
};

export default getRequest;

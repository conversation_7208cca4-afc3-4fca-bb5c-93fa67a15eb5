import React, {useCallback, useMemo, useRef, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../../routes';
import {Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {ImageIcon, Label, LikeShare, Link, Spacer} from 'components/atoms';
import {RouteProp, useTheme} from '@react-navigation/native';
import {ScrollView, View, Dimensions, TouchableOpacity} from 'react-native';
import CarouselStack from 'react-native-reanimated-carousel';
import ModalComponent from 'components/atoms/modalComponent';
import {showInfoMessage, showSuccessMessage} from 'utils/show_messages';
import tokenClass from 'utils/token';
import {bookmarkNews} from 'api';
import {t} from 'i18next';
import NewsWebModal from 'components/molecules/newswebmodal';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'NewsDetails'>;
};

const NewsDetails = ({navigation, route}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {item, currentItem} = route.params;
  const width = Dimensions.get('window').width;
  const [activeIndex, setActiveIndex] = useState(0);
  const carouselRef = useRef(null);
  const [ShowModal, setShowModal] = useState(false);
  const [showNewsLink, setShowNewsLink] = useState<null | string>(null);
  const [showNews, setShowNews] = useState(false);
  const [bookmarkSave, setBookmarkSave] = useState<boolean>(true);

  const initialScrollIndex = useMemo(() => {
    setActiveIndex(item.findIndex(i => i.id === currentItem?.id) || 0);
    return item.findIndex(i => i.id === currentItem.id) || 0;
  }, [item, currentItem]);

  const onSavePress = useCallback(async () => {
    if (await tokenClass.loginStatus()) {
      const {data} = await bookmarkNews({id: item[activeIndex].id});
      if (data.bookmarkNews) {
        setBookmarkSave(true);
        showSuccessMessage(t('toastMassages.saveSuccess'));
      }
    } else {
      showInfoMessage(t('toastMassages.loginInfo'));
      navigation.navigate('Login', {
        nextScreenName: 'News',
        nextScreenParams: {
          screen: 'NewsDetails',
          currentNews: item[activeIndex],
        },
      });
    }
  }, [activeIndex, item, navigation]);
  const renderItem = useCallback(
    ({item, index}: {item: News; index: number}) => {
      return (
        <ScrollView showsVerticalScrollIndicator={false} key={index}>
          <ImageIcon
            resizeMode="contain"
            style={styles.image}
            sourceType="url"
            source={item?.image}
          />
          <View style={styles.continuerBg}>
            <Label style={styles.title} text={item?.title} />
            <Spacer type="Vertical" size="s" />
            <Label style={styles.dateText} text={item?.date} />
            <Spacer type="Vertical" size="m" />
            <Label
              text={
                item?.content.length > 400
                  ? String(item.content).substring(90) + '...'
                  : item.content
              }
            />

            <Spacer type="Vertical" size="m" />
            <Link
              onPress={() => {
                setShowNews(true);
                setShowNewsLink(item?.source_link);
              }}
              style={styles.link}
              text={item?.source_link}
            />
          </View>
        </ScrollView>
      );
    },
    [],
  );
  return (
    <SafeAreaView style={styles.container}>
      <Header
        backButton
        navigation={navigation}
        likes
        newsId={item[activeIndex]?.id}
        extraItems={() => (
          <LikeShare
            isSaved={false}
            onSavePress={onSavePress}
            likeText={t('newsDetails.like')}
            shareText={t('wishList.shareText')}
            saveText={t('profileDetails.save')}
            news={item[activeIndex]}
            navigation={navigation}
          />
        )}
      />
      <CarouselStack
        loop
        ref={carouselRef}
        pagingEnabled
        width={width}
        height={width * 2}
        vertical
        defaultIndex={initialScrollIndex}
        onSnapToItem={setActiveIndex}
        currentIndex={activeIndex || initialScrollIndex}
        data={item}
        scrollAnimationDuration={1000}
        renderItem={renderItem}
      />
      {ShowModal ? (
        <ModalComponent
          modelStyle={[styles.modelView]}
          visible={ShowModal}
          onClose={() => setShowModal(false)}>
          <TouchableOpacity
            onPress={() => {
              setShowModal(false);
            }}
            style={styles.closeButton}>
            <ImageIcon tintColor="blackIcons" icon="closeIcons" />
          </TouchableOpacity>
          {renderItem({item: item[activeIndex], index: activeIndex})}
        </ModalComponent>
      ) : null}
      {showNewsLink && (
        <NewsWebModal
          IsVisible={showNews}
          onCloseMethod={() => {
            setShowNewsLink(null);
          }}
          ButtonPress={() => {
            setShowNews(false);
          }}
          NewsLink={showNewsLink}
        />
      )}
    </SafeAreaView>
  );
};

export default NewsDetails;

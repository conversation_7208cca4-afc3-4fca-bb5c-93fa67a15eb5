import React, {useCallback} from 'react';
import {ImageProps, TouchableOpacity, View, ViewProps} from 'react-native';
import Label from 'components/atoms/label';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import Tag from '../tag';
import getImageUrl, {productDummyImage} from 'utils/imageUrlHelper';
import {Button} from 'components/molecules';
import Spacer from '../spacer';
import {useTheme} from '@react-navigation/native';
import Ratings from '../ratings';
import FastImagesItem from '../fastImages';
import FastImage from 'react-native-fast-image';
import {useSelector} from 'react-redux';
import Icons from 'common/icons';
import {Sizes} from 'common';
import {useMemo} from 'react';

type Props = {
  item:
    | ProductData
    | GetAttributesBySkuOutput
    | getHomepageSuperListItemProps[];
  style?: ViewProps['style'];
  searchAmountStyle?: ViewProps['style'];
  soldOutStyle?: ViewProps['style'];
  HorizontalImgStyle?: ImageProps['style'];
  descriptionStyle?: ViewProps['style'];
  descriptionTextStyle?: ViewProps['style'];
  index: number;
  Products?: boolean;
  rating?: boolean;
  offAmount?: boolean;
  soldOutProduct?: boolean;
  HorizontalsItems?: boolean;
  tag?: boolean;
  otherProduct?: boolean;
  CartAndWishlist?: boolean;
  onPress?: () => void;
  onViewProduct?: () => void;
  addToWishList?: () => void;
  onCart?: () => void;
  rewordPoint?: boolean;
  reviewsPoint?: boolean;
  ratingCounts?: boolean;
  SingleLineSpace?: boolean;
};

const ProductItems = ({
  style,
  SingleLineSpace = false,
  soldOutStyle,
  HorizontalImgStyle,
  item,
  Products,
  HorizontalsItems = false,
  tag = false,
  otherProduct = false,
  index,
  rating,
  offAmount,
  CartAndWishlist = false,
  onViewProduct,
  onCart,
  onPress,
  searchAmountStyle,
  descriptionStyle,
  rewordPoint = true,
  reviewsPoint = true,
  descriptionTextStyle,
  allPromotionProducts,
  addToWishList,
  freeProduct,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {baseCountryData} = useSelector((state: RootState) => state.app);
  return (
    <>
      <TouchableOpacity
        key={index}
        onPress={onPress}
        style={[
          item === null && styles.nullBox,
          styles.containerSellers,
          Products && styles.containerSellersProduct,
          HorizontalsItems && styles.HorizontalsItemsStyles,
          style,
        ]}>
        <View
          style={[styles.pulsImgStyle, rating && styles.pulsImgStyleProduct]}>
          {item?.is_in_stock ? null : (
            <View
              style={[
                styles.pulsImgStyleSold,
                item?.is_in_stock ? null : styles.pulsImgStyleSoldCont,
                rating && styles.imgPlusProduct,
                soldOutStyle,
              ]}>
              <Label
                weight="700"
                size="l"
                color="whiteColor"
                text={t('soldProducts.soldOut')}
              />
            </View>
          )}

          <FastImage
            style={[
              styles.imgPlus,
              rating && styles.imgPlusProduct,
              HorizontalImgStyle,
            ]}
            onError={e => {
              e.target.uri = productDummyImage;
            }}
            source={{
              uri: item?.thumbnail_url
                ? getImageUrl(item?.thumbnail_url)
                : getImageUrl(item?.image_url) || item === null
                ? productDummyImage
                : null,
            }}
          />
        </View>
        <View style={[HorizontalsItems && styles.items, descriptionStyle]}>
          <Label color="textLight" weight="200" text={'Express Delivery'} />
          <Label
            color="textLight"
            weight="400"
            style={styles.paddingSpace}
            numberOfLines={1}
            text={item?.name}
            ellipsizeMode={'tail'}
          />
          {item?.short_description ? (
            <View style={[SingleLineSpace ? null : {height: Sizes.x3l}]}>
              <Label
                style={[styles.descriptionTextSub]}
                numberOfLines={descriptionStyle ? 2 : 1}
                text={item?.short_description}
                ellipsizeMode={'tail'}
              />
            </View>
          ) : (
            <Spacer size="x3l" />
          )}

          {rating && (
            <View style={[styles.ratingView, descriptionStyle && styles.left]}>
              {reviewsPoint && offAmount ? null : item?.average_rating ||
                item.rating ? (
                <Label
                  color="linkText"
                  size="m"
                  text={Number.parseFloat(
                    item?.average_rating || item.rating,
                  ).toFixed(1)}
                />
              ) : null}
              {item?.average_rating || item.rating ? (
                <Ratings
                  size={10}
                  style={styles.raining}
                  count={5}
                  defaultRating={parseInt(item?.average_rating || item.rating)}
                  showRating={false}
                  onFinishRating={() => {}}
                />
              ) : (
                <View style={styles.imgView}>
                  <FastImage
                    resizeMode="contain"
                    style={styles.successIcon}
                    source={Icons.starGroupNullIcon}
                  />
                </View>
              )}
              {reviewsPoint && item?.rating_count ? (
                <Label
                  color="linkText"
                  size="m"
                  text={'(' + item?.rating_count + ')'}
                />
              ) : null}
            </View>
          )}

          <View
            style={[
              styles.prizeTextView,
              rating && styles.prizeTextProducts,
              HorizontalsItems && styles.prizeWithTag,
              searchAmountStyle,
              SingleLineSpace ? null : {height: Sizes.x5l},
            ]}>
            <View style={freeProduct && styles.freeView}>
              <Label
                style={styles.prizeText}
                text={
                  item?.price?.minimalPrice?.amount?.currency_symbol +
                  item?.price?.minimalPrice?.amount?.value
                    ? item?.price?.minimalPrice?.amount?.currency_symbol +
                      item?.price?.minimalPrice?.amount?.value
                    : item?.price?.minimalPrice?.amount?.value
                }
              />
            </View>

            {item.type_id === 'grouped' ? (
              item?.price?.regularPrice?.amount?.value === 0 ? null : (
                <Label
                  style={styles.descriptionTextSub}
                  text={t('productItems.starting')}
                />
              )
            ) : (
              <>
                {item?.price?.regularPrice?.amount?.value >
                item?.price?.minimalPrice?.amount?.value ? (
                  <Label
                    style={styles.removePrize}
                    text={
                      item.price?.regularPrice.amount.currency_symbol +
                      '' +
                      item.price?.regularPrice?.amount?.value
                    }
                  />
                ) : null}
              </>
            )}
            <Spacer size="s" />

            {offAmount &&
            item?.price?.regularPrice?.amount?.value >
              item?.price?.minimalPrice?.amount?.value ? (
              <Label
                letterSpacing={0.1}
                size="m"
                color="grassGreen"
                weight="400"
                text={
                  (
                    100 -
                    (Number(item?.price?.minimalPrice?.amount?.value) * 100) /
                      Number(
                        item?.price?.regularPrice?.amount?.value ||
                          item?.price?.minimalPrice?.amount?.value,
                      )
                  ).toFixed(2) + t('productItems.off')
                }
              />
            ) : null}
            {tag && <Tag label={t('productItems.unavailable')} />}
          </View>
          {rewordPoint &&
          baseCountryData?.country_id === 'IN' &&
          item.reward_point_product &&
          item.reward_point_product !== 0 ? (
            <View style={styles.directionView}>
              <FastImagesItem
                FastImageStyle={styles.rewardIcon}
                source={{
                  uri: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/coin.png',
                }}
              />
              <Spacer type="Horizontal" size="s" />
              <Label
                size="m"
                color="textLight"
                text={item?.reward_point_product}
              />
            </View>
          ) : null}
          {allPromotionProducts &&
          allPromotionProducts?.some(e => e.product_sku === item?.sku) ? (
            <View>
              <Label
                color="freeProduct"
                weight="bold"
                text={t('productItems.freeProduct')}
              />
            </View>
          ) : null}

          {otherProduct && (
            <Tag style={styles.tag} label={t('productItems.check')} />
          )}

          {(freeProduct &&
            freeProduct?.some(e => e?.product_sku === item?.sku)) ||
          (freeProduct &&
            freeProduct?.some(e => e?.parent_sku === item?.sku)) ? (
            <Label
              style={styles.space}
              size="m"
              weight="500"
              align="center"
              color="freeProduct"
              text={' Enjoy Free Product'}
            />
          ) : (
            <Spacer type="Vertical" size="l" />
          )}

          {CartAndWishlist && (
            <>
              <Spacer size="xm" />
              <View style={styles.viewContinuer}>
                {item.type_id === 'simple' ? (
                  <>
                    <Button
                      tintColor={!item.is_in_stock && 'textLight'}
                      labelStyle={[
                        styles.addButtonText,
                        !item.is_in_stock && {color: colors.textLight},
                      ]}
                      radius="m"
                      onPress={onCart}
                      style={[
                        styles.addToCart,
                        !item.is_in_stock ? styles.opacity : null,
                      ]}
                      disabled={item.is_in_stock ? false : true}
                      iconStyle={styles.icons}
                      iconLeft={'cartBucketIcon'}
                      text={t('buttons.addToCart')}
                    />

                    <Spacer type="Horizontal" size="l" />
                    <Button
                      labelStyle={styles.addButtonText}
                      styleLeftIcon={styles.buttonLeft}
                      radius="m"
                      iconStyle={styles.icons}
                      tintColor="blackColor"
                      onPress={addToWishList}
                      style={[styles.addToWishlist, styles.buttonBackground]}
                      iconCenter={'heartIcon'}
                    />
                  </>
                ) : (
                  <Button
                    labelStyle={styles.addButtonText}
                    radius="m"
                    onPress={onViewProduct}
                    style={styles.viewProduct}
                    text={t('buttons.viewProduct')}
                  />
                )}
              </View>
              <Spacer size="l" />
            </>
          )}
        </View>
      </TouchableOpacity>
    </>
  );
};

export default ProductItems;

import React, {
  useCallback,
  useState,
  useRef,
  useEffect,
  useMemo,
  memo,
} from 'react';
import {
  Alert,
  TextInput,
  TouchableOpacity,
  View,
  ViewProps,
  LayoutAnimation,
  InteractionManager,
  Animated,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {
  Label,
  Radio,
  Spacer,
  ImageIcon,
  CheckBox,
  BillingAddress,
} from 'components/atoms';
import stylesWithOutColor from './style';
import {Sizes} from 'common';
import {navigate} from 'utils/navigationRef';
import {getUserInfo, setLoading} from 'app-redux-store/slice/appSlice';
import {showSuccessMessage} from 'utils/show_messages';
import {countryPhoneCode} from 'constants/countryPhoneCode/phoneCode';
import {deleteAddress, checkPinCodeValid} from 'services/address';
import {
  debounce,
  getTag,
  distance,
  onCheckGst,
  fullNameText,
} from 'utils/utils';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {debugLog} from 'utils/debugLog';
import {CustomDialog, Button} from 'components/molecules';

type Props = {
  item: CustomerAddressV2 | string;
  index: number;
  itemLength: number;
  // onSelectPostcode: (postcode: string) => void;
  // onSelectCountry: (countryId: string) => void;
  onSelect: () => void;
  isEdit?: boolean;
  selected: boolean;
  style?: ViewProps['style'];
  cartAddress?: boolean;
  selectedCountry?: CustomerAddressV2['country_id'];
  selectedPostcode?: CustomerAddressV2['postcode'];
  cardType?: string;
  buyNow?: boolean;
  onChangeSelectAddress: (item: CustomerAddressV2, hideModal: boolean) => void;
  selectedAddress: CustomerAddressV2;
  setIsAddressModalOpen: (value: boolean) => void;
  onChangeLocation: (item: CustomerAddressV2) => void;
  coords: any;
  onItemChangeAddress: (id: number) => void;
  addBillingId?: number;
  billingAddress?: CustomerAddressV2;
  shippingBillingAddressId?: number;
} & AddressType;

const AddressCard = ({
  item,
  itemLength,
  // onSelectPostcode,
  // onSelectCountry,
  onSelect,
  style,
  index,
  selected,
  cardType,
  selectedCountry,
  selectedPostcode,
  buyNow,
  onChangeSelectAddress,
  selectedAddress,
  setIsAddressModalOpen,
  onChangeLocation,
  coords,
  onItemChangeAddress,
  addBillingId,
  billingAddress,
  shippingBillingAddressId,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const [deleteModel, setDeleteModel] = useState(false);
  const [postcode, setPostcode] = useState('');
  const [loader, setLoader] = useState(false);
  const [validPinCode, setValidPinCode] = useState(false);
  const [pinCodeError, setPinCodeError] = useState('');
  const {countryListings, userInfo} = useSelector(
    (state: RootState) => state.app,
  );
  const scaleAnim = useRef(new Animated.Value(1)).current;
  // useEffect(() => {
  //   if (postcode && postcode.length === 6) {
  //     debouncedUpdate(postcode, false);
  //   } else {
  //     setValidPinCode(false);
  //   }
  // }, [postcode]);

  // const debouncedUpdate = useCallback(
  //   debounce(async (code: string, action: boolean) => {
  //     const response = await checkPinCodeValid(code);
  //     const {status, data} = response;
  //     setLoader(false);
  //     setPinCodeError(status ? '' : data?.message);
  //     setValidPinCode(status ? false : true);
  //     if (action && status) {
  //       onSelectPostcode(code);
  //     }
  //   }, 1000),
  //   [],
  // );

  // const onChangePin = () => {
  //   if (postcode && postcode.length === 6) {
  //     setLoader(true);
  //     debouncedUpdate(postcode, true);
  //   } else {
  //     setPinCodeError('');
  //     setValidPinCode(true);
  //   }
  // };

  // const addressDelete = useCallback(async (id: number) => {
  //   Alert.alert(
  //     t('addressList.deletedAddress'),
  //     t('addressList.confirmDeleted'),
  //     [
  //       {
  //         text: 'Cancel',
  //         onPress: () => debugLog('Cancel Pressed'),
  //         style: 'cancel',
  //       },
  //       {
  //         text: 'Delete',
  //         onPress: async () => {
  //           dispatch(setLoading(true));
  //           const {data, status} = await deleteAddress(id);
  //           dispatch(setLoading(false));
  //           if (status) {
  //             showSuccessMessage(t('addressList.defaultBilling'));
  //             dispatch(getUserInfo());
  //           }
  //         },
  //       },
  //     ],
  //     {cancelable: false},
  //   );
  // }, []);

  // useEffect(() => {
  //   LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  // }, [selected]);

  const onSelectItemClick = () => {
    if (selected) {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    } else {
      onSelect();
    }
  };

  const onEdit = useCallback(
    (skipLocation = false) => {
      setIsAddressModalOpen(false);
      onChangeLocation({...item, edit: true, skipLocation});
    },
    [item, setIsAddressModalOpen, onChangeLocation],
  );

  const kmText = useMemo(() => {
    if (
      !coords?.latitude ||
      !coords?.longitude ||
      !item?.latitude ||
      !item?.longitude
    )
      return '';
    const distanceValue = distance(
      [coords.latitude, coords.longitude],
      [item.latitude, item.longitude],
      'km',
    );
    const formattedDistance = parseFloat(distanceValue).toFixed(2);
    const isHere = Number(formattedDistance) === 0;
    return `${isHere ? '' : `${formattedDistance} `}${t(
      isHere ? 'address.youHere' : 'address.kmWay',
    )}`;
  }, [coords?.latitude, coords?.longitude, item?.latitude, item?.longitude, t]);

  const onSelectAddress = async () => {
    const checkBilling = item.vat_id
      ? await onCheckGst(item.vat_id, item?.region?.region)
      : {status: true};
    if (!checkBilling?.status) {
      onEdit(true);
      return;
    }
    Animated.timing(scaleAnim, {
      toValue: 0.9,
      friction: 5,
      tension: 80,
      useNativeDriver: true,
      duration: 0,
      delay: 0,
    }).start(() => {
      setIsAddressModalOpen(false);
      onItemChangeAddress(undefined);
      onSelect();
    });
  };

  const addressDelete = useCallback(async () => {
    setDeleteModel(false);
    dispatch(setLoading(true));
    const {data, status} = await deleteAddress(billingAddress?.id);
    dispatch(setLoading(false));
    if (status) {
      showSuccessMessage(t('addressCard.billingMsg'));
      onItemChangeAddress(undefined);
      dispatch(getUserInfo());
      await localStorage.removeItem('shippingAddressId');
    }
  }, [billingAddress]);

  const onDeleteBilling = () => {
    if (
      billingAddress?.default_billing &&
      shippingBillingAddressId == item?.id
    ) {
      setDeleteModel(!deleteModel);
    } else {
      onItemChangeAddress(item?.id);
    }
  };

  const onBillingAddress = (data, deleteBilling) => {
    deleteBilling ? onDeleteBilling() : onItemChangeAddress(data?.id);
  };

  const cartAddress = (data, showBilling, deleteBillingOption) => {
    let address = '';
    if (data) {
      const {street, map_address, customer_street_2, city, region, postcode} =
        data;
      let street2 = '';
      if (customer_street_2) {
        street2 = customer_street_2;
      } else if (map_address) {
        const parts = map_address?.split(',');
        const beforePin = parts
          .slice(0, parts.length - 2)
          .join(',')
          .trim();
        street2 = beforePin;
      }
      address = `${street?.length > 0 ? street[0] : ''}${
        street2 ? ', ' + street2 : ''
      }${city ? ', ' + city : ''}${
        region?.region ? ', ' + region?.region : ''
      }${postcode ? ' - ' + postcode : ''}`;
    }
    return (
      <View>
        <View style={styles.rowCenter}>
          <View style={styles.defaultSubView}>
            <Label
              text={data?.tag ? data?.tag : fullNameText(data) || ''}
              weight="600"
              size="mx"
              color="text"
              textTransform="capitalize"
            />
            <Spacer size="s" type="Horizontal" />
            <View style={styles.shippingTag}>
              <Label
                text={t(
                  data.default_billing
                    ? 'addressCard.billingAddress'
                    : 'addressCard.shippingAddress',
                )}
                weight="500"
                size="xms"
                color="text2"
                textTransform="capitalize"
              />
            </View>
            <Spacer size="s" type="Horizontal" />
            {data?.latitude && coords?.latitude ? (
              <>
                <Spacer size="s" type="Horizontal" />
                <View style={styles.awayView}>
                  <Label
                    text={kmText}
                    fontFamily="Medium"
                    size="xms"
                    color="green2"
                    textTransform="capitalize"
                  />
                </View>
              </>
            ) : (
              <View />
            )}
          </View>
          {data?.default_shipping ? (
            <FastImage
              style={styles.defaultGif}
              source={Icons.defaultGif}
              resizeMode="contain"
            />
          ) : (
            <View />
          )}
        </View>
        <Spacer size="sx" />
        {data?.tag ? (
          <View style={styles.row}>
            <Label
              text={fullNameText(data) || ''}
              size="mx"
              weight="600"
              textTransform="capitalize"
              color="text2"
            />
          </View>
        ) : (
          <View />
        )}
        {data?.street?.length > 1 && data?.street[1] ? (
          <>
            <Spacer size="s" />
            <View style={styles.fRow}>
              <ImageIcon
                size="xx"
                tintColor="text"
                icon="map"
                resizeMode="contain"
              />
              <Spacer size="s" type="Horizontal" />
              <Label
                text={data?.street[1] || ''}
                size="m"
                weight="400"
                color="text2"
                textTransform="capitalize"
                style={styles.fOne}
              />
            </View>
          </>
        ) : (
          <View />
        )}
        <Spacer size="s" />
        <View style={styles.fRow}>
          <ImageIcon
            size="xx"
            tintColor="text"
            icon="addressPin"
            resizeMode="contain"
          />
          <Spacer size="s" type="Horizontal" />
          <Label
            text={address}
            size="m"
            weight="400"
            color="text2"
            textTransform="capitalize"
            style={styles.fOne}
          />
        </View>
        <Spacer size="s" />
        <View style={styles.rowCenter}>
          <ImageIcon size="mx" icon="phone1" />
          <Spacer size="s" type="Horizontal" />
          <Label
            text={`${data?.telephone}${
              data?.custom_attributes?.length > 0 &&
              data?.custom_attributes[0].value
                ? ', ' + data?.custom_attributes[0].value
                : ''
            }`}
            weight="400"
            size="m"
            color="text2"
            textTransform="capitalize"
          />
        </View>

        {data?.vat_id ? (
          <>
            <Spacer size="s" />
            <View style={styles.rowCenter}>
              <ImageIcon size="xx" icon="gst" tintColor="text2" />
              <Spacer size="s" type="Horizontal" />
              <Label text={data?.vat_id} weight="400" size="m" color="text2" />
            </View>
          </>
        ) : (
          <View />
        )}
        {!showBilling && (
          <>
            <Spacer size="s" />
            <TouchableOpacity
              style={styles.alignCenter}
              onPress={() => onBillingAddress(data, deleteBillingOption)}>
              <CheckBox
                selected={
                  addBillingId == data?.id || deleteBillingOption ? false : true
                }
                onValueChange={() =>
                  onBillingAddress(data, deleteBillingOption)
                }
                borderColor="white"
                style={styles.checkboxStyle}
              />
              <Spacer size="sx" type="Horizontal" />
              <Label
                text={t('addressCard.billingSame')}
                size="m"
                weight="400"
                color="text2"
                textTransform="capitalize"
                style={styles.fOne}
              />
            </TouchableOpacity>
          </>
        )}
        <Spacer size="xm" />
        <View style={styles.bottomView}>
          <TouchableOpacity
            style={styles.editBtn}
            activeOpacity={0.7}
            onPress={
              data.default_billing
                ? () => onItemChangeAddress(item?.id)
                : onEdit
            }>
            <ImageIcon size="m" icon="editPencil" tintColor="newSunnyOrange" />
            <Spacer size="xm" type="Horizontal" />
            <Label
              text={t('manageAddress.edit')}
              size="m"
              color="newSunnyOrange"
              fontFamily="Medium"
              textTransform="capitalize"
            />
          </TouchableOpacity>
          <View style={styles.fOne} />
          {!showBilling && (
            <Button
              style={styles.button}
              text={t('buttons.deliverHere')}
              onPress={onSelectAddress}
              labelStyle={styles.saveButton}
              labelSize="l"
              size="m"
              radius="sx"
              withGradient
              gradientColors={[colors.coral, colors.persimmon]}
            />
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.fOne}>
      {item === '' ? (
        <View style={[styles.container, style]}>
          {/* <View>
            {cardType !== 'checkoutPage' && (
              <View style={styles.subContainerView}>
                <Label
                  text={t('addressCard.pinCodeTitle')}
                  size="mx"
                  color="text2"
                  fontFamily="SemiBold"
                  weight="500"
                />
                <Spacer size="xm" />
                <View style={styles.borderTop}>
                  <Spacer size="xm" />
                  <View style={styles.onSelectView}>
                    <View style={styles.defaultSubView}>
                      <Label
                      text={
                        countryPhoneCode.find(
                          obj => obj?.code === selectedCountry,
                        )?.emoji ?? ''
                      }
                      color="grey2"
                    />
                      <FastImage
                        resizeMode="contain"
                        style={styles.flagIcon}
                        source={Icons.indiaFlag}
                      />
                      <Label text={' | '} color="placeholderColor" />

                      <TextInput 
                        testID="txtAddressPostCode"
                        value={postcode}
                        style={styles.postCodeStyle}
                        onChangeText={text =>
                          setPostcode(text.replace(/[^0-9]/g, ''))
                        }
                        maxLength={6}
                        allowFontScaling={false}
                        keyboardType="phone-pad"
                      />
                    </View>
                    {loader ? (
                      <ActivityIndicator size="small" color={colors.primary} />
                    ) : (
                      <TouchableOpacity onPress={() => onChangePin()}>
                        <Label
                          text={
                            selectedPostcode
                              ? t('buttons.change')
                              : t('buttons.check')
                          }
                          size="m"
                          fontFamily="Regular"
                          color="grey"
                          textTransform="uppercase"
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                  {validPinCode && (
                    <>
                      <Spacer size="s" />
                      <Label
                        text={String(
                          validPinCode
                            ? pinCodeError
                              ? pinCodeError
                              : postcode?.trim() === ''
                              ? t('validations.postcodeRequired')
                              : t('validations.validPostCode')
                            : '',
                        )}
                        color="textError"
                        size="m"
                      />
                    </>
                  )}
                </View>
              </View>
            )}
            <Spacer size="m" />
          </View> */}
          {userInfo?.id && (
            <>
              <TouchableOpacity
                style={styles.addressView}
                onPress={() => {
                  setIsAddressModalOpen(false);
                  onChangeLocation(item);
                }}>
                <View style={styles.checkoutSubView}>
                  <Label text={'+'} size="xl" color="grey" />
                </View>
                <Spacer size="m" type="Horizontal" />
                <Label
                  text={t('otherText.addNewAddressTxt')}
                  size="mx"
                  color="text"
                  fontFamily="Medium"
                />
              </TouchableOpacity>
              <Spacer size="m" />
            </>
          )}
        </View>
      ) : (
        <TouchableOpacity
          style={[styles.addressCard, style, selected && styles.selectedAdd]}
          activeOpacity={1}
          onPress={onSelectAddress}>
          {selected && (
            <ImageIcon size="m" icon="blackCheck" style={styles.tick} />
          )}
          <Animated.View
            style={{
              transform: [{scale: scaleAnim}],
            }}>
            <View style={styles.selectedView}>
              {/* <Radio
              selected={selected}
              onPress={onSelectItemClick}
              fillColor="text2"
            /> */}
              <View style={styles.tagView}>
                <ImageIcon size="m" icon={getTag(item?.tag)} />
              </View>
              <Spacer size="xm" type="Horizontal" />
              <View style={styles.fOne}>
                {cartAddress(item, shippingBillingAddressId == item?.id, false)}
                <Spacer size="m" />
                {shippingBillingAddressId == item?.id &&
                  cartAddress(billingAddress, false, true)}
              </View>
            </View>
            {/* {cardType === 'checkoutPage' && (
            <>
              <View style={styles.checkoutView}>
                <TouchableOpacity
                  style={styles.buttonView}
                  onPress={() => addressDelete(item?.id)}>
                  <Label
                    text={t('buttons.remove').toUpperCase()}
                    size="m"
                    fontFamily="Medium"
                    color="text2"
                  />
                </TouchableOpacity>
                <Spacer size="s" />
                <TouchableOpacity
                  style={styles.buttonView}
                  onPress={() => navigate('ManageAddress', {address: item})}>
                  <Label
                    text={t('manageAddress.edit')}
                    size="m"
                    fontFamily="Medium"
                    color="text2"
                  />
                </TouchableOpacity>
              </View>
            </>
          )} */}
          </Animated.View>
        </TouchableOpacity>
      )}

      {item?.id && addBillingId == item?.id ? (
        <BillingAddress
          address={billingAddress ? billingAddress : undefined}
          addressId={item.id}
          onCancel={() => onItemChangeAddress(item?.id)}
          onDeleteBilling={onDeleteBilling}
          itemLength= {itemLength}
          index={index}
        />
      ) : (
        <View />
      )}
      {deleteModel && (
        <CustomDialog
          visible={deleteModel}
          title={t('addressCard.billingTitle')}
          subTitle={t('addressCard.confirmBDeleted')}
          onClose={() => setDeleteModel(false)}
          btn1Click={() => addressDelete()}
          btn2Click={() => [setDeleteModel(false), onItemChangeAddress(null)]}
        />
      )}
    </View>
  );
};

export default memo(AddressCard);

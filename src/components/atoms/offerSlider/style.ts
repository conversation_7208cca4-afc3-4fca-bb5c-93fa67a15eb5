import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    fRow: {
      flexDirection: 'row',
    },
    flex: {
      flex: Sizes.x,
    },
    offersView: {
      paddingHorizontal: Sizes.m,
      alignItems: 'center',
      flexDirection: 'row',
    },
    offerItemImg: {
      width: '100%',
      height: 95,
    },
    offerView: {
      backgroundColor: colors.background,
      height: Sizes.ex0,
      marginTop: Sizes.s,
    },
    offerMainView: {
      backgroundColor: colors.mintCream,
      height: Sizes.exl,
    },
    disView: {
      position: 'absolute',
      zIndex: Sizes.xs,
      alignSelf: 'center',
    },
    sliderItem: {
      height: 105,
    },
  });

export default styles;

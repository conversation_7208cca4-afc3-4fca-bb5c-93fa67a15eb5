import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    headingTop: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      marginHorizontal: Sizes.m,
    },

    topBrand: {
      paddingHorizontal: Sizes.s,
      paddingVertical: Sizes.xm,
    },
    listAlign: {alignItems: 'center'},
    deration: {flexDirection: 'row'},
    subCategoriesList: {
      width: '92%',
      fontWeight: '400',
      fontSize: Sizes.m,
      // textAlign: 'center',
    },
    separates: {
      borderBottomWidth: 0.5,
      borderBottomColor: colors.lightGray,
    },
    topBrandsAlign: {
      alignItems: 'center',
      alignSelf: 'center',
      width: '20%',
      paddingVertical: Sizes.l,
    },
    paddingSpace: {padding: Sizes.s},
  });

export default styles;

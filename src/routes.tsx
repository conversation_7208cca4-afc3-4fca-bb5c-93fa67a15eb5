import React, {useContext, useEffect} from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  HomePage,
  RegisterOtp,
  Login,
  RegisterUser,
  ForgetPassword,
  Categories,
  ProductDetail,
  SearchProduct,
  ProductDescription,
  Cart,
  WishList,
  UserWishlist,
  Profile,
  ProfileDetails,
  ManageAddress,
  AddressList,
  News,
  Saved,
  Multimedia,
  VideoPlay,
  OrderDetail,
  OrderList,
  NewsDetails,
  CategoryDetail,
  UrlResolver,
  AllBrands,
  MembershipPage,
  Membership,
  HelpCenter,
  HelpOrder,
  MyRewords,
  Reword,
  Whatsapp,
  PaymentPage,
  AppLoading,
  Shorts,
  ShortVideos,
  MyReferral,
  ItemNotFound,
  ChangeEmailOrMobile,
  ChangeEmailOrMobileOtp,
  RewardZoneScene,
  CouponsScene,
  OrderReturnListScene,
  OrderReturnTermsScene,
  MagazineScene,
  SavedShorts,
  ChangePassword,
  VerifyEmailScene,
  CustomerPhotosScene,
  Offers,
  ForgetPasswordScene,
  BuyAgainScene,
  ThankYouPage,
  SavedContent,
  SellOnDentalkart,
  ReviewListScene,
  Freebies,
  ProfileCompletionScene,
  MapLocation,
} from './scenes';
import {useNavigation, useTheme} from '@react-navigation/native';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import {Platform, StyleSheet, View, useColorScheme} from 'react-native';
import {CommunityTab} from 'components/organisms';
import {navigate} from 'utils/navigationRef';
import {Sizes} from 'common';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import NetworkLogger from 'react-native-network-logger';
import ScreenNetworkLogger from './NetworkLogger/ScreenNetworkLogger';
import SplashScreen from 'scenes/splashScreen';
import {ScrollContext} from 'scenes/homeScreen/homePage/contextApi/scrollContext';
import {openWhatsApp} from 'utils/utils';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import {debugLog} from 'utils/debugLog';
import Sales from 'scenes/sales';
import OtpScene from 'components/organisms/otpScene';
import EmailVerificationOtpScene from 'scenes/emailVerificationOtpScene';
import ReactMoE from 'react-native-moengage';
import { resolveUrl } from 'utils/resolveUrl';
import { setLoading } from 'app-redux-store/slice/appSlice';
// import { initializeAppData } from 'app-redux-store/slice/appSlice';
// const NetworkLoggerScreen = () => <NetworkLogger />;

export type RootTabParamList = {
  Shop: undefined;
  Categories: undefined;
  News: undefined;
  Profile: undefined;
  Multimedia: undefined;
  Saved: undefined;
};

export type RootStackParamsList = {
  SplashScreen: undefined;
  AppLoading: undefined;
  Tab: {screen: keyof RootTabParamList};
  Login: {
    nextScreenName?: keyof RootStackParamsList;
    nextScreenParams?: any;
    nextRouterState?: any;
    otpScreen?: any;
    loginType?: string;
    back?: boolean;
    nextScreenNameLink?: keyof RootStackParamsList;
  };
  ForgetPassword: undefined;
  RegisterUser: {
    phoneNumber: string;
    source: 'registerWithMobile' | 'registerWithEmail';
    email: string;
    otp: string;
    password: string;
  };
  Otp: {
    emailPhoneNumber: string;
    password?: string;
    source:
      | 'login'
      | 'forgetPasswordWithEmail'
      | 'forgetPasswordWithMobile'
      | 'registerWithMobile'
      | 'registerWithEmail';
    nextScreenProps: any;
    nextScreenName: any;
  };
  HomePage: {productId: number};
  Offers: undefined;
  RegisterOtp: {email?: string};
  ResetPassword: {
    emailPhoneNumber: string;
    source: 'forgetPasswordWithEmail' | 'forgetPasswordWithMobile';
    otp: string;
  };
  EmailVerification: {
    values: any;
    back: boolean;
  };
  Categories: undefined;
  SearchProduct: undefined;
  ProductDetail: {productId: number; orderId: number; ProductItems: string};
  ProductDescription: {productId: number};
  Cart: {productId: number};
  WishList: {productId: string};
  UserWishlist: {
    wishlist_id: String;
    productId: number;
    wishlists: any[];
    updateData?: any;
    friendData?: any;
    defaultTab?: any;
  };
  Profile: {productId: number};
  ProfileDetails: {productId: number};
  ManageAddress: {address: CustomerAddressV2};
  News: undefined;
  NewsTab: undefined;
  NewsDetails: {item: string; currentItem: string};
  Saved: undefined;
  Multimedia: undefined;
  VideoPlay: {source: string};
  OrderDetail: {orderId: string};
  OrderList: {Data: string; pageType: string; buyAgainDropDown: boolean};
  CategoryDetail: {categoryId: number};
  UrlResolver: {
    urlKey: string;
    typeId?: string;
    referralCode?: string;
    referType?: string;
    productSku?: string;
  };
  AllBrands: {saleUrl?: string};
  CustomerPhotosScene: undefined;
  Membership: undefined;
  MembershipPage: undefined;
  AddressList?: {checkout: string};
  HelpCenter?: {login: boolean};
  HelpOrder?: {item: string};
  MyRewords?: {orderId: string};
  Reword?: undefined;
  Whatsapp?: undefined;
  PaymentPage?: {selectedAddress: any; cart: any};
  Shorts: undefined;
  MyReferral: undefined;
  RewardZoneScene: undefined;
  ItemNotFound: undefined;
  ShortVideos: {
    videoId?: string | number;
    source?: 'SCROLL' | 'THUMBNAIL' | 'RECOMMENDED' | 'SEARCH';
  };
  ChangeEmailOrMobile: {isEditType?: string};
  ChangeEmailOrMobileOtp: {isEditType: any; EmailOrMobileValue: any};
  ChangePassword: undefined;
  CouponsScene: undefined;
  OrderReturnListScene: {returnReasons: string};
  OrderReturnTermsScene: {orderId: string};
  VerifyEmailScene: undefined;
  SellOnDentalkart: undefined;
  ThankYouPage: undefined;
  ForgetPasswordScene: {mobileNumber: string; credential: string};
  BuyAgainScene: undefined;
  ReviewListScene: {productId?: number};
  Freebies: undefined;
  ProfileCompletion: {
    phoneNumber: string;
    source: 'registerWithMobile' | 'registerWithEmail';
    // email: string;
  };
  OtpScene: undefined;
  MapLocation: {
    deliverAddress: any;
    cardType?: string;
    location?: true;
    coords: any;
    goBack?: any;
    onChangeSelectAddress?: any;
  };
};

const Tab = createBottomTabNavigator<RootTabParamList>();

const Stack = createNativeStackNavigator<RootStackParamsList>();

const NewsTab = () => {
  const scheme = useColorScheme();
  const {colors} = useTheme();

  return (
    <Tab.Navigator
      initialRouteName="News"
      screenOptions={() => ({
        tabBarStyle: {
          backgroundColor:
            scheme === 'light' ? colors.lightPrimaryColor : colors.topItemsBg,
          paddingVertical: 10,
          borderTopLeftRadius: 5,
          borderTopColor: colors.transparentColor,
        },
        tabBarActiveTintColor: colors.tabActive,
        tabBarInactiveTintColor: colors.tabInActive,
        headerShown: false,
      })}>
      <Tab.Screen
        options={({navigation}) => {
          return {
            tabBarIcon: ({focused}) => (
              <ImageIcon
                style={{width: 29, height: 26}}
                tintColor={focused ? 'tabActive' : 'tabInActive'}
                icon="shops"
              />
            ),
          };
        }}
        name="Shop"
        component={HomePage}
        listeners={({navigation, route}) => ({
          tabPress: e => {
            // Prevent default action
            e.preventDefault('Shop');
            navigation.navigate('Tab', {screen: 'Shop'});
          },
        })}
      />
      <Tab.Screen
        options={{
          tabBarIcon: ({color, focused}) => (
            <ImageIcon
              style={{width: 29, height: 26}}
              tintColor={focused ? 'tabActive' : 'tabInActive'}
              icon={'news'}
            />
          ),
        }}
        name="News"
        component={News}
      />
     { (Platform.OS !== 'ios') && <Tab.Screen
        options={{
          tabBarIcon: ({focused}) => (
            <ImageIcon
              style={{width: 29, height: 26}}
              tintColor={focused ? 'tabActive' : 'tabInActive'}
              icon="multimediaIcon"
            />
          ),
        }}
        name="Shorts"
        component={Shorts}
      />}
      <Tab.Screen
        options={{
          tabBarIcon: ({focused}) => (
            <ImageIcon
              style={{width: 29, height: 26}}
              tintColor={focused ? 'tabActive' : 'tabInActive'}
              icon="saveIcon"
            />
          ),
        }}
        name="Saved"
        component={Saved}
      />
      <Tab.Screen
        options={{
          tabBarIcon: ({focused}) => (
            <ImageIcon
              style={{width: 29, height: 26}}
              tintColor={focused ? 'tabActive' : 'tabInActive'}
              icon="news"
            />
          ),
        }}
        name="Magazine"
        component={MagazineScene}
      />
    </Tab.Navigator>
  );
};
const TabBar = () => {
  const scheme = useColorScheme();
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const {scrollToTop} = useContext(ScrollContext);
  const {whatsAppLink} = useSelector((state: RootState) => state.app);

  return (
    <Tab.Navigator
      screenOptions={() => ({
        tabBarStyle: {
          height:
            Platform.OS === 'ios'
              ? Sizes.x58 + insets.bottom
              : Sizes.x68 + insets.bottom,
          backgroundColor: colors.whiteColor,
          borderTopColor: colors.transparentColor,
          shadowColor: colors.black,
          shadowOffset: {
            width: 0,
            height: Sizes.m,
          },
          shadowOpacity: Sizes.z,
          shadowRadius: Sizes.xms,
          elevation: Sizes.xl,
        },
        tabBarIconStyle: {
          paddingVertical: Sizes.x,
        },
        tabBarItemStyle: {
          paddingVertical: Platform.OS === 'ios' ? Sizes.xs : Sizes.sx,
          borderTopWidth: 0.25,
          borderColor: colors.soldoutbackground1,
        },
        tabBarActiveTintColor: colors.tabActive,
        tabBarInactiveTintColor: colors.tabInActive,
        headerShown: false,
      })}>
      <Tab.Screen
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.tabFlex}>
              <ImageIcon
                size="xxl"
                icon={focused ? 'homeActive' : 'homeInActive'}
                tintColor={focused ? 'tabActive' : 'tabInActive'}
              />
              <Spacer size="xm" />
              <Label
                text="Home"
                size="m"
                weight="600"
                color={focused ? 'tabActive' : 'tabInActive'}
              />
            </View>
          ),
          tabBarLabel: ({focused}) => null,
        }}
        name="Home"
        component={HomePage}
        listeners={({navigation, route}) => ({
          tabPress: e => {
            // If already on the Home tab, scroll to top
            if (navigation.isFocused()) {
              e.preventDefault(); // Prevent default navigation behavior
              scrollToTop(); // Scroll to top using the context function
            }
          },
        })}
      />
      <Tab.Screen
        options={{
          tabBarIcon: ({color, focused}) => (
            <View style={styles.tabFlex}>
              <ImageIcon
                size="xxl"
                icon={focused ? 'brandsActive' : 'brandsInActive'}
              />
              <Spacer size="xm" />
              <Label
                text="Brands"
                size="m"
                weight="600"
                color={focused ? 'tabActive' : 'tabInActive'}
              />
            </View>
          ),
          tabBarLabel: ({focused}) => null,
        }}
        name="Brands"
        component={AllBrands}
      />

    {(Platform.OS !== 'ios') &&  <Tab.Screen
        options={({route}) => ({
          // tabBarStyle: {display: route?.name === 'Shorts' ? 'none' : 'flex'},
          tabBarIcon: ({focused}) => (
            <View style={styles.tabFlex}>
              <ImageIcon
                size="xxl"
                icon={focused ? 'playActive' : 'playInActive'}
              />
              <Spacer size="xm" />
              <Label
                text="Shorts"
                size="m"
                weight="600"
                color={focused ? 'tabActive' : 'tabInActive'}
              />
            </View>
          ),
          tabBarLabel: ({focused}) => null,
        })}
        name="Shorts"
        component={Shorts}
      />}

      {/* <Tab.Screen
        listeners={({navigation}) => ({
          tabPress: e => {
            e.preventDefault();
            navigation.navigate('CategoryDetail', {categoryId: 2566});
          },
        })}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.tabFlex}>
              <ImageIcon
                size="xxl"
                icon={focused ? 'offersActive' : 'offersInActive'}
              />
              <Spacer size="xm" />
              <Label
                text="Offers"
                size="m"
                weight="600"
                color={focused ? 'tabActive' : 'tabInActive'}
              />
            </View>
          ),
          tabBarLabel: ({focused}) => null,
        }}
        name="Offers"
        component={Offers}
      /> */}
      <Tab.Screen
        listeners={({navigation}) => ({
          tabPress: e => {
            e.preventDefault();
            navigation.navigate('CategoryDetail', {categoryId: 2587});
          },
        })}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.tabFlex}>
              <ImageIcon
                size="xxl"
                icon={focused ? 'freebiesActive' : 'freebiesInActive'}
              />
              <Spacer size="xm" />
              <Label
                text="Freebies"
                size="m"
                weight="600"
                color={focused ? 'tabActive' : 'tabInActive'}
              />
            </View>
          ),
          tabBarLabel: ({focused}) => null,
        }}
        name="Freebies"
        component={Freebies}
      />
      <Tab.Screen
        listeners={({navigation}) => ({
          tabPress: e => {
            e.preventDefault();
            if (whatsAppLink?.app_link) {
              openWhatsApp(whatsAppLink?.app_link);
            }
          },
        })}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.tabFlex}>
              <ImageIcon size="x26" icon="whatsapp1" tintColor={'text'} />
              <Spacer size="xm" />
              <Label
                text={t('otherText.help')}
                size="m"
                weight="600"
                color={focused ? 'tabActive' : 'tabInActive'}
              />
            </View>
          ),
          tabBarLabel: ({focused}) => null,
        }}
        name="HelpCenter"
        component={HelpCenter}
      />
    </Tab.Navigator>
  );
};

const Routes = ({isAppReady}: {isAppReady: boolean}) => {
  // if (!isAppReady) {
  //   return <SplashScreen />;
  // }

  const dispatch = useDispatch();
  const navigation = useNavigation()
  // dispatch(initializeAppData());
  useEffect(() => {
    ReactMoE.setEventListener('inAppCampaignClicked',async (inAppData) => {
      
      if (inAppData?.action && inAppData?.action?.navigationUrl) {
        dispatch(setLoading(true))
        await resolveUrl({ urlKey: inAppData?.action?.navigationUrl.split('.com')[1], navigation })
        dispatch(setLoading(false))
      }    
    });
  }, []);
  return (
    <Stack.Navigator
      initialRouteName="SplashScreen"
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}>
      <Stack.Screen name="SplashScreen" component={SplashScreen} />
      {/* <Stack.Screen name="AppLoading" component={AppLoading} /> */}
      <Stack.Screen name="Tab" component={TabBar} />
      <Stack.Screen name="OtpScene" component={OtpScene} />

      <Stack.Screen name="NewsTab" component={NewsTab} />
      <Stack.Screen
        name="ScreenNetworkLogger"
        component={ScreenNetworkLogger}
      />
      <Stack.Screen name="RegisterOtp" component={RegisterOtp} />
      <Stack.Screen
        name="ProfileCompletion"
        component={ProfileCompletionScene}
      />
      <Stack.Screen
        name="EmailVerification"
        component={EmailVerificationOtpScene}
      />

      {/* <Stack.Screen
        name="Login"
        component={ProfileCompletionScene}
      /> */}
      <Stack.Screen
        name="Login"
        component={Login}
        options={{
          // animation: 'slide_from_bottom',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen name="HomePage" component={HomePage} />
      <Stack.Screen name="RegisterUser" component={RegisterUser} />

      <Stack.Screen name="ForgetPassword" component={ForgetPassword} />
      <Stack.Screen name="Categories" component={Categories} />
      <Stack.Screen name="SearchProduct" component={SearchProduct} />
      <Stack.Screen
        name="ProductDetail"
        component={ProductDetail}
        options={{
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen name="ProductDescription" component={ProductDescription} />
      <Stack.Screen name="Cart" component={Cart} />
      <Stack.Screen name="WishList" component={WishList} />
      <Stack.Screen name="UserWishlist" component={UserWishlist} />
      <Stack.Screen name="Profile" component={Profile} />
      <Stack.Screen name="ProfileDetails" component={ProfileDetails} />
      <Stack.Screen name="ManageAddress" component={ManageAddress} />
      <Stack.Screen name="MapLocation" component={MapLocation} />
      <Stack.Screen name="AddressList" component={AddressList} />
      <Stack.Screen name="News" component={News} />
      <Stack.Screen name="NewsDetails" component={NewsDetails} />
      <Stack.Screen name="Saved" component={Saved} />
      <Stack.Screen name="Multimedia" component={Multimedia} />
      <Stack.Screen name="VideoPlay" component={VideoPlay} />
      <Stack.Screen name="OrderList" component={OrderList} />
      <Stack.Screen name="OrderDetail" component={OrderDetail} />
      <Stack.Screen name="CategoryDetail" component={CategoryDetail} />
      <Stack.Screen name="UrlResolver" component={UrlResolver} />
      <Stack.Screen name="AllBrands" component={AllBrands} />
      <Stack.Screen name="MembershipPage" component={MembershipPage} />
      <Stack.Screen name="Membership" component={Membership} />
      <Stack.Screen name="HelpCenter" component={HelpCenter} />
      <Stack.Screen name="HelpOrder" component={HelpOrder} />
      <Stack.Screen name="MyRewords" component={MyRewords} />
      <Stack.Screen name="Reword" component={Reword} />
      <Stack.Screen name="Whatsapp" component={Whatsapp} />
      <Stack.Screen name="PaymentPage" component={PaymentPage} />
      <Stack.Screen name="ShortVideos" component={ShortVideos} />
      <Stack.Screen name="MyReferral" component={MyReferral} />
      <Stack.Screen name="RewardZoneScene" component={RewardZoneScene} />
      {(Platform.OS !== 'ios') && <Stack.Screen name="SavedShorts" component={SavedShorts} />}
      <Stack.Screen name="ItemNotFound" component={ItemNotFound} />
      <Stack.Screen name="MagazineScene" component={MagazineScene} />
      {(Platform.OS !== 'ios') && <Stack.Screen name="Shorts" component={Shorts} />}
      <Stack.Screen name="Sales" component={Sales} />
      <Stack.Screen name="ThankYouPage" component={ThankYouPage} />
      <Stack.Screen name="BuyAgainScene" component={BuyAgainScene} />
      <Stack.Screen name="SavedContent" component={SavedContent} />
      <Stack.Screen name="SellOnDentalkart" component={SellOnDentalkart} />
      <Stack.Screen name="ReviewListScene" component={ReviewListScene} />
      <Stack.Screen
        name="CustomerPhotosScene"
        component={CustomerPhotosScene}
      />
      <Stack.Screen
        name="ChangeEmailOrMobileOtp"
        component={ChangeEmailOrMobileOtp}
      />
      <Stack.Screen name="ChangePassword" component={ChangePassword} />
      <Stack.Screen
        name="ChangeEmailOrMobile"
        component={ChangeEmailOrMobile}
      />
      <Stack.Screen name="CouponsScene" component={CouponsScene} />
      <Stack.Screen
        name="OrderReturnListScene"
        component={OrderReturnListScene}
      />
      <Stack.Screen
        name="OrderReturnTermsScene"
        component={OrderReturnTermsScene}
      />
      <Stack.Screen name="VerifyEmailScene" component={VerifyEmailScene} />
      <Stack.Screen
        name="ForgetPasswordScene"
        component={ForgetPasswordScene}
      />
      {/* <Stack.Screen
        name="NetworkLoggerScreen"
        component={NetworkLoggerScreen}
      /> */}
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  tabFlex: {
    alignItems: 'center',
    justifyContent: 'center',
    width: Sizes.screenWidth * 0.2,
  },
});

export default Routes;

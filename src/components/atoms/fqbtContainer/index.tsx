import React, {useRef, useState} from 'react';
import {View, FlatList, Animated, Dimensions} from 'react-native';
import {t} from 'i18next';
import Label from '../label';
import Spacer from '../spacer';
import stylesWithOutColor from './style';
import {Button} from 'components/molecules';
import {useTheme} from '@react-navigation/native';
import ImageIcon from '../imageIcon';
import HorizontalScrollBar from '../horizontalScroll';
import {checkDevice} from 'utils/utils';
import {useMemo} from 'react';

interface Product {
  id: number;
  is_in_stock: boolean;
}
interface FrequentlyBoughtTogetherProps {
  frequentlyBoughtProducts: Product[];
  totalPrice: number;
  renderProductItemFqbt: any;
  selectedItems: ProductData[];
  addToCartButton: any;
}

const FrequentlyBoughtTogether: React.FC<FrequentlyBoughtTogetherProps> = ({
  frequentlyBoughtProducts,
  totalPrice,
  selectedItems,
  renderProductItemFqbt,
  addToCartButton,
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const flatListRef = useRef(null);
  const scrollX = useRef(new Animated.Value(0)).current;
  const [scrollBarWidth, setScrollBarWidth] = useState(0);
  const [listWidth, setListWidth] = useState(0);
  const {width: screenWidth} = Dimensions.get('window');

  const handleScroll = Animated.event(
    [{nativeEvent: {contentOffset: {x: scrollX}}}],
    {useNativeDriver: false},
  );

  const handleContentSizeChange = contentWidth => {
    setListWidth(contentWidth);
    if (screenWidth > 0 && contentWidth > screenWidth) {
      const calculatedScrollBarWidth =
        (screenWidth / contentWidth) * (screenWidth / 4);
      setScrollBarWidth(Math.max(calculatedScrollBarWidth, 20));
    }
  };

  const scrollBarPosition = scrollX.interpolate({
    inputRange: [0, Math.max(0, listWidth - screenWidth)],
    outputRange: [0, Math.max(0, screenWidth / 6 - scrollBarWidth)],
    extrapolate: 'clamp',
  });
  return (
    <>
      <Spacer size="l" />
      <View style={styles.subProductNameView}>
        <Label
          text={t('PDP.frequentlyMsg')}
          size="l"
          weight="600"
          color="text"
        />
        <Spacer size="xm" />
        <View style={styles.feedbackMainView}>
          <View style={styles.hideAddToCartView}>
            <FlatList
              horizontal
              ref={flatListRef}
              showsHorizontalScrollIndicator={false}
              data={frequentlyBoughtProducts}
              keyExtractor={(_, i) => i.toString()}
              renderItem={({item, index}) => {
                return renderProductItemFqbt(item, 0.43, 'small', index);
              }}
              ItemSeparatorComponent={() => (
                <View style={styles.alignmentCenter}>
                  <ImageIcon icon="plus" tintColor="text" size="xxl" />
                </View>
              )}
              onScroll={handleScroll}
              scrollEventThrottle={8}
              onContentSizeChange={contentWidth =>
                handleContentSizeChange(contentWidth)
              }
            />
            <Spacer size="l" />
          </View>
          <View style={styles.buyBothView}>
            <Label text={t('PDP.buyBoth')} size="l" weight="500" color="text" />
            <Label
              text={`${t('PDP.rupee')}${totalPrice}`}
              style={styles.infoBottomIcon}
              size="l"
              weight="700"
              color="text"
            />
          </View>
          <Spacer size="l" />
          <View style={styles.fqbtBtn}>
            <Button
              onPress={() => {
                selectedItems.forEach(item => {
                  addToCartButton(item, selectedItems?.length);
                });
              }}
              radius="xms"
              size="large"
              selfAlign="stretch"
              weight="500"
              text={`${t('buttons.add')} ${selectedItems.length} ${t(
                'PDP.iCart',
              )}`}
              type="secondary"
              labelColor="whiteColor"
              iconLeft="cart"
              iconSize="xxl"
              tintColor="whiteColor"
              iconStyle={styles.fqbtIcon}
              style={checkDevice() && styles.buttonWidth}
            />
          </View>
        </View>
        {scrollBarWidth >= 0 && (
          <HorizontalScrollBar
            activeColor={colors.blue3}
            scrollBarWidth={scrollBarWidth}
            scrollBarPosition={scrollBarPosition}
          />
        )}
      </View>
    </>
  );
};

export default FrequentlyBoughtTogether;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    imageModelMain: {
      backgroundColor: colors.blueLagoon,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    subImageView: {
      backgroundColor: colors.whiteColor,
      width: '100%',
      borderTopLeftRadius: Sizes.m,
      borderTopRightRadius: Sizes.m,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x60,
    },
    btnStyle: {
      width: '40%',
      height: Sizes.xx4l,
    },
    btnView: {
      flexDirection: 'row',
      paddingTop: Sizes.x56,
      paddingBottom: Sizes.x44,
      marginLeft: Sizes.xms,
    },
    filterListView: {
      paddingTop: Sizes.m,
    },
    paymentView: {
      paddingVertical: Sizes.mx,
      backgroundColor: colors.background,
    },
    paymentSubView: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xm,
      marginTop: Sizes.l,
    },
    flex: {
      flex: Sizes.x,
    },
    payCashView: {
      width: Sizes.ex90,
      height: Sizes.l,
    },
    mainBtnStyle: {
      width: '100%',
      alignItems: 'flex-end',
    },
    rePayView: {
      paddingVertical: Sizes.xl,
    },
    paymentMView: {
      paddingHorizontal: Sizes.l,
    },
  });

export default styles;

declare module 'react-native-config' {
export interface NativeConfig {
    WEBSITE_URL?: string;
    API_BASE_URL?: string;
    API_STAGE_BASE_URL?: string;
    API_STAGE_ADMIN_URL?: string;
    FEEDS_API_BASE_URL?: string;
    X_API_KEY: string;
    SECRETKEY: string;
    IMAGE_BASE_URL?: string;
    SUB_IMAGE_BASE_URL?:string;
    REFERRAL_X_API_KEY?:string
    GoogleLoginConfigs?:string
    supportNumber?:string
    Version?:string
    clarity?:string
    termsAndConditions?:string
    privacyPolicy?:string
    supportEmail?:string
    phoneNumber?:string
    videoThumbnails?:string
    mapPlaceKeyAndroid?:string
    mapPlaceKeyIOS?:string
    GraphQL_URL?:string
  }

  export const Config: NativeConfig;
  export default Config;
}
import React, {useCallback, useState, useRef} from 'react';
import {TouchableOpacity, View, Platform} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {RouteProp, useTheme} from '@react-navigation/native';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {Label, Spacer, OTPTextView} from 'components/atoms';
import {t} from 'i18next';
import {OtpInput} from 'react-native-otp-entry';
import {useDispatch} from 'react-redux';
import {getUserInfo, setLoading} from 'app-redux-store/slice/appSlice';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {useSelector} from 'react-redux';
import {Formik} from 'formik';
import CountDown from 'react-native-countdown-component';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {userLogin, verifyOTP} from 'services/auth';
import {otpVerifyValidationSchema} from 'utils/validationError';
import {openWhatsApp} from 'utils/utils';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, ' ChangeEmailOrMobileOtp'>;
};

const ChangeEmailOrMobileOtp = ({navigation, route}: Props) => {
  const {isEditType, EmailOrMobileValue} = route.params;
  const {userInfo, whatsAppLink} = useSelector((state: RootState) => state.app);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const [editableAccess, setEditableAccess] = useState(false);
  const [error, setError] = useState(false);
  const [shouldShow, setShouldShow] = useState(true);
  // const [otp, setOtp] = useState('');
  const [lastProcessedLength, setLastProcessedLength] = useState(0);
  const otpRef = useRef(null);

  const updateMobileEmail = useCallback(
    async values => {
      dispatch(setLoading(true));
      setEditableAccess(true);
      const {data, status} = await verifyOTP({
        recipient: EmailOrMobileValue.emailOrPhone,
        credential: values.otp,
        authentication_type: EmailOrMobileValue.type,
        verification_type: 'otp',
        action:
          EmailOrMobileValue.type === 'email'
            ? 'email_update'
            : 'mobile_update',
      });
      dispatch(setLoading(false));

      if (status && data) {
        dispatch(getUserInfo(userInfo));
        showSuccessMessage(data.message);
        navigation.navigate('ProfileDetails');
      } else {
        setEditableAccess(false);
        showErrorMessage(data.message);
        setError(data.message);
        const match = data?.message?.match(
          /(\d+) wrong attempts, (\d+) more left/,
        );
        if (match?.length > 1 && parseInt(match[1]) === 5) {
          navigation.pop(2);
        }
      }
    },
    [
      dispatch,
      EmailOrMobileValue.emailOrPhone,
      EmailOrMobileValue.type,
      userInfo,
      navigation,
    ],
  );

  // *************resend OTP************************
  const sendOtpMobileEmail = async () => {
    dispatch(setLoading(true));
    const {data, status} = await userLogin({
      recipient: EmailOrMobileValue.emailOrPhone,
      action:
        EmailOrMobileValue.type === 'email' ? 'email_update' : 'mobile_update',
      authentication_type: EmailOrMobileValue.type,
    });
    dispatch(setLoading(false));
    if (status && data) {
      setShouldShow(true);
      if (data.message) {
        showSuccessMessage(data?.message);
      }
    } else {
      showErrorMessage(data.message);
      setError(data.message);
    }
  };
  const completeOtpTimer = () => {
    setShouldShow(false);
  };

  // Enhanced function to handle OTP code processing with paste support
  const processOtpCode = useCallback(
    (code: string, setFieldValue: Function) => {
      const sanitizedCode = code.replace(/[^0-9]/g, '');
      const finalCode = sanitizedCode.slice(0, 6);

      setFieldValue('otp', finalCode);
      // setOtp(finalCode);
      setLastProcessedLength(finalCode.length);

      // Clear errors when user starts typing/pasting
      setError('');
    },
    [],
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        backButton
        navigation={navigation}
        text={t('profile.personalInfo')}
      />
      <Spacer size="l" />
      <View style={styles.updateEmailLabel}>
        {isEditType === 'password' ? (
          <Label
            text={t('profile.changePasswordStep')}
            size="mx"
            weight="400"
            lineHeight="xxl"
            textTransform="capitalize"
          />
        ) : (
          <View style={styles.stepView}>
            <Label
              text={`${t('profile.updateYour')} ${
                isEditType === 'email'
                  ? t('profile.emailID')
                  : t('profile.mobileNumber')
              }`}
              size="l"
              weight="400"
              lineHeight="xxl"
              color="categoryTitle"
              fontFamily="Regular"
            />
            <Spacer size="xm" type="Horizontal" />
            <Label
              text={`${t('profile.steps2')}`}
              size="m"
              fontFamily="Regular"
              color="text2"
              textTransform="capitalize"
            />
          </View>
        )}
      </View>

      <Spacer size="l" />
      <View style={styles.subContainer}>
        <Label
          text={`${t('profile.otpSend')} ${
            EmailOrMobileValue?.emailOrPhone || '*******@gmail.com'
          }`}
          style={styles.otherlabel}
          weight="400"
          size="m"
        />
        <Spacer size="l" />
        <Label
          text={t('signUp.otpClick')}
          size="l"
          weight="500"
          style={styles.otplabel}
        />
        <Spacer size="l" />
        <Formik
          initialValues={{otp: ''}}
          validationSchema={otpVerifyValidationSchema}
          onSubmit={updateMobileEmail}>
          {({handleSubmit, setFieldValue, values, errors, isValid}) => (
            <>
              <View style={styles.otpView}>
                <OtpInput
                  numberOfDigits={6}
                  focusColor={errors.otp ? colors.textError : colors.grey}
                  focusStickBlinkingDuration={500}
                  onTextChange={code => processOtpCode(code, setFieldValue)}
                  onFilled={code => processOtpCode(code, setFieldValue)}
                  textInputProps={{
                    accessibilityLabel: 'One-Time Password',
                  }}
                  theme={{
                    containerStyle: styles.otpInputContainer,
                    inputsContainerStyle: styles.inputsContainer,
                    pinCodeContainerStyle: [
                      styles.otpInput,
                      errors.otp && styles.error,
                    ],
                    pinCodeTextStyle: styles.pincodeStyle,
                  }}
                  type='numeric'
                />
              </View>
              <Spacer size="l" />
              {!!errors && (
                <Label
                  color="textError"
                  size="m"
                  text={t(errors.otp as string)}
                />
              )}
              {!!error && (
                <>
                  <Spacer size="sx" />
                  <Label
                    style={styles.otpErrorLabel}
                    text={t(
                      !error
                        ? (error as unknown as string)
                        : error
                        ? String(error).replace('Error: GraphQL error:', '')
                        : '',
                    )}
                    color="textError"
                    size="m"
                  />
                </>
              )}
              <Spacer size="l" />
              <Button
                onPress={handleSubmit}
                style={styles.subCautionerBox}
                text={t('profile.verifyOTP')}
                labelSize="l"
                type={!isValid === true ? 'disabled' : 'secondary'}
                radius="sx"
                labelColor="whiteColor"
                weight="500"
              />
            </>
          )}
        </Formik>
      </View>
      <Spacer size="x6l" />
      {shouldShow ? (
        <View style={styles.resendOtpTimer}>
          <CountDown
            resendOtpApi={sendOtpMobileEmail}
            until={30}
            size={12}
            onFinish={completeOtpTimer}
            digitStyle={styles.digitView}
            digitTxtStyle={styles.digitTxtStyle}
            timeToShow={['M', 'S']}
            timeLabels={{m: null, s: null}}
            showSeparator={true}
            separatorStyle={styles.separatorStyle}
          />
        </View>
      ) : (
        <></>
      )}

      <Spacer size="l" />

      <View style={styles.resendOtpView}>
        <TouchableOpacity onPress={sendOtpMobileEmail} disabled={shouldShow}>
          <Label
            text={t('otp.ResendOtp')}
            size="mx"
            color={shouldShow ? 'lightDisabled' : 'newSunnyOrange'}
          />
        </TouchableOpacity>
        <Spacer size="xm" />
        <Label text={t('otp.or')} size="mx" color="newSunnyOrange" />
        <Spacer size="xm" />
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Label
            text={
              isEditType === 'email' ? t('otp.editMail') : t('otp.editNumber')
            }
            size="mx"
            color={editableAccess ? 'lightDisabled' : 'newSunnyOrange'}
          />
        </TouchableOpacity>
        <Spacer size="l" />
        <TouchableOpacity
          style={styles.shouldShowView}
          onPress={() => openWhatsApp(whatsAppLink?.app_link)}>
          <Label
            text={t('otp.needHelp')}
            size="mx"
            color={editableAccess ? 'lightDisabled' : 'categoryTitle'}
            weight="400"
          />
          <Spacer size="s" type="Horizontal" />
          <FastImage
            resizeMode="contain"
            style={styles.coinImage}
            source={editableAccess ? Icons.whatsappGray : Icons.whatsAppIcon}
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default ChangeEmailOrMobileOtp;

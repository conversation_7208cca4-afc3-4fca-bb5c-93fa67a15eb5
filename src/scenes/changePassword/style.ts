import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    subContainer: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.l,
      paddingTop: Sizes.m,
    },
    updateEmailLabel: {
      backgroundColor: colors.gray10,
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.xm,
      color: colors.categoryTitle,
    },
    dotStyle: {
      flexDirection: 'row',
      marginBottom: Sizes.s,
    },
    buttonView: {
      width: '100%',
      borderRadius: Sizes.sx,
      marginBottom: Sizes.xms,
      height: Sizes.x7l,
    },
    passwordInput: {
      color: colors.newPrimary,
      fontSize: Sizes.mx,
    },
    inputView: {
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
    },
    flexOne: {
      flex: Sizes.x,
    },
    noteDes: {
      flex: Sizes.x,
      marginTop: -5,
    },
    dotView: {
      backgroundColor: colors.text2,
      height: Sizes.s,
      width: Sizes.s,
      borderRadius: Sizes.xs,
      marginHorizontal: 5,
      marginRight: Sizes.xms,
      marginTop: 3,
    },
  });

export default styles;

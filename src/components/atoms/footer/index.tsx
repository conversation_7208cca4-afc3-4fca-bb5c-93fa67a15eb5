import {useTheme} from '@react-navigation/native';
import Icons from 'common/icons';
import {Button} from 'components/molecules';
import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {
  ViewProps,
  View,
  TabBarIOSProps,
  TextProps,
  TextStyle,
  FlexAlignType,
} from 'react-native';
import stylesWithOutColor from './style';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Sizes} from 'common';
import {Label} from 'components/atoms';
import {t} from 'i18next';
import {useMemo} from 'react';

export type ButtonProps = {
  hidden: boolean;
  text: string;
  disabled?: boolean;
  onPress: () => void;
  type?: 'primary' | 'secondary' | 'bulk' | 'bordered';
  iconRight?: keyof typeof Icons | null;
  fastImageLeft?: keyof typeof Icons | null;
  iconLeft?: keyof typeof Icons | null;
  radius?: keyof typeof Sizes;
  labelSize?: keyof typeof Sizes;
  size?: 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large';
  selfAlign?: FlexAlignType;
  weight?: TextStyle['fontWeight'];
  labelColor?: keyof Theme['colors'];
  borderColor?: keyof Theme['colors'];
  filterCounts?: boolean;
  filterCountText?: string;
  iconSize?: keyof typeof Sizes;
  loading?: boolean;
  fastImageStyle?: ViewProps['style'];
  ghost?: boolean;
  backgroundColor?: string;
  height?: number | string;
  borderWidth?: number | string;
  width?: string | number;
  filterCountStyle?: string;
  labelStyle?: TextProps['style'];
  tintColor?: TabBarIOSProps | ['tintColor'];
  fontFamily?: string;
  subText?: string;
};

type Props = {
  style?: ViewProps['style'];
  buttonLabel?: ButtonProps['labelStyle'];
  shadowProps?: boolean;
  footerStyle?: ViewProps['style'];
  borderColor?: keyof Theme['colors'];
  buttons: ButtonProps[];
  separator?: () => React.ReactElement;
  useInsets?: boolean;
};

const FooterButton = ({
  buttons,
  separator,
  borderColor,
  shadowProps = true,
  footerStyle,
  useInsets = false,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const insets = useSafeAreaInsets();

  const filterSortText = (text: string) => {
    switch (text) {
      case 'rcm':
        return t('filters.recommended');
      case 'ASC':
        return t('filters.priceLH');
      case 'DSC':
        return t('filters.priceHL');
      case 'price_lh':
        return t('filters.priceLH');
      case 'price_hl':
        return t('filters.priceHL');
      default:
        return '';
    }
  };

  return (
    <>
      <View
        style={[
          styles.filtersFooter,
          shadowProps && styles.shadowProp,
          useInsets && {paddingBottom: insets.bottom},
          footerStyle,
        ]}>
        {buttons.reduce((acc, button, index) => {
          if (button?.hidden ?? false) return acc;
          acc.push(
            <View key={index.toString()} style={styles.container}>
              <Button borderColor={borderColor} {...button} />
              {button?.subText && (
                <Label
                  text={filterSortText(button?.subText)}
                  size="m"
                  fontFamily="Regular"
                  color="categoryTitle"
                  style={styles.sortSubText}
                />
              )}
            </View>,
          );

          if (separator && index !== buttons.length - 1) {
            acc.push(
              React.cloneElement(separator(), {
                key: `separator-${index}`,
              }),
            );
          }

          return acc;
        }, [] as React.ReactNode[])}
      </View>
    </>
  );
};

export default FooterButton;

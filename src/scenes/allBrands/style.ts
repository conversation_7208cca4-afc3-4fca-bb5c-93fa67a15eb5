import {StyleSheet} from 'react-native';
import {Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {checkDevice} from 'utils/utils';

const styles = (colors: CustomTheme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background4,
    },
    subContainer: {
      paddingHorizontal: Sizes.xm,
    },
    featuredView: {
      flexDirection: 'row',
      alignItems: 'center',
      height: Sizes.ex180,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.mx,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingHorizontal: Sizes.xms,
    },
    sliderView: {
      alignItems: 'center',
      flex: Sizes.x,
      width: Sizes.screenWidth - Sizes.ex210,
      justifyContent: 'flex-end',
      marginLeft: Sizes.xl,
    },
    sliderItem: {
      width: Sizes.screenWidth * 0.9,
      flex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
    },
    sectionCardView: {
      backgroundColor: colors.whiteColor,
      paddingVertical: Sizes.xm,
      paddingHorizontal: Sizes.m,
    },
    searchBarStyle: {
      width: '95%',
      height: Sizes.xx4l,
      alignSelf: 'center',
      borderRadius: Sizes.xxl,
      paddingHorizontal: Sizes.m,
      borderColor: colors.border,
    },
    alphaView: {
      borderRadius: Sizes.x4l,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.m,
      flexDirection: 'row',
    },
    alphaContainer: {
      flex: Sizes.x,
      marginTop: Sizes.xs,
    },
    alphaOrderView: {
      width:
        (DeviceWidth - (checkDevice() ? Sizes.x56 : Sizes.ex90)) /
        (checkDevice() ? Sizes.x26 : Sizes.m + Sizes.x),
      alignItems: 'center',
      justifyContent: 'center',
      height: Sizes.l,
    },
    activeAlpha: {
      backgroundColor: colors.grey11,
      borderRadius: Sizes.xm,
      height: Sizes.l,
      width: Sizes.l,
      alignItems: 'center',
      justifyContent: 'center',
    },
    flexOne: {
      flex: Sizes.x,
    },
    brandFView: {
      alignItems: 'center',
    },
    branCharBottomView: {
      position: 'absolute',
      top: Sizes.x3l,
      left: Sizes.xm,
      zIndex: -Sizes.x,
      width: Sizes.xxxl,
      height: Sizes.s + Sizes.x,
    },
    alphaLine: {
      borderColor: colors.grey2,
      flex: Sizes.x,
      borderWidth: Sizes.z,
    },
    brandCardView: {
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.mx,
      paddingHorizontal: Sizes.xm,
      shadowColor: colors.blackColor,
      shadowOffset: {
        width: 0,
        height: Sizes.x,
      },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
      elevation: Sizes.xs,
      paddingTop: Sizes.l,
      paddingBottom: Sizes.xl,
      marginBottom: Sizes.m,
    },
    brandItemView: {
      paddingTop: Sizes.l,
      alignItems: 'center',
      width:
        (DeviceWidth - (checkDevice() ? Sizes.x6l : Sizes.x4l)) /
        (checkDevice() ? Sizes.s : 3),
    },
    sectionTitleView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    sTitle: {
      marginLeft: Sizes.xm,
    },
    brandImgView: {
      borderRadius: Sizes.xm,
      flex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
    },
    brandSectionView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      width: DeviceWidth,
    },
    brandImg: {
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      width:
        (DeviceWidth - (checkDevice() ? Sizes.x6l : Sizes.x4l)) /
        (checkDevice() ? Sizes.s : 3),
      height: Sizes.ex84,
    },
    imageStyle: {
      width:
        (DeviceWidth - (checkDevice() ? Sizes.x6l : Sizes.x4l)) /
          (checkDevice() ? Sizes.s : 3) -
        Sizes.x3l,
      height: Sizes.x9l,
      resizeMode: 'contain',
      backgroundColor: 'transparent',
    },
    renderImg: {
      width: Sizes.ex0 + Sizes.xs,
      height: Sizes.x42,
    },
    searchInputStyle: {
      paddingLeft: Sizes.xm,
      justifyContent: 'center'
    },
    emptyView: {
      marginTop: Sizes.x6l,
      alignItems: 'center',
      justifyContent: 'center',
    },
    dualShadow: {
      shadowColor: colors.shadowColor2,
      shadowOffset: {
        width: 0,
        height: Sizes.s,
      },
      shadowOpacity: 0.27,
      shadowRadius: 4.65,
      elevation: Sizes.sx,
    },
    brandChar: {
      marginTop: -Sizes.xs,
    },
    fRow: {
      flexDirection: 'row',
    },
  });
export default styles;

import React from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {RouteProp} from '@react-navigation/native';
import {Header} from 'components/molecules';
import ErrorHandler from 'utils/ErrorHandler';
import { View } from 'react-native';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'Offers'>;
};

const Offers = ({navigation}: Props) => {
  const TAG='Offers'
  return (
    <SafeAreaView>
       <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
      <Header backButton navigation={navigation} text="Offers" />
      </ErrorHandler>
    </SafeAreaView>
  );
};

export default Offers;

import React, {memo, useCallback, useState} from 'react';
import {View, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Label, Separator, Spacer, Tag} from 'components/atoms';
import {FlatList} from 'react-native';
import {Button} from 'components/molecules';
import {dateTimeFormat} from 'utils/formatter';
import {ActivityIndicator} from 'react-native';
import {t} from 'i18next';
import {useMemo} from 'react';

const ListFooterComponent = ({onPressPostQuestion}) => (
  <>
    <Spacer size="m" />
    <View style={{alignItems: 'center'}}>
      <Label
        text="Have doubts regarding this product?"
        size="mx"
        weight="500"
        color="text2"
      />
      <Spacer size="l" />
      <Button
        borderColor="categoryTitle"
        type="bordered"
        onPress={onPressPostQuestion}
        text="Post Your Question"
        labelSize="mx"
        paddingHorizontal="m"
        radius="xm"
        weight="500"
        labelColor="categoryTitle"
        size="extra-small"
      />
    </View>
  </>
);

type Props = {
  useInsets?: boolean;
  visible: boolean;
  onClose?: (visible: boolean) => void;
  onPressPostQuestion: (visible: boolean) => void;
  content?: React.ReactElement;
  flex?: number;
  review: ProductReviewResponse;
  faqLoading: boolean;
  getFaqLoading: boolean;
  editQuestion: (props: {like?: number; dislike?: number; _id: string}) => void;
  handleTextChange: (text: string) => void;
};

const ReviewListModal = ({
  visible,
  onClose,
  useInsets = false,
  flex = 1,
  editQuestion,
  faqLoading,
  getFaqLoading,
  review,
  handleTextChange,
  onPressPostQuestion,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const insets = useSafeAreaInsets();
  const [changeFilter, setChangeFilter] = useState(false);
  const [currentText, setCurrentText] = useState<any>('most_recent');
  const [expandedReviews, setExpandedReviews] = useState<
    Record<number, boolean>
  >({});

  const toggleReadMore = useCallback((index: number) => {
    setExpandedReviews(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  }, []);

  const onPostQuestion = useCallback(() => {
    onClose?.(!visible);
    onPressPostQuestion(true);
  }, [visible]);

  return (
    <Modal
      onBackButtonPress={() => onClose?.(!visible)}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={[styles.flexContainer]}>
        <View style={styles.centeredView}>
          <View style={styles.modalCloseBtnContainer} />
          <View
            style={[
              styles.modelBg,
              {flex: flex},
              useInsets && {paddingBottom: insets.bottom},
            ]}>
            <View style={styles.mainQuestionsView}>
              <View style={styles.questionsView}>
                <View>
                  <Label
                    text="Customer Review"
                    size="l"
                    weight="500"
                    color="text"
                  />
                  <Spacer size="s" />
                </View>
                <TouchableOpacity onPress={() => onClose?.(!visible)}>
                  <ImageIcon icon="cross" size="xxxl" />
                </TouchableOpacity>
              </View>
              <Spacer size="m" />
              <TouchableOpacity
                style={styles.searchView}
                onPress={() => {
                  setChangeFilter(true);
                  setTimeout(() => {
                    setChangeFilter(false);
                  }, 500);
                  let text: any =
                    currentText == 'most_recent'
                      ? 'most_relevant'
                      : 'most_recent';
                  setCurrentText(text);
                  handleTextChange(text);
                }}>
                {changeFilter ? (
                  <ActivityIndicator size="small" />
                ) : (
                  <>
                    <ImageIcon
                      icon="sortIcons"
                      tintColor="categoryTitle"
                      size="xx"
                    />
                    <Spacer size="xm" type="Horizontal" />

                    <Label
                      text={
                        currentText == 'most_recent'
                          ? 'Most Relevant'
                          : 'Most Recent'
                      }
                      size="mx"
                      weight="500"
                      color="categoryTitle"
                    />
                  </>
                )}
              </TouchableOpacity>
              <View style={styles.flexView}>
                <FlatList
                  data={review?.reviews || []}
                  keyExtractor={(_, i) => i.toString()}
                  renderItem={({item, index}) => {
                    const isExpanded = expandedReviews[index];
                    return (
                      <>
                        <View style={styles.ratingView}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <Tag
                              style={styles.starIconView}
                              label={(item?.rating || 0).toFixed(1)}
                              icon="starIcon"
                              color="green2"
                              isRightIcon
                            />
                            <Spacer size="xm" type="Horizontal" />
                            <Label
                              text={item?.title}
                              size="mx"
                              weight="600"
                              color="text2"
                            />
                          </View>
                          <Spacer size="xm" />

                          <Label
                            text={
                              item?.content.length > 40 && !isExpanded
                                ? item?.content.substring(0, 40) + '...'
                                : item?.content
                            }
                            size="mx"
                            fontFamily="Medium"
                            color="text2"
                          />
                          <>
                            {item?.content.length > 40 && (
                              <TouchableOpacity
                                onPress={() => toggleReadMore(index)}>
                                <Label
                                  text={
                                    isExpanded
                                      ? ` ${t('faqs.readLess')}`
                                      : ` ${t('faqs.readMore')}`
                                  }
                                  size="mx"
                                  fontFamily="Medium"
                                  color="text"
                                />
                              </TouchableOpacity>
                            )}
                          </>

                          <Spacer size="m" />
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <View style={styles.nicNameView}>
                              <Label
                                text={item?.author?.substring(0, 24)}
                                size="mx"
                                weight="500"
                                color="grey3"
                              />
                              <Spacer size="xm" type="Horizontal" />
                              <Separator
                                color="grey3"
                                Vertical
                                thickness="z"
                                height="l"
                              />
                              <Spacer size="xm" type="Horizontal" />
                              <Label
                                text={dateTimeFormat(item?.date, 'D MMM YYYY')}
                                size="mx"
                                weight="500"
                                color="grey3"
                              />
                            </View>
                          </View>
                        </View>
                      </>
                    );
                  }}
                />
              </View>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default memo(ReviewListModal);

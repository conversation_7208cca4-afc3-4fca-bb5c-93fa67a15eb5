import React, {memo} from 'react';
import {View} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import Label from 'components/atoms/label';
import {useMemo} from 'react';

const Thumb = ({
  fillColor,
  borderColor,
}: {
  fillColor?: keyof Theme['colors'];
  borderColor?: keyof Theme['colors'];
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <>
      <View
        style={[
          styles.root,
          {
            backgroundColor: fillColor ? colors[fillColor] : colors.whiteColor,
            borderColor: borderColor ? colors[borderColor] : colors.primary,
          },
        ]}></View>
    </>
  );
};

export default memo(Thumb);

import { Sizes } from 'common';
import { Dimensions, StyleSheet } from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      // backgroundColor: colors.background,
      // backgroundColor:'red'
    },
    body: {
      // flex: Sizes.x,
            padding: Sizes.xl,

    },
    imageBackGround: {
      // flex: Sizes.x,
      padding: Sizes.xl,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    containerForm: {
      flex: Sizes.x,
    },
    newOrCreateAccView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.sx,
      textAlign: 'center',
    },
    separatorLine: {
      flex: Sizes.x,
      height: Sizes.x,
      backgroundColor: colors.smoothGrey,
    },
    itemsCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemsCenters: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '20%'
    },
    errorContainer: {
      marginTop: Sizes.s,
      marginLeft: Sizes.xs,
    },
    inputView: {
      borderWidth: Sizes.x,
      color: colors.grey2,
      borderColor: colors.grey2,
    },
    inputStyleView: {
      fontSize: Sizes.mx,
      color: colors.text,
      flex: Sizes.x,
    },
    crossIconStyle: {
      height: Sizes.x3l,
      width: Sizes.x3l,
      position: 'absolute',
      top: 15,
      right: 20,
      zIndex: 2,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xl,
    },
    thankYouImage: {
      height: '100%',
      width: '100%',
      position: 'absolute',
      resizeMode: 'cover',
    },
        imageFullWidth: {
      // marginTop: 200,
      width: '100%',
    },
    logoView: {
      alignSelf: 'center',
      padding:0,
      margin:0,
      width: Dimensions.get('window').width,
      height: 69,
      // marginTop: 70,
    },
        signUpLogin: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 5,
      // marginTop: Sizes.mx,
    },
    coinImage: {
      height: 16,
      width: 16,
      marginLeft:4
    },
        loginSubTitle:{
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
        googleBtn: {
      color: colors.newPrimary,
    },
    helpView: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      // width: '20%'
    },
  });
export default styles;

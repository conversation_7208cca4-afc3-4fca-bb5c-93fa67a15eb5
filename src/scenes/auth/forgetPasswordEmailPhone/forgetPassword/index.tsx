import React, { useCallback, useState } from 'react';
import { View, TouchableOpacity, StatusBar, Platform, Text, KeyboardAvoidingView, ScrollView } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Formik } from 'formik';
import { Button, ConnectWithUs, CustomStatusBar, PhoneInputText } from 'components/molecules';
import stylesWithOutColor from './style';
import {
  Label,
  Spacer,
  Link,
  Separator,
  LoginGoogle,
  ImageIcon,
} from 'components/atoms';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import {
  generateNewCart,
  getUserInfo,
  setIsLoggedIn,
  setLoading,
} from 'app-redux-store/slice/appSlice';
import { CommonActions, RouteProp, useTheme } from '@react-navigation/native';
import { showErrorMessage, showSuccessMessage } from 'utils/show_messages';
import { socialLogin, verifyOTP } from 'services/auth';
import tokenClass from 'utils/token';
import { RootStackParamsList } from 'routes';
import { forgotValidationSchema } from 'utils/validationError';
import { openWhatsApp } from 'utils/utils';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import ErrorHandler from 'utils/ErrorHandler';
import { useMemo } from 'react';
import { debugError } from 'utils/debugLog';
import TextInputBox from 'components/molecules/textInputBox';
import LinearGradient from 'react-native-linear-gradient';
import { Sizes } from 'common';
import { AnalyticsEvents } from 'components/organisms';
// import { getPasswordRules } from '../validation';

type LoginWithOtpProps = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ForgetPasswordScene'>;
};

const ForgetPasswordScene = ({ navigation, route }: LoginWithOtpProps) => {
  const TAG = 'ForgotPasswordScreen';
  const { credential, mobileNumber, back } = route.params;
  const { t } = useTranslation();
  const [apiError, setApiError] = useState(null);
  const [isPasswordSecure, setIsPasswordSecure] = useState(true);
  const [isConfirmPasswordSecure, setIsConfirmPasswordSecure] = useState(true);
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const { whatsAppLink } = useSelector((state: RootState) => state.app);
  const [editableAccess, setEditableAccess] = useState(false);
  // const [rules, setRules] = useState([
  //   { label: 'Use 8 or more characters', isValid: false },
  //   { label: 'Mix uppercase & lowercase letters', isValid: false },
  //   { label: 'Include at least one number', isValid: false },
  //   { label: 'Add a special symbol like @, #, or $', isValid: false },
  // ]);
  // const isAllRulesValid = useMemo(() => {
  //   return rules.every(rule => rule.isValid);
  // }, [rules]);
  const loginSubmit = useCallback(async (values: any) => {
    // dispatch(setLoading(true));
    setEditableAccess(true);
    // console.log(isAllRulesValid,'isAllRulesValid**')
    // if(isAllRulesValid){
    const { data } = await verifyOTP({
      recipient: mobileNumber,
      action: 'forgot_password',
      verification_type: 'password',
      authentication_type: mobileNumber.includes('@') ? 'email' : 'mobile',
      credential: credential,
      new_password: values.password,
    });
    dispatch(setLoading(false));
    if (data.token) {
      tokenClass.setToken(data.token);
      setApiError(null);
      AnalyticsEvents(
        'USER_LOGGED_IN',
        'Login-Forgot-Password',
        data,
        {},
        false,
      );
      navigation.navigate('Tab', { screen: 'Shop' });
      showSuccessMessage(data?.message);
      dispatch(setIsLoggedIn(true));
      dispatch(getUserInfo());
    } else {
      setEditableAccess(false);
      dispatch(setLoading(false));
      setApiError(data?.message);
      showErrorMessage(data?.message);
    }

  }, []);

  // ================google login====================
  const getUserData = useCallback(
    async (googleResponse: any) => {
      try {
        dispatch(setLoading(true));

        const { data } = await socialLogin({
          entity_type: 'google',
          token: googleResponse.idToken,
        });

        if (data?.token) {
          setApiError(null);

          // Run async actions in parallel for better performance
          await Promise.all([
            tokenClass.setToken(data.token),
            dispatch(setIsLoggedIn(true)),
            dispatch(getUserInfo()),
            dispatch(generateNewCart()),
          ]);
          AnalyticsEvents(
            'SIGN_UP_LOGIN_GOOGLE',
            'Sign Up/Login Google',
            data,
            {},
            false,
          );
          showSuccessMessage(t('toastMassages.loginSuccess'));
          navigation.navigate('Tab', { screen: 'Shop' });
        } else {
          setApiError(data?.token || 'Login failed');
        }
      } catch (error) {
        debugError('Login Error:', error);
        setApiError('Login failed. Please try again.');
      } finally {
        dispatch(setLoading(false));
      }
    },
    [navigation, dispatch]
  );

  // const getRules = (password: string) => {
  //   const updatedRules = [
  //     { label: 'Use 8 or more characters', isValid: password.length >= 8 },
  //     { label: 'Mix uppercase & lowercase letters', isValid: /(?=.*[a-z])(?=.*[A-Z])/.test(password) },
  //     { label: 'Include at least one number', isValid: /[0-9]/.test(password) },
  //     { label: 'Add a special symbol like @, #, or $', isValid: /[@#$%^&*!]/.test(password) },
  //   ];
  //   setRules(updatedRules);
  // };

  return (
    <>
      <CustomStatusBar backgroundColor={colors.sunnyOrange3} />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.container}>
            <View style={[styles.container2]}>
              <Formik
                initialValues={{ password: '', confirmPassword: '' }}
                validationSchema={forgotValidationSchema}
                onSubmit={loginSubmit}>
                {({
                  handleSubmit,
                  handleChange,
                  values,
                  errors,
                  touched,
                  isValid,
                  handleReset,
                }) => (
                  <>
                    <View style={styles.container}>
                      <StatusBar
                        translucent={Platform.OS === 'android'}
                        backgroundColor="transparent"
                      />
                      <View style={styles.containerGif}>
                      <FastImage
                        source={Icons.authGif}
                        style={styles.thankYouImage}
                        resizeMode='cover'
                      /> 
                      <LinearGradient
                        colors={['rgba(255,255,255,0)', 'rgba(255,255,255,1)','rgba(255,255,255,1)']}
                        style={styles.bottomFade}
                      />
                      <TouchableOpacity
                        style={styles.crossIconStyle}
                        onPress={() => {
                          if (back) {
                            back();
                          }
                          navigation.goBack();
                        }}>
                        <ImageIcon icon="whiteCross" size="xxl" />
                      </TouchableOpacity>
                      </View>
                      <View style={styles.body}>
                        <View style={styles.headerContent}>
                          <View style={styles.imageFullWidth}>
                            <ImageIcon
                              style={styles.logoView}
                              icon="dentalkartUpdatedIcon"
                            />

                            <View style={styles.signUpLogin}>
                              <Label
                                text={t('login.logIn')}
                                color="skyBlue23"
                                fontFamily="SemiBold"
                                size="mx"
                              />
                              <Label
                                text={t('signUp.Or')}
                                size="mx"
                                color="skyBlue23"
                                fontFamily="Medium"
                              />
                              <Label
                                text={'Sign Up'}
                                size="mx"
                                color="skyBlue23"
                                fontFamily="SemiBold"
                              />
                            </View>
                          </View>

                        </View>
                        <Spacer type="Vertical" size="xm" />
                        <>
                          <Spacer size="s" />
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <TextInputBox
                              testID="txtForgotPassword"
                              label={t('login.passwordPlace')}
                              placeholderColor={colors.text}
                              autoComplete="off"
                              leftIcon={Icons.lock}
                              tintColorIconRight={
                                !values.password ? 'lightDisabled' : 'text2'
                              }
                              rightIcon={isPasswordSecure ? Icons.eyeHideIcon : Icons.eyeShowIcon}
                              onRightIconPress={() => isPasswordSecure
                                ? setIsPasswordSecure(false)
                                : setIsPasswordSecure(true)}
                              secureTextEntry={isPasswordSecure}
                              // onChangeText={handleChange('password')}
                              onChangeText={(text) => {
                                handleChange('password')(text);
                                // getRules(text);
                              }}
                              errorText={errors.password}
                              value={values.password}
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="xm" />
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <TextInputBox
                              testID="txtForgotPassword"
                              label={t('login.confirmPassword')}
                              placeholderColor={colors.text}
                              autoComplete="off"
                              leftIcon={Icons.lock}
                              tintColorIconRight={
                                !values.password ? 'lightDisabled' : 'text2'
                              }
                              rightIcon={isConfirmPasswordSecure ? Icons.eyeHideIcon : Icons.eyeShowIcon}
                              onRightIconPress={() => isConfirmPasswordSecure
                                ? setIsConfirmPasswordSecure(false)
                                : setIsConfirmPasswordSecure(true)}
                              secureTextEntry={isConfirmPasswordSecure}
                              onChangeText={
                                handleChange('confirmPassword')}
                              errorText={errors.confirmPassword}
                              error={touched.password && errors.confirmPassword
                                ? String(errors.confirmPassword)
                                : undefined}
                              value={values.confirmPassword}
                            />
                          </ErrorHandler>
                        </>
                        <Spacer type="Vertical" size="l" />
                        <ErrorHandler
                          componentName={`${TAG} Button`}
                          onErrorComponent={<View />}>
                          <Button
                            radius="m"
                            // disabled={!isValid}
                            text={t('resetPassword.resetPassword')}
                            onPress={handleSubmit}
                            type={'secondary'}
                            style={
                              {
                                backgroundColor:
                                  (!values?.password || !values?.confirmPassword)
                                    ? colors.grey2
                                    : colors.categoryTitle,
                              }
                            }
                            selfAlign="stretch"
                            labelColor="whiteColor"
                            labelSize="mx"
                            size="large"
                            disabled={!values?.password || !values?.confirmPassword}
                          />
                        </ErrorHandler>
                        <Spacer type="Vertical" size="xm" />
                        <View style={styles.itemsCenter}>
                          <Separator style={styles.separatorLine} />
                          <Label
                            text={t('login.Or')}
                            size="mx"
                            style={styles.labelText}
                          />
                          <Separator style={styles.separatorLine} />
                        </View>
                        <Spacer type="Vertical" size="xm" />

                        <>
                          <ErrorHandler
                            componentName={`${TAG} LoginGoogle`}
                            onErrorComponent={<View />}>
                            <LoginGoogle
                              textStyle={{ color: colors.newPrimary }}
                              getUserData={getUserData}
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="xm" />
                          <ConnectWithUs />
                        </>
                      </View>
                    </View>
                  </>
                )}
              </Formik>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
};
export default ForgetPasswordScene;

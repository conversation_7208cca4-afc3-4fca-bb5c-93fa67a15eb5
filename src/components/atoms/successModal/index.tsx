import React from 'react';
import {View, ViewProps} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer, GradientText} from 'components/atoms';
import stylesWithOutColor from './style';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  notes?: string;
  type?: string;
  titleStyle?: ViewProps['style'];
  descriptionStyle?: ViewProps['style'];
};

const SuccessModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {
    visible,
    onClose,
    title,
    description,
    notes,
    type,
    titleStyle,
    descriptionStyle,
  } = props;

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={() => onClose()}
      content={
        <View style={styles.mainView}>
          <View style={styles.centerView}>
            {type === 'orderSuccess' ? (
              <FastImage source={Icons.orderSuccess} style={styles.imageView} />
            ) : (
              <FastImage
                resizeMode="contain"
                style={styles.emptyImage}
                source={Icons.suggestGif}
              />
            )}
            <Spacer size="mx" />
            <GradientText
              linearColor={[
                colors.blueLine,
                colors.velvetLightPink2,
                colors.purpleMountain,
              ]}
              style={[styles.titleStyle, titleStyle]}>
              {title}
            </GradientText>
            {description && (
              <GradientText
                linearColor={[
                  colors.blueLine,
                  colors.velvetLightPink2,
                  colors.purpleMountain,
                ]}
                style={[styles.desStyle, descriptionStyle]}>
                {description}
              </GradientText>
            )}
            {notes && (
              <>
                <Spacer size="mx" />
                <Label
                  text={notes}
                  size="mx"
                  color="skyBlue22"
                  fontFamily="Bold"
                />
              </>
            )}
          </View>
          <Spacer size="mx" />
        </View>
      }
    />
  );
};

export default React.memo(SuccessModal);

// components/RecentlyViewedItem.tsx

import React, {memo} from 'react';
import {View} from 'react-native';
import {checkDevice} from 'utils/utils';
import ProductCardVertical from '../productCardVertical';
import ErrorHandler from 'utils/ErrorHandler';

type Props = {
  item: any;
  index: number;
  navigation: any;
  TAG: string;
  EMPTY_FREE_PRODUCTS: any;
};

const RecentlyViewedItem = ({
  item,
  index,
  navigation,
  TAG,
  EMPTY_FREE_PRODUCTS,
}: Props) => {
  return (
    <ErrorHandler
      componentName={`${TAG} ProductCardVertical`}
      onErrorComponent={<View />}>
      <ProductCardVertical
        index={index}
        actionBtn={item?.action_btn}
        size="small"
        skuId={item?.sku}
        imageWithBorder={true}
        maxWidth={checkDevice() ? 0.24 : 0.43}
        item={item}
        inStock={item.is_in_stock}
        productType={item?.type}
        maxSaleQty={item?.max_sale_qty}
        demoAvailable={item?.demo_available}
        msrp={item?.msrp}
        isBestSeller={item?.is_best_seller}
        image={item?.media?.mobile_image}
        name={item?.name}
        rewardPoint={item?.reward_points}
        description={item?.short_description}
        rating={(item?.rating === 'null' || item?.average_rating === null
          ? 0
          : Number(item?.rating) || Number(item?.average_rating)
        ).toFixed(1)}
        ratingCount={!!item?.rating_count ? `(${item?.rating_count})` : '(0)'}
        price={item?.price}
        sellingPrice={item?.selling_price}
        currencySymbol={item?.currency_symbol}
        discount={item?.discount?.label}
        navigation={navigation}
        freeProducts={EMPTY_FREE_PRODUCTS}
        showWishlist={true}
      />
    </ErrorHandler>
  );
};

export default memo(RecentlyViewedItem);

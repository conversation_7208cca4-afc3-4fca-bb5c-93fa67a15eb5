import React, {useCallback, useEffect, useState} from 'react';
import {SafeAreaView, TouchableOpacity, View} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {RouteProp, useTheme} from '@react-navigation/native';
import {
  ImageIcon,
  Label,
  ListView,
  Spacer,
  OfferLineAnimation,
} from 'components/atoms';
import {t} from 'i18next';
import DropDownList from 'components/molecules/drop-down-list';
import {FlatList, ScrollView} from 'react-native-gesture-handler';
import {memberShipsFaqApi} from 'services/mambership';
import {useSelector} from 'react-redux';
import {openWhatsApp, htmlReg} from 'utils/utils';
import {CardListLoader} from 'skeletonLoader';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import DropDownListDelete from './component/index'
type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'HelpOrder'>;
};

const HelpOrderScene = ({navigation, route}: Props) => {
  const TAG = 'HelpOrderScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {item} = route.params;
  const insets = useSafeAreaInsets();
  const {whatsAppLink} = useSelector((state: RootState) => state.app);
  const [faqQuestions, setFaqQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState();

  const helpFaqData = async () => {
    try {
      setLoading(true);
      const response = await memberShipsFaqApi({parents: [item]});
      const data = response?.data?.result;

      if (data) {
        setFaqQuestions(data);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    helpFaqData();
  }, []);

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  return (
    <SafeAreaView style={[styles.flex, { paddingTop: insets.top }]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          navigation={navigation}
          backButton
          bagIcon
          searchIcon
          text={t('profile.helpCenter')}
        />
      </ErrorHandler>
      {/* <OfferLineAnimation
        text1={t('helpOrderOptions.offerText')}
        // text1={t('helpOrderOptions.offerText1')}
      /> */}
      {loading ? (
        <CardListLoader />
      ) : (
        <ErrorHandler
          componentName={`${TAG} HelpOrderList`}
          onErrorComponent={<View />}>
          <View style={styles.mainContainer}>
            <FlatList
              data={['']}
              showsVerticalScrollIndicator={false}
              renderItem={() => (
                <ScrollView>
                  <View style={styles.headingView}>
                    <Spacer size="m" />
                    <Label color="text2" text={item} weight="500" size="l" />
                    <Spacer size="m" />
                  </View>
                  <View style={styles.dropDownMenu}>
                    <ListView
                      data={faqQuestions}
                      ItemSeparatorComponent={() => (
                        <Spacer type="Vertical" size="xm" />
                      )}
                      keyExtractor={listViewKeyExtractor}
                      renderItem={({item, index}) => {
                        const isHTML = htmlReg.test(item?.answer);
                        return (
                          <View key={index} style={styles.backColor}>
                            <DropDownList
                              isborder={true}
                              displayHtml={isHTML}
                              item={{
                                question: item?.question,
                                answer: item?.answer,
                              }}
                              // onPress={() =>
                              //   setCurrentIndex(
                              //     currentIndex === index ? undefined : index,
                              //   )
                              // }
                            />
                          </View>
                        );
                      }}
                      />
                      {item ==='Login & My Account' && 
                      <><Spacer size='xm' />
                            <View style={styles.backColor}>
                              <DropDownListDelete
                                isborder={true}
                                />
                            </View>
                          </>}
                    
                  </View>
                </ScrollView>
              )}
            />
            <TouchableOpacity
              onPress={() => openWhatsApp(whatsAppLink?.app_link)}
              style={styles.whatsappIcon}>
              <View style={styles.whatAppBg}>
                <ImageIcon size="xxl" icon="whatsAppIcon" />
              </View>
              <Label
                color="text"
                text={t('helpOrderOptions.chatUs')}
                fontFamily="Medium"
                size="mx"
              />
            </TouchableOpacity>
          </View>
        </ErrorHandler>
      )}
    </SafeAreaView>
  );
};
export default HelpOrderScene;

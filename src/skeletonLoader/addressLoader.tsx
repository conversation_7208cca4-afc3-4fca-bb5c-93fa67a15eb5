import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const AddressLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1830}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="0" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="220" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="440" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="660" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="880" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="1100" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="1320" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="1540" rx="5" ry="5" width={width - 20} height="210" />
        <Rect x={10} y="1760" rx="5" ry="5" width={width - 20} height="50" />
      </ContentLoader>
    </ScrollView>
  );
};

export default AddressLoader;

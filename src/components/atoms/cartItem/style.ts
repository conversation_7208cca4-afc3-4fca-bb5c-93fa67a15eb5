import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    cartItem: {
      padding: Sizes.sx,
      backgroundColor: colors.violet1,
      borderRadius: Sizes.mx,
      alignItems: 'center',
    },
    mainView: {
      borderRadius: Sizes.mx,
    },
    width: {width: Sizes.x34},
    cartContinuer: {
      flexDirection: 'row',
      shadowColor: colors.shadow2,
      shadowOffset: {width: 0, height: Sizes.xs},
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: Sizes.s,
      backgroundColor: colors.background,
      borderRadius: Sizes.mx,
      borderWidth: Sizes.x,
      borderColor: colors.lightGray2,
    },
    cartSubItem: {
      flex: Sizes.x,
      paddingLeft: Sizes.xm,
      paddingVertical: Sizes.xm,
    },
    cartSubContinuer: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    subContainer2: {
      width: '30%',
      padding: Sizes.s,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
      borderRadius: Sizes.m,
      backgroundColor: colors.background,
    },
    imgPlus: {
      width: Sizes.ex84,
      height: Sizes.exl + Sizes.xs,
    },
    cardView: {
      flex: Sizes.x,
    },
    cartBrands: {
      flex: Sizes.x,
      backgroundColor: colors.background,
      justifyContent: 'center',
    },
    rewardPointView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    deliveryTruck: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rowView: {
      flexDirection: 'row',
    },
    underPriceView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    discountStyle: {
      backgroundColor: colors.green4,
      borderRadius: Sizes.s,
      paddingHorizontal: Sizes.xm,
      height: Sizes.xxl,
    },
    closeView: {
      paddingHorizontal: Sizes.xm,
      marginTop: Sizes.s,
      paddingRight: Sizes.m,
    },
    cartQtyView: {
      flex: Sizes.x,
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingRight: Sizes.xms,
    },
    fOne: {
      flex: Sizes.x,
    },
    alignEnd: {
      alignItems: 'flex-end',
    },
    itemFullSizeView: {
      flex: Sizes.x,
      flexDirection: 'row',
      paddingRight: Sizes.sx,
    },
    itemRight: {
      marginRight: Sizes.s,
    },
    freeContinuer: {
      backgroundColor: colors.greenLight,
      width: Sizes.x34,
      justifyContent: 'center',
      borderTopRightRadius: Sizes.mx,
      borderBottomRightRadius: Sizes.mx,
      flexDirection: 'row',
      alignItems: 'center',
    },
    freeSubContinuer: {
      backgroundColor: colors.background,
      width: Sizes.l,
      height: Sizes.l,
      borderRadius: Sizes.xm,
      left: Sizes.xs,
    },
    freeText: {
      transform: [{rotate: '270deg'}],
      width: Sizes.x5l,
      textAlign: 'center',
      paddingBottom: Sizes.xm,
    },
    imgLeft: {marginLeft: Sizes.s},
    errorMsg: {
      marginTop: -Sizes.xs,
    },
    outStockBg: {
      backgroundColor: colors.white1,
      opacity: 0.3,
    },
    outStock: {
      backgroundColor: colors.orient20,
      height: Sizes.x4l,
      width: Sizes.ex90,
      borderRadius: Sizes.sx,
      paddingVertical: 0,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
    },
    blur: {
      backgroundColor: colors.orient20,
    },
    labelBottom: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

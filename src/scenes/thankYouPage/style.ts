import {StyleSheet} from 'react-native';
import {Fonts, Sizes} from 'common';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    image: {
      top: Sizes.ex2l,
      height: Sizes.ex2l + Sizes.xl,
      width: Sizes.ex2l + Sizes.x3l,
    },
    button: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      height: Sizes.x7l,
      width: 234,
      justifyContent: 'center',
      alignContent: 'center',
    },
    labelView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    view: {
      alignItems: 'center',
      position: 'absolute',
      width: '100%',
      borderColor: 'blue',
    },
    mainView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
    },
    section: {
      marginTop: Sizes.ex4l + Sizes.xl,
      flexDirection: 'column',
      position: 'absolute',
      width: '100%',
      paddingHorizontal: Sizes.xx4l,
    },
    titleStyle: {
      textTransform: 'capitalize',
      textAlign: 'center',
      fontSize: Sizes.l,
      fontFamily: Fonts.SemiBold,
    },
  });

export default styles;

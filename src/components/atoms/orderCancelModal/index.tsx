import React, {useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Label, Spacer, DropDown} from 'components/atoms';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import Modal from 'react-native-modal';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  selectedSubReason?: CancelOrderInput;
  cancelOrders: () => void;
  setSelectedSubReason: (item: CancelOrderInput) => void;
  regions: CancelOrderInput[];
  cancelLoader: boolean;
};

const OrderCancelModal = (props: Props) => {
  const {
    visible,
    onClose,
    selectedSubReason,
    cancelOrders,
    setSelectedSubReason,
    regions,
    cancelLoader,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [openDropDown, setOpenDropDown] = useState(false);
  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={styles.mainViewModel}>
        <View
          style={[
            styles.subViewModel,
            // openDropDown ? styles.subViewModel1 : styles.subViewModel,
          ]}>
          <TouchableOpacity
            onPress={() => onClose()}
            style={styles.modalCloseButton}>
            <ImageIcon icon="close" size="xx4l" />
          </TouchableOpacity>
          <Spacer size="l" />
          <Label
            fontFamily="SemiBold"
            size="mx"
            color="text"
            align="center"
            text={t('orderListing.cancelOrder')}
          />
          <Spacer size="l" />
          <Label
            fontFamily="Medium"
            size="m"
            color="text"
            align="center"
            text={t('orderListing.cancelOrderMsg')}
          />
          <Spacer size="l" />
          <DropDown
            placeholderStyle={styles.placeHolderText}
            selectedTextStyle={styles.dropDownTextStyle}
            searchInputStyle={styles.searchTextStyle}
            itemTextStyle={styles.dropDownTextStyle}
            listContainerStyle={styles.listContainer}
            iconStyle={styles.iconStyle}
            placeholder={t('orderReturn.selectOption')}
            styleDropDown={styles.dropDown}
            data={regions}
            labelField="reason"
            valueField="reason"
            value={selectedSubReason}
            setOpenDropDown={setOpenDropDown}
            onChange={item => setSelectedSubReason(item)}
            search={false}
            renderItem={item => (
              <View style={styles.reasonView}>
                <Label
                  fontFamily="Regular"
                  size="m"
                  color="text2"
                  text={item?.reason}
                />
              </View>
            )}
            renderRightIcon={(focus: boolean) => {
              return (
                <ImageIcon
                  size="l"
                  tintColor={'text'}
                  icon="downArrow1"
                  resizeMode="contain"
                  style={focus && styles.downImg}
                />
              );
            }}
          />
          {selectedSubReason && (
            <View style={styles.btnView}>
              <Spacer type="Horizontal" size="xms" />
              <Button
                onPress={() => (cancelLoader ? {} : cancelOrders())}
                text={t('buttons.submit')}
                paddingHorizontal="xx"
                labelColor="whiteColor"
                weight="500"
                labelSize="mx"
                radius="xms"
                withGradient
                loading={cancelLoader}
                gradientColors={[colors.coral, colors.persimmon]}
                size="small"
              />
            </View>
          )}
          <Spacer size="l" />
        </View>
      </View>
    </Modal>
  );
};

export default OrderCancelModal;

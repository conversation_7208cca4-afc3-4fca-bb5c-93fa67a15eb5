import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    parentHr: {
      height: Sizes.x,
      color: colors.whiteColor,
      width: '100%',
    },
    label: {
      paddingVertical: Sizes.xs,
      marginTop: Sizes.xs,
    },
  });

export default styles;

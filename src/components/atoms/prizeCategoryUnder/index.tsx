import {Sizes} from 'common';
import Icons from 'common/icons';
import {t} from 'i18next';
import React, {useCallback} from 'react';
import {FlatList, Image, View} from 'react-native';
import {TypeOf} from 'yup';
import Label from '../label';
import {Spacer, WithBackground} from 'components/atoms';
import stylesWithOutColor from './style';
import LinearGradient from 'react-native-linear-gradient';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  icon?: keyof typeof Icons;
};

type Item = {
  id: number;
  img: keyof typeof Icons;
  title: string;
  color: string;
  subTitle: string;
  amount: number;
};
const data = [
  {
    id: 1,
    img: Icons.medicalGowns,
    title: 'underPrize.tooth',
    color: 'offerTitleBlue',
    subTitle: '₹',
    amount: 700,
  },
  {
    id: 2,
    img: Icons.underPrizeIcon,
    title: 'underPrize.tooth',
    color: 'offerTitleBlue',
    subTitle: '₹',
    amount: 700,
  },
  {
    id: 3,
    img: Icons.underPrizeIcon,
    title: 'underPrize.tooth',
    color: 'offerTitleBlue',
    subTitle: '₹',
    amount: 700,
  },
  {
    id: 4,
    img: Icons.underPrizeIcon,
    title: 'underPrize.tooth',
    color: 'offerTitleBlue',
    subTitle: '₹',
    amount: 700,
  },
];

const PrizeCategoryUnder = ({icon}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const renderItem = useCallback(
    ({item, index}: {item: Item; index: Number}) => {
      return (
        <View style={styles.underView}>
          <WithBackground image={'underPrizeIcon'} style={styles.images}>
            <LinearGradient
              style={styles.linerColor}
              colors={['rgba(5, 47, 113, 0)', 'rgba(255, 255, 255, 0)']}>
              <View style={styles.underLableText}>
                <Label text={t(item.title)} />
                <View style={styles.amount}>
                  <Label style={styles.rupesSymble} text={item.subTitle} />
                  <Spacer type="Vertical" size="xm" />
                  <Label style={styles.amountText} text={item.amount} />
                </View>
              </View>
            </LinearGradient>
          </WithBackground>
        </View>
      );
    },
    [],
  );

  const keyExtractor = useCallback((item: any, index: Number) => {
    return index;
  }, []);

  return (
    <View style={styles.mapView}>
      <FlatList
        data={data}
        numColumns={2}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
      />
    </View>
  );
};

export default PrizeCategoryUnder;

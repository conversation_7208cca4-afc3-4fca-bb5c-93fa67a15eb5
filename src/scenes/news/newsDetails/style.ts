import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    image: {
      width: '100%',
      height: 250,
    },
    continuerBg: {
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.xl + Sizes.s,
      borderTopRightRadius: Sizes.xl + Sizes.s,
      zIndex: Sizes.xs,
      flex: Sizes.x,
      bottom: Sizes.x4l + Sizes.xs,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xl,
      shadowOpacity: 0.25,
      shadowRadius: Sizes.s,
    },
    title: {fontWeight: '700'},
    link: {color: colors.primary, fontSize: Sizes.m},
    dateText: {
      color: colors.lightGray,
      fontWeight: '400',
      fontSize: Sizes.m,
    },
    closeButton: {
      backgroundColor: colors.textLight,
      position: 'absolute',
      zIndex: Sizes.xs,
      right: Sizes.m,
      top: Sizes.s,
      alignItems: 'center',
      justifyContent: 'center',
      padding: Sizes.s,
      borderRadius: Sizes.m,
    },
    showMore: {color: colors.primary},
    modelView: {
      height: '100%',
      width: '100%',
      marginTop: '18%',
      borderTopRightRadius: Sizes.xl,
      borderTopLeftRadius: Sizes.xl,
      backgroundColor: colors.background,
    },
  });

export default styles;

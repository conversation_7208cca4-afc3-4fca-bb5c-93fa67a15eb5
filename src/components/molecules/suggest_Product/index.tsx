import React, {useCallback, useEffect, useState} from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {
  TextInputProps,
  View,
  Platform,
  SafeAreaView,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import PhoneInputText from '../phone-input';
import {t} from 'i18next';
import Button from '../button';
import {Sizes} from 'common';
import {useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {debounce, urlReg} from 'utils/utils';
import {useMemo} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {searchProducts} from 'services/productDetail';

type Props = {
  style?: TextInputProps['style'];
  modelClose?: () => void;
  isReset: boolean;
  visible?: boolean;
  navigation: any;
  onSubmit: (data: {
    productName: string;
    brandName?: string;
    comment?: string;
    url?: string;
    email?: string;
    visible?: boolean;
  }) => void;
  search?: boolean;
};

const SuggestProduct = ({
  modelClose,
  onSubmit,
  isReset,
  visible,
  navigation,
  search = true,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [productName, setProductName] = useState('');
  const [brandName, setBrandName] = useState('');
  const [url, setUrl] = useState('');
  const [email, setEmail] = useState('');
  const [comment, setComment] = useState('');
  const [error, setError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [urlError, setUrlError] = useState('');
  const [existingProductCount, setExistingProductCount] = useState<
    number | null
  >(null);

  const fetchProductCount = useCallback(
    debounce(async (name: string) => {
      if (!name) {
        setExistingProductCount(null);
        return;
      }
      try {
        const response = await searchProducts(`query=${name}`);
        let count = response?.data?.hits?.nbHits;
        setExistingProductCount(count);
      } catch (e) {
        setExistingProductCount(null); // Or handle error state
      }
    }, 300),
    [],
  );

  const validateEmail = useCallback(email => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  const onSubmitPress = useCallback(() => {
    let hasError = false;

    if (!productName) {
      setError(t('validations.productName'));
      hasError = true;
    }
    // else if (/[^a-zA-Z0-9\s]/.test(productName)) {
    //   setError(t('validations.invalidProductName')); // Set an appropriate validation message
    //   hasError = true;
    // }
    else {
      setError('');
    }
    const urlValue = url?.trim()?.toLowerCase();
    if (url && !urlReg.test(urlValue)) {
      setUrlError(t('validations.invalidUrl'));
      hasError = true;
    } else {
      setUrlError('');
    }

    if (email && !validateEmail(email)) {
      setEmailError(t('validations.invalidEmail'));
      hasError = true;
    } else {
      setEmailError('');
    }

    if (hasError) return;
    let obj = {
      productName: productName.trim(),
      comment: comment.trim(),
      brandName: brandName.trim(),
      url: urlValue,
      email: email.trim(),
    };
    onSubmit(obj);
  }, [brandName, comment, email, productName, url, validateEmail]);

  useEffect(() => {
    if (isReset) {
      setProductName('');
      setBrandName('');
      setComment('');
      setEmail('');
      setUrl('');
      setError('');
      setEmailError('');
    }
  }, [isReset]);
  const insets = useSafeAreaInsets();
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );
  }, []);

  return (
    <>
      <DynamicHeightModal
        useInsets
        visible={visible}
        onClose={modelClose}
        content={
          // <SafeAreaView style={styles.flex}>
          <KeyboardAvoidingView
          // style={[styles.flex,
          // {paddingTop: isKeyboardVisible ? insets.top : 0},
          // ]}
          >
            <View style={styles.container}>
              <View style={styles.topLabel}>
                <Label
                  size="l"
                  weight="500"
                  color="text"
                  allowFontScaling={false}
                  text={t('productSuggestModal.suggest')}
                />
              </View>
              <View style={styles.sectionContainer}>
                <View style={styles.productSugView}>
                  <Label
                    text={t('productSuggestModal.productSuggest')}
                    weight="500"
                    color="text2"
                    size="mx"
                  />
                  <Spacer size="xms" type="Vertical" />
                  <FastImage
                    source={Icons.suggestProductGif}
                    style={styles.searchIconSize}
                  />
                </View>
                <Spacer type="Vertical" size="xm" />
                <Label
                  text={t('productSuggestModal.suggestProd')}
                  weight="500"
                  color="text2"
                  size="mx"
                />
                <Spacer size="m" />
                <View
                  style={{
                    paddingVertical:
                      Platform.OS === 'ios' ? Sizes.xms : Sizes.xm,
                  }}>
                  <PhoneInputText
                    testID="txtSuggestProductName"
                    inputStyle={styles.input}
                    searchIconStyle={styles.searchIconStyle}
                    style={styles.inputBorderStyle}
                    onChangeText={value => {
                      const trimmedValue = value.trimStart();
                      setProductName(trimmedValue);
                      setError('');
                      if (search && trimmedValue.length > 2) {
                        fetchProductCount(trimmedValue);
                      } else {
                        setExistingProductCount(null);
                      }
                    }}
                    placeholder={t('suggestProduct.productName')}
                    tintColor="text"
                    value={productName}
                    icon="shippingBox"
                    placeholderTextColor={colors.text2}
                    error={error}
                  />

                  {existingProductCount !== null &&
                    existingProductCount > 0 && (
                      <Label
                        text={
                          t('suggestProduct.existingProductPre') +
                          ` ${existingProductCount} ` +
                          t('suggestProduct.existingProductPost')
                        }
                        color="text2"
                        size="m">
                        <Label
                          text={t('suggestProduct.clickHere')}
                          color="silkBlue"
                          size="m"
                          weight="500"
                          onPress={() => {
                            modelClose?.();
                            navigation.navigate('SearchProduct', {
                              queryName: productName,
                            });
                          }}
                        />
                        <Label
                          text={t('suggestProduct.view')}
                          color="text2"
                          size="m"
                        />
                      </Label>
                    )}
                  <Spacer type="Vertical" size="m" />
                  <PhoneInputText
                    testID="txtSuggestProductBrandName"
                    inputStyle={styles.input}
                    searchIconStyle={styles.searchIconStyle}
                    style={styles.inputBorderStyle}
                    onChangeText={value => setBrandName(value.trimStart())}
                    placeholder={t('suggestProduct.optional')}
                    value={brandName}
                    tintColor="text"
                    icon="brandTag"
                    placeholderTextColor={colors.text2}
                  />
                  <Spacer type="Vertical" size="m" />
                  <PhoneInputText
                    testID="txtSuggestProductURL"
                    inputStyle={styles.input}
                    searchIconStyle={styles.searchIconStyle}
                    style={styles.inputBorderStyle}
                    onChangeText={value => {
                      setUrl(value.trimStart());
                      setUrlError('');
                    }}
                    placeholder={t('suggestProduct.url')}
                    tintColor="text"
                    value={url}
                    icon="urlIcon"
                    placeholderTextColor={colors.text2}
                    error={urlError}
                  />
                  <Spacer type="Vertical" size="m" />
                  <PhoneInputText
                    testID="txtSuggestProductEmail"
                    inputStyle={styles.input}
                    searchIconStyle={styles.searchIconStyle}
                    style={styles.inputBorderStyle}
                    onChangeText={value => {
                      setEmail(value.trimStart());
                      setEmailError('');
                    }}
                    placeholder={t('suggestProduct.productEmail')}
                    value={email}
                    tintColor="text"
                    icon="emailIcon"
                    placeholderTextColor={colors.text2}
                    error={emailError}
                  />
                  <Spacer type="Vertical" size="m" />
                  <PhoneInputText
                    testID="txtSuggestProductComment"
                    style={styles.commentStyle}
                    onChangeText={value => setComment(value.trimStart())}
                    placeholder={t('suggestProduct.comment')}
                    value={comment}
                    multiline={true}
                    numberOfLine={4}
                    placeholderTextColor={colors.text2}
                    textAlignVertical="top"
                  />
                  <Spacer type="Vertical" size="m" />
                  <View style={styles.btnStyle}>
                    <Button
                      onPress={onSubmitPress}
                      radius="xms"
                      size="large"
                      paddingHorizontal="x7l"
                      text={t('buttons.submit')}
                      type="bordered"
                      labelColor="sunnyOrange3"
                      borderColor="sunnyOrange3"
                    />
                  </View>
                </View>
              </View>
              <SafeAreaView />
            </View>
          </KeyboardAvoidingView>
          // </SafeAreaView>
        }
      />
    </>
  );
};

export default React.memo(SuggestProduct);

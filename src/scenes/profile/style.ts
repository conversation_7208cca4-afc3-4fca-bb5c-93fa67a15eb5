import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    profileImage: {
      width: Sizes.xxl * 2,
      height: Sizes.xxl * 2,
    },
    profileContainer: {
      alignItems: 'center',
    },
    userList: {
      backgroundColor: colors.skyBlue,
      padding: Sizes.xm,
      borderRadius: Sizes.xs,
      alignItems: 'center',
      justifyContent: 'center',
    },
    user: {
      flexDirection: 'row',
    },
    itemDerication: {
      justifyContent: 'space-between',
      alignItems: 'center',
      flex: Sizes.x,
    },
    logout: {fontSize: Sizes.l, fontWeight: '500', color: colors.whiteColor},
    logOutButton: {width: '47%', height: Sizes.xxl * 2},
    buttonIcon: {tintColor: colors.textLight, width: Sizes.l, height: Sizes.l},
    mainContainer: {marginHorizontal: Sizes.l, flex: Sizes.x},
  });

export default styles;

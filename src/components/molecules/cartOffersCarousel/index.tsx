import React, {useState} from 'react';
import {Platform, TouchableOpacity, View} from 'react-native';
import stylesWithOutColor from './style';
import {Label, ReanimatedCarouselPagination, Spacer} from 'components/atoms';
import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';
import LinearGradient from 'react-native-linear-gradient';
import Carousel from 'react-native-reanimated-carousel';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {t} from 'i18next';
import {useMemo} from 'react';

const CartOfferCarousel = ({data, bottomColor}: any) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [currentIndex, setCurrentIndex] = useState(0);

  return (
    <View>
      <View style={styles.disView}>
        <FastImage source={Icons.dis} style={styles.offerImage} />
      </View>
      <LinearGradient
        colors={[colors.black0, colors.green2, colors.black0]}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        style={styles.gradientLine}
      />

      <LinearGradient
        style={styles.offerLinear}
        colors={['#ebfff4', bottomColor || colors.background]}
        start={{x: 0, y: 0}}
        end={{x: 0, y: 1}}>
        <Carousel
          vertical={false}
          height={
            Platform.OS === 'ios'
              ? Sizes.screenHeight * 0.11
              : Sizes.screenHeight * 0.14
          }
          width={Sizes.screenWidth - 56}
          loop={true}
          pagingEnabled={true}
          snapEnabled={true}
          autoPlay={true}
          autoPlayInterval={1500}
          onSnapToItem={(index: number) => {
            setCurrentIndex(index);
          }}
          data={data}
          renderItem={({item}) => {
            return (
              <View>
                <TouchableOpacity
                  style={[
                    styles.alignCenter,
                    {height: Sizes.screenHeight * 0.09},
                  ]}>
                  <Spacer size="l" />
                  <Label
                    color="green2"
                    size="xl"
                    weight="500"
                    text={t('PDP.offers')}
                  />
                  <View style={styles.rowCenter}>
                    <Label
                      text={item?.message}
                      weight="500"
                      size="mx"
                      align="center"
                      color="text2"
                    />
                  </View>
                </TouchableOpacity>
              </View>
            );
          }}
        />
        {data?.length > 1 && (
          <ReanimatedCarouselPagination
            activeIndex={currentIndex}
            paginationType="normal"
            dotsLength={data?.length}
          />
        )}
      </LinearGradient>
    </View>
  );
};

export default React.memo(CartOfferCarousel);

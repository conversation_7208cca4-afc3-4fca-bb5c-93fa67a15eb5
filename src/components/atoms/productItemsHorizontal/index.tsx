import Label from 'components/atoms/label';
import React from 'react';
import {FlatList, Image, TouchableOpacity, View, ViewProps} from 'react-native';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import Spacer from '../spacer';
import HartButton from '../hartButton';
import {useTheme} from '@react-navigation/native';
import {productDataProps} from '@types/local';
import Tag from '../tag';
import {Button} from 'components/molecules';
import CheckBox from '../../atoms/checkBox';
import {dateTimeFormat} from 'utils/formatter';
import getImageUrl, {productDummyImage} from 'utils/imageUrlHelper';
import Ratings from '../ratings';
import FastImage from 'react-native-fast-image';
import {Text} from 'react-native';
import offerPer from '../../../assets/images/offerIcon.png';
import {useMemo} from 'react';

type Props = {
  item:
    | productDataProps
    | ProductData
    | getHomepageSuperListItemProps
    | simpleProductData;
  onPress?: () => void;
  onPressDelete?: () => void;
  onPressMove?: () => void;
  addToWishList?: () => void;
  navigateCategory?: () => void;
  onCart?: () => void;
  index: number;
  style?: ViewProps['style'];
  styleItems?: ViewProps['style'];
  searchAmountStyle?: ViewProps['style'];
  prizeStyle?: ViewProps['style'];
  Products?: boolean;
  soldOutProduct?: boolean;
  SearchItems?: boolean;
  descriptions?: boolean;
  soldOut?: any;
  descriptionsLink?: boolean;
  CartAndWishlist?: boolean;
  checkBox?: boolean;
  selected?: boolean;
  OtherButtons?: boolean;
  ratingAndReviews?: boolean;
  pageType?: string;
  selectCategories?: (productId: number) => void;
  activeOpacity?: number | undefined;
  // allPromotionProducts?: any;
};

const ProductItemsHorizontal = ({
  style,
  item,
  index,
  Products = false,
  descriptions = false,
  CartAndWishlist = false,
  descriptionsLink = false,
  soldOut = false,
  styleItems,
  checkBox,
  selected = false,
  onPress,
  onPressDelete,
  selectCategories,
  addToWishList,
  onPressMove,
  onCart,
  OtherButtons = false,
  ratingAndReviews = false,
  prizeStyle,
  searchAmountStyle,
  navigateCategory,
  promotionBySku,
  pageType,
}: // activeOpacity,
// allPromotionProducts,
Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  let minimalPrice = item?.price?.minimalPrice?.amount?.value;
  let symbol = item?.price?.regularPrice?.amount?.currency_symbol;

  const renderItem = ({item, index}) => {
    return (
      <>
        <View key={index} style={styles.itemView}>
          {Array.isArray(item) && item.length > 0 ? (
            <View
              style={[
                promotionBySku ? styles.auto : styles.static,
                styles.boxOffer,
              ]}>
              <View
                style={[
                  styles.padding,
                  promotionBySku ? styles.promotionBySkuPadding : {},
                ]}>
                {item.map((priceItem, i) => (
                  <Label
                    key={i.toString()}
                    color="tierPrice"
                    weight="400"
                    size="m"
                    key={index}
                    text={
                      t('productItemsHorizontal.buy') +
                      ' ' +
                      priceItem?.qty +
                      ' ' +
                      t('productItemsHorizontal.above') +
                      ' ' +
                      symbol +
                      ' ' +
                      priceItem?.value +
                      ' ' +
                      t('productItemsHorizontal.each') +
                      ' ' +
                      (
                        100 -
                        (Number(priceItem?.value) * 100) / Number(minimalPrice)
                      ).toFixed(2) +
                      t('productItemsHorizontal.off')
                    }
                  />
                ))}
              </View>
            </View>
          ) : item?.message ? (
            <View
              style={[styles.boxOffer, item?.message && styles.messageWidth]}>
              <View style={styles.paddingView}>
                <Text
                  allowFontScaling={false}
                  style={styles.tierPrice}
                  key={index}>
                  {item?.message}
                </Text>
              </View>
            </View>
          ) : null}
        </View>
      </>
    );
  };

  return (
    <TouchableOpacity
      // activeOpacity={activeOpacity}
      disabled={pageType === 'PDP' ? true : false}
      key={index}
      style={[styles.container, style]}
      onPress={navigateCategory}>
      <View style={styles.directionView}>
        {pageType !== 'PDP' ? (
          <View style={styles.subContainer}>
            {pageType !== 'WishList' && (
              <>
                {!!item?.is_in_stock ? null : (
                  <View
                    style={[
                      styles.pulsImgStyleSold,
                      !!item?.is_in_stock ? {} : styles.soldImage,
                    ]}>
                    <Label
                      weight="700"
                      size="l"
                      color="whiteColor"
                      text={t('soldProducts.soldOut')}
                    />
                  </View>
                )}
              </>
            )}

            <FastImage
              resizeMode="stretch"
              style={styles.image}
              onError={e => {
                e.target.uri = productDummyImage;
              }}
              source={{
                uri: item?.image_url
                  ? getImageUrl(item.image_url)
                  : getImageUrl(item.thumbnail_url),
              }}
            />
          </View>
        ) : null}

        {descriptions && (
          <View
            style={[
              styles.descriptionContinuer,
              descriptions && styles.descriptionContinuerHorizontal,
              styleItems,
            ]}>
            <View style={styles.row}>
              <Label
                weight="400"
                numberOfLines={2}
                ellipsizeMode={'tail'}
                style={[
                  styles.descriptionsText,
                  descriptions && styles.widthLabel,
                ]}
                text={item?.name}
              />

              {soldOut && (
                <Tag
                  labelStyle={styles.soldOutLabel}
                  style={styles.soldOutTag}
                  label={t('soldProducts.soldOut')}
                />
              )}
              {checkBox && (
                <View style={styles.checkBoxContainer}>
                  <CheckBox
                    style={styles.checkBoxRadius}
                    selected={selected}
                    value={item}
                    onValueChange={selectCategories}
                  />
                </View>
              )}
            </View>

            <Label
              color="lightGray"
              size="m"
              weight="300"
              key={index}
              style={[
                styles.descriptionSubText,
                descriptions && styles.widthWithExpiryProduct,
              ]}
              numberOfLines={descriptions ? 2 : 1}
              text={item?.short_description}
              ellipsizeMode={'tail'}>
              {item?.pd_expiry_date && (
                <Label
                  size="m"
                  weight="300"
                  color="primary"
                  style={styles.descriptionSubText}
                  text={
                    ' ' +
                    t('productItemsHorizontal.expiry') +
                    ' ' +
                    dateTimeFormat(item?.pd_expiry_date)
                  }
                />
              )}
            </Label>
            {Products && (
              <View style={[styles.ratingView, styles.directionView]}>
                <Label key={index} size="m" text={item.rating} />
                <Ratings
                  size={10}
                  style={styles.ratingImg}
                  count={5}
                  defaultRating={parseInt(item?.average_rating || item.rating)}
                  showRating={false}
                  onFinishRating={() => {}}
                />
              </View>
            )}
            {ratingAndReviews && (
              <>
                <Spacer size="s" />
                <View style={styles.directionView}>
                  <Tag
                    style={styles.tagView}
                    label={(item.average_rating === 'null'
                      ? 0
                      : Number(item.average_rating)
                    ).toFixed(1)}
                    icon="starIcon"
                  />
                  <Label
                    weight="400"
                    size="m"
                    style={styles.reviewText}
                    text={
                      item?.rating_count === null
                        ? 0 + ' ' + t('productItemsHorizontal.rate&review')
                        : item?.rating_count +
                          ' ' +
                          t('productItemsHorizontal.rate&review')
                    }
                  />
                </View>
              </>
            )}
            {/* {debugLog(
              'allPromotionProducts==========component====',
              allPromotionProducts?.some(e => e.product_sku === item),
            )}
            {allPromotionProducts &&
            allPromotionProducts?.some(e => e.product_sku === item) ? (
              <View>
                <Label
                  color="freeProduct"
                  weight="bold"
                  text={'free product'}
                />
              </View>
            ) : null} */}
            <View
              style={[
                styles.prizeTextView,
                styles.directionView,
                descriptions && styles.prizeTextViewHoz,
                prizeStyle,
                searchAmountStyle,
              ]}>
              {item?.type_id === 'grouped' ? (
                <Label size="mx" text={t('productItemsHorizontal.starting')} />
              ) : null}
              <Spacer size="sx" />
              <Label
                weight="700"
                color="primary"
                style={styles.prizeText}
                text={
                  item?.price?.minimalPrice?.amount?.currency_symbol +
                  '' +
                  item?.price?.minimalPrice?.amount?.value
                }
              />
              <Spacer size="s" />
              {item.price?.regularPrice?.amount?.value ===
                item?.price?.minimalPrice?.amount?.value ||
              item.price?.regularPrice?.amount?.value == 0 ? null : (
                <Label
                  style={styles.removePrize}
                  text={
                    item.price?.regularPrice.amount.currency_symbol +
                    '' +
                    item.price?.regularPrice?.amount?.value
                  }
                />
              )}

              {(descriptions &&
                item.price?.regularPrice?.amount?.value === 0) ||
              item.price?.regularPrice?.amount?.value ===
                item?.price?.minimalPrice?.amount?.value ? null : (
                <Label
                  color="grassGreen"
                  style={styles.offText}
                  text={
                    (
                      100 -
                      (Number(item?.price?.minimalPrice?.amount?.value) * 100) /
                        Number(
                          item?.price?.regularPrice?.amount?.value ||
                            item?.price?.minimalPrice?.amount?.value,
                        )
                    ).toFixed(2) + t('productItemsHorizontal.off')
                  }
                />
              )}
            </View>
            <Spacer size="sx" />

            {
              // store.baseCountryData &&
              // store.baseCountryData?.country_id === 'IN' &&
              item.reward_point_product && item.reward_point_product !== 0 ? (
                <View style={styles.directionView}>
                  <Image
                    onError={e => {
                      e.target.uri = productDummyImage;
                    }}
                    source={{
                      uri: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/coin.png',
                    }}
                    style={styles.rewardIcon}
                  />
                  <Spacer type="Horizontal" size="s" />
                  <Label
                    style={styles.pointProduct}
                    size="m"
                    color="textLight"
                    text={item?.reward_point_product}
                  />
                </View>
              ) : null
            }
            <Spacer size="s" />
            {pageType === 'PDP' ? (
              <>
                {item?.tier_prices?.length > 0 || promotionBySku ? (
                  <View style={styles.offer}>
                    <Image style={styles.offerImg} source={offerPer} />
                    <Label
                      style={styles.AvailOfferView}
                      text={'Available Offers'}
                    />
                  </View>
                ) : null}
                <FlatList
                  data={[item?.tier_prices, {message: promotionBySku}]}
                  horizontal
                  ItemSeparatorComponent={() => (
                    <View style={styles.separator} />
                  )}
                  renderItem={renderItem}
                  keyExtractor={(_, i) => i.toString()}
                  showsHorizontalScrollIndicator={false}
                />
              </>
            ) : null}

            <Spacer size="sx" />
          </View>
        )}
        {descriptions ? null : <HartButton bin={true} item={undefined} />}
      </View>
      {OtherButtons && (
        <View style={styles.buttonView}>
          <View style={[styles.buttonDirections, styles.row]}>
            <Button
              labelStyle={styles.addToCartText}
              tintColor="whiteColor"
              radius="m"
              onPress={onCart}
              style={styles.addTwoCart}
              iconLeft={'cartBucketIcon'}
              text={t('buttons.addToCart')}
            />
            <Button
              tintColor="textLight"
              radius="m"
              onPress={onPressMove}
              labelStyle={styles.buttonText}
              style={styles.moveButton}
              iconLeft={'upArrowIcon'}
              text={t('buttons.move')}
            />
            <Button
              onPress={onPressDelete}
              styleLeftIcon={styles.leftIcon}
              style={styles.binButton}
              iconLeft={'binIcon'}
            />
          </View>
        </View>
      )}
      {descriptionsLink ? (
        <TouchableOpacity style={styles.checkContinuer}>
          <Label
            ellipsizeMode={'tail'}
            numberOfLines={1}
            style={styles.checkOtherProduct}
            text={item.url_key}
          />
        </TouchableOpacity>
      ) : null}
      <Spacer size="xs" />

      {CartAndWishlist && (
        <View style={[styles.otherButtonsView, styles.directionView]}>
          <Button
            labelStyle={styles.labelsColor}
            tintColor="whiteColor"
            radius="m"
            disabled={!item.is_in_stock ? true : false}
            iconStyle={styles.iconStyle}
            onPress={onPress}
            style={[styles.addTwoCart, !item.is_in_stock && styles.opacity]}
            iconLeft={'cartBucketIcon'}
            text={t('buttons.addToCart')}
          />
          <Button
            labelStyle={styles.labelsColor}
            radius="m"
            iconStyle={styles.iconStyle}
            tintColor="whiteColor"
            onPress={addToWishList}
            style={[styles.addTwoCart, styles.buttonBackground]}
            iconLeft={'heartIcon'}
            text={t('buttons.addToWishList')}
          />
        </View>
      )}
    </TouchableOpacity>
  );
};

export default ProductItemsHorizontal;

import React, {useState} from 'react';
import {View} from 'react-native';
import stylesWithOutColor from './style';
import {Menu, MenuDivider} from 'react-native-material-menu';
import {Button} from 'components/molecules';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Item = {
  title: string;
  onPress: () => void;
  children: () => React.ReactElement;
};
type Props = {
  items: Item[];
};

const MenuButtons = ({items, toggleMenu, menuModel}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={styles.moreOptions}>
      <Menu
        style={styles.container}
        visible={menuModel}
        anchor={
          <Button
            tintColor="textLight"
            style={styles.moreButton}
            onPress={toggleMenu}
            ghost
            iconCenter="moreIcon"
          />
        }
        onRequestClose={toggleMenu}>
        {items?.map((item: Item, index: number) => (
          <View key={index.toString()}>
            {index !== 0 ? <MenuDivider /> : null}
            {item.children()}
          </View>
        ))}
      </Menu>
    </View>
  );
};

export default MenuButtons;

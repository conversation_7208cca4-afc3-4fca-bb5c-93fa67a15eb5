import React, {useMemo, memo} from 'react';
import {View, TouchableOpacity} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer, ImageIcon} from 'components/atoms';
import {t} from 'i18next';
import {getTag, distance, fullNameText} from 'utils/utils';
import ToggleSwitch from 'toggle-switch-react-native';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';

type Props = {
  index: number;
  item: CustomerAddressV2;
  coords?: {latitude: number; longitude: number};
  toggleVal: string | undefined;
  onEdit: (item: CustomerAddressV2) => void;
  onDelete: (id: number) => void;
  onChangeToggle: (item: CustomerAddressV2, index: number) => void;
};

const AddressCard = ({
  index,
  item,
  coords,
  toggleVal,
  onEdit,
  onDelete,
  onChangeToggle,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const kmText = useMemo(() => {
    if (
      !coords?.latitude ||
      !coords?.longitude ||
      !item?.latitude ||
      !item?.longitude
    )
      return '';
    const distanceValue = distance(
      [coords?.latitude, coords?.longitude],
      [item.latitude, item.longitude],
      'km',
    );
    const formattedDistance = parseFloat(distanceValue).toFixed(2);
    const isHere =
      Number(formattedDistance) === 0 || Number(formattedDistance) < 0.04;
    return `${isHere ? '' : `${formattedDistance} `}${t(
      isHere ? 'address.youHere' : 'address.kmWay',
    )}`;
  }, [coords, item?.latitude, item?.longitude, t]);

  const address = useMemo(() => {
    if (item) {
      const {street, map_address, customer_street_2, city, region, postcode} =
        item;
      let street2 = '';
      if (customer_street_2) {
        street2 = customer_street_2;
      } else if (map_address) {
        const parts = map_address?.split(',');
        const beforePin = parts
          .slice(0, parts.length - 2)
          .join(',')
          .trim();
        street2 = beforePin;
      }
      return `${street?.length > 0 ? street[0] : ''}${
        street2 ? ', ' + street2 : ''
      }${city ? ', ' + city : ''}${
        region?.region ? ', ' + region?.region : ''
      }${postcode ? ' - ' + postcode : ''}`;
    }
    return '';
  }, [item]);

  return (
    <View key={index} style={styles.renderContainer}>
      <View style={styles.tagView}>
        <ImageIcon size="m" icon={getTag(item.tag)} />
      </View>
      <View style={styles.flex}>
        <View style={styles.rowCenter}>
          <View style={styles.fRow}>
            <Label
              text={item?.tag ? item?.tag : fullNameText(item)}
              weight="600"
              size="mx"
              color="text"
              textTransform="capitalize"
            />
            {item.latitude && coords?.latitude ? (
              <>
                <Spacer size="sx" type="Horizontal" />
                <View style={styles.awayView}>
                  <Label
                    text={kmText}
                    weight="500"
                    size="xms"
                    color="green2"
                    textTransform="capitalize"
                  />
                </View>
              </>
            ) : (
              <View />
            )}
          </View>
          {item?.default_shipping ? (
            <>
              {/* <View style={styles.defaultView}>
              <Label
                text={t('otherText.es')}
                weight="500"
                size="m"
                color="whiteColor"
                textTransform="capitalize"
              />
            </View> */}
              <FastImage
                style={styles.defaultGif}
                source={Icons.defaultGif}
                resizeMode="contain"
              />
            </>
          ) : (
            <View />
          )}
        </View>
        <Spacer size="xs" />
        <View style={styles.row}>
          <View style={styles.flex}>
            {item?.tag ? (
              <View style={styles.row}>
                {/* <ImageIcon
                size="xx"
                tintColor="text"
                icon="user"
                resizeMode="contain"
              />
              <Spacer size="xm" type="Horizontal" /> */}
                <Label
                  text={fullNameText(item)}
                  weight="600"
                  size="m"
                  color="text2"
                  textTransform="capitalize"
                  style={styles.flex}
                />
              </View>
            ) : (
              <View />
            )}

            <Spacer size="s" />
            <View style={styles.rowCenter}>
              <ImageIcon size="mx" icon="phone1" tintColor="text2" />
              <Spacer size="s" type="Horizontal" />
              <Label
                text={`${item?.telephone}${
                  item?.custom_attributes?.length > 0 &&
                  item?.custom_attributes[0].value
                    ? ', ' + item?.custom_attributes[0].value
                    : ''
                }`}
                weight="400"
                size="m"
                color="text2"
                textTransform="capitalize"
              />
            </View>
            {item?.vat_id ? (
              <>
                <Spacer size="s" />
                <View style={styles.rowCenter}>
                  <ImageIcon size="xx" icon="gst" tintColor="text2" />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    text={item?.vat_id}
                    weight="400"
                    size="m"
                    color="text2"
                  />
                </View>
              </>
            ) : (
              <View />
            )}
            <Spacer size="s" />
            <View style={styles.row}>
              <View style={styles.flex}>
                {item?.street?.length > 1 && item?.street[1] ? (
                  <>
                    <View style={styles.row}>
                      <ImageIcon
                        size="xx"
                        tintColor="text2"
                        icon="map"
                        resizeMode="contain"
                      />
                      <Spacer size="xm" type="Horizontal" />
                      <Label
                        text={item?.street[1] || ''}
                        size="m"
                        weight="400"
                        color="text2"
                        textTransform="capitalize"
                        style={styles.flex}
                      />
                    </View>
                    <Spacer size="s" />
                  </>
                ) : (
                  <View />
                )}
                <View style={styles.row}>
                  <ImageIcon
                    size="xx"
                    tintColor="text2"
                    icon="addressPin"
                    resizeMode="contain"
                  />
                  <Spacer size="xm" type="Horizontal" />
                  <Label
                    text={address}
                    size="m"
                    weight="400"
                    color="text2"
                    textTransform="capitalize"
                    style={styles.flex}
                  />
                </View>
              </View>
              {item.latitude ? (
                <>
                  <Spacer size="xm" type="Horizontal" />
                  <ImageIcon size="x8l" icon="map1" resizeMode="contain" />
                </>
              ) : (
                <View />
              )}
            </View>
          </View>
        </View>

        <Spacer size="s" />
        <View style={styles.rowCenter}>
          <TouchableOpacity style={styles.editBtn} onPress={() => onEdit(item)}>
            <ImageIcon size="m" icon="editPencil" tintColor="newSunnyOrange" />
            <Spacer size="xm" type="Horizontal" />
            <Label
              text={t('manageAddress.edit')}
              size="m"
              color="newSunnyOrange"
              fontFamily="Medium"
            />
          </TouchableOpacity>
          {item.default_shipping ? null : (
            <>
              <Spacer type="Horizontal" size="xms" />
              <TouchableOpacity
                style={styles.deleteBtn}
                onPress={() => onDelete(item?.id)}>
                <ImageIcon size="m" icon="delete" tintColor="newSunnyOrange" />
                <Spacer size="xm" type="Horizontal" />
                <Label
                  text={t('manageAddress.delete')}
                  size="m"
                  fontFamily="Medium"
                  color="newSunnyOrange"
                />
              </TouchableOpacity>
            </>
          )}
          <View style={styles.flex} />
          {!item.default_shipping && (
            <View style={styles.rowCenter}>
              <Label
                text={t('manageAddress.default')}
                size="m"
                fontFamily="Medium"
                color="newSunnyOrange"
              />
              <Spacer size="xm" type="Horizontal" />
              <ToggleSwitch
                isOn={item?.default_shipping || toggleVal === item?.id}
                onColor={colors.text2}
                offColor={colors.placeholderColor}
                size="medium"
                onToggle={value => onChangeToggle(item, index)}
                thumbOffStyle={styles.toggleOff}
                thumbOnStyle={[
                  {
                    backgroundColor: item?.default_shipping
                      ? colors.whiteColor
                      : colors.placeholderColor,
                  },
                  styles.toggleOn,
                  styles.toggleView,
                ]}
              />
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default memo(AddressCard);

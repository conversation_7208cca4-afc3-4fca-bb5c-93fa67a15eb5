import React, {useCallback, useEffect, useState} from 'react';
import {
  TouchableOpacity,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  ButtonProps,
  View,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {ImageIcon, Label, Separator, Spacer} from 'components/atoms';
import Icons from 'common/icons';
import {navigate} from 'utils/navigationRef';
import stylesWithOutColor from './style';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from 'routes';
import {Header} from 'components/molecules';
import {multimediaAPi} from 'api';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {useMemo} from 'react';

type Props = {
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  text?: string;
  icon?: keyof typeof Icons | null;
  onPress?: () => void;
  style?: ButtonProps['style'];
  type: 'news' | 'save';
};

const MultimediaScene = ({}: Props) => {
  const [multimediaData, setMultimediaData] = useState([]);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const media = useCallback(async () => {
    const {data} = await multimediaAPi({type: 'video'});
    if (data?.multimedia) {
      setMultimediaData(data?.multimedia?.video);
    }
  }, []);
  useEffect(() => {
    media();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <Header hadarLogo={false} text={t('multimedia.multimedia')} />
      <ScrollView style={styles.flex}>
        {multimediaData?.map((item, index) => (
          <>
            <TouchableOpacity
              key={index}
              onPress={() => navigate('VideoPlay', {source: item?.source})}
              style={[styles.mainContinuer, styles.directions]}>
              <ImageIcon
                resizeMode="contain"
                style={styles.image}
                sourceType="url"
                source={item?.thumbnail}
              />
              <View style={styles.body}>
                <Label weight="700" numberOfLines={2} text={item?.title} />
                <Spacer type="Vertical" size="l" />
                <Label
                  color="lightGray"
                  numberOfLines={3}
                  text={item?.created_at?.split(' ')[0]}
                />
                <Spacer type="Vertical" size="m" />
              </View>
            </TouchableOpacity>
            <Separator style={styles.separator} />
          </>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};
export default MultimediaScene;

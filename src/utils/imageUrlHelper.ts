import {IMAGE_BASE_URL, ImagePath} from 'config/apiEndpoint';

export const productDummyImage = IMAGE_BASE_URL + 'placeholder_oLMT6qIw9.png';
export const categoryDummyImage = ImagePath.category + 'Orthodontics.png';

const getImageUrl = (image: string, imagePath?: string) => {
  const imageMappings: any = {
    category: {imageUrl: ImagePath.imageUrl, staticImg: categoryDummyImage},
    product: {imageUrl: ImagePath.product, staticImg: productDummyImage},
    featureProduct: {
      imageUrl: ImagePath.featureProduct,
      staticImg: productDummyImage,
    },
    coin: {imageUrl: ImagePath.imageUrl, staticImg: ''},
  };
  const {imageUrl, staticImg} = imageMappings[imagePath ? imagePath : ''] || {
    imageUrl: ImagePath.product,
    staticImg: productDummyImage,
  };
  if (image) {
    if (image.includes('https://')) {
      return image;
    }
    return `${imageUrl}${image.charAt(0) === '/' ? '' : '/'}${image}`;
  } else {
    return staticImg;
  }
};

export const getImageCDNURL = (image: string) => {
  return ImagePath.media + image;
};

export default getImageUrl;

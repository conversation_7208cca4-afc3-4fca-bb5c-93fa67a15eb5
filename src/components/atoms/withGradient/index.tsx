import React from 'react';
import LinearGradient, {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  LinearGradientProps,
} from 'react-native-linear-gradient';
type Props = {
  children: React.ReactNode;
  gradientColors: LinearGradientProps['colors'];
  gradientAngle?: LinearGradientProps['angle'];
  gradientStyle?: LinearGradientProps['style'];
};

const WithGradient = ({
  children,
  gradientColors,
  gradientAngle = 90,
  gradientStyle,
}: Props) => {
  return (
    <>
      <LinearGradient
        useAngle
        angle={gradientAngle}
        style={gradientStyle}
        colors={gradientColors}>
        {children}
      </LinearGradient>
    </>
  );
};

export default WithGradient;

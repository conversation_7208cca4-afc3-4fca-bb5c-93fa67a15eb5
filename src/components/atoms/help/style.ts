import {Sizes} from 'common';
import {StyleSheet, Platform} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    chatView: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      position: 'absolute',
      width: Sizes.x72,
      height: Sizes.x4l,
      bottom: Platform.OS === 'android' ? Sizes.ex : Sizes.ex110,
      right: Sizes.m,
      borderRadius: Sizes.l,
      borderColor: colors.islandGreen,
      borderWidth: Sizes.x,
      backgroundColor: colors.paleGreen,
    },
    icon: {
      width: Sizes.l,
      height: Sizes.l,
    },
    helpTxt: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

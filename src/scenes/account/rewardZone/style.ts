import {Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    mainContainer: {flex: Sizes.x},
    container: {paddingHorizontal: Sizes.m, flex: Sizes.x},
    howIt: {alignSelf: 'center', paddingVertical: Sizes.xm},
    workContainer: {
      borderRadius: Sizes.m,
      backgroundColor: colors.pattensBlue24,
      padding: Sizes.l,
      flexDirection: 'row',
      alignItems: 'center',
    },
    answerLabel: {
      fontSize: Sizes.m,
      color: colors.text2,
    },
    rewardImg: {
      width: '100%',
      height: Sizes.ex374 + Sizes.x,
    },
    rewardImg1: {
      height: Sizes.ex234,
    },
    workItImg: {
      width: '100%',
      height: Sizes.ex88,
    },
    workItImg1: {
      height: Sizes.ex122,
    },
    moreImg: {
      width: '100%',
      height: Sizes.ex90 + Sizes.s,
    },
    moreImg1: {
      height: Sizes.ex166,
    },
    signText: {
      flex: Sizes.x,
      marginRight: Sizes.xms,
    },
    renderImg: {
      width: Sizes.x56,
      height: Sizes.x56,
    },
    fRow: {
      flexDirection: 'row',
    },
    renderImg1: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    howView: {
      backgroundColor: colors.pattensBlue24,
      padding: Sizes.m,
      borderRadius: Sizes.m,
      marginTop: Sizes.m,
      marginBottom: Sizes.s,
      paddingTop: Sizes.s,
    },
    howContainer: {
      flexDirection: 'row',
      width: (DeviceWidth - Sizes.ex250) / 3,
      marginHorizontal: Sizes.x34,
    },
    howItText: {
      flex: Sizes.x,
    },
    rewardValue: {
      width: '100%',
      height: Sizes.x9l + Sizes.s,
    },
  });

export default styles;

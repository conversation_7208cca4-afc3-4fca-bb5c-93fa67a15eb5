apply plugin: "com.android.application"
apply plugin: "com.facebook.react"
apply plugin: "com.google.gms.google-services"
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"
apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"
apply from: "../../node_modules/react-native-code-push/android/codepush.gradle"
apply plugin: 'kotlin-android'
import com.android.build.OutputFile


/* This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
*/

react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/react-native-codegen
    // codegenDir = file("../node_modules/react-native-codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to create four separate APKs instead of one,
 * one for each native architecture. This is useful if you don't
 * use App Bundles (https://developer.android.com/guide/app-bundle/)
 * and want to have separate APKs to upload to the Play Store.
 */
def enableSeparateBuildPerCPUArchitecture = false

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/* Private function to get the list of Native Architectures you want to build.
 * This reads the value from reactNativeArchitectures in your gradle.properties
 * file and works together with the --active-arch-only flag of react-native run-android.
 */
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}
project.ext.envConfigFiles = [
        productiondebug: ".env.production",
        productionrelease: ".env.production",
        stagingdebug: ".env.staging",
        stagingrelease: ".env.staging",
        devdebug: ".env.dev",
        devrelease: ".env.dev",
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

android {
    ndkVersion rootProject.ext.ndkVersion

    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "com.vasadental.dentalkart"
    defaultConfig {
        applicationId "com.vasadental.dentalkart"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 10300052
        versionName "3.0.3"
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    def buildPerArchitecture = project.hasProperty("android.injected.BUILD_PER_ARCHITECTURE") ? project.getProperty("android.injected.BUILD_PER_ARCHITECTURE") : "false"
    println "BUILD_PER_ARCHITECTURE: " + buildPerArchitecture
    splits {
        abi {
            reset()
            enable = (buildPerArchitecture ?: enableSeparateBuildPerCPUArchitecture).toBoolean()
            universalApk true  // If true, also generate a universal APK
            include (*reactNativeArchitectures())
        }
    }
     signingConfigs {
        debug {
            storeFile file('../dentalkart-app.jks')
            storePassword '17944c62-0a46-11e8-8964-0a580a78261f'
            keyAlias 'QHNob3J5YXZhaXNoL2RlbnRhbGthcnQtYXBw'
            keyPassword '17944c6f-0a46-11e8-8964-0a580a78261f'
        }
//        release {
//            storeFile file('../dentalkart-app.jks')
//            storePassword '17944c62-0a46-11e8-8964-0a580a78261f'
//            keyAlias 'QHNob3J5YXZhaXNoL2RlbnRhbGthcnQtYXBw'
//            keyPassword '17944c6f-0a46-11e8-8964-0a580a78261f'
//        }

         production {
             storeFile file('../dentalkart-app.jks')
             storePassword '17944c62-0a46-11e8-8964-0a580a78261f'
             keyAlias 'QHNob3J5YXZhaXNoL2RlbnRhbGthcnQtYXBw'
             keyPassword '17944c6f-0a46-11e8-8964-0a580a78261f'
         }
         staging {
             storeFile file('../dentalkart-app-staging.jks')
             storePassword 'gRVHGC2z6kqH5wMWk3oYMfuyLjz2c3PY'
             keyAlias 'zHsTdMnUrUwC7PkgD1Tbbeb598AO6xP1'
             keyPassword 'XiSUZFStHY88FuXkdjXhj2AmZcnjnfFY'
         }
         dev {
             storeFile file('../dentalkart-app-dev.jks')
             storePassword 'mliYrqboQnQzmayGBZfzgJzIMA35B3YW'
             keyAlias 'UL6FP2rQeqEOlM0xeZu4OvqX6wnTkUHF'
             keyPassword 'AtcbpXESjH2Y8npXBFdvyU00zNbqL0jV'
         }
    }
    flavorDimensions 'environments'
    productFlavors {
        production {
            dimension 'environments'
            resValue "string", "build_config_package", "com.vasadental.dentalkart"
            resValue("string", "app_name", "Dentalkart")
            signingConfig signingConfigs.production
            manifestPlaceholders = [
                    appIcon: "@mipmap/ic_launcher",
                    appIconRound: "@mipmap/ic_launcher_round",
                    permissionName: "com.vasadental.dentalkart.permission.MIPUSH_RECEIVE"
            ]
        }
        staging {
            dimension 'environments'
            resValue "string", "build_config_package", "com.vasadental.dentalkart"
            applicationIdSuffix ".staging"
            resValue("string", "app_name", "S Dentalkart")
            signingConfig signingConfigs.staging
            manifestPlaceholders = [
                    appIcon: "@mipmap/ic_launcher",
                    appIconRound: "@mipmap/ic_launcher_round",
                    permissionName: "com.vasadental.dentalkart.staging.permission.MIPUSH_RECEIVE"
            ]
        }
        dev {
            dimension 'environments'
            resValue "string", "build_config_package", "com.vasadental.dentalkart"
            applicationIdSuffix ".dev"
            resValue("string", "app_name", "D Dentalkart")
            signingConfig signingConfigs.dev
            manifestPlaceholders = [
                    appIcon: "@mipmap/ic_launcher",
                    appIconRound: "@mipmap/ic_launcher_round",
                    permissionName: "com.vasadental.dentalkart.dev.permission.MIPUSH_RECEIVE"
            ]
        }
    }
    buildTypes {
        debug {
            //signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            //signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
             firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            }
        }
    }

    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // https://developer.android.com/studio/build/configure-apk-splits.html
            // Example: versionCode 1 will generate 1001 for armeabi-v7a, 1002 for x86, etc.
            def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
            def abi = output.getFilter(OutputFile.ABI)
            if (abi != null) {  // null for the universal-debug, universal-release variants
                output.versionCodeOverride =
                        defaultConfig.versionCode * 1000 + versionCodes.get(abi)
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {

    implementation("com.facebook.react:react-android")
    implementation("ai.easyinsights.ei_sdk:ei_sdk:1.0.6")
    implementation("com.moengage:rich-notification:5.1.1")
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    implementation 'com.android.installreferrer:installreferrer:1.1.1'
    implementation 'com.google.firebase:firebase-messaging:23.4.0'
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}") {
        exclude group:'com.facebook.fbjni'
    }

    debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        exclude group:'com.squareup.okhttp3', module:'okhttp'
    }

    debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}")
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])

    implementation("androidx.core:core:1.9.0")
    implementation("androidx.appcompat:appcompat:1.4.2")
    implementation("androidx.lifecycle:lifecycle-process:2.7.0")
    implementation("com.google.android.gms:play-services-ads-identifier:18.0.1")
    implementation "androidx.activity:activity:1.9.3"
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
googleServices.disableVersionCheck = true

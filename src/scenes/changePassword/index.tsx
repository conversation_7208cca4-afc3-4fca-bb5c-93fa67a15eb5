import React, {useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {RouteProp, useTheme} from '@react-navigation/native';
import {<PERSON><PERSON>, Header, TextInputBox} from 'components/molecules';
import stylesWithOutColor from './style';
import {Label, Spacer} from 'components/atoms';
import {t} from 'i18next';
import {useDispatch} from 'react-redux';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {useSelector} from 'react-redux';
import {View} from 'react-native';
import {updatePassword} from 'services/auth';
import {setLoading, getUserInfo} from 'app-redux-store/slice/appSlice';
import {useMemo} from 'react';
import Icons from 'common/icons';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ChangePassword'>;
};

const ChangePassword = ({navigation, route}: Props) => {
  const {isEditType} = route.params;
  const dispatch = useDispatch();
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isPasswordSecure, setIsPasswordSecure] = useState(true);
  const [isConfirmPass, setIsConfirmPass] = useState(true);
  const [currentPasswordSecure, setCurrentPasswordSecure] = useState(true);
  const [password, setPassword] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const {userInfo} = useSelector((state: RootState) => state.app);
  const [values, setValues] = useState({
    emailOrPhone: isEditType === 'email' ? userInfo.email : userInfo.mobile,
    type: isEditType,
  });

  //*update Password
  const passwordChange = async () => {
    dispatch(setLoading(true));
    let obj = {
      new_password: password.newPassword,
    };
    if (userInfo?.is_cred_exist) {
      obj['current_password'] = password.currentPassword;
    }
    const {data, status} = await updatePassword(obj);
    dispatch(setLoading(false));
    if (status) {
      showSuccessMessage(
        t(
          userInfo?.is_cred_exist
            ? 'profileDetails.passwordUpdated'
            : 'profileDetails.createPasswordMsg',
        ),
      );
      if (!userInfo?.is_cred_exist) {
        dispatch(getUserInfo());
      }
      navigation.goBack();
    } else {
      showErrorMessage(data?.message);
    }
    [];
  };

  const renderText = (text: string) => {
    return (
      <View style={styles.dotStyle}>
        <View style={styles.dotView} />
        <Label
          text={text}
          size="m"
          style={styles.noteDes}
          fontFamily="Regular"
          color="text2"
        />
      </View>
    );
  };

  const checkPassword = useMemo(() => {
    if (userInfo?.is_cred_exist) {
      return (
        password.currentPassword !== password.newPassword &&
        password.currentPassword?.length > 5 &&
        password.newPassword?.length > 5
      );
    } else {
      return (
        password.newPassword === password.confirmPassword &&
        password.newPassword?.length > 5 &&
        password.confirmPassword?.length > 5
      );
    }
  }, [password]);

  return (
    <SafeAreaView style={styles.container}>
      <Header
        backButton
        navigation={navigation}
        text={t('profile.profileHeader')}
      />
      <Label
        text={t(
          userInfo?.is_cred_exist
            ? 'profile.changePassword'
            : 'profile.createPassword',
        )}
        style={styles.updateEmailLabel}
        size="l"
        fontFamily="Regular"
        lineHeight="xxl"
      />
      <View style={styles.subContainer}>
        {userInfo?.is_cred_exist ? (
          <>
            <TextInputBox
              testID="txtChangePasswordCurrentPassword"
              label={t('profile.currentPassword')}
              value={password.currentPassword}
              containerStyle={{color: colors.text}}
              onChangeText={text =>
                setPassword(prev => ({...prev, currentPassword: text}))
              }
              leftIcon={Icons.lock}
              secureTextEntry={currentPasswordSecure}
              rightStyle={{
                tintColor:
                  password.currentPassword.length === 0
                    ? colors.grey2
                    : colors.text,
              }}
              rightIcon={
                currentPasswordSecure ? Icons.viewHide : Icons.eyeShowIcon
              }
              onRightIconPress={() =>
                setCurrentPasswordSecure(!currentPasswordSecure)
              }
            />
            <Spacer size="xm" />
          </>
        ) : (
          <View />
        )}

        <TextInputBox
          testID="txtChangePasswordNewPassword"
          label={t('profile.newPassword')}
          value={password.newPassword}
          containerStyle={{color: colors.text}}
          onChangeText={text =>
            setPassword(prev => ({...prev, newPassword: text}))
          }
          error={
            password.currentPassword === password.newPassword &&
            password.newPassword?.length > 5
          }
          errorText={
            password.currentPassword === password.newPassword &&
            password.newPassword?.length > 5 &&
            t('validations.passwordSame')
          }
          leftIcon={Icons.lock}
          secureTextEntry={isPasswordSecure}
          rightStyle={{
            tintColor:
              password.newPassword.length === 0 ? colors.grey2 : colors.text,
          }}
          rightIcon={isPasswordSecure ? Icons.viewHide : Icons.eyeShowIcon}
          onRightIconPress={() => setIsPasswordSecure(!isPasswordSecure)}
        />

        {!userInfo?.is_cred_exist ? (
          <>
            <Spacer size="xm" />
            <TextInputBox
              testID="txtConfirmPassword"
              label={t('profile.confirmPassword')}
              value={password.confirmPassword}
              containerStyle={{color: colors.text}}
              onChangeText={text =>
                setPassword(prev => ({...prev, confirmPassword: text}))
              }
              error={
                password.newPassword !== password.confirmPassword &&
                password.confirmPassword?.length > 5
              }
              errorText={
                password.newPassword !== password.confirmPassword &&
                password.confirmPassword?.length > 5 &&
                t('validations.confirmPassMatch')
              }
              leftIcon={Icons.lock}
              secureTextEntry={isConfirmPass}
              rightStyle={{
                tintColor:
                  password.confirmPassword.length === 0
                    ? colors.grey2
                    : colors.text,
              }}
              rightIcon={isConfirmPass ? Icons.viewHide : Icons.eyeShowIcon}
              onRightIconPress={() => setIsConfirmPass(!isConfirmPass)}
            />
          </>
        ) : (
          <View />
        )}
        <Spacer size="l" />
        <Label
          text={t(
            userInfo?.is_cred_exist
              ? 'profile.passNote'
              : 'login.strongPasswordRulesHeading',
          )}
          size="m"
          fontFamily="Medium"
          color="text2"
        />
        <Spacer size="xm" />
        <View>
          {renderText(t('profile.passNote1'))}
          {userInfo?.is_cred_exist && renderText(t('profile.passNote2'))}
          {renderText(t('profile.passNote3'))}
        </View>
        <Spacer size="l" />

        <Button
          style={styles.buttonView}
          text={t(
            userInfo?.is_cred_exist
              ? 'profile.changePassword'
              : 'profile.createPassword',
          )}
          onPress={() => {
            if (checkPassword) {
              passwordChange();
            }
          }}
          labelSize="mx"
          type={checkPassword ? 'secondary' : 'disabled'}
          radius="xm"
          labelColor="whiteColor"
          weight="500"
        />
      </View>
    </SafeAreaView>
  );
};

export default ChangePassword;

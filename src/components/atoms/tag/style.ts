import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    iconView: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: Sizes.s
    },
    container: {
      borderRadius: Sizes.xl,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: Sizes.s,
      backgroundColor: colors.cartCountColor,
      paddingHorizontal: Sizes.s,
      borderWidth: Sizes.x,
    },
    title: {
      paddingTop: -Sizes.xs,
    },
  });

export default styles;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    searchIconView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xx,
      backgroundColor: colors.background2,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.l,
      borderColor: colors.background2,
    },
    searchSubView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      height: Sizes.x6l,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.xms,
      borderColor: colors.grey2,
      backgroundColor: colors.background,
    },
    textInputView: {
      color: colors.text2,
      fontFamily:"Regular",    
      fontSize: Sizes.mx,
    },
    flex: {
      flex: Sizes.x,
    },
    childProductView: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      padding: Sizes.m,
      backgroundColor: colors.background,
      marginBottom: Sizes.xm,
    },
    subChildProductView: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    childView: {
      flexDirection: 'row',
    },
    earnView: {
      flexDirection: 'row',
    },
    maximalPriceView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    amountView: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    expiryDateView: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    infoIcon: {
      paddingHorizontal: Sizes.s,
    },
    fRow: {
      flexDirection: 'row',
    },
    expiryView: {
      flex: Sizes.x,
      marginRight: Sizes.xm,
    },
    expireEndView: {
      alignSelf: 'flex-end',
    },
    imgDeliveryIcon: {
      marginRight: Sizes.s,
    },
    left: {
      marginLeft: Sizes.s,
    },
    soldOutLabel: {color: colors.whiteColor},
    soldOutTag: {backgroundColor: colors.textError, padding: Sizes.xs},
    centerItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    deliveryView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

export default styles;

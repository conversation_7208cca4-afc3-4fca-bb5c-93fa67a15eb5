import {Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    locationCont: {
      flexDirection: 'row',
    },
    locationView: {
      width: Sizes.xl,
      height: Sizes.xl,
      backgroundColor: colors.incrementColor,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xl,
      marginRight: Sizes.s,
    },
    labelProductKey: {
      fontSize: Sizes.m,
      fontWeight: '400',
    },
    locationIconStyle: {
      width: Sizes.xm,
      height: Sizes.m,
    },
    horizontal: {
      paddingTop: Sizes.xm,
    },
    modelBackGround: {
      backgroundColor: colors.background2,
      borderRadius: Sizes.m,
      height: Sizes.windowHeight,
      width: Sizes.windowWidth,
      // paddingHorizontal: Sizes.m,
      paddingTop: Sizes.xxl,
      paddingBottom: Sizes.xl,
    },
    addressModelCont: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    modelInput: {
      flexDirection: 'row',
      marginVertical: Sizes.m,
      justifyContent: 'space-between',
    },
    inputLabel: {
      width: '65%',
      borderRadius: Sizes.xs,
      height: Sizes.xx4l,
    },
    buttonCheck: {
      width: '30%',
      height: Sizes.xx4l,
    },
    statusMessage: {
      bottom: Sizes.m,
    },
    countryButton: {
      borderWidth: Sizes.x,
      borderColor: colors.grey,
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.sx,
      height: Sizes.x6l,
      paddingHorizontal: Sizes.m,
    },
    deliveryCont: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xms,
      paddingVertical: Sizes.xm,
      alignItems: 'center',
      borderRadius: Sizes.xm,
      backgroundColor: colors.background,
      justifyContent: 'space-between',
    },
    imgDeliveryIcon: {
      marginRight: Sizes.s,
    },
    countryPicker: {
      width: '100%',
      borderWidth: Sizes.z,
      borderColor: colors.border,
      marginBottom: '3%',
      backgroundColor: colors.background,
    },
    arrowStyle: {
      tintColor: colors.textLight,
    },
    placeholderStyles: {
      color: colors.textLight,
    },
    orText: {
      textAlign: 'center',
    },
    mainCont: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginHorizontal: Sizes.m,
    },
    sepCSS: {
      backgroundColor: colors.borderSaprater,
      height: Sizes.s,
    },
    dotView: {
      paddingRight: '2%',
    },
    modelView: {width: '90%', backgroundColor: colors.background},
    buttonCenter: {height: Sizes.xl, alignSelf: 'center'},
    space: {marginHorizontal: Sizes.sx},
    centerItem: {alignItems: 'center'},
    codCheckView: {alignSelf: 'flex-end', right: Sizes.xms},
    imgLeft: {marginLeft: Sizes.s},
    changeView: {
      borderColor: colors.categoryTitle,
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.sx,
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
    },
    deliveryTruckView: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: Sizes.s,
      paddingHorizontal: Sizes.sx,
      paddingVertical: Sizes.xm,
      borderRadius: Sizes.xm,
      backgroundColor: colors.background,
    },
    fRow: {
      flexDirection: 'row',
    },
    addressView: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: Sizes.x,
    },
    fOne: {
      flex: Sizes.x,
    },
    deliveryDetails: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.m,
    },
    deliveryLine: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    deliveryItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    deliveryBtn: {
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      height: Sizes.x7l,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.xm,
      marginTop: Sizes.xms,
    },
    modalStyle: {
      margin: 0,
    },
    deliveryBtnView: {
      marginBottom: Platform.OS === 'ios' ? Sizes.x60 : Sizes.l,
    },
    addressBtnView: {
      marginBottom: Platform.OS === 'ios' ? Sizes.x60 : Sizes.z,
      marginHorizontal: Sizes.m,
    },
    seperator: {
      marginHorizontal: Sizes.s,
    },
    tagView: {
      backgroundColor: colors.aliceBlue,
      height: Sizes.x26,
      width: Sizes.x26,
      borderRadius: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: Sizes.xm,
    },
    addDesTxt: {
      marginLeft: Sizes.sx,
    },
    checkoutCard: {
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.m,
      borderRadius: Sizes.xm,
      backgroundColor: colors.background,
    },
    defaultGif: {
      width: Sizes.x70,
      height: Sizes.xl,
      borderRadius: Sizes.s,
      overflow: 'hidden',
    },
    changeView1: {
      flexDirection: 'row',
      alignItems: 'center',
      borderColor: colors.vividOrange,
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.sx,
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
    },
    vLine: {
      backgroundColor: colors.grey2,
      width: Sizes.x,
      height: Sizes.l,
      marginHorizontal: Sizes.sx,
    },
    hLine: {
      backgroundColor: colors.grey2,
      height: Sizes.x,
      marginVertical: Sizes.xm,
    },
    scrollStyle: {
      flexGrow: 1,
    },
  });

export default styles;

// import WebEngage from 'react-native-webengage';
// import firebase from 'react-native-firebase';
// var webengage = new WebEngage();

import { debugLog } from "utils/debugLog";

export const setLoginId = (loginId = '') => {
  // webengage.user.setOptIn('push', true);
  // webengage.user.setOptIn('in_app', true);
  // webengage.user.setOptIn('email', false);
  // webengage.user.setOptIn('sms', true);
  // webengage.user.login(loginId);
};

export const logout = () => {
  setEvent('User Logout', {});
  // webengage.user.logout();
};

export const setEmail = (email = '') => {
  // webengage.user.setEmail(email);
};

export const setPhone = (phone = '') => {
  // webengage.user.setPhone(phone);
};

export const setDOB = (dob = '') => {
  // webengage.user.setBirthDateString(dob);
};

export const setFirstName = (firstName = '') => {
  // webengage.user.setFirstName(firstName);
};

export const setLastName = (lastName = '') => {
  // webengage.user.setLastName(lastName);
};

export const enabledPushNotification = () => {
  debugLog('enabledPushNotification');
  // webengage.user.setDevicePushOptIn(true);
};

export const setEvent = async (eventName = '', data = {}) => {
  try {
    // webengage.track(eventName, data);
    // let firebaseEventName = eventName.replace(/ /g, '_');
    // firebaseEventName = firebaseEventName.replace(/ /g, '_');
    // await firebase.analytics().logEvent(firebaseEventName, data);
  } catch (e) {
    debugLog('setEvent error', e);
  }
};

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    durationsBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    box: {
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.xms,
      flex: Sizes.x,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xms,
    },
    approvedView: {
      backgroundColor: colors.oysterBay,
      alignItems: 'center',
      height: Sizes.xsl,
      justifyContent: 'center',
      paddingHorizontal: 11,
      borderRadius: Sizes.s,
    },
    imageBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    image: {
      borderWidth: Sizes.x,
      padding: Sizes.s,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      alignItems: 'center',
      justifyContent: 'center',
      width: Sizes.x52,
      height: Sizes.x52,
    },
    processingContinuer: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    imageSize: {
      width: Sizes.x44,
      height: Sizes.x44,
    },
    btnStyle: {
      height: Sizes.xx4l,
      width: 83,
    },
  });

export default styles;

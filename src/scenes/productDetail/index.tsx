import React, {
  startTransition,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import * as yup from 'yup';

import { RootStackParamsList } from '../../routes';
import {
  <PERSON><PERSON>,
  Header,
  RenderCustomHTML,
  SuggestProduct,
  ProductImageSlider,
  CartOfferCarousel,
} from 'components/molecules';
import stylesWithOutColor from './style';
import { RouteProp, useFocusEffect, useTheme } from '@react-navigation/native';
import DashedLine from 'react-native-dashed-line';
import {
  BulkModal,
  DownloadCatalogue,
  EmiModal,
  FaqItem,
  FooterButton,
  FreeBieModal,
  HorizontalScrollBar,
  ImageIcon,
  KnowMoreFooterModal,
  Label,
  PostQuestionModal,
  ProductCardVertical,
  Quantity,
  RateProductModal,
  ReferralModal,
  Separator,
  ShareModal,
  SimilarProductModal,
  Spacer,
  SuccessModal,
  Tag,
  TireSuccessModal,
  ValuableFeedbackModal,
  WishlistButton,
  WithGradient,
} from 'components/atoms';
import {
  View,
  TouchableOpacity,
  FlatList,
  Share as ShareRN,
  TextInput,
  ActivityIndicator,
  ImageBackground,
  ScrollView,
  LayoutAnimation,
  Platform,
  KeyboardAvoidingView,
  Share,
  InteractionManager,
  useWindowDimensions,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native';
import { t } from 'i18next';
import { navigate } from 'utils/navigationRef';
import {
  showErrorMessage,
  showInfoMessage,
  showSuccessMessage,
} from 'utils/show_messages';
import FastImage from 'react-native-fast-image';
import sortByPosition from 'utils/sort_by_position';
import { useDispatch, useSelector } from 'react-redux';
import {
  addToCart,
  notify,
  setLoading,
  getOfferProduct,
} from 'app-redux-store/slice/appSlice';
import localStorage from 'utils/localStorage';
import Icons from 'common/icons';
import { defaultPinCode, WEBSITE_URL } from 'config/environment';
import {
  AnalyticsEvents,
  FaqListModal,
  FullSizeProductImagesModal,
} from 'components/organisms';
import { dateTimeFormat } from 'utils/formatter';
import { Sizes } from 'common';
import { IconWithBackground } from 'components/hoc';
import {
  allRecommendedProducts,
  buyNow,
  getAllAttributesData,
  getAttributesByProductId,
  getChildProductsData,
  getFaqsData,
  getProductDetail,
  getReviewsData,
  getFrequentlyBought,
  getSimpleFreeBie,
  getGroupedFreeBie,
  likeFaqs,
} from 'services/productDetail';
import { addProductSuggestion, recentlyViewedProduct } from 'services/home';
import { ProductDetailLoader } from 'skeletonLoader';
import { checkServiceAvailability } from 'services/cart';
import DeliveryInfoModal from 'components/atoms/deliveryInfoModal';
import {paymentOptions, benefits} from 'staticData';
import TierPricingDetails from 'components/atoms/productTierPrice';
import FrequentlyBoughtTogether from 'components/atoms/fqbtContainer';
import RenderHTML from 'react-native-render-html';
import { checkDevice, handleErrMsg, truncateText, debounce, recommendedTabType } from 'utils/utils';
import ErrorBoundary from 'react-native-error-boundary';
import ErrorHandler from 'utils/ErrorHandler';
import {
  setCurrentScreenName,
  setCustomUserId,
} from '@microsoft/react-native-clarity';
import { useDebouncedCallback } from 'use-debounce';
import { resolveUrl } from 'utils/resolveUrl';
import { debugError, debugLog } from 'utils/debugLog';
import { ProductData } from '@types/local';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';
import useDeviceConfig from 'components/customHooks/useDeviceConfig';
import DeliveryDetails from 'components/molecules/deliveryDetails/deliveryDetails';
import FAQsComponent from 'components/molecules/faqComponent/FAQComponent';
import RatingsReviewsComponent from 'components/molecules/ratingReviews/RatingsReviews';
import ProductDoubtsSection from 'components/molecules/productDoubts/productDoubtsSection';
import ProductFooter from 'components/molecules/productFooter/productFooter';
import SingleTierSection from 'components/molecules/singleTierSection/singleTierSection';
import SearchProductComponent from 'components/molecules/groupProduct/groupProduct';
import AddToCartSection from './addToCartSection';
import base64 from 'react-native-base64';
import ReactMoE from 'react-native-moengage';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';
import { appsFlyerEvent } from 'components/organisms/analytics-Events/appsFlyerEvent';
import {getReferrals} from 'services/account';

type Benefits = {
  icon: keyof typeof Icons;
  label: string;
  content: string[];
};

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  gethomepagebrands: BasicBannerDetails;
  route: RouteProp<RootStackParamsList, 'ProductDetail'>;
  GroupedProductdetail?: boolean;
  ConfigureProductdetail?: boolean;
  value?: any;
};
let disable = true;

const ProductDetailScene = ({ navigation, route }: Props) => {
  const TAG = 'PoductDetailScreen';
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();

  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const getOfferItems = useSelector(
    (state: RootState) => state.app.getOfferItems,
  );
  const prevUserInfoRef = useRef(null);

  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const { itemHeight } = useDeviceConfig();

  const { productId, ProductItems, referralCode, referType, productSku } =
    route.params;
  const [product, setProduct] = useState<ProductData | null>(ProductItems);
  const [productLoader, setProductLoader] = useState(true);
  const [description, setDescription] = useState<AttributeItem | null>();
  const [modelVisible, setModelVisible] = useState(false);
  const [suggestProductModal, setSuggestProductModal] = useState(false);
  const [carouselIndex, setCarouselIndex] = useState(0);
  const [groupProduct, setGroupProduct] = useState<Array<ChildProduct>>([]);
  const [visibleFullScreenImg, setVisibleFullScreenImg] = useState(false);
  const [isShowFooterModal, setIsShowFooterModal] = useState(false);
  const [isShowEmiModal, setIsShowEmiModal] = useState({
    show: false,
    label: '',
  });
  const [isShowFeedbackModal, setIsShowFeedbackModal] = useState(false);
  const [bulkModal, setBulkModal] = useState(false);
  const [showFaqListModal, setShowFaqListModal] = useState(false);
  const [modalText, setModalText] = useState<Benefits>(benefits[0]);
  const [accordionActiveIndex, setAccordionActiveIndex] = useState<number>(-1);
  const [contentType, setContentType] = useState<
    'knowMore' | 'benefits' | 'paymentOptions' | 'suggestProduct'
  >('benefits');
  const [isAddToCartPressed, setIsAddToCartPressed] = useState(false);
  const [pdfFiles, setPdfFiles] = useState<ProductAttachment[]>([]);
  const [tagData, setTagData] = useState<ProductAttachment[]>([]);
  const [referralModel, setReferralModel] = useState(false);
  const [shareModel, setShareModel] = useState(false);
  const [postQuestionModel, setPostQuestionModel] = useState(false);
  const [transformedTierPrices, setTransformedTierPrices] = useState('');
  const [totalTierAmount, setTotalTierAmount] = useState(0);
  const [totalOriginalAmount, setTotalOriginalAmount] = useState(0);
  const [totalItemCount, setTotalItemCount] = useState(0);
  const [faqQuestions, setFaqQuestions] = useState<QuestionsOutput | null>(
    null,
  );
  const [faqQuestionsModal, setFaqQuestionsModal] =
    useState<QuestionsOutput | null>(null);

  // =============single tire prices state===================
  const [transformedSinglePricesData, setTransformedSinglePricesData] =
    useState('');

  const [totalSingleAmount, setTotalSingleAmount] = useState(0);
  const [totalSingleItemCount, setTotalSingleItemCount] = useState(0);
  const [GPItem, setGPItem] = useState([]);
  const [singleProductTierText, setSingleProductTierText] = useState([]);
  const [singleProductPrice, setSingleProductPrice] = useState(0);
  const [singleProductTierModal, setSingleProductTierModal] = useState(false);
  const [congratsMsgShow, setCongratsMsgShow] = useState(false);
  // ============================================================
  const [searchQuery, setSearchQuery] = useState('');
  const [searchQueryModal, setSearchQueryModal] = useState('');
  const [searchProductQuery, setSearchProductQuery] = useState('');
  const [faqLoading, setFaqLoading] = useState(false);
  const [getFaqLoading, setGetFaqLoading] = useState(false);
  const [checkPincodeLoading, setCheckPincodeLoading] = useState(false);
  const [faqSearchText, setFaqSearchText] = useState('');
  const [review, setReview] = useState<ProductReviewResponse | null>();
  const [reviewChange, setReviewChange] =
    useState<ProductReviewResponse | null>();
  const [tierPriceIndex, setTierPriceIndex] = useState(0);
  const [productImages, setProductImages] = useState<MediaGalleryEntry[]>();
  const [notifyIcons, setNotifyIcons] = useState({});

  const [relatedProductData, setRelatedProductData] = useState<{
    [key: string]: string;
  }>({});
  const [selectedItems, setSelectedItems] = useState<ProductData[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const isInStock = product?.inventory?.is_in_stock;
  const [availablePaymentMethod, setAvailablePaymentMethod] = useState<
    PaymentMethodResponseV2 | undefined
  >(null);
  const [deliveryStatusData, setDeliveryStatusData] =
    useState<DeliveryInfo | null>(null);

  const [sellImage, setSellImage] = useState();
  const [rateProductModal, setRateProductModal] = useState<Boolean>(false);
  const [frequentlyBoughtProducts, setFrequentlyBoughtProducts] = useState([]);
  const previousOffsetY = useRef(0);
  const [offerModal, setOfferModal] = useState(false);
  const [selectedAddress, setSelectedAddress] =
    useState<Partial<CustomerAddressV2> | null>(null);
  const [postCode, setPostCode] = useState<string>('');
  const [returnInfoModel, setReturnInfoModel] = useState(false);
  const [similarProductModal, setSimilarProductModal] = useState(false);
  const [infoIcon, setInfoIcon] = useState('');
  const [qtyForUI, setQtyForUI] = useState(0);
  const qtyRef = useRef(0);
  const [successTitle, setSuccessTitle] = useState('');
  const [isReset, setIsReset] = useState(false);
  const [selectedGroupProductsForUI, setSelectedGroupProductsForUI] = useState<
    Array<{ data: { sku: string; quantity: number } }>
  >([]);
  const selectedGroupedProductsRef = useRef<
    Array<{ data: { sku: string; quantity: number } }>
  >([]);
  const [selectedSingleProducts, setSelectedSingleProducts] = useState<
    Array<{ data: { sku: string; quantity: number } }>
  >([]);
  const [bulkOrderPrice, setBulkOrderPrice] = useState<ProductPrices | null>(
    null,
  );
  const [freeBieLoading, setFreeBieLoading] = useState(true);
  const [reqPriceModal, setReqPriceModal] = useState(false);
  const [successModel, setSuccessModel] = useState(false);
  const [freeBie, setFreeBie] = useState([]);
  const [freeBieModal, setFreeBieModal] = useState(false);
  const [freeBieMessage, setFreeBieMessage] = useState('');
  const flatListRefTop = useRef(null);
  const [notifyIcon, setNotifyIcon] = useState(false);
  const ratingHeightRef = useRef(0);
  const [openIndex, setOpenIndex] = useState(0);
  const [itemQty, setItemQty] = useState(0);
  const [referCoin, setReferCoin] = useState(0);
  const [moreHtmlView, setMoreHtmlView] = useState(false);
  const [notifyLoading, setNotifyLoading] = useState(false);
  const [isBuyNowInProgress, setIsBuyNowInProgress] = useState(false);
  const [isAddToCartInProgress, setIsAddToCartInProgress] = useState(false);
  const [showScrollToTopButton, setShowScrollToTopButton] = useState(false);
  const [selectedTab, setSelectedTab] = useState(recommendedTabType.Similar);
  const scrollX1 = useRef(new Animated.Value(0)).current;
  const [scrollX, setScrollX] = useState(0);
  const { width: screenWidth } = useMemo(() => Dimensions.get('window'), []);
  const [scrollBarWidth, setScrollBarWidth] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);
  const [listWidth, setListWidth] = useState(0);
  const horizontalFlatListRef = useRef(null);
  const [isScrolling, setIsScrolling] = useState(false);
  const [pinCodeWrong, setPinCodeWrong] = useState(false);
  const [refersRecord, setRefersRecord] = useState();
  const [refersLoader, setRefersLoader] = useState(false);
  const handleItemToggleInternal = useCallback(index => {
    // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setOpenIndex(prevIndex => (prevIndex === index ? null : index));
    setMoreHtmlView(false);
  }, []);
  const handleItemToggle = useCallback(
    debounce(handleItemToggleInternal, 200),
    [handleItemToggleInternal],
  );

  const updateQtyForUIAndRef = useCallback((qty: number) => {
    setQtyForUI(qty);
    qtyRef.current = qty;
  }, []);

  const isMounted = useRef(true);
  const quantityInputRef = useRef<TextInput>(null);

  useFocusEffect(
    useCallback(() => {
      scrollToTop();
      if (productId && product?.selling_price) {
        trackEvent('VIEW_CONTENT', {
          contentId: productId,
          contentType: 'product',
          currency: 'INR',
          value: product?.selling_price,
          // params: { product: product }
        });

        // appFlyer product views Event
        appsFlyerEvent('ProductViewed', {
          price: product?.selling_price,
          name: product?.name,
          p_id: productId,
          currency: 'INR',
        });
      }
    }, [route.params]),
  );

  useEffect(() => {
    if (isLoggedIn) {
      recentlyViewedProduct({ product_id: Number(productId) });
    }
    setCurrentScreenName('PDP page');
    return () => {
      isMounted.current = false;
      setCurrentScreenName('Clarity event');
    };
  }, [route.params]);

  const handleItemToggleOffer = useCallback(() => {
    LayoutAnimation.configureNext({
      duration: 500,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      delete: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
  }, []);

  const handleScroll = useCallback(event => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setShowScrollToTopButton(
      offsetY > 0 && offsetY < previousOffsetY.current ? true : false,
    );
    setTimeout(() => {
      previousOffsetY.current = offsetY;
    }, 300);
  }, []);

  const ratingLayout = useCallback(event => {
    const { layout } = event.nativeEvent;
    ratingHeightRef.current = layout.y;
  }, []);

  const addedItem = useMemo(() => {
    return parseFloat(selectedSingleProducts.map(i => i.data.quantity));
  }, [selectedSingleProducts]);

  const getProductReferral = async () => {
   if (isLoggedIn) {
      setRefersLoader(true);
      let {status, data} = await getReferrals({
        refer_type: 'PRODUCT',
        product_sku: product?.sku,
        url_key: product?.seo?.url_key,
      });
      setRefersLoader(false);
      if (data && status) {
        setRefersRecord(data);
      } else {
        setRefersRecord(undefined);
        showErrorMessage(handleErrMsg(data));
      }
    }
  };

  const scrollToTop = useCallback(() => {
    Keyboard.dismiss();
    if (flatListRefTop.current) {
      flatListRefTop.current.scrollToOffset({ offset: 0, animated: true });
    }
  }, [flatListRefTop]);

  const flatListRef1 = useRef(null);
  const scrollToSpecificOffset = useCallback(() => {
    flatListRef1.current?.scrollToOffset({
      offset: ratingHeightRef.current,
      animated: true,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [flatListRef1]);

  const openSimilarProductModal = useCallback(() => {
    setSimilarProductModal(true);
  }, [setSimilarProductModal]);

  const openShareModel = useCallback(() => {
    setShareModel(true);
  }, [setShareModel]);
  const findFirstActiveIndex = useCallback((object: any) => {
    let index = Object.keys(object).find(key =>
      object[key]?.rows?.some((i: { active: boolean }) => i.active === true),
    );
    return index;
  }, []);

  // const onUpdateQuantity = useCallback(
  //   (quantity: number, productPrice: number, sku: string) => {
  //     if (Object.keys(transformedTierPrices)?.length === 0) {
  //       return;
  //     }
  //     var newArray = transformedTierPrices?.[productPrice]?.rows;
  //     var items = transformedTierPrices?.[productPrice]?.items;
  //     transformedTierPrices?.[productPrice]?.rows?.map((item, index) => {
  //       let skuItemIndex = items?.findIndex(i => i.sku === sku);
  //       if (skuItemIndex !== -1) {
  //         items[skuItemIndex].qty = quantity;
  //       } else {
  //         items.push({sku, qty: quantity});
  //       }
  //       const totalQty = Array.isArray(items)
  //         ? items.reduce((prev, current) => Number(current?.qty) || 0 + prev, 0)
  //         : 0;
  //       transformedTierPrices[productPrice].items = items;

  //       if (index === 0 && totalQty < newArray[index]?.qtyLimit) {
  //         newArray[index].qty = totalQty;
  //       } else if (
  //         totalQty >= newArray[index]?.qtyLimit &&
  //         (totalQty < newArray[index + 1]?.qtyLimit ||
  //           index + 1 === newArray.length)
  //       ) {
  //         newArray[index].qty = totalQty;
  //       } else {
  //         newArray[index].qty = 0;
  //       }
  //     });
  //     const totalTierAmount = Object.values(transformedTierPrices)?.reduce(
  //       (prev, current) => {
  //         if (!current || !Array.isArray(current.rows)) {
  //           return prev;
  //         }

  //         const total = current.rows.reduce((last, next) => {
  //           return (
  //             (next?.qtyLimit > next?.qty || next.active === false
  //               ? next.minimalPrice
  //               : next?.priceLimit) *
  //               next?.qty +
  //             last
  //           );
  //         }, 0);
  //         debugLog(total ,'total')
  //         debugLog(prev + total, 'prev + total**')
  //         return prev + total;
  //       },
  //       0,
  //     );

  //     const totalRegularPriceAmount = Object.values(
  //       transformedTierPrices,
  //     )?.reduce((prev, current) => {
  //       const total = current.rows.reduce(
  //         (last, next) => next?.regularPrice * next?.qty + last,
  //         0,
  //       );
  //       return prev + total;
  //     }, 0);

  //     transformedTierPrices[productPrice].rows = newArray;
  //     startTransition(() => {
  //       if (
  //         transformedTierPrices[productPrice].rows?.some(i => i.active === true)
  //       ) {
  //         setTierPriceIndex(String(productPrice));
  //       }
  //       setTotalOriginalAmount(totalRegularPriceAmount);
  //       debugLog(totalTierAmount,'totalTierAmount')
  //       setTotalTierAmount(totalTierAmount);
  //     });
  //   },
  //   [
  //     transformedTierPrices,
  //     setTierPriceIndex,
  //     setTotalOriginalAmount,
  //     setTotalTierAmount,
  //   ],
  // );
  // ==================single product quntity selector================

  const onUpdateQuantity = useCallback(
    (quantity: number, productPrice: number, sku: string) => {
      if (Object.keys(transformedTierPrices)?.length === 0) {
        return;
      }
      const priceData = transformedTierPrices?.[productPrice];
      if (!priceData) return;
      const newArray = [...priceData.rows]; // Ensure immutability
      let items = [...priceData.items]; // Copy items array
      // Find if SKU already exists
      let existingItem = items.find(i => i.sku === sku);
      if (existingItem) {
        existingItem.qty = quantity; // Update existing quantity
      } else {
        items.push({ sku, qty: quantity }); // Add new entry
      }
      // Compute total quantity for the given price tier
      const totalQty = items.reduce(
        (prev, curr) => prev + (Number(curr.qty) || 0),
        0,
      );
      priceData.items = items; // Update the items array
      // Update tiered pricing rules
      newArray.forEach((tier, index) => {
        if (index === 0 && totalQty < newArray[index]?.qtyLimit) {
          newArray[index].qty = totalQty;
        } else if (
          totalQty >= newArray[index]?.qtyLimit &&
          (totalQty < newArray[index + 1]?.qtyLimit ||
            index + 1 === newArray.length)
        ) {
          newArray[index].qty = totalQty;
        } else {
          newArray[index].qty = 0;
        }
      });
      transformedTierPrices[productPrice].rows = newArray;
      // **Recalculate total tier amount**
      const newTotalTierAmount = Object.values(transformedTierPrices)?.reduce(
        (prev, current) => {
          if (!current || !Array.isArray(current.rows)) {
            return prev;
          }
          return (
            prev +
            current.rows.reduce((last, next) => {
              const priceToUse =
                next.qtyLimit > next.qty || next.active === false
                  ? next.minimalPrice
                  : next.priceLimit;
              return last + priceToUse * next.qty;
            }, 0)
          );
        },
        0,
      );
      const newTotalRegularPriceAmount = Object.values(
        transformedTierPrices,
      )?.reduce((prev, current) => {
        return (
          prev +
          current.rows.reduce((last, next) => {
            return last + next.regularPrice * next.qty;
          }, 0)
        );
      }, 0);
      startTransition(() => {
        if (
          transformedTierPrices[productPrice].rows?.some(i => i.active === true)
        ) {
          setTierPriceIndex(String(productPrice));
        }
        setTotalOriginalAmount(newTotalRegularPriceAmount);
        debugLog(newTotalTierAmount, 'newTotalTierAmount');
        setTotalTierAmount(newTotalTierAmount);
      });
    },
    [
      transformedTierPrices,
      setTierPriceIndex,
      setTotalOriginalAmount,
      setTotalTierAmount,
    ],
  );
  const transformTierSinglePrices = useCallback(data => {
    const tierPrices = data?.pricing?.tier_price || [];
    const priceValue = data?.pricing?.selling_price ?? 0;
    const regularPrice = data?.pricing?.price ?? 0;
    const transformed = {
      [data?.pricing?.selling_price]: {
        rows: tierPrices.map(tier => ({
          qtyLimit: tier?.qty,
          priceLimit: tier?.value,
          regularPrice: regularPrice,
          minimalPrice: priceValue,
          qty: 0,
          text: `Buy ${tier?.qty} or above for ₹${tier?.value} each`,
          savings: (100 - (tier?.value * 100) / priceValue).toFixed(2),
        })),
        items: [],
      },
    };

    return transformed;
  }, []);

  const onUpdateQuantitySingleProduct = useCallback(
    (quantity: number, productPrice: number, sku: string) => {
      if (
        !transformedSinglePricesData ||
        Object.keys(transformedSinglePricesData)?.length === 0
      ) {
        return;
      }

      // Ensure productPrice entry exists
      if (!transformedSinglePricesData[productPrice]) {
        transformedSinglePricesData[productPrice] = { rows: [], items: [] };
      }

      let newArray = transformedSinglePricesData?.[productPrice]?.rows;
      let items = transformedSinglePricesData?.[productPrice]?.items;

      let skuItemIndex = items?.findIndex(i => i.sku === sku);
      if (skuItemIndex !== -1) {
        items[skuItemIndex].qty = quantity;
      } else {
        items.push({ sku, qty: quantity });
      }

      transformedSinglePricesData[productPrice].items = items;

      const totalQty = Array.isArray(items)
        ? items.reduce((prev, current) => Number(current?.qty) || 0 + prev, 0)
        : 0;

      newArray.forEach((tier, index) => {
        if (index === 0 && totalQty < newArray[index]?.qtyLimit) {
          newArray[index].qty = totalQty;
        } else if (
          totalQty >= newArray[index]?.qtyLimit &&
          (totalQty < newArray[index + 1]?.qtyLimit ||
            index + 1 === newArray.length)
        ) {
          newArray[index].qty = totalQty;
        } else {
          newArray[index].qty = 0;
        }
      });

      const totalTierAmount = Object.values(
        transformedSinglePricesData,
      )?.reduce((prev, current) => {
        if (!current || !Array.isArray(current.rows)) {
          return prev;
        }

        const total = current?.rows?.reduce((last, next) => {
          if (!next || typeof next.qty !== 'number') {
            return last;
          }

          const priceToUse =
            next.qtyLimit > next.qty || next.active === false
              ? next.minimalPrice
              : next.priceLimit;

          const validPrice = typeof priceToUse === 'number' ? priceToUse : 0;
          const validQty = typeof next.qty === 'number' ? next.qty : 0;

          return last + validPrice * validQty;
        }, 0);

        return prev + total;
      }, 0);

      const singleTotalItemCount = Object.values(
        transformedSinglePricesData,
      )?.reduce((prev, current) => {
        const total = current.rows.reduce(
          (last, next) => Number(next?.qty || 0) + last,
          0,
        );
        return prev + total;
      }, 0);

      transformedSinglePricesData[productPrice].rows = newArray;

      startTransition(() => {
        if (
          transformedSinglePricesData[productPrice].rows?.some(
            i => i.active === true,
          )
        ) {
          setTierPriceIndex(String(productPrice));
        }

        setTotalSingleAmount(totalTierAmount);
        setTotalSingleItemCount(singleTotalItemCount);
      });
    },
    [
      transformedSinglePricesData,
      setTierPriceIndex,
      setTotalSingleAmount,
      setTotalSingleItemCount,
    ],
  );

  const GPQty = useMemo(() => {
    return GPItem.reduce((ac, cv) => ac + cv, 0);
  }, [GPItem]);

  useEffect(() => {
    if (product?.type !== 'grouped') {
      const firstKey = Object.keys(transformedSinglePricesData)[0];
      const rows = transformedSinglePricesData[firstKey]?.rows ?? [];
      let matchingRow = rows
        .filter(row => totalSingleItemCount < row.qtyLimit)
        .sort((a, b) => a.qtyLimit - b.qtyLimit)[0];
      const exactTierIndex = rows.findIndex(row => row.qty === row.qtyLimit);
      setCongratsMsgShow(exactTierIndex !== -1 ? true : false);
      if (!matchingRow && rows.length > 0) {
        matchingRow = [...rows].sort((a, b) => b.qtyLimit - a.qtyLimit)[0];
      }
      const exactMatch = rows.find(
        row => totalSingleItemCount === row?.qtyLimit,
      );
      startTransition(() => {
        if (matchingRow) {
          setSingleProductTierText(matchingRow.text);
          setSingleProductPrice(matchingRow.minimalPrice);
        }
        if (exactMatch && totalSingleItemCount > itemQty) {
          setSingleProductTierModal(true);
          setItemQty(totalSingleItemCount);
        } else {
          // setSingleProductTierModal(false);
          setItemQty(totalSingleItemCount);
        }
      });
    }
  }, [
    addedItem,
    totalSingleItemCount,
    GPItem,
    transformedSinglePricesData,
    itemQty,
    product,
  ]);

  // ============================================================

  const onSubmitProductSuggestion = useCallback(
    async (formData: {
      productName: string;
      brandName?: string;
      comment?: string;
      url?: string;
      email?: string;
    }) => {
      if (formData?.productName) {
        try {
          dispatch(setLoading(true));

          const { data, status } = await addProductSuggestion({
            searched_key: '',
            product_name: formData?.productName,
            brand: formData?.brandName,
            comment: formData?.comment,
            url: formData?.url,
            user: formData?.email
              ? formData?.email
              : isLoggedIn
                ? userInfo?.email
                : 'guest_user',
          });
          dispatch(setLoading(false));
          if (status) {
            setSuggestProductModal(false);
            setIsReset(true);
            setSuccessTitle(t('toastMassages.suggestMsg'));
            setSuccessModel(true);
            setTimeout(() => {
              setSuccessModel(false);
            }, 3500);
          }
        } catch (err) {
          debugLog(err);
          dispatch(setLoading(false));
        }
      } else {
        showErrorMessage(t('searchProduct.errorProduct'));
      }
    },
    [isLoggedIn, userInfo?.email],
  );

  const checkDeliveryStatus = useCallback(async () => {
    if (
      (product?.type === 'grouped' && groupProduct?.length > 0) ||
      (product?.type !== 'grouped' && productId)
    ) {
      if (selectedAddress?.postcode) {
        await localStorage.set('delivery_address', selectedAddress);
      }
      setCheckPincodeLoading(true);
      const productIds: Array<number> =
        product?.type === 'grouped'
          ? groupProduct.map(item => item.product_id)
          : [productId];
      const { data, status }: { data: any } = await checkServiceAvailability({
        postcode: Number(selectedAddress?.postcode),
        country_code: selectedAddress?.country_id,
        product_ids: productIds,
      });
      startTransition(() => {
        if (status) {
          setDeliveryStatusData(data?.response);
          setCheckPincodeLoading(false);
        } else {
          setCheckPincodeLoading(false);
          showErrorMessage(handleErrMsg(data));
        }
      });
    }
  }, [
    groupProduct,
    product?.type,
    productId,
    selectedAddress?.country_id,
    selectedAddress?.postcode,
  ]);

  const transformTierPrices = useCallback((childProducts: ChildProduct[]) => {
    const result = {};
    if (childProducts && Array.isArray(childProducts)) {
      for (const item of childProducts) {
        const priceValue = item?.pricing?.selling_price ?? 0;
        const regularPrice = item?.pricing?.price ?? 0;
        const tierPrices =
          item?.pricing?.tier_price && Array.isArray(item?.pricing?.tier_price)
            ? item?.pricing?.tier_price.map(tierPrice => ({
              text: `Buy ${tierPrice.qty} or above for ₹${tierPrice.value} each`,
              savings: (100 - (tierPrice.value * 100) / priceValue).toFixed(
                2,
              ),
              active: true,
              qty: 0,
              qtyLimit: tierPrice.qty,
              priceLimit: tierPrice.value,
              regularPrice: regularPrice,
              minimalPrice: priceValue,
            }))
            : [];
        if (tierPrices.length > 0) {
          result[priceValue] = { rows: tierPrices, items: [] };
        } else {
          result[priceValue] = {
            rows: [
              {
                text: '',
                savings: 0,
                active: false,
                qty: 0,
                qtyLimit: 0,
                priceLimit: 0,
                regularPrice: regularPrice,
                minimalPrice: priceValue,
              },
            ],
            items: [],
          };
        }
      }
    }
    if (Object.values(result).some(o => o.rows.some(i => i.active === true))) {
      return result;
    } else {
      return {};
    }
  }, []);

  const currentRequest = useRef<AbortController | null>(null);

  useEffect(() => {
    return () => {
      isMounted.current = false;
      // Cancel any pending request
      const controller = currentRequest.current;
      if (controller) {
        controller.abort();
      }
    };
  }, []);

  const productData = useCallback(async () => {
    if (currentRequest.current) {
      currentRequest.current.abort();
    }

    currentRequest.current = new AbortController();

    clearCartPrice();
    const { data }: { data: ProductData } = await getProductDetail(productId);
    setProductLoader(false);
    setProduct(data);
    const activeEntries = data?.media?.filter(entry => !entry.disabled);
    if (activeEntries) {
      setProductImages(activeEntries);
    } else {
      const placeholderMedia = {
        file: '',
        disabled: false,
        id: 0,
        label: '',
        media_type: 'image',
        position: 0,
        types: [],
        video_content: null,
      };
      setProductImages([placeholderMedia]);
    }

    requestAnimationFrame(() => {
      const generatedSingleData = transformTierSinglePrices(data);

      setTransformedSinglePricesData(generatedSingleData);

      AnalyticsEvents(
        'PRODUCT_SCREEN',
        'Product Viewed',
        data,
        userInfo,
        isLoggedIn,
      );
      if (userInfo) {
        // Check if we need to update ReactMoE
        const shouldUpdate =
          !prevUserInfoRef.current ||
          prevUserInfoRef.current.id !== userInfo.id ||
          prevUserInfoRef.current.email !== userInfo.email ||
          prevUserInfoRef.current.mobile !== userInfo.mobile;

        if (shouldUpdate) {
          // Only update ReactMoE when user info actually changes
          let eventID;
          if (userInfo?.email) {
            eventID = base64.encode(userInfo.email.toString());
          } else {
            eventID = base64.encode(
              userInfo.mobile?.toString() + '@dentalkart.user',
            );
          }
          ReactMoE.setUserUniqueID(eventID);
          setCustomUserId(eventID)
            .then(() => {
              debugLog('Clarity User ID set', eventID);
            })
            .catch(e => {
              debugLog('Clarity User ID error', e);
            });
          prevUserInfoRef.current = { ...userInfo };
        }
      }
      // let activeEntries = data?.media?.filter(
      //   entry => !entry.disabled && entry.media_type === 'image',
      // );
      // const imageMedia = data?.media?.filter(
      //   entry => !entry.disabled && entry.media_type === 'image',
      // );
      // const videoMedia = data?.media?.filter(
      //   entry => entry.media_type === 'external-video',
      // );
      // activeEntries = sortByPosition(imageMedia);

      // setProductImages([...activeEntries, ...videoMedia]);
      if (data?.type === 'grouped') {
        getChildProducts();
      } else {
        getReviews([]);
        setGroupProduct([]);
      }
    });
  }, [productId]);

  const getChildProducts = useCallback(async () => {
    const { data } = await getChildProductsData(productId);

    if (data?.child_products) {
      setGroupProduct(data?.child_products);
      const generatedData = transformTierPrices(data?.child_products);

      setTransformedTierPrices(generatedData);
    }
    getReviews(data?.child_products?.map(e => e?.product_id) ?? []);
  }, [productId]);

  const getFaqs = useCallback(async () => {
    const { data } = await getFaqsData(productId, faqSearchText);
    if (data?.result) {
      setFaqQuestions(data);
      setFaqQuestionsModal(data);
    }
  }, [faqSearchText, productId]);

  const handleLikeDislike = useCallback(
    async (postId, newLikes, newDislikes) => {
      const { data, status } = await likeFaqs({
        _id: postId,
        like: newLikes,
        dislike: newDislikes,
      });
      if (status && data) {
        showSuccessMessage(t('toastMassages.feedbackMessage'));
      }
      getFaqs();
    },
    [getFaqs],
  );

  const editQuestion = useCallback(async () => {
    await getFaqs();
  }, [getFaqs]);

  const getReviews = useCallback(
    async (childIds: Array<number>, filter?: any) => {
      const { data } = await getReviewsData(
        productId,
        filter || 'most_recent',
        1,
        35,
        childIds,
      );

      if (data) {
        if (filter) {
          setReviewChange(data);
        } else {
          setReviewChange(data);
          setReview(data);
        }
      }
    },
    [productId],
  );

  const getFrequentlyBoughtData = useCallback(async () => {
    const { data } = await getFrequentlyBought(productId);

    if (data && data?.related?.products) {
      setFrequentlyBoughtProducts(data?.related?.products);
    }
  }, [productId]);

  yup.addMethod(yup.number, 'maxPrize', function (errorMessage) {
    return this.test('test-max-prize', errorMessage, function (value) {
      const { path, createError } = this;
      return (
        (value &&
          bulkOrderPrice?.minimalPrice?.amount?.value &&
          value <= bulkOrderPrice?.minimalPrice?.amount?.value &&
          value >= bulkOrderPrice?.minimalPrice?.amount?.value * 0.5) ||
        createError({ path, message: errorMessage })
      );
    });
  });

  const renderersProps = useMemo(
    () => ({
      a: {
        onPress: () => {
          navigate(
            'ProductDescription',
            { productId: product?.product_id },
            { product: product },
          );
        },
      },
    }),
    [product],
  );

  const updateSelectedGroupProducts = useCallback(
    (qty: number, productPrice: number, sku: string) => {
      debugLog('mbl updateSelectedGroupProducts', sku, qty);
      const prevSelectedProducts = selectedGroupedProductsRef.current;

      let selectedProducts = prevSelectedProducts.filter(
        product => product?.data?.sku !== sku,
      );

      if (qty > 0) {
        let obj = { sku, quantity: qty, parent_id: product?.product_id };
        if (sku === productSku) {
          obj['referral_code'] = referralCode;
        }
        selectedProducts.push({ data: obj });
      }

      // Ensure correct count
      const uniqueSku = new Set(selectedProducts.map(i => i?.data?.sku)).size;
      setTotalItemCount(uniqueSku > 0 ? uniqueSku : 0);
      setGPItem(selectedProducts.map(i => i?.data?.quantity));

      // Return updated selectedProducts for state update
      selectedGroupedProductsRef.current = selectedProducts;
      selectedProducts.forEach(item => {
        debugLog('mbl selectedProducts', item?.data?.sku, item?.data?.quantity);
      });

      setSelectedGroupProductsForUI(selectedProducts);
      onUpdateQuantity(qty, productPrice, sku);

      const rows = transformedTierPrices[productPrice]?.rows || [];
      if (rows.length > 0) {
        const exactTierIndex = rows.findIndex(row => row.qty === row.qtyLimit);
        if (exactTierIndex !== -1) {
          setSingleProductTierModal(true);
          const nextTier = rows[exactTierIndex];
          if (nextTier) {
            setCongratsMsgShow(true);
            setSingleProductTierText(nextTier.text);
            setSingleProductPrice(nextTier.minimalPrice);
          }
        } else {
          const nextTier = getTierByTotalQty(rows);
          // const nextTier = rows.find(row => row.qty < row.qtyLimit);
          setCongratsMsgShow(false);
          setSingleProductTierText(nextTier ? nextTier.text : '');
          setSingleProductPrice(nextTier ? nextTier.minimalPrice : 0);
        }
      }
    },
    [onUpdateQuantity, transformedTierPrices, productSku, referralCode],
  );

  const getTierByTotalQty = rows => {
    const totalQty = rows.reduce((sum, item) => sum + item.qty, 0);
    // Sort by qtyLimit ascending
    const sortedRows = [...rows].sort((a, b) => a.qtyLimit - b.qtyLimit);
    if (totalQty <= 0) return null;
    let matchedTier = null;
    for (let i = 0; i < sortedRows.length; i++) {
      const current = sortedRows[i];
      const next = sortedRows[i + 1];
      if (
        (totalQty <= current.qtyLimit || totalQty >= current.qtyLimit) &&
        (!next || totalQty < next.qtyLimit)
      ) {
        matchedTier = current;
        break;
      }
      // If totalQty is >= last tier's qtyLimit
      if (!next && totalQty >= current.qtyLimit) {
        matchedTier = current;
      }
    }
    return matchedTier;
  };

  const suggestProductPressed = useCallback(() => {
    setContentType('suggestProduct');
    setSuggestProductModal(true);
  }, []);

  // =======================updateSelected  Single Products===============
  const updateSelectedSingleProducts = useCallback(
    (qty: number, productPrice: number, sku: string) => {
      updateQtyForUIAndRef(qty);
      let selectedProducts = selectedSingleProducts.filter(
        product => product?.data?.sku !== sku,
      );
      if (qty > 0) {
        selectedProducts.push({
          data: { sku: sku, quantity: qty },
        });
      }
      setSelectedSingleProducts(selectedProducts);
      onUpdateQuantitySingleProduct(qty, productPrice, sku);
    },
    [selectedSingleProducts, onUpdateQuantitySingleProduct],
  );

  const notifyClick = useCallback(async () => {
    try {
      if (!isLoggedIn) {
        showInfoMessage(t('toastMassages.loginInfo'));
        navigate('Login', {
          nextScreenName: 'ProductDetail',
          nextScreenParams: { productId: productId },
        });
        return;
      }
      setNotifyLoading(true);
      const response = await dispatch(notify(product?.product_id));
      setNotifyLoading(false);
      if (response) {
        showSuccessMessage(t('PDP.notify'));
        setNotifyIcon(true);
      }
    } catch (error) {
      setNotifyLoading(false);
      debugError(error);
    }
  }, [dispatch, product?.product_id]);

  const clearCartPrice = useCallback(() => {
    startTransition(() => {
      updateQtyForUIAndRef(0);
      setSelectedGroupProductsForUI([]);
      selectedGroupedProductsRef.current = [];
      setGPItem([]);
      setSelectedSingleProducts([]);
      setTotalTierAmount(0);
      setTotalOriginalAmount(0);
      setIsAddToCartPressed(false);
      setTierPriceIndex(0);
      setTotalItemCount(0);
      if (product?.type === 'grouped') {
        getChildProducts();
      }
    });
  }, []);

  const filteredProducts = useMemo(
    () =>
      groupProduct.filter(childProduct =>
        childProduct?.name
          .toLowerCase()
          .includes(searchProductQuery?.toLowerCase()),
      ),
    [groupProduct, searchProductQuery],
  );

  const addToCartPress = useCallback(async () => {
    await flushQuantityUpdate();
    if (product?.type === 'grouped') {
      if (selectedGroupedProductsRef.current.length === 0) {
        return showErrorMessage(t('PDP.productQuantity'));
      }
    } else {
      if (qtyRef.current === 0) {
        return showErrorMessage(t('PDP.selectedQuantity'));
      }
    }
    setIsAddToCartInProgress(true);
    let cartObj;
    if (product.type === 'grouped') {
      cartObj = selectedGroupedProductsRef.current;
    } else {
      let obj = {
        quantity: qtyRef.current,
        sku: product?.sku,
      };
      if (referralCode && product?.sku === productSku) {
        obj['referral_code'] = referralCode;
      }
      cartObj = [{ data: obj }];
    }
    const obj = {
      cart_items: cartObj,
      image: product?.media?.find(item => item.file)?.file,
    };
    try {
      await dispatch(addToCart(obj));

      clearCartPrice();
      const cartData =
        product?.type === 'grouped'
          ? {
            filteredProducts,
            product,
            selectedGroupProducts: selectedGroupedProductsRef.current,
            deliveryStatusData,
          }
          : { ...product, qty: qtyRef.current, deliveryStatusData };

      // Trigger the appropriate cart event
      AnalyticsEvents(
        product?.type === 'grouped'
          ? 'ADDED_TO_CART_GROUP'
          : 'ADDED_TO_CART_SIMPLE',
        'Added to Cart',
        cartData,
        userInfo,
        isLoggedIn,
      );
      trackEvent('ADD_TO_CART', {
        contentId: cartData?.product_id,
        contentType: 'product',
        currency: 'INR',
        value: cartData?.pricing?.selling_price,
        // params: { cartData: cartData }
      });

      // appFlyer Add to Cart Event Data
      const appsFlyerAddToCartData =
        product?.type === 'grouped'
          ?
          {
            price: cartData?.product?.pricing?.selling_price,
            sku: cartData?.product?.sku,
            productId: cartData?.product?.product_id,
            category: cartData?.product?.name,
            currency: 'INR',
            quantity: itemQty,
          }
          :
          {
            price: cartData?.pricing?.selling_price,
            sku: cartData?.sku,
            productId: cartData?.product_id,
            category: cartData?.name,
            currency: 'INR',
            quantity: itemQty,
          }
      appsFlyerEvent('AddToCart', appsFlyerAddToCartData);

    } catch (error) {
      debugError(error);
    } finally {
      setIsAddToCartInProgress(false);
    }
  }, [
    product,
    selectedGroupProductsForUI,
    referralCode,
    productSku,
    clearCartPrice,
    filteredProducts,
    userInfo,
    isLoggedIn,
  ]);

  const onSuccess = useCallback((type: string) => {
    if (type === 'valuableFeedback') {
      setIsShowFeedbackModal(false);
      setSuccessTitle(t('feedback.valuableFeedbackSuccess'));
    } else if (type === 'bulk') {
      setBulkModal(false);
      setReqPriceModal(false);
      setSuccessTitle(t('bulk.bulkSuccessMsg'));
    } else if (type === 'postQuestion') {
      setPostQuestionModel(false);
      setSuccessTitle(t('faqs.questionSuccess'));
    } else if (type === 'suggestion') {
      setSuggestProductModal(false);
      setSuccessTitle(t('feedback.feedbackSuccess'));
    }
    InteractionManager.runAfterInteractions(() => {
      setSuccessModel(true);
    });
    setTimeout(() => {
      setSuccessModel(false);
    }, 4000);
  }, []);

  const onPressBuyNow = useCallback(async () => {
    await flushQuantityUpdate();
    if (!isLoggedIn) {
      showInfoMessage(t('toastMassages.loginInfo'));
      navigate('Login', {
        nextScreenName: 'ProductDetail',
        nextScreenParams: { productId: productId },
      });
      return;
    }
    if (product?.type === 'grouped') {
      if (selectedGroupedProductsRef.current.length === 0) {
        return showErrorMessage(t('PDP.productQuantity'));
      }
    } else if (qtyRef.current === 0) {
      return showErrorMessage(t('PDP.selectedQuantity'));
    }
    setIsBuyNowInProgress(true);
    dispatch(setLoading(true));
    let cartObj;
    if (product.type === 'grouped') {
      cartObj = selectedGroupedProductsRef.current;
    } else {
      let obj = {
        quantity: qtyRef.current,
        sku: product?.sku,
      };
      if (referralCode && product?.sku === productSku) {
        obj['referral_code'] = referralCode;
      }
      cartObj = [{ data: obj }];
    }
    const obj = {
      cart_items: cartObj,
      country_code: 'IN',
    };
    try {
      const { data, status } = await buyNow(obj);
      dispatch(setLoading(false));
      if (status === true) {
        clearCartPrice();
        setPostCode('');
        trackEvent('INITIATE_CHECKOUT', {
          // contentId: cartData?.sku,
          // contentType: 'product',
          // currency: 'INR',
          // value: cartData?.selling_price,
          params: { cart_id: data?.cart?.cart_id },
        });

        // appFlyer Checkout Started(INITIATE_CHECKOUT) Event
        appsFlyerEvent('CheckoutStarted', {
          totalPrice: data?.cart?.pricing_details?.grand_total?.amount?.value,
          productIds: productId,
          category: product?.name,
          currency: 'INR',
          totalQuantity: data?.cart?.total_quantity,
        });

        return navigation.navigate('PaymentPage', {
          cart: data?.cart,
          buyNow: true,
          title: t('buttons.buyNow'),
        });
      } else {
        clearCartPrice();
        showErrorMessage(handleErrMsg(data));
      }
    } catch (err) {
      dispatch(setLoading(false));
      showErrorMessage(err);
    } finally {
      setIsBuyNowInProgress(false);
    }
  }, [
    clearCartPrice,
    isLoggedIn,
    navigation,
    product?.sku,
    product?.type,
    productId,
    productSku,
    referralCode,
    selectedGroupProductsForUI,
  ]);

  let sharePressed = useCallback(product => {
    ShareRN.share(
      {
        message: `Check this out ${product.name} ${WEBSITE_URL}${product.url_key}.html`,
      },
      {
        dialogTitle: 'ShareRN on ..',
        tintColor: 'green',
      },
    ).catch(err => debugLog(err));
  }, []);

  const getPromotionOfferBySku = useCallback(async (sku: string) => {
    setFreeBieLoading(true);
    const { data, status } = await getSimpleFreeBie(sku);
    if (status) {
      setFreeBie(data?.data);
    }
    setFreeBieLoading(false);
  }, []);

  const getPromotionOfferByParent = useCallback(async (parentId: string) => {
    const { data, status } = await getGroupedFreeBie(parentId);
    if (status) {
      if (data?.data?.group_messages.length > 0) {
        setFreeBie(data?.data?.group_messages);
      }
    }
  }, []);
  const getLocalAddress = useCallback(async () => {
    const deliveryAddress = await localStorage.get('delivery_address');
    setPostCode(deliveryAddress ? deliveryAddress?.postcode : '');
    setSelectedAddress(
      deliveryAddress ? deliveryAddress : { country_id: 'IN', postcode: '' },
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setSuggestProductModal(false);
  }, []);

  const relatedProductsData = useCallback(async () => {
    const { data, status } = await allRecommendedProducts(productId);
    if (status) {
      setRelatedProductData(data?.related_products || {});
    }
  }, [productId]);

  const getOffer = useCallback(() => {
    dispatch(getOfferProduct());
  }, []);

  useEffect(() => {
    productData();

    // Defer non-critical data loading
    const loadSecondaryData = async () => {
      if (!isMounted.current) return;

      try {
        await Promise.all([
          getFaqs(),
          relatedProductsData(),
          getFrequentlyBoughtData(),
          getOffer(),
        ]);
      } catch (error) { }
    };
    setTimeout(loadSecondaryData, 100);
  }, [
    getFaqs,
    getFrequentlyBoughtData,
    productData,
    relatedProductsData,
    route.params,
    getOffer,
  ]);

  const matchingCategory = useMemo(
    () =>
      product?.categories?.find(i => {
        const getFirstWord = str => str?.trim()?.split(' ')[0]?.toLowerCase();

        const name = getFirstWord(i?.name);
        const brand = getFirstWord(product?.brand?.name);

        return name === brand;
      })?.url_path,
    [product?.categories, product?.brand?.name],
  );

  useEffect(() => {
    if (product?.type === 'simple') {
      setReferCoin(
        product?.attributes?.reward_points || product?.reward_points,
      );
      getPromotionOfferBySku(product?.sku);
    } else if (product?.type === 'grouped') {
      getPromotionOfferByParent(product?.product_id);
    }
    if (product?.sku && product?.seo?.url_key) {
      getProductReferral();
    }
  }, [product]);

  const getFreeBieMessage = useCallback(() => {
    if (product?.type === 'simple') {
      setFreeBieMessage(freeBie?.message);
      setFreeBieModal(true);
    } else if (product?.type === 'grouped') {
      filteredProducts?.map((childProduct, index) => {
        const selectedFreebie = freeBie.find(e => e?.sku === childProduct.sku);
        if (selectedFreebie) {
          setFreeBieMessage(selectedFreebie?.message);
        }
        setFreeBieModal(true);
      });
    }
  }, [product?.type, freeBie, filteredProducts]);

  useEffect(() => {
    getLocalAddress();
  }, [getLocalAddress]);

  useEffect(() => {
    if (selectedAddress?.country_id && selectedAddress?.postcode) {
      const postcode = /^\d+$/.test(selectedAddress?.postcode) ? Number(selectedAddress?.postcode) : 0;
      checkDeliveryStatus();
      if (postcode !== 0) {
        setPinCodeWrong(false);
      } else {
        setPinCodeWrong(true);
      }
    }
  }, [selectedAddress, checkDeliveryStatus]);

  useEffect(() => {
    (async () => {
      if (productId) {
        try {
          const { data } = await getAttributesByProductId(productId);

          if (data && data?.attachments) {
            // do a deep comparison of data and tagData
            if (JSON.stringify(data) !== JSON.stringify(tagData)) {
              setTagData(data);
              setPdfFiles(data?.attachments);
              setSellImage(data?.attachments?.thumbnail);
            }
          }
        } catch (e) {
          showErrorMessage(e?.message);
        }
        try {
          const { data } = await getAllAttributesData(productId);
          if (data?.attributes) {
            setDescription(data?.attributes);
          }
        } catch (e) {
          showErrorMessage(e?.message);
        }
      }
    })();
  }, [product]);

  const referAndEarn = useCallback(() => {
    if (isLoggedIn) {
      setReferralModel(true);
      if (!refersRecord?.onelink_url && !refersLoader) {
        getProductReferral();
      }
    } else {
      showInfoMessage(t('toastMassages.referLogin'));
      navigate('Login', {
        nextRouterState: {
          index: 0,
          routes: 'ProductDetail',
          productId: productId,
        },
      });
    }
  }, [isLoggedIn, getProductReferral, product, productId, refersRecord, refersLoader]);

  const groupedReferAndEarn = useCallback(
    rewardPoints => {
      setReferCoin(rewardPoints);
      referAndEarn();
    },
    [setReferCoin, referAndEarn],
  );

  const postQuestionVerify = useCallback(() => {
    if (isLoggedIn) {
      setPostQuestionModel(true);
    } else {
      navigate('Login', {
        nextRouterState: {
          index: 0,
          routes: 'ProductDetail',
          productId: productId,
        },
      });
      showInfoMessage(t('PDP.login'));
    }
  }, [isLoggedIn]);


  const handleButtonClick = useCallback(
    (buttonName: string) => {
      setSelectedTab(buttonName);
    },
    [setSelectedTab],
  );

  const renderRecommendedTab = useCallback(() => {
    const buttonConfig = [
      {
        key: recommendedTabType.Similar,
        text: t('PDP.similarProduct'),
        visible: showSimilar && (relatedProductData?.crosssell?.length ?? 0) >= 2,
      },
      {
        key: recommendedTabType.Related,
        text: t('PDP.related'),
        visible: (relatedProductData?.related?.length ?? 0) >= 2,
      },
      {
        key: recommendedTabType.More,
        text: t('PDP.moreBrand'),
        visible: (relatedProductData?.more_from_this_manufacturer?.length ?? 0) >= 2,
      },
    ];

    const visibleButtons = buttonConfig.filter(btn => btn.visible);

    if (visibleButtons.length === 0) return null;

    return (
      <View style={styles.recommenedButtonView}>
        {visibleButtons.map((btn, index) => (
          <React.Fragment key={btn.key}>
            {index > 0 && <Spacer type="Horizontal" size="xm" />}
            <ErrorHandler
              componentName={`${TAG} Button`}
              onErrorComponent={<View />}>
              <Button
                text={btn.text}
                type={selectedTab === btn.key ? 'secondary' : 'bordered'}
                radius="sx"
                labelColor={selectedTab === btn.key ? 'background' : 'categoryTitle'}
                labelSize="mx"
                size="extra-small"
                paddingHorizontal="xx"
                weight="500"
                onPress={() => handleButtonClick(btn.key)}
                style={styles.btnStyle}
                labelStyle={styles.btnTxt}
              />
            </ErrorHandler>
          </React.Fragment>
        ))}
      </View>
    );
  }, [selectedTab, handleButtonClick, styles, t, showSimilar, relatedProductData]);


  const scrollBarPosition = scrollX1.interpolate({
    inputRange: [0, Math.max(0, listWidth - screenWidth)],
    outputRange: [0, Math.max(0, screenWidth / 6 - scrollBarWidth)],
    extrapolate: 'clamp',
  });

  const handleRateProduct = useCallback(() => {
    if (isLoggedIn) {
      setRateProductModal(true);
    } else {
      navigate('Login', {
        nextRouterState: {
          index: 0,
          routes: 'ProductDetail',
          productId: productId,
        },
      });
      showInfoMessage(t('toastMassages.rateLogin'));
    }
  }, [isLoggedIn]);

  const handleNotifyClick = useCallback(async (sku, id) => {
    try {
      if (!isLoggedIn) {
        showInfoMessage(t('toastMassages.loginInfo'));
        navigate('Login', {
          nextScreenName: 'ProductDetail',
          nextScreenParams: { productId: productId },
        });
        return;
      }
      const response = await dispatch(notify(id));

      if (response) {
        setNotifyIcons(prevState => ({
          ...prevState,
          [sku]: !prevState[sku],
        }));
      }
    } catch (error) {
      debugError('Error in notify API call:', error);
    }
  }, []);

  const price = useMemo(() => {
    return (
      product?.price?.regularPrice?.amount?.currency_symbol +
      product?.price?.minimalPrice?.amount?.value
    );
  }, [product]);

  const handleProductSearch = useCallback(text => {
    setSearchProductQuery(text);
  }, []);

  const handleTextChange = useCallback(text => {
    setSearchQuery(text);
  }, []);

  const handleTextChangeModal = useCallback(text => {
    setSearchQueryModal(text);
  }, []);

  const filteredQuestions = useMemo(
    () =>
      faqQuestions?.result.filter(item =>
        item.question.toLowerCase().includes(searchQuery.toLowerCase()),
      ) || [],
    [faqQuestions?.result, searchQuery],
  );

  const filteredQuestionsModal = useMemo(
    () =>
      faqQuestionsModal?.result.filter(item =>
        item.question.toLowerCase().includes(searchQueryModal.toLowerCase()),
      ) || [],
    [faqQuestionsModal?.result, searchQueryModal],
  );

  const extractFeatures = useCallback(data => {
    const featuresAttribute = data?.find(
      attr => attr.attribute_code === 'features',
    );

    if (featuresAttribute) {
      return featuresAttribute.attribute_value;
    }
    return '';
  }, []);

  const features = useMemo(
    () => extractFeatures(description),
    [description, extractFeatures],
  );
  const liCount = features.split('</li>').length - 1;
  const productCardMaxWidth = useMemo(() => {
    return checkDevice() ? 0.25 : 0.46;
  }, []);

  const handleProductCardPress = useCallback(
    (item: ProductData) => {
      navigation.push('ProductDetail', {
        productId: item?.product_id,
        ProductItems: {
          media: {
            mobile_image: item?.media?.mobile_image,
          },
        },
      });
    },
    [navigation],
  );

  const renderProductItem = useCallback(
    (
      item: ProductData,
      index: number,
      maxWidth = 0.46,
      size?: 'small' | 'medium' | 'large',
    ) => {
      return (
        <ScrollView
          horizontal
          key={index}
          keyboardShouldPersistTaps="always"
          bounces={false}
          showsVerticalScrollIndicator={false}>
          <ErrorHandler
            componentName={`${TAG} ProductCardVertical`}
            onErrorComponent={<View />}>
            <ProductCardVertical
              onPress={handleProductCardPress.bind(null, item)}
              index={index}
              size={size}
              skuId={item?.sku}
              inStock={item?.is_in_stock}
              actionBtn={item?.action_btn}
              imageWithBorder={true}
              // maxWidth={maxWidth}
              maxWidth={checkDevice() ? 0.25 : maxWidth}
              item={item}
              productType={item?.type}
              maxSaleQty={item?.max_sale_qty}
              demoAvailable={item?.is_demo}
              msrp={item?.msrp}
              image={item?.media?.mobile_image}
              name={item?.name}
              rewardPoint={item?.reward_points}
              description={item?.short_description}
              rating={(item?.rating === 'null' || item?.average_rating === null
                ? 0
                : Number(item?.rating) || Number(item?.average_rating)
              ).toFixed(1)}
              ratingCount={
                item?.rating_count ? `(${item?.rating_count})` : '(0)'
              }
              price={item?.price}
              sellingPrice={item?.selling_price}
              currencySymbol={item?.currency_symbol}
              discount={item?.discount?.label}
              navigation={navigation}
              showWishlist={true}
            />
          </ErrorHandler>
        </ScrollView>
      );
    },
    [handleProductCardPress, productCardMaxWidth, navigation],
  );

  const renderFaqItem = useCallback(
    ({ item, index }) => {
      const isLastItem =
        (filteredQuestions.length === 2 && index === 1) ||
        (filteredQuestions.length === 1 && index === 0);

      return (
        <ErrorHandler
          componentName={`${TAG} FaqItem`}
          onErrorComponent={<View />}>
          <FaqItem
            question={item.question}
            questionViewStyle={
              isLastItem && { borderBottomWidth: 0, paddingVertical: Sizes.m }
            }
            answer={item?.answer?.value}
            createdAt={item?.created_at}
            customerName={item?.customer_name}
            dislikeCount={item?.dislike}
            likeCount={item?.like}
            faqDislikeLoading={faqLoading}
            faqLikeLoading={faqLoading}
            postId={item?._id}
            onUpdate={handleLikeDislike}
          />
        </ErrorHandler>
      );
    },
    [filteredQuestions.length, faqLoading, handleLikeDislike],
  );

  const itemSeparator = useCallback(() => {
    return <Spacer size="xm" />;
  }, []);

  const renderProductItemStable = useCallback(
    ({ item, index }: { [key: string]: any }) => {
      return renderProductItem(item, index, 0.46, 'large');
    },
    [renderProductItem],
  );

  const keyExtractor = useCallback((_, index) => index.toString(), []);
  const mainFlatListData = useMemo(() => {
    return [''];
  }, []);

  const handleHotSellingSectionPress = useCallback(async () => {
    const urlKey = '/offer-zone/hot-selling.html';
    await resolveUrl({ urlKey, navigation });
  }, [navigation]);

  const handleScrolls = useMemo(
    () =>
      Animated.event([{ nativeEvent: { contentOffset: { x: scrollX1 } } }], {
        useNativeDriver: false,
        listener: event => {
          const offsetX = event.nativeEvent.contentOffset.x;
          setScrollX(offsetX);
        },
      }),
    [scrollX1],
  );

  const handleContentSizeChange = contentWidth => {
    setListWidth(contentWidth);
    if (screenWidth > 0 && contentWidth > screenWidth) {
      const calculatedScrollBarWidth =
        (screenWidth / contentWidth) * (screenWidth / 4);
      setScrollBarWidth(Math.max(calculatedScrollBarWidth, 20));
    }
  };

  const handleFlatListContentSizeChange = useCallback(contentWidth => {
    setContentWidth(contentWidth);
    handleContentSizeChange(contentWidth);
  }, []);

  useEffect(() => {
    if (horizontalFlatListRef.current) {
      horizontalFlatListRef.current.scrollToOffset({ offset: 0, animated: false });
    }
  }, [selectedTab]);

  const validTabs = useMemo(() => {
    return [
      relatedProductData?.crosssell && (relatedProductData?.crosssell?.length ?? 0) >= 2 && recommendedTabType.Similar,
      (relatedProductData?.related?.length ?? 0) >= 2 && recommendedTabType.Related,
      (relatedProductData?.more_from_this_manufacturer?.length ?? 0) >= 2 && recommendedTabType.More,
    ].filter(Boolean);
  }, [relatedProductData]);

  useEffect(() => {
    if (validTabs.length > 0) {
      setSelectedTab(validTabs[0]);
    }
  }, [validTabs]);

  const getProductListByTab = useCallback(() => {
    switch (selectedTab) {
      case recommendedTabType.Similar:
        return relatedProductData?.crosssell ?? [];
      case recommendedTabType.Related:
        return relatedProductData?.related ?? [];
      case recommendedTabType.More:
        return relatedProductData?.more_from_this_manufacturer ?? [];
      default:
        return [];
    }
  }, [selectedTab, relatedProductData]);

  const activeProductList = useMemo(() => getProductListByTab(), [getProductListByTab]);

  const renderActiveProductList = useMemo(() => {
    if (!activeProductList.length) return null;

    return (
      <View style={[styles.subProductNameView, { paddingHorizontal: Sizes.s }]}>
        <Spacer size="m" />
        <OptimizedFlatList
          ref={horizontalFlatListRef}
          horizontal
          onScroll={handleScrolls}
          data={activeProductList}
          keyExtractor={item => item?.product_id?.toString()}
          ItemSeparatorComponent={itemSeparator}
          renderItem={renderProductItemStable}
          onContentSizeChange={handleFlatListContentSizeChange}
          removeClippedSubviews={true}
          updateCellsBatchingPeriod={50}
          showsHorizontalScrollIndicator={false}
        />
      </View>
    );
  }, [
    activeProductList,
    itemSeparator,
    renderProductItemStable,
    scrollX1,
    scrollBarWidth,
    listWidth,
    scrollX
  ]);

  const shouldShowScrollBar = useMemo(() => {
    return scrollBarWidth > 0 && activeProductList.length > 2;
  }, [scrollBarWidth, activeProductList]);


  const memoizedSeparator = useCallback(() => {
    return <Spacer type="Horizontal" size="mx" />;
  }, []);

  const mainListStyle = useMemo(() => {
    return [styles.subContainer, { paddingBottom: insets.bottom }];
  }, [styles.subContainer, insets.bottom]);

  const upsellProductsSlice = useMemo(() => {
    return relatedProductData?.upsell?.slice(0, 4);
  }, [relatedProductData?.upsell]);

  const upsellProductNumColumns = useMemo(() => {
    return checkDevice() ? undefined : Sizes.xs;
  }, []);

  const showSimilar = useMemo(() => {
    return relatedProductData?.crosssell?.length >= 2;
  }, [relatedProductData?.crosssell]);

  const renderDeliveryDetails = useMemo(() => {
    return (
      <DeliveryDetails
        product={product as ProductData}
        postCode={postCode}
        setPostCode={setPostCode}
        deliveryStatusData={deliveryStatusData}
        setDeliveryStatusData={setDeliveryStatusData}
        checkPincodeLoading={checkPincodeLoading}
        setSelectedAddress={setSelectedAddress}
        setReturnInfoModel={setReturnInfoModel}
        setInfoIcon={setInfoIcon}
        pinCodeWrong={pinCodeWrong}
        setPinCodeWrong={setPinCodeWrong}
      />
    );
  }, [
    product,
    postCode,
    deliveryStatusData,
    checkPincodeLoading,
    selectedAddress,
    returnInfoModel,
    infoIcon,
    pinCodeWrong,
    setPinCodeWrong
  ]);

  const renderRatingsReviews = useMemo(() => {
    return (
      <RatingsReviewsComponent
        review={review}
        productId={productId}
        navigation={navigation}
        handleRateProduct={handleRateProduct}
        ratingLayout={ratingLayout}
      />
    );
  }, [review, productId, navigation, rateProductModal, ratingLayout]);

  const renderFAQsComponent = useMemo(
    () => (
      <FAQsComponent
        faqQuestions={faqQuestions}
        review={review}
        filteredQuestions={filteredQuestions}
        searchQuery={searchQuery}
        getFaqLoading={getFaqLoading}
        handleTextChange={handleTextChange}
        setSearchQuery={setSearchQuery}
        setShowFaqListModal={setShowFaqListModal}
        renderFaqItem={renderFaqItem}
      />
    ),
    [
      faqQuestions,
      review,
      filteredQuestions,
      searchQuery,
      getFaqLoading,
      showFaqListModal,
      renderFaqItem,
    ],
  );

  const renderProductFooter = useMemo(
    () => (
      <ProductFooter
        memoizedSeparator={memoizedSeparator}
        notifyIcon={notifyIcon}
        isAddToCartInProgress={isAddToCartInProgress}
        isInStock={isInStock}
        product={product}
        isLoggedIn={isLoggedIn}
        notifyLoading={notifyLoading}
        addToCartPress={addToCartPress}
        setReqPriceModal={setReqPriceModal}
        setBulkModal={setBulkModal}
        showInfoMessage={showInfoMessage}
        navigation={navigation}
        notifyClick={notifyClick}
        showSimilar={showSimilar}
        setSimilarProductModal={setSimilarProductModal}
        isBuyNowInProgress={isBuyNowInProgress}
        onPressBuyNow={onPressBuyNow}
      />
    ),
    [
      memoizedSeparator,
      notifyIcon,
      isAddToCartInProgress,
      isInStock,
      product,
      isLoggedIn,
      notifyLoading,
      addToCartPress,
      reqPriceModal,
      bulkModal,
      showInfoMessage,
      navigation,
      showSimilar,
      similarProductModal,
      isBuyNowInProgress,
      onPressBuyNow,
    ],
  );

  const handleSingleProductQuantityUpdate = useCallback(
    (count: number) => {
      if (count <= 0) {
        setIsAddToCartPressed(false);
        updateSelectedSingleProducts(
          count,
          product?.pricing?.selling_price,
          product?.sku,
        );
      } else {
        updateSelectedSingleProducts(
          count,
          product?.pricing?.selling_price,
          product?.sku,
        );
      }
    },
    [
      product?.pricing?.selling_price,
      product?.sku,
      updateSelectedSingleProducts,
    ],
  );

  const debouncedHandleSingleProductQuantityUpdate = useDebouncedCallback(
    handleSingleProductQuantityUpdate,
    500,
  );

  const handleGroupProductQuantityUpdate = useCallback(
    (count: number, productPrice: number, sku: string) => {
      updateSelectedGroupProducts(count, productPrice, sku);
    },
    [updateSelectedGroupProducts],
  );

  const pendingGroupQuantityUpdatesRef = useRef<
    Record<
      string,
      {
        timeout: NodeJS.Timeout;
        params: { count: number; price: number; sku: string };
      }
    >
  >({});

  const handleGroupQuantityUpdateDebounce = useCallback(
    (count: number, price: number, sku: string, change: boolean) => {
      if (change) {
        if (pendingGroupQuantityUpdatesRef.current[sku]) {
          clearTimeout(pendingGroupQuantityUpdatesRef.current[sku].timeout);
        }
        pendingGroupQuantityUpdatesRef.current[sku] = {
          timeout: setTimeout(() => {
            handleGroupProductQuantityUpdate(count, price, sku);
            delete pendingGroupQuantityUpdatesRef.current[sku];
          }, 500),
          params: { count, price, sku },
        };
      } else {
        handleGroupProductQuantityUpdate(count, price, sku);
      }
    },
    [handleGroupProductQuantityUpdate],
  );
  const sanitizeHtmlContent = useCallback((rawHtml: string) => {
    const tableMatch = rawHtml.match(/<table[^>]*>[\s\S]*?<\/table>/i);
    let tableHtml = '';
    let remainingHtml = rawHtml;

    if (tableMatch) {
      tableHtml = tableMatch[0];
      remainingHtml = rawHtml.replace(tableHtml, '');
    }

    remainingHtml = remainingHtml.replace(/<li>\s*<\/li>/gi, '');
    remainingHtml = remainingHtml
      .replace(/\r\n/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    return `
      ${tableHtml}
      ${remainingHtml}
    `;
  }, []);
  const renderSearchProductComponent = useMemo(
    () => (
      <SearchProductComponent
        searchProductQuery={searchProductQuery}
        handleProductSearch={handleProductSearch}
        setSearchProductQuery={setSearchProductQuery}
        filteredProducts={filteredProducts}
        product={product}
        updateSelectedGroupProducts={updateSelectedGroupProducts}
        handleGroupQuantityUpdateDebounce={handleGroupQuantityUpdateDebounce}
        selectedGroupProductsForUI={selectedGroupProductsForUI}
        notifyIcons={notifyIcons}
        handleNotifyClick={handleNotifyClick}
        quantityInputRef={quantityInputRef}
        setReturnInfoModel={setReturnInfoModel}
        setInfoIcon={setInfoIcon}
        deliveryStatusData={deliveryStatusData}
        postCode={postCode}
        availablePaymentMethod={availablePaymentMethod}
        freeBie={freeBie}
        getFreeBieMessage={getFreeBieMessage}
        pinCodeWrong={pinCodeWrong}
      />
    ),
    [
      searchProductQuery,
      filteredProducts,
      product,
      updateSelectedGroupProducts,
      handleGroupQuantityUpdateDebounce,
      selectedGroupProductsForUI,
      notifyIcons,
      handleNotifyClick,
      quantityInputRef,
      returnInfoModel,
      infoIcon,
      deliveryStatusData,
      postCode,
      availablePaymentMethod,
      freeBie,
      getFreeBieMessage,
      pinCodeWrong,
    ],
  );
  const flushGroupQuantityUpdatesDebounce = useCallback(
    (execute = true) => {
      Object.entries(pendingGroupQuantityUpdatesRef.current).forEach(
        ([sku, data]) => {
          clearTimeout(data.timeout);

          if (execute) {
            // Execute immediately if requested
            const { count, price, sku: skuValue } = data.params;
            handleGroupProductQuantityUpdate(count, price, skuValue);
          }

          delete pendingGroupQuantityUpdatesRef.current[sku];
        },
      );
    },
    [handleGroupProductQuantityUpdate],
  );

  const flushQuantityUpdate = useCallback(async () => {
    if (Keyboard.isVisible()) {
      quantityInputRef.current?.blur();
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    debouncedHandleSingleProductQuantityUpdate.flush();
    flushGroupQuantityUpdatesDebounce();
  }, [
    debouncedHandleSingleProductQuantityUpdate,
    flushGroupQuantityUpdatesDebounce,
  ]);

  useEffect(() => {
    return () => {
      flushGroupQuantityUpdatesDebounce(false);
    };
  }, [flushGroupQuantityUpdatesDebounce]);

  const orderedProductImages = useMemo(() => {
    if (!productImages || productImages.length < 2) {
      return productImages;
    }

    const reorderedImages = [...productImages];
    const videoIndex = reorderedImages.findIndex(
      item => item?.media_type === 'external-video',
    );
    if (videoIndex !== -1 && videoIndex !== 1) {
      const videoItem = reorderedImages.splice(videoIndex, 1)[0];
      reorderedImages.splice(1, 0, videoItem);
    }
    return reorderedImages;
  }, [productImages]);

  const mainFlatListRenderItem = useCallback(
    ({ item, index }) => (
      <View style={mainListStyle}>
        <ErrorHandler
          componentName={`${TAG} ProductImageSlider`}
          onErrorComponent={<View />}>
          <ProductImageSlider
            scrollToSpecificOffset={scrollToSpecificOffset}
            rating={product?.rating || product?.average_rating}
            openSimilar={openSimilarProductModal}
            reviewsCount={
              product?.reviews_count > 0 ? product?.reviews_count : 0
            }
            share={true}
            productImages={orderedProductImages}
            showSimilar={showSimilar}
            productData={product}
            navigation={navigation}
            tagData={tagData}
            openShare={openShareModel}
          />
        </ErrorHandler>
        {/* <Spacer size="m" /> */}
        {/* ========= === singl product card ==== =============== */}
        {product?.type === 'simple' ? (
          <>
            <View style={styles.subProductNameView}>
              <View style={styles.referAndEarnView}>
                {product?.action_btn?.text === 'Request Price' ? null : (
                  <TouchableOpacity
                    style={styles.brandName}
                    onPress={async () => {
                      if (matchingCategory) {
                        // navigation.navigate('UrlResolver', {
                        //   urlKey: matchingCategory + '.html',
                        // })
                        const urlKey = matchingCategory + '.html';
                        await resolveUrl({ urlKey, navigation });
                      }
                    }}>
                    <Label
                      weight="600"
                      size="mx"
                      textDecorationLine="underline"
                      color="blueOffer"
                      text={product?.brand?.name}
                    />
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={referAndEarn}
                  style={styles.earnView}>
                  <Label
                    weight="500"
                    color="silkBlue"
                    size="mx"
                    text={t('PDP.referEarn')}
                  />
                  <Spacer type="Horizontal" size="xms" />
                  <ImageIcon
                    size="xxl"
                    icon="announcement"
                    tintColor="silkBlue"
                  />
                </TouchableOpacity>
              </View>
              <Spacer size="xm" />
              <View style={styles.childProductView}>
                <Label
                  weight="600"
                  color="text"
                  size="mx"
                  text={product?.name}
                />

                {!product?.pricing?.is_price_request && !productLoader ? (
                  <>
                    <View style={styles.priceView}>
                      <View style={styles.basePriceView}>
                        <Label
                          weight="600"
                          color="text"
                          size="xl"
                          text={`₹ ${product?.pricing?.selling_price ||
                            product?.selling_price
                            }`}
                        />
                        {product?.pricing?.price !==
                          product?.pricing?.selling_price && (
                            <Label
                              fontFamily="Medium"
                              size="l"
                              color="grey3"
                              textDecorationLine="line-through"
                              text={`₹ ${product?.pricing?.price}`}
                            />
                          )}
                      </View>
                      <Spacer size="xm" type="Horizontal" />
                      {product?.pricing?.selling_price ||
                        product?.selling_price !== product?.pricing?.price ||
                        product?.price ? (
                        <>
                          <Spacer size="xm" type="Horizontal" />
                          <Label
                            weight="500"
                            color="green2"
                            textTransform="capitalize"
                            size="mx"
                            text={
                              product?.pricing?.discount?.label ||
                              product?.discount?.label
                            }
                          />

                          {!!product?.pricing?.custom_fee && (
                            <Label
                              weight="500"
                              color="text"
                              size="mx"
                              text={`+ ₹${Math.ceil(
                                product?.pricing?.custom_fee,
                              )} ${t('PDP.deliveryfee')} `}
                            />
                          )}
                        </>
                      ) : null}
                    </View>
                    <View style={styles.priceCoinViews}>
                      <ImageIcon icon="coin" size="xx" />
                      <Spacer size="s" type="Horizontal" />
                      <Label
                        weight="500"
                        color="orange"
                        style={styles.alignCoin}
                        size="l"
                        text={
                          product?.attributes?.reward_points ||
                          product?.reward_points
                        }
                      />
                    </View>
                  </>
                ) : null}

                <Spacer size="sx" />
                <Label
                  weight="500"
                  textTransform="capitalize"
                  color="text2"
                  size="m"
                  text={
                    product?.attributes?.short_description ||
                    product?.short_description
                  }
                />
                <Spacer size="sx" type="Horizontal" />

                <View style={styles.centeredRow}>
                  {!!product?.attributes?.expiry_date?.label && (
                    <View style={styles.leftContainer}>
                      <Label
                        weight="500"
                        color="text"
                        size="m"
                        text={t('rewardCoin.expiryDate')}
                      />
                      <Label
                        weight="500"
                        color="categoryTitle"
                        size="m"
                        text={product?.attributes?.expiry_date?.label}
                      />
                      <Spacer size="s" type="Horizontal" />
                      <TouchableOpacity
                        onPress={() => {
                          setReturnInfoModel(true);
                          setInfoIcon('expiryDate');
                        }}>
                        <ImageIcon
                          tintColor="categoryTitle"
                          size="xl"
                          icon="infoCircle"
                        />
                      </TouchableOpacity>
                    </View>
                  )}
                  <Spacer size="xms" type="Horizontal" />
                  <View style={styles.rightContainer}>
                    {product?.is_in_stock || product?.inventory?.is_in_stock ? (
                      <>
                        {!freeBieLoading && !!freeBie?.message && (
                          <ErrorHandler
                            componentName={`${TAG} Tag`}
                            onErrorComponent={<View />}>
                            <TouchableOpacity
                              style={styles.freeBieButton}
                              activeOpacity={1}
                              onPress={getFreeBieMessage}>
                              <FastImage
                                // resizeMode="contain"
                                style={styles.emptyImage}
                                source={Icons.freebieGif}
                              />
                            </TouchableOpacity>
                          </ErrorHandler>
                        )}

                        <AddToCartSection
                          isAddToCartPressed={isAddToCartPressed}
                          product={product}
                          productLoader={productLoader}
                          updateSelectedSingleProducts={
                            updateSelectedSingleProducts
                          }
                          setIsAddToCartPressed={setIsAddToCartPressed}
                          t={t}
                          quantityInputRef={quantityInputRef}
                          qtyForUI={qtyForUI}
                          onUpdate={(count, change) => {
                            if (change) {
                              debouncedHandleSingleProductQuantityUpdate(count);
                            } else {
                              handleSingleProductQuantityUpdate(count);
                            }
                          }}
                          TAG={TAG}
                        />
                      </>
                    ) : (
                      <ErrorHandler
                        componentName={`${TAG} Button`}
                        onErrorComponent={<View />}>
                        <Button
                          disabled
                          type="disabled"
                          onPress={() => null}
                          text={t('PDP.outStock')}
                          radius="sx"
                          size="extra-small"
                          selfAlign="flex-end"
                          borderColor="grey2"
                          labelColor="text"
                          labelSize="mx"
                          style={styles.outStock}
                          labelStyle={styles.labelBottom}
                          paddingHorizontal="m"
                        />
                      </ErrorHandler>
                    )}
                  </View>
                </View>
              </View>
            </View>
            <ErrorHandler
              componentName={`${TAG} DownloadCatalogue`}
              onErrorComponent={<View />}>
              <DownloadCatalogue pdfFiles={pdfFiles} product={product} />
            </ErrorHandler>
            <Spacer size="xm" />
          </>
        ) : null}
        {/* ========= === group product card ==== =============== */}
        {product?.type === 'grouped' ? (
          <>
            <View style={styles.subProductNameView}>
              <View style={styles.referAndEarnView}>
                {product?.action_btn?.text == 'Request Price' ? null : (
                  <TouchableOpacity
                    style={styles.brandName}
                    onPress={async () => {
                      if (matchingCategory) {
                        // navigation.navigate('UrlResolver', {
                        //   urlKey: matchingCategory + '.html',
                        // })
                        const urlKey = matchingCategory + '.html';
                        await resolveUrl({ urlKey, navigation });
                      }
                    }}>
                    <Label
                      weight="600"
                      size="mx"
                      textDecorationLine="underline"
                      color="blueOffer"
                      text={product?.brand?.name}
                    />
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={() => referAndEarn()}
                  style={styles.earnView}>
                  <Label
                    weight="500"
                    color="silkBlue"
                    size="mx"
                    text={t('PDP.referEarn')}
                  />
                  <Spacer type="Horizontal" size="xms" />
                  <ImageIcon
                    size="xxl"
                    icon="announcement"
                    tintColor="silkBlue"
                  />
                </TouchableOpacity>
              </View>
              <Spacer size="xm" />
              <View style={styles.productNameView}>
                <Label
                  weight="600"
                  color="text"
                  size="mx"
                  text={product?.name}
                />
                <Spacer size="l" />
                <View style={styles.transformedTierView}>
                  <Label
                    style={styles.flex}
                    weight="500"
                    color="text2"
                    size="m"
                    text={
                      product?.attributes?.short_description ||
                      product?.short_description
                    }
                  />
                  <Spacer type="Horizontal" size="m" />
                </View>
                <View style={[styles.flexRow, styles.flexEnd]}>
                  <Label
                    weight="500"
                    color="text"
                    textTransform="capitalize"
                    size="l"
                    text={t('productItems.starting')}
                  />

                  <Label
                    weight="600"
                    color="text"
                    size="xl"
                    text={` ₹${product?.pricing?.selling_price ||
                      product?.selling_price ||
                      0
                      }`}
                  />
                </View>
              </View>
            </View>
            {Platform.OS === 'ios' && <Spacer size="xm" />}
            <ErrorHandler
              componentName={`${TAG} DownloadCatalogue`}
              onErrorComponent={<View />}>
              <DownloadCatalogue pdfFiles={pdfFiles} product={product} />
            </ErrorHandler>
            <Spacer size="xm" />
          </>
        ) : null}
        {product?.inventory?.is_in_stock &&
          product?.action_btn?.text !== 'Request Price' &&
          (product?.type === 'grouped'
            ? filteredProducts.filter(data => data?.is_in_stock)?.length > 0
            : true) &&
          renderDeliveryDetails}
        {/* ==========================address end========================= */}

        {product?.type === 'grouped' ? (
          <ErrorHandler
            componentName={`${TAG} TierPricingDetails`}
            onErrorComponent={<View />}>
            <TierPricingDetails
              transformedTierPrices={transformedTierPrices}
              totalTierAmount={totalTierAmount}
              totalOriginalAmount={totalOriginalAmount}
              totalItemCount={totalItemCount}
              tierPriceIndex={tierPriceIndex}
              setTierPriceIndex={setTierPriceIndex}
              setReturnInfoModel={setReturnInfoModel}
              setInfoIcon={setInfoIcon}
              findFirstActiveIndex={findFirstActiveIndex}
            />
          </ErrorHandler>
        ) : null}

        {/* ==========group products===================  */}
        {product?.type === 'grouped' && groupProduct?.length > 0 ? (
          <>
            <Spacer size="xms" />
            {renderSearchProductComponent}
          </>
        ) : (
          <View />
        )}
        {/* ==========================end============================ */}
        <Spacer size="l" />
        <View style={styles.subProductNameView}>
          {liCount > 0 && (
            <View style={styles.highlightView}>
              <View style={styles.highlightSubView}>
                <Label
                  text={t('PDP.productHighlights')}
                  size="l"
                  weight="600"
                  color="text"
                />
                <Spacer size="xm" />
              </View>
              <DashedLine
                dashLength={2}
                dashThickness={1}
                dashColor={colors.grey5}
              />
              <View style={styles.featureView}>
                <Label
                  text={t('PDP.features')}
                  size="l"
                  weight="500"
                  color="text2"
                />
                <Spacer size="s" />
                <ScrollView
                  style={{ maxHeight: moreHtmlView ? 345 : 50 }}
                  showsVerticalScrollIndicator={true}
                  nestedScrollEnabled={true}>
                  <ErrorHandler
                    componentName={`${TAG} RenderCustomHTML`}
                    onErrorComponent={<View />}>
                    <RenderCustomHTML
                      html={features}
                      tagsStyles={styles.tagStyle}
                      renderersProps={renderersProps}
                      contentWidth={300}
                    />
                  </ErrorHandler>
                </ScrollView>
              </View>
              <View style={styles.productDescriptionView}>
                <TouchableOpacity
                  style={styles.productDesView}
                  onPress={() => [
                    setMoreHtmlView(!moreHtmlView),
                    setOpenIndex(null),
                  ]}>
                  <Label
                    text={moreHtmlView ? t('PDP.viewLess') : t('PDP.viewMore')}
                    size="l"
                    weight="500"
                    color="newSunnyOrange"
                    style={styles.detailsView}
                  />
                  <WithGradient
                    gradientColors={[colors.mandyPink, colors.background]}
                    gradientAngle={270}
                    gradientStyle={styles.arrowBottomView}>
                    <ImageIcon
                      icon="arrowRight"
                      size="xxl"
                      tintColor="text"
                      style={[
                        styles.arrowStyle,
                        moreHtmlView && styles.highlistGradient,
                      ]}
                    />
                  </WithGradient>
                </TouchableOpacity>
              </View>
            </View>
          )}
          <Spacer size="l" />
          <View>
            {Array.isArray(description) &&
              description
                .filter(item => item.attribute_label !== 'Features') // "Features" को हटाएं
                .map((item, index) => {
                  const isItemOpen = openIndex === index;
                  return (
                    <View key={index}>
                      <TouchableOpacity
                        onPress={() => handleItemToggle(index)}
                        style={styles.descriptionKeyView}>
                        <View style={styles.descriptionKey}>
                          <Label
                            text={item.attribute_label}
                            weight="500"
                            color="text"
                            size="l"
                          />
                          <Spacer size="m" type="Horizontal" />
                          <ImageIcon
                            icon={isItemOpen ? 'arrowUp' : 'arrowRight'}
                            size="xxl"
                            tintColor="text"
                            style={styles.arrowStyle}
                          />
                        </View>
                      </TouchableOpacity>

                      {isItemOpen && (
                        <ScrollView
                          style={styles.renderHtml}
                          showsVerticalScrollIndicator={true}
                          nestedScrollEnabled={true}>
                          <RenderHTML
                            source={{
                              html: sanitizeHtmlContent(item.attribute_value),
                            }}
                            tagsStyles={styles.tagStyle}
                            contentWidth={width}
                            allowFontScaling={false}
                          />
                        </ScrollView>
                      )}
                    </View>
                  );
                })}
          </View>

          <Spacer size="m" />
          <View style={styles.dentalKartView}>
            <Label
              text={t('PDP.dentalKartBenefits')}
              size="l"
              weight="600"
              color="text"
            />
            <ErrorHandler
              componentName={`${TAG} Button`}
              onErrorComponent={<View />}>
              <Button
                onPress={() => {
                  setContentType('knowMore');
                  setIsShowFooterModal(!isShowFooterModal);
                }}
                text={t('PDP.knowMore')}
                labelSize="mx"
                weight="500"
                labelColor="newSunnyOrange"
                iconRight="doubleRightArrow"
                tintColor="sunnyOrange3"
                iconSize="xl"
              />
            </ErrorHandler>
          </View>
          <Spacer size="xm" />
          <View style={styles.contentTypeView}>
            {benefits.map((item: BenefitsInfo, index: number) => (
              <>
                <TouchableOpacity
                  onPress={() => {
                    setContentType('benefits');
                    setModalText(item);
                    setIsShowFooterModal(!isShowFooterModal);
                  }}
                  style={styles.contentTypeSubView}
                  key={index.toString()}>
                  <IconWithBackground
                    icon={item?.icon}
                    backgroundColor={item?.backgroundColor}
                    borderRadius="xms"
                    tintColor="text"
                    size="xxl"
                  />
                  <Spacer type="Vertical" size="xm" />
                  <Label
                    align="center"
                    text={item?.label}
                    size="m"
                    weight="500"
                    color="text2"
                    numberOfLines={3}
                    textTransform={'capitalize'}
                  />
                </TouchableOpacity>
                <Spacer type="Horizontal" size="sx" />
              </>
            ))}
          </View>
          <Spacer size="xxl" />
          <View style={styles.paymentOptionsView}>
            <Label
              style={styles.paymentLabel}
              text={t('PDP.paymentOptions')}
              size="l"
              fontFamily="SemiBold"
              color="text"
              weight="600"
            />
            <Spacer size="xms" />
            <View style={styles.paymentView}>
              {paymentOptions.map((paymentOption, index) => {
                return (
                  <View style={styles.paymentOptionsSubView}>
                    <TouchableOpacity
                      style={styles.paymentBox}
                      onPress={() => {
                        const paymentOptions = ['Cards', 'Pay Later', ' EMI'];
                        if (paymentOptions.includes(paymentOption.label)) {
                          setIsShowEmiModal({
                            show: true,
                            label: paymentOption.label,
                          });
                        } else {
                          setContentType('paymentOptions');
                          setAccordionActiveIndex(-1);
                          setModalText(paymentOption);
                          setIsShowFooterModal(!isShowFooterModal);
                        }
                      }}
                      key={index.toString()}>
                      <ImageIcon
                        icon={paymentOption.icon}
                        size="xl"
                        tintColor={colors.text2}
                      />
                      <Spacer type="Horizontal" size="s" />
                      <Label
                        text={paymentOption.label}
                        size="mx"
                        weight="400"
                        color="text2"
                        align="center"
                        ellipsizeMode="tail"
                        allowFontScaling={false}
                      />
                      <Spacer type="Horizontal" size="s" />
                      <ImageIcon
                        icon="infoCircle"
                        size="xl"
                        tintColor="text2"
                      />
                    </TouchableOpacity>
                  </View>
                );
              })}
            </View>
          </View>
        </View>
        <Spacer size="m" />
        {/* Feedback section */}
        <View style={styles.feedbackView}>
          <Label
            text={t('PDP.aboutProduct')}
            size="mx"
            weight="400"
            color="text"
            style={styles.aboutProductWidth}
          />
          <ErrorHandler
            componentName={`${TAG} Button`}
            onErrorComponent={<View />}>
            <Button
              onPress={() => {
                setIsShowFeedbackModal(true);
              }}
              text={t('PDP.feedback')}
              labelSize="mx"
              weight="500"
              labelColor="newSunnyOrange"
              iconRight="doubleRightArrow"
              tintColor="sunnyOrange3"
              iconSize="xl"
            />
          </ErrorHandler>
        </View>
        <Spacer size="l" />
        <View style={styles.feedbackSubView}>
          <Label
            text={t('PDP.moreQuantity')}
            size="mx"
            weight="500"
            color="text"
          />
          <Spacer size="xm" />
          <ErrorHandler
            componentName={`${TAG} Button`}
            onErrorComponent={<View />}>
            <Button
              borderColor="categoryTitle"
              type="bordered"
              onPress={() => [setBulkModal(true), setReqPriceModal(false)]}
              text={t('PDP.bulkNow')}
              labelSize="mx"
              radius="sx"
              paddingHorizontal="xm"
              weight="600"
              labelColor="categoryTitle"
              size="extra-small"
            />
          </ErrorHandler>
        </View>
        <Spacer size="m" />

        {/* {frequentlyBoughtProducts?.length > 0 &&
                  frequentlyBoughtProducts.every(i => i?.is_in_stock) && (
                    <FrequentlyBoughtTogether
                      frequentlyBoughtProducts={frequentlyBoughtProducts}
                      totalPrice={totalPrice}
                      selectedItems={selectedItems}
                      // renderProductItemFqbt={renderProductItemFqbt}
                      addToCartButton={addToCartButton}
                    />
                  )}
                <Spacer size="l" /> */}
        {/* Hot Seller */}
        {/* {relatedProductData?.upsell?.length > 0 && (
          <>
            <ImageBackground
              source={Icons.hotSellingBanner}
              style={styles.backgroundImage}>
              <TouchableOpacity
                onPress={handleHotSellingSectionPress}
                style={styles.sellerView}>
                <Label
                  text={t('PDP.hotSeller')}
                  size="l"
                  weight="600"
                  color="whiteColor"
                />
                <ImageIcon
                  icon="hotSellerRightArrow"
                  size="xxl"
                  tintColor="whiteColor"
                />
              </TouchableOpacity>
              <View style={styles.listDataView}>
                <OptimizedFlatList
                  horizontal={checkDevice()}
                  numColumns={upsellProductNumColumns}
                  data={upsellProductsSlice}
                  keyExtractor={item => item?.product_id?.toString()}
                  renderItem={renderProductItemStable}
                  ItemSeparatorComponent={itemSeparator}
                  onEndReachedThreshold={0.8}
                  removeClippedSubviews={true}
                  windowSize={5}
                  maxToRenderPerBatch={5}
                  updateCellsBatchingPeriod={50}
                  getItemLayout={(_, index) => ({
                    length: itemHeight,
                    offset: itemHeight * index,
                    index,
                  })}
                />
              </View>
            </ImageBackground>
            <Spacer size="m" />
          </>
        )} */}
        {/* FAQs */}
        {faqQuestions?.count > 0 && renderFAQsComponent}
        {/* Post Your Question */}
        <Spacer size="l" />
        <ProductDoubtsSection postQuestionVerify={postQuestionVerify} />
        <Spacer size="xl" />
        {validTabs.length > 0 && (
          <>
            <Spacer size="xl" />
            <ImageBackground
              source={Icons.recommendedBanner}
              style={styles.backgroundImageContainer}
            >
              <View style={styles.recommendedContainer}>
                <Label
                  text={t('PDP.recommended4U')}
                  size="l"
                  weight="600"
                  color="newPrimary"
                  fontStyle={Platform.OS === 'ios' ? 'italic' : 'normal'}
                  fontFamily="SemiBoldItalic"
                />
              </View>

              <FlatList
                data={['']}
                horizontal
                showsHorizontalScrollIndicator={false}
                renderItem={renderRecommendedTab}
                removeClippedSubviews={true}
                windowSize={5}
                maxToRenderPerBatch={5}
                updateCellsBatchingPeriod={50}
              />

              {renderActiveProductList}

              {shouldShowScrollBar && (
                <ErrorHandler
                  componentName={`${TAG} HorizontalScrollBar`}
                  onErrorComponent={<View />}
                >
                  <HorizontalScrollBar
                    activeColor={colors.smoothPink}
                    scrollBarWidth={scrollBarWidth}
                    scrollBarPosition={scrollBarPosition}
                  />
                </ErrorHandler>
              )}
            </ImageBackground>
          </>
        )}
        <Spacer size="xm" />
        <Spacer size="xm" />
        {/* Ratings & Reviews */}
        {renderRatingsReviews}
        <Spacer size="xl" />
        <View style={styles.suggestedView}>
          <Label
            text={t('homePage.lookingFor')}
            size="xxl"
            weight="600"
            color="grey"
            align="center"
          />
          <Spacer size="xms" />
          <View style={styles.rateSubView}>
            <Label
              size="m"
              text={t('homePage.knowUs')}
              weight="500"
              color="grey"
              align="center"
            />
            <View>
              <FastImage
                resizeMode="contain"
                style={styles.editIconGif}
                source={Icons.editIconGif}
              />
            </View>
            <Label
              text={t('homePage.detailBelow')}
              size="m"
              weight="500"
              color="grey"
              align="center"
            />
          </View>

          {/* <Spacer size="s" />
                  <Label
                    text={t('homePage.suggestProduct')}
                    size="m"
                    weight="500"
                    color="grey2"
                    align="center"
                  /> */}
        </View>
        <Spacer size="mx" />
        <ErrorHandler
          componentName={`${TAG} Button`}
          onErrorComponent={<View />}>
          <Button
            onPress={suggestProductPressed}
            radius="xms"
            size="large"
            paddingHorizontal="xxl"
            selfAlign="center"
            text={t('buttons.suggestProduct')}
            type="bordered"
          />
        </ErrorHandler>
        <Spacer size="xl" />
      </View>
    ),
    [
      availablePaymentMethod,
      checkPincodeLoading,
      deliveryStatusData?.delivery_info,
      deliveryStatusData?.service_availability,
      description,
      faqLoading,
      faqQuestions?.count,
      features,
      filteredProducts,
      filteredQuestions,
      findFirstActiveIndex,
      freeBie,
      freeBieLoading,
      getFaqLoading,
      getFreeBieMessage,
      groupProduct?.length,
      isAddToCartPressed,
      isLoggedIn,
      isShowFooterModal,
      itemSeparator,
      keyExtractor,
      mainListStyle,
      matchingCategory,
      moreHtmlView,
      navigation,
      notifyIcons,
      openIndex,
      openShareModel,
      openSimilarProductModal,
      pdfFiles,
      postCode,
      postQuestionVerify,
      product,
      productId,
      productImages,
      productLoader,
      ratingLayout,
      referAndEarn,
      relatedProductData?.related?.length,
      relatedProductData?.upsell?.length,
      renderProductItemStable,
      // renderRelatedProducts,
      // renderSimilarProducts,
      renderActiveProductList,
      renderersProps,
      review?.review_meta?.average_rating,
      review?.review_meta?.total_reviews,
      review?.review_meta?.verified_buyers,
      review?.reviews,
      scrollToSpecificOffset,
      searchProductQuery,
      searchQuery,
      selectedGroupProductsForUI,
      showSimilar,
      suggestProductPressed,
      tagData,
      tierPriceIndex,
      totalItemCount,
      totalOriginalAmount,
      totalTierAmount,
      transformedTierPrices,
      updateSelectedGroupProducts,
      updateSelectedSingleProducts,
      upsellProductNumColumns,
      upsellProductsSlice,
      selectedTab,
      scrollX1,
      scrollBarWidth,
      listWidth,
      scrollX
    ],
  );

  return (
    <KeyboardAvoidingView
      style={styles.flex}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      <View style={styles.flex}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <Header
            onShare={() => sharePressed(product)}
            customIcon={true}
            backButton={true}
            navigation={navigation}
            bagIcon={true}
            searchIcon
            cartCount={true}
            heartIcon
            useInsets
            onWishListNavigate={() => addToWishListItem(product)}
            text={truncateText(product?.name, 18) || ''}
            bottomShadow
          />
        </ErrorHandler>
        {productLoader ? (
          <ProductDetailLoader />
        ) : (
          <>
            {/* <TouchableOpacity style={styles.topIconView} onPress={scrollToTop}>
              <View style={styles.topIconSunView}>
                <ImageIcon icon="arrowTop" tintColor="grey2" size="xsl" />
              </View>
            </TouchableOpacity> */}
            <View style={styles.listContainer2}>
              {showScrollToTopButton && (
                <TouchableOpacity
                  style={styles.topIconView}
                  activeOpacity={0.7}
                  onPress={() => {
                    scrollToTop();
                  }}>
                  <ImageIcon icon="arrowTop" tintColor="background" size="mx" />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    text={t('otherText.gotoTop')}
                    size="m"
                    weight="500"
                    color="whiteColor"
                    style={styles.goTop}
                  />
                </TouchableOpacity>
              )}
              <View style={styles.flex}>
                {getOfferItems?.length > 0 && (
                  <>
                    <TouchableOpacity
                      onPress={() => {
                        setOfferModal(!offerModal);
                        handleItemToggleOffer();
                      }}
                      style={styles.offerSubView}>
                      <FastImage source={Icons.dis} style={styles.offerImage} />
                      <Spacer size="sx" type="Horizontal" />
                      <Label
                        text={t('PDP.offers')}
                        weight="500"
                        size="l"
                        color="text"
                      />
                      <Spacer size="sx" type="Horizontal" />
                      <View style={styles.offerIcon}>
                        <ImageIcon
                          icon={!offerModal ? 'arrowBottom' : 'arrowUp'}
                          size="xxl"
                          tintColor="text"
                        />
                      </View>
                    </TouchableOpacity>
                    {offerModal && (
                      <ErrorHandler
                        componentName={`${TAG} CartOfferCarousel`}
                        onErrorComponent={<View />}>
                        <CartOfferCarousel data={getOfferItems} />
                      </ErrorHandler>
                    )}
                  </>
                )}

                <FlatList
                  ref={ref => {
                    flatListRefTop.current = ref;
                    flatListRef1.current = ref;
                  }}
                  // ref={flatListRefTop}
                  onScroll={handleScroll}
                  showsVerticalScrollIndicator={false}
                  data={mainFlatListData}
                  // extraData={selectedTab}
                  nestedScrollEnabled={true}
                  keyExtractor={keyExtractor}
                  renderItem={mainFlatListRenderItem}
                  removeClippedSubviews={true}
                  windowSize={5}
                  maxToRenderPerBatch={5}
                  updateCellsBatchingPeriod={50}
                  onMomentumScrollBegin={() => setIsScrolling(true)}
                  onMomentumScrollEnd={() => setIsScrolling(false)}
                />
                <View>
                  {(addedItem > 0 &&
                    singleProductTierText &&
                    singleProductPrice) ||
                    (GPQty > 0 && singleProductTierText && singleProductPrice) ? (
                    <>
                      <SingleTierSection
                        addedItem={addedItem}
                        singleProductPrice={singleProductPrice}
                        totalSingleAmount={totalSingleAmount}
                        totalTierAmount={totalTierAmount}
                        congratsMsgShow={congratsMsgShow}
                        singleProductTierModal={singleProductTierModal}
                        setSingleProductTierModal={setSingleProductTierModal}
                        singleProductTierText={singleProductTierText}
                      />
                    </>
                  ) : null}

                  {!productLoader && renderProductFooter}
                </View>
              </View>
            </View>
          </>
        )}
        <ErrorHandler
          componentName={`${TAG} FullSizeProductImagesModal`}
          onErrorComponent={<View />}>
          <FullSizeProductImagesModal
            visible={visibleFullScreenImg}
            onClose={setVisibleFullScreenImg}
            productImages={productImages}
            carouselIndex={carouselIndex}
            setCarouselIndex={setCarouselIndex}
          />
        </ErrorHandler>
        {isShowFooterModal && (
          <ErrorHandler
            componentName={`${TAG} KnowMoreFooterModal`}
            onErrorComponent={<View />}>
            <KnowMoreFooterModal
              setIsShowFooterModal={setIsShowFooterModal}
              visible={isShowFooterModal}
              contentType={contentType}
              modalText={modalText}
              onClose={() => setIsShowFooterModal(false)}
            />
          </ErrorHandler>
        )}
        {rateProductModal && (
          <ErrorHandler
            componentName={`${TAG} RateProductModal`}
            onErrorComponent={<View />}>
            <RateProductModal
              visible={rateProductModal}
              product={product}
              review={review}
              navigation={navigation}
              groupProduct={groupProduct}
              onClose={() => setRateProductModal(false)}
              setRateProductModal={setRateProductModal}
            />
          </ErrorHandler>
        )}
        {/* ******************* similar product modal************************* */}
        {similarProductModal && showSimilar && (
          <ErrorHandler
            componentName={`${TAG} SimilarProductModal`}
            onErrorComponent={<View />}>
            <SimilarProductModal
              similarProducts={relatedProductData?.crosssell || []}
              visible={similarProductModal}
              onClose={() => setSimilarProductModal(false)}
              setSimilarProductModal={setSimilarProductModal}
              navigation={navigation}
            />
          </ErrorHandler>
        )}
        {/* ************************feedback modal********************************** */}
        {isShowFeedbackModal && (
          <ErrorHandler
            componentName={`${TAG} ValuableFeedbackModal`}
            onErrorComponent={<View />}>
            <ValuableFeedbackModal
              visible={isShowFeedbackModal}
              product={product}
              onClose={() => setIsShowFeedbackModal(false)}
              onSuccess={onSuccess.bind(null, 'valuableFeedback')}
            />
          </ErrorHandler>
        )}

        {/* ************************post your modal ***************************** */}
        <ErrorHandler
          componentName={`${TAG} PostQuestionModal`}
          onErrorComponent={<View />}>
          <PostQuestionModal
            visible={postQuestionModel}
            product={product}
            review={review}
            getFaqs={() => getFaqs()}
            onClose={() => setPostQuestionModel(false)}
            onSuccess={onSuccess.bind(null, 'postQuestion')}
          />
        </ErrorHandler>
        {/* ************************refer and earn modal ***************************** */}
        {referralModel && (
          <ErrorHandler
            componentName={`${TAG} ReferralModal`}
            onErrorComponent={<View />}>
            <ReferralModal
              visible={referralModel}
              refersRecord={refersRecord}
              coin={referCoin}
              loading={refersLoader}
              onClose={() => setReferralModel(false)}
            />
          </ErrorHandler>
        )}
        {/* ************************Share modal ***************************** */}

        {shareModel && (
          <ErrorHandler
            componentName={`${TAG} ShareModal`}
            onErrorComponent={<View />}>
            <ShareModal
              visible={shareModel}
              coin={referCoin}
              shareLink={`${WEBSITE_URL}${product?.seo?.url_key}.html`}
              onClose={() => setShareModel(false)}
            />
          </ErrorHandler>
        )}
        {/* ************************EMI modal ***************************** */}
        {isShowEmiModal.show && (
          <ErrorHandler
            componentName={`${TAG} EmiModal`}
            onErrorComponent={<View />}>
            <EmiModal
              visible={isShowEmiModal.show}
              label={isShowEmiModal.label}
              onClose={() => setIsShowEmiModal({ show: false, label: '' })}
            />
          </ErrorHandler>
        )}

        {/* ************************Bulk modal********************************** */}
        {bulkModal && (
          <ErrorHandler
            componentName={`${TAG} BulkModal`}
            onErrorComponent={<View />}>
            <BulkModal
              visible={bulkModal}
              product={product}
              requestPrice={reqPriceModal}
              onClose={() => [setBulkModal(false), setReqPriceModal(false)]}
              onSuccess={onSuccess.bind(null, 'bulk')}
            />
          </ErrorHandler>
        )}

        <ErrorHandler
          componentName={`${TAG} FaqListModal`}
          onErrorComponent={<View />}>
          <FaqListModal
            useInsets
            visible={showFaqListModal}
            review={review}
            onClose={setShowFaqListModal}
            flex={3}
            editQuestion={editQuestion}
            faqLoading={faqLoading}
            onUpdate={handleLikeDislike}
            faqQuestions={faqQuestionsModal}
            handleTextChange={handleTextChangeModal}
            getFaqLoading={getFaqLoading}
            onPressPostQuestion={postQuestionVerify}
            setSearchQuery={setSearchQueryModal}
          />
        </ErrorHandler>

        <ErrorHandler
          componentName={`${TAG} DeliveryInfoModal`}
          onErrorComponent={<View />}>
          <DeliveryInfoModal
            sellinPrice={totalTierAmount}
            MRP={totalOriginalAmount}
            visible={returnInfoModel}
            onClose={() => setReturnInfoModel(false)}
            infoIcon={infoIcon}
            deliveryStatusData={deliveryStatusData}
          />
        </ErrorHandler>

        <ErrorHandler
          componentName={`${TAG} FreeBieModal`}
          onErrorComponent={<View />}>
          <FreeBieModal
            visible={freeBieModal}
            onClose={() => setFreeBieModal(!freeBieModal)}
            message={freeBieMessage}
          />
        </ErrorHandler>
        {contentType === 'suggestProduct' && suggestProductModal && (
          <ErrorHandler
            componentName={`${TAG} SuggestProduct`}
            onErrorComponent={<View />}>
            <SuggestProduct
              visible={suggestProductModal}
              modelClose={handleCloseModal}
              isReset={isReset}
              onSubmit={onSubmitProductSuggestion}
              navigation={navigation}
            />
          </ErrorHandler>
        )}
        {singleProductTierText && (
          <ErrorHandler
            componentName={`${TAG} TireSuccessModal`}
            onErrorComponent={<View />}>
            <TireSuccessModal
              visible={singleProductTierModal}
              onClose={() => {
                setSingleProductTierModal(false);
              }}
            />
          </ErrorHandler>
        )}
        {successModel && (
          <ErrorHandler
            componentName={`${TAG} SuccessModal`}
            onErrorComponent={<View />}>
            <SuccessModal
              visible={successModel}
              title={successTitle}
              onClose={() => setSuccessModel(!successModel)}
            />
          </ErrorHandler>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

export default React.memo(ProductDetailScene);

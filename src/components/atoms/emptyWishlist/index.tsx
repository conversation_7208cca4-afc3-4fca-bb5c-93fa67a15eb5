import React from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import FastImage from 'react-native-fast-image';
import {Button} from 'components/molecules';
import {navigate} from 'utils/navigationRef';
import {ScrollView} from 'react-native-gesture-handler';
import Spacer from '../spacer';
import Label from '../label';
import Icons from 'common/icons';
import {t} from 'i18next';
import {useMemo} from 'react';

export type Props = {
  title?: string;
  text?: string;
  onCall?: () => void;
  btnShow?: boolean;
  hideIcon?: boolean;
};

const EmptyWishlist = ({title, text, hideIcon, onCall, btnShow}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <ScrollView style={styles.mainView}>
      {!hideIcon && (
        <FastImage
          resizeMode="contain"
          style={styles.thumpTopIcon}
          source={Icons.thumbsUp}
        />
      )}
      <View style={styles.wrapper}>
        <View style={styles.imageView}>
          <FastImage
            resizeMode="contain"
            style={styles.circle}
            source={Icons.cloudBg}
          />
          <FastImage
            resizeMode="contain"
            style={styles.emptyImage}
            source={Icons.emptyWishlist}
          />
          {!hideIcon && (
            <>
              <FastImage
                resizeMode="contain"
                style={styles.plusLeftIcon}
                source={Icons.plusIcon}
              />
              <FastImage
                resizeMode="contain"
                style={styles.plusRightIcon}
                source={Icons.plusIcon}
              />
            </>
          )}
        </View>
        {title && (
          <>
            <Label
              fontFamily="Medium"
              color="text"
              text={title}
              size="mx"
              align="center"
            />
          </>
        )}
        <Spacer size="xx4l" />
        {btnShow && (
          <Button
            onPress={() => {
              if (onCall) {
                onCall();
              }
              navigate('SearchProduct');
            }}
            style={styles.continueShopping}
            radius="m"
            type="secondary"
            labelSize="mx"
            labelStyle={styles.wishBtnTxt}
            labelColor="whiteColor"
            text={text ? text : t('buttons.createAWishlist')}
            // text={t('buttons.createAWishlist')}
          />
        )}
      </View>
      <Spacer size="xl" />
    </ScrollView>
  );
};

export default EmptyWishlist;

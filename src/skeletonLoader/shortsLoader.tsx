import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ShortsLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1000}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="10" rx="5" ry="5" width={width / 2} height="270" />
        <Rect
          x={(width - 20) / 2}
          y="10"
          rx="5"
          ry="5"
          width={width / 2}
          height="270"
        />

        <Rect x={10} y="270" rx="5" ry="5" width={width / 2} height="270" />
        <Rect
          x={(width - 20) / 2}
          y="270"
          rx="5"
          ry="5"
          width={width / 2}
          height="270"
        />

        <Rect x={10} y="550" rx="5" ry="5" width={width / 2} height="270" />
        <Rect
          x={(width - 20) / 2}
          y="550"
          rx="5"
          ry="5"
          width={width / 2}
          height="270"
        />

        <Rect x={10} y="830" rx="5" ry="5" width={width / 2} height="270" />
        <Rect
          x={(width - 20) / 2}
          y="830"
          rx="5"
          ry="5"
          width={width / 2}
          height="270"
        />
      </ContentLoader>
    </ScrollView>
  );
};

export default ShortsLoader;

import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const MembershipLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1750}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="10" rx="5" ry="5" width={width - 20} height={170} />
        <Rect
          x="10"
          y="190"
          rx="5"
          ry="5"
          width={(width - 30) / 2}
          height="100"
        />
        <Rect
          x={width / 2 + 5}
          y="190"
          rx="5"
          ry="5"
          width={(width - 30) / 2}
          height="100"
        />
        <Rect x="10" y="300" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="460" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="620" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="780" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="940" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1100" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1260" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1420" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1580" rx="5" ry="5" width={width - 20} height={150} />
      </ContentLoader>
    </ScrollView>
  );
};

export default MembershipLoader;

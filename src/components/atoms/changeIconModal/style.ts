import { Theme } from '@react-navigation/native';
import { Sizes } from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: Sizes.l,
        paddingHorizontal: Sizes.xxl,
        paddingVertical: Sizes.sx,
        margin: Sizes.xl,
        maxWidth: Sizes.screenWidth * 0.85,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: Sizes.xs,
        },
        shadowOpacity: 0.25,
        shadowRadius: Sizes.s,
        elevation: 5,
    },
    modalTitle: {
        fontSize: Sizes.xl,
        fontWeight: '600',
        color: colors.secondary,
        textAlign: 'center',
        marginVertical: Sizes.xm,
    },
    modalSubtitle: {
        color: '#666',
        textAlign: 'center',
        marginBottom: Sizes.xxl,
        lineHeight: Sizes.xsl,
    },
    imagesContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        // marginBottom: Sizes.x4l,
    },
    imageWrapper: {
        alignItems: 'center',
    },
    iconImage: {
        width: Sizes.ex,
        height: Sizes.ex,
        borderRadius: Sizes.l,
        marginBottom: Sizes.xm,
    },
    imageLabel: {
        fontSize: Sizes.mx,
        color: '#666',
        fontWeight: '500',
    },
    arrowContainer: {
        marginHorizontal: Sizes.xl,
    },
    icon: {
        width: Sizes.ex,
        height: Sizes.ex,
      },
    arrow: {
        fontSize: Sizes.xxl,
        color: colors.sunnyOrange,
        fontWeight: 'bold',
    },
    buttonsContainer: {
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: Sizes.x,
        borderTopColor: colors.lightgrey,
        marginTop: Sizes.l,
        marginHorizontal: -Sizes.xxl,
        paddingHorizontal: 0,
    },
    button: {
        flex: 1,
        paddingVertical: Sizes.mx,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: Sizes.x44,
    },
    noButton: {
        backgroundColor: 'white',
        borderRightColor: colors.lightgrey,
        borderRightWidth: 1
    },
    yesButton: {
        backgroundColor: 'white',
    },
    noButtonText: {
        fontSize: Sizes.l,
        fontWeight: '400',
        color: colors.grey
    },
    yesButtonText: {
        fontSize: Sizes.l,
        fontWeight: '600',
        color: colors.sunnyOrange
    },
  });
export default styles;
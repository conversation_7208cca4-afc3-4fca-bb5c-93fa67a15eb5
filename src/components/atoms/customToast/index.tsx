import React, {useMemo} from 'react';
import Toast, {BaseToastProps} from 'react-native-toast-message';
import {Image, TouchableOpacity, View} from 'react-native';
import Label from '../label';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import Spacer from '../spacer';
import stylesWithoutColor from './style';
import {useTheme} from '@react-navigation/native';
import getImageUrl from 'utils/imageUrlHelper';
import {navigationRef} from 'utils/navigationRef';
import {debugError} from 'utils/debugLog';

const CustomToast = ({type, text1, text2, props}: BaseToastProps) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithoutColor(colors), [colors]);
  const getBackgroundColor = () => {
    switch (type) {
      case 'info':
        return {bg: colors.blueInfo, round: colors.darkBlue1};
      case 'success':
      case 'cart':
      case 'wishList':
        return {bg: colors.green5, round: colors.darkCyanGreen};
      case 'error':
        return {bg: colors.linen1, round: colors.strongRed};
      case 'warn':
        return {bg: colors.warnToast1, round: colors.lightOrange};
      default:
        return {bg: colors.background, round: colors.background};
    }
  };

  const getIconSource = () => {
    if (props?.isAccountDelete) {
      return Icons.delete;
    }

    switch (type) {
      case 'info':
        return Icons.iMark;
      case 'error':
        return;
      case 'warn':
        return Icons.warningBold;
      case 'success':
        return Icons.checkVerifiedNew;
      default:
        return null;
    }
  };

  const getTintColor = () => {
    if (props?.theme === 'light') {
      switch (type) {
        case 'warn':
          return 'yellowToast';
        case 'error':
          return 'redToast';
        case 'info':
          return 'blueLine';
        case 'success':
        case 'cart':
        case 'wishList':
          return 'green2';
        default:
          return 'background';
      }
    }
    return 'background' || colors.background;
  };

  const handleNavigation = (target: string) => {
    Toast.hide();
    if (navigationRef.current) {
      navigationRef.current.navigate(target);
    } else {
      debugError('Navigation reference is not set!');
    }
  };
  const smallToast = type === 'cart' || type === 'wishList';

  return (
    <TouchableOpacity
      style={[
        smallToast ? styles.subContainer : styles.container,
        {backgroundColor: getBackgroundColor().bg},
      ]}
      activeOpacity={1}
      onPress={() =>
        smallToast
          ? handleNavigation(type === 'cart' ? 'Cart' : 'WishList')
          : {}
      }>
      {smallToast ? (
        props?.multiBorder ? (
          <View style={styles.underPrizeView}>
            {props?.qtyCount > 1 && <View style={styles.image1} />}
            {props?.qtyCount > 2 && <View style={styles.image2} />}
            <View
              style={[
                styles.underPrizeLastView,
                props?.qtyCount > 1 && styles.image3,
              ]}>
              <Image
                source={{uri: getImageUrl(props?.image)}}
                style={styles.imageStyle}
                resizeMode="stretch"
              />
            </View>
          </View>
        ) : (
          <Image
            source={
              props?.image === 'membershipImage'
                ? Icons.mamberShipCardImage
                : {uri: getImageUrl(props?.image)}
            }
            style={styles.imageStyle}
            resizeMode="stretch"
          />
        )
      ) : (
        <FastImage
          source={getIconSource()}
          resizeMode="contain"
          style={styles.iconStyle}
          tintColor={
            props?.theme === 'light'
              ? type === 'warn'
                ? colors.yellowToast
                : type === 'error'
                ? colors.redToast
                : type === 'info'
                ? colors.blueLine
                : type === 'success' || smallToast
                ? colors.green2
                : colors.background
              : type === 'warn'
              ? colors.warnToast
              : colors.background
          }
        />
      )}
      {smallToast ? <Spacer size="xs" type="Horizontal" /> : <View />}
      <View style={text2 ? styles.cartView : styles.labelView}>
        {text2 && (
          <Label
            color="background"
            text={text2}
            weight="600"
            size="m"
            style={text1 && styles.labelStyle}
          />
        )}
        {text1 && (
          <Label
            color={type === 'warn' ? 'warnToast' : 'background'}
            weight="500"
            text={text1}
            numberOfLines={3}
            size="m"
          />
        )}
      </View>
      {smallToast ? <Spacer size="xs" type="Horizontal" /> : <View />}
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => Toast.hide()}
        style={[
          styles.closeButton,
          {backgroundColor: getBackgroundColor().round},
        ]}>
        <Image source={Icons.cross} style={styles.crossStyle} />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default CustomToast;

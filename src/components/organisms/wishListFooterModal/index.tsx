import React, {memo} from 'react';
import {View, TouchableOpacity, SafeAreaView} from 'react-native';
import Modal from 'react-native-modal';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {ImageIcon} from 'components/atoms';
import {TouchableWithoutFeedback} from 'react-native';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose?: (visible: boolean) => void;
  Content: any;
};

const WishlistFoterModal = ({visible, onClose, Content}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <Modal
      onBackButtonPress={() => onClose?.(!visible)}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}
      avoidKeyboard={true}
>
      <SafeAreaView style={styles.flexContainer}>
        <View style={styles.centeredView}>
          <View style={styles.flexContainer} />
          <TouchableWithoutFeedback
            onPress={() => {
              onClose?.(!visible);
            }}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={() => onClose?.(!visible)}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x6l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>

          <View style={styles.modelBg}>{Content}</View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default memo(WishlistFoterModal);

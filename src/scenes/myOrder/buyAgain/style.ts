import {Fonts, Sizes} from 'common';
import {DeviceHeight, DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors?.background2,
    },
    subContainer: {
      backgroundColor: colors?.background2,
      flex: Sizes.x,
    },
    linerColor: {
      height: Sizes.x4l,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    searchIconView: {
      marginHorizontal: Sizes.m,
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      height: Sizes.x6l,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.m,
      borderColor: colors.text2,
      width: DeviceWidth - Sizes.xxl,
    },
    textInputView: {
      color: colors.text2,
      fontFamily: Fonts.Medium,
      fontSize: Sizes.m,
      width: DeviceWidth - Sizes.ex,
      height: Sizes.x6l,
      marginTop: Sizes.s,
    },
    saveFormBtn: {
      alignItems: 'center',
      flex: Sizes.x,
      width: (DeviceWidth - Sizes.x5l) / Sizes.xs,
      height: Sizes.x8l,
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      flexDirection: 'row',
      marginRight: Sizes.m,
      paddingHorizontal: Sizes.s,
    },
    subReqBtn: {
      paddingVertical: Sizes.mx,
      width: (DeviceWidth - Sizes.x5l) / Sizes.xs,
      height: Sizes.x8l,
      flexShrink: Sizes.x,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
    },
    image: {
      borderWidth: Sizes.x,
      padding: Sizes.sx,
      flexShrink: Sizes.x,
      alignItems: 'center',
      borderRadius: Sizes.s,
      borderColor: colors.grey2,
    },
    image1: {
      zIndex: -1,
      borderWidth: Sizes.x,
      alignItems: 'center',
      borderRadius: Sizes.s,
      borderColor: colors.grey2,
      width: Sizes.x34,
      height: Sizes.x34,
    },
    image2: {
      zIndex: -2,
      marginLeft: -26,
      borderWidth: Sizes.x,
      alignItems: 'center',
      borderRadius: Sizes.s,
      borderColor: colors.grey2,
      width: Sizes.x34,
      height: Sizes.x34,
    },
    image3: {
      marginLeft: -26,
      zIndex: 1,
      backgroundColor: colors.whiteColor,
    },
    imageSizeMain: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    subView: {
      backgroundColor: colors.whiteColor,
      marginTop: DeviceHeight - 670,
      alignItems: 'flex-start',
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.x,
    },
    modalSubView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
    },
    modalCloseBtnContainer: {
      top: -Sizes.x8l,
      alignSelf: 'center',
      position: 'absolute',
    },
    filteredDataView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xms,
    },
    flex: {
      flex: Sizes.x,
    },
    bottomBtnView: {
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.xsl,
      flexDirection: 'row',
      backgroundColor: colors.whiteColor,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: Sizes.xs,
    },
    halfWidth: {
      width: DeviceWidth / Sizes.sx,
    },
    modalOverlay: {
      flex: Sizes.x,
      backgroundColor: colors.blueLagoon,
    },
    modalStyle: {
      margin: 0,
    },
    listView: {
      height: 420,
      marginBottom: 5,
    },
  });

export default styles;

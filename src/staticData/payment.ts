const bankData = [
  {
    icon: 'bankImageIcon',
    backgroundColor: 'mistyRose',
    label: '15+ Banks',
  },
  {
    icon: 'flaxibalTimeIcon',
    backgroundColor: 'linen',
    label: 'Flexible time',
  },
  {
    icon: 'tansparentIcon',
    backgroundColor: 'grey7',
    label: 'Transparent charges',
  },
];

const allBankName = [
  {
    label: 'One Card',
  },
  {
    label: 'Indusind bank',
  },
  {
    label: 'IDFC FIRST Bank',
  },
  {
    label: 'ICICI Bank',
  },
  {
    label: 'HSBC',
  },
  {
    label: 'HSFC_DC',
  },
  {
    label: 'State Bank of India',
  },
  {
    label: 'One Card',
  },
  {
    label: 'Indusind bank',
  },
  {
    label: 'IDFC FIRST Bank',
  },
  {
    label: 'ICICI Bank',
  },
];

const debitCartData = [
  {
    icon: 'noDocumentIcon',
    backgroundColor: 'mistyRose',
    label: 'No Document',
  },
  {
    icon: 'flaxibalTimeIcon',
    backgroundColor: 'linen',
    label: 'Flexible time',
  },
  {
    icon: 'flowbiteCash',
    backgroundColor: 'grey7',
    label: 'Loan upto ₹ 100000',
  },
];

const payLaterData = [
  {
    icon: 'lazyPayIcon',
    label: 'LazyPay',
  },
  {
    icon: 'simplePayIcon',
    label: 'Simpl',
  },
];

const paymentOptions = [
  {
    icon: 'rupeeCircleWithOutBg',
    label: 'COD',
    content: [
      [
        'Experience Convenience and Trust with Our Cash on Delivery (COD) Payment Service',
      ],
      [
        'COD enabled for select products only. If all items in your cart are COD enabled, then COD service will be available on checkout. Max. cart amount applicable for COD is ₹20,000',
      ],
    ],
  },
  {
    icon: 'bank',
    label: 'Net Banking',
    content: [
      [
        'Retail and corporate net banking is now accepted for all major banks. Choose your preferred account type for a seamless transaction',
      ],
    ],
  },
  {
    icon: 'upiCardImage',
    label: ' UPI ',
    content: [
      'UPI payments accepted via Google Pay, PhonePe, BHIM, Paytm, CRED UPI, Amazon Pay UPI, and more for a fast, convenient checkout experience',
    ],
  },
  {
    icon: 'timeLine',
    label: 'Pay Later',
    content: [
      'Buy Now Pay Later powered by DentalKart.',
      'Avail upto 30 days interest free credit and upto 2% monthly interest there after',
      [
        'This offer is only applicable for healthcare professionals, establishments and resellers for duration upto 3 months',
        'Eligibility will be communicated within 24hrs post submission of relevant business documents(PAN, Aadhar, GST and Bank statements) on checkout.',
        'Processing fee upto ₹199+ GST may be applicable',
      ],
      'Other Options',
      'Avail upto 45 days interest free credit period',
      [
        'Need to have registered ICICI Paylater account',
        'Processing fee upto ₹199+ GST may be applicable',
      ],
      'Maximum: upto Rs. 50,000',
      'Avail 15 days interest free credit period',
      [
        'Need to have registered Lazypay account',
        'Processing fee upto ₹199+ GST may be applicable',
      ],
      'Maximum: upto Rs. 100,000',
    ],
  },
  {
    icon: 'creditCardOutline',
    label: 'Cards',
    content: [
      'Terms & Conditions',
      [
        'Currently, COD is available only for select pin codes. COD will be available nationwide very soon.',
        'COD is only open across limited products. For applicable products, COD will show in payment options on Product Page view eligible products.',
        'Cart is eligible for COD only when all products in the cart are eligible for COD.',
        'COD is only applicable for orders less than INR 25,000.',
        'Delivery person will reach out to you on registered mobile number only.',
      ],
      'Other Important Instructions',
      [
        'Delivery OTP will be shared on registered mobile number and registered mail id only.',
        'Failing to deliver in 3 attempts will lead to return of the shipment.',
        'In case of wrong/damaged goods, take a picture of received goods and share with Medikabazaar within 24 hrs on mail.',
        'Incase of delivery confirmation message without receiving goods.Medikabazaar must be informed within 24 hrs on mail.',
        'In-case of return, 2 attempts of pick up will be made.',
        'At the time of pickup, please make sure that the pick-up person seals the package in front of you.',
      ],
    ],
  },
  {
    icon: 'creditCardOutline',
    label: ' EMI',
    content: [
      'Bank Name',
      'Min Amount 3 months',
      '(Interest Rate)',
      '6 months',
      '(Interest Rate)',
      'KOTAK DC EMIINR 3000/-0%0%SBI DC EMIINR 3000/-0%0%ICICI EMIINR 3000/-0%0%',
      [
        'This offer is only applicable for active card holders of respective banks. Max eligible amount is dependent on credit line applicable for respective card.',
        'Processing fee up to ₹199 + GST may be applicable.',
      ],
    ],
  },
];

const paymentMethods = {
  payment_methods: [
    {
      code: 'razorpay',
      title: 'PAY ONLINE USING UPI, DEBIT/CREDIT CARD, NETBANKING, WALLET, EMI',
      __typename: 'PaymentMethod',
    },
    {
      code: 'cashondelivery',
      title: 'CASH ON DELIVERY (AVAILABLE FOR ORDER AMOUNT BELOW 20,000)',
      __typename: 'PaymentMethod',
    },
  ],
  checkcod: [
    {
      cod_available: true,
      message: 'COD available.',
      message_arr: [],
      product_id: 44990,
      service_available: true,
      type: '1',
      __typename: 'CodPrepaidResponseV2',
    },
  ],
  max_delivery_days: '',
  delivery_days: [],
  return_info: {
    message: '',
    message_arr: [],
    return_period: 0,
    returnable: false,
    __typename: 'ReturnResponse',
  },
  __typename: 'PaymentMethodResponseV2',
};

export {
  bankData,
  allBankName,
  debitCartData,
  payLaterData,
  paymentOptions,
  paymentMethods,
};

import {Sizes} from 'common';
import {t} from 'i18next';
import Toast, {ToastPosition} from 'react-native-toast-message';
import {Platform} from 'react-native';
export const showErrorMessage = (
  message: string,
  pos?: ToastPosition,
  theme?: string,
  extraProps?: Record<string, any>
) => {
  Toast.show({
    text1: message,
    position: pos ? pos : 'bottom',
    bottomOffset: Sizes.x70,
    autoHide: true,
    visibilityTime: 4000,
    type: 'error',
    props: {
      theme: theme ? theme : 'dark',
      ...extraProps,
    },
    onPress: () => Toast.hide(),
  });
};

export const showSuccessMessage = (
  message: string,
  pos?: ToastPosition,
  theme?: string,
) => {
  Toast.show({
    text1: message,
    bottomOffset: Sizes.x70,
    position: pos ? pos : 'bottom',
    autoHide: true,
    visibilityTime: 4000,
    type: 'success',
    props: {theme: theme ? theme : 'dark'},
    onPress: () => Toast.hide(),
  });
};
export const showWarnMessage = (
  message: string,
  pos?: ToastPosition,
  theme?: string,
) => {
  Toast.show({
    text1: message,
    bottomOffset: Sizes.x70,
    position: pos ? pos : 'bottom',
    autoHide: true,
    visibilityTime: 4000,
    type: 'warn',
    props: {theme: theme ? theme : 'dark'},
    onPress: () => Toast.hide(),
  });
};
export const showCartMessage = (
  message?: string,
  pos?: ToastPosition,
  image?: string | undefined,
  multiBorder?: boolean | null,
  qtyCount?: number | undefined,
  theme?: string,
) => {
  Toast.show({
    text1: message,
    text2: t('PDP.ViewCart'),
    bottomOffset: Platform.OS === 'ios' ? Sizes.exl : Sizes.ex,
    position: pos ? pos : 'bottom',
    autoHide: true,
    visibilityTime: 4000,
    type: 'cart',
    props: {image, multiBorder, qtyCount, theme: theme ? theme : 'dark'},
    onPress: () => Toast.hide(),
  });
};
export const showWishListMessage = (
  message: string,
  pos?: ToastPosition,
  image?: string | undefined,
  theme?: string,
) => {
  Toast.show({
    text1: '',
    text2: t('toastMassages.addedToWishlist'),
    bottomOffset: Sizes.x70,
    position: pos ? pos : 'bottom',
    autoHide: true,
    visibilityTime: 4000,
    type: 'wishList',
    props: {image, theme: theme ? theme : 'dark'},
    onPress: () => Toast.hide(),
  });
};

export const showInfoMessage = (
  message: string,
  pos?: ToastPosition,
  theme?: string,
) => {
  Toast.show({
    text1: message,
    position: pos ? pos : 'bottom',
    bottomOffset: Sizes.x70,
    autoHide: true,
    visibilityTime: 4000,
    type: 'info',
    props: {theme: theme ? theme : 'dark'},
    onPress: () => Toast.hide(),
  });
};

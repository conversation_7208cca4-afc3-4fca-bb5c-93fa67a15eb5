import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import Modal from 'react-native-modal';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  btn1Click: () => void;
  btn2Click: () => void;
  title: string;
  subTitle: string;
  btnText1?: string;
  btnText2?: string;
};

const CustomDialog = (props: Props) => {
  const {
    visible,
    onClose,
    btn1Click,
    btn2Click,
    title,
    subTitle,
    btnText1,
    btnText2,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <Modal
      onBackButtonPress={() => onClose()}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.25}>
      <>
        <View style={styles.modalView}>
          <View style={styles.modalSubView}>
            <View style={styles.subView}>
              <Spacer size="s" />
              <Label text={title} size="l" weight="600" color="text" />
              <Spacer size="xm" />
              <Label text={subTitle} size="mx" weight="500" color="text2" />
            </View>
          </View>
          <Spacer size="m" />
          <View style={styles.borderBottomView} />
          <Spacer size="m" />
          <View style={styles.btnItemView}>
            <TouchableOpacity
              style={styles.btn1View}
              onPress={() => btn1Click()}>
              <Label
                text={btnText1 || t('buttons.yes')}
                size="mx"
                weight="500"
                color="text"
              />
            </TouchableOpacity>
            <View style={styles.borderLine} />
            <Spacer size="xm" type="Horizontal" />
            <TouchableOpacity
              style={styles.btn2View}
              onPress={() => btn2Click()}>
              <Label
                text={btnText2 || t('buttons.no')}
                size="mx"
                weight="500"
                color="text"
              />
            </TouchableOpacity>
          </View>
          <Spacer size="s" />
        </View>
      </>
    </Modal>
  );
};

export default React.memo(CustomDialog);

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    iconContainer: {
      flexDirection: 'row',
      flex: 1,
    },
    sec4LabelView: {
      flexDirection: 'row',
      justifyContent: 'center',
    },
    sec4View1: {flex: 2.5, overflow: 'hidden'},
    sec4View2: {flex: 1},
    card4: {
      flex: 1,
      flexDirection: 'row',
      height: 200,
      borderWidth: 1,
      paddingHorizontal: Sizes.m,
      backgroundColor: '#fff',
      borderRadius: Sizes.m,
      borderColor: colors.grey2,
      paddingVertical: Sizes.l,
      // elevation: 3,
      shadowColor: colors.black,
      shadowOpacity: 0.1,
      shadowRadius: 5,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      elevation: 2,
    },
    labelBorder: {paddingHorizontal: Sizes.sx},

    cardImage: {
      width: '100%',
      height: '100%',
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },

    maincontainer: {
      paddingHorizontal: Sizes.m,
      flex: 1,
    },
  });

export default styles;

import React, {useEffect, useState, useMemo} from 'react';
import {
  Keyboard,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {Button, PhoneInputText} from 'components/molecules';
import {t} from 'i18next';
import {bulkProducts} from 'services/productDetail';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {
  handleErrMsg,
  stringReg,
  emailRegExp,
  phoneReg,
  pinCodeRegExp,
  numberRegExp,
  decimalNumRegExp,
} from 'utils/utils';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';
import { debugLog } from 'utils/debugLog';

type Props = {
  visible: boolean;
  onClose: () => void;
  product: ProductData | null;
  onSuccess: () => void;
  requestPrice: boolean;
};

const BulkModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const {visible, onClose, product, onSuccess, requestPrice} = props;
  const [errors, setErrors] = useState({});
  const [values, setValues] = useState({
    name: '',
    expected_price: '',
    email_id: '',
    contact_no: '',
    postcode: '',
    address: '',
    quantity: '',
  });

  const validate = () => {
    const error = {};
    let errorStatus = true;
    // Validation Rules
    const validations = [
      {
        field: 'userName',
        rule: stringReg,
        message: t('validations.nameField'),
        matchMessage: t('validations.nameAlphabets'),
      },
      {
        field: 'userPhone',
        rule: phoneReg,
        message: t('validations.phoneField'),
        matchMessage: t('validations.phoneCorrect'),
      },
      {
        field: 'userEmail',
        rule: emailRegExp,
        message: t('validations.emailField'),
        matchMessage: t('validations.emailCorrect'),
      },
      {
        field: 'userPincode',
        rule: pinCodeRegExp,
        message: t('validations.pinCode'),
        matchMessage: t('validations.pinCodeValid'),
      },
      {
        field: 'expectedPrice',
        rule: decimalNumRegExp,
        message: t('validations.exPrice'),
        matchMessage: t('validations.priceNum'),
      },
      {
        field: 'bulkQuantity',
        rule: numberRegExp,
        message: t('validations.quantityField'),
        matchMessage: t('validations.quantityNum'),
      },
      {field: 'address', message: t('validations.addressField')},
    ];

    // General Field Validation
    for (const {
      field,
      rule,
      message,
      matchMessage,
      required = true,
    } of validations) {
      const value =
        field.split('.').reduce((obj, key) => obj?.[key], values) || '';

      if (required && !value) {
        error[field] = message;
        errorStatus = false;
      } else if (rule && !rule.test(value)) {
        error[field] = matchMessage;
        errorStatus = false;
      }
    }

    // Bulk Pricing & Quantity Validation
    const expectedPrice = parseFloat(values.expectedPrice || 0);
    const bulkQuantity = parseInt(values.bulkQuantity || 0, 10);
    const source = 0; // Business logic condition
    const price = product?.pricing?.selling_price || 0;

    if (values?.expectedPrice && source === 0 && price) {
      const inRange = expectedPrice <= price && expectedPrice >= price * 0.5;
      const totalPrice = expectedPrice * bulkQuantity;

      if (!inRange) {
        error['expectedPrice'] = `${t('bulk.lessPrice')} ${price * 0.5} ${t(
          'bulk.greaterPrice',
        )} ${price}`;
        errorStatus = false;
      }

      if (bulkQuantity && inRange && totalPrice < 10000) {
        error['bulkQuantity'] = t('bulk.bulkTitle');
        errorStatus = false;
      }
    }

    setErrors(error);
    return errorStatus;
  };

  const createBulkOrder = async () => {
    let checkValid = await validate();
    if (checkValid) {
      Keyboard.dismiss();
      let payload = {
        product_id: product?.product_id,
        name: values?.userName.trim(),
        email_id: values?.userEmail.trim(),
        contact_no: values?.userPhone.trim(),
        postcode: values?.userPincode.trim(),
        quantity: values?.bulkQuantity.trim(),
        expected_price: values?.expectedPrice.trim().replace(/\.$/, '.00'),
        source: 0,
        address: values.address.trim(),
      };
      bulkProducts(payload)
        .then((res: any) => {
          if (res?.status) {
            onSuccess();
          } else {
            showErrorMessage(handleErrMsg(res?.data));
          }
          onClose();
        })
        .catch(err => {
          showErrorMessage(t('validations.someThingWrong'));
          debugLog(err, 'err res');
        });
    }
  };

  const onChangeText = (text, key) => {
    const validationRules = {
      userName: {
        pattern: /^[a-zA-Z\s]*$/,
        required: t('validations.nameField'),
        invalid: t('validations.nameAlphabets'),
      },
      userPhone: {
        pattern: phoneReg,
        required: t('validations.phoneField'),
        invalid: t('validations.phoneCorrect'),
      },
      userEmail: {
        pattern: emailRegExp,
        required: t('validations.emailField'),
        invalid: t('validations.emailCorrect'),
      },
      userPincode: {
        pattern: pinCodeRegExp,
        required: t('validations.pinCode'),
        invalid: t('validations.pinCodeValid'),
      },
      address: {required: t('validations.addressField')},
      expectedPrice: {
        pattern: decimalNumRegExp,
        required: t('validations.exPrice'),
        invalid: t('validations.priceNum'),
      },
      bulkQuantity: {
        pattern: numberRegExp,
        required: t('validations.quantityField'),
        invalid: t('validations.quantityNum'),
      },
    };

    const error = {...errors};
    const rule = validationRules[key];

    // Update state
    setValues(prev => ({...prev, [key]: text}));

    if (!rule) return;

    // General Validation
    if (!text && rule.required) {
      error[key] = rule.required;
    } else if (rule.pattern && !rule.pattern.test(text)) {
      error[key] = rule.invalid || '';
    } else {
      error[key] = '';
    }

    // Bulk Pricing & Quantity Validation
    if (key === 'expectedPrice' || key === 'bulkQuantity') {
      const expectedPrice =
        key === 'expectedPrice'
          ? parseFloat(text)
          : parseFloat(values.expectedPrice || 0);
      const bulkQuantity =
        key === 'bulkQuantity'
          ? parseInt(text || 0, 10)
          : parseInt(values.bulkQuantity || 0, 10);
      const source = 0; // Business logic
      const price = product?.pricing?.selling_price || 0;

      if (expectedPrice && source === 0 && price) {
        const inRange = expectedPrice <= price && expectedPrice >= price * 0.5;
        const totalPrice = expectedPrice * bulkQuantity;

        if (!inRange) {
          error['expectedPrice'] = `${t('bulk.lessPrice')} ${price * 0.5} ${t(
            'bulk.greaterPrice',
          )} ${price}`;
        } else {
          error['expectedPrice'] = '';
        }

        if (bulkQuantity && inRange && totalPrice < 10000) {
          error['bulkQuantity'] = t('bulk.bulkTitle');
        } else {
          error['bulkQuantity'] = '';
        }
      }
    }
    setErrors(error);
  };
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );

    const defaultAddress = userInfo?.addresses.find(
      item => item?.default_shipping,
    );

    const selectAddress = defaultAddress || userInfo?.addresses?.[0] || '';

    if (selectAddress) {
      const {
        postcode = '',
        street = [],
        city = '',
        region = {},
      } = selectAddress;

      const formattedAddress = [
        street.join(', '),
        city,
        region?.region ? region.region : '',
      ]
        .filter(Boolean)
        .join(', ');

      setValues({
        ...values,
        userPincode: postcode,
        address: formattedAddress,
      });
    }

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);
  const insets = useSafeAreaInsets();

  return (
    <DynamicHeightModal
      // useInsets
      visible={visible}
      onClose={onClose}
      maxHeight={0.86}
      content={
        // <SafeAreaView>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[
              styles.flex,
              // {paddingTop: isKeyboardVisible ? insets.top : 0},
            ]}>
            <ScrollView keyboardShouldPersistTaps="always" style={styles.overallView}>
              <Spacer size="l" />
              <Label
                text={
                  requestPrice ? t('buttons.reqPrice') : t('bulk.bulkTitle')
                }
                size="l"
                weight="500"
                color="text"
              />
              <Spacer size="m" />
              <View style={styles.bottomLine} />
              <Spacer size="m" />
              <PhoneInputText 
                testID="txtBulkEnterYourName"
                inputStyle={styles.input}
                searchIconStyle={styles.searchIconStyle}
                style={styles.inputBorderStyle}
                placeholder={t('bulk.namePlace')}
                icon="newUser"
                placeholderTextColor={colors.text2}
                tintColor="text2"
                onChangeText={text =>
                  onChangeText(text.trimStart(), 'userName')
                }
                value={values.userName}
                error={String(errors?.userName || '')}
              />
              <Spacer size="m" />
              <PhoneInputText
                testID="txtBulkPhoneNumber"
                inputStyle={styles.input}
                searchIconStyle={styles.searchIconStyle}
                type="number-pad"
                style={styles.inputBorderStyle}
                maxLength={10}
                placeholder={t('bulk.phonePlace')}
                icon="phoneIcon"
                placeholderTextColor={colors.text2}
                tintColor="text2"
                onChangeText={text =>
                  onChangeText(text.trimStart(), 'userPhone')
                }
                value={values.userPhone}
                error={String(errors?.userPhone || '')}
              />

              <Spacer size="m" />
              <PhoneInputText
                testID="txtBulkEmail"
                inputStyle={styles.input}
                searchIconStyle={styles.searchIconStyle}
                style={styles.inputBorderStyle}
                placeholder={t('bulk.emailPlace')}
                icon="emailIcon"
                placeholderTextColor={colors.text2}
                tintColor="text2"
                onChangeText={text =>
                  onChangeText(text.trimStart(), 'userEmail')
                }
                value={values.userEmail}
                error={String(errors?.userEmail || '')}
              />
              <Spacer size="m" />
              <PhoneInputText
                testID="txtBulkPinCode"
                inputStyle={styles.input}
                searchIconStyle={styles.searchIconStyle}
                type="number-pad"
                style={styles.inputBorderStyle}
                placeholder={t('bulk.pinCodePlace')}
                maxLength={6}
                icon="mapPin"
                placeholderTextColor={colors.text2}
                tintColor="text2"
                onChangeText={text =>
                  onChangeText(
                    text?.replace(/[^0-9]/g, '').trimStart(),
                    'userPincode',
                  )
                }
                value={values.userPincode}
                error={String(errors?.userPincode || '')}
              />

              <Spacer size="m" />
              <PhoneInputText
                testID="txtBulkAddress"
                inputStyle={styles.input}
                searchIconStyle={styles.searchIconStyle}
                style={styles.inputBorderStyle}
                placeholder={t('bulk.addressPlace')}
                icon="mapPin"
                placeholderTextColor={colors.text2}
                tintColor="text2"
                onChangeText={text => onChangeText(text.trimStart(), 'address')}
                value={values.address}
                error={String(errors?.address || '')}
              />

              <Spacer size="m" />
              <PhoneInputText
                testID="txtBulkQuantity"
                inputStyle={styles.input}
                searchIconStyle={styles.searchIconStyle}
                style={styles.inputBorderStyle}
                type="number-pad"
                placeholder={t('bulk.quantityPlace')}
                icon="addIcon"
                placeholderTextColor={colors.text2}
                tintColor="text2"
                onChangeText={text =>
                  onChangeText(
                    text?.replace(/[^0-9]/g, '').trimStart(),
                    'bulkQuantity',
                  )
                }
                value={values.bulkQuantity}
                error={String(errors?.bulkQuantity || '')}
              />

              <Spacer size="m" />

              <PhoneInputText
                testID="txtBulkExpectedPrice"
                inputStyle={styles.input}
                searchIconStyle={styles.searchIconStyle}
                type="numeric"
                style={styles.inputBorderStyle}
                placeholder={t('bulk.pricePlace')}
                icon="rupeeCircle"
                placeholderTextColor={colors.text2}
                tintColor="text2"
                onChangeText={text =>
                  onChangeText(
                    text?.replace(/[^0-9.]/g, '').trimStart(),
                    'expectedPrice',
                  )
                }
                value={values?.expectedPrice}
                error={String(errors?.expectedPrice || '')}
              />
              <Spacer size="m" />
              <View style={styles.cancelView}>
                {requestPrice && (
                  <Button
                    onPress={() => onClose()}
                    size="large"
                    labelSize="mx"
                    weight="400"
                    paddingHorizontal="x7l"
                    text={t('buttons.cancel')}
                    labelColor="categoryTitle"
                  />
                )}
                <Button
                  onPress={() => createBulkOrder()}
                  radius="xms"
                  size="large"
                  labelSize="mx"
                  weight="400"
                  paddingHorizontal="x7l"
                  text={t('buttons.submit')}
                  type="bordered"
                  labelColor="sunnyOrange3"
                  borderColor="sunnyOrange3"
                />
              </View>
              <Spacer size="m" />
            </ScrollView>
          </KeyboardAvoidingView>
        // </SafeAreaView>
      }
    />
  );
};

export default React.memo(BulkModal);

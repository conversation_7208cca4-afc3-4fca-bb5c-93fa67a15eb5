import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ListLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1600}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="10" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="120" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="230" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="340" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="450" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="560" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="670" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="780" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="890" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="1000" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="1110" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="1220" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="1330" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x={10} y="1440" rx="5" ry="5" width={width - 20} height="100" />
      </ContentLoader>
    </ScrollView>
  );
};

export default ListLoader;

import Icons from 'common/icons';
import React from 'react';
import {Image, TouchableOpacity} from 'react-native';
import Label from '../label';
import stylesWithOutColor from './style';
import Spacer from '../spacer';
import {navigate} from 'utils/navigationRef';
import {useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {productDummyImage} from 'utils/imageUrlHelper';
import {useMemo} from 'react';

type Props = {
  icon?: keyof typeof Icons;
  card?: boolean;
  index: number;
  item: News;
  AllNews?: any;
};

const NewsCardItem = ({item, index, AllNews}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <TouchableOpacity
      onPress={() => {
        navigate('NewsDetails', {item: AllNews, currentItem: item});
      }}
      key={index}
      style={styles.mainContinuer}>
      <FastImage
        onError={(e: {target: {uri: string}}) => {
          e.target.uri = productDummyImage;
        }}
        resizeMode="cover"
        style={styles.imageCarousal}
        source={{uri: item.image}}
      />
      <Spacer type="Vertical" size="xm" />
      <Label
        numberOfLines={2}
        weight="700"
        color="textLight"
        text={item.title}
      />
      <Spacer type="Vertical" size="s" />
      <Label numberOfLines={3} color="lightGray" text={item.content} />
    </TouchableOpacity>
  );
};

export default NewsCardItem;

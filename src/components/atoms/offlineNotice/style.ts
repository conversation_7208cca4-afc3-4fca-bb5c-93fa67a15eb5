import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    },
    view: {
      flex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
    },
    image: {
      height: Sizes.ex330,
      width: Sizes.ex234,
    },
    btnStyle: {
      alignSelf: 'center',
      height: Sizes.x7l,
      width: Sizes.ex248,
    },
    btnText: {
      fontFamily: Fonts.Medium,
    },
  });

export default styles;

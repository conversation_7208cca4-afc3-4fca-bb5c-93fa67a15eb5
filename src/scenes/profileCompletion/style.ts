import {Dimensions, StyleSheet} from 'react-native';
import {Fonts, Sizes} from 'common';

const stylesWithOutColor = (colors: Theme['colors']) =>
  StyleSheet.create({
    backgroundContainer: {
      flex: 1,
      backgroundColor: '#E8F5E9', // Light green background
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    keyboardAvoidView: {
      flex: 1,
    },
    parent: {
      position: 'relative',
    },
    scrollViewContent: {
      flexGrow: 1,
      paddingHorizontal: Sizes.l,
      paddingTop: Sizes.xl,
      paddingBottom: Sizes.xxl,
    },
    logoContainer: {
      alignItems: 'center',
      // marginTop: Sizes.l,
    },
    logoView: {
      alignSelf: 'center',
      padding: 0,
      margin: 0,
      width: Dimensions.get('window').width,
      height: 41,
    },
    logo: {
      width: 300,
      height: 80,
    },
    formContainer: {
      marginTop: Sizes.m,
    },
    headingContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
      gap: Sizes.s,
    },
    headerText: {
      textAlign: 'center',
    },
    verifiedTag: {
      position: 'absolute',
      flexDirection: 'row',
      justifyContent: 'center',
      alignSelf: 'flex-end',
      height: '100%',
      alignItems: 'center',
      right: Sizes.m,
      gap: Sizes.s,
      top: 5,
    },
    fieldContainer: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      gap: Sizes.mx,
    },
    expandField: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: Sizes.xs,
    },
    verifiedIcon: {
      marginLeft: Sizes.xs,
    },
    eyeIcon: {
      padding: Sizes.xs,
    },
    passwordHelp: {
      marginLeft: Sizes.s,
    },
    referralContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginTop: Sizes.m,
    },
    checkboxStyle: {
      height: Sizes.mx,
      width: Sizes.mx,
      top: 2,
    },
    downImg: {
      transform: [{rotate: '180deg'}],
    },
    termsTextContainer: {
      flex: 1,
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginLeft: Sizes.s,
      // alignItems: 'center',
      marginTop: -2,
      gap: Sizes.x,
    },
    pHor: {
      paddingHorizontal: Sizes.s,
    },
    buttonsContainer: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'center',
      // backgroundColor:'red'
    },
    skipButton: {
      width: '82%',
      left: 10,
      borderRadius: Sizes.m,
    },
    completeButton: {
      width: '82%',
      right: 10,
      borderRadius: Sizes.m,
    },
    helpContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: Sizes.xms,
    },
    footerContainer: {
      flexDirection: 'row',
      gap: Sizes.s,
      alignItems: 'center',
    },

    inputTextView: {
      color: colors.text,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      marginBottom: -Sizes.xs,
    },
    dropDownTextStyle: {
      color: colors.text,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    styleDropDown: {
      height: Sizes.x7l,
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    dropDownItem: {
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.sx,
    },
    placeTextStyle: {
      color: colors.grey,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
    },
    dropDownLeft: {
      marginRight: 12,
      marginLeft: 8,
    },
    closeIcons: {
      position: 'absolute',
      top: Sizes.x,
      right: -5,
      zIndex: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
  });

export default stylesWithOutColor;

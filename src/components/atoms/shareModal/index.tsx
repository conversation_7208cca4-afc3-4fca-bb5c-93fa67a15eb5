import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  Pressable,
  ToastAndroid,
  Image,
  Share,
  Platform,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import Clipboard from '@react-native-community/clipboard';
// import Share from 'react-native-share';
import {t} from 'i18next';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import RNFetchBlob from 'rn-fetch-blob';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {showSuccessMessage} from 'utils/show_messages';
import {useMemo} from 'react';
import { debugLog } from 'utils/debugLog';
import { Sizes } from 'common';

type Props = {
  visible: boolean;
  onClose: () => void;
  shareLink: string;
  coin: number;
};

const ShareModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [showGif, setShowGif] = useState(false);
  const {visible, onClose, shareLink, coin} = props;

  const copyToClipboard = data => {
    Clipboard.setString(data);
    setShowGif(true);
    showSuccessMessage('copy to clipboard');
  };
  const rupee = Math.floor(coin / 2);

  const downloadImage = async (imageUrl: string) => {
    const {fs} = RNFetchBlob;
    const path = `${fs.dirs.CacheDir}/myimage.jpg`;

    const response = await RNFetchBlob.config({
      fileCache: true,
      path,
    }).fetch('GET', imageUrl);

    debugLog('Response: ' + response.path());

    return response.path(); // Returns local file path
  };

  // const referralShare = async (url, socialName) => {
  //   try {
  //     if (socialName === 'INSTAGRAM') {
  //       debugLog(url, 'url***');
  //       const filePath = await downloadImage(url);
  //       debugLog(`file://${filePath}`);
  //       const shareOptions = {
  //         url: url,
  //         type: 'text',
  //         social: Share.Social.INSTAGRAM,
  //       };
  //       return await Share.shareSingle(shareOptions);
  //     } else if (socialName === 'WHATSAPP') {
  //       return await Share.shareSingle({
  //         // url: url,
  //         message: url,
  //         social: Share?.Social?.WHATSAPP,
  //       });
  //     }
  //     return await Share.shareSingle({
  //       url: url,
  //       social: Share?.Social[socialName],
  //     });
  //   } catch (error) {
  //     ToastAndroid.show(t('PDP.installed'), ToastAndroid.SHORT);
  //   }
  // };

  const onShare = () => {
    Share.share(
      {
        message: `${shareLink}`,
      },
      {dialogTitle: 'Share on ..', tintColor: 'green'},
    ).catch(err => debugLog(err));
  };
  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={() => onClose()}
      content={
        <View style={[styles.referView, Platform.OS === 'ios' && {maxHeight : Sizes.exl}]}>
          <View style={styles.referLinkView}>
            <Label
              text={t('myReferral.copyLink')}
              size="mx"
              weight="500"
              color="text"
            />
            <Label
              onPress={() => copyToClipboard(shareLink)}
              style={styles.flex}
              text={shareLink}
              size="mx"
              weight="500"
              align="left"
              numberOfLines={1}
              color="orangeOffer"
            />
            <Spacer size="xm" type="Horizontal" />
            {!showGif ? (
              <Pressable
                onPress={() => copyToClipboard(shareLink)}
                style={styles.referralLink}>
                <Spacer type="Horizontal" size="xm" />
                <ImageIcon
                  tintColor="silkBlue2"
                  icon="newCopyIcon"
                  size="xxl"
                />
              </Pressable>
            ) : (
              <FastImage
                source={Icons.copyGif}
                style={styles.copyGif}
                resizeMode="contain"
              />
            )}
          </View>
          {showGif && (
            <>
              <Spacer size="s" />
              <Label
                color="green2"
                fontFamily="SemiBold"
                size="mx"
                text={t('toastMassages.copyLinkText')}
               />
            </>
          )}
          <Spacer size="l" />
          <View style={styles.dericationView}>
            <Label
              align="center"
              color="text"
              weight="600"
              size="l"
              text={t('myReferral.shareVia')}
            />
            <TouchableOpacity
              // onPress={() => referralShare(shareLink, 'WHATSAPP')}
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="ri_whatsappIcon" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(shareLink, 'FACEBOOK')}
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="uil_facebookIcon" />
            </TouchableOpacity>
            {/* <TouchableOpacity
              onPress={() =>
                referralShare(
                  'https://sample-videos.com/img/Sample-png-image-100kb.png',
                  'INSTAGRAM',
                )
              }> */}
            <TouchableOpacity
              // onPress={() => referralShare(shareLink, 'INSTAGRAM')}
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="ri_instagramFillIcon" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(shareLink, 'TWITTER')}
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="twitter" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(shareLink, 'PINTEREST')}
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="Pinterest"/>
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(shareLink, 'LINKEDIN')}
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="mdi_linkedinIcon" />
            </TouchableOpacity>
          </View>
          <Spacer size="l" />
        </View>
      }
    />
  );
};
export default ShareModal;

import {Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';
const isTablet = Sizes.windowWidth >= 768;
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    modalHeaderView: {
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
      width: '100%',
    },
    subContainer: {
      marginTop: Sizes.sx,
      paddingHorizontal: Sizes.m,
      marginBottom: Sizes.m,
    },
    marqueeView: {
      backgroundColor: colors.lavender,
      paddingVertical: Sizes.l,
      marginBottom: Sizes.xl,
    },
    backgroundImage: {
      resizeMode: 'contain',
      justifyContent: 'center',
    },
    recentlyView: {
      flex: Sizes.x,
      marginBottom: Sizes.mx,
    },
    recentlySubView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xl,
    },
    recentlyLine: {
      width: Sizes.x46,
      height: Sizes.x,
    },
    waldentView: {paddingHorizontal: Sizes.xms},
    productView: {paddingBottom: Sizes.m},
    homeMainView: {
      backgroundColor: 'white',
      paddingHorizontal: Sizes.m,
    },
    homeSubView: {
      width: '100%',
      height: Sizes.ex0,
    },
    sellerView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: Sizes.l,
    },
    listDataView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingBottom: Sizes.xxl,
    },
    searchedView: {
      borderWidth: Sizes.x,
      height: Sizes.x56,
      paddingHorizontal: Sizes.xl,
      marginHorizontal: Sizes.s,
      borderRadius: Sizes.xm,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderColor: colors.blue,
    },
    hotSellerProductView: {
      flex: Sizes.x,
      paddingTop: Sizes.m,
    },
    backImageView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.l,
      paddingBottom: Sizes.m,
      alignItems: 'center',
    },
    endKingView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: Sizes.xms,
      paddingRight: Sizes.x9l,
    },
    countView: {
      flex: Sizes.x,
      paddingVertical: 0,
      paddingHorizontal: Sizes.m,
    },
    offerZoneBannerView: {
      backgroundColor: colors.grey7,
      borderRadius: Sizes.xxl,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.m,
    },
    offerBannerView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    offerBannerSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    offerBannerImageView: {width: Sizes.ex104, height: Sizes.xxl},
    scissorsView: {marginLeft: Sizes.s},
    tryOffersView: {
      borderRadius: Sizes.mx,
      height: Sizes.x7l,
      backgroundColor: colors.blue2Offer,
      paddingHorizontal: Sizes.m,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    sampleMainView: {
      flex: Sizes.x,
      paddingVertical: Sizes.m,
      backgroundColor: colors.aliceBlue1,
    },
    sampleMainSubView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.m,
      alignItems: 'center',
    },
    excludeView: {width: '20%', height: Sizes.x, marginLeft: Sizes.xms},
    productSubView: {paddingVertical: Sizes.m, paddingHorizontal: Sizes.xms},
    linkSubView: {
      paddingTop: Sizes.m,
      paddingHorizontal: Sizes.m,
    },
    arrowRightView: {
      flexDirection: 'row',
      alignSelf: 'flex-end',
      alignItems: 'center',
    },
    videoDataView: {
      paddingHorizontal: Sizes.m,
      flex: Sizes.x,
    },
    praiseView: {
      paddingHorizontal: Sizes.l,
      backgroundColor: colors.whiteColor,
      width: '100%',
    },
    praiseSubView: {width: '60%', alignSelf: 'center'},
    drPhotoView: {
      borderRadius: Sizes.xms,
      width: '100%',
    },
    drPhotoSubView: {
      width: '100%',
      height: Sizes.ex3l,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    drPhotoMainView: {
      backgroundColor: colors.silkBlue2,
      padding: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
    },
    madeMainView: {
      padding: Sizes.xxl,
      paddingHorizontal: Sizes.x8l,
      width: '100%',
      borderBottomLeftRadius: Sizes.xms,
      borderBottomRightRadius: Sizes.xms,
      shadowColor: colors.blackColor,
      shadowOffset: {
        width: 0,
        height: Sizes.x,
      },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
      backgroundColor: colors.whiteColor,
      elevation: Sizes.xs,
    },
    madeSubView: {flexDirection: 'row'},
    lookingView: {
      width: Sizes.screenWidth * 0.7,
      alignSelf: 'center',
      alignItems: 'center',
    },
    lookingSubView: {flexDirection: 'row', alignItems: 'center'},
    categoryNameView: {width: Sizes.x9l + Sizes.xs},
    imageSubView: {
      marginLeft: Sizes.xms,
    },
    listDataSubView: {
      flex: Sizes.x,
      paddingVertical: 0,
      paddingHorizontal: Sizes.m,
    },
    viewList: {
      marginLeft: Sizes.mx,
      marginBottom: Sizes.m,
    },
    searchedProduct: {
      paddingHorizontal: Sizes.m,
    },
    sliderItem: {
      height: isTablet ? Sizes.ex210 + Sizes.x6l : Sizes.ex2l,
    },
    sliderSubItemView: {
      height: checkDevice() ? Sizes.ex170 + Sizes.xm : Sizes.exl + Sizes.l,
    },
    threeBanner: {
      height: Platform.OS === 'ios' ? Sizes.ex180 : Sizes.ex170,
    },
    sliderSpace: {
      marginTop: -Sizes.mx,
      marginBottom: -Sizes.s,
    },
    praiseSliderItem: {
      height: 680,
    },
    featuredImageView: {
      flex: Sizes.x,
    },
    carouselCardItemStyle: {
      paddingHorizontal: Sizes.l,
    },
    topIconView: {
      position: 'absolute',
      height: Sizes.xxxl,
      borderRadius: Sizes.x3l,
      backgroundColor: colors.black2,
      paddingHorizontal: Sizes.xms,
      elevation: Sizes.s,
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
      flex: Sizes.x,
      alignSelf: 'center',
      flexDirection: 'row',
    },
    goTop: {
      marginBottom: -Sizes.xs,
    },
    listContainer: {
      flex: Sizes.x,
    },
    topIconSunView: {
      padding: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
    },
    excludeSubView: {
      justifyContent: 'center',
      flexDirection: 'row',
    },
    excludeMainView: {
      transform: [{rotate: '180deg'}],
    },
    emptyImage: {
      height: Sizes.xx,
      width: Sizes.xsl,
      opacity: 0.45,
    },
    searchInputStyle: {
      paddingLeft: Sizes.xm,
    },
    searchStyle: {
      borderColor: colors.text,
    },
    fOne: {
      flex: Sizes.x,
    },
    image: {
      width: '98%',
      height: '100%',
      borderRadius: Sizes.mx,
    },
  });
export default styles;

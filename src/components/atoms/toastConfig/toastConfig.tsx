import {useMemo} from 'react';
import CustomToast from '../customToast';

const useToastConfig = ( visibilityTime = 8000) => {
  // Define text props for toast components
  const toastProps = useMemo(
    () => ({
      text1Props: {
        numberOfLines: 4,
      },
      text2Props: {
        numberOfLines: 4,
      },
    }),
    [],
  );

  // Memoize the entire toast configuration
  const toastConfig = useMemo(
    () => ({
      warn: props => <CustomToast {...props} {...toastProps} />,
      cart: props => <CustomToast {...props} {...toastProps} />,
      wishList: props => <CustomToast {...props} {...toastProps} />,
      success: props => <CustomToast {...props} {...toastProps} />,
      error: props => <CustomToast {...props} {...toastProps} />,
      info: props => <CustomToast {...props} {...toastProps} />,
      visibilityTime,
    }),
    [CustomToast, toastProps, visibilityTime],
  );

  return {toastConfig};
};

export default useToastConfig;

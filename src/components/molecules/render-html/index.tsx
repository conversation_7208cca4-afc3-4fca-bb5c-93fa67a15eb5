import React, {useMemo} from 'react';
import {useWindowDimensions} from 'react-native';
import RenderHtml from 'react-native-render-html';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';

type Props = {
  html: string;
  tagsStyles?: any;
  renderersProps?: any;
  contentWidth?: number;
};

const RenderCustomHTML = ({
  html,
  tagsStyles,
  renderersProps,
  contentWidth,
}: Props) => {
  const {colors} = useTheme();
  const {width} = useWindowDimensions();

  // Memoize styles
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  // Memoize RenderHtml props
  const htmlProps = useMemo(
    () => ({
      contentWidth: contentWidth || width,
      source: {html},
      tagsStyles,
      renderersProps,
      baseFontStyle: styles.descText,
      allowFontScaling: false,
    }),
    [contentWidth, width, html, tagsStyles, renderersProps, styles.descText],
  );

  return <RenderHtml {...htmlProps} />;
};

const propsAreEqual = (prevProps: Props, nextProps: Props) => {
  return (
    prevProps.html === nextProps.html &&
    prevProps.contentWidth === nextProps.contentWidth &&
    prevProps.tagsStyles === nextProps.tagsStyles &&
    prevProps.renderersProps === nextProps.renderersProps
  );
};

export default React.memo(RenderCustomHTML, propsAreEqual);

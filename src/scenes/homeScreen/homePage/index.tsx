import React, {
  useCallback,
  useEffect,
  useState,
  useRef,
  useMemo,
  useContext,
} from 'react';
import stylesWithOutColor from './style';
import base64 from 'react-native-base64';
import {
  showErrorMessage,
  showInfoMessage,
  showSuccessMessage,
} from 'utils/show_messages';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {
  setIsLoggedIn,
  setLoading,
  setLogout,
  initializeAppData,
} from 'app-redux-store/slice/appSlice';
import {
  Button,
  Header,
  SuggestProduct,
  LogoutUserModal,
  AccountModal,
} from 'components/molecules';
import {
  ImageIcon,
  Label,
  ProductCardVertical,
  ReanimatedCarousel,
  SearchInput,
  Spacer,
  CategoriesItem,
  TopBrands,
  WishlistButton,
  TypingEffect,
  HorizontalScrollBar,
  SuccessModal,
  WebViewModal,
  FlatListCarousel,
  RecentlyViewedItem,
  HotSellingSection,
  MostSearchedSection,
  StandardCarouselSectionWithTag,
  TopCategoriesSection,
} from 'components/atoms';
import {
  Animated,
  FlatList,
  Linking,
  Platform,
  RefreshControl,
  TouchableOpacity,
  View,
  Dimensions,
} from 'react-native';
import {t} from 'i18next';
import {AnalyticsEvents} from 'components/organisms';
import Icons from 'common/icons';
import {Marquee} from '@animatereactnative/marquee';
import {Sizes} from 'common';
import {getUserInfo, getRewardCoins} from 'app-redux-store/slice/appSlice';
import {
  addProductSuggestion,
  homePageSections,
  recentlyViewedSection,
} from 'services/home';
import {HomeLoader} from 'skeletonLoader';
import LinearGradient from 'react-native-linear-gradient';
import FastImage from 'react-native-fast-image';
import crashlytics from '@react-native-firebase/crashlytics';
import {
  checkDevice,
  getWebLink,
  openInBrowser,
  orderHomeData,
} from 'utils/utils';
import ReactMoE, {MoEAppStatus} from 'react-native-moengage';
import Carousel from 'react-native-reanimated-carousel';
import {WEBSITE_URL} from 'config/environment';
import ErrorBoundary from 'react-native-error-boundary';
import ErrorHandler from 'utils/ErrorHandler';
import {ScrollContext} from './contextApi/scrollContext';
import {myMemberShipsData} from 'services/mambership';
import {
  setCurrentScreenName,
  setCustomUserId,
} from '@microsoft/react-native-clarity';
import FlastlistCarousel from 'components/atoms/flastlistCarousel';
import {debugLog} from 'utils/debugLog';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import tokenClass from 'utils/token';

const HomePageScene = React.memo(({navigation}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const dispatch = useDispatch();
  const [isReset, setIsReset] = useState(false);
  const [modelVisible, setModelVisible] = useState(false);
  const [accountVisible, setAccountVisible] = useState(false);
  const [sections, setSections] = useState<Section[]>([]);
  const [recentlyViewData, setRecentlyViewData] = useState<Section[]>([]);
  const [loader, setLoader] = useState(true);
  const [logoutModal, setLogoutModal] = useState(false);
  const [scrollBarWidth1, setScrollBarWidth1] = useState(0);
  const [listWidth1, setListWidth1] = useState(0);
  const [scrollBarWidth2, setScrollBarWidth2] = useState(0);
  const [listWidth2, setListWidth2] = useState(0);
  const [successModel, setSuccessModel] = useState(false);
  const {width: screenWidth} = Dimensions.get('window');
  const [itemHeights, setItemHeights] = useState([]);
  const [maxHeight, setMaxHeight] = useState(0);
  const flatListRef = useRef(null);
  const recentFlatListRef = useRef(null);
  const scrollX1 = useRef(new Animated.Value(0)).current;
  const scrollX2 = useRef(new Animated.Value(0)).current;
  const [scrollX, setScrollX] = useState(0);
  const [scrollFeaturedX, setScrollFeaturedX] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);
  const [featuredWidth, setFeaturedWidth] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const [featuredContainerWidth, setFeaturedContainerWidth] = useState(0);
  const [webViewModel, setWebViewModel] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activePlan, setActivePlan] = useState<boolean>(false);
  const [webLink, setWebLink] = useState('');
  const [isScrolling, setIsScrolling] = useState(false);
  const ITEM_WIDTH = useMemo(() => screenWidth * 0.48, [screenWidth]);
  const ITEMS_TO_SCROLL = useMemo(() => 2, []);
  const TAG = useMemo(() => 'HOMESCREEN', []);
  const prevUserInfoRef = useRef(null);
  const previousOffsetY = useRef(0);
  // const {
  //   flatListRefTop,
  //   scrollToTop: showScrollToTopButton,
  //   showScrollToTopButton: showToTop,
  //   handleScroll,
  // } = useContext(ScrollContext);
  const insets = useSafeAreaInsets();
  const flatListRefTop = useRef(null);
  const [showScrollToTopButton, setShowScrollToTopButton] = useState(false);

  const getAllSections = useCallback(async () => {
    try {
      setLoader(true);
      const [firstRes, secondRes] = await Promise.all([
        homePageSections(null, 1),
        homePageSections(null, 2),
      ]);
      const data = firstRes?.data;
      const status = firstRes?.status;
      const moreData = secondRes?.data;
      const moreStatus = secondRes?.status;

      if (data?.sections && status) {
        const combinedSections =
          moreData?.sections && moreStatus
            ? [...data.sections, ...moreData.sections]
            : data.sections;

        const orderedData = orderHomeData(combinedSections);
        setSections(orderedData);
        setLoader(false);
      }
    } catch (error) {
      showErrorMessage(error);
    } finally {
      setLoader(false);
      // await dispatch(initializeAppData());
      myMembershipApis();
    }
  }, []);

  const updateRecentlyViewed = useCallback(async () => {
    try {
      const loginStatus = await tokenClass.loginStatus();
      if(loginStatus){
        const {data, status} = await recentlyViewedSection('recently_viewed');
  
        if (data?.sections && status) {
          setRecentlyViewData(data.sections[0]?.elements);
        }
      }
    } catch (error) {
      showErrorMessage(error);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      // scrollToTop();
      if (userInfo?.id) {
        updateRecentlyViewed();
        const pageData = {
          pageName: 'HomePage',
        };
        AnalyticsEvents(
          'VIEW_PAGE',
          'Home Page viewed',
          pageData,
          userInfo,
          isLoggedIn,
        );
      }
    }, [userInfo?.id]),
  );

  useEffect(() => {
    if (isLoggedIn) {
      setShowScrollToTopButton(false);
    }
  }, [isLoggedIn]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getAllSections().finally(() => {
      setTimeout(() => {
        dispatch(initializeAppData());
        myMembershipApis();
      }, 0);
    });
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  }, []);

  const myMembershipApis = useCallback(async () => {
    const {data, status} = await myMemberShipsData();
    if (status) {
      if (data?.memberships?.length > 0) {
        const activePlanList = data?.memberships?.filter(
          (item: MembershipModel) => item.is_active,
        );
        setActivePlan(activePlanList?.length > 0 ? true : false);
      } else {
        setActivePlan(false);
      }
    }
  }, []);

  const handleCloseModal = useCallback(() => {
    setModelVisible(false);
  }, []);

  const onSubmitProductSuggestion = useCallback(
    async (formData: {
      productName: string;
      brandName?: string;
      comment?: string;
      url?: string;
      email?: string;
    }) => {
      if (formData?.productName) {
        try {
          dispatch(setLoading(true));
          const {data, status} = await addProductSuggestion({
            searched_key: '',
            product_name: formData?.productName,
            brand: formData?.brandName,
            comment: formData?.comment,
            url: formData?.url,
            user: !!formData?.email
              ? formData?.email
              : isLoggedIn
              ? userInfo?.email
              : 'guest_user',
          });

          dispatch(setLoading(false));
          if (status) {
            setIsReset(true);
            handleCloseModal();
            setSuccessModel(true);
            setTimeout(() => {
              setSuccessModel(false);
            }, 3500);
          }
        } catch (err) {
          debugLog(err);
          dispatch(setLoading(false));
        }
      } else {
        showErrorMessage(t('searchProduct.errorProduct'));
      }
    },
    [handleCloseModal, isLoggedIn, userInfo?.email],
  );

  const navigateToScreen = useCallback(
    async (item: screenProps) => {
      if (!isLoggedIn) {
        showInfoMessage(t('toastMassages.loginMessage'));
      }
      navigation.navigate(item?.screenName, {
        ...item.screenParams,
        goBack: () => setAccountVisible(true),
      });
      setAccountVisible(false);
    },
    [isLoggedIn, navigation],
  );

  const userLogout = useCallback(() => {
    dispatch(setLogout('Login'));
    dispatch(setIsLoggedIn(false));
    // updateRecentlyViewed();
    setLogoutModal(false);
    showSuccessMessage(t('toastMassages.logOut'));
    ReactMoE.logout();
  }, []);

  const logout = useCallback(() => {
    setLogoutModal(true);
    setAccountVisible(false);
  }, []);

  const EMPTY_FREE_PRODUCTS = useMemo(() => [], []);

  const handleScroll = useCallback(event => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setShowScrollToTopButton(
      offsetY > 0 && offsetY < previousOffsetY.current ? true : false,
    );
    setTimeout(() => {
      previousOffsetY.current = offsetY;
    }, 300);
  }, []);

  const handleScroll1 = useMemo(
    () =>
      Animated.event([{nativeEvent: {contentOffset: {x: scrollX1}}}], {
        useNativeDriver: false,
        listener: event => {
          const offsetX = event.nativeEvent.contentOffset.x;
          setScrollX(offsetX);
        },
      }),
    [scrollX1],
  );

  const handleScroll2 = useMemo(
    () =>
      Animated.event([{nativeEvent: {contentOffset: {x: scrollX2}}}], {
        useNativeDriver: false,
        listener: event => {
          const offsetX = event.nativeEvent.contentOffset.x;
          setScrollFeaturedX(offsetX);
        },
      }),
    [scrollX2],
  );

  const handleContentSizeChange = useCallback(
    (contentWidth: number) => {
      setContentWidth(contentWidth);
      handleContentSizeChange1(contentWidth);
    },
    [handleContentSizeChange1],
  );

  const handleContentSizeChange1 = useCallback(
    (contentWidth: number) => {
      setListWidth1(contentWidth);
      if (screenWidth > 0 && contentWidth > screenWidth) {
        const calculatedScrollBarWidth = Math.floor(
          (screenWidth / contentWidth) * (screenWidth / 4),
        );
        setScrollBarWidth1(Math.max(calculatedScrollBarWidth, 20));
      }
    },
    [screenWidth],
  );

  const handleContentSizeChange2 = useCallback(
    (contentWidth: number) => {
      setListWidth2(contentWidth);
      if (screenWidth > 0 && contentWidth > screenWidth) {
        const calculatedScrollBarWidth = Math.floor(
          (screenWidth / contentWidth) * (screenWidth / 4),
        );
        setScrollBarWidth2(Math.max(calculatedScrollBarWidth, 20));
      }
    },
    [screenWidth],
  );

  const scrollBarPosition1 = scrollX1.interpolate({
    inputRange: [0, Math.max(0, listWidth1 - screenWidth)],
    outputRange: [0, Math.max(0, screenWidth / 6 - scrollBarWidth1)],
    extrapolate: 'clamp',
  });

  const scrollBarPosition2 = scrollX2.interpolate({
    inputRange: [0, Math.max(0, listWidth2 - screenWidth)],
    outputRange: [
      0,
      Math.max(0, Math.floor(screenWidth / 6) - scrollBarWidth2),
    ],
    extrapolate: 'clamp',
  });

  const scrollToTop = useCallback(() => {
    if (flatListRefTop.current) {
      flatListRefTop.current.scrollTo({y: 0, animated: true});
    }
  }, []);

  const scrollToEnd = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollFeaturedX + ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [scrollFeaturedX, ITEM_WIDTH, ITEMS_TO_SCROLL]);

  const scrollToStart = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollFeaturedX - ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [scrollFeaturedX, ITEM_WIDTH, ITEMS_TO_SCROLL]);

  const recentScrollToEnd = useCallback(() => {
    if (recentFlatListRef.current) {
      recentFlatListRef.current.scrollToOffset({
        offset: scrollX + ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [scrollX, ITEM_WIDTH, ITEMS_TO_SCROLL]);

  const recentScrollToStart = useCallback(() => {
    if (recentFlatListRef.current) {
      recentFlatListRef.current?.scrollToOffset({
        offset: scrollX - ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [scrollX, ITEM_WIDTH, ITEMS_TO_SCROLL]);

  const setCrashlayticsAttributes = useCallback(async () => {
    await Promise.all([crashlytics().setUserId('' + userInfo?.id)]);
  }, [userInfo?.id]);

  useEffect(() => {
    getAllSections().finally(() => {
      setTimeout(() => {
        dispatch(initializeAppData());
        // myMembershipApis();
      }, 0);
    });
    setCurrentScreenName('Home');
    return () => {
      setCurrentScreenName('Clarity event');
    };
  }, []);

  useEffect(() => {
    if (userInfo?.id) {
      dispatch(getUserInfo());
      dispatch(getRewardCoins());
      setCrashlayticsAttributes();
      getAllSections().finally(() => {
        setTimeout(() => {
          dispatch(initializeAppData());
          myMembershipApis();
        }, 0);
      });
    }
  }, [userInfo?.id]);

  useEffect(() => {
    if (userInfo) {
      // Check if we need to update ReactMoE
      const shouldUpdate =
        !prevUserInfoRef.current ||
        prevUserInfoRef.current.id !== userInfo.id ||
        prevUserInfoRef.current.email !== userInfo.email ||
        prevUserInfoRef.current.mobile !== userInfo.mobile;

      if (shouldUpdate) {
        // Only update ReactMoE when user info actually changes
        let eventID;
        if (userInfo?.email) {
          eventID = base64.encode(userInfo.email.toString());
        } else {
          eventID = base64.encode(
            userInfo.mobile?.toString() + '@dentalkart.user',
          );
        }
        ReactMoE.setUserUniqueID(eventID);
        setCustomUserId(eventID)
          .then(() => {
            debugLog('Clarity User ID set', eventID);
          })
          .catch(e => {
            debugLog('Clarity User ID error', e);
          });
        ReactMoE.setUserEmailID(userInfo.email?.toString());
        ReactMoE.setUserContactNumber(userInfo.mobile?.toString());
        ReactMoE.setUserFirstName(userInfo.firstname?.toString());
        ReactMoE.setUserLastName(userInfo.lastname?.toString());
        ReactMoE.setAppStatus(MoEAppStatus.Install);

        // Save current userInfo to compare later
        prevUserInfoRef.current = {...userInfo};
      }
    }
  }, [userInfo]);

  const navigateTo = useCallback(
    async (value: string, productId?: number, categoryId?: number) => {
      if (value?.includes('#')) {
        return;
      } else if (value?.includes('shorts')) {
        navigation.navigate('Shorts');
      } else if (value?.includes('sale')) {
        const saleId = value?.split('/').pop();
        if (saleId) {
          navigation.navigate('Sales', {saleId});
        }
      } else if (productId) {
        navigation.navigate('ProductDetail', {productId});
      } else if (categoryId) {
        navigation.navigate('CategoryDetail', {categoryId});
      } else if (value) {
        const link = getWebLink(value);
        openInBrowser(link);
        // return Linking.openURL(link ?? '#');
      } else {
        switch (value) {
          case 'account/reward-zone':
            navigation.navigate('RewardZoneScene');
            break;
          case 'membership':
            navigation.navigate(isLoggedIn ? 'MembershipPage' : 'Login');
            break;
          case 'sell-on-dentalkart':
            navigation.navigate('SellOnDentalkart');
            break;
          default:
            navigation.navigate('UrlResolver', {urlKey: value});
            break;
        }
      }
    },
    [navigation, isLoggedIn, setWebLink, setWebViewModel],
  );

  const carouselStyles = useMemo(
    () => ({
      mainSlider: styles.sliderItem,
      threeBanner: styles.threeBanner,
      cardItem: styles.carouselCardItemStyle,
      subItem: styles.sliderSubItemView,
      sliderSpace: styles.sliderSpace,
      threeBannerWithSpace: [styles.threeBanner, styles.sliderSpace],
      mainSliderWithSpace: [styles.sliderItem, styles.sliderSpace],
    }),
    [styles],
  );

  const mainCarouselProps = useMemo(
    () => ({
      pagination: true,
      loop: true,
      autoPlay: true,
      mode: 'parallax',
      autoplayInterval: 3000,
      paginationType: 'capsule',
      paginationSpacer: true,
      urlResolverKey: 'url',
      snapEnabled: false,
      pagingEnabled: false,
    }),
    [],
  );

  const standardCarouselProps = useMemo(
    () => ({
      loop: true,
      autoPlay: true,
      autoplayInterval: 3000,
      urlResolverKey: 'url',
    }),
    [],
  );

  const bannerAdsClick = useCallback(
    item => {
      // const updatedItem = {...item, PageName: 'Home page'};
      // debugLog(updatedItem, 'updatedItem***');
      // AnalyticsEvents(
      //   'BANNER_CLICKED',
      //   'Banner Clicked',
      //   updatedItem,
      //   userInfo,
      //   isLoggedIn,
      // );
      const {url, product_id, category_id} = item?.landing_page_entity || {};
      if (url || product_id || category_id) {
        navigateTo(url || '', product_id, category_id);
      }
    },
    [navigateTo],
  );

  const handlePress = item => {
    const updatedItem = {
      ...item,
      PageName: 'Home page',
      Section: 'Slider Layout',
    };
    AnalyticsEvents(
      'BANNER_CLICKED',
      'Banner Clicked',
      updatedItem,
      userInfo,
      isLoggedIn,
    );
    bannerAdsClick(item);
  };
  const renderCarouselItem = useCallback(
    ({item}: {item: SectionElement}) => (
      <TouchableOpacity onPress={() => handlePress(item)} activeOpacity={0.9}>
        <FastImage
          source={{uri: item.media?.mobile_image}}
          style={styles.image}
          resizeMode="stretch"
        />
      </TouchableOpacity>
    ),
    [bannerAdsClick, styles.image],
  );

  const renderRecentlyViewedItem = useCallback(
    ({item, index}) => (
      <RecentlyViewedItem
        item={item}
        index={index}
        TAG={TAG}
        navigation={navigation}
        EMPTY_FREE_PRODUCTS={EMPTY_FREE_PRODUCTS}
      />
    ),
    [TAG, navigation, EMPTY_FREE_PRODUCTS],
  );

  const handleUserIconPress = useCallback(() => {
    isLoggedIn ? setAccountVisible(true) : navigation.navigate('Login');
  }, [isLoggedIn, navigation]);

  useEffect(() => {
    debugLog(
      '🔄 HomePageScene rendered. Current sections length:',
      sections.length,
    );
  }, [sections.length]);

  useEffect(() => {
    debugLog(
      '📝 Sections updated:',
      sections.map(s => s?.layout_type?.code).join(', '),
    );
  }, [sections]);

  const renderFeaturedItem = useCallback(
    ({item, index: inx6}) => (
      <ErrorHandler
        componentName={`${TAG} ProductCardVertical`}
        onErrorComponent={<View />}>
        <ProductCardVertical
          index={inx6}
          actionBtn={item?.action_btn}
          skuId={item?.sku}
          size="small"
          imageWithBorder={true}
          maxWidth={0.45}
          item={item}
          inStock={item.is_in_stock}
          productType={item?.type}
          maxSaleQty={item?.max_sale_qty}
          demoAvailable={item?.demo_available}
          msrp={item?.msrp}
          isBestSeller={item?.is_best_seller}
          image={item?.media?.mobile_image}
          name={item?.name}
          rewardPoint={item?.reward_points}
          description={item?.short_description}
          rating={(item?.rating === 'null' || item.average_rating === null
            ? 0
            : Number(item?.rating) || Number(item?.average_rating)
          ).toFixed(1)}
          ratingCount={item?.rating_count ? `(${item?.rating_count})` : '(0)'}
          price={item?.price}
          sellingPrice={item?.selling_price}
          currencySymbol={item?.currency_symbol}
          discount={item?.discount?.label}
          navigation={navigation}
          freeProducts={[]}
        />
      </ErrorHandler>
    ),
    [navigation],
  );

  const renderWaldentItem = useCallback(
    ({item, index: inx2}) => {
      const waldentStyle =
        checkDevice() && inx2 === 0 ? {paddingRight: 10} : {};
      return (
        <View key={inx2} style={waldentStyle}>
          <ErrorHandler
            componentName={`${TAG} ProductCardVertical`}
            onErrorComponent={<View />}>
            <ProductCardVertical
              index={inx2}
              actionBtn={item?.action_btn}
              skuId={item?.sku}
              size="medium"
              imageWithBorder={true}
              maxWidth={checkDevice() ? 0.24 : 0.48}
              item={item}
              inStock={item.is_in_stock}
              productType={item?.type}
              maxSaleQty={item?.max_sale_qty}
              demoAvailable={item?.demo_available}
              msrp={item?.msrp}
              isBestSeller={item?.is_best_seller}
              image={item?.media?.mobile_image}
              name={item?.name}
              rewardPoint={item?.reward_points}
              description={item?.short_description}
              rating={(item?.rating === 'null' || item.average_rating === null
                ? 0
                : Number(item?.rating) || Number(item?.average_rating)
              ).toFixed(1)}
              ratingCount={
                !!item?.rating_count ? `(${item?.rating_count})` : '(0)'
              }
              price={item?.price}
              sellingPrice={item?.selling_price}
              currencySymbol={item?.currency_symbol}
              discount={item?.discount?.label}
              navigation={navigation}
              freeProducts={[]}
              waldent={true}
              showWishlist={true}
            />
          </ErrorHandler>
        </View>
      );
    },
    [TAG, navigation],
  );

  const keyExtractor = useCallback((_, index) => index.toString(), []);

  const itemSeparator = useMemo(() => <Spacer size="xm" />, []);

  const recentViewedColors = useMemo(
    () => [colors.recentViewedBlue, colors.recentViewedBlue24],
    [colors],
  );

  const onLayoutRecentViewed = useCallback(
    event => {
      setContainerWidth(event.nativeEvent.layout.width);
    },
    [setContainerWidth],
  );

  const sectionDataMap = useMemo(() => {
    return sections.reduce((acc, section) => {
      if (section?.layout_type?.code === 'top_categories') {
        acc[section.id] = ['All', ...(section?.elements ?? [])];
      }
      return acc;
    }, {});
  }, [sections]);

  const handleContentSizeChangeCallback = useCallback(
    (contentWidth: number) => {
      setFeaturedWidth(contentWidth);
      handleContentSizeChange2(contentWidth);
    },
    [handleContentSizeChange2],
  );

  const renderSectionItem = useCallback(
    ({item: section, index}: {item: Section; index: number}) => {
      return (
        <View key={index}>
          {section?.layout_type?.code === 'notice' ? (
            <>
              <View style={styles.marqueeView}>
                <Marquee spacing={Sizes.xms} speed={0.5} loop>
                  <Label
                    size="mx"
                    lineHeight="l"
                    color="marqueeColor"
                    text={
                      sections.find(
                        section => section?.layout_type?.code === 'notice',
                      )?.description
                    }
                  />
                </Marquee>
              </View>
            </>
          ) : section?.layout_type?.code === 'top_categories' ? (
            <TopCategoriesSection
              sectionData={sectionDataMap[section.id] ?? []}
              navigation={navigation}
              filter={false}
            />
          ) : section?.layout_type?.code === 'main_slider' ? (
            <>
              {section?.elements.length > 0 ? (
                <>
                  <ErrorHandler
                    componentName={`${TAG} ReanimatedCarousel`}
                    onErrorComponent={<View />}>
                    <ReanimatedCarousel
                      {...mainCarouselProps}
                      data={section?.elements}
                      carouselStyle={carouselStyles.mainSliderWithSpace}
                      carouselCardItemStyle={carouselStyles.mainSlider}
                      bannerImgStyle={carouselStyles.mainSlider}
                      onCardPress={bannerAdsClick}
                      screenName={'Home Page'}
                      sectionType={section?.title}
                      paginationSize={true}
                    />
                  </ErrorHandler>
                  <Spacer size="mx" />
                </>
              ) : (
                <View />
              )}
            </>
          ) : section?.layout_type?.code === 'top_brands' ? (
            <ErrorHandler
              componentName={`${TAG} TopBrands`}
              onErrorComponent={<View />}>
              <TopBrands section={section} />
            </ErrorHandler>
          ) : isLoggedIn &&
            section?.layout_type?.code === 'recently_vw_carousel' &&
            recentlyViewData?.length > 0 ? (
            <View onLayout={onLayoutRecentViewed}>
              <LinearGradient
                colors={recentViewedColors}
                start={{x: 0, y: 0}}
                end={{x: 0, y: 1}}
                style={styles.recentlyView}>
                <View style={styles.recentlySubView}>
                  <Label
                    text={section?.title}
                    size={checkDevice() ? 'xxl' : 'l'}
                    fontFamily="Medium"
                    color="categoryTitle"
                    textTransform="capitalize"
                    weight="600"
                  />
                  <View style={styles.excludeSubView}>
                    {scrollX > 0 && (
                      <TouchableOpacity onPress={recentScrollToStart}>
                        <ImageIcon icon="Include" size="xxl" />
                      </TouchableOpacity>
                    )}
                    <Spacer size="xms" />
                    {containerWidth + scrollX <= contentWidth && (
                      <TouchableOpacity onPress={recentScrollToEnd}>
                        <ImageIcon icon="Exclude" size="xxl" />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>

                <View style={styles.waldentView}>
                  <FlatList
                    ref={recentFlatListRef}
                    horizontal
                    data={recentlyViewData}
                    style={styles.productView}
                    showsHorizontalScrollIndicator={false}
                    keyExtractor={keyExtractor}
                    renderItem={renderRecentlyViewedItem}
                    onScroll={handleScroll1}
                    scrollEventThrottle={16}
                    onContentSizeChange={handleContentSizeChange}
                    removeClippedSubviews={true}
                    windowSize={5}
                    maxToRenderPerBatch={5}
                    updateCellsBatchingPeriod={50}
                    initialNumToRender={3}
                    showsVerticalScrollIndicator={false}
                  />
                </View>

                <Spacer size="l" />
                {scrollBarWidth1 > 0 && (
                  <ErrorHandler
                    componentName={`${TAG} HorizontalScrollBar`}
                    onErrorComponent={<View />}>
                    <HorizontalScrollBar
                      activeColor={colors.blue3}
                      scrollBarWidth={scrollBarWidth1}
                      scrollBarPosition={scrollBarPosition1}
                    />
                  </ErrorHandler>
                )}
              </LinearGradient>
            </View>
          ) : section?.layout_type?.code === 'featured_carousal' ? (
            <View
              onLayout={event =>
                setFeaturedContainerWidth(event.nativeEvent.layout.width)
              }>
              <LinearGradient
                colors={[colors.recentViewedBlue, colors.recentViewedBlue24]}
                start={{x: 0, y: 0}}
                end={{x: 0, y: 1}}
                style={styles.recentlyView}>
                <View style={styles.recentlySubView}>
                  <Label
                    text={section?.title}
                    size="l"
                    fontFamily="SemiBold"
                    weight="600"
                    textTransform="capitalize"
                    color="blueLine"
                    style={styles.fOne}
                  />
                  <LinearGradient
                    colors={[colors.danube100, colors.danube0]}
                    start={{x: 0, y: 0}}
                    end={{x: 1, y: 1}}
                    style={styles.recentlyLine}
                  />
                  <Spacer size="xms" />
                  <View style={styles.excludeSubView}>
                    {scrollFeaturedX > 0 && (
                      <TouchableOpacity onPress={scrollToStart}>
                        <ImageIcon icon="Include" size="x4l" />
                      </TouchableOpacity>
                    )}
                    <Spacer size="xms" />
                    {featuredContainerWidth + scrollFeaturedX <=
                      featuredWidth && (
                      <TouchableOpacity onPress={scrollToEnd}>
                        <ImageIcon icon="Exclude" size="x4l" />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
                <View style={styles.waldentView}>
                  <FlatList
                    ref={flatListRef}
                    horizontal
                    data={section?.elements}
                    style={styles.productView}
                    keyExtractor={keyExtractor}
                    renderItem={renderWaldentItem}
                    showsHorizontalScrollIndicator={false}
                    onScroll={handleScroll2}
                    scrollEventThrottle={16}
                    onContentSizeChange={handleContentSizeChangeCallback}
                    removeClippedSubviews={true}
                    windowSize={5}
                    maxToRenderPerBatch={5}
                    updateCellsBatchingPeriod={50}
                    initialNumToRender={6}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
                <Spacer size="l" />
                {scrollBarWidth2 > 0 && (
                  <ErrorHandler
                    componentName={`${TAG} HorizontalScrollBar`}
                    onErrorComponent={<View />}>
                    <HorizontalScrollBar
                      activeColor={colors.blue5}
                      scrollBarWidth={scrollBarWidth2}
                      scrollBarPosition={scrollBarPosition2}
                    />
                  </ErrorHandler>
                )}
              </LinearGradient>
            </View>
          ) : section?.layout_type?.code === 'standard_carousel' &&
            section?.header_type === 'hot_selling' ? (
            <HotSellingSection
              section={section}
              userInfo={userInfo}
              isLoggedIn={isLoggedIn}
              navigation={navigation}
            />
          ) : section?.layout_type?.code === 'slider_layout' ? (
            <>
              <FlastlistCarousel
                {...standardCarouselProps}
                data={section.elements}
                loop
                autoPlay
                carouselCardItemStyle={carouselStyles.cardItem}
                carouselStyle={carouselStyles.subItem}
                bannerImgStyle={carouselStyles.subItem}
                onCardPress={item => handlePress(item)}
                screenName={'Home Page'}
                sectionType={section?.title}
              />
              <Spacer size="m" />
            </>
          ) : section?.layout_type?.code === 'three_banner_linear_layout' ? (
            <>
              <ErrorHandler
                componentName={`${TAG} ReanimatedCarousel`}
                onErrorComponent={<View />}>
                <FlastlistCarousel
                  {...standardCarouselProps}
                  data={Platform.OS ==='ios' ?section.elements.filter((ele) => ele.title !== "Shorts Dentalkart"): section.elements}
                  loop
                  autoPlay
                  carouselCardItemStyle={carouselStyles.cardItem}
                  carouselStyle={carouselStyles.subItem}
                  bannerImgStyle={carouselStyles.subItem}
                  onCardPress={bannerAdsClick}
                  screenName={'Home Page'}
                  sectionType={section?.title}
                />
              </ErrorHandler>
              <Spacer size="m" />
            </>
          ) : section.layout_type?.code === 'standard_carousel' &&
            section?.header_type === 'most_search' ? (
            <>
              <MostSearchedSection
                section={section}
                userInfo={userInfo}
                isLoggedIn={isLoggedIn}
                navigation={navigation}
              />
            </>
          ) : (section.layout_type?.code === 'standard_carousel' &&
              section?.header_type === 'tag') ||
            (section.layout_type?.code === 'standard_carousel' &&
              section?.landing_url !== null) ? (
            <StandardCarouselSectionWithTag
              section={section}
              userInfo={userInfo}
              isLoggedIn={isLoggedIn}
              navigation={navigation}
              bannerImage={Icons.endokingBanner}
              icon="endokingRightArrowIcon"
              defaultUrl="/endodontics.html"
            />
          ) : section.layout_type?.code === 'three_banner_grid_layout' ? (
            <>
              <ErrorHandler
                componentName={`${TAG} ReanimatedCarousel`}
                onErrorComponent={<View />}>
                <FlastlistCarousel
                  {...standardCarouselProps}
                  data={section?.elements}
                  loop
                  autoPlay
                  carouselCardItemStyle={carouselStyles.cardItem}
                  carouselStyle={carouselStyles.subItem}
                  bannerImgStyle={carouselStyles.subItem}
                  onCardPress={bannerAdsClick}
                  screenName={'Home Page'}
                  sectionType={section?.title}
                />
              </ErrorHandler>
              <Spacer size="m" />
            </>
          ) : section.layout_type?.code === 'featured_carousal' ? (
            <>
              <View style={styles.offerZoneBannerView}>
                <View style={styles.offerBannerView}>
                  <View style={styles.offerBannerSubView}>
                    <ImageIcon
                      icon="offerZoneBanner"
                      style={styles.offerBannerImageView}
                    />
                    <ImageIcon icon="dis" size="xxl" />
                  </View>
                  <ImageIcon icon="Exclude" size="xxl" />
                </View>
                <Label
                  style={styles.scissorsView}
                  size="l"
                  weight="500"
                  color="orangeOffer"
                  text={t('homePage.scissors')}
                />

                <FlatList
                  horizontal
                  data={section?.elements}
                  style={styles.productView}
                  keyExtractor={keyExtractor}
                  renderItem={renderFeaturedItem}
                  removeClippedSubviews={true}
                  windowSize={5}
                  maxToRenderPerBatch={5}
                  updateCellsBatchingPeriod={50}
                />

                <TouchableOpacity style={styles.tryOffersView}>
                  <Label
                    size="xx"
                    weight="500"
                    text={t('homePage.tryOffers')}
                    color="yellowOffer"
                  />
                  <ImageIcon
                    size="xxl"
                    icon="arrowRight"
                    tintColor="yellowOffer"
                  />
                </TouchableOpacity>
              </View>
              <Spacer size="xl" />
            </>
          ) : section.layout_type?.code === 'two_banner_linear_layout' &&
            section?.elements.length > 0 ? (
            <>
              <ErrorHandler
                componentName={`${TAG} ReanimatedCarousel`}
                onErrorComponent={<View />}>
                <FlastlistCarousel
                  {...standardCarouselProps}
                  data={section?.elements}
                  loop
                  autoPlay
                  carouselCardItemStyle={carouselStyles.cardItem}
                  carouselStyle={carouselStyles.subItem}
                  bannerImgStyle={carouselStyles.subItem}
                  onCardPress={bannerAdsClick}
                  screenName="Home Page"
                  sectionType={section?.title}
                />
              </ErrorHandler>
              <Spacer size="m" />
            </>
          ) : null}
        </View>
      );
    },
    [
      TAG,
      bannerAdsClick,
      containerWidth,
      contentWidth,
      featuredContainerWidth,
      featuredWidth,
      handleContentSizeChange,
      handleContentSizeChange2,
      handleScroll1,
      handleScroll2,
      itemSeparator,
      keyExtractor,
      mainCarouselProps,
      onLayoutRecentViewed,
      recentScrollToEnd,
      recentScrollToStart,
      recentViewedColors,
      renderCarouselItem,
      renderFeaturedItem,
      renderRecentlyViewedItem,
      screenWidth,
      scrollBarPosition1,
      scrollBarPosition2,
      scrollBarWidth1,
      scrollBarWidth2,
      scrollFeaturedX,
      scrollToEnd,
      scrollToStart,
      scrollX,
      sectionDataMap,
      sections,
      standardCarouselProps,
    ],
  );

  const handleSuggestProductPress = useCallback(() => {
    setModelVisible(true);
  }, []);

  const initialNumToRender = useMemo(() => (checkDevice() ? 6 : 6), []);
  const maxToRenderPerBatch = useMemo(() => (checkDevice() ? 5 : 2), []);
  const windowSize = useMemo(() => (checkDevice() ? 7 : 5), []);

  const renderSectionList = useCallback(
    ({index}) => (
      <View key={index}>
        <FlatList
          keyExtractor={keyExtractor}
          data={sections}
          removeClippedSubviews={true}
          renderItem={renderSectionItem}
          initialNumToRender={initialNumToRender}
          windowSize={windowSize}
          maxToRenderPerBatch={maxToRenderPerBatch}
          updateCellsBatchingPeriod={50}
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={16}
        />
        <Spacer size="l" />
        <View style={styles.lookingView}>
          <Label
            text={t('searchProduct.find')}
            size="xxl"
            weight="600"
            color="grey"
            align="center"
          />
          <Spacer size="xms" />
          <View style={styles.lookingSubView}>
            <Label
              text={t('searchProduct.filling')}
              size="m"
              weight="500"
              color="grey"
              align="center"
              textTransform="capitalize"
            />
            <View>
              <FastImage
                resizeMode="contain"
                style={styles.emptyImage}
                source={Icons.editIconGif}
              />
            </View>
            <Label
              text={t('homePage.detailBelow')}
              size="m"
              weight="500"
              color="grey"
              align="center"
              textTransform="capitalize"
            />
          </View>
        </View>
        <Spacer size="mx" />
        <Button
          onPress={handleSuggestProductPress}
          radius="xms"
          size="large"
          paddingHorizontal="xxl"
          selfAlign="center"
          text={t('buttons.suggestProduct')}
          type="bordered"
        />
        <Spacer size="m" />
      </View>
    ),
    [handleSuggestProductPress, keyExtractor, sections, renderSectionItem],
  );

  const data = useMemo(() => {
    return [''];
  }, []);

  const onPressLogo = useCallback(() => {
    scrollToTop();
  }, [scrollToTop]);

  const onPressSearch = useCallback(() => {
    navigation.navigate('SearchProduct');
  }, [navigation]);

  const onChangeTextInSearch = useCallback(() => {}, []);

  const handleSearchInputPress = useCallback(() => {
    navigation.navigate('SearchProduct');
  }, [navigation]);

  const handleSuccessModalClose = useCallback(() => {
    setSuccessModel(false);
  }, []);

  const handleAccountModalClose = useCallback(() => {
    setAccountVisible(false);
  }, []);

  const handleLogoutModalClose = useCallback(() => {
    setLogoutModal(false);
  }, []);

  const handleAccountModalNavigate = useCallback(
    navigationData => {
      navigateToScreen(navigationData);
    },
    [navigateToScreen],
  );

  const handleWebViewModalClose = useCallback(() => {
    setWebViewModel(false);
  }, []);

  return (
    <View style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          useInsets
          homePage={true}
          userIcon
          bagIcon
          userIconPress={handleUserIconPress}
          onPressLogo={onPressLogo}
        />
      </ErrorHandler>

      <TouchableOpacity
        style={styles.subContainer}
        onPress={handleSearchInputPress}>
        <ErrorHandler
          componentName={`${TAG} SearchInput`}
          onErrorComponent={<View />}>
          <SearchInput
            isStatic={false}
            onChangeText={onChangeTextInSearch}
            inputStyle={styles.searchInputStyle}
            containerStyle={styles.searchStyle}
            editable={false}
            onPressIn={onPressSearch}
            placeHolder={t('homePage.searchProducts')}
            // placeHolderText={true}
          />
        </ErrorHandler>
      </TouchableOpacity>

      {showScrollToTopButton && (
        <TouchableOpacity
          style={[styles.topIconView, {top: insets.top + 115}]}
          activeOpacity={0.7}
          onPress={scrollToTop}>
          <ImageIcon icon="backToArrowTop" size="mx" />
          <Spacer size="s" type="Horizontal" />
          <Label
            text={t('otherText.gotoTop')}
            size="m"
            weight="500"
            color="whiteColor"
            style={styles.goTop}
          />
        </TouchableOpacity>
      )}

      {loader ? (
        <HomeLoader />
      ) : (
        <KeyboardAwareScrollView
          innerRef={ref => {
            flatListRefTop.current = ref;
          }}
          enableAutomaticScroll={Platform.OS === 'ios'}
          extraScrollHeight={Platform.OS === 'ios' ? Sizes.ex : 0}
          extraHeight={Platform.OS === 'ios' ? Sizes.ex0 : 0}
          keyboardShouldPersistTaps="handled"
          keyboardOpeningTime={0}
          contentContainerStyle={{flexGrow: 1}}
          enableResetScrollToCoords={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onScroll={handleScroll}
          onMomentumScrollBegin={() => setIsScrolling(true)}
          onMomentumScrollEnd={() => setIsScrolling(false)}
          style={styles.fOne}>
          <View style={styles.listContainer}>
            <FlatList
              keyExtractor={keyExtractor}
              data={data}
              renderItem={renderSectionList}
              scrollEventThrottle={16}
              removeClippedSubviews
              windowSize={5}
              maxToRenderPerBatch={5}
              updateCellsBatchingPeriod={50}
            />
          </View>
        </KeyboardAwareScrollView>
      )}
      {modelVisible && (
        <ErrorHandler
          componentName={`${TAG} SuggestProduct`}
          onErrorComponent={<View />}>
          <SuggestProduct
            visible={modelVisible}
            modelClose={handleCloseModal}
            isReset={isReset}
            onSubmit={onSubmitProductSuggestion}
            navigation={navigation}
          />
        </ErrorHandler>
      )}

      {successModel && (
        <ErrorHandler
          componentName={`${TAG} SuccessModal`}
          onErrorComponent={<View />}>
          <SuccessModal
            visible={successModel}
            title={t('feedback.feedbackSuccess')}
            onClose={handleSuccessModalClose}
          />
        </ErrorHandler>
      )}

      {accountVisible && (
        <ErrorHandler
          componentName={`${TAG} AccountModal`}
          onErrorComponent={<View />}>
          <AccountModal
            visible={accountVisible}
            onClose={handleAccountModalClose}
            navigateToScreen={handleAccountModalNavigate}
            activePlan={activePlan}
            logout={logout}
          />
        </ErrorHandler>
      )}
      {logoutModal && (
        <ErrorHandler
          componentName={`${TAG} LogoutUserModal`}
          onErrorComponent={<View />}>
          <LogoutUserModal
            visible={logoutModal}
            onClose={handleLogoutModalClose}
            userLogout={userLogout}
          />
        </ErrorHandler>
      )}
      {webViewModel && (
        <ErrorHandler
          componentName={`${TAG} WebViewModal`}
          onErrorComponent={<View />}>
          <WebViewModal
            visible={webViewModel}
            url={webLink}
            onClose={handleWebViewModalClose}
          />
        </ErrorHandler>
      )}
    </View>
  );
});
export default HomePageScene;

import React, {useMemo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {ImageIcon, Label, Spacer, Separator} from 'components/atoms';
import stylesWithOutColor from './style';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import Modal from 'react-native-modal';
import Icons from 'common/icons';
import {btnClickCallBack} from 'utils/utils';

type Props = {
  visible: boolean;
  onClose: () => void;
  removeCart: () => void;
  addWishlist: () => void;
  item: Item;
};

const CartRemoveModal = (props: Props) => {
  const {visible, onClose, removeCart, addWishlist, item} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <Modal
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.25}
      style={styles.modalStyle}>
      <View style={styles.removeModalView}>
        <View style={styles.removeModalSubView}>
          <View style={styles.productImgView}>
            <FastImage
              source={
                item?.is_member_ship_product
                  ? Icons.mamberShipCardImage
                  : {uri: getImageUrl(item.product.image.url)}
              }
              style={styles.removeImage}
              resizeMode="contain"
            />
            {item.stock_status == 0 && (
              <View style={styles.outStock}>
                <Label
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  text={t('PDP.outStock')}
                  size="m"
                  fontFamily="Medium"
                  color="text"
                  style={styles.labelBottom}
                />
              </View>
            )}
          </View>
          <Spacer size="xl" type="Horizontal" />
          <View style={styles.removeView}>
            <Spacer size="s" />
            <Label
              text={t('cart.cartRemoveMsg')}
              size="mx"
              fontFamily="Regular"
              textTransform="capitalize"
              color="text2"
            />
            <Spacer size="xl" />
            {!item?.is_member_ship_product && (
              <Label
                text={t('cart.tipMsg')}
                size="mx"
                textTransform="capitalize"
                fontFamily="Regular"
                color="text2"
              />
            )}
          </View>
        </View>
        <Spacer size="xm" />
        <View style={styles.borderBottomView} />
        <Spacer size="xm" />
        <View style={styles.deleteCartItemView}>
          <TouchableOpacity
            testID='tODeleteCartItemView'
            style={styles.deleteCartSubView}
            onPress={() => btnClickCallBack(() => removeCart())}>
            <Label
              text={t('buttons.remove')}
              size="mx"
              fontFamily="Medium"
              color="text2"
              weight="500"
            />
          </TouchableOpacity>
          {!item?.is_member_ship_product && (
            <>
              <Separator
                color="grey2"
                height="xxl"
                Vertical={true}
                thickness="x"
              />
              <Spacer size="xm" type="Horizontal" />
              <TouchableOpacity
                testID='tOAddWishList'
                style={styles.wishlistView}
                disabled={item?.is_member_ship_product}
                onPress={() => btnClickCallBack(() => addWishlist())}>
                <Label
                  text={t('buttons.addWishlist')}
                  size="mx"
                  textTransform="capitalize"
                  fontFamily="Medium"
                  color="pink"
                  weight="500"
                />
                <Spacer size="m" type="Horizontal" />
                <ImageIcon icon="heart" size="xxl" tintColor="text" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default React.memo(CartRemoveModal);

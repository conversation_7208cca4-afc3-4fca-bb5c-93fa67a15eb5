import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
import {useTheme} from '@react-navigation/native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    mapView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginHorizontal: Sizes.l,
    },
    images: {
      height: 164,
      width: '100%',
      borderRadius: Sizes.xl,
      // overflow: 'hidden',
      // flex: Sizes.x,
    },
    underView: {
      width: '48%',
      height: 164,
      // position: 'absolute',
      // backgroundColor:linear-gradient(357.87deg, rgba(24, 24, 28, 0.8) 11.31%, rgba(5, 47, 113, 0) 102.07%),
      borderRadius: Sizes.xl,
      marginHorizontal: Sizes.s,
      marginVertical: Sizes.s,
    },
    underLableText: {
      alignItems: 'center',

      justifyContent: 'flex-end',
      height: 160,
    },
    amount: {
      flexDirection: 'row',

      alignItems: 'center',
    },
    amountText: {
      fontSize: Sizes.xl,
      fontWeight: '700',
      color: colors.textError,
      left: Sizes.xs,
    },
    rupesSymble: {
      fontWeight: '400',
      color: colors.textError,
    },
    linerColor: {borderRadius: Sizes.xl, height: 164},
  });

export default styles;

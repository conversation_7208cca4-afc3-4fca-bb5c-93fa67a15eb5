import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background2,
    },
    headerSection: {
      backgroundColor: colors.background,
      paddingBottom: Sizes.s,
    },
    fOne: {
      flex: Sizes.x,
    },
    subView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xm,
    },
    orderReviewView: {
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xms,
      flexDirection: 'row',
      alignItems: 'center',
    },
    gifStyle: {
      height: Sizes.x78,
      width: Sizes.x78,
    },
    pReviewView: {
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xm,
      padding: Sizes.s,
      flexDirection: 'row',
      alignItems: 'center',
    },
    pImg: {
      backgroundColor: colors.whiteBlue1,
      height: Sizes.x56,
      width: Sizes.x56,
      borderRadius: Sizes.s,
    },
    imgStyle: {
      borderRadius: Sizes.s,
    },
    otherView: {
      backgroundColor: colors.whiteBlue1,
      padding: Sizes.s,
    },
    inputStyle: {
      backgroundColor: colors.whiteBlue1,
      height: Sizes.ex92,
      width: '100%',
      marginBottom: Sizes.m,
      textAlignVertical: 'top',
    },
    btnStyle: {
      height: Sizes.xx4l,
      width: Sizes.ex124,
      paddingVertical: 0,
    },
    btnContainer: {
      shadowColor: colors.black,
      shadowOffset: {
        width: 2,
        height: 2,
      },
      elevation: Sizes.s,
      backgroundColor: colors.whiteColor,
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.l,
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
    },
    btnTxt: {
      fontFamily: Fonts.Medium,
    },
    saveStyle: {
      height: Sizes.x7l,
      width: '100%',
    },
    safeView: {
      backgroundColor: colors.whiteColor,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectImg: {
      backgroundColor: colors.whiteColor,
      height: Sizes.x9l,
      width: Sizes.x9l,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: Sizes.m,
    },
    sImgStyle: {
      borderRadius: Sizes.xm,
      overflow: 'hidden',
    },
    addView: {
      backgroundColor: colors.background3,
      height: Sizes.x9l,
      width: Sizes.x9l,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
    },
    cancelView: {
      backgroundColor: colors.grayishRed,
      height: Sizes.xl,
      width: Sizes.xl,
      borderRadius: Sizes.xms,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      top: -Sizes.xms,
      right: -Sizes.xms,
    },
  });

export default styles;

// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        kotlinVersion = "1.5.31"
        // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
        ndkVersion = "23.1.7779620"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        classpath("com.google.gms:google-services:4.3.15")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("com.google.firebase:firebase-crashlytics-gradle:2.9.0")
        classpath ("com.google.firebase:perf-plugin:1.4.2")
        classpath ("org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.22")
    }
}
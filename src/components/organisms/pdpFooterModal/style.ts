import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
const Styles = (colors: Theme['colors']) => {
  const insets = useSafeAreaInsets();
  return StyleSheet.create({
    modalStyle: {margin: 0},
    flexContainer: {flex: Sizes.x},
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    modelBg: {
      flex: Sizes.s,
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
      paddingHorizontal: Sizes.s,
    },
    modelView: {flex: Sizes.x},
    filterListLeftContainer: {
      flex: Sizes.xs,
      backgroundColor: colors.background,
    },
    filterListLeft: {
      flex: Sizes.x,
      backgroundColor: colors.grey4,
      borderTopRightRadius: Sizes.m,
    },
    listItemLeft: {
      paddingVertical: Sizes.mx,
      paddingHorizontal: Sizes.xx,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
    },
    searchInput: {
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    filterListRightContainer: {
      flex: 3,
      paddingLeft: Sizes.x7l,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    modalHeader: {
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.xl,
    },
    flexRow: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    filterSectionHeader: {
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.xx,
    },
    rangeSliderStyle: {
      paddingTop: '25%',
      paddingBottom: '4%',
    },
    sortContainer: {
      flex: Sizes.x,
      padding: Sizes.l,
    },
    rowCenteredItems: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    footerSection: {
      borderTopColor: colors.grey2,
      borderTopWidth: 1,
      paddingBottom: insets.bottom,
    },
    footerClearBtn: {
      fontSize: Sizes.mx,
      fontWeight: '500',
      color: colors.newSunnyOrange,
    },
    footerApplyBtn: {
      fontSize: Sizes.l,
      fontWeight: '500',
      color: colors.text2,
    },
    rangeLabelContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingTop: '1%',
      paddingBottom: '5%',
    },
    ratingCheckboxRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.sx,
    },
    starIconRow: {paddingBottom: Sizes.s, flexDirection: 'row'},
  });
};

export default Styles;

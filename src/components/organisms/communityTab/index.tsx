import React, {useState} from 'react';
import {View, TouchableOpacity, Modal, Animated} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {BlurView} from '@react-native-community/blur';
import {Sizes} from 'common';
import {navigate} from 'utils/navigationRef';
import {useMemo} from 'react';

const CommunityTab = ({focused}) => {
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [tabView, setTabView] = useState(false);
  const animation = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(animation, {
      toValue: tabView ? 1 : 0,
      duration: 500,
      friction: 4,
      useNativeDriver: false,
    }).start();
  }, [tabView, animation]);

  const opacity = {
    opacity: animation.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0, 0, 1],
    }),
  };

  return (
    <View>
      <TouchableOpacity
        onPress={() => setTabView(true)}
        style={{alignItems: 'center'}}>
        <ImageIcon
          resizeMode="contain"
          size="xxxl"
          icon={focused ? 'communityActive' : 'community'}
        />
        <Spacer size="x" />
        <Label
          text="Community"
          size="m"
          weight="600"
          color={focused ? 'tabActive' : 'tabInActive'}
        />
      </TouchableOpacity>

      <Modal
        visible={tabView}
        transparent
        onRequestClose={() => {
          setTabView(false);
        }}>
        <BlurView
          style={{flex: 1}}
          blurType="dark"
          blurAmount={1} // Adjust the blur amount as needed
          reducedTransparencyFallbackColor="#6B7186BF" // Fallback color for devices that don't support blur
        >
          <View
            style={[
              styles.touchableViewContainer,
              {
                paddingBottom: insets.bottom,
              },
            ]}>
            <TouchableOpacity
              onPress={() => setTabView(!tabView)}
              activeOpacity={1}
              style={styles.itemsContainer}>
              <TouchableOpacity
                onPress={() => {
                  navigate('News');
                  setTabView(!tabView);
                }}>
                <Animated.View
                  style={[
                    styles.item,
                    opacity,
                    {
                      transform: [
                        {
                          translateX: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenWidth * 0.42)],
                          }),
                        },
                        {
                          translateY: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenHeight * 0.04)],
                          }),
                        },
                      ],
                    },
                  ]}>
                  <ImageIcon icon="news" size="x9l" />
                  <Label text="NEWS" size="m" weight="600" color="background" />
                </Animated.View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  navigate('MagazineScene');
                  setTabView(!tabView);
                }}>
                <Animated.View
                  style={[
                    styles.item,
                    opacity,
                    {
                      transform: [
                        {
                          translateX: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenWidth * 0.32)],
                          }),
                        },
                        {
                          translateY: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenWidth * 0.3)],
                          }),
                        },
                      ],
                    },
                  ]}>
                  <ImageIcon icon="magazine" size="x9l" />
                  <Label
                    text="MAGAZINE"
                    size="m"
                    weight="600"
                    color="background"
                  />
                </Animated.View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  navigate('Shorts');
                  setTabView(!tabView);
                }}>
                <Animated.View
                  style={[
                    styles.item,
                    opacity,
                    {
                      transform: [
                        {
                          translateX: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenWidth * 0.1)],
                          }),
                        },
                        {
                          translateY: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenWidth * 0.4)],
                          }),
                        },
                      ],
                    },
                  ]}>
                  <ImageIcon icon="shorts" size="x9l" />
                  <Label
                    text="SHORTS"
                    size="m"
                    weight="600"
                    color="background"
                  />
                </Animated.View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setTabView(!tabView);
                }}>
                <Animated.View
                  style={[
                    styles.item,
                    opacity,
                    {
                      transform: [
                        {
                          translateX: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, Sizes.screenWidth * 0.12],
                          }),
                        },
                        {
                          translateY: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenWidth * 0.3)],
                          }),
                        },
                      ],
                    },
                  ]}>
                  <ImageIcon icon="blogs" size="x9l" />
                  <Label
                    text="BLOGS"
                    size="m"
                    weight="600"
                    color="background"
                  />
                </Animated.View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setTabView(!tabView);
                }}>
                <Animated.View
                  style={[
                    styles.item,
                    opacity,
                    {
                      transform: [
                        {
                          translateX: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, Sizes.screenWidth * 0.23],
                          }),
                        },
                        {
                          translateY: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -(Sizes.screenHeight * 0.04)],
                          }),
                        },
                      ],
                    },
                  ]}>
                  <ImageIcon icon="event" size="x9l" />
                  <Label
                    text="EVENTS"
                    size="m"
                    weight="600"
                    color="background"
                  />
                </Animated.View>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => setTabView(!tabView)}>
                <Animated.View
                  style={[
                    styles.addButtonInner,
                    {
                      transform: [
                        {
                          rotate: animation.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['45deg', '90deg'],
                          }),
                        },
                      ],
                    },
                  ]}>
                  <ImageIcon icon="close" size="x7l" />
                </Animated.View>
              </TouchableOpacity>
            </TouchableOpacity>
          </View>
        </BlurView>
      </Modal>
    </View>
  );
};

export default CommunityTab;

import moment from 'moment';
import {format, parseISO} from 'date-fns';
export const dateTimeFormat = (
  date: string,
  newFormat: string = 'MMM DD, YYYY hh:mm A',
) => {
  if (date && (date.includes('/') || date.includes('-'))) {
    if (date.includes('/')) {
      return moment(!!date ? date : new Date(), 'MM/DD/YYYY hh:mm A').format(
        newFormat,
      );
      // return format(parseISO(!!date ? date : new Date()), newFormat);
    } else {
      // return moment(!!date ? date : new Date(), 'MM-DD-YYYY hh:mm A').format(
      //   format,
      // );
      return format(parseISO(!!date ? date : new Date()), newFormat);
    }
  } else {
    return moment(Number(date)).format(newFormat);
  }
};

export const snakeToTitleCase = (
  s: string = '',
  searchString = '',
  replaceString = '',
) => {
  const str = s.replace(/^_*(.)|_+(.)/g, (s, c, d) =>
    c ? c.toUpperCase() : ' ' + d.toUpperCase(),
  );
  if (!!searchString && !!replaceString) {
    return str.replace(searchString, replaceString);
  } else {
    return str;
  }
};

import {Sizes} from 'common';
import {Dimensions, StyleSheet} from 'react-native';

const {height, width} = Dimensions.get('window');

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    progressForground: {
      height: Sizes.s,
      backgroundColor: colors.background,
    },
    progressBackground: {
      width: '100%',
      height: Sizes.s,
      backgroundColor: colors.progressBackgroundColor,
      position: 'absolute',
      top: 0,
      zIndex: Sizes.m + Sizes.x,
    },
    player: {
      width: '100%',
      height: height,
      backgroundColor: colors.background,
    },
    playIconContainer: {
      alignSelf: 'center',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      right: 0,
      left: 0,
      top: 0,
      bottom: 0,
      zIndex: 111,
    },
    container: {
      width: '100%',
      height: height,
    },
    iconStyle: {
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: Sizes.s,
    },
    videoPlayerContainer: {
      width: width,
      height: height,
      backgroundColor: colors.textLight,
    },
  });

export default styles;

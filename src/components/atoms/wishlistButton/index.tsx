import React, {useCallback, useMemo, useRef} from 'react';
import {Animated, TouchableOpacity, ViewProps, Easing, Vibration} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {useDispatch, useSelector} from 'react-redux';
import {addToWishListThunk, removeFromWishlist} from 'app-redux-store/slice/appSlice';
import {ImageIcon} from 'components/atoms';
import {showInfoMessage} from 'utils/show_messages';
import {t} from 'i18next';
import { RootState } from '@types/local';
import { debounce } from 'utils/utils';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';
import { appsFlyerEvent } from 'components/organisms/analytics-Events/appsFlyerEvent';

type Props = {
  item: Object;
  productId: number;
  image: string;
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  style?: ViewProps['style'];
  onClose?: () => void;
};

const WishlistButton = ({
  item,
  productId,
  image,
  navigation,
  style,
  onClose,
}: Props) => {
  const dispatch = useDispatch();
  const {isLoggedIn, wishlists} = useSelector((state: RootState) => state.app);
  const isInWishlist = useMemo(() => {
    // Check if product is in any wishlist
    if (wishlists) {
      return Object.values(wishlists).some(wishlist =>
        wishlist?.productIds?.includes(Number(productId)),
      );
    }
    return false;
  }, [wishlists, productId]);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const animateHeart = useCallback(() => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.3,
        duration: 200,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
    ]).start();
  }, [scaleAnim]);

  const onPressWishlist = useCallback(
    debounce(() => {
      if (isLoggedIn) {
        if (isInWishlist) {
          dispatch(removeFromWishlist({productId}));
        } else {
          dispatch(addToWishListThunk({productId, image}));
          trackEvent('ADD_TO_WISHLIST', {
            contentId: productId,
            contentType: 'product',
            // currency: 'INR',
            // value: 499,
            // params: { item_name: 'Toothpaste' }
          });
          // appsFlyer Add to Wishlist Event
          appsFlyerEvent('AddToWishlist', {
            price: item?.selling_price,
            productId: productId,
            category: item?.name,
            currency: 'INR',
          });
        }
        animateHeart(); // Trigger the animation on press

        if (onClose) {
          onClose();
        }
      } else {
        showInfoMessage(t('toastMassages.wishlistLogin'));
        if (onClose) {
          onClose();
        }
        navigation.navigate('Login', {back: true});
      }
    }, 350)
  , [isLoggedIn, navigation, productId, isInWishlist, animateHeart, dispatch, image, onClose]);

  return (
    <TouchableOpacity onPress={onPressWishlist} style={style}>
      <Animated.View style={{transform: [{scale: scaleAnim}]}}>
        <ImageIcon
          icon={isInWishlist ? 'heartFilled' : 'heart'}
          size="xxxl"
          tintColor={isInWishlist ? 'red1' : 'text'}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

export default React.memo(WishlistButton);
import React from 'react';
import {throttle} from 'lodash';
import {GestureResponderEvent} from 'react-native';

interface Props {
  onPress: (event: GestureResponderEvent) => void;
}

function withOnPressDebounce<T>(WrappedComponent: React.ComponentType<T>) {
  return class extends React.Component<T & Props> {
    render() {
      const {onPress, ...props} = this.props;
      return (
        <WrappedComponent
          onPress={throttle(onPress, 750, {
            leading: true,
            trailing: false,
          })}
          {...(props as T)}
        />
      );
    }
  };
}

export default withOnPressDebounce;

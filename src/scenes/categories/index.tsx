import React, {
  useCallback,
  useEffect,
  useState,
  useRef,
  useMemo,
  memo,
} from 'react';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../routes';
import stylesWithOutColor from './style';
import {<PERSON><PERSON>, <PERSON><PERSON>} from 'components/molecules';
import {
  FlatList,
  TouchableOpacity,
  View,
  TextInput,
  SafeAreaView,
  InteractionManager,
} from 'react-native';
import {ImageIcon, Label, Spacer, Separator} from 'components/atoms';
import {useTheme} from '@react-navigation/native';
import {navigate} from 'utils/navigationRef';
import LinearGradient from 'react-native-linear-gradient';
import {getAllCategories} from 'services/category';
import {Sizes} from 'common';
import {CategoriesLoader} from 'skeletonLoader';
import getImageUrl from 'utils/imageUrlHelper';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import ErrorHandler from 'utils/ErrorHandler';
import {RootState} from '@types/local';
import {useSelector} from 'react-redux';
import {AnalyticsEvents} from 'components/organisms';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

type Category = {
  name?: string;
  children?: Category[];
};

const CategoriesScene = ({navigation}: Props) => {
  const TAG = 'CategoriesScreen';
  const [category, setCategory] = useState<ProductCategoryData[]>([]);
  const [tempCategory, setTempCategory] = useState<ProductCategoryData[]>([]);
  const [leftCategory, setLeftCategory] = useState<SubCategory[]>([]);
  const [tempLeftCategory, setTempLeftCategory] = useState<SubCategory[]>([]);
  const [rightCategory, setRightCategory] = useState<SubCategory[]>([]);
  const [tempRightCategory, setTempRightCategory] = useState<SubCategory[]>([]);
  const [activeCategory, setActiveCategory] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [activeScreen, setActiveScreen] = useState<
    'allCategoriesOptions' | 'subCategory'
  >('allCategoriesOptions');
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [search, setSearch] = useState('');
  const [level, setLevel] = useState<SubCategory[]>([]);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const flatListRef = useRef<FlatList>(null);
  const inputRef = useRef(null);
  const insets = useSafeAreaInsets();

  const categoriesData = useCallback(async () => {
    setLoading(true);
    const response = await getAllCategories();
    const {data, status} = response;
    setLoading(false);
    if (status) {
      const categoryData =
        data?.collection?.length > 0
          ? filterIncludeInMenu(data?.collection[0])
          : [];
      setCategory(categoryData);
      setTempCategory(categoryData);
      setActiveScreen('allCategoriesOptions');
    }
  }, []);

  const filterIncludeInMenu = data => {
    if (!data?.children) return [];

    return data?.children
      .filter(child => child.include_in_menu === 1)
      .map(child => ({
        ...child,
        children: filterIncludeInMenu(child),
      }))
      .sort((a, b) => a?.position - b?.position);
  };

  const onBackPress = useCallback(() => {
    if (activeScreen === 'allCategoriesOptions') {
      return navigation.goBack();
    } else {
      setShowSearchBar(false);
      setSearch('');
      setActiveScreen('allCategoriesOptions');
      setLevel([]);
      setRightCategory([]);
      setTempRightCategory([]);
      setLeftCategory([]);
      setTempLeftCategory([]);
      setActiveCategory(0);
    }
  }, [activeScreen, navigation]);

  const itemSeparatorComponent = useCallback(
    (size: keyof typeof Sizes) => <Spacer size={size} type="Vertical" />,
    [],
  );
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  useEffect(() => {
    categoriesData();
    AnalyticsEvents('ALL_BRANDS', 'Brands viewed', {}, userInfo, isLoggedIn);
  }, []);

  const filterCategories = (
    categories: Category[],
    text: string,
  ): Category[] => {
    if (
      !Array.isArray(categories) ||
      typeof text !== 'string' ||
      text.trim() === ''
    ) {
      return [];
    }
    return categories
      .map(item => {
        if (!item) return null;

        const filteredChildren = Array.isArray(item?.children)
          ? filterCategories(item?.children, text)
          : [];

        if (
          item.name?.toLowerCase()?.includes(text.toLowerCase()) ||
          filteredChildren.length > 0
        ) {
          return {
            ...item,
            children:
              filteredChildren.length > 0 ? filteredChildren : undefined,
          };
        }

        return null;
      })
      .filter((item): item is Category => item !== null);
  };

  const handleSearch = useCallback(
    text => {
      if (text) {
        if (activeScreen === 'allCategoriesOptions') {
          const newData = filterCategories(tempCategory, text);
          setCategory(newData);
        } else {
          const newLeftData = filterCategories(tempLeftCategory, text);
          const newRightData = filterCategories(tempRightCategory, text);
          setLeftCategory(newLeftData);
          setRightCategory(newRightData);
        }
        setSearch(text);
      } else {
        if (activeScreen === 'allCategoriesOptions') {
          setCategory(tempCategory);
        } else {
          setLeftCategory(tempLeftCategory);
          setRightCategory(tempRightCategory);
        }
        setSearch('');
      }
    },
    [
      activeScreen,
      tempCategory,
      tempLeftCategory,
      tempRightCategory,
      filterCategories,
      setCategory,
      setLeftCategory,
      setRightCategory,
      setSearch,
    ],
  );

  const onSelectOption = useCallback(
    (item, index) => {
      if (!item) return;
      const children = item.children || [];
      setLeftCategory(tempRightCategory);
      setTempLeftCategory(tempRightCategory);
      setRightCategory(children);
      setTempRightCategory(children);
      setLevel(prev => [...prev, {level: rightCategory, id: item.id}]);
      setActiveCategory(item.id);
      setShowSearchBar(false);
      setSearch('');
      setActiveIndex(index);
      InteractionManager.runAfterInteractions(() => {
        scrollToActiveItem(index);
      });
    },
    [
      tempRightCategory,
      rightCategory,
      setLeftCategory,
      setTempLeftCategory,
      setRightCategory,
      setTempRightCategory,
      setLevel,
      setActiveCategory,
      setShowSearchBar,
      setSearch,
      setActiveIndex,
      scrollToActiveItem,
    ],
  );

  const getItemLayout = useCallback(
    (data: any, index: number) => ({
      length: 150,
      offset: 150 * index,
      index,
    }),
    [],
  );

  const scrollToActiveItem = useCallback((index: number) => {
    if (!flatListRef.current || index < 0) return;
    const offset = index * 107;
    setTimeout(() => {
      flatListRef.current?.scrollToOffset({
        offset,
        animated: true,
      });
    }, 300);
  }, []);

  const renderCategory = useCallback(
    ({item, index}) => (
      <View style={[styles.renderView, {marginRight: index % 4 == 3 ? 0 : 8}]}>
        <TouchableOpacity
          onPress={() => {
            const findIndex = tempCategory.findIndex(
              data => data?.id === item?.id,
            );
            const subChildData =
              findIndex !== -1
                ? tempCategory[findIndex].children
                : item?.children;
            if (subChildData?.length > 0) {
              setLevel(prev => [...prev, {level: tempCategory, id: item.id}]);
              setActiveScreen('subCategory');
              setShowSearchBar(false);
              setSearch('');
              setCategory(tempCategory);
              setLeftCategory(tempCategory);
              setTempLeftCategory(tempCategory);
              setRightCategory(subChildData);
              setTempRightCategory(subChildData);
              setActiveCategory(item.id);
              setActiveIndex(findIndex);
              InteractionManager.runAfterInteractions(() => {
                scrollToActiveItem(findIndex);
              });
            } else {
              navigate('CategoryDetail', {
                categoryId: item?.id,
              });
            }
          }}
          style={styles.categoryRenderView}>
          <ErrorHandler
            componentName={`${TAG} FastImage`}
            onErrorComponent={<View />}>
            <FastImage
              resizeMode="contain"
              style={styles.categoryTitleView}
              source={
                item?.thumbnail
                  ? {
                      uri: getImageUrl(item?.thumbnail, 'category'),
                    }
                  : Icons.defaultImage
              }
            />
          </ErrorHandler>
          <Spacer size="sx" />
          <Label
            color="categoryTitle"
            size="xms"
            fontFamily="Medium"
            text={item?.name}
            align="center"
            numberOfLines={2}
          />
        </TouchableOpacity>
      </View>
    ),
    [
      tempCategory,
      setLevel,
      setActiveScreen,
      setShowSearchBar,
      setSearch,
      setCategory,
      setLeftCategory,
      setTempLeftCategory,
      setRightCategory,
      setTempRightCategory,
      setActiveCategory,
      setActiveIndex,
      scrollToActiveItem,
      navigate,
    ],
  );

  const leftCategoryStyle = useCallback(
    (item, index) => [
      styles.categorySubView,
      styles.leftList,
      item.id === activeCategory && {backgroundColor: colors.skyBlue18},
      index === 0 && styles.firstIndex,
    ],
    [activeCategory, styles, colors.skyBlue18],
  );

  const handleLeftPress = useCallback(
    item => {
      if (item?.children?.length > 0) {
        setRightCategory(item?.children);
        setTempRightCategory(item?.children);
        setActiveCategory(item.id);
        level.pop();
        level.push({level: leftCategory, id: item.id});
      } else {
        navigation.navigate('CategoryDetail', {
          categoryId: item?.id,
        });
      }
    },
    [
      leftCategory,
      navigation,
      setRightCategory,
      setTempRightCategory,
      setActiveCategory,
      level,
    ],
  );

  const handleCategoryPress = useCallback(
    item => {
      navigation.navigate('CategoryDetail', {
        categoryId: item?.id,
      });
    },
    [navigation],
  );

  const renderLeftCategory = useCallback(
    ({item, index}) => {
      return (
        <TouchableOpacity
          onPress={() => handleLeftPress(item)}
          style={leftCategoryStyle(item, index)}>
          <ErrorHandler
            componentName={`${TAG} FastImage`}
            onErrorComponent={<View />}>
            <FastImage
              resizeMode="cover"
              style={styles.categoryTitleSubView}
              source={
                item?.thumbnail
                  ? {uri: getImageUrl(item?.thumbnail, 'category')}
                  : Icons.defaultImage
              }
            />
          </ErrorHandler>
          <Spacer size="sx" />
          <TouchableOpacity
            style={styles.backStyle}
            onPress={() => handleCategoryPress(item)}>
            <View style={styles.textWrapper}>
              <Label
                color="categoryTitle"
                size="m"
                fontFamily="Regular"
                text={item?.name}
                align="center"
                numberOfLines={1}
              />
            </View>
            <ImageIcon
              tintColor="categoryTitle"
              size="m"
              resizeMode="contain"
              icon="rightArrow"
              style={styles.rightArrow}
            />
          </TouchableOpacity>
        </TouchableOpacity>
      );
    },
    [leftCategoryStyle, handleLeftPress, handleCategoryPress],
  );

  const isOdd = useMemo(
    () => rightCategory.length % 2 !== 0,
    [rightCategory.length],
  );

  const handleSubPress = useCallback(
    (item, index) => {
      if (item?.children?.length > 0) {
        onSelectOption(item, index);
      } else {
        navigate('CategoryDetail', {categoryId: item?.id});
      }
    },
    [onSelectOption, navigate],
  );

  const handleOptionPress = useCallback(
    (item, index) => {
      onSelectOption(item, index);
    },
    [onSelectOption],
  );

  const renderSubCategory = useCallback(
    ({item, index}) => {
      const isLastItem = index === rightCategory.length - 1;
      return (
        <View
          style={[
            styles.itemView,
            index % 2 === 0 && {marginRight: Sizes.sx},
            isLastItem && isOdd ? styles.flexHalf : null,
          ]}>
          <TouchableOpacity
            onPress={() => handleSubPress(item, index)}
            style={styles.categoryView}>
            <ErrorHandler
              componentName={`${TAG} FastImage`}
              onErrorComponent={<View />}>
              <FastImage
                resizeMode="cover"
                style={styles.categoryChildView}
                source={
                  item?.thumbnail
                    ? {uri: getImageUrl(item?.thumbnail, 'category')}
                    : Icons.defaultImage
                }
              />
            </ErrorHandler>
            <Spacer size="s" />
            <Label
              color="categoryTitle"
              size="m"
              align="center"
              fontFamily="Regular"
              text={item?.name}
              numberOfLines={2}
              style={styles.sTitle}
            />
          </TouchableOpacity>

          {item?.children?.length > 0 ? (
            <>
              <Spacer size="s" />
              <TouchableOpacity
                style={styles.categoryOptionWarper}
                onPress={() => handleOptionPress(item, index)}>
                <View style={styles.categoryOptionsContainer}>
                  <Label
                    color="green2"
                    size="m"
                    fontFamily="Medium"
                    text={`${item?.children?.length} ${t('otherText.options')}`}
                    style={styles.optionTxt}
                  />
                  <ImageIcon
                    tintColor="green2"
                    style={styles.goBackImage}
                    resizeMode="contain"
                    icon="arrowRight"
                  />
                </View>
              </TouchableOpacity>
            </>
          ) : (
            <View />
          )}
        </View>
      );
    },
    [rightCategory.length, handleSubPress, handleOptionPress],
  );

  const emptyLabelText = useMemo(() => {
    if (search?.length > 0) {
      return t('categoryDetail.noSearchCategory') as string;
    }
    if (activeScreen === 'allCategoriesOptions') {
      return t('categoryDetail.noCategoryData') as string;
    }
    return t('categoryDetail.noSubCategoryData') as string;
  }, [search, activeScreen, t]);

  const handleGoHome = useCallback(() => {
    navigation.popToTop();
  }, [navigation]);

  const emptyView = useCallback(
    () => (
      <View style={styles.emptyView}>
        <FastImage
          resizeMode="contain"
          source={Icons.categoryGif}
          style={styles.notFoundImg}
        />
        <Label
          text={emptyLabelText}
          size="l"
          fontFamily="SemiBold"
          color="text2"
          align="center"
        />
        <Spacer size="xx" />
        <Button
          type="secondary"
          onPress={handleGoHome}
          labelColor="whiteColor"
          style={styles.buttonStyle}
          text={t('buttons.goToHome') as string}
        />
      </View>
    ),
    [styles, Icons, emptyLabelText, handleGoHome, t],
  );

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.headerView}>
        {showSearchBar ? (
          <View style={styles.searchView}>
            <TouchableOpacity style={styles.backStyle} onPress={onBackPress}>
              <Spacer size="xm" type="Horizontal" />
              <ImageIcon
                icon="down"
                size="l"
                tintColor="text2"
                style={styles.leftArrow}
              />
              <Spacer size="xm" type="Horizontal" />
            </TouchableOpacity>
            <Separator color="grey5" Vertical />
            <Spacer size="sx" type="Horizontal" />
            <ErrorHandler
              componentName={`${TAG} TextInput`}
              onErrorComponent={<View />}>
              <TextInput
                testID="txtCatPDPSearchByName"
                ref={inputRef}
                onChangeText={handleSearch}
                placeholder={t('PDP.searchByName')}
                placeholderTextColor={colors.text2}
                style={styles.inputBox}
                value={search}
                allowFontScaling={false}
              />
            </ErrorHandler>
            <Spacer size="sx" type="Horizontal" />
            <Separator color="grey5" Vertical />
            <Spacer size="xm" type="Horizontal" />
            <ImageIcon icon="search2" size="xl" tintColor="text2" />
            <Spacer size="m" type="Horizontal" />
          </View>
        ) : (
          <ErrorHandler
            componentName={`${TAG} Header`}
            onErrorComponent={<View />}>
            <Header
              navigation={navigation}
              searchIcon={true}
              backButton={true}
              searchBar={showSearchBar}
              text={t(
                activeScreen === 'allCategoriesOptions'
                  ? 'categories.title'
                  : 'categories.seeCategory',
              )}
              onPressSearchIcon={() => {
                setShowSearchBar(!showSearchBar);
                InteractionManager.runAfterInteractions(() => {
                  inputRef?.current?.focus();
                });
              }}
              isRedious
              searchColor="categoryTitle"
              tintColorGOBack="categoryTitle"
              onPressNavigation={onBackPress}
            />
          </ErrorHandler>
        )}
      </View>

      {loading ? (
        <CategoriesLoader />
      ) : (
        <View style={styles.subContainer}>
          <Spacer size="m" />
          <View style={styles.mainContainer}>
            {activeScreen === 'allCategoriesOptions' &&
              category?.length > 0 && (
                <>
                  <Spacer size="xl" />
                  <View style={styles.sectionHeader}>
                    <ImageIcon
                      size="x3l"
                      icon="categoryLineImage"
                      style={styles.lineIcon}
                    />
                    <Spacer size="xm" type="Horizontal" />
                    <Label
                      color="categoryTitle"
                      size="mx"
                      fontFamily="SemiBold"
                      text={t('otherText.seeAllCat')}
                      textTransform="uppercase"
                    />
                    <Spacer size="xm" type="Horizontal" />
                    <ImageIcon
                      size="x3l"
                      icon="categoryLineImage"
                      style={styles.lineImage}
                    />
                  </View>
                  <Spacer size="m" />
                </>
              )}

            {activeScreen === 'allCategoriesOptions' ? (
              <FlatList
                showsVerticalScrollIndicator={false}
                numColumns={4}
                data={category}
                style={styles.marginH12}
                renderItem={renderCategory}
                keyExtractor={item => item?.id?.toString() ?? ''}
                ItemSeparatorComponent={() => itemSeparatorComponent('xm')}
                ListEmptyComponent={emptyView}
              />
            ) : null}

            {activeScreen === 'subCategory' ? (
              <View style={styles.bothSectionContainer}>
                <LinearGradient
                  start={{x: 0, y: 0}}
                  end={{x: 0, y: 0}}
                  style={styles.leftSection}
                  colors={[colors.skyBlue18, colors.background3]}>
                  <FlatList
                    ref={flatListRef}
                    style={styles.leftList}
                    showsVerticalScrollIndicator={false}
                    keyExtractor={item => item?.id?.toString()}
                    data={leftCategory}
                    getItemLayout={getItemLayout}
                    renderItem={renderLeftCategory}
                  />
                </LinearGradient>
                <Spacer type="Horizontal" size="s" />
                <View style={styles.rightSection}>
                  {rightCategory?.length > 0 && (
                    <View>
                      <Label
                        text={`${rightCategory?.length ?? 0} ${t(
                          'otherText.itemsIn',
                        )}`}
                        fontFamily="SemiBold"
                        size="mx"
                        color="categoryTitle"
                      />
                      <Spacer size="s" />
                    </View>
                  )}
                  <FlatList
                    numColumns={2}
                    showsVerticalScrollIndicator={false}
                    data={rightCategory}
                    columnWrapperStyle={styles.subCategoryView}
                    renderItem={renderSubCategory}
                    ItemSeparatorComponent={() => itemSeparatorComponent('xms')}
                    ListEmptyComponent={emptyView}
                  />
                </View>
              </View>
            ) : null}
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default memo(CategoriesScene);

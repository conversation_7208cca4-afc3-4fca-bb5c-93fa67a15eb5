import React, {useMemo} from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import Modal from 'react-native-modal';
import {t} from 'i18next';

type Props = {
  visible: boolean;
  onClose: () => void;
};

const ReviewSuccessModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose} = props;

  return (
    <Modal
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.25}
      style={styles.modalStyle}>
      <View style={styles.modalView}>
        <View style={styles.mainView}>
          <View style={styles.centerView}>
            <FastImage
              resizeMode="contain"
              style={styles.rateImg}
              source={Icons.rateGif}
            />
            <Spacer size="mx" />
            <Label
              text={t('rate.ratingSubmitted')}
              size="m"
              color="text"
              fontFamily="SemiBold"
              weight="600"
            />
            <Spacer size="m" />
            <Label
              text={t('rate.thxFeedback')}
              size="mx"
              color="skyBlue23"
              fontFamily="SemiBold"
              weight="600"
            />
            <Spacer size="m" />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default React.memo(ReviewSuccessModal);

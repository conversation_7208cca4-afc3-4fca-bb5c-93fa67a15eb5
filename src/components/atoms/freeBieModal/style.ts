import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    imageModel: {
      backgroundColor: colors.blueLagoon,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    subImageView: {
      backgroundColor: colors.whiteColor,
      width: '100%',
      marginTop: '40%',
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x6l,
    },
    modelSubView: {
      padding: Sizes.m,
      paddingBottom: Sizes.l,
    },
    mainViewReturn: {
      flexDirection: 'row',
    },
    textItem: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.l,
    },
    dot: {
      bottom: Sizes.sx,
    },
  });

export default styles;

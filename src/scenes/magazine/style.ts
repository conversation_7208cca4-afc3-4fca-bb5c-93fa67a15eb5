import {Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.l,
    },
    searchBarView: {
      width: '100%',
      height: Sizes.x6l,
      marginTop: Sizes.mx,
      borderRadius: Sizes.xms,
      backgroundColor: colors.grey4,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    searchIconView: {
      flexDirection: 'row',
      alignContent: 'center',
      justifyContent: 'space-between',
    },
    flex: {
      flex: Sizes.x,
    },
    magBannerView: {
      width: '100%',
      height: Sizes.exl,
      borderRadius: Sizes.mx,
    },
    suggestMagBtn: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.orangeLight44,
      padding: Sizes.xms,
    },
    button1Opacity: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    searchInput: {
      flex: Sizes.x,
    },
    filterValueMainView: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: Sizes.m,
    },
    filterValueView: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    filterValue: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      paddingHorizontal: Sizes.xm,
      borderRadius: Sizes.xl,
    },
    suggestMagBtnView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    magCardView: {
      width: (DeviceWidth - 44) / 2,
      borderRadius: Sizes.xms,
      borderWidth: Sizes.z,
      borderColor: colors.grey2,
      backgroundColor: colors.offWhite,
      padding: Sizes.xms,
      alignItems: 'center',
    },
    title: {
      flexDirection: 'row',
    },
    monthText: {
      width: '100%',
    },
    magImage: {
      borderRadius: Sizes.xms,
      width: Sizes.ex1,
      height: Sizes.ex2l + Sizes.xs,
    },
    footerCard: {
      flex: Sizes.x,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    subFooterCard: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    arrowButton: {
      margin: Sizes.sx,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      textAlign: 'center',
    },
    readBtn: {
      borderWidth: Sizes.z,
      borderRadius: Sizes.sx,
      borderColor: colors.categoryTitle,
      paddingHorizontal: Sizes.m,
      justifyContent: 'center',
      alignItems: 'center',
      height: Sizes.x26,
    },
    cardFlatList: {
      justifyContent: 'space-between',
    },
    filterBtnLabel: {
      color: colors.text,
      fontSize: Sizes.l,
      fontWeight: '400',
    },
  });
export default styles;

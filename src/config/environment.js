import {Dimensions, Platform} from 'react-native';
export const window = Dimensions.get('screen');
export const DeviceHeight = window.height;
export const DeviceWidth = window.width;
export const WEBSITE_URL = 'https://www.dentalkart.com/';
export const API_BASE_URL = 'https://apis.dentalkart.com/';
export const API_STAGE_BASE_URL = 'https://staging-apis.dentalkart.com/';
export const API_STAGE_ADMIN_URL = 'https://dental-admin.dentalkart.com/';
export const FEEDS_API_BASE_URL = 'https://feeds-prod.dentalkart.com/';
export const X_API_KEY = 'ZFobrRyccnTyXyXHPUVO4eyyKEKoSjWB';
export const SECRETKEY = '2b7e151628aed2a6abf7168809cf2f3c';
export const IMAGE_BASE_URL = 'https://images.dentalkart.com/';
export const SUB_IMAGE_BASE_URL = 'https://images1.dentalkart.com';
export const REFERRAL_X_API_KEY = 'XUQVEomDnXBI5IaZabnujPkbS1rpPlSseG';
export const GoogleLoginConfigs = {
  webClientId:
    '285108782909-ouh6alnd146f1het75vn5lvim26tuvpm.apps.googleusercontent.com',
  iosClientId: '',
};
export const supportNumber = '+917289999459';
export const Version = Platform.OS === 'ios' ? '13.0.2' : '3.0.2';
export const clarity = 'pxd7kocwnf';
export const termsAndConditions =
  'https://www.dentalkart.com/terms-and-conditions';
export const privacyPolicy = 'https://www.dentalkart.com/privacy-policy';
export const supportEmail = '<EMAIL>';
export const phoneNumber = 7289999456;
export const videoThumbnails =
  'https://dentalkart-application-media.s3.ap-south-1.amazonaws.com/video_placeholder.png';
export const mapPlaceKey =
  Platform.OS === 'ios'
    ? 'AIzaSyBXqEoPsLFHCGRos6KIFzCiAy9q3OGxSGY'
    : 'AIzaSyCud5-OguWrMmC95SpA0TV2Iaz2wUXz7h8';
export const defaultPinCode = '110074';

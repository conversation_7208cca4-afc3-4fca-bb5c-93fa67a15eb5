import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const RewardZone = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1820}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="10" rx="5" ry="5" width={width - 20} height={250} />
        <Rect x="10" y="270" rx="5" ry="5" width={width - 20} height={100} />
        <Rect x="10" y="380" rx="5" ry="5" width={width - 20} height={100} />
        <Rect
          x={width / 2 - 100}
          y="490"
          rx="5"
          ry="5"
          width={200}
          height={30}
        />
        <Rect x="10" y="530" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="690" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="850" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1010" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1170" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1330" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1490" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1650" rx="5" ry="5" width={width - 20} height={150} />
      </ContentLoader>
    </ScrollView>
  );
};

export default RewardZone;

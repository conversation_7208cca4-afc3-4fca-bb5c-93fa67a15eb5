import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    binHeart: {
      flexDirection: 'row',
      position: 'absolute',
      zIndex: Sizes.xs,
      right: 0,
      top: 0,
      backgroundColor: colors.smoothBlue,
      padding: Sizes.s,
      borderRadius: Sizes.x6l,
      elevation: Sizes.m,
      alignItems: 'center',
      justifyContent: 'center',
    },
    binStyle: {
      width: Sizes.x6l + Sizes.s,
      justifyContent: 'space-between',
    },
  });

export default styles;

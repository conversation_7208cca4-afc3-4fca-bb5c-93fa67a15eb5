import Clipboard from '@react-native-community/clipboard';
import {RouteProp, useFocusEffect, useTheme} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {GetWishlistOutput, RootState} from '@types/local';
import Icons from 'common/icons';
import {ImageIcon, Label, ListView, Radio, Separator} from 'components/atoms';
import EmptyWishlist from 'components/atoms/emptyWishlist';
import Spacer from 'components/atoms/spacer';
import {But<PERSON>, EditWishList, Header, InputBox} from 'components/molecules';
import {AnalyticsEvents, WishListFooterModal} from 'components/organisms';
import {WEBSITE_URL} from 'config/environment';
import {t} from 'i18next';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  BackHandler,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  addFriendWishlist,
  getFriendWishlist,
  getWishlist,
  updateUserWishlist,
} from 'services/wishlist';
import {WishlistLoader} from 'skeletonLoader';
import ErrorHandler from 'utils/ErrorHandler';
import getImageUrl, {productDummyImage} from 'utils/imageUrlHelper';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {RootStackParamsList} from '../../routes';
import stylesWithOutColor from './style';
import {useSelector} from 'react-redux';
import { debugError, debugLog } from 'utils/debugLog';
import { deleteWishlistThunk } from 'app-redux-store/slice/appSlice';
import { useDispatch } from 'react-redux';
import { truncateText } from 'utils/utils';

const ModalContent = ({
  modalType,
  currentWishlist,
  setModalType,
  setVisible,
  onDeleteSubmit,
  styles,
  onSubmit,
  allWishlists,
  moveWishListItem,
}: {
  modalType:
    | 'wishlistOptions'
    | 'edit'
    | 'delete'
    | 'share'
    | 'productOptions'
    | 'shareProduct'
    | 'deleteProduct'
    | 'MoveWishListItem'
    | 'confirmDefault';
  currentWishlist: GetWishlistOutput;
}) => {
  const [values, setValues] = useState({
    wishlistName: currentWishlist?.title,
  });
  const [wishlistError, setWishlistError] = useState(false);

  let showVisibilityText =
    currentWishlist?.visibility == 'private' ? 'Public' : 'Private';

  let setVisibilityText =
    currentWishlist?.visibility == 'private' ? 'public' : 'private';

  const shareWishlistLink = `${WEBSITE_URL}account/wishlist/friend?id=`;

  const copyToClipboard = (textToCopy: String) => {
    Clipboard.setString(String(textToCopy));
    setVisible(false);
    showSuccessMessage(t('toastMassages.copyLinkText'));
    // onSubmit(currentWishlist?.title, setVisibilityText);
  };

  const otherListRenderItems = useCallback(
    ({item}: {item: GetWishlistOutput}) => {
      return (
        <TouchableOpacity
          onPress={() => {
            moveWishListItem(item.id);
          }}>
          <View>
            <View style={styles.currentListView}>
              <Radio
                selected={currentWishlist.id === item?.id}
                fillColor={'black'}
                borderColor="black"
                onPress={() => moveWishListItem(item.id)}
              />
              <Spacer size="xms" type="Vertical" />
              <View style={styles.wishlistNameView}>
                <Label
                  size="mx"
                  fontFamily="Medium"
                  color="text2"
                  text={item?.title}
                />
              </View>
            </View>
          </View>
          <Spacer size="xms" />
        </TouchableOpacity>
      );
    },
    [],
  );

  const onSaveWishlist = () => {
    if (values.wishlistName.trim().length === 0) {
      setWishlistError(true);
    } else {
      setWishlistError(false);
      onSubmit(values?.wishlistName);
    }
  };

  return (
    <>
      {modalType === 'productOptions' ? (
        <>
          {currentWishlist?.visibility !== 'private' ? (
            <>
              <Spacer size="xl" />
              <TouchableOpacity
                onPress={() => {
                  setModalType('shareProduct');
                  setVisible(true);
                }}>
                <View style={styles.footerView}>
                  <Label
                    color="text2"
                    text={t('wishList.shareText')}
                    size="mx"
                    fontFamily="Medium"
                  />
                  <ImageIcon size="xx" icon="share" tintColor="text2" />
                </View>
              </TouchableOpacity>
            </>
          ) : (
            <Spacer size="xms" type="Vertical" />
          )}

          {/* edit wishlist options */}
          <Spacer size="xms" type="Vertical" />
          <Separator style={styles.catSep} />
          <TouchableOpacity
            onPress={() => {
              setModalType('edit');
              setVisible(true);
            }}>
            <View style={styles.footerView}>
              <Label
                size="mx"
                color="text2"
                text={t('wishList.rename')}
                fontFamily="Medium"
              />
              <ImageIcon size="mx" icon="wishListEditIcon" tintColor="text2" />
            </View>
          </TouchableOpacity>

          {!currentWishlist?.is_default && <><Spacer size="xms" type="Vertical" /><Separator style={styles.catSep} /><TouchableOpacity
            onPress={() => {
              setModalType('deleteProduct');
              setVisible(true);
            } }>
            <View style={styles.footerView}>
              <Label
                size="mx"
                fontFamily="Medium"
                text={t('wishList.delete')}
                color="text2" />
              <ImageIcon size="m" icon="cancel" tintColor="text2" />
            </View>
          </TouchableOpacity></>}
          <Spacer size="xms" type="Vertical" />
          <Separator style={styles.catSep} />

          {/* make private or setDefault */}
          {!currentWishlist?.is_default && (
            <>
              <TouchableOpacity
                onPress={() => {
                  setModalType('confirmDefault');
                  setVisible(true);
                }}>
                <View style={styles.footerView}>
                  <Label
                    size="mx"
                    color="text2"
                    text={t('otherText.default')}
                    fontFamily="Medium"
                  />
                  <ImageIcon size="l" icon="noteList" tintColor="text2" />
                </View>
              </TouchableOpacity>

              <Spacer size="xms" type="Vertical" />
              <Separator style={styles.catSep} />
            </>
          )}

          <TouchableOpacity
            onPress={() => onSubmit(currentWishlist?.title, setVisibilityText)}>
            <View style={styles.footerView}>
              <Label
                size="mx"
                color="text2"
                text={`${t('wishList.make')} ${showVisibilityText}`}
                fontFamily="Medium"
              />
              <ImageIcon size="l" icon="lock1" tintColor="text2" />
            </View>
          </TouchableOpacity>

          <Spacer size="xms" type="Vertical" />
          <Separator style={styles.catSep} />
        </>
      ) : null}
      {modalType === 'shareProduct' ? (
        <>
          <Spacer size="xxl" type="Vertical" />
          <View style={styles.footerView}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.shareWishList')}
            />
          </View>
          <View style={styles.listNameView}>
            <Separator style={styles.catSep} />
            <Spacer size="xxl" />
            <InputBox
              style={styles.modelInputView}
              textInputColor={styles.modelInputBox}
              editable={false}
              value={shareWishlistLink + currentWishlist?.id}
              multiline={true}
              numberOfLines={3}
            />
            <Spacer size="xms" />
            <Button
              type="primary"
              onPress={() =>
                copyToClipboard(shareWishlistLink + currentWishlist?.id)
              }
              text={t('buttons.copy')}
              radius="sx"
              size="large"
              labelSize="l"
              labelColor="whiteColor"
              style={styles.modelBtn}
              labelStyle={styles.btnLabel}
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'edit' ? (
        <>
          <Spacer size="l" type="Vertical" />
          <View style={styles.footerView}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.editWishList')}
            />
          </View>
          <View style={styles.listNameView}>
            <Spacer size="xms" type="Vertical" />
            <Separator style={styles.catSep} />
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.yourLastName')}
            />
            <Spacer size="xm" />
            <View style={styles.a1}>
              <InputBox
                style={styles.input}
                textInputColor={styles.modelEditInputBox}
                onChangeText={text =>
                  setValues(prev => {
                    const name = text.trimStart();
                    if (name.length > 0) {
                      setWishlistError(false);
                    }
                    return {...prev, wishlistName: name};
                  })
                }
                value={values?.wishlistName}
                keyboardType="name-phone-pad"
                maxLength={20}
              />
              <Label
                style={styles.rightText}
                text={
                  values?.wishlistName?.length == undefined || 0
                    ? '0/20'
                    : `${values?.wishlistName?.trimStart()?.length}/20`
                }
                color="grey2"
              />
            </View>
            {wishlistError && (
              <Label
                color="textError"
                text={t('validations.wishListNameRequired')}
                size="m"
                weight="400"
              />
            )}
            <Button
              labelStyle={styles.buttonText}
              type="primary"
              text={t('buttons.confirm')}
              style={styles.modelBtn}
              onPress={() => onSaveWishlist()}
              labelSize="l"
              size="large"
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'deleteProduct' ? (
        <>
          <View style={styles.sureWantView}>
            <View style={styles.sureView}>
              <Label
                size="mx"
                color="text2"
                text={t('wishList.deleteWishListMsg')}
                fontFamily="Medium"
                align="center"
              />
            </View>
            <Spacer size="xms" type="Vertical" />
            <Button
              type="primary"
              onPress={() => {
                onDeleteSubmit();
              }}
              text={t('wishList.delete')}
              radius="sx"
              size="large"
              labelSize="l"
              labelColor="whiteColor"
              style={styles.modelBtn}
              labelStyle={styles.buttonText}
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'confirmDefault' ? (
        <>
          <View style={styles.sureWantView}>
            <View style={styles.sureView}>
              <Label
                size="mx"
                color="text2"
                text={`${t('wishList.confirmDefault')} ${
                  currentWishlist?.title
                } ${t('wishList.confirmDefault1')}`}
                fontFamily="Medium"
                align="center"
              />
            </View>
            <Spacer size="xxxl" type="Vertical" />
            <Button
              labelStyle={styles.buttonText}
              type="primary"
              text={t('buttons.confirm')}
              style={styles.modelBtn}
              onPress={() => onSubmit(currentWishlist?.title, null, true)}
              labelSize="l"
              size="large"
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'MoveWishListItem' ? (
        <>
          <Spacer size="l" type="Vertical" />

          <View style={styles.footerView}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.moveAnotherWish')}
            />
            <Spacer size="l" type="Vertical" />
          </View>
          <View style={styles.allWishlistView}>
            <Separator style={styles.catSep} />

            <FlatList
              data={allWishlists}
              renderItem={otherListRenderItems}
              keyExtractor={(_, i) => i.toString()}
            />
            <Spacer size="x3l" type="Vertical" />
          </View>
        </>
      ) : null}
    </>
  );
};

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'WishList'>;
};

const WishListScene = ({navigation, route}: Props) => {
  const TAG = 'WhishListScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [wishlist, setWishlist] = useState<GetWishlistOutput[] | []>([]);
  const [friendWishlist, setFriendWishlist] = useState<
    GetWishlistOutput[] | []
  >([]);
  const [currentTab, setCurrentTab] = useState(route?.params?.pages || 'My Lists');
  const [visible, setVisible] = useState(false);
  const [modalType, setModalType] = useState<
    'wishlistOptions' | 'edit' | 'delete' | 'share' | 'productOptions'
  >('wishlistOptions');
  const [allWishlists, setAllWishlists] = useState<GetWishlistOutput[] | []>(
    [],
  );

  const [currentWishlist, setCurrentWishlist] = useState<
    GetWishlistOutput | {} | ''
  >({});

  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const {isLoggedIn, userInfo, wishlists} = useSelector((state: RootState) => state.app);

  useFocusEffect(
    useCallback(() => {
      const loadData = async () => {
        
        try {
          const wishlistIds = Object.values(wishlists).map(wishlist => wishlist.id);
          const routeId = route?.params?.id;
          
          // Check if we have a route ID and if it's not already in our wishlists
          if (routeId && !wishlistIds.includes(routeId)) {
            setLoading(true)
            await addFriendWishlists(routeId);
            setLoading(false);
          }
          if (routeId && wishlistIds.includes(routeId)) {
            navigation.setParams({ 
              ...route.params, 
              id: ''
            });
            setCurrentTab('My Lists')
          }
          if (currentTab === 'Friend') {
            await getFriendWishlistData();
          } else {
            await wishListData();
          }
        } catch (error) {
          console.error("Error loading data:", error); 
        }
      };
  
      loadData();
      
    }, [currentTab, route?.params?.id, wishlists]),
  );
  const dispatch = useDispatch()

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      // Check if the user is going back using the slide-back gesture (iOS Specific)
      if ((e.data.action.type === 'GO_BACK' || e.data.action.type === "POP") && typeof route.params?.goBack === 'function') {
        route.params?.goBack();
      }
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    AnalyticsEvents('WISHLIST', 'Wishlist viewed', {}, userInfo, isLoggedIn);
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack && typeof route.params?.goBack === 'function') {
      route.params.goBack();
    }
    return true;
  };
  // ================= Product  Image =========================
  const getFriendWishlistData = async () => {
    setLoader(true);
    const {data, status} = await getFriendWishlist();
    if (data && status) {
      setFriendWishlist(data?.friend_wishlists);
    }
    setLoader(false);
  };

  const addFriendWishlists = async (id : string) => {
    setLoader(true);
    const {data, status} = await addFriendWishlist(id);
    debugLog("status",status);
    setLoader(false);
  };

  const wishListData = async () => {
    setLoader(true);
    let wishlistData: any = await getWishlist();
    let data: any = wishlistData?.data;
    setLoader(false);
    if (data?.wishlists && data?.wishlists?.length) {
      setWishlist(data?.wishlists);
      setCurrentWishlist(data?.wishlists?.find(e => e.id));
      setAllWishlists(data?.wishlists);
    }

    let productIds = [];
    data?.wishlists?.forEach(wishlist => {
      if (wishlist.products) {
        wishlist.products.forEach(product => {
          productIds.push(product.product_id);
        });
      }
    });
  };

  const renderItems = useCallback(
    ({item, index}: {item: GetWishlistOutput; index: number}) => {
      return (
        <View
          key={index}
          style={[
            styles.mainProductView,
            {marginRight: index % 2 == 0 ? 8 : 0},
          ]}>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('UserWishlist', {
                updateData: async () => await wishListData(),
                wishlist_id: item?.id,
                wishlists: wishlist,
                productId: item?.items?.flatMap(x => x?.product_id),
              });
            }}
            style={styles.subViewProduct}>
            <View style={styles.topSection}>
              <View style={styles.wishlistCardHeadingView}>
                <Label
                  text={truncateText(item.title,13)}
                  size="mx"
                  fontFamily="Medium"
                  color="text2"
                />
                <View style={styles.productView}>
                  <Label
                    text={`${t('wishList.items')} - ${
                      item?.items?.length > 0 ? item?.items?.length : 0
                    }`}
                    size="xms"
                    color="text2"
                    fontFamily="Medium"
                    textTransform="capitalize"
                  />
                  {item.is_default === true ? (
                    <>
                      <Spacer type="Horizontal" size="xm" />
                      <View style={styles.horizontalLine} />
                      <Spacer type="Horizontal" size="xm" />
                      <Label
                        text={t('otherText.es')}
                        size="xms"
                        color="text2"
                        fontFamily="Medium"
                      />
                    </>
                  ) : null}
                </View>
              </View>
              <TouchableOpacity
                onPress={(e) => {
                  e.stopPropagation();
                  setModalType('productOptions');
                  setVisible(true);
                  setCurrentWishlist(item);
                }}
                style={styles.threeDotView}
                >
                <ImageIcon icon="moreVerticalIcon" size="xx" style={styles.threeDotImageView} />
              </TouchableOpacity>
            </View>

            <View style={styles.fastImage}>
              {item?.items?.length > 0 ? (
                <>
                  {item.items?.slice(0, 4).map(item => {
                    return (
                      <View style={styles.products}>
                        <FastImage
                          resizeMode="contain"
                          key={index}
                          style={styles.img}
                          onError={e => {
                            e.target.uri = productDummyImage;
                          }}
                          source={{
                            uri: getImageUrl(item?.media?.mobile_image || ''),
                          }}
                        />
                      </View>
                    );
                  })}
                </>
              ) : (
                <View style={styles.emptyView}>
                  <Label
                    text={t('wishList.noProductsAvailable')}
                    size="xx"
                    color="blankGray"
                    fontFamily="Medium"
                    align="center"
                  />
                </View>
              )}
            </View>
            <LinearGradient
              style={styles.gradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 1}}
              colors={
                item.visibility === 'public'
                  ? [
                      colors.mandarianOrange,
                      colors.coralOrange,
                      colors.mandarianOrange,
                    ]
                  : [
                      colors.prussianBlue,
                      colors.cobaltBlue,
                      colors.prussianBlue,
                    ]
              }>
              <Spacer type="Horizontal" size="s" />

              <ImageIcon
                tintColor="whiteColor"
                size="xms"
                icon={item.visibility === 'public' ? 'groupUsers' : 'lockIcon'}
              />
              <Spacer type="Horizontal" size="s" />
              <Label
                color="whiteColor"
                fontFamily="Medium"
                size="m"
                text={item.visibility}
                textTransform="capitalize"
                style={styles.statusStyle}
              />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      );
    },
    [],
  );

  const renderFriendItems = useCallback(
    ({item, index}: {item: GetWishlistOutput; index: number}) => {
      return (
        <View
          style={[
            styles.mainProductView,
            {marginRight: index % 2 == 0 ? 8 : 0},
          ]}>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('UserWishlist', {
                updateData: () => wishListData(),
                friendData: () => getFriendWishlistData(),
                wishlist_id: item?.id,
                wishlists: friendWishlist,
                productId: item?.items?.flatMap(x => x?.product_id),
                defaultTab: 'Friend',
              });
            }}
            style={styles.subViewProduct}>
            <View style={styles.shareTopSection}>
              <View style={styles.shareHeadingView}>
                <Label
                  text={item.friend_name}
                  size="mx"
                  fontFamily="Medium"
                  color="text2"
                  style={styles.flex}
                  textTransform="capitalize"
                />
                <View style={styles.crossIconView}>
                  <TouchableOpacity
                    onPress={() => {
                      setModalType('deleteProduct');
                      setVisible(true);
                      setCurrentWishlist({...item, friend: true});
                    }}>
                    <ImageIcon icon="cross" size="xl" />
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.productView}>
                <Label
                  text={`${t('wishList.items')} - ${
                    item?.wishlist?.items?.length
                      ? item?.wishlist?.items?.length
                      : 0
                  }`}
                  size="xms"
                  color="text2"
                  fontFamily="Medium"
                  textTransform="capitalize"
                />
                {item.is_default === true && (
                  <>
                    <Spacer type="Horizontal" size="xms" />
                    <View style={styles.horizontalLine} />
                    <Spacer type="Horizontal" size="xms" />
                    <Label
                      text={
                        item.is_default === true ? t('wishList.default') : ''
                      }
                      size="xms"
                      color="text2"
                      fontFamily="Medium"
                    />
                  </>
                )}
              </View>
            </View>

            <View style={styles.fastImage}>
              {item?.wishlist?.items?.length > 0 ? (
                <>
                  {item?.wishlist?.items?.slice(0, 4).map(item => {
                    return (
                      <View style={styles.products}>
                        <ErrorHandler
                          componentName={`${TAG} FastImage`}
                          onErrorComponent={<View />}>
                          <FastImage
                            resizeMode="contain"
                            key={index}
                            style={styles.img}
                            onError={e => {
                              e.target.uri = productDummyImage;
                            }}
                            source={{
                              uri: getImageUrl(item?.media?.mobile_image || ''),
                            }}
                          />
                        </ErrorHandler>
                      </View>
                    );
                  })}
                </>
              ) : (
                <View style={styles.emptyView}>
                  <Label
                    text={t('wishList.noProductsAvailable')}
                    size="xl"
                    color="blankGray"
                    fontFamily="Medium"
                  />
                </View>
              )}
            </View>
            <LinearGradient
              style={styles.gradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 1}}
              colors={[
                colors.resolutionBlue,
                colors.royalBlue,
                colors.resolutionBlue,
              ]}>
              <Spacer type="Horizontal" size="s" />
              <ErrorHandler
                componentName={`${TAG} FastImage`}
                onErrorComponent={<View />}>
                <FastImage
                  tintColor="whiteColor"
                  style={styles.grpImage}
                  source={Icons.groupIcon}
                />
              </ErrorHandler>
              <Spacer type="Horizontal" size="s" />
              <Label
                color="whiteColor"
                fontFamily="Medium"
                size="m"
                text={t('wishList.shared')}
                textTransform="capitalize"
                style={styles.statusStyle}
              />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      );
    },
    [],
  );

  const deleteWishListItem = async () => {
    setLoading(true);
    
    try {
      // Use the Redux thunk to handle the deletion and state update
      const result = await dispatch(
        deleteWishlistThunk({
          wishlistId: currentWishlist.id,
          isFriendWishlist: !!currentWishlist.friend
        })
      ).unwrap();
      
      if (result.status) {
        setVisible(false);
        
        // If it's a friend wishlist, you might need to refresh that data separately
        if (currentWishlist.friend) {
          await getFriendWishlistData();
        }
        await wishListData();
      }
    } catch (error) {
      debugError('Error in deleteWishListItem:', error);
      showErrorMessage(t('validations.someThingWrong'));
    } finally {
      setLoading(false);
    }
  };

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const editWishListItem = async (
    title: String,
    visibility?: boolean,
    type?: boolean,
  ) => {
    if (title?.trim().length !== 0) {
      const payloadData = {
        title: title,
        visibility: visibility || currentWishlist.visibility,
      };
      if (type === false || type === true) {
        payloadData.is_default = type;
      }
      setLoading(true);
      const response = await updateUserWishlist(
        currentWishlist.id,
        payloadData,
      );
      setLoading(false);
      if (response?.status && response?.data) {
        wishListData();
        setVisible(false);
        showSuccessMessage(t('toastMassages.updateSuccess'));
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          text={t('wishList.wishList')}
          backButton={true}
          navigation={navigation}
          searchIcon={true}
          bagIcon={true}
          onPressNavigation={() => {
            navigation.goBack();
            if (route.params?.goBack) {
              route.params?.goBack();
            }
          }}
        />
      </ErrorHandler>
      <View style={styles.createView}>
        <ErrorHandler
          componentName={`${TAG} EditWishList`}
          onErrorComponent={<View />}>
          <EditWishList
            tab={currentTab}
            modalType="create"
            onUpdate={wishListData}
          />
        </ErrorHandler>
      </View>
      <View style={styles.fRow}>
        <ErrorHandler
          componentName={`${TAG} Button`}
          onErrorComponent={<View />}>
          <Button
            type={currentTab === 'My Lists' ? 'Primary' : 'bulk'}
            style={[
              styles.tabBtn,
              {
                backgroundColor:
                  currentTab === 'My Lists'
                    ? colors.categoryTitle
                    : colors.whiteColor,
              },
            ]}
            onPress={() => {
              setCurrentTab('My Lists');
              wishListData();
            }}
            text={t('wishList.myLists')}
            labelStyle={[
              styles.tabLabel,
              {
                color:
                  currentTab === 'My Lists' ? colors.background : colors.text2,
              },
            ]}
            labelSize="l"
          />
        </ErrorHandler>
        <ErrorHandler
          componentName={`${TAG} Button`}
          onErrorComponent={<View />}>
          <Button
            type={currentTab === 'Friend' ? 'Primary' : 'bulk'}
            style={[
              styles.tabBtn,
              {
                backgroundColor:
                  currentTab === 'Friend'
                    ? colors.categoryTitle
                    : colors.whiteColor,
              },
            ]}
            onPress={() => {
              setCurrentTab('Friend');
              getFriendWishlistData();
            }}
            text={t('wishList.shareWithMe')}
            labelStyle={[
              styles.tabLabel,
              {
                color:
                  currentTab === 'Friend' ? colors.background : colors.text2,
              },
            ]}
            labelSize="l"
          />
        </ErrorHandler>
      </View>
      {loader ? (
        <WishlistLoader />
      ) : (
        <View style={styles.flex}>
          <Spacer type="Vertical" size="xm" />
          {/* <View style={styles.list}>
            <Label
              size="l"
              text={
                currentTab === 'My Lists'
                  ? t('wishList.yourList')
                  : t('wishList.shared')
              }
              fontFamily="SemiBold"
              color="categoryTitle"
            />
          </View>
          <Spacer type="Vertical" size="xm" /> */}
          <View style={styles.container}>
            {currentTab === 'My Lists' && (
              <>
                <ErrorHandler
                  componentName={`${TAG} ListView`}
                  onErrorComponent={<View />}>
                  <ListView
                    style={styles.listStyle}
                    numColumns={2}
                    keyExtractor={listViewKeyExtractor}
                    data={wishlist}
                    renderItem={renderItems}
                    ItemSeparatorComponent={() => (
                      <Spacer type="Vertical" size="l" />
                    )}
                    ListEmptyComponent={
                      loading ? (
                        <View style={styles.loading}>
                          <ActivityIndicator
                            size={'large'}
                            color={colors.primary}
                          />
                        </View>
                      ) : (
                        <EmptyWishlist />
                      )
                    }
                  />
                </ErrorHandler>
                {visible ? (
                  <ErrorHandler
                    componentName={`${TAG} WishListFooterModal`}
                    onErrorComponent={<View />}>
                    <WishListFooterModal
                      visible={visible}
                      onClose={setVisible}
                      Content={
                        <ModalContent
                          modalType={modalType}
                          currentWishlist={currentWishlist}
                          setModalType={setModalType}
                          setVisible={setVisible}
                          // editWishList={editWishList}
                          onSubmit={editWishListItem}
                          styles={styles}
                          onDeleteSubmit={deleteWishListItem}
                          allWishlists={allWishlists}
                          // moveWishListItem={moveWishListItem}
                        />
                      }
                    />
                  </ErrorHandler>
                ) : null}
              </>
            )}
            {currentTab === 'Friend' && (
              <>
                <ErrorHandler
                  componentName={`${TAG} ListView`}
                  onErrorComponent={<View />}>
                  <ListView
                    style={styles.listStyle}
                    numColumns={2}
                    keyExtractor={listViewKeyExtractor}
                    data={friendWishlist}
                    renderItem={renderFriendItems}
                    ItemSeparatorComponent={() => (
                      <Spacer type="Vertical" size="l" />
                    )}
                    ListEmptyComponent={
                      loading ? (
                        <View style={styles.loading}>
                          <ActivityIndicator
                            size={'large'}
                            color={colors.primary}
                          />
                        </View>
                      ) : (
                        <ErrorHandler
                          componentName={`${TAG} EmptyWishlist`}
                          onErrorComponent={<View />}>
                          <EmptyWishlist
                            title={t('wishList.noShareWishlist')}
                            hideIcon={true}
                          />
                        </ErrorHandler>
                      )
                    }
                  />
                </ErrorHandler>

                {visible === true ? (
                  <ErrorHandler
                    componentName={`${TAG} WishListFooterModal`}
                    onErrorComponent={<View />}>
                    <WishListFooterModal
                      visible={visible}
                      onClose={setVisible}
                      Content={
                        <ModalContent
                          modalType={modalType}
                          currentWishlist={currentWishlist}
                          setModalType={setModalType}
                          setVisible={setVisible}
                          // editWishList={editWishList}
                          onSubmit={editWishListItem}
                          styles={styles}
                          onDeleteSubmit={deleteWishListItem}
                          allWishlists={allWishlists}
                          // moveWishListItem={moveWishListItem}
                        />
                      }
                    />
                  </ErrorHandler>
                ) : null}
              </>
            )}
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default WishListScene;

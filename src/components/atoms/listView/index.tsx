import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {FlatList, View, ViewProps} from 'react-native';
import styles from './style';

type Props = {
  renderItem: ({
    item,
    index,
  }: {
    item: any;
    index: number;
  }) => React.ReactElement | null;
  keyExtractor: ({
    item,
    index,
  }: {
    id: string;
    item: any;
    index: number;
  }) => string;
  scrollEnabled?: boolean;
  data: any[];
  extraData?: any[];
  style?: ViewProps['style'];
  numColumns?: number | undefined;
  horizontal?: boolean;
  ListHeaderComponent?: React.ReactElement | undefined;
  ListFooterComponent?: React.ReactElement | undefined;
  ItemSeparatorComponent?: React.ComponentType | undefined;
  ListEmptyComponent?: React.ReactElement | undefined;
  stickyHeaderIndices?: React.ReactElement | undefined;
  contentContainerStyle?: any;
  ListFooterComponentStyle?: any;
  onEndReached?: any;
  ref?: any;
  maxToRenderPerBatch?: number;
  scrollEventThrottle?: number;
  onEndReachedThreshold?: number;
  showsVerticalScrollIndicator?: boolean;
  showsHorizontalScrollIndicator?: boolean;
};
const ListView = ({
  renderItem,
  scrollEnabled,
  data,
  extraData,
  style,
  numColumns,
  horizontal,
  ListHeaderComponent,
  ListFooterComponent,
  ListFooterComponentStyle,
  ItemSeparatorComponent,
  showsVerticalScrollIndicator = false,
  showsHorizontalScrollIndicator = false,
  ListEmptyComponent,
  keyExtractor,
  stickyHeaderIndices,
  contentContainerStyle,
  onEndReached,
  maxToRenderPerBatch = 5,
  scrollEventThrottle,
  onEndReachedThreshold,
  ...rest
}: Props) => {
  return (
    <View style={style}>
      <FlatList
        ListFooterComponent={ListFooterComponent}
        ListFooterComponentStyle={ListFooterComponentStyle}
        contentContainerStyle={contentContainerStyle}
        extraData={extraData}
        stickyHeaderIndices={stickyHeaderIndices}
        ListHeaderComponentStyle={styles.listHeaderStyle}
        ItemSeparatorComponent={ItemSeparatorComponent}
        showsVerticalScrollIndicator={showsVerticalScrollIndicator}
        showsHorizontalScrollIndicator={showsHorizontalScrollIndicator}
        numColumns={numColumns}
        horizontal={horizontal}
        ListHeaderComponent={ListHeaderComponent}
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        scrollEnabled={scrollEnabled}
        ListEmptyComponent={ListEmptyComponent}
        onEndReached={onEndReached}
        maxToRenderPerBatch={maxToRenderPerBatch}
        scrollEventThrottle={scrollEventThrottle}
        onEndReachedThreshold={onEndReachedThreshold}
        {...rest}
        removeClippedSubviews={true}
        windowSize={5}
        updateCellsBatchingPeriod={50}
      />
    </View>
  );
};

export default ListView;

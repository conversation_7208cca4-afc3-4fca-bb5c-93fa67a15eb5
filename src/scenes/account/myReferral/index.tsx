import React, {useCallback, useEffect, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Header} from 'components/molecules';
import {RootStackParamsList} from 'routes';
import {useTheme, RouteProp} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {ImageIcon, Label, Link, Spacer} from 'components/atoms';
import {
  FlatList,
  Pressable,
  TouchableOpacity,
  View,
  ImageBackground,
  ViewProps,
  BackHandler,
  Share,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import Clipboard from '@react-native-community/clipboard';
import {t} from 'i18next';
import LinearGradient from 'react-native-linear-gradient';
import {getMyReferral, getReferrals} from 'services/account';
import {useDispatch} from 'react-redux';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {ReferEarn} from 'skeletonLoader';
import {socialData} from 'staticData';
import {checkDevice, handleErrMsg} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import {debugLog} from 'utils/debugLog';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'MyReferral'>;
};

const MyReferralScene = ({navigation, route}: Props) => {
  const TAG = 'MyReferralScreen';
  const dispatch = useDispatch();
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [refersCount, setRefersCount] = useState('');
  const [refersRecord, setRefersRecord] = useState('');
  const [loading, setLoading] = useState(false);

  const getReferralContes = useCallback(async () => {
    let {data, status} = await getMyReferral();
    if (data && status) {
      setRefersCount(data);
    }
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const getReferralRecord = async () => {
    let {status, data} = await getReferrals({refer_type: 'USER'});
    if (data && status) {
      setRefersRecord(data);
    } else {
      showErrorMessage(handleErrMsg(data));
    }
  };

  const copyToClipboard = (data: string) => {
    Clipboard.setString(data);
    showSuccessMessage(t('toastMassages.codeCopiedText'));
  };

  // const referralShare = async (url: any, socialName: string) => {
  //   try {
  //     if (socialName === 'INSTAGRAM') {
  //       const {config, fs} = RNFetchBlob;
  //       const filePath = `${fs.dirs.CacheDir}/temp_image.jpg`;
  //       await config({fileCache: true, path: filePath}).fetch('GET', url);
  //       const shareOptions = {
  //         url: `file://${filePath}`,
  //         type: 'image/jpeg',
  //         social: Share.Social.INSTAGRAM,
  //       };
  //       const shareResponse = await Share.shareSingle(shareOptions);
  //     }
  //     const shareResponse = await Share.shareSingle({
  //       url: url,
  //       social: Share.Social[socialName],
  //     });
  //   } catch (error) {
  //     ToastAndroid.show(t('toastMassages.socialMsg'), ToastAndroid.SHORT);
  //   }
  // };

  const onShare = () => {
    Share.share(
      {
        message: referLink,
      },
      {dialogTitle: 'Share on ..', tintColor: 'green'},
    ).catch(err => debugLog(err));
  };
  useEffect(() => {
    setLoading(true);
    Promise.all([getReferralContes(), getReferralRecord()]).then(() => {
      setLoading(false);
    });
  }, []);

  const renderLabel = (
    text: string,
    color: string,
    size: string,
    font: string,
    style?: ViewProps['style'],
  ) => {
    return (
      <Label
        fontFamily={font}
        size={size}
        color={color}
        text={text}
        style={style}
      />
    );
  };

  const referLink = refersRecord?.onelink_url
    ? refersRecord?.onelink_url?.replace('/checkout', '')
    : '';
  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton
          navigation={navigation}
          searchIcon={true}
          bagIcon={true}
          text={t('myReferral.refer')}
          onPressNavigation={() => {
            navigation.goBack();
            if (route.params?.goBack) {
              route.params?.goBack();
            }
          }}
        />
      </ErrorHandler>
      <Spacer size="mx" />
      {loading ? (
        <ReferEarn />
      ) : (
        <FlatList
          style={styles.subContainer}
          showsVerticalScrollIndicator={false}
          data={['']}
          renderItem={() => (
            <>
              <ErrorHandler
                componentName={`${TAG} MyReferralList`}
                onErrorComponent={<View />}>
                <LinearGradient
                  colors={[colors.blackberry, colors.blackRussian]}
                  start={{x: 0.2549, y: 0.2614}}
                  end={{x: 0.5751, y: 0.739}}
                  locations={[0.3569, 1]}
                  style={styles.linearGradient}>
                  <ImageBackground
                    source={Icons.referralBg}
                    style={[
                      styles.bottomLineBg,
                      checkDevice() && styles.tabImg,
                    ]}>
                    <View style={checkDevice() && styles.cardView}>
                      <View style={styles.referView}>
                        {!checkDevice() && (
                          <>
                            <Spacer size="mx" />
                            <FastImage
                              source={Icons.giftIcon}
                              style={styles.giftbox}
                              resizeMode="contain"
                            />
                          </>
                        )}
                        <Spacer size="mx" />
                        <View>
                          <Label
                            align={checkDevice() ? 'left' : 'center'}
                            fontFamily="Bold"
                            size="xxl"
                            lineHeight="x3l"
                            color="whiteColor"
                            text={t('myReferral.referRewards')}
                          />
                        </View>
                        <Spacer size="m" />
                        <Label
                          fontFamily="Medium"
                          size={checkDevice() ? 'xx' : 'm'}
                          color="whiteColor"
                          text={t('myReferral.introduce')}
                          align={checkDevice() ? 'left' : 'center'}
                        />
                        <Spacer size="l" />
                        <Label
                          align={checkDevice() ? 'left' : 'center'}
                          size={checkDevice() ? 'mx' : 'm'}
                          text={t('myReferral.referralLink')}
                          color="whiteColor"
                          fontFamily="Medium"
                        />
                        <Spacer size="xm" />
                        <View style={styles.linkBox}>
                          <View style={styles.linkStyle}>
                            <Link
                              color="text2"
                              numberOfLines={1}
                              fontFamily="Medium"
                              weight="400"
                              size={checkDevice() ? 'mx' : 'm'}
                              text={referLink?.substr(0, 50)}
                            />
                          </View>
                          <Pressable
                            onPress={() => copyToClipboard(referLink)}
                            style={styles.copyTeg}>
                            <Label
                              size={checkDevice() ? 'mx' : 'm'}
                              text={t('buttons.copy')}
                              color="whiteColor"
                              fontFamily="Medium"
                            />
                          </Pressable>
                        </View>
                        <Spacer size="l" />
                        <Label
                          align={checkDevice() ? 'left' : 'center'}
                          size={checkDevice() ? 'mx' : 'm'}
                          fontFamily="Medium"
                          text={t('myReferral.referralCode')}
                          color="whiteColor"
                        />
                        <Spacer size="sx" />
                        <TouchableOpacity
                          style={[
                            styles.referCodeView,
                            checkDevice() && styles.alignSelfStart,
                          ]}
                          onPress={() =>
                            copyToClipboard(refersRecord?.referral_code)
                          }>
                          <Label
                            align="center"
                            style={styles.underText}
                            color="whiteColor"
                            fontFamily="Medium"
                            size="xsl"
                            text={refersRecord?.referral_code}
                          />
                          <View style={styles.referralLink}>
                            <Spacer type="Horizontal" size="xm" />
                            <FastImage
                              tintColor={colors.background}
                              source={Icons.copy1}
                              style={styles.newCopy}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                      {checkDevice() && (
                        <View style={styles.cardView}>
                          <FastImage
                            source={Icons.giftIcon}
                            style={styles.giftbox1}
                            resizeMode="contain"
                          />
                        </View>
                      )}
                    </View>
                  </ImageBackground>
                </LinearGradient>
                <Spacer size="l" />
                <View style={styles.shareText}>
                  <Label
                    align="center"
                    fontFamily="Medium"
                    size={checkDevice() ? 'xx' : 'mx'}
                    color="grey"
                    text={t('myReferral.shareReferral')}
                  />
                </View>
                <Spacer size="xl" />
                <View style={styles.or}>
                  <View style={styles.lineCut} />
                  <Label
                    size={checkDevice() ? 'xx' : 'm'}
                    style={styles.lineSpacer}
                    color="grey"
                    text={t('myReferral.alsoShareVia')}
                    fontFamily="Medium"
                  />
                  <View style={styles.lineCut} />
                </View>
                <Spacer size="x5l" />
                <View style={styles.dericationView}>
                  <FlatList
                    horizontal={true}
                    showsHorizontalScrollIndicator={false}
                    data={socialData}
                    contentContainerStyle={styles.listStyle}
                    renderItem={({
                      item,
                      index,
                    }: {
                      item: SocialIcon;
                      index: number;
                    }) => {
                      return (
                        <TouchableOpacity
                          key={index}
                          // onPress={() => referralShare(referLink, item?.type)}>
                          onPress={() => onShare()}>
                          <ImageIcon size="x42" icon={item?.icon} />
                        </TouchableOpacity>
                      );
                    }}
                  />
                </View>
                <Spacer size="xxl" />
                <View style={styles.or}>
                  <View style={styles.lineCut} />
                  <Label
                    size={checkDevice() ? 'xx' : 'm'}
                    style={styles.lineSpacer}
                    text={t('myReferral.yourActivity')}
                    fontFamily="Medium"
                    color="grey"
                  />
                  <View style={styles.lineCut} />
                </View>
                <Spacer size="m" />
                <View style={styles.referBoxView}>
                  <View
                    style={[
                      styles.referBoxLeft,
                      checkDevice() && styles.flexOne,
                    ]}>
                    <View
                      style={
                        checkDevice()
                          ? styles.mainReferBox1
                          : styles.mainReferBox
                      }>
                      <ImageIcon
                        resizeMode="contain"
                        size={checkDevice() ? 'xxl' : 'l'}
                        sourceType="local"
                        icon="imageGoldCoinIcon"
                      />
                      {checkDevice() && <Spacer size="m" type="Horizontal" />}
                      {checkDevice()
                        ? renderLabel(
                            t('myReferral.coinsEarned'),
                            'text2',
                            'xxl',
                            'SemiBold',
                          )
                        : renderLabel(
                            refersCount?.rewardsCoinsEarned || 0,
                            'text2',
                            'x5l',
                            'SemiBold',
                            styles.earnText,
                          )}
                      {checkDevice() && <Spacer size="m" type="Horizontal" />}
                      {checkDevice()
                        ? renderLabel(
                            refersCount?.rewardsCoinsEarned || 0,
                            'text2',
                            'x44',
                            'SemiBold',
                          )
                        : renderLabel(
                            t('myReferral.coinsEarned'),
                            'text2',
                            'm',
                            'SemiBold',
                          )}
                    </View>
                  </View>
                  <View style={styles.referBoxRight}>
                    <View
                      style={[
                        styles.referBox,
                        checkDevice() && styles.tabBoxSize,
                      ]}>
                      <Label
                        fontFamily="SemiBold"
                        size={checkDevice() ? 'xxl' : 'm'}
                        color="text2"
                        text={t('myReferral.productsReferred')}
                        style={styles.referTxt}
                      />
                      <Label
                        color="text2"
                        fontFamily="SemiBold"
                        size="x5l"
                        text={
                          refersCount?.referredCount?.find(
                            e => e.refer_type === 'PRODUCT',
                          )?.count !== undefined
                            ? refersCount.referredCount
                                .find(e => e.refer_type === 'PRODUCT')
                                .count.toString()
                                .padStart(2, '0')
                            : '00'
                        }
                        style={styles.productText}
                      />
                    </View>
                    <Spacer type="Vertical" size="xms" />
                    <View
                      style={[
                        styles.referBox,
                        checkDevice() && styles.tabBoxSize,
                      ]}>
                      <Label
                        fontFamily="SemiBold"
                        size={checkDevice() ? 'xxl' : 'm'}
                        color="text2"
                        text={t('myReferral.usersReferred')}
                        style={styles.referTxt}
                      />
                      <Label
                        fontFamily="SemiBold"
                        color="text2"
                        size="x5l"
                        text={
                          refersCount?.referredCount?.find(
                            e => e.refer_type === 'USER',
                          )?.count !== undefined
                            ? refersCount.referredCount
                                .find(e => e.refer_type === 'USER')
                                .count.toString()
                                .padStart(2, '0')
                            : '00'
                        }
                        style={styles.productText}
                      />
                    </View>
                  </View>
                </View>
                <Spacer size="xxl" />
                <View>
                  <View style={styles.or}>
                    <View style={styles.lineCut} />
                    <Label
                      size={checkDevice() ? 'xx' : 'm'}
                      text={t('myReferral.termsConditions')}
                      fontFamily="Medium"
                      color={checkDevice() ? 'text2' : 'grey'}
                      style={styles.lineSpacer}
                    />
                    <View style={styles.lineCut} />
                  </View>

                  <Spacer size="m" />
                  <View style={styles.terms}>
                    <View style={styles.dot} />
                    <Label
                      fontFamily="Regular"
                      color={checkDevice() ? 'text2' : 'grey'}
                      size={checkDevice() ? 'xx' : 'm'}
                      text={t('myReferral.afterReferring')}
                      style={styles.flexOne}
                    />
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.terms}>
                    <View style={styles.dot} />
                    <Label
                      fontFamily="Regular"
                      color="grey"
                      size={checkDevice() ? 'xx' : 'm'}
                      text={t('myReferral.availability')}
                      style={styles.flexOne}
                    />
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.terms}>
                    <View style={styles.dot} />
                    <Label
                      fontFamily="Regular"
                      color="grey"
                      size={checkDevice() ? 'xx' : 'm'}
                      text={t('myReferral.discretion')}
                      style={styles.flexOne}
                    />
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.terms}>
                    <View style={styles.dot} />
                    <Label
                      fontFamily="Regular"
                      color="grey"
                      size={checkDevice() ? 'xx' : 'm'}
                      text={t('myReferral.material')}
                      style={styles.flexOne}
                    />
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.terms}>
                    <View style={styles.dot} />
                    <Label
                      fontFamily="Regular"
                      color="grey"
                      size={checkDevice() ? 'xx' : 'm'}
                      text={t('myReferral.through')}
                      style={styles.flexOne}
                    />
                  </View>
                  <Spacer size="xxl" />
                  <View style={styles.or}>
                    <View style={styles.lineCut} />
                    <Label
                      style={styles.lineSpacer}
                      size={checkDevice() ? 'xx' : 'm'}
                      text={t('myReferral.end')}
                      fontFamily="Medium"
                      color="grey"
                    />
                    <View style={styles.lineCut} />
                  </View>
                  <Spacer size="xxl" />
                </View>
              </ErrorHandler>
            </>
          )}
        />
      )}
    </SafeAreaView>
  );
};

export default MyReferralScene;

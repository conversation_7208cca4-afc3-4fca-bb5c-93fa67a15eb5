import Icons from 'common/icons';
import {t} from 'i18next';
import React, {useCallback} from 'react';
import {View, ViewProps} from 'react-native';
import Label from '../label';
import Spacer from '../link';
import ListView from '../listView';
import stylesWithOutColor from './style';
import {Link} from '..';
import {Sizes} from 'common';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  icon?: keyof typeof Icons;
  text?: string;
  allProduct?: string;
  numColumns?: number | undefined;
  horizontal?: boolean;
  style?: ViewProps['style'];
  data: any;
  index?: number;
  onPress?: () => void;
  renderItem: ({
    item,
    index,
  }: {
    item: any;
    index: number;
    style?: ViewProps['style'];
  }) => React.ReactElement | null;
  ListHeaderComponent?: React.ReactElement | undefined;
  ListEmptyComponent?: React.ReactElement | undefined;
  ListFooterComponent?: React.ReactElement | undefined;
  onEndReached?: any;
  maxToRenderPerBatch?: number;
  scrollEventThrottle?: any;
  onEndReachedThreshold?: number;
  ref?: any;
};

const ProductList = ({
  text,
  numColumns,
  renderItem,
  horizontal,
  style,
  data,
  index,
  ListHeaderComponent,
  ListEmptyComponent,
  ListFooterComponent,
  allProduct,
  onEndReached,
  maxToRenderPerBatch,
  scrollEventThrottle,
  onEndReachedThreshold,
  onPress,
  ref,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );
  return (
    <View key={index} style={[styles.mainContainer, style]}>
      {text && (
        <View style={styles.view}>
          <Label size="l" weight="400" key={index} text={text} />
          <Link
            color="linkText"
            weight="400"
            onPress={onPress}
            text={allProduct}
          />
        </View>
      )}
      {text && <Spacer type="Vertical" size="m" />}
      <View style={style}>
        <ListView
          onEndReached={onEndReached}
          data={data}
          renderItem={renderItem}
          numColumns={numColumns}
          horizontal={horizontal}
          ListHeaderComponent={ListHeaderComponent}
          ListEmptyComponent={ListEmptyComponent}
          ListFooterComponent={ListFooterComponent}
          maxToRenderPerBatch={maxToRenderPerBatch}
          scrollEventThrottle={scrollEventThrottle}
          onEndReachedThreshold={onEndReachedThreshold}
          seprat
          ItemSeparatorComponent={() => <Spacer size="xm" />}
          ref={ref}
          // keyExtractor={({index: itemIndex}) => itemIndex?.toString()}
          keyExtractor={listViewKeyExtractor}
          // keyExtractor={(
          // item: {sku: {toString: () => any}},
          // index: {itemIndex},
          // ) => item?.sku?.toString() + index.toString()}
        />
      </View>
    </View>
  );
};

export default ProductList;

import React, { useState } from 'react';
import {
    View,
    UIManager,
    Platform,
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import { Label, ImageIcon, Spacer } from 'components/atoms';
import { benefits } from 'staticData';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import { Sizes } from 'common';
import stylesWithOutColor from './style';
import { useMemo } from 'react';

// Enable LayoutAnimation for Android
if (
    Platform.OS === 'android' &&
    UIManager.setLayoutAnimationEnabledExperimental
) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
}

type Props = {
    visible: boolean;
    onClose: () => void;
    contentType: string;
    modalText: string;
    setIsShowFooterModal: boolean;
};

const CommonModal = (props: Props) => {
    const { colors } = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
    const { visible, onClose, modalText } =
        props;

    return (
        <DynamicHeightModal
            useInsets
            visible={visible}
            onClose={() => onClose()}
            content={
                <View style={styles.modalAccordion}>
                    <Label
                        text={modalText}
                        color="text2"
                        size="m"
                        weight="400"
                    />
                </View>
            }
        />
    );
};

export default React.memo(CommonModal);

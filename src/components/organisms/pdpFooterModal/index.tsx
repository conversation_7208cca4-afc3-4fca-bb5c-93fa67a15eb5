import React, {memo} from 'react';
import {View, TouchableOpacity, TouchableWithoutFeedback} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {ImageIcon} from 'components/atoms';
import {useMemo} from 'react';

type Props = {
  useInsets?: boolean;
  visible: boolean;
  onClose?: (visible: boolean) => void;
  content?: React.ReactElement;
  flex?: number;
};

const PdpFooterModal = ({
  visible,
  onClose,
  content,
  useInsets = false,
  flex = 1,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const insets = useSafeAreaInsets();

  return (
    <Modal
      onBackButtonPress={() => onClose?.(!visible)}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={[styles.flexContainer]}>
        <View style={styles.centeredView}>
          <TouchableWithoutFeedback
            onPress={() => {
              onClose?.(!visible);
            }}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={() => onClose?.(!visible)}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x7l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <View
            style={[
              styles.modelBg,
              {flex: flex},
              useInsets && {paddingBottom: insets.bottom},
            ]}>
            {content}
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default memo(PdpFooterModal);

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background2,
    },
    listView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xms,
    },
    loaderStyle: {
      alignItems: 'center',
      flex: Sizes.x,
      justifyContent: 'center',
    },
    mainContainer: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    orderImageView: {
      padding: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
    },
    returnImageView: {
      paddingVertical: Sizes.x8l,
      paddingHorizontal: Sizes.x3l,
      alignItems: 'center',
      justifyContent: 'center',
    },
    orderBackground: {
      width: Sizes.ex3l,
      height: Sizes.ex3l,
    },
    returnBackground: {
      width: Sizes.ex2l + Sizes.x8l,
      height: Sizes.ex2l,
    },
    image: {
      height: Sizes.ex0,
      width: Sizes.ex0,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      resizeMode: 'contain',
    },
    orderButton: {
      alignSelf: 'center',
      paddingHorizontal: Sizes.x8l,
      paddingVertical: Sizes.mx,
    },
    tabColor: {
      color: colors.newSunnyOrange,
      fontWeight: '600',
      fontSize: Sizes.l,
    },
    excludeSubView: {
      flexDirection: 'row',
    },

    loading: {
      flex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    filter: {
      borderWidth: Sizes.x,
      width: Sizes.ex + Sizes.xms,
      height: Sizes.x6l,
      borderRadius: Sizes.sx,
      borderColor: colors.text2,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.xms,
      marginLeft: Sizes.xms,
    },
    linerColor: {
      height: Sizes.x4l,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    imageCoupon: {
      height: Sizes.ex84,
      width: '100%',
      borderRadius: Sizes.m,
    },
    searchMainView: {
      paddingHorizontal: Sizes.xms,
    },
    searchView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
    },
    searchSubView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      borderColor: colors.text2,
      flex: Sizes.x,
      height: Sizes.x6l,
      flexDirection: 'row',
      alignItems: 'center',
      paddingLeft: Sizes.xms,
      paddingRight: Sizes.sx,
    },
    filterIcon: {
      transform: [{rotate: '180deg'}],
      height: Sizes.xx,
      width: Sizes.xx,
    },
    textInputView: {
      flex: Sizes.x,
      color: colors.text2,
      fontWeight: '500',
      fontSize: Sizes.m,
    },
    buttonView: {
      paddingRight: Sizes.m,
      flexDirection: 'row',
    },
    orderTebView: {
      width: '100%',
      height: Sizes.x6l,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xm,
      justifyContent: 'space-evenly',
      flexDirection: 'row',
      alignItems: 'center',
    },
    forgetView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productView: {
      paddingVertical: Sizes.xm,
    },
    productTopView: {
      paddingRight: Sizes.xm,
      paddingLeft: Sizes.m,
    },
    productListView: {
      marginHorizontal: Sizes.xm,
    },
    didYouForgotView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    paginationView: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: Sizes.l,
      marginBottom: Sizes.xms,
      alignSelf: 'center',
    },
    pageBox: {
      backgroundColor: colors.whiteColor,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      height: Sizes.x4l,
      width: Sizes.x4l,
      marginRight: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
    },
    successTitle: {
      fontSize: Sizes.xl,
    },
    lineStyle: {
      height: Sizes.xl + Sizes.x,
      width: Sizes.x,
      backgroundColor: colors.grey,
    },
    btnStyle: {
      height: Sizes.x4l + Sizes.x,
      paddingVertical: 0,
    },
    btnTxt: {
      marginBottom: -Sizes.xs,
    },
    orderCard: {
      backgroundColor: colors.whiteColor,
      padding: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.xm,
    },
    orderCardBtn: {
      backgroundColor: colors.whiteColor,
      borderColor: colors.newSunnyOrange,
      borderWidth: Sizes.x,
      borderRadius: Sizes.s,
      flexDirection: 'row',
      paddingVertical: Sizes.s,
      paddingHorizontal: Sizes.xm,
    },
    rightArrow: {
      height: Sizes.xx,
      width: Sizes.xx,
    },
    leftArrow: {
      transform: [{rotate: '180deg'}],
      height: Sizes.xx,
      width: Sizes.xx,
    },
  });

export default styles;

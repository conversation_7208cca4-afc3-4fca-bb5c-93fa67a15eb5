import React, {useState, useCallback} from 'react';
import {View, TouchableOpacity, TextInput} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer, ModalComponent} from 'components/atoms';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {showErrorMessage} from 'utils/show_messages';
import {Button} from 'components/molecules';
import {ReturnPickupDateUpdateApi} from 'services/orders';
import {useMemo} from 'react';

type Props = {
  returnId: string;
  visible: boolean;
  onClose: () => void;
};

const PickupModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose, returnId} = props;
  const [isValid, setIsValid] = useState(true);
  const [text, setText] = useState('');

  const handleTextChange = (input: string) => {
    setText(input);
    setIsValid(validateDate(input));
  };

  const validateDate = (input: string) => {
    const regex = /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{2}$/;
    return regex.test(input);
  };

  const returnPickupDateUpdate = useCallback(async () => {
    const requestData = {
      id: returnId,
      request_pick_up_date: text,
    };
    const {data} = await ReturnPickupDateUpdateApi(requestData);
    showErrorMessage(data.message);
    onClose();
  }, []);

  return (
    <ModalComponent modelStyle={styles.modalView} visible={visible}>
      <View style={styles.modalCloseBtnContainer}>
        <TouchableOpacity
          onPress={() => onClose()}
          style={styles.modalCloseButton}>
          <ImageIcon icon="close" size="x6l" />
        </TouchableOpacity>
      </View>
      <View style={styles.removeModalView}>
        <View style={styles.removeModalSubView}>
          {isValid ? (
            <>
              <Label
                text={t('orderTrack.requestSubmitted')}
                fontFamily="Medium"
                size="mx"
                color="text"
                align="center"
              />
              <Spacer size="m" />
              <Button
                onPress={() => returnPickupDateUpdate()}
                radius="xms"
                size="small"
                weight="500"
                text={t('buttons.oK')}
                withGradient
                gradientColors={[colors.coral, colors.persimmon]}
                paddingHorizontal="s"
                labelSize="l"
                labelColor="whiteColor"
              />
            </>
          ) : (
            <>
              <Label
                text={t('orderTrack.enterDate')}
                fontFamily="SemiBold"
                size="mx"
                color="text"
                align="center"
              />
              <Spacer size="m" />
              <Label
                text={t('orderTrack.enterPickupDate')}
                fontFamily="SemiBold"
                size="mx"
                color="grey"
                align="center"
              />
              <Spacer size="m" />
              <TextInput testID="txtOrderTrackPickupDate"
                style={[styles.input, !isValid && styles.invalidInput]}
                placeholder="DD/MM/YY"
                placeholderTextColor="#aaa"
                textAlign="center"
                value={text}
                onChangeText={handleTextChange}
                keyboardType="numeric"
                allowFontScaling={false}
              />
            </>
          )}
          {!isValid && (
            <Label
              text={t('orderTrack.invalidDate')}
              fontFamily="SemiBold"
              size="mx"
              color="red"
              align="center"
            />
          )}
          <Spacer size="m" />
          <Label
            text={t('orderTrack.diffDate')}
            fontFamily="SemiBold"
            size="mx"
            color="red"
            align="center"
          />
        </View>
      </View>
    </ModalComponent>
  );
};

export default React.memo(PickupModal);

import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    modalView: {
      backgroundColor: colors.blueLagoon,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    subContainer: {
      backgroundColor: colors.background,
      width: '100%',
      borderTopLeftRadius: Sizes.m,
      borderTopRightRadius: Sizes.m,
      paddingHorizontal: Sizes.m,
    },
    modalCloseView: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x60,
    },
    imageSubView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingTop: Sizes.xm,
    },
    image: {
      borderWidth: Sizes.x,
      padding: Sizes.sx,
      borderRadius: Sizes.xm,
      borderColor: colors.imageBorderColor,
      justifyContent: 'center',
    },
    imageSize: {
      width: Sizes.x46,
      height: Sizes.x46,
      overflow: 'hidden',
      borderRadius: Sizes.xm,
    },
    nameSubView: {
      flex: Sizes.x,
    },
    selectReasonView: {
      flex: 3.5,
      justifyContent: 'flex-end',
      bottom: 0,
    },
    reasonView: {
      justifyContent: 'space-between',
      padding: Sizes.xm,
      paddingVertical: Sizes.xm,
    },
    dropdown: {
      borderWidth: 0.7,
      paddingHorizontal: Sizes.xms,
      borderRadius: Sizes.xm,
      borderColor: colors.pattensBlue70,
      height: Sizes.x46,
    },
    dropDownSelected: {
      color: colors.categoryTitle,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
    },
    dropDownIcon: {
      tintColor: colors.text,
    },
    placeholderStyle: {
      paddingHorizontal: Sizes.sx,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
      color: colors.grey2,
    },
    attachmentView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    imageContainerClose: {
      position: 'absolute',
      borderRadius: Sizes.xms,
      right: -Sizes.xx,
      top: -Sizes.xx,
      padding: Sizes.xms,
    },
    imag: {
      width: '100%',
      height: Sizes.ex82,
      borderRadius: Sizes.xm,
    },
    imgView: {
      width: '100%',
      height: Sizes.ex82,
      borderRadius: Sizes.xm,
      justifyContent: 'center',
      alignItems: 'center',
    },
    imag1: {
      width: Sizes.xxl,
      height: Sizes.xxl,
      borderRadius: Sizes.xm,
    },
    imageContainer: {
      width: Sizes.ex84,
      height: Sizes.ex84,
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.imageBorderColor,
      marginRight: Sizes.xm,
    },
    resetView: {
      justifyContent: 'center',
      flexDirection: 'row',
      paddingHorizontal: Sizes.sx,
    },
    returnView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    closeStyle: {
      width: Sizes.xsl,
      height: Sizes.xsl,
    },
    resetBtn: {
      width: '45%',
      backgroundColor: 'orange',
    },
    descriptionView: {
      width: '100%',
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
    },
    saveBtnView: {
      height: Sizes.x46,
      width: Sizes.ex176,
    },
    flex: {
      flex: Sizes.x,
    },
    dropDownTextStyle: {
      color: colors.text2,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
    },
    labelBtnStyle: {
      marginTop: -Sizes.xs,
    },
    plus: {
      fontSize: Sizes.m,
      color: colors.text,
    },
    qtyView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    lineStyle: {
      backgroundColor: colors.grey2,
      height: Sizes.xx,
      width: Sizes.x,
      marginHorizontal: Sizes.xm,
    },
  });

export default styles;

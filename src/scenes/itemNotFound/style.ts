import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    mainView: {
      height: '100%',
      width: '100%',
    },
    buttonStyle: {
      borderRadius: Sizes.xm,
      height: Sizes.x7l,
      width: 222,
      justifyContent: 'center',
      alignItems: 'center',
    },
    button2: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
    },
    bottomView: {
      bottom: Sizes.exl,
      flexDirection: 'column',
      position: 'absolute',
      alignSelf: 'center',
      paddingHorizontal: Sizes.l,
    },
    linearTextStyle: {
      textAlign: 'center',
      fontSize: Sizes.l,
      fontFamily: Fonts.Medium,
    },
    flex: {
      flex: Sizes.x,
    },
  });
export default styles;

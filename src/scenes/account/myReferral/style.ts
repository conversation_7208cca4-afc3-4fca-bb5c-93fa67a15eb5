import {Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet, Dimensions} from 'react-native';
const {width} = Dimensions.get('screen');

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    referBoxView: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    referBox: {
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      height: Sizes.x8l,
      paddingHorizontal: Sizes.xms,
      borderColor: colors.coralOrange,
      flexDirection: 'row',
      alignItems: 'center',
    },
    tabBoxSize: {
      height: Sizes.x56,
    },
    referTxt: {
      flex: Sizes.x,
      marginRight: 5,
    },
    referBoxLeft: {
      width: Sizes.ex1,
      marginRight: Sizes.xms,
    },
    referBoxRight: {
      flex: Sizes.x,
    },
    mainReferBox: {
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      height: Sizes.exl + Sizes.xms,
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.xms,
      borderColor: colors.coralOrange,
      flex: Sizes.x,
      paddingVertical: Sizes.sx,
    },
    mainReferBox1: {
      flexDirection: 'row',
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      height: Sizes.exl + Sizes.xms,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: Sizes.xms,
      borderColor: colors.coralOrange,
      flex: Sizes.x,
    },
    lineCut: {
      flex: Sizes.x,
      height: Sizes.x,
      backgroundColor: colors.grey2,
    },
    lineSpacer: {
      paddingHorizontal: Sizes.xsl,
    },
    dot: {
      backgroundColor: 'gray',
      height: Sizes.xs,
      width: Sizes.xs,
      borderRadius: Sizes.x,
      marginRight: Sizes.xms,
      marginTop: 9,
    },
    terms: {
      flexDirection: 'row',
      marginHorizontal: Sizes.sx,
    },
    underText: {
      textDecorationLine: 'underline',
    },
    subContainer: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.l,
    },
    dericationView: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    referralLink: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'center',
    },
    linkBox: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.x4l,
      alignItems: 'center',
      borderColor: colors.placeholderColor,
      backgroundColor: colors.whiteColor,
      flexDirection: 'row',
      paddingLeft: Sizes.m,
      padding: Sizes.xs,
    },
    linkStyle: {
      flex: Sizes.x,
    },
    or: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    newCopy: {
      width: Sizes.xxl,
      height: Sizes.xxl,
    },
    referCodeView: {
      justifyContent: 'center',
      flexDirection: 'row',
      backgroundColor: colors.sunnyDark,
      borderRadius: Sizes.xm,
      padding: Sizes.m,
      paddingHorizontal: Sizes.m,
      alignSelf: 'center',
    },
    shareVia: {
      borderBottomWidth: Sizes.x,
      alignItems: 'center',
      borderBottomColor: colors.smoothGrey,
      width: '27%',
    },
    giftbox: {
      width: Sizes.ex176,
      height: Sizes.ex176,
      alignSelf: 'center',
    },
    giftbox1: {
      width: Sizes.ex250,
      height: Sizes.ex250,
      alignSelf: 'center',
      marginLeft: Sizes.x6l,
    },
    copyTeg: {
      backgroundColor: colors.sunnyDark,
      padding: Sizes.m,
      paddingHorizontal: Sizes.l,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: Sizes.x3l,
    },
    linearGradient: {
      alignSelf: 'center',
      width: '100%',
      borderRadius: Sizes.mx,
      padding: Sizes.xsl,
      paddingBottom: Sizes.l,
    },
    shareText: {
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.xms,
    },
    bottomLineBg: {
      width: width - Sizes.x3l,
      height: Sizes.ex5l + Sizes.xl,
      marginLeft: -Sizes.xxl,
      marginRight: -Sizes.xxl,
    },
    tabImg: {
      height: Sizes.ex4l,
    },
    flexOne: {
      flex: Sizes.x,
    },
    listStyle: {
      flex: Sizes.x,
      justifyContent: 'space-around',
    },
    referView: {
      paddingHorizontal: Sizes.xl,
    },
    cardView: {
      width: (DeviceWidth - Sizes.x4l) / Sizes.xs,
      flexDirection: 'row',
      paddingHorizontal: Sizes.x3l,
    },
    alignSelfStart: {
      alignSelf: 'flex-start',
    },
    earnText: {
      marginBottom: -Sizes.xms,
    },
    productText: {
      marginTop: -Sizes.xs + Sizes.x,
    },
  });

export default styles;

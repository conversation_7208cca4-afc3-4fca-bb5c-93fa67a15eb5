import {Sizes, Fonts} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    planCardContainer: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    sliderAmount: {
      alignSelf: 'center',
      marginTop: Sizes.xm,
      paddingRight: Sizes.s,
    },
    underText: {
      paddingBottom: Sizes.s,
    },
    borderBox: {
      borderRadius: Sizes.m,
      borderColor: colors.grey7,
      backgroundColor: colors.offWhite,
      width: '100%',
      borderWidth: Sizes.x,
      alignSelf: 'center',
      marginBottom: Sizes.xm,
      padding: Sizes.xms,
    },
    bannerImg: {
      width: DeviceWidth,
      height: Sizes.ex3l + Sizes.xx4l,
      borderBottomLeftRadius: Sizes.xxl,
      borderBottomRightRadius: Sizes.xxl,
    },
    bannerImg1: {
      width: DeviceWidth,
      height: Sizes.ex510,
      borderBottomLeftRadius: Sizes.xxl,
      borderBottomRightRadius: Sizes.xxl,
    },
    membershipCalculation: {
      paddingHorizontal: Sizes.m,
    },
    monthBox: {
      backgroundColor: colors.cerulean1,
      borderRadius: Sizes.mx,
      padding: Sizes.xl,
      alignItems: 'center',
      width: (DeviceWidth - Sizes.x5l) / Sizes.xs,
    },
    notSelected: {
      borderWidth: Sizes.z,
      borderColor: colors.grayTextColor,
      borderRadius: Sizes.mx,
      padding: Sizes.xl,
      alignItems: 'center',
      width: (DeviceWidth - Sizes.x5l) / Sizes.xs,
      marginTop: Sizes.m,
    },
    checkedICon: {
      position: 'absolute',
      right: -Sizes.s,
      backgroundColor: colors.whiteColor,
      width: Sizes.xxxl,
      height: Sizes.xxxl,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xl,
      top: -Sizes.s + Sizes.x,
    },
    journey: {
      alignSelf: 'center',
      alignItems: 'center',
      width: '60%',
    },
    freeShippingBoxView: {
      overflow: 'hidden',
      shadowColor: colors.black25,
      shadowOffset: {
        width: 0,
        height: 0,
      },
      shadowOpacity: Sizes.z,
      shadowRadius: Sizes.z,
      elevation: Sizes.z,
    },
    choosePlanView: {
      backgroundColor: colors.aliceBlue2,
      borderRadius: Sizes.mx,
      flex: Sizes.x,
      padding: Sizes.m,
    },
    choosePlanLine: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    circleContainer: {
      backgroundColor: colors.whiteColor,
      padding: Sizes.sx,
      borderRadius: Sizes.xxl,
      width: '10%',
      alignItems: 'center',
    },
    moneyView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    totalPiceView: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      flex: Sizes.x,
      padding: Sizes.m,
      backgroundColor: colors.whiteColor,
      justifyContent: 'center',
    },
    howItWorksContainer: {
      paddingHorizontal: Sizes.m,
    },
    line: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
      flex: Sizes.x,
      alignSelf: 'center',
    },
    showMoreView: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.text2,
      flex: Sizes.x,
      padding: Sizes.m,
      backgroundColor: colors.whiteColor,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    mainView: {
      paddingVertical: Sizes.xms,
      paddingHorizontal: Sizes.m,
      backgroundColor: colors.whiteColor,
    },
    btnStyle: {
      backgroundColor: colors.text,
      height: Sizes.x7l,
    },
    btnText: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    flex: {
      flex: Sizes.x,
    },
    fRow: {
      flexDirection: 'row',
    },
    saprater: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: Sizes.xl,
    },
    benefits: {
      paddingHorizontal: Sizes.xm,
    },
    priceView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    dotView: {
      height: Sizes.xs,
      width: Sizes.xs,
      borderRadius: Sizes.x,
      backgroundColor: colors.text2,
      marginRight: Sizes.xms,
      marginLeft: Sizes.s,
      marginTop: Sizes.xm,
    },
    linearContainer: {
      paddingLeft: Sizes.l,
      paddingRight: Sizes.m,
      padding: Sizes.m,
      width: (DeviceWidth - Sizes.x5l) / Sizes.xs,
      height: Sizes.ex172,
      borderRadius: Sizes.mx,
    },
    benefitsStyle: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    truckViewIcon: {
      borderRadius: Sizes.sx,
      overflow: 'hidden',
      alignSelf: 'flex-end',
    },
    howItWorksView: {
      borderRadius: Sizes.m,
      overflow: 'hidden',
    },
    linearHowItWorks: {
      flex: Sizes.x,
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.xl,
      alignItems: 'center',
      flexDirection: 'row',
    },
    workTitle: {
      flex: Sizes.x,
      marginRight: Sizes.xms,
    },
    plusMember: {
      flex: Sizes.x,
      borderRadius: Sizes.mx,
      borderWidth: Sizes.xs,
      borderColor: colors.blue,
      backgroundColor: colors.jordyBlue61,
      padding: Sizes.m,
      paddingVertical: Sizes.xxl,
    },
    coinView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    coinView1: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    underBox: {
      borderRadius: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.background,
      backgroundColor: colors.white28,
      flexDirection: 'row',
      alignItems: 'center',
      height: checkDevice() ? Sizes.x56 : Sizes.x44,
      width: Sizes.ex176,
      paddingHorizontal: Sizes.l,
    },
    coinTabView: {
      justifyContent: 'center',
    },
    disableStyle: {
      backgroundColor: colors.grey2,
    },
  });

export default styles;

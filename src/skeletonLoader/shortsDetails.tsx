import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width, height} = Dimensions.get('window');

const ShortsDetailLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={height}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="10" rx="5" ry="5" width={width - 20} height="50" />
        <Rect x="10" y="70" rx="5" ry="5" width={width - 20} height={height - 230} />
        <Rect x="10" y={height - 150} rx="5" ry="5" width={width - 90} height={30} />
        <Rect x="10" y={height - 110} rx="5" ry="5" width={width - 90} height={100} />
        <Rect x={width - 60} y={height - 150} rx="5" ry="5" width={50} height={40} />
        <Rect x={width - 60} y={height - 100} rx="5" ry="5" width={50} height={40} />
        <Rect x={width - 60} y={height - 50} rx="5" ry="5" width={50} height={40} />
      </ContentLoader>
    </ScrollView>
  );
};

export default ShortsDetailLoader;

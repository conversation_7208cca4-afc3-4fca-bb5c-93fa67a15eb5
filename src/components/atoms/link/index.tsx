import React from 'react';
import {TouchableOpacity} from 'react-native';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import Label, {Props as TextProps} from '../label';

type Props = {
  onPress: () => void;
  isUnderlined?: boolean;
  disabled?: boolean;
} & TextProps;

const Link = ({
  onPress,
  isUnderlined,
  disabled = undefined,
  ...props
}: Props) => {
  return (
    <TouchableOpacity onPress={onPress} disabled={disabled}>
      <Label
        style={{textDecorationLine: isUnderlined ? 'underline' : 'none'}}
        {...props}
      />
    </TouchableOpacity>
  );
};

export default Link;

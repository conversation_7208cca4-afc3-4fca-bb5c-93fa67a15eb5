import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    detailMainView: {
      backgroundColor: colors.background,
      padding: Sizes.m,
      borderRadius: Sizes.mx,
    },
    detailBox: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
    },
    fOne: {
      flex: Sizes.x,
    },
    btnStyle: {
      backgroundColor: colors.placeholderColor,
      paddingHorizontal: Sizes.m,
      borderRadius: Sizes.xms,
      paddingVertical: Sizes.xm,
    },
  });

export default styles;

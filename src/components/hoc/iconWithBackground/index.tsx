import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';
import Icons from 'common/icons';
import {ImageIcon} from 'components/atoms';
import React from 'react';
import {View, ViewProps} from 'react-native';

type Props = {
  borderRadius: keyof typeof Sizes;
  backgroundColor?: keyof Theme['colors'];
  tintColor?: keyof Theme['colors'];
  size?: keyof typeof Sizes;
  icon: keyof typeof Icons;
  padding: keyof typeof Sizes;
  style?: ViewProps['style'];
};

const IconWithBackground = ({
  borderRadius = 'xms',
  size = 'xxl',
  padding = 's',
  style,
  icon,
  backgroundColor = 'skyBlue8',
  tintColor,
}: Props) => {
  const {colors} = useTheme();
  return (
    <>
      <View
        style={[
          {borderRadius: Sizes[borderRadius]},
          padding ? {padding: Sizes[padding]} : {},
          backgroundColor ? {backgroundColor: colors[backgroundColor]} : {},
          style ? style : {},
        ]}>
        <ImageIcon size={size} icon={icon} tintColor={tintColor} />
      </View>
    </>
  );
};

export default IconWithBackground;

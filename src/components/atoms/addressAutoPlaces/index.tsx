import React, {useMemo, memo, useState, useEffect} from 'react';
import {View, TouchableOpacity, Keyboard} from 'react-native';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer, ImageIcon} from 'components/atoms';
import {t} from 'i18next';
import {mapPlaceKey} from 'config/environment';

type Props = {
  selected: PlaceSuggestion;
  onPress: (value: PlaceSuggestion) => void;
  locationShow?: boolean;
  locationRef?: any;
  insets?: any;
};

const AddressAutoPlaces = ({
  selected,
  onPress,
  locationShow,
  locationRef,
  insets,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [search, setSearch] = useState('');

  useEffect(() => {
    if (locationShow) {
      locationRef?.current?.focus();
    }
  }, [locationShow]);

  const handlePlaceSelect = (data, details) => {
    if (!details) return;
    const components = details.address_components || [];
    const getComponent = type =>
      components.find(comp => comp.types.includes(type))?.long_name || '';
    const postcode = getComponent('postal_code') || '';
    const city = getComponent('locality') || '';
    const stateName = getComponent('administrative_area_level_1') || '';
    const state2 = getComponent('administrative_area_level_2') || '';
    const state3 = getComponent('administrative_area_level_3') || '';
    const county = getComponent('country') || '';
    const subpremise = getComponent('subpremise') || '';
    const premise = getComponent('premise') || '';
    const neighborhood = getComponent('neighborhood') || '';
    const street = getComponent('route') || '';
    const sublocality1 = getComponent('sublocality_level_1') || '';
    const sublocality2 = getComponent('sublocality_level_2') || '';
    const sublocality3 = getComponent('sublocality_level_3') || '';
    const customAddress = [
      data?.structured_formatting?.main_text,
      subpremise === data?.structured_formatting?.main_text ? '' : subpremise,
      premise === data?.structured_formatting?.main_text ? '' : premise,
      street === data?.structured_formatting?.main_text ? '' : street,
      neighborhood == data?.structured_formatting?.main_text
        ? ''
        : neighborhood,
      sublocality3,
      sublocality2,
      sublocality1,
      state3,
    ]
      .filter(Boolean)
      .join(', ');
    const area =
      data?.structured_formatting?.main_text ||
      sublocality3 ||
      sublocality2 ||
      sublocality1 ||
      state3 ||
      city;

    const lat = details.geometry?.location?.lat;
    const lng = details.geometry?.location?.lng;
    const obj = {
      area,
      postcode,
      city,
      state: stateName,
      county,
      formatted_address: details.formatted_address || data.description,
      lat,
      lng,
      deliveryArea: customAddress || data.description,
    };
    Keyboard.dismiss();
    onPress(obj);
  };

  return (
    <View style={styles.container}>
      <View
        style={[styles.searchInput, search?.length < 2 && styles.searchHeight]}>
        <Spacer size="m" type="Horizontal" />
        <ImageIcon
          size="xxl"
          tintColor="text"
          icon="search"
          resizeMode="contain"
          style={styles.searchIcon}
        />
        <View style={styles.fOne}>
          <GooglePlacesAutocomplete
            ref={locationRef}
            minLength={2}
            // debounce={300}
            fetchDetails={true}
            placeholder={t('address.locationSearch')}
            query={{
              key: mapPlaceKey,
              language: 'en',
              components: 'country:in',
              // location: {}, //To get near by results
              // radius: {},
            }}
            isRowScrollable={true}
            currentLocation={false}
            numberOfLines={3}
            nearbyPlacesAPI="GoogleReverseGeocoding"
            textInputProps={styles.placeText}
            styles={{
              textInputContainer: styles.input,
              textInput: styles.textMain,
              listView: styles.mapItemList,
              row: styles.mapItemRow,
              separator: styles.mapItemLine,
            }}
            enablePoweredByContainer={false}
            // GooglePlacesDetailsQuery={{fields: 'geometry'}}
            onPress={(data, details) => handlePlaceSelect(data, details)}
            textInputProps={{
              onChangeText: text => {
                setSearch(text);
              },
              autoCapitalize: 'none',
              placeholderTextColor: colors.text2,
              clearButtonMode: 'never',
            }}
            renderRightButton={() =>
              search?.length > 0 ? (
                <TouchableOpacity
                  style={styles.closeIcon}
                  onPress={() => {
                    locationRef?.current?.setAddressText('');
                    setSearch('');
                    locationRef?.current?.clear();
                  }}>
                  <ImageIcon
                    size="l"
                    tintColor="text"
                    icon="closeIcons"
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              ) : (
                <View />
              )
            }
            renderRow={(rowData, index) => {
              return (
                <View style={styles.rowCenter} key={index}>
                  <ImageIcon
                    size="xxl"
                    tintColor="text"
                    icon="location1"
                    resizeMode="contain"
                  />
                  <Spacer size="xm" type="Horizontal" />
                  <Label
                    text={rowData.description}
                    size="mx"
                    fontFamily="Medium"
                    color="text2"
                    numberOfLines={3}
                  />
                </View>
              );
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default memo(AddressAutoPlaces);

import React, {useState} from 'react';
import {View, TouchableOpacity} from 'react-native';
import ImageIcon from '../imageIcon';
import stylesWithOutColor from './style';
import Spacer from '../spacer';
import Label from '../label';
import {useMemo} from 'react';
import {useTheme} from '@react-navigation/native';

const LikeDislike = ({
  initialLikes,
  initialDislikes,
  postId,
  updatePost,
}: any) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [likes, setLikes] = useState(initialLikes);
  const [dislikes, setDislikes] = useState(initialDislikes);
  const [userAction, setUserAction] = useState<string | null>(null);

  const handleAction = async (type: string) => {
    let updatedLikes = likes;
    let updatedDislikes = dislikes;

    if (type === 'like') {
      if (userAction === 'like') {
        updatedLikes -= 1;
        setUserAction(null);
      } else {
        updatedLikes += 1;
        if (userAction === 'dislike') {
          updatedDislikes -= 1;
        }
        setUserAction('like');
      }
    } else if (type === 'dislike') {
      if (userAction === 'dislike') {
        updatedDislikes -= 1;
        setUserAction(null);
      } else {
        updatedDislikes += 1;
        if (userAction === 'like') {
          updatedLikes -= 1;
        }
        setUserAction('dislike');
      }
    }

    setLikes(updatedLikes);
    setDislikes(updatedDislikes);
    await updatePost(postId, updatedLikes, updatedDislikes);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={() => {
          handleAction('like');
        }}
        style={styles.subContainer}>
        <ImageIcon
          icon={userAction === 'like' ? 'likeFilled' : 'like'}
          size={userAction === 'like' ? 'xx' : 'xxl'}
          tintColor={userAction !== 'like' && 'text2'}
        />
        <Spacer size="s" type="Horizontal" />
        <Label
          style={styles.label}
          text={likes}
          size="mx"
          fontFamily="Regular"
          color="text2"
        />
      </TouchableOpacity>
      <Spacer size="xm" type="Horizontal" />
      <TouchableOpacity
        onPress={() => {
          handleAction('dislike');
        }}
        style={styles.subContainer}>
        <ImageIcon
          icon={userAction === 'dislike' ? 'dislikeFilled' : 'dislike'}
          size={userAction === 'dislike' ? 'xx' : 'xxl'}
          tintColor={userAction !== 'dislike' && 'text2'}
          style={styles.top}
        />
        <Spacer size="s" type="Horizontal" />
        <Label
          style={styles.label}
          text={dislikes}
          size="mx"
          fontFamily="Regular"
          color="text2"
        />
      </TouchableOpacity>
    </View>
  );
};

export default LikeDislike;

import React, {
    useCallback,
    useEffect,
    useState,
    createContext,
    FC,
  } from 'react';
  import Config from 'react-native-config';
  // import { infoLog } from '@utils/LogUtils';
  import ButtonInterceptor from './ButtonInterceptor';
import { debugLog } from 'utils/debugLog';
  
  export const NetworkLoggerContext = createContext({});
  
  const TAG = 'NetworkLoggerProvider: ';
  
  interface NetworkLoggerProviderProps {
    children: React.ReactNode;
  }
  
  const NetworkLoggerProvider: FC<NetworkLoggerProviderProps> = ({children}) => {
    const [isOpen, setOpen] = useState(false);
  
    useEffect(() => {
      debugLog(TAG + `logger status -> ${isOpen}`);
    }, [isOpen]);
  
    const toggleInterceptor = useCallback(() => {
      setOpen(!isOpen);
    }, [isOpen]);
  
    const mProps = {
      toggleInterceptor,
      isOpen,
    };
    return (
      <NetworkLoggerContext.Provider value={mProps}>
        {children}
  
        <ButtonInterceptor show={Config.REACT_APP_USE_INTERCEPTOR === 'true'} />
      </NetworkLoggerContext.Provider>
    );
  };
  
  export default NetworkLoggerProvider;
  
import React, {useState} from 'react';
import {View, SafeAreaView, Keyboard, ActivityIndicator} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer, WithGradient} from 'components/atoms';
import stylesWithOutColor from './style';
import {PhoneInputText, Button} from 'components/molecules';
import {useSelector} from 'react-redux';
import {showErrorMessage} from 'utils/show_messages';
import {addNewFaq} from 'services/productDetail';
import tokenClass from 'utils/token';
import {t} from 'i18next';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {TouchableOpacity} from 'react-native';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  product: ProductData | null;
  getFaqs: () => void;
  review: string;
  onSuccess: () => void;
};

const PostQuestionModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose, product, getFaqs, onSuccess, review} = props;
  const [faqPostText, setFaqPostText] = useState('');
  const [loader, setLoader] = useState(false);
  const [faqError, setFaqError] = useState('');
  const {userInfo} = useSelector((state: RootState) => state.app);

  const onPressPostFaq = async () => {
    Keyboard.dismiss();
    setLoader(true);
    if (faqPostText.trim().length === 0) {
      setFaqError(t('validations.questionReq'));
      setLoader(false);
      return false;
    }
    setFaqError('');
    try {
      const token = await tokenClass.getToken();
      const {data, status} = await addNewFaq({
        question: faqPostText.trim(),
        product_id: product?.product_id,
        product_name: product?.name,
        product_image: product?.media?.find(
          media => media?.disabled === false && media?.media_type === 'image',
        )?.file,
        like: 0,
        dislike: 0,
        enable: false,
        customer_token: token,
        user: 'customer',
        customer_email: userInfo?.email ?? '',
        customer_name: userInfo?.fullname ?? '',
      });
      if (data && status) {
        setLoader(false);
        setFaqPostText('');
        onSuccess();
        getFaqs();
      } else {
        setLoader(false);
      }
    } catch (error) {
      showErrorMessage(t('validations.someThingWrong'));
    } finally {
      setLoader(false);
    }
  };

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={() => onClose()}
      content={
        <SafeAreaView style={styles.flex}>
          <View style={styles.postQuestionView}>
            <View style={styles.answerView}>
              <Spacer size="l" />
              <Label
                text={t('PDP.questionAnswer')}
                size="l"
                weight="600"
                color="text"
              />
              <Spacer size="xs" />

              {!!review?.review_meta?.verified_buyers > 0 && (
                <Label
                  text={`${review?.review_meta?.verified_buyers} ${t(
                    'PDP.verifiedBuyers',
                  )}`}
                  size="mx"
                  weight="500"
                  color="text2"
                />
              )}

              <Spacer size="m" />
              <PhoneInputText
                testID="txtPdpPostQuestions"
                inputStyle={styles.flex}
                multiline
                autoFocus
                numberOfLine={6}
                style={styles.postInput}
                type="default"
                onChangeText={value => {
                  setFaqPostText(value);
                  if (value.trim().length > 0) {
                    setFaqError('');
                  }
                }}
                value={faqPostText}
                placeholderTextColor={colors.grey2}
                placeholder={t('PDP.postQuestions')}
                error={faqError}
                textAlignVertical="top"
              />
              <Spacer size="m" />
              <TouchableOpacity
                onPress={onPressPostFaq}
                disabled={loader}
                style={styles.onCheckoutMainView}>
                <WithGradient
                  gradientColors={[colors.coral, colors.persimmon]}
                  gradientStyle={styles.onCheckoutView}>
                  {loader ? (
                    <ActivityIndicator
                      size={'small'}
                      color={colors.whiteColor}
                    />
                  ) : (
                    <Label
                      text={t('buttons.submit')}
                      size="mx"
                      fontFamily="Medium"
                      color="background"
                      align="center"
                    />
                  )}
                </WithGradient>
              </TouchableOpacity>

              <Spacer size="l" />
            </View>
          </View>
        </SafeAreaView>
      }
    />
  );
};

export default React.memo(PostQuestionModal);

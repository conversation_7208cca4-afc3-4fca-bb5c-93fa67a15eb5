import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const Styles = (colors: Theme['colors']) => {
  return StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    container: {
      flex: Sizes.s,
    },
    centeredView: {
      flex: Sizes.x,
      backgroundColor: colors.blackTransparent,
    },
    modalContainer: {
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xms,
      width: '100%',
      flex: Sizes.x,
      flexDirection: 'column',
    },
    Button: {
      padding: Sizes.m,
    },
    filterView: {
      paddingVertical: Sizes.l,
      width: '100%',
      paddingHorizontal: Sizes.xl,
      borderBottomColor: colors.grey2,
      borderBottomWidth: Sizes.x,
    },
    modalView2: {
      paddingVertical: Sizes.l,
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
      borderColor: colors.grey2,
      borderBottomWidth: Sizes.x,
      backgroundColor: colors.background,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.m,
    },
    col1: {
      flex: Sizes.x,
      backgroundColor: colors.grey4,
      borderTopRightRadius: Sizes.xms,
    },
    col2Item: {
      flexDirection: 'row',
      marginLeft: '30%',
    },
    col2: {
      paddingRight: Sizes.xl,
      flex: Sizes.xs,
      justifyContent: 'flex-start',
    },
    modalView3: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    quickFilters: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    filterOpacity: {
      flex: Sizes.xs,
    },
    filterView2: {
      flexDirection: 'row',
      flex: Sizes.xs,
      marginHorizontal: '26%',
    },
    titleStyle: {
      textTransform: 'capitalize',
    },
    filterSection: {
      marginBottom: Sizes.xms,
    },
    checkBox: {
      height: Sizes.xx,
      width: Sizes.xx,
      borderColor: colors.grey,
    },
    checkBoxHeader: {
      height: Sizes.xx,
      width: Sizes.xx,
      borderColor: colors.text,
    },
    filterLabel: {
      fontSize: Sizes.l,
      fontWeight: 'bold',
      marginBottom: Sizes.s + Sizes.x,
    },
    button1Opacity: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.m,
      width: 127,
      height: Sizes.x42,
      marginLeft: Sizes.xm,
    },
    selectedLabel: {
      backgroundColor: colors.whiteColor,
      width: '100%',
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.mx,
    },
    normalLabel: {
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.mx,
    },
    buttonStyle: {
      flexDirection: 'row',
      width: 127,
      height: Sizes.x42,
      backgroundColor: colors.grey2,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.m,
    },
    buttonView: {
      flexDirection: 'row',
      alignContent: 'center',
      justifyContent: 'space-between',
      borderTopWidth: Sizes.x,
      width: '100%',
      borderColor: colors.grey2,
      padding: Sizes.l,
    },
    modalText: {
      marginBottom: Sizes.l,
      textAlign: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
  });
};

export default Styles;

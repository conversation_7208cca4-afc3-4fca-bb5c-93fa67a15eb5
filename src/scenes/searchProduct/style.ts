import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    modelStyle: {
      backgroundColor: colors.background,
      padding: Sizes.xl,
      borderTopLeftRadius: Sizes.s,
      borderTopRightRadius: Sizes.s,
      elevation: Sizes.l,
      borderRadius: Sizes.s,
    },
    shadowProp: {
      shadowColor: colors.shadowBg,
      shadowOffset: {width: -Sizes.xs, height: Sizes.xs},
      shadowOpacity: 0.2,
      shadowRadius: Sizes.xs,
    },
    modelViewAndroid: {
      flex: 0.92,
      backgroundColor: colors.background,
    },
    iosFlex: {
      flex: Sizes.x,
      shadowOpacity: Sizes.xs,
    },
    buttonStyle: {
      width: '50%',
      height: Sizes.xx4l,
      backgroundColor: 'orange',
    },
    flexOne: {
      flex: 1,
    },
    modelView: {
      width: '100%',
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
    },
    addButtonText: {
      fontSize: Sizes.m,
      fontWeight: '600',
    },
    clearText: {
      fontSize: Sizes.mx,
      fontWeight: '600',
      color: colors.text,
      textTransform: 'uppercase',
    },
    topIconView: {
      position: 'absolute',
      height: Sizes.xxxl,
      borderRadius: Sizes.x3l,
      backgroundColor: colors.black2,
      paddingHorizontal: Sizes.xms,
      elevation: Sizes.s,
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
      flex: Sizes.x,
      alignSelf: 'center',
      marginTop: Sizes.sx,
      flexDirection: 'row',
    },
    goTop: {
      marginBottom: -Sizes.xs,
    },
    amountSpace: {
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
    emptySearchWrapper: {
      backgroundColor: colors.whiteColor,
      alignSelf: 'center',
    },
    loading: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: Sizes.sx,
    },
    buttonCenter: {
      alignSelf: 'center',
    },
    listView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.mx,
    },
    filterItemTag: {
      paddingHorizontal: Sizes.xm,
    },
    filterItemLabel: {
      color: colors.categoryTitle,
      fontWeight: '400',
      marginBottom: -Sizes.x,
    },
    filterItemBetweenSpace: {
      padding: Sizes.s,
      marginBottom: Sizes.sx,
    },
    filtersListRow: {
      paddingRight: Sizes.s,
      flexDirection: 'row',
      alignItems: 'center',
    },
    clearFilterBtnRow: {
      justifyContent: 'space-between',
      alignSelf: 'center',
      flexDirection: 'row',
      paddingHorizontal: Sizes.sx,
      marginBottom: Sizes.sx,
    },
    searchHeader: {
      height: Sizes.x6l,
      marginHorizontal: Sizes.mx,
    },
    letUsView: {
      justifyContent: 'center',
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
      flexWrap: 'wrap',
      alignItems: 'center',
    },
    emptyImage: {
      width: Sizes.l,
      height: Sizes.l,
      marginRight: Sizes.s,
    },
    emptyMainView: {
      flex: Sizes.x,
      height: Sizes.windowHeight,
    },
    filterStyle: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      paddingHorizontal: Sizes.xms,
    },
    searchStyle: {
      paddingHorizontal: Sizes.mx,
    },
    suggestProductBtn: { 
      marginTop: Sizes.x4l, 
      alignSelf: 'center' 
    }
  });

export default styles;

import React from 'react';
import {
  GestureResponderEvent,
  TouchableOpacity,
  View,
  ViewProps,
} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';


type Props = {
  selected?: boolean;
  style?: ViewProps['style'];
  underStyles?: ViewProps['style'];
  onPress?: (event: GestureResponderEvent) => void;
  borderColor?: keyof Theme['colors'];
  fillColor?: keyof Theme['colors'];
};

const Radio = ({
  selected,
  style,
  underStyles,
  onPress,
  disabled,
  borderColor,
  fillColor,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={onPress}
      style={[
        styles.container,
        {borderColor: borderColor ? colors[borderColor] : colors.border},
        style,
      ]}>
      {selected && (
        <View
          style={[
            styles.innerContainer,
            {backgroundColor: fillColor ? colors[fillColor] : colors.primary},
            underStyles,
          ]}
        />
      )}
    </TouchableOpacity>
  );
};

export default Radio;

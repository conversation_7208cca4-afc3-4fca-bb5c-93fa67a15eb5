import React, {useMemo} from 'react';
import {View} from 'react-native';
import FastImage from 'react-native-fast-image';
import stylesWithOutColor from '../../../scenes/productDetail/style';
import {useTheme} from '@react-navigation/native';
import Icons from 'common/icons';
import {Label, Spacer, TireSuccessModal} from 'components/atoms';
import <PERSON>rrorHandler from 'utils/ErrorHandler';
import {t} from 'i18next';

const TAG = 'PoductDetailScreen';

type Props = {
  addedItem: number;
  singleProductPrice: number;
  totalSingleAmount: number;
  totalTierAmount: number;
  singleProductTierModal: boolean;
  setSingleProductTierModal: React.Dispatch<React.SetStateAction<boolean>>;
  singleProductTierText: any;
  congratsMsgShow: boolean;
};

const SingleTierSection = ({
  addedItem,
  singleProductPrice,
  totalSingleAmount,
  totalTierAmount,
  singleProductTierModal,
  setSingleProductTierModal,
  singleProductTierText,
  congratsMsgShow,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={styles.singleTireMainView}>
      <View style={styles.singleTireSubView}>
        <FastImage
          resizeMode="stretch"
          style={styles.simpleGifView}
          source={Icons?.simpleProductGif}
        />
        <Spacer size="xm" type="Horizontal" />

        <View>
          <Label
            text={`${t('PDP.subTotal')} :₹${
              addedItem > 0
                ? addedItem === 1
                  ? singleProductPrice
                  : totalSingleAmount?.toFixed(2)
                : totalTierAmount?.toFixed(2)
            } `}
            size="mx"
            weight="600"
            textTransform="capitalize"
            color="background"
          />
          <Label
            text={
              congratsMsgShow ? t('PDP.congratsMessage') : singleProductTierText
            }
            size="m"
            weight="500"
            color="background"
          />
          {singleProductTierText && (
            <ErrorHandler
              componentName={`${TAG} TireSuccessModal`}
              onErrorComponent={<View />}>
              <TireSuccessModal
                visible={singleProductTierModal}
                onClose={() => setSingleProductTierModal?.(false)}
              />
            </ErrorHandler>
          )}
        </View>
      </View>
    </View>
  );
};

export default SingleTierSection;

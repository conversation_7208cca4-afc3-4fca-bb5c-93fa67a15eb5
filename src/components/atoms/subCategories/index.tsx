import Icons from 'common/icons';
import React, {useEffect, useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import Label from '../label';
import {TopBrandsItems, TopItemWithLabel} from 'components/atoms';
import stylesWithOutColor from './style';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from 'routes';
import {useTheme} from '@react-navigation/native';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {homePageTopCategoryItemProps} from '@types/local';
import {navigate} from 'utils/navigationRef';
import FastImage from 'react-native-fast-image';
import {Sizes} from 'common';
import {AnalyticsEvents} from 'components/organisms';
import {productDummyImage} from 'utils/imageUrlHelper';
import {useMemo} from 'react';


type Props = {
  index: number;
  item?: homePageTopCategoryItemProps;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  onViewAllPress?: () => void;
};
const SubCategories = ({
  item,
  navigation,
  index,

  onViewAllPress,
  childIndex,
  setChildIndex,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View
      key={index}
      style={childIndex === index ? styles.whiteBg : styles.continuerBlue}>
      <TouchableOpacity
        // activeOpacity={0.6}
        onPress={() => {
          // AnalyticsEvents('SUB_CATEGORY_VIEWED', 'Sub Category viewed', item);
          // setParentModal(false);

          item?.children?.length > 0
            ? setChildIndex(childIndex === index ? -1 : index)
            : item?.name
            ? navigation.navigate('UrlResolver', {
                urlKey: item?.url_path + '.html',
              })
            : null;
        }}
        style={[
          styles.labelView,
          styles.labelViewSub,
          childIndex === index && styles.borderBottom,
        ]}>
        {item?.image && (
          <View style={styles.imageContainer}>
            <FastImage
              onError={e => {
                e.target.uri = productDummyImage;
              }}
              style={styles.imageCategories}
              source={{uri: item.iconUrl}}
            />
          </View>
        )}
        <View style={styles.textAdd}>
          {item?.name ? null : (
            <TouchableOpacity onPress={onViewAllPress} style={styles.textAdd}>
              <Label
                style={[styles.subCategoriesList, {right: Sizes.m}]}
                text={'View All'}
              />
            </TouchableOpacity>
          )}
          <Label style={styles.subCategoriesList} text={item?.name} />
          {item?.children?.length > 0 ? (
            <TouchableOpacity
              onPress={() => {
                item?.children?.length > 0
                  ? setChildIndex(childIndex === index ? -1 : index)
                  : navigation.navigate('UrlResolver', {
                      urlKey: item?.url_path + '.html',
                    });
              }}>
              <FastImage
                style={styles.optionsImage}
                onError={e => {
                  e.target.uri = productDummyImage;
                }}
                resizeMode="contain"
                source={childIndex === index ? Icons.minusIcon : Icons.addIcon}
              />
            </TouchableOpacity>
          ) : null}
        </View>
      </TouchableOpacity>
      {childIndex === index ? (
        <>
          <TopBrandsItems
            viewAllBox={styles.viewBoxes}
            viewAllPress={() =>
              item?.name &&
              navigate('UrlResolver', {
                urlKey: item?.url_path + '.html',
              })
            }
            ViewHeading
            horizontal={false}
            heading={false}
            index={index}
            data={item?.children}
            renderItem={({item, index}) => {
              return (
                <TopItemWithLabel
                  onPress={() => {
                    navigate('UrlResolver', {
                      urlKey: item?.url_path + '.html',
                    });
                  }}
                  labelStyle={childIndex && styles.labelsStyle}
                  style={[childIndex && styles.labelView, styles.borderBottom]}
                  item={item}
                  labels={true}
                  index={index}
                  productImage={false}
                  numberOfLines={1}
                />
              );
            }}
          />
        </>
      ) : null}
    </View>
  );
};

export default SubCategories;

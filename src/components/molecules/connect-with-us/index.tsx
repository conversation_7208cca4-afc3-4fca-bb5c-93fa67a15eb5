import React, { useMemo } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation, useTheme } from '@react-navigation/native';
import { Label, ImageIcon } from 'components/atoms';
import { openWhatsApp } from 'utils/utils';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '@types/local';
import stylesWithOutColor from './style';
import { Sizes } from 'common';

type ConnectWithUsProps = {
    containerStyle?: object;
    showWhatsAppIcon?: boolean;
    showHelpCenter?: boolean;
};

const ConnectWithUs: React.FC<ConnectWithUsProps> = ({
    containerStyle = {},
    showWhatsAppIcon = true,
    showHelpCenter = true,
}) => {
    const { colors } = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
    const navigation = useNavigation();
    const { t } = useTranslation();
    const { whatsAppLink } = useSelector((state: RootState) => state.app);

    const handleHelpCenterPress = () => {
        // navigation.navigate('HelpCenter', { login: true })
        openWhatsApp(whatsAppLink?.app_link)
    };

    return (
        <TouchableOpacity style={[styles.helpView, containerStyle]}  onPress={handleHelpCenterPress}>
            <View>
                <Label
                    size="m"
                    text={t('profileCompletion.needHelp')}
                    fontFamily="Medium"
                    weight="500"
                />
            </View>
            <View style={styles.helpView}>
                    <Label
                        color="skyBlue23"
                        size="m"
                        text={t('login.contactUs')}
                        fontFamily="Medium"
                        weight="600"
                    />
                <View style={{ padding: Sizes.xs }}>
                    <ImageIcon size="l" icon="whatsapp" />
                </View>
            </View>
        </TouchableOpacity>
    );
};

export default ConnectWithUs;

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    whatsappButton: {
        padding: 8,
    },
});

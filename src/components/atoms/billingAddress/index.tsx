import React, {useCallback, useEffect, useState, useMemo, useRef} from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import {Button, TextInputBox} from 'components/molecules';
import {
  Spacer,
  Label,
  DropDown,
  ImageIcon,
  DeliveryInfoModal,
} from 'components/atoms';
import {RootStackParamsList} from 'routes';
import {RouteProp, useTheme} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {useDispatch, useSelector} from 'react-redux';
import {getUserInfo, setLoading} from 'app-redux-store/slice/appSlice';
import {
  createCostumerAddress,
  getAddressValidationRule,
  updateCostumerAddress,
  checkPinCodeValid,
} from 'services/address';
import {
  stringReg,
  phoneReg,
  nameSplit,
  btnClickCallBack,
  fullNameReg,
  onCheckGst,
} from 'utils/utils';
import localStorage from 'utils/localStorage';
import {AnalyticsEvents} from 'components/organisms';
import ErrorHandler from 'utils/ErrorHandler';
import Icons from 'common/icons';
import { Sizes } from 'common';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ManageAddress'>;
  index: number;
  itemLength: number;
};

const BillingAddress = ({
  navigation,
  route,
  addressId,
  address,
  onCancel,
  onDeleteBilling,
  itemLength,
  index
}: Props) => {
  const TAG = 'BillingAddress';
  const {colors} = useTheme();
  const dispatch = useDispatch();
  const {countryListings, cartId} = useSelector(
    (state: RootState) => state.app,
  );
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  //   const address = route?.params?.address;
  const [infoModel, setInfoModel] = useState(false);
  const [apiValidation, setApiValidation] = useState({});
  const [validPinCode, setValidPinCode] = useState(false);
  const [alterNoShow, setAlterNoShow] = useState(false);
  const [pinCodeData, setPinCodeData] = useState();
  const [values, setValues] = useState({
    firstName: '',
    country_code: '',
    region: {id: '', state_code: '', state_name: ''},
  });
  const [errors, setErrors] = useState({});
  const [postCodeFormat, setPostCodeFormat] = useState<undefined | RegExp>(
    undefined,
  );
  const [taxFormat, setTaxFormat] = useState<undefined | RegExp>(undefined);
  const [telephoneFormat, setTelephoneFormat] = useState<undefined | RegExp>(
    undefined,
  );
  const [regionList, setRegionList] = useState([]);
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const scrollRef = useRef(null);

  const GetRegex = useCallback((data: string) => {
    if (data) {
      let index = data.lastIndexOf('/');
      let s = data.substring(0, index) + '' + data.substring(index + 1);
      return new RegExp(s.replace('/', ''));
    }
  }, []);

  // ------------------- create Address -----------
  const createUserAddress = useCallback(async () => {
    try {
      const isValid = await validate(values);
      if (!isValid) {
        return;
      }
      const gst = values?.vat_id?.trim();
      const stateRegion = values?.region;
      dispatch(setLoading(true));
      if (gst?.trim()?.length > 0) {
        const gstResponse = await checkGst(gst, stateRegion?.state_name, true);
        if (!gstResponse?.status) {
          dispatch(setLoading(false));
          return;
        }
      }
      const stateCode = stateRegion?.state_code;
      const stateCodeMismatch =
        stateCode !== pinCodeData?.district?.state?.state_code ||
        values?.postcode !== pinCodeData?.pincode;
      let isPinCodeValid = true;
      if (stateCodeMismatch) {
        const pinCodeRes = await checkPinCodeValid(values?.postcode);
        const verifyPostCode =
          stateCode !== pinCodeRes?.data?.district?.state?.state_code ||
          values?.postcode !== pinCodeRes?.data;
        if (verifyPostCode) {
          showErrorMessage(t('validations.postcodeState'));
          Keyboard.dismiss();
          dispatch(setLoading(false));
          return;
        }
        isPinCodeValid = pinCodeRes?.status;
      }
      if (!isPinCodeValid) {
        dispatch(setLoading(false));
        return;
      }
      setErrors({});
      const {fname, lname} = nameSplit(values?.firstName?.trim());
      let variables = {
        firstname: fname || '',
        lastname: lname ? lname : fname,
        street: [
          values?.street?.trim().replaceAll(',', '') || '',
          values?.landmark?.trim() || '',
        ],
        postcode: values?.postcode?.trim() || '',
        city: values?.city?.trim() || '',
        telephone: values?.telephone?.trim() || '',
        default_shipping: false,
        default_billing: true,
        vat_id: gst || '',
        country_id: values?.country_code?.country_id,
        region: {
          region_id: values?.region?.id,
          region: values.region?.state_name,
          region_code: values.region?.state_code,
        },
        custom_attributes: [
          {
            attribute_code: 'alternate_telephone',
            value: values?.alternateNumber?.trim() || '',
          },
        ],
        shipping_address_id: addressId,
      };
      if (address?.default_billing) {
        variables.id = address.id;
      }
      const submit = address?.default_billing
        ? updateCostumerAddress
        : createCostumerAddress;
      const {data, status} = await submit(variables);
      dispatch(setLoading(false));
      if (!status) {
        showErrorMessage(data?.message);
        return;
      }
      await localStorage.set('shippingAddressId', addressId);
      onCancel();
      showSuccessMessage(
        address
          ? t('addressCard.billingUpdated')
          : t('addressCard.billingAdded'),
      );

      if (address) {
        AnalyticsEvents(
          'BILLING_DETAILS_UPDATED',
          'Billing Details Updated',
          variables,
          userInfo,
          isLoggedIn,
        );
      }
      dispatch(getUserInfo());
    } catch (error) {
      dispatch(setLoading(false));
      showErrorMessage(t('validations.someThingWrong'));
    }
  }, [
    values,
    address,
    userInfo,
    pinCodeData,
    dispatch,
    navigation,
    route,
    isLoggedIn,
  ]);

  const getAddressValidations = async (country_Id = 'IN') => {
    try {
      const {data} = await getAddressValidationRule(country_Id);
      if (data) {
        setApiValidation(data);
        let TX = GetRegex(data.tax_format);
        let TF = GetRegex(data.telephone_format);
        let PF = GetRegex(data.postcode_format);
        setTaxFormat(TX);
        setTelephoneFormat(TF);
        setPostCodeFormat(PF);
        setValues(prev => ({
          ...prev,
          alternate_telephone_required: data?.alternate_telephone_required,
          postcode_required: data?.postcode_required,
          tax_required: data?.tax_required,
          tax_format: TX,
          postcode_format: PF,
          telephone_format: TF,
        }));
      }
    } catch (error) {
      showErrorMessage(`${error?.message}. Please try again.`);
    }
  };

  const validate = () => {
    const error = {};
    let errorStatus = true;
    const validations = [
      {
        field: 'firstName',
        rule: stringReg,
        message: t('validations.fullNameReq'),
        matchMessage: t('validations.fullNameCorrect'),
      },
      {
        field: 'telephone',
        rule: phoneReg,
        message: t('validations.telephoneRequired'),
        matchMessage: t('validations.telephoneCorrect'),
      },
      {
        field: 'alternateNumber',
        rule: phoneReg,
        message: t('validations.alternateRequired'),
        matchMessage: t('validations.alternateCorrect'),
        required: values?.alternateNumber ? true : false,
      },
      {field: 'street', message: t('validations.street')},
      {
        field: 'postcode',
        rule: postCodeFormat,
        message: t('validations.postcodeRequired'),
        matchMessage: t('validations.validPostCode'),
      },
      {
        field: 'city',
        rule: stringReg,
        message: t('validations.cityReq'),
        matchMessage: t('validations.alphabets'),
      },
      {field: 'region', message: t('validations.region')},
      {
        field: 'vat_id',
        rule: taxFormat,
        message: t('validations.tax'),
        matchMessage: t('validations.validTex'),
        required: values?.tax_required,
      },
    ];
    for (const {
      field,
      rule,
      message,
      matchMessage,
      required = true,
    } of validations) {
      const value = field.split('.').reduce((obj, key) => obj?.[key], values);
      if (required && !value) {
        error[field] = message;
        errorStatus = false;
      } else if (required && rule && !value?.match(rule)) {
        error[field] = matchMessage;
        errorStatus = false;
      } else if (field === 'region' && !value?.id) {
        error[field] = message;
        errorStatus = false;
      } else if (field === 'country_code' && !value?.country_id) {
        error[field] = message;
        errorStatus = false;
      } else if (
        field === 'vat_id' &&
        value?.trim()?.length > 0 &&
        rule &&
        !value?.trim()?.match(rule)
      ) {
        error[field] = matchMessage;
        errorStatus = false;
      }
    }
    setErrors(error);
    return errorStatus;
  };

  useEffect(() => {
    if (address?.postcode) {
      checkPinCode(address?.postcode, true);
    }
    const item = countryListings.find(
      item =>
        item.country_id ===
        (address?.country_code?.id ? address?.country_code?.id : 'IN'),
    );

    setRegionList(item?.states || []);
    const alternateTelephoneIndex = address?.custom_attributes?.findIndex(
      e => e.attribute_code === 'alternate_telephone',
    );
    let state = address?.region
      ? item?.states?.find(r => r.id === address?.region?.region_id)
      : {id: '', state_code: '', state_name: ''};
    if (address?.state) {
      const states = item?.states?.filter(
        item => item.state_name.toLowerCase() === address?.state?.toLowerCase(),
      );
      if (states?.length > 0) {
        state = states[0];
      }
    }
    const alternateNo =
      address?.custom_attributes?.[alternateTelephoneIndex]?.value;
    getAddressValidations(item?.id);
    setAlterNoShow(alternateNo ? true : false);
    const lastName =
      address?.firstname === address?.lastname ? '' : address?.lastname;
    setValues(prev => ({
      ...prev,
      firstName:
        [
          address?.firstname ||
            `${userInfo?.firstname || ''} ${userInfo?.lastname || ''}` ||
            '',
          lastName || '',
        ]
          .filter(Boolean)
          .join(' ') || '',
      postcode: address?.postcode || '',
      street: address?.street?.[0] || '',
      landmark: address?.street?.[1] || '',
      city: address?.city || '',
      telephone: address?.telephone || userInfo?.mobile || '',
      default_shipping: address?.default_shipping
        ? Boolean(address?.default_shipping)
        : userInfo?.addresses?.length === 0
        ? true
        : false,
      default_billing: false,
      alternateNumber: alternateNo ?? '',
      country_code: item,
      region: state,
      vat_id: address?.vat_id ? address?.vat_id : '',
    }));
  }, [address, countryListings]);

  const onErrorView = (error: string) => {
    return (
      <>
        <Spacer size="sx" />
        <Label
          text={String(t(error) || '')}
          size="m"
          weight="400"
          color="red3"
        />
      </>
    );
  };

  const checkPinCode = useCallback(
    async (postcode: string, check = false) => {
      try {
        const {data, status} = await checkPinCodeValid(postcode);
        dispatch(setLoading(false));
        if (!check) {
          setValidPinCode(status ? false : true);
        }
        if (status && data) {
          setPinCodeData(data);
          if (!check) {
            const states = regionList?.filter(
              item =>
                item.state_code.toLowerCase() ===
                data?.district?.state?.state_code?.toLowerCase(),
            );
            const obj = {
              city: data?.district?.district_name,
            };
            if (states?.length > 0 && states[0]) {
              obj['region'] = states[0];
              setErrors({...errors, region: '', city: ''});
            } else {
              setErrors({...errors, city: ''});
            }
            setValues(prev => ({
              ...prev,
              ...obj,
            }));
          }
        }
      } catch (error) {}
    },
    [regionList],
  );

  const onChangeText = (text, key) => {
    if (
      key === 'region' &&
      text?.state_code !== pinCodeData?.district?.state?.state_code &&
      values?.postcode
    ) {
      showErrorMessage(t('validations.postcodeState'));
      Keyboard.dismiss();
    }
    const validationRules = {
      firstName: {
        required: t('validations.fullNameReq'),
        pattern: stringReg,
        invalid: t('validations.fullNameCorrect'),
      },
      telephone: {
        required: t('validations.telephoneRequired'),
        pattern: phoneReg,
        invalid: t('validations.telephoneCorrect'),
      },
      alternateNumber: {
        required:
          key === 'alternateNumber' && text?.length > 0
            ? t('validations.alternateRequired')
            : '',
        pattern: phoneReg,
        invalid: t('validations.alternateCorrect'),
      },
      street: {
        required: t('validations.street'),
      },
      postcode: {
        required: t('validations.postcodeRequired'),
        pattern: postCodeFormat,
        invalid: t('validations.validPostCode'),
      },
      city: {
        required: t('validations.cityReq'),
        pattern: stringReg,
        invalid: t('validations.alphabets'),
      },
      region: {
        required: t('validations.region'),
      },
      vat_id: {
        required: values?.tax_required ? t('validations.tax') : '',
        pattern: taxFormat,
        invalid: t('validations.validTex'),
      },
    };

    const error = {...errors};
    const rule = validationRules[key];
    setValues(prev => ({...prev, [key]: text}));
    if (!rule) return;
    if (!text && rule.required) {
      error[key] = rule.required;
    } else if (rule.required && rule.pattern && !text?.match(rule.pattern)) {
      error[key] = rule.invalid || '';
    } else if (key === 'region' && !text?.id) {
      error[field] = rule.required;
    } else if (key === 'country_code' && !text?.country_id) {
      error[field] = rule.required;
    } else if (
      key === 'vat_id' &&
      text?.trim()?.length > 0 &&
      rule.pattern &&
      !text?.trim()?.match(rule.pattern)
    ) {
      error[key] = rule.invalid || '';
    } else {
      error[key] = '';
    }
    setErrors(error);
    if (key === 'country_code') {
      setValues(prev => ({...prev, region: text?.states?.[0]}));
      setRegionList(text?.states || []);
      getAddressValidations(text?.country_id);
    }

    if (key === 'postcode' && text?.length > 5) {
      checkPinCode(text);
    }
    if (key === 'region' && values?.vat_id) {
      checkGst(values?.vat_id, text.state_name);
    }
  };

  const checkGst = async (gst, name, checkReturn = false) => {
    const response = await onCheckGst(gst?.trim(), name);
    const {status, error} = response;
    if (!status) {
      setErrors({...errors, region: error, gst: true});
      if (checkReturn) {
        return response;
      }
    } else {
      setErrors({...errors, region: '', gst: false});
      if (checkReturn) {
        return {status: true};
      }
    }
  };

  const renderItem = (item, selected) => {
    return (
      <View style={[styles.dropDownItem, selected && styles.itemSelected]}>
        <Label
          color={selected ? 'whiteColor' : 'text2'}
          text={item.state_name}
          size="mx"
          weight="500"
        />
      </View>
    );
  };

  const renderLabel = text => {
    return (
      <View style={styles.placeStyle}>
        <Label color="grey" text={text} size="m" weight="500" />
        <Label color="red3" text=" *" size="m" weight="500" />
      </View>
    );
  };

  return (
    <View>
      <KeyboardAvoidingView
        style={styles.fOne}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <View style={styles.fOne}>
          <ErrorHandler
            componentName={`${TAG} AddressForm`}
            onErrorComponent={<View />}>
            <ScrollView
              ref={scrollRef}
              keyboardShouldPersistTaps="always"
              bounces={false}
              showsVerticalScrollIndicator={false}>
              <View style={styles.mainView}>
                <Spacer size="sx" />
                <Label
                  size="mx"
                  text={t(
                    address?.default_billing
                      ? 'addressCard.updateBillingAddress'
                      : 'addressCard.addBillingAddress',
                  )}
                  weight="500"
                  color="black"
                />
                <Spacer size="m" />
                <TextInputBox
                  testID="txtManageAddressFullName"
                  label={t('manageAddress.fullName')}
                  fieldMandatory={true}
                  onChangeText={text =>
                    onChangeText(text.trimStart(), 'firstName')
                  }
                  value={values?.firstName}
                  error={!!errors?.firstName}
                  errorText={errors?.firstName}
                  containerStyle={styles.textStyle}
                  returnKeyType="next"
                  leftIcon={Icons.user}
                />
                <Spacer size="sx" />
                <TextInputBox
                  testID="txtManageAddressTelePhoneNumber"
                  label={t('manageAddress.telephoneNumber')}
                  fieldMandatory={true}
                  onChangeText={text =>
                    onChangeText(text.trimStart(), 'telephone')
                  }
                  value={values?.telephone}
                  error={!!errors?.telephone}
                  errorText={errors?.telephone}
                  containerStyle={styles.textStyle}
                  returnKeyType="done"
                  maxLength={10}
                  keyboardType="phone-pad"
                  leftIcon={Icons.phone1}
                />
                <Spacer size="sx" />
                <View style={styles.fOne}>
                  {alterNoShow ? (
                    <>
                      <TextInputBox
                        testID="txtManageAddressAlternateTelephone"
                        label={t('manageAddress.alternateTelephone')}
                        onChangeText={text =>
                          onChangeText(text.trimStart(), 'alternateNumber')
                        }
                        value={values?.alternateNumber}
                        error={!!errors?.alternateNumber}
                        errorText={errors?.alternateNumber}
                        containerStyle={styles.textStyle}
                        returnKeyType="done"
                        maxLength={10}
                        keyboardType="phone-pad"
                        leftIcon={Icons.mobile}
                      />
                      {(!values?.alternateNumber ||
                        values?.alternateNumber?.length === 0) && (
                        <TouchableOpacity
                          style={styles.closeIcons}
                          onPress={() => [
                            setAlterNoShow(false),
                            onChangeText('', 'alternateNumber'),
                          ]}>
                          <ImageIcon
                            size="l"
                            tintColor="text"
                            icon="closeIcons"
                            resizeMode="contain"
                          />
                        </TouchableOpacity>
                      )}
                    </>
                  ) : (
                    <TouchableOpacity
                      style={[styles.rowCenter, styles.labelTxt]}
                      onPress={() => setAlterNoShow(!alterNoShow)}>
                      <ImageIcon
                        size="l"
                        icon="plus"
                        tintColor="newSunnyOrange"
                      />
                      <Spacer size="xm" type="Horizontal" />
                      <Label
                        size="m"
                        text={t('manageAddress.addAlternate')}
                        fontFamily="Medium"
                        color="newSunnyOrange"
                      />
                      <Spacer size="xs" type="Horizontal" />
                      <Pressable onPress={() => setInfoModel(!infoModel)}>
                        <ImageIcon
                          size="xxl"
                          icon="infoCircle"
                          tintColor="text2"
                        />
                      </Pressable>
                    </TouchableOpacity>
                  )}
                </View>
                <Spacer size="sx" />
                <TextInputBox
                  testID="txtManageAddressFullAddress"
                  label={t('manageAddress.fullAddress')}
                  fieldMandatory={true}
                  onChangeText={text =>
                    onChangeText(text.trimStart(), 'street')
                  }
                  value={values?.street}
                  error={!!errors?.street}
                  errorText={errors?.street}
                  containerStyle={styles.textStyle}
                  returnKeyType="next"
                  leftIcon={Icons.home2}
                  // numberOfLines={4}
                  // multiline={true}
                  // style={styles.addressMainView}
                />
                <Spacer size="sx" />
                <View style={styles.rowView}>
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressLandmark"
                      label={t('otherText.landmark')}
                      onChangeText={text => {
                        setValues(prev => ({
                          ...prev,
                          landmark: text.trimStart(),
                        }));
                      }}
                      value={values?.landmark}
                      error={!!errors?.landmark}
                      errorText={errors?.landmark}
                      containerStyle={[styles.textStyle,{paddingRight:Sizes.l}]}
                      returnKeyType="next"
                      leftIcon={Icons.map}
                      maxLength={30}
                    />
                  </View>
                  <Spacer size="m" type="Horizontal" />
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressPostcode"
                      label={t('manageAddress.pincode')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'postcode')
                      }
                      value={values?.postcode}
                      containerStyle={styles.textStyle}
                      error={errors?.postcode || validPinCode ? true : false}
                      errorText={String(
                        errors?.postcode || validPinCode
                          ? values.postcode?.trim() === ''
                            ? t('validations.postcodeRequired')
                            : t('validations.validPostCode')
                          : '',
                      )}
                      returnKeyType="done"
                      maxLength={6}
                      keyboardType={
                        Platform.OS === 'ios' ? 'number-pad' : 'numeric'
                      }
                      leftIcon={Icons.location1}
                    />
                  </View>
                </View>
                <Spacer size="sx" />
                <View style={styles.rowView}>
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressCity"
                      label={t('manageAddress.city')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'city')
                      }
                      value={values?.city}
                      error={!!errors?.city}
                      errorText={errors?.city}
                      containerStyle={styles.textStyle}
                      returnKeyType="next"
                      leftIcon={Icons.locationPin}
                    />
                  </View>
                  <Spacer size="m" type="Horizontal" />
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressGstIn"
                      label={t('orderTrack.gstIn')}
                      fieldMandatory={values?.tax_required ? true : false}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'vat_id')
                      }
                      value={values?.vat_id}
                      error={!!errors?.vat_id || errors?.gst}
                      errorText={errors?.vat_id}
                      containerStyle={styles.textStyle}
                      returnKeyType="next"
                      onBlur={() => {
                        if (
                          values?.region?.state_name &&
                          values?.vat_id?.length > 0
                        ) {
                          checkGst(values?.vat_id, values?.region?.state_name);
                        }
                      }}
                    />
                  </View>
                </View>
                <Spacer size="m" />
                <ErrorHandler
                  componentName={`${TAG} DropDown`}
                  onErrorComponent={<View />}>
                  <DropDown
                    testID="manageAddressSelectRegion"
                    heading={t('manageAddress.selectState')}
                    placeholder={renderLabel(t('manageAddress.selectState'))}
                    fieldMandatory={true}
                    listContainerStyle={styles.dropDownItemView}
                    selectedTextStyle={styles.inputTextView}
                    styleDropDown={[
                      styles.styleDropDown,
                      errors?.region && styles.errorBorder,
                    ]}
                    itemTextStyle={styles.dropDownTextStyle}
                    placeholderStyle={styles.placeTextStyle}
                    labelStyle={styles.labelStyle}
                    hideLabel={true}
                    search={false}
                    value={values?.region?.id ? values?.region : null}
                    onChange={item => onChangeText(item, 'region')}
                    returnValueOnly={false}
                    data={regionList}
                    renderItem={renderItem}
                    valueField="state_code"
                    labelField="state_name"
                    dropdownPosition= {itemLength === 1 ? 'top' : (itemLength === index || itemLength === index+1) ? 'top' : 'bottom'}
                    error={errors.region ? t(errors.region) : null}
                    renderRightIcon={(focus: boolean) => {
                      if (focus && Platform.OS === 'ios') {
                        setTimeout(() => {
                          scrollRef?.current?.scrollTo({
                            y: 100,
                            animated: true,
                          });
                        }, 500);
                      }
                      return (
                        <ImageIcon
                          size="xl"
                          tintColor="text"
                          icon="dUpArrow"
                          resizeMode="contain"
                          style={!focus && styles.downImg}
                        />
                      );
                    }}
                  />
                </ErrorHandler>
                {errors?.region && onErrorView(errors?.region || '')}

                <Spacer size="xm" />

                <View style={styles.btnContainer}>
                  <TouchableOpacity
                    style={styles.btnView}
                    onPress={() => onCancel()}>
                    <Label
                      size="mx"
                      text={t('buttons.cancel')}
                      weight="500"
                      color="text"
                    />
                  </TouchableOpacity>
                  <Spacer size="xm" type="Horizontal" />
                  <Button
                    style={styles.btnView}
                    labelStyle={styles.btnTxt}
                    text={t('buttons.save')}
                    withGradient
                    gradientColors={[colors.coral, colors.persimmon]}
                    onPress={() => btnClickCallBack(() => createUserAddress())}
                    labelSize="mx"
                    size="small"
                    radius="xm"
                    labelColor="whiteColor"
                    weight="500"
                  />
                </View>
              </View>
            </ScrollView>
          </ErrorHandler>
        </View>
      </KeyboardAvoidingView>
      {infoModel && (
        <DeliveryInfoModal
          visible={infoModel}
          onClose={() => setInfoModel(false)}
          infoIcon="phoneNumber"
        />
      )}
    </View>
  );
};

export default BillingAddress;

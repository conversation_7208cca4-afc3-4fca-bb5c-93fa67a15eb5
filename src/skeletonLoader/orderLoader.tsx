import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const OrderLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1700}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="10" rx="5" ry="5" width={width - 20} height="200" />
        <Rect x="10" y="220" rx="5" ry="5" width={width - 20} height="200" />
        <Rect x="10" y="430" rx="5" ry="5" width={width - 20} height="200" />
        <Rect x="10" y="640" rx="5" ry="5" width={width - 20} height="200" />
        <Rect x="10" y="850" rx="5" ry="5" width={width - 20} height="200" />
        <Rect x="10" y="1060" rx="5" ry="5" width={width - 20} height="200" />
        <Rect x="10" y="1270" rx="5" ry="5" width={width - 20} height="200" />
        <Rect x="10" y="1480" rx="5" ry="5" width={width - 20} height="200" />
      </ContentLoader>
    </ScrollView>
  );
};

export default OrderLoader;

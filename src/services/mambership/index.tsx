import Api from 'services';
import {apiEndpoints, apiRoute} from 'config/apiEndpoint';
import tokenClass from 'utils/token';

const API = new Api();

export const myMemberShipsData = async () => {
  const loginStatus = await tokenClass.loginStatus();
  if(loginStatus){
    const url = `${apiRoute.reward}${apiEndpoints.customerMemberships}`;
    const resp = await API.get(url);
    return resp;
  }
};

export const memberShipsPlanApi = async () => {
  const url = `${apiRoute.reward}${apiEndpoints.memberships}`;
  const resp = await API.get(url, false);
  return resp;
};

export const getMemberShipData = async () => {
  const url = `${apiRoute.reward}${apiEndpoints.memberships}/${apiEndpoints.content}`;
  const resp = await API.get(url, false);
  return resp;
};

export const memberShipsFaqApi = async parents => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.faqItems}`;
  const resp = await API.post(url, parents);
  return resp;
};

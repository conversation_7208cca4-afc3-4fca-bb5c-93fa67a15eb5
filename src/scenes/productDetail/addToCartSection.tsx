import React, {useCallback, useMemo} from 'react';
import {View} from 'react-native';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from 'utils/ErrorHandler';
import {Button} from 'components/molecules';
import {Quantity} from 'components/atoms';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';

interface AddToCartSectionProps {
  isAddToCartPressed: boolean;
  product: any;
  productLoader: boolean;
  updateSelectedSingleProducts: (
    qty: number,
    price: number,
    sku: string,
  ) => void;
  setIsAddToCartPressed: (pressed: boolean) => void;
  t: any;
  styles: any;
  quantityInputRef: any;
  qtyForUI: number;
  onUpdate: (qty: number, change: boolean) => void;
  TAG: string;
}

const AddToCartSection: React.FC<AddToCartSectionProps> = React.memo(
  ({
    isAddToCartPressed,
    product,
    productLoader,
    updateSelectedSingleProducts,
    setIsAddToCartPressed,
    t,
    quantityInputRef,
    qtyForUI,
    onUpdate,
    TAG,
  }) => {
    const {colors} = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

    const onAddToCartPress = useCallback(() => {
      updateSelectedSingleProducts(
        1,
        product?.pricing?.selling_price,
        product?.sku,
      );
      setIsAddToCartPressed(true);
    }, [
      updateSelectedSingleProducts,
      product?.pricing?.selling_price,
      product?.sku,
      setIsAddToCartPressed,
    ]);

    const minQty = 0;
    const maxQty = useMemo(
      () => product?.inventory?.max_sale_qty ?? 1,
      [product?.inventory?.max_sale_qty],
    );

    return (
      <View>
        {!isAddToCartPressed ? (
          !product?.pricing?.is_price_request && !productLoader ? (
            <View style={styles.addBtn}>
              <ErrorHandler
                componentName={`${TAG} Button`}
                onErrorComponent={<View />}>
                <Button
                  type="bordered"
                  onPress={onAddToCartPress}
                  text={t('buttons.add')}
                  radius="sx"
                  selfAlign="center"
                  style={styles.qtyStyle}
                  labelStyle={styles.labelStyle}
                  borderColor="newSunnyOrange"
                  labelColor="newSunnyOrange"
                  paddingHorizontal="x6l"
                />
              </ErrorHandler>
            </View>
          ) : null
        ) : (
          <ErrorHandler
            componentName={`${TAG} Quantity`}
            onErrorComponent={<View />}>
            <Quantity
              textInputRef={quantityInputRef}
              // min={product?.inventory?.min_sale_qty ?? 1}
              min={minQty}
              max={maxQty}
              value={qtyForUI}
              onUpdate={onUpdate}
              backgroundColor="newSunnyOrange"
              inputStyle={styles.qtytextStyle}
              style={styles.qtytextStyle}
              minusStyle={styles.addWidth}
              plusStyle={styles.addWidth}
              hideCartBtn
            />
          </ErrorHandler>
        )}
      </View>
    );
  },
);

export default AddToCartSection;

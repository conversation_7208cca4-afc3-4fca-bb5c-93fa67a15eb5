import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Header, PhoneInputText} from 'components/molecules';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {
  ImageBackground,
  Platform,
  FlatList,
  ScrollView,
  View,
  TouchableOpacity,
} from 'react-native';
import Modal from 'react-native-modal';
import {ImageIcon, Label, Spacer, CatalogueModal} from 'components/atoms';
import {Sizes} from 'common';
import LinearGradient from 'react-native-linear-gradient';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {validateSellDentalkart} from 'utils/validationError';
import {
  faqData,
  termsAndConditions,
  whyRegisterWithDentalKart,
} from 'staticData';
import {sellOnDentalKart} from 'services/home';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {useDispatch} from 'react-redux';
import { debugError } from 'utils/debugLog';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const SellOnDentalkart = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const [catalogueData, setCatalougeData] = useState('');
  const [formData, setFormData] = useState<{
    companyName: string;
    companyType: string;
    mobile: string;
    emailId: string;
    city: string;
    address: string;
    productName: string;
    gstNumber: string;
    catalogue: string;
  }>({
    companyName: '',
    companyType: '',
    mobile: '',
    emailId: '',
    city: '',
    address: '',
    productName: '',
    gstNumber: '',
    catalogue: '',
  });
  const [modal, setModal] = useState(false);
  const [catalogueModal, setCatalougeModal] = useState(false);
  const [showThankYou, setShowThankYou] = useState(false);
  const [errors, setErrors] = useState({});
  const inputRef = useRef(null);
  const [key, setKey] = useState(0);

  const handleInputChange = (name, value) => {
    setFormData(prevFormData => ({
      ...prevFormData,
      [name]: value,
    }));
  };

  const handleForm = useCallback(async () => {
    dispatch(setLoading(true));
    try {
      const {data, status} = await sellOnDentalKart({
        company_name: formData?.companyName,
        gst_number: formData?.gstNumber,
        email_id: formData?.emailId,
        contact_no: formData?.mobile,
        company_type: formData?.companyType,
        city: formData?.city,
        address: formData?.address,
        product_name: formData?.productName,
        catalogue: formData?.catalogue,
      });
      if (data) {
        dispatch(setLoading(false));
        if (data?.request_id) {
          setFormData({
            companyName: '',
            companyType: '',
            mobile: '',
            emailId: '',
            city: '',
            address: '',
            productName: '',
            gstNumber: '',
            catalogue: '',
          });
          showSuccessMessage(data.message);
          setShowThankYou(!showThankYou);
          setTimeout(() => {
            setShowThankYou(!showThankYou);
          }, 3000);
        }
      }
    } catch (error) {
      dispatch(setLoading(false));
      debugError('Error sending form data:', error);
    }
  }, [formData]);

  const handleSubmit = async () => {
    const isValid = await validate(formData);
    if (isValid) {
      await handleForm();
      setKey(prevKey => prevKey + 1);
    } else {
      showErrorMessage(t('validations.fillAllForm'));
    }
  };

  const clearSelectedCatalouge = () => {
    setCatalougeData('');
    setCatalougeModal(!catalogueData);
  };

  const saveCatalouge = () => {
    setFormData(prevState => ({
      ...prevState,
      catalogue: catalogueData?.uri,
    }));
    setCatalougeData('');
    setCatalougeModal(!catalogueModal);
  };

  const deleteItem = () => {
    setFormData(prevState => ({
      ...prevState,
      catalogue: '',
    }));
  };

  const validate = useCallback(async formData => {
    try {
      const valid = await validateSellDentalkart().validateSync(formData, {
        abortEarly: false,
      });
      setErrors({});
      return true;
    } catch (err) {
      const errorsMessages = {};
      err?.inner?.forEach(element => {
        const path = element.path;
        errorsMessages[path]
          ? errorsMessages[path].push(t(element.message))
          : (errorsMessages[path] = [t(element.message)]);
      });
      setErrors(errorsMessages);
      return false;
    }
  }, []);

  const ThankYouMessage = ({visible, onClose}) => {
    return (
      <>
        <Modal
          onBackButtonPress={onClose}
          isVisible={modalVisibleImage}
          animationIn="fadeIn"
          animationOut="fadeOut"
          animationInTiming={75}
          animationOutTiming={75}
          backdropOpacity={0.01}
          style={styles.modalStyle}>
          <View style={styles.modalBackground}>
            <View style={styles.modalContainer}>
              <FastImage source={Icons.thankYou} style={styles.thankYouImage} />
              <Spacer size="mx" />
              <View>
                <Label
                  color="velvetLightPink2"
                  size="xl"
                  fontFamily="SemiBold"
                  align="center"
                  text={t('sellOnDentalkart.thankYou')}
                />
                <Label
                  color="velvetLightPink2"
                  align="center"
                  size="xl"
                  fontFamily="Medium"
                  text={t('sellOnDentalkart.registerd')}
                />
              </View>
              <Spacer size="mx" />
              <View>
                <Label
                  color="skyBlue22"
                  align="center"
                  size="mx"
                  fontFamily="Medium"
                  text={`Note: ${t('sellOnDentalkart.willContact')}`}
                />
              </View>
              <Spacer size="mx" />
            </View>
          </View>
        </Modal>
      </>
    );
  };
  const renderItem = ({item}) => (
    <>
      <View style={styles.miniCard}>
        <Label
          style={styles.label}
          color="categoryTitle"
          size="l"
          fontFamily="SemiBold"
          text={item?.heading}
        />
        <Label
          style={styles.label}
          color="categoryTitle"
          size="mx"
          fontFamily="Medium"
          text={item?.label}
        />
      </View>
      <Spacer size="xm" />
    </>
  );
  const renderItems = ({item}) => (
    <View style={styles.rowDirection}>
      <Label size="l" color="text2" text={item?.icon} />
      <Spacer type="Horizontal" size="m" />
      <Label size="m" style={styles.flex} color="grey" text={item?.label} />
    </View>
  );

  const handleFocusInput = () => {
    inputRef.current?.focus();
  };

  useEffect(() => {
    validate(formData);
  }, [formData, validate]);

  return (
    <>
      <Header
        navigation={navigation}
        backButton={true}
        bagIcon
        useInsets
        searchIcon
        text={t('sellOnDentalkart.sellOnDentalkart')}
      />
      <SafeAreaView style={styles.mainContainer}>
        <FlatList
          data={['']}
          showsVerticalScrollIndicator={false}
          renderItem={() => (
            <>
              {/* ..................................dentalKart Card Design.................................. */}
              <View>
                <Spacer size="m" />
                <LinearGradient
                  colors={[colors.skyBlue19, colors.background]}
                  style={styles.cardRadius}
                  start={{x: 0, y: 0}}
                  end={{x: Sizes.x, y: 1}}>
                  <View style={styles.cardView}>
                    <View style={styles.doubleFlex}>
                      <Label
                        color="black"
                        style={styles.label}
                        size="xms"
                        fontFamily="Regular"
                        text={t('sellOnDentalkart.productToday')}
                      />
                      <Label
                        color="categoryTitle"
                        style={styles.label}
                        size="l"
                        fontFamily="SemiBold"
                        text={t('sellOnDentalkart.become')}
                      />
                      <Label
                        color="black"
                        fontFamily="Regular"
                        style={[styles.label, styles.cardDes]}
                        text={t('sellOnDentalkart.expand')}
                      />
                      <Spacer size="x5l" />
                      <TouchableOpacity
                        style={styles.rowDirection}
                        onPress={handleFocusInput}>
                        <LinearGradient
                          colors={[colors.coral, colors.persimmon]}
                          start={{x: 0, y: 0}}
                          end={{x: Sizes.x, y: 1}}
                          style={styles.regButton}>
                          <Label
                            size="m"
                            color="whiteColor"
                            fontFamily="Bold"
                            text={t('sellOnDentalkart.registerNow')}
                          />
                        </LinearGradient>
                      </TouchableOpacity>
                      <Spacer size="xxl" />
                    </View>
                    <View style={styles.flex}>
                      <FastImage
                        source={Icons.partnerShip}
                        style={styles.cardImage}
                      />
                    </View>
                  </View>
                </LinearGradient>
              </View>
              <Spacer size="m" />
              {/* .................................. why register with dentalKart.................................. */}

              <ScrollView
                style={styles.subContainer}
                showsVerticalScrollIndicator={false}>
                <FastImage
                  source={Icons.registerBG}
                  resizeMode="stretch"
                  style={styles.commonBackground}
                />
                <View style={styles.commonDentalKartText}>
                  <Label
                    style={styles.registerLabel}
                    color="text"
                    size="l"
                    align="center"
                    fontFamily="Medium"
                    text={t('sellOnDentalkart.whyWith')}
                  />
                  <ImageBackground
                    style={styles.dentalKartIcon}
                    resizeMode="contain"
                    source={Icons.dentalKartBG}>
                    <Label
                      size="l"
                      color="categoryTitle"
                      fontFamily="SemiBold"
                      align="center"
                      style={styles.label}
                      text={t('sellOnDentalkart.dentalkart')}
                    />
                  </ImageBackground>
                </View>
                <Spacer size="xms" />
                <FlatList
                  data={whyRegisterWithDentalKart}
                  renderItem={renderItem}
                  showsVerticalScrollIndicator={false}
                />
                <Spacer size="m" />
                <TouchableOpacity
                  style={[styles.regButtonView, {alignSelf: 'center'}]}
                  onPress={handleFocusInput}>
                  <LinearGradient
                    colors={[colors.coral, colors.persimmon]}
                    start={{x: 0, y: 0}}
                    end={{x: Sizes.x, y: 1}}
                    style={[styles.regButton, styles.regBtn1]}>
                    <Label
                      size="m"
                      color="whiteColor"
                      fontFamily="Bold"
                      text={t('sellOnDentalkart.registerNow')}
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </ScrollView>
              <Spacer size="m" />
              {/* .................................Become a Seller on DentalKart.................................. */}

              <View style={styles.sellerView}>
                <Spacer size="l" />
                <FastImage
                  source={Icons.becomeSellerBG}
                  resizeMode="stretch"
                  style={styles.commonBackground}
                />
                <View style={styles.heading}>
                  <Label
                    size="l"
                    color="text"
                    fontFamily="Medium"
                    align="center"
                    style={styles.label}
                    text={t('sellOnDentalkart.whoSell')}
                  />
                  <ImageBackground
                    style={styles.dentalKartIcon}
                    resizeMode="contain"
                    source={Icons.dentalKartBG}>
                    <Label
                      size="l"
                      color="categoryTitle"
                      fontFamily="SemiBold"
                      align="center"
                      style={styles.label}
                      text={t('sellOnDentalkart.dentalkart')}
                    />
                  </ImageBackground>
                </View>
                <Spacer size="l" />
                <View style={styles.text}>
                  <Label
                    size="m"
                    style={styles.label}
                    color="categoryTitle"
                    align="center"
                    fontFamily="Regular"
                    text={t('sellOnDentalkart.welcomeNote')}
                  />
                </View>
                <Spacer size="m" />
              </View>
              <Spacer size="m" />
              {/* .................................. how register with dentalKart.................................. */}

              <View>
                <View style={styles.commonDentalKartText}>
                  <Label
                    size="l"
                    align="center"
                    fontFamily="Medium"
                    color="text"
                    text={t('sellOnDentalkart.howWith')}
                  />
                  <ImageBackground
                    style={styles.dentalKartIcon}
                    resizeMode="contain"
                    source={Icons.dentalKartBG}>
                    <Label
                      size="l"
                      color="categoryTitle"
                      fontFamily="SemiBold"
                      align="center"
                      style={styles.label}
                      text={t('sellOnDentalkart.dentalkart')}
                    />
                  </ImageBackground>
                </View>
                <Spacer size="m" />
                <FastImage
                  source={Icons.underPrizeIcon}
                  style={styles.imageContainer}
                />
                <Spacer size="m" />
                <View>
                  <Label
                    size="mx"
                    text={t('sellOnDentalkart.step')}
                    color="grey"
                    align="center"
                    fontFamily="Regular"
                  />
                  <Spacer size="l" />
                  <View style={styles.commonDentalKartText}>
                    <Label
                      size="l"
                      text={t('sellOnDentalkart.sellOn')}
                      color="text"
                      align="center"
                      fontFamily="Medium"
                    />
                    <ImageBackground
                      style={styles.dentalKartIcon}
                      resizeMode="contain"
                      source={Icons.dentalKartBG}>
                      <Label
                        size="l"
                        color="categoryTitle"
                        fontFamily="SemiBold"
                        align="center"
                        style={styles.label}
                        text={t('sellOnDentalkart.dentalkart')}
                      />
                    </ImageBackground>
                  </View>
                  <Spacer size="l" />
                  <Label
                    size="mx"
                    text={t('sellOnDentalkart.accept') + ' '}
                    color="wedgewoodBlue"
                    align="center"
                    fontFamily="Medium">
                    <Label
                      size="mx"
                      text={t('sellOnDentalkart.dentalkart1')}
                      color="wedgewoodBlue"
                      align="center"
                      fontFamily="Bold"
                    />
                  </Label>

                  <Spacer size="l" />
                </View>
              </View>
              {/* .................................. sell product form on dentalkart .................................. */}

              <ScrollView
                style={styles.form}
                showsVerticalScrollIndicator={false}
                key={key}>
                <View>
                  <Label
                    size="m"
                    color="wedgewoodBlue"
                    fontFamily="Medium"
                    style={styles.label}
                    text={t('sellOnDentalkart.enquiryForm') + ' '}>
                    <Label
                      size="m"
                      color="grey"
                      fontFamily="Medium"
                      style={styles.label}
                      text={t('sellOnDentalkart.fillForm')}
                    />
                  </Label>
                </View>
                <Spacer size="m" />
                <View>
                  <Label
                    text={t('sellOnDentalkart.companyName')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKCompanyName"
                    style={styles.inputBorder}
                    onChangeText={text =>
                      handleInputChange('companyName', text)
                    }
                    icon="newUser"
                    ref={inputRef}
                    placeholder={t('sellOnDentalkart.companyNamePlace')}
                    value={formData?.companyName}
                    error={String(errors?.companyName?.[0] || '')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                  <Spacer size="xm" />

                  <Label
                    text={t('sellOnDentalkart.companyAs')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKCompanyType"
                    onChangeText={text =>
                      handleInputChange('companyType', text)
                    }
                    style={styles.inputBorder}
                    icon="newUser"
                    placeholder={t('sellOnDentalkart.companyAsPlace')}
                    value={formData?.companyType}
                    error={String(errors?.companyType?.[0] || '')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                  <Spacer size="xm" />

                  <Label
                    text={t('sellOnDentalkart.mobileNumber')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKMobileNumber"
                    type="numeric"
                    icon="callIcon"
                    value={formData?.mobile}
                    onChangeText={text => handleInputChange('mobile', text)}
                    maxLength={10}
                    style={[
                      styles.inputBorder,
                      Platform.OS === 'ios' && styles.iosInput,
                    ]}
                    placeholder={t('sellOnDentalkart.mobileNumberPlace')}
                    error={String(errors?.mobile?.[0] || '')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                  <Spacer size="xm" />

                  <Label
                    text={t('sellOnDentalkart.email')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKEmail"
                    onChangeText={text => handleInputChange('emailId', text)}
                    type="email-address"
                    style={styles.inputBorder}
                    icon="emailIcon"
                    value={formData?.emailId}
                    placeholder={t('sellOnDentalkart.emailPlace')}
                    error={String(errors?.emailId?.[0] || '')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                  <Spacer size="xm" />

                  <Label
                    text={t('sellOnDentalkart.city')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKCity"
                    onChangeText={text => handleInputChange('city', text)}
                    icon="locationIcon"
                    value={formData?.city}
                    style={[
                      styles.inputBorder,
                      Platform.OS === 'ios' && styles.iosInput,
                    ]}
                    placeholder={t('sellOnDentalkart.cityPlace')}
                    error={String(errors?.city?.[0] || '')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                  <Spacer size="xm" />

                  <Label
                    text={t('sellOnDentalkart.address')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKAddress"
                    onChangeText={text => handleInputChange('address', text)}
                    value={formData?.address}
                    icon="locationIcon"
                    style={[
                      styles.inputBorder,
                      Platform.OS === 'ios' && styles.iosInput,
                    ]}
                    placeholder={t('sellOnDentalkart.addressPlace')}
                    error={String(errors?.address?.[0] || '')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                  <Spacer size="xm" />

                  <Label
                    text={t('sellOnDentalkart.productName')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKProductName"
                    onChangeText={text =>
                      handleInputChange('productName', text)
                    }
                    value={formData?.productName}
                    icon="box"
                    style={[
                      styles.inputBorder,
                      Platform.OS === 'ios' && styles.iosInput,
                    ]}
                    error={String(errors?.productName?.[0] || '')}
                    placeholder={t('sellOnDentalkart.productNamePlace')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                  <Spacer size="xm" />

                  <Label
                    text={t('sellOnDentalkart.gstNo')}
                    size="mx"
                    color="categoryTitle4"
                    fontFamily="Medium"
                  />
                  <Spacer size="xm" />
                  <PhoneInputText
                    testID="txtSellOnDKgst"
                    onChangeText={text => handleInputChange('gstNumber', text)}
                    value={formData?.gstNumber}
                    type="name-phone-pad"
                    maxLength={15}
                    icon="newUser"
                    style={[
                      styles.inputBorder,
                      Platform.OS === 'ios' && styles.iosInput,
                    ]}
                    error={String(errors?.gstNumber?.[0] || '')}
                    placeholder={t('sellOnDentalkart.gstNoPlace')}
                    placeholderTextColor={colors.placeHoldersTextColor1}
                  />
                </View>
                <Spacer size="m" />

                {formData?.catalogue ? (
                  <>
                    <View style={styles.rowDirection}>
                      {formData.catalogue.length > 0 && (
                        <FastImage
                          source={{uri: formData.catalogue}}
                          style={styles.selectedCatalogue} // Adjust styles as needed
                        >
                          <TouchableOpacity onPress={() => deleteItem()}>
                            <ImageIcon
                              style={styles.imageCrossIcon}
                              size="xl"
                              icon="xRedIcon"
                            />
                          </TouchableOpacity>
                        </FastImage>
                      )}
                      {/* {formData.catalogue.map((uri, index) => (
                      <FastImage
                        key={index}
                        source={{uri}}
                        style={styles.selectedCatalogue}>
                        <TouchableOpacity onPress={() => deleteItem(index)}>
                          <ImageIcon
                            style={styles.imageCrossIcon}
                            size="xl"
                            icon="xRedIcon"
                          />
                        </TouchableOpacity>
                      </FastImage>
                    ))} */}
                      <Spacer type="Vertical" size="m" />
                    </View>
                    <Spacer size="m" />
                  </>
                ) : null}

                {/* ..................................Catalouge Modal.................................. */}

                <CatalogueModal
                  visible={catalogueModal}
                  catalogueData={catalogueData}
                  saveCatalouge={saveCatalouge}
                  clearSelectedCatalouge={clearSelectedCatalouge}
                  setCatalougePress={(values: CatalogueData) =>
                    setCatalougeData(values)
                  }
                  onClose={() => [
                    setCatalougeModal(!catalogueModal),
                    setCatalougeData(''),
                  ]}
                />

                <View style={styles.regButtonView}>
                  <TouchableOpacity
                    style={[
                      styles.regButtonCatalogue,
                      {backgroundColor: colors.categoryTitle4},
                    ]}
                    onPress={() => setCatalougeModal(!catalogueModal)}>
                    <Label
                      size="mx"
                      color="whiteColor"
                      fontFamily="Medium"
                      text={t('sellOnDentalkart.uploadCatalogue')}
                    />
                  </TouchableOpacity>
                </View>
                <Spacer size="m" />
                <View style={styles.regButtonView}>
                  <TouchableOpacity onPress={handleSubmit}>
                    <LinearGradient
                      colors={[colors.coral, colors.persimmon]}
                      start={{x: 0, y: 0}}
                      end={{x: Sizes.x, y: 1}}
                      style={styles.regButtonCatalogue}>
                      <Label
                        size="mx"
                        color="whiteColor"
                        fontFamily="Medium"
                        text={t('sellOnDentalkart.submit')}
                      />
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </ScrollView>
              <Spacer size="m" />
              {/* .................................. terms and conditions UI .................................. */}

              <View>
                <TouchableOpacity onPress={() => setModal(!modal)}>
                  <LinearGradient
                    colors={[colors.offWhite1, colors.categoryTitle4]}
                    start={{x: 0, y: 0}}
                    end={{x: 1.5, y: 1}}
                    style={styles.terms}>
                    <Label
                      size="l"
                      color="categoryTitle"
                      fontFamily="Medium"
                      text={t('sellOnDentalkart.terms')}
                      style={styles.label}
                    />

                    <ImageIcon
                      size="xxl"
                      tintColor="whiteColor"
                      icon={modal ? 'arrowUp' : 'downArrowIcon'}
                    />
                  </LinearGradient>
                </TouchableOpacity>
                {modal ? (
                  <View style={styles.termAndConditionModal}>
                    <FlatList
                      data={termsAndConditions}
                      renderItem={renderItems}
                      showsVerticalScrollIndicator={false}
                      ItemSeparatorComponent={<Spacer size="m" />}
                    />
                  </View>
                ) : null}
              </View>
              <Spacer size="m" />
              {/* .................................. Seller Success Stories UI .................................. */}

              <View>
                <LinearGradient
                  colors={[
                    colors.categoryTitle7,
                    colors.categoryTitle5,
                    colors.categoryTitle7,
                  ]}
                  useAngle
                  angle={90}
                  style={styles.storyHeading}>
                  <View>
                    <Label
                      color="whiteColor"
                      style={styles.label}
                      size="l"
                      align="center"
                      fontFamily="SemiBold"
                      text={t('sellOnDentalkart.stories')}
                    />
                  </View>
                </LinearGradient>
                <LinearGradient
                  style={styles.storyView}
                  useAngle
                  angle={180}
                  colors={[
                    colors.categoryTitle5,
                    colors.categoryTitle6,
                    colors.categoryTitle7,
                  ]}>
                  <View>
                    <View style={styles.storyImageView}>
                      <FastImage
                        source={Icons.sellerStoryImage}
                        style={styles.sellerImage}
                      />
                      <Spacer size="m" />
                      <Label
                        color="whiteColor"
                        style={styles.label}
                        size="l"
                        fontFamily="MediumItalic"
                        text={t('sellOnDentalkart.name')}
                      />
                    </View>
                    <Spacer size="xms" />
                    <View style={styles.storyViewContent}>
                      <Label
                        style={styles.comma}
                        lineHeight="x52"
                        fontFamily="MediumItalic"
                        size="x6l"
                        color="whiteColor"
                        text="“"
                      />
                      <Spacer type="Horizontal" size="xms" />
                      <Label
                        size="m"
                        align="center"
                        style={styles.label}
                        color="whiteColor"
                        fontFamily="MediumItalic"
                        text={t('sellOnDentalkart.storiesDes')}
                      />
                    </View>
                    <Spacer size="m" />
                    <View style={styles.videoView}>
                      {/* <VideoPlayer
                    source={{
                      uri: Icons.sellerStoryVideo,
                      type: 'mp4',
                    }}
                  /> */}
                    </View>
                  </View>
                </LinearGradient>
              </View>
              <Spacer size="m" />
              {/* .................................. FAQ UI .................................. */}

              <View style={styles.faqMainView}>
                <FastImage
                  source={Icons.faqBG}
                  resizeMode="stretch"
                  style={styles.commonBackground}
                />
                <View>
                  <Label
                    size="l"
                    fontFamily="Medium"
                    color="categoryTitle"
                    text={t('sellOnDentalkart.faq')}
                  />
                </View>
                <Spacer size="m" />
                <View>
                  <FlatList
                    showsVerticalScrollIndicator={false}
                    ItemSeparatorComponent={<Spacer size="xsl" />}
                    data={faqData}
                    renderItem={({item}) => (
                      <View>
                        <Label
                          text={item.question}
                          color="categoryTitle"
                          size="m"
                          fontFamily="Bold"
                          style={styles.label}
                        />
                        <Spacer size="m" />
                        <Label
                          text={item.answer}
                          color="text2"
                          size="m"
                          fontFamily="Regular"
                          style={styles.label}
                        />
                      </View>
                    )}
                  />
                </View>
              </View>
              <ThankYouMessage
                visible={showThankYou}
                onClose={() => setShowThankYou(!showThankYou)}
              />
            </>
          )}
        />
      </SafeAreaView>
    </>
  );
};

export default SellOnDentalkart;

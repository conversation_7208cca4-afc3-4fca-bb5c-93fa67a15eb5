import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    durationsBox: {
      flex: Sizes.x,
      flexWrap: 'wrap',
      flexDirection: 'row',
    },
    box: {
      backgroundColor: colors.whiteColor,
      paddingHorizontal: Sizes.m,
    },
    fRow: {
      flexDirection: 'row',
    },
    imageBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    image: {
      position: 'relative',
      borderWidth: Sizes.x,
      padding: Sizes.xs,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      backgroundColor: colors.whiteBlue1,
      alignItems: 'center',
      justifyContent: 'center',
      width: Sizes.x60,
      height: Sizes.x60,
      marginRight: Sizes.xm,
    },
    imageSize: {
      width: Sizes.x58,
      height: Sizes.x58,
      borderRadius: Sizes.xm,
    },
    buttonStyle: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    iconStyle: {
      width: Sizes.xxl,
      height: Sizes.xxl,
      marginRight: Sizes.xms,
    },
    orderTxtView: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: Sizes.m,
    },
    underPrizeIconView: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.darkBlue,
      zIndex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    earnedView: {
      flexDirection: 'row',
    },
    buyAgainDropView: {
      backgroundColor: colors.lightCyan,
      width: Sizes.xx4l,
      alignItems: 'center',
      height: Sizes.xx4l,
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    buyAgainSubView: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    canCancelView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: Sizes.l,
      width: '100%',
      alignSelf: 'center',
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xms,
      marginTop: Sizes.s,
    },
    rewardIconSmall: {
      width: Sizes.m,
      height: Sizes.m,
    },
    infoStyle: {},
    rowCenter: {
      flexWrap: 'wrap',
      flexDirection: 'row',
      alignItems: 'center',
    },
    dotStyle: {
      height: Sizes.s,
      width: Sizes.s,
      borderRadius: Sizes.xs,
      backgroundColor: colors.grey2,
    },
    lineStyle: {
      height: Sizes.xms,
      width: Sizes.x,
      backgroundColor: colors.grey2,
    },
    minusStyle: {
      height: 1.5,
      width: 5,
      backgroundColor: colors.brown,
    },
    btnStyle: {
      marginBottom: Sizes.xm,
      height: Sizes.xx4l,
    },
    orangeBtn: {
      backgroundColor: colors.softOrange,
      marginBottom: Sizes.xm,
      height: Sizes.xx4l,
    },
    btnLeftIcon: {
      paddingLeft: Sizes.s,
    },
    orderNote: {
      backgroundColor: colors.lightCyan1,
      borderRadius: Sizes.s,
      height: Sizes.xx,
      width: Sizes.xx,
      justifyContent: 'center',
      alignItems: 'center',
    },
    badge: {
      paddingHorizontal: Sizes.m,
      height: Sizes.x26,
      borderTopRightRadius: Sizes.xm,
      borderBottomRightRadius: Sizes.xm,
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: Sizes.xm,
      alignSelf: 'flex-start',
      marginTop: -Sizes.s,
      justifyContent: 'center',
      alignItems: 'center',
    },
    rCStyle: {
      width: 3,
      height: Sizes.s,
      position: 'absolute',
      zIndex: -Sizes.x,
      top: -Sizes.s,
      left: -3,
    },
    roundStyle: {
      top: -Sizes.s,
      left: -Sizes.sx,
      width: Sizes.sx,
      height: Sizes.s,
      zIndex: Sizes.x,
      position: 'absolute',
    },
    rateIcon: {
      paddingLeft: Sizes.s,
      marginRight: Sizes.xm,
    },
    rateView: {
      backgroundColor: colors.whiteBlue1,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xms,
      borderRadius: Sizes.s,
      flexDirection: 'row',
      alignItems: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    rowView: {
      flex: Sizes.x,
      flexWrap: 'wrap',
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

export default styles;

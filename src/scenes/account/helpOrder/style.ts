import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {flex: Sizes.x},
    answerView: {paddingHorizontal: Sizes.xms},
    feqContainer: {
      flex: Sizes.x,
      width: '100%',
      flexDirection: 'row',
      backgroundColor: colors.whiteColor,
      color: colors.text2,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'space-between',
      fontSize: Sizes.m,
    },
    mainContainer: {
      flex: Sizes.x,
      backgroundColor: colors.grey7,
      paddingHorizontal: Sizes.mx,
    },
    linerColor: {
      height: Sizes.x4l,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    headingView: {
      paddingHorizontal: Sizes.s,
    },
    dropDownMenu: {
      paddingHorizontal: Sizes.xs,
    },
    whatsappIcon: {
      position: 'absolute',
      flexDirection: 'row',
      alignItems: 'center',
      height: Sizes.x46,
      alignSelf: 'flex-end',
      bottom: Sizes.ex0 + Sizes.xms,
      backgroundColor: colors.offWhite,
      paddingLeft: Sizes.xm,
      paddingRight: Sizes.xx4l,
      borderTopLeftRadius: Sizes.xm,
      borderBottomLeftRadius: Sizes.xm,
    },
    whatAppBg: {
      backgroundColor: colors.paleGreen,
      height: Sizes.xx4l,
      width: Sizes.xx4l,
      borderRadius: Sizes.xx + Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: Sizes.m,
    },
    backColor: {backgroundColor: colors.whiteColor, borderRadius: Sizes.xm},
  });

export default styles;

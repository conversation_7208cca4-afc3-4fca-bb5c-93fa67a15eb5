import React from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {Button, PhoneInputText} from 'components/molecules';
import {t} from 'i18next';
import {Formik} from 'formik';
import {bulkProducts} from 'services/productDetail';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {bulkFormValidations} from 'utils/validationError';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {useMemo} from 'react';
import { debugLog } from 'utils/debugLog';

type Props = {
  visible: boolean;
  onClose: () => void;
  product: ProductData | null;
  onSuccess: () => void;
};
const ReqPriceModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose, product, onSuccess} = props;

  const validatePriceAndQuantity = (values: BulkForm) => {
    const errors = {};
    const expectedPrice = parseFloat(values.expectedPrice);
    const bulkQuantity = parseInt(values.bulkQuantity, 10);
    const source = 0;
    let price = product?.pricing?.selling_price || 0;
    if (values.expectedPrice && source === 0) {
      if (price) {
        let inRange = expectedPrice <= price && expectedPrice >= price * 0.5;
        let totalPrice = expectedPrice * bulkQuantity;
        if (!inRange) {
          errors.expectedPrice = `${t('bulk.lessPrice')} ${price * 0.5} ${t(
            'bulk.greaterPrice',
          )} ${price}`;
        }
        if (bulkQuantity && inRange && totalPrice < 10000) {
          errors.bulkQuantity = t('bulk.bulkTitle');
        }
      }
    }
    return errors;
  };

  const createBulkOrder = (values: BulkForm) => {
    let payload = {
      product_id: product?.product_id,
      name: values?.userName,
      email_id: values?.userEmail,
      contact_no: values?.userPhone,
      postcode: values?.userPincode,
      quantity: values?.bulkQuantity,
      expected_price: values?.expectedPrice,
      source: 0,
      address: values.address,
    };
    bulkProducts(payload)
      .then((res: any) => {
        if (res?.status) {
          showSuccessMessage(res?.data?.message);
          onSuccess();
        } else {
          showErrorMessage(t('validations.someThingWrong'));
        }
        onClose();
      })
      .catch(err => {
        showErrorMessage(t('validations.someThingWrong'));
        debugLog(err, 'err res');
      });
  };

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={onClose}
      content={
        <View style={styles.overallView}>
          <Formik
            initialValues={{
              userName: '',
              expectedPrice: '',
              userEmail: '',
              userPincode: '',
              address: '',
              bulkQuantity: '',
              userPhone: '',
            }}
            validate={validatePriceAndQuantity}
            validationSchema={bulkFormValidations}
            onSubmit={(values: any) => {
              createBulkOrder(values);
            }}>
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
            }) => (
              <>
                <Spacer size="l" />
                <Label
                  text={t('otherText.reqPriceTitle')}
                  size="l"
                  weight="500"
                  color="text"
                />
                <Spacer size="m" />
                <View style={styles.bottomLine} />
                <Spacer size="m" />
                <PhoneInputText
                  testID="txtPriceModalBulkName"
                  inputStyle={styles.input}
                  searchIconStyle={styles.searchIconStyle}
                  style={styles.inputBorderStyle}
                  placeholder={t('bulk.namePlace')}
                  icon="newUser"
                  placeholderTextColor={colors.text2}
                  tintColor="text2"
                  onChangeText={handleChange('userName')}
                  onBlur={handleBlur('userName')}
                  value={values.userName}
                  error={touched.userName ? t(errors?.userName) : ''}
                />
                <Spacer size="m" />
                <PhoneInputText
                testID="txtPriceModalBulkPrice"
                  inputStyle={styles.input}
                  searchIconStyle={styles.searchIconStyle}
                  type="number-pad"
                  style={styles.inputBorderStyle}
                  placeholder={t('bulk.pricePlace')}
                  icon="rupeeCircle"
                  placeholderTextColor={colors.text2}
                  tintColor="text2"
                  onChangeText={handleChange('expectedPrice')}
                  onBlur={handleBlur('expectedPrice')}
                  value={values?.expectedPrice}
                  error={touched.expectedPrice ? t(errors?.expectedPrice) : ''}
                />
                <Spacer size="m" />
                <PhoneInputText
                  testID="txtPriceModalEmail"
                  inputStyle={styles.input}
                  searchIconStyle={styles.searchIconStyle}
                  style={styles.inputBorderStyle}
                  placeholder={t('bulk.emailPlace')}
                  icon="emailIcon"
                  placeholderTextColor={colors.text2}
                  tintColor="text2"
                  onChangeText={handleChange('userEmail')}
                  onBlur={handleBlur('userEmail')}
                  value={values.userEmail}
                  error={touched.userEmail ? t(errors?.userEmail) : ''}
                />
                <Spacer size="m" />
                <PhoneInputText
                  testID="txtPriceModalPhone"
                  inputStyle={styles.input}
                  searchIconStyle={styles.searchIconStyle}
                  type="number-pad"
                  style={styles.inputBorderStyle}
                  maxLength={10}
                  placeholder={t('bulk.phonePlace')}
                  icon="phoneIcon"
                  placeholderTextColor={colors.text2}
                  tintColor="text2"
                  onChangeText={handleChange('userPhone')}
                  onBlur={handleBlur('userPhone')}
                  value={values.userPhone}
                  error={touched.userPhone ? t(errors?.userPhone) : ''}
                />
                <Spacer size="m" />
                <PhoneInputText
                  testID="txtPriceModalPinCode"
                  inputStyle={styles.input}
                  searchIconStyle={styles.searchIconStyle}
                  type="number-pad"
                  style={styles.inputBorderStyle}
                  placeholder={t('bulk.pinCodePlace')}
                  maxLength={6}
                  icon="mapPin"
                  placeholderTextColor={colors.text2}
                  tintColor="text2"
                  onChangeText={handleChange('userPincode')}
                  onBlur={handleBlur('userPincode')}
                  value={values.userPincode}
                  error={touched.userPincode ? t(errors?.userPincode) : ''}
                />
                <Spacer size="m" />
                <PhoneInputText
                  testID="txtPriceModalAddress"
                  inputStyle={styles.input}
                  searchIconStyle={styles.searchIconStyle}
                  style={styles.inputBorderStyle}
                  placeholder={t('bulk.addressPlace')}
                  icon="mapPin"
                  placeholderTextColor={colors.text2}
                  tintColor="text2"
                  onChangeText={handleChange('address')}
                  onBlur={handleBlur('address')}
                  value={values.address}
                  error={touched.address ? t(errors?.address) : ''}
                />
                <Spacer size="m" />
                <PhoneInputText
                  testID="txtPriceModalQuantity"
                  inputStyle={styles.input}
                  searchIconStyle={styles.searchIconStyle}
                  style={styles.inputBorderStyle}
                  type="number-pad"
                  placeholder={t('bulk.quantityPlace')}
                  icon="addIcon"
                  placeholderTextColor={colors.text2}
                  tintColor="text2"
                  onChangeText={handleChange('bulkQuantity')}
                  onBlur={handleBlur('bulkQuantity')}
                  value={values.bulkQuantity}
                  error={touched.bulkQuantity ? t(errors?.bulkQuantity) : ''}
                />
                <Spacer size="m" />
                <View style={styles.cancelView}>
                  <Button
                    onPress={() => onClose?.(!visible)}
                    size="large"
                    labelSize="mx"
                    weight="400"
                    paddingHorizontal="x7l"
                    text={t('buttons.cancel')}
                    labelColor="categoryTitle"
                  />
                  <Button
                    onPress={() => handleSubmit(values)}
                    radius="xms"
                    size="large"
                    labelSize="mx"
                    weight="400"
                    paddingHorizontal="x7l"
                    text={t('buttons.submit')}
                    type="bordered"
                    labelColor="sunnyOrange3"
                    borderColor="sunnyOrange3"
                  />
                </View>
                <Spacer size="m" />
              </>
            )}
          </Formik>
        </View>
      }
    />
  );
};

export default React.memo(ReqPriceModal);

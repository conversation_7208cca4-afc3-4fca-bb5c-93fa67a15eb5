import React, {useMemo} from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import DashedLine from 'react-native-dashed-line';

type Props = {
  onClose: () => void;
  visible: boolean;
  message?: string;
};

const FreeBieModal = (props: Props) => {
  const {visible, onClose, message} = props;
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <DynamicHeightModal
      // useInsets
      visible={visible}
      onClose={onClose}
      content={
        <View style={styles.modelSubView}>
          <Label size="l" text="Get Freebie" color="text" fontFamily="Medium" />
          <Spacer size="sx" />
          <DashedLine
            dashLength={4}
            dashThickness={1}
            dashColor={colors.grey2}
          />
          <Spacer size="m" />
          <View style={styles.mainViewReturn}>
            <Label size="mx" text={message} color="text2" fontFamily="Medium" />
          </View>
        </View>
      }
    />
  );
};

export default FreeBieModal;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    container: {flex: 1, justifyContent: 'center', alignItems: 'center'},
    preview: {
      marginTop: Sizes.xl,
      width: Sizes.ex2l,
      height: Sizes.ex2l,
      borderRadius: Sizes.xms,
    },
    modalOverlay: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      backgroundColor: 'rgba(0,0,0,0.4)',
    },
    modalContent: {
      backgroundColor: colors.whiteColor,
      padding: Sizes.xl,
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
    },
    option: {
      paddingVertical: Sizes.l,
      flexDirection: 'row',
      alignItems: 'center',
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    lineStyle: {
      backgroundColor: colors.grey2,
      height: Sizes.x,
    },
  });

export default styles;

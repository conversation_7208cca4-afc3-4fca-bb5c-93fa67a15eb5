import * as Yup from 'yup';

export const validateFeedbackSchema: any = Yup.object().shape({
  quality: Yup.string()
    .required('Quality is required')
    .matches(/^[a-zA-Z]+$/, 'Quality must contain only alphabets'), // Adjust the regex if you want to allow spaces or other characters

  price: Yup.number()
    .required('Price is required')
    .matches(/^\d+$/, 'Price must be a valid number')
    .positive('Price must be a positive number')
    .integer('Price must be an integer')
    .typeError('Price must be a number'), // Ensures that the input is a number, even if it's provided as a string
  feedback: Yup.string()
    .required('Feedback is required')
    .min(1, 'Feedback must be at least 1 character long'), // Adjust the min length if needed
});

export const bulkFormValidations: any = Yup.object().shape({
  userName: Yup.string()
    .required('Name is required field.')
    .matches(/^[a-zA-Z\s]+$/, 'Please enter only alphabets.'),
  userPhone: Yup.string()
    .required('Phone number is required field.')
    .matches(/^\d{10}$/, 'Please enter correct phone number.'),
  userEmail: Yup.string()
    .required('Email is required field.')
    .email('Please enter correct email'),
  userPincode: Yup.string()
    .required('Pincode is required')
    .length(6, 'Pincode is required.'),
  address: Yup.string().required('Address is required field.'),
  expectedPrice: Yup.number().required('Price per piece is required.'),
  bulkQuantity: Yup.number().required('Quantity is required.'),
});

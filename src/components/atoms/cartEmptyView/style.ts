import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    wrapper: {
      flex: Sizes.x,
      marginTop: Sizes.x6l,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    mainView: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    forgetView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingRight: Sizes.m,
      alignItems: 'center',
    },
    forgetSection: {
      backgroundColor: colors.lightblue1,
      paddingLeft: Sizes.l,
      borderWidth: 0.5,
      borderColor: colors.shadow,
      marginBottom: Sizes.xl,
    },
    circle: {
      width: Sizes.ex2l + Sizes.x6l,
      height: Sizes.ex2l + Sizes.xxl,
    },
    imageView: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyImage: {
      position: 'absolute',
      width: Sizes.ex1 + Sizes.sx,
      height: Sizes.ex1 + Sizes.sx,
    },
    labelStyle: {
      fontWeight: '500',
    },
    btnStyle: {
      alignSelf: 'center',
      height: Sizes.x7l,
      width: 268,
    },
    btnText: {
      fontFamily: Fonts.Medium,
    },
    backgroundImage: {
      resizeMode: 'cover',
      justifyContent: 'center',
    },
    sellerView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: Sizes.l,
    },
    listDataView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingBottom: Sizes.xxl,
    },
    textCap: {
      textTransform: 'capitalize',
    },
  });

export default styles;

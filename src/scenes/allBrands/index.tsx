import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {TouchableOpacity, View, FlatList, Image} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useTheme, Theme, useIsFocused} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import {RootStackParamsList} from '../../routes';
import {Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {ImageIcon, Label, SearchInput, Spacer} from 'components/atoms';
import {t} from 'i18next';
import {getAllBrands} from 'services/brands';
import {BrandLoader} from 'skeletonLoader';
import {checkDevice} from 'utils/utils';
import {AnalyticsEvents} from 'components/organisms';
import {WEBSITE_URL} from 'config/environment';
import {useSelector} from 'react-redux';
import Icons from 'common/icons';
import {setCurrentScreenName} from '@microsoft/react-native-clarity';

interface CustomTheme extends Theme {
  colors: Theme['colors'] & {
    skyBlue10: string;
    sunnyOrange5: string;
    whiteColor: string;
    // Add other custom colors as needed
  };
}

interface gethomepageBrandsItemProps {
  title: string;
  data: Brand[];
}

interface Brand {
  brand_name: string;
  is_active: boolean;
  logo?: string;
  url: string;
  category_id: string;
}

interface BrandsResponse {
  data: {
    brands: Record<string, Brand[]>;
  };
}

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  gethomepagebrands: gethomepageBrandsItemProps;
};

const AllBrands = ({navigation}: Props) => {
  const {colors} = useTheme() as CustomTheme;
  const flatListRef = useRef<FlatList<gethomepageBrandsItemProps>>(null);
  const styles = useMemo(
    () => stylesWithOutColor(colors as CustomTheme['colors']),
    [colors],
  );
  const [search, setSearch] = useState<string>('');
  const [allBrandLoading, setAllBrandLoading] = useState<boolean>(false);
  const [alphabetsVisible, setAlphabetsVisible] = useState<boolean>(false);
  const [activeAlphabetIndex, setActiveAlphabetIndex] = useState<number>();
  const [filteredDataSource, setFilteredDataSource] = useState<
    gethomepageBrandsItemProps[]
  >([]);
  const [masterDataSource, setMasterDataSource] = useState<
    gethomepageBrandsItemProps[]
  >([]);
  const [brandAToM, setBrandAToM] = useState([]);
  const [brandNToZ, setBrandNToZ] = useState([]);
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const isFocus = useIsFocused();

  useEffect(() => {
    if (
      isFocus &&
      !allBrandLoading &&
      flatListRef?.current &&
      filteredDataSource?.length > 0 &&
      search.length === 0
    ) {
      setActiveAlphabetIndex(undefined);
      // flatListRef?.current?.scrollToIndex({index: 0, animated: true});
    }
  }, [isFocus, filteredDataSource]);

  const memoizedFilteredData = useMemo(
    () => filteredDataSource,
    [filteredDataSource],
  );

  const filterData = useCallback(
    (searchText: string) => {
      let filteredData: gethomepageBrandsItemProps[] = [];
      const cleanedSearchText = searchText.toLowerCase();

      if (cleanedSearchText) {
        masterDataSource.forEach(record => {
          const filteredSearch = record?.data?.filter((item: Brand) =>
            item.brand_name.toLowerCase().includes(cleanedSearchText),
          );
          if (filteredSearch.length > 0) {
            filteredData.push({...record, data: filteredSearch});
          }
        });

        return filteredData;
      } else {
        return masterDataSource;
      }
    },
    [masterDataSource],
  );

  const allBrandsData = useCallback(async () => {
    setAllBrandLoading(true);
    const response = (await getAllBrands('')) as BrandsResponse;
    const {data} = response;
    setAllBrandLoading(false);
    if (data?.brands) {
      const formattedData = Object.keys(data?.brands).map(key => ({
        title: key,
        data: data?.brands[key],
      }));
      const result = formattedData.flatMap(section => {
        const activeBrands = section.data.filter(item => item.is_active);
        return activeBrands.length ? [{...section, data: activeBrands}] : [];
      });
      result.forEach(section => {
        section?.data.sort((a, b) => {
          const nameA = a.brand_name?.toLowerCase() || '';
          const nameB = b.brand_name?.toLowerCase() || '';
          return nameA.localeCompare(nameB);
        });
      });
      const firstAlpha = result.slice(0, 13).map(item => item.title);
      const secondAlpha = result.slice(13).map(item => item.title);
      setBrandAToM(firstAlpha);
      setBrandNToZ(secondAlpha);
      setMasterDataSource(result);
      setFilteredDataSource(result);
    }
  }, []);

  const onSelectAlpha = useCallback(
    (index: number) => {
      if (search?.trim()?.length === 0) {
        setActiveAlphabetIndex(index);
        flatListRef.current?.scrollToIndex({
          index,
          animated: true,
          viewOffset: 0,
          viewPosition: 0,
        });
      }
    },
    [search],
  );

  const createAlphabetPressHandler = useCallback(
    (ind: number, offset: number) => {
      return () => {
        if (filteredDataSource?.length > ind + offset) {
          onSelectAlpha(ind + offset);
        }
      };
    },
    [filteredDataSource, onSelectAlpha],
  );

  const getAlphabetStyle = useCallback(
    (ind: number, offset: number) => {
      return activeAlphabetIndex === ind + offset
        ? [styles.alphaOrderView, styles.activeAlpha]
        : styles.alphaOrderView;
    },
    [styles, activeAlphabetIndex],
  );

  const AlphabetItem = useCallback(
    ({alpha, ind, offset}: {alpha: string; ind: number; offset: number}) => {
      const pressHandler = createAlphabetPressHandler(ind, offset);
      const itemStyle = getAlphabetStyle(ind, offset);

      return (
        <TouchableOpacity onPress={pressHandler} style={itemStyle}>
          <Label
            text={alpha}
            color="whiteColor"
            size="mx"
            fontFamily="Medium"
          />
        </TouchableOpacity>
      );
    },
    [createAlphabetPressHandler, getAlphabetStyle],
  );

  const renderAlphabetAtoM = useCallback(
    ({item: alpha, index: ind}) => (
      <AlphabetItem alpha={alpha} ind={ind} offset={1} />
    ),
    [AlphabetItem],
  );

  const renderAlphabetNtoZ = useCallback(
    ({item: alpha, index: ind}) => (
      <AlphabetItem alpha={alpha} ind={ind} offset={14} />
    ),
    [AlphabetItem],
  );

  const handleScrollToIndexFailed = useCallback((info: {index: number}) => {
    const wait = new Promise(resolve => setTimeout(resolve, 300));
    wait.then(() => {
      flatListRef.current?.scrollToIndex({
        index: info.index,
        animated: true,
        viewPosition: 0,
      });
    });
  }, []);

  useEffect(() => {
    allBrandsData();
    setCurrentScreenName('Brand Category');
    return () => {
      setCurrentScreenName('Clarity event');
    };
  }, []);

  useEffect(() => {
    const data = filterData(search);
    setFilteredDataSource(data);
  }, [search, filterData]);

  const setSearchKey = useCallback(
    (key: string) => {
      setSearch(key?.length === 0 ? '' : key);
      setActiveAlphabetIndex(undefined);
    },
    [setSearch],
  );

  useEffect(() => {
    if (filteredDataSource.length > 0) {
      flatListRef.current?.scrollToIndex({
        index: 0,
        animated: true,
      });
    }
  }, [filteredDataSource]);

  const keyExtractor = useCallback((_, index) => index.toString(), []);

  const onToggleAlphabets = useCallback(() => {
    setAlphabetsVisible(!alphabetsVisible);
  }, [alphabetsVisible]);

  const toggleAlphabetsStyle = useMemo(() => {
    return {
      transform: [{rotate: alphabetsVisible ? '270deg' : '90deg'}],
    };
  }, [alphabetsVisible]);

  const onSearchSection = useMemo(() => {
    return (
      <View style={styles.sectionCardView}>
        <SearchInput
          onChangeText={setSearchKey}
          value={search}
          placeHolder={t('allBrands.searchBrand') as string}
          inputStyle={styles.searchInputStyle}
        />
        <Spacer size="xm" />
        <LinearGradient
          colors={[colors.skyBlue15, colors.red1]}
          useAngle
          angle={90}
          style={[
            styles.alphaView,
            {borderRadius: alphabetsVisible ? 20 : 33},
          ]}>
          <View style={styles.alphaContainer}>
            <View style={styles.fRow}>
              {(checkDevice() ? [...brandAToM, ...brandNToZ] : brandAToM).map(
                (alpha, ind) => (
                  <TouchableOpacity
                    key={ind}
                    onPress={() =>
                      filteredDataSource?.length > ind
                        ? onSelectAlpha(ind)
                        : null
                    }
                    activeOpacity={search?.trim()?.length === 0 ? 0 : 1}
                    style={styles.alphaOrderView}>
                    <View
                      style={activeAlphabetIndex === ind && styles.activeAlpha}>
                      <Label
                        text={alpha}
                        color={
                          activeAlphabetIndex === ind ? 'text' : 'whiteColor'
                        }
                        size="mx"
                        fontFamily="Medium"
                        style={styles.brandChar}
                      />
                    </View>
                  </TouchableOpacity>
                ),
              )}
            </View>
            {alphabetsVisible && !checkDevice() && (
              <>
                <Spacer size="xxl" />
                <View style={styles.fRow}>
                  {brandNToZ.map((alpha, ind) => (
                    <TouchableOpacity
                      key={ind}
                      style={styles.alphaOrderView}
                      activeOpacity={search?.trim()?.length === 0 ? 0 : 1}
                      onPress={() =>
                        filteredDataSource?.length > ind + 13
                          ? onSelectAlpha(ind + 13)
                          : null
                      }>
                      <View
                        style={
                          activeAlphabetIndex === ind + 13 && styles.activeAlpha
                        }>
                        <Label
                          text={alpha}
                          color={
                            activeAlphabetIndex === ind + 13
                              ? 'text'
                              : 'whiteColor'
                          }
                          size="mx"
                          fontFamily="Medium"
                          style={styles.brandChar}
                        />
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              </>
            )}
          </View>
          {!checkDevice() && (
            <>
              <Spacer size="xms" type="Horizontal" />
              <TouchableOpacity activeOpacity={0.7} onPress={onToggleAlphabets}>
                <ImageIcon
                  icon="Exclude"
                  size="xxl"
                  style={{
                    transform: [
                      {rotate: alphabetsVisible ? '270deg' : '90deg'},
                    ],
                  }}
                />
              </TouchableOpacity>
            </>
          )}
        </LinearGradient>
      </View>
    );
  }, [
    activeAlphabetIndex,
    alphabetsVisible,
    brandAToM,
    brandNToZ,
    colors.red1,
    colors.skyBlue15,
    filteredDataSource?.length,
    onSelectAlpha,
    onToggleAlphabets,
    search,
    setSearchKey,
    styles.activeAlpha,
    styles.alphaContainer,
    styles.alphaOrderView,
    styles.alphaView,
    styles.brandChar,
    styles.fRow,
    styles.searchInputStyle,
    styles.sectionCardView,
  ]);

  const searchText = useMemo(() => {
    return ` ${search ? `"${search}"` : t('allBrands.brands')}`;
  }, [search]);

  const emptyView = useMemo(() => {
    return (
      <View style={styles.emptyView}>
        <Label
          text={t('allBrands.noData')}
          size="mx"
          fontFamily="Medium"
          color="categoryTitle"
          align="center">
          <Label
            text={searchText}
            size="mx"
            fontFamily="Bold"
            color="categoryTitle"
          />
        </Label>
      </View>
    );
  }, [styles.emptyView, searchText]);

  const handleBrandPress = useCallback(
    (subItem, sectionIndex) => () => {
      if (sectionIndex !== 0) {
        setActiveAlphabetIndex(sectionIndex);
      }

      const brandData = {
        Brand: subItem?.brand_name,
        'Page URL': `${WEBSITE_URL}/${subItem?.url}.html`,
      };

      AnalyticsEvents(
        'BRAND_VIEW',
        'Brand View',
        brandData,
        userInfo,
        isLoggedIn,
      );

      navigation.navigate('CategoryDetail', {
        categoryId: subItem?.category_id,
      });
    },
    [isLoggedIn, navigation, userInfo, setActiveAlphabetIndex],
  );

  const brandItemViewStyle = useCallback((idx: number) => {
    return {
      marginRight: (idx + 1) % (checkDevice() ? 4 : 3) === 0 ? 0 : 8,
    };
  }, []);
  const brandGradientColors = useMemo(() => {
    return [colors.whiteColor, colors.whiteColor];
  }, [colors.whiteColor]);

  const renderBrandsItem = useCallback(
    ({item: subItem, index: idx}, sectionIndex) => {
      const pressHandler = handleBrandPress(subItem, sectionIndex);
      const itemViewStyle = brandItemViewStyle(idx);
      const brandContainerStyle = [styles.brandItemView, itemViewStyle];

      return (
        <TouchableOpacity
          key={idx}
          onPress={pressHandler}
          style={brandContainerStyle}>
          <View style={styles.brandImg}>
            <LinearGradient
              colors={brandGradientColors}
              useAngle
              angle={45}
              style={styles.brandImgView}>
              {subItem?.logo && (
                <ImageIcon
                  source={subItem?.logo}
                  icon="Exclude"
                  size="xxl"
                  sourceType="url"
                  style={styles.imageStyle}
                />
              )}
            </LinearGradient>
          </View>
          <Spacer size="s" />
          <Label
            size="m"
            color="categoryTitle"
            align="center"
            text={subItem?.brand_name}
            numberOfLines={1}
            ellipsizeMode="tail"
            fontFamily="Medium"
          />
        </TouchableOpacity>
      );
    },
    [
      handleBrandPress,
      brandItemViewStyle,
      styles.brandItemView,
      styles.brandImg,
      styles.brandImgView,
      styles.imageStyle,
      brandGradientColors,
    ],
  );

  const renderSections = useCallback(
    ({item, index}: {item: gethomepageBrandsItemProps; index: number}) => {
      const activeBrands = item?.data?.filter((data: Brand) => data?.is_active);
      if (activeBrands?.length > 0) {
        return (
          <View key={index} style={styles.brandCardView}>
            <View style={styles.sectionTitleView}>
              <View style={styles.brandFView}>
                <Label
                  text={item?.title}
                  size="mx"
                  fontFamily="Medium"
                  color="categoryTitle"
                  style={styles.sTitle}
                />
              </View>
              {/* <Spacer size="m" type="Horizontal" />
              <View style={styles.alphaLine} />
              <Spacer size="m" type="Horizontal" /> */}
            </View>
            <Image style={styles.branCharBottomView} source={Icons.ellipse} />
            {/* <FlatList
              data={activeBrands}
              removeClippedSubviews={true}
              windowSize={5}
              numColumns={checkDevice() ? 4 : 3}
              renderItem={({item: subItem, index: idx}) => {
                return ( */}
            <View style={styles.brandSectionView}>
              {activeBrands.map((subItem, idx) => (
                <TouchableOpacity
                  key={idx}
                  onPress={() => {
                    if (index !== 0) {
                      setActiveAlphabetIndex(index);
                    }
                    const brandData = {
                      Brand: subItem?.brand_name,
                      'Page URL': `${WEBSITE_URL}/${subItem?.url}.html`,
                    };
                    AnalyticsEvents(
                      'BRAND_VIEW',
                      'Brand View',
                      brandData,
                      userInfo,
                      isLoggedIn,
                    );
                    navigation.navigate('CategoryDetail', {
                      categoryId: parseInt(subItem.category_id, 10),
                    });
                  }}
                  style={[
                    styles.brandItemView,
                    {
                      marginRight:
                        (idx + 1) % (checkDevice() ? 4 : 3) === 0 ? 0 : 8,
                    },
                  ]}>
                  <View style={styles.brandImg}>
                    <LinearGradient
                      colors={[colors.whiteColor, colors.whiteColor]}
                      useAngle
                      angle={45}
                      style={styles.brandImgView}>
                      <ImageIcon
                        source={subItem?.logo ? subItem?.logo : ''}
                        size="xxl"
                        sourceType={subItem?.logo ? 'url' : 'local'}
                        icon="dentalkartLogo"
                        style={styles.imageStyle}
                      />
                    </LinearGradient>
                  </View>
                  <Spacer size="s" />
                  <Label
                    size="m"
                    color="categoryTitle"
                    align="center"
                    text={subItem?.brand_name}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                    fontFamily="Medium"
                  />
                </TouchableOpacity>
              ))}
            </View>
            {/* );
              }}
              keyExtractor={(_, i) => i?.toString()}
            /> */}
          </View>
        );
      } else {
        return <View key={index} />;
      }
    },
    [
      colors.whiteColor,
      isLoggedIn,
      navigation,
      styles.branCharBottomView,
      styles.brandCardView,
      styles.brandFView,
      styles.brandImg,
      styles.brandImgView,
      styles.brandItemView,
      styles.brandSectionView,
      styles.imageStyle,
      styles.sTitle,
      styles.sectionTitleView,
      userInfo,
    ],
  );

  return (
    <View style={styles.container}>
      <Header
        useInsets
        backButton={true}
        navigation={navigation}
        bagIcon={true}
        searchIcon
        isRedious
        text={t('allBrands.allBrands') as string}
      />
      {allBrandLoading ? (
        <BrandLoader />
      ) : (
        <View style={styles.flexOne}>
          <Spacer size="xm" />
          {onSearchSection}
          <Spacer size="xm" />
          <FlatList
            data={filteredDataSource}
            ref={flatListRef}
            scrollEventThrottle={16}
            removeClippedSubviews={true}
            windowSize={5}
            keyboardShouldPersistTaps="handled"
            keyExtractor={keyExtractor}
            ListEmptyComponent={emptyView}
            onScrollToIndexFailed={handleScrollToIndexFailed}
            showsVerticalScrollIndicator={false}
            renderItem={renderSections}
            initialNumToRender={27}
            maxToRenderPerBatch={15}
            updateCellsBatchingPeriod={50}
          />
        </View>
      )}
    </View>
  );
};

export default React.memo(AllBrands);

import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    wrapper: {
      flex: Sizes.x,
      marginTop: Sizes.exl,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    mainView: {
      backgroundColor: colors.background,
    },
    circle: {
      width: Sizes.ex3l,
      height: Sizes.ex2l,
    },
    imageView: {
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: Sizes.x3l,
    },
    emptyImage: {
      position: 'absolute',
      width: Sizes.ex1 + Sizes.sx,
      height: Sizes.ex1 + Sizes.sx,
    },
    continueShopping: {
      alignSelf: 'center',
      height: Sizes.x7l,
      width: Sizes.ex2l,
    },
    wishBtnTxt: {
      fontFamily: Fonts.Medium,
      fontSize: Sizes.mx,
    },
    plusLeftIcon: {
      width: Sizes.xx,
      height: Sizes.xx,
      left: Sizes.xsl,
      top: Sizes.ex1,
      position: 'absolute',
    },
    plusRightIcon: {
      width: Sizes.x3l,
      height: Sizes.x3l,
      right: 5,
      top: Sizes.ex1,
      position: 'absolute',
    },
    thumpTopIcon: {
      width: Sizes.xx,
      height: Sizes.xx,
      right: Sizes.x8l,
      top: Sizes.xl,
      position: 'absolute',
    },
  });

export default styles;

import React, {useCallback} from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import Svg, {Circle, G} from 'react-native-svg';
import {Label} from 'components/atoms';
import stylesWithOutColor from './style';
import {Sizes} from 'common';
import {useMemo} from 'react';

const CircularProgress = ({percentage}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const radius = Sizes.xl;
  const strokeWidth = 5;
  const normalizedRadius = radius - strokeWidth / Sizes.xs;
  const circumference = normalizedRadius * Sizes.xs * Math.PI;
  const strokeDashoffset =
    circumference - (percentage / Sizes.exl) * circumference;

  return (
    <View style={styles.container}>
      <Svg height={radius * Sizes.xs} width={radius * Sizes.xs}>
        <G rotation="-90" origin={`${radius}, ${radius}`}>
          <Circle
            stroke={colors.placeholderColor}
            fill="none"
            cx={radius}
            cy={radius}
            r={normalizedRadius}
            strokeWidth={strokeWidth}
          />
          <Circle
            stroke={colors.categoryTitle}
            fill="none"
            cx={radius}
            cy={radius}
            r={normalizedRadius}
            strokeWidth={strokeWidth}
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
          />
        </G>
      </Svg>
      <Label
        color="text2"
        size="m"
        style={styles.percentageText}
        fontFamily="Medium"
        text={`${percentage}%`}
      />
    </View>
  );
};

export default CircularProgress;

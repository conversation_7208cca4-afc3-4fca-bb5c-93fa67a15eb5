import {StyleSheet} from 'react-native';
import {Fonts, Sizes} from 'common';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    searchBarView: {
      width: '100%',
      borderRadius: Sizes.m,
      borderColor: colors.borderColor,
      borderWidth: Sizes.x,
      height: Sizes.x8l,
      alignItems: 'center',
      justifyContent: 'center',
    },
    greyBackground: {
      borderWidth: 0,
      backgroundColor: colors.grey10,
    },
    searchBar: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 0,
    },
    searchIconView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    searchCancelIcon : {
      height:Sizes.x6l, 
      width:Sizes.x3l, 
      justifyContent:'center',
      alignItems:'center'
    },
    searchView: {
      flex: Sizes.x,
      padding: Sizes.xms,
    },
    searchSubView: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Regular,
      height: Sizes.x8l,
      color: colors.text2,
      marginTop: Sizes.xs + Sizes.x,
    },
    staticView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    fOne: {
      flex: Sizes.x,
    },
  });

export default styles;

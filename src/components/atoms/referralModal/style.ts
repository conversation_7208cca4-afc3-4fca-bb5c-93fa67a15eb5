import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flexContainer: {flex: Sizes.x},
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    postQuestionView: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.s,
    },
    referView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xxl,
    },
    dericationView: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    referralLink: {
      alignSelf: 'flex-start',
    },
    flex: {
      flex: Sizes.x,
    },
    copyGif: {
      height: Sizes.x42,
      width: Sizes.x42,
    },
    newCopy: {width: Sizes.xxl, height: Sizes.xxl},
    referLinkView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xms,
      borderColor: 'rgba(234, 78, 0, 0.48)',
      backgroundColor: colors.rose,
      height: Sizes.x46,
      alignItems: 'center',
      padding: Sizes.xm,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    codeView: {
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      padding: Sizes.sx,
      borderRadius: Sizes.xms,
    },
    loaderView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.m,
      paddingTop: Sizes.xl,
    },
  });

export default styles;

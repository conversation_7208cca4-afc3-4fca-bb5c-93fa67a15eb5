import React, {useCallback, useEffect, useState} from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import stylesWithOutColor from './styles';
import {useTheme} from '@react-navigation/native';
import {DropDown, FastImagesItem, Label, Radio, Spacer} from 'components/atoms';
import {t} from 'i18next';
import {Sizes} from 'common';
import {useMemo} from 'react';

const RenderOrderedItems = ({
  item,
  returnActions,
  returnReasons,
  removeReturnItemObject,
  updateReutrnItemObject,
  isSelected,
  formSubmitted,
  canReturn,
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [qtyItems, setQtyItems] = useState([]);
  const [subReasons, setSubReasons] = useState([]);
  const [reason, setReason] = useState(null);
  const [qty, setQty] = useState(null);
  const [subReason, setSubReason] = useState('');
  const [action, setAction] = useState(null);
  const [selectCheckbox, setSelectCheckbox] = useState(false);

  const updateObject = () => {
    let reasonAttachment = returnReasons.find(
      element => element.value === reason,
    );
    let data = {
      sku: item?.sku,
      name: item?.name,
      image: item?.image,
      qty: qty,
      action: action,
      reason: reason,
      sub_reason: subReason,
      attachment: reasonAttachment?.attachment ?? false,
    };
    // if (isSelected) {
    //   removeReturnItemObject(data);
    // } else {
    //   updateReutrnItemObject(data);
    // }
    updateReutrnItemObject(data);
  };

  useEffect(() => {
    let sub = returnReasons.filter(item => item.value === reason);

    setSubReasons(sub?.[0]?.children?.length > 0 ? sub?.[0]?.children : []);
  }, [reason, returnReasons]);

  useEffect(() => {
    if (item && item?.qty_ordered > 0) {
      let data = [];
      for (let i = 1; i <= item?.qty_ordered; i++) {
        let newData = {
          id: i,
          label: String(i),
          value: i,
        };
        data.push(newData);
      }
      setQtyItems(data);
    }
  }, [item]);

  useEffect(() => {
    if (reason || action || qty || subReason) {
      updateObject();
    }
  }, [reason, action, qty, subReason]);

  useEffect(() => {
    setQty(null), setReason(null);
    setAction(null);
    setSelectCheckbox(false);
  }, [formSubmitted]);

  useEffect(() => {
    if (!selectCheckbox) {
      setQty(null);
      setReason(null);
      setAction(null);
      setSubReason('');
    }
  }, [selectCheckbox]);

  return (
    <>
      <Spacer size="m" />
      <View style={styles.imageView}>
        <View style={styles.rowContinuer}>
          <Label text={t('orderReturn.items')} />
          <Label text={t('orderReturn.select')} />
        </View>
        <Spacer size="l" />
        <View style={[styles.rowContinuer, styles.itemsCenter]}>
          <View style={styles.iconView}>
            <FastImagesItem
              FastImageStyle={{width: 40, height: 40}}
              source={{
                uri: item?.image,
              }}
              icon="profileIcon"
              size="x6l"
            />
          </View>
          <Label
            color="textLight"
            style={styles.label}
            size="m"
            weight="700"
            text={item?.name}
          />
          <Radio
            disabled={canReturn}
            selected={!!selectCheckbox}
            onPress={() => {
              setSelectCheckbox(selectCheckbox => !selectCheckbox),
                selectCheckbox
                  ? removeReturnItemObject(item?.sku)
                  : updateObject();
            }}
            style={styles.radio}
          />
          {canReturn ? (
            <Label style={styles.returnErrorText} text={'Non Returnable'} />
          ) : null}
        </View>
        <View pointerEvents={isSelected ? 'auto' : 'none'}>
          <DropDown
            search={false}
            placeholderStyle={styles.placeHolderText}
            placeholder={t('orderReturn.qty')}
            styleDropDown={styles.dropDown}
            data={qtyItems}
            onChange={item => setQty(item)}
          />

          <Spacer size="l" />
          <DropDown
            search={false}
            placeholderStyle={styles.placeHolderText}
            placeholder={t('orderReturn.reason')}
            styleDropDown={styles.dropDown}
            data={returnReasons}
            onChange={item => setReason(item)}
          />

          {reason && subReasons.length > 0 && (
            <>
              <Spacer size="l" />
              <DropDown
                search={false}
                placeholderStyle={styles.placeHolderText}
                placeholder={t('orderReturn.subReason')}
                styleDropDown={styles.dropDown}
                data={subReasons.map(reasonItem => {
                  return {
                    ...reasonItem,
                    label: reasonItem.reason,
                    value: reasonItem.reason,
                  };
                })}
                value={subReason}
                onChange={item => setSubReason(item)}
              />
            </>
          )}

          <Spacer size="l" />
          <DropDown
            search={false}
            placeholderStyle={styles.placeHolderText}
            placeholder={t('orderReturn.action')}
            styleDropDown={styles.dropDown}
            data={returnActions}
            value={action}
            onChange={item => setAction(item)}
          />
        </View>
      </View>
    </>
  );
};
export default RenderOrderedItems;

import React, {useState, memo, useMemo} from 'react';
import {TouchableOpacity, View, LayoutAnimation} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {FastImagesItem, Label} from 'components/atoms';
import Icons from 'common/icons';
import {RenderCustomHTML} from 'components/molecules';

const DropDownList = ({
  item,
  type,
  isborder,
  answerLabelStyle,
  displayHtml,
}: any) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  };

  return (
    <>
      <TouchableOpacity
        style={[
          type === 'HTML'
            ? [isborder ? styles.subContainer : styles.border]
            : styles.mainContainer,
        ]}
        onPress={toggleExpand}>
        <View style={styles.titleView}>
          {type === 'HTML' ? (
            <>
              <Label
                lineHeight="l"
                style={styles.labelText}
                size="m"
                color="text2"
                fontFamily="Medium"
                text={
                  item?.title || item?.question?.replace(/<\/?[^>]+(>|$)/g, '')
                }
              />
            </>
          ) : (
            <Label
              lineHeight="l"
              style={styles.labelText}
              size="m"
              color="text2"
              fontFamily="Medium"
              text={item?.question?.replace(/<\/?[^>]+(>|$)/g, '')}
            />
          )}

          <FastImagesItem
            source={Icons.arrowBottom}
            FastImageStyle={
              isExpanded
                ? styles.upView
                : styles.fastImageView
            }
            tintColor={colors.text2}
          />
        </View>

        {isExpanded && (
          <View style={styles.answerView}>
            {type === 'HTML' || displayHtml ? (
              <>
                <RenderCustomHTML
                  html={item?.answer}
                  baseFontStyle={styles.descText}
                  tagsStyles={styles.htmlRenderStyle}
                />
              </>
            ) : (
              <>
                <Label
                  style={answerLabelStyle}
                  size="m"
                  color="text2"
                  fontFamily="Regular"
                  text={item?.answer?.replace(/<\/?[^>]+(>|$)/g, '')}
                />
              </>
            )}
          </View>
        )}
      </TouchableOpacity>
    </>
  );
};

export default memo(DropDownList);
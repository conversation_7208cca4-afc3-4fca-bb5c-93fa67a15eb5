app_identifier([
  "com.dentalkart.vasadental",             # Main app identifier
  "com.dentalkart.vasadental.staging",             # Main app identifier
  "com.dentalkart.vasadental.dev",             # Main app identifier
  "com.dentalkart.vasadental.dev.NCEDev",    # Extension (e.g., Notification Service Extension)
  "com.dentalkart.vasadental.dev.NSEDev"       # Widget target identifier
])

git_url("https://github.com/mahesh-dentalkart/fastalne.git")

storage_mode("git")

type("appstore") # The default type, can be: appstore, adhoc, enterprise or development

# app_identifier(["tools.fastlane.app", "tools.fastlane.app2"])
# username("<EMAIL>") # Your Apple Developer Portal username

# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match

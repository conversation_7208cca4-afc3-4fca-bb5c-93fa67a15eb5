import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    subView: {
      flex: Sizes.x,
      backgroundColor: colors.background2,
    },
    deliveryNoteView: {
      borderRadius: Sizes.xm,
      paddingVertical: Sizes.xm,
      backgroundColor: colors.background,
      marginHorizontal: Sizes.xm,
    },
    onCheckoutView: {
      padding: Sizes.mx,
      paddingHorizontal: Sizes.x8l,
      borderRadius: Sizes.xms,
    },
    promotionCartView: {
      backgroundColor: colors.bubbles,
      padding: Sizes.xms,
      paddingHorizontal: Sizes.xm,
      marginTop: Sizes.xm,
    },
    promotionCartSubView: {
      backgroundColor: colors.green4,
      padding: Sizes.xms,
      borderRadius: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
    },
    deliveryView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.xm,
      borderBottomColor: colors.grey2,
      paddingBottom: Sizes.xm,
      borderBottomWidth: Sizes.x,
    },
    underPrizeView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xm,
    },
    underPrizeMainView: {
      flexDirection: 'row',
    },
    imageView: {
      borderRadius: Sizes.xm,
      height: Sizes.x9l,
      width: Sizes.x9l,
    },
    image1: {
      zIndex: -Sizes.xs + Sizes.x,
      borderLeftWidth: Sizes.z,
      borderTopWidth: Sizes.z,
      borderBottomWidth: Sizes.z,
      borderTopLeftRadius: Sizes.xm,
      borderBottomLeftRadius: Sizes.xm,
      borderTopColor: colors.orient16,
      borderLeftColor: colors.orient16,
      borderBottomColor: colors.orient16,
      width: Sizes.x9l,
      height: Sizes.x9l - Sizes.x,
    },
    image2: {
      zIndex: -Sizes.xs,
      marginLeft: -Sizes.x58,
      borderLeftWidth: Sizes.z,
      borderTopWidth: Sizes.z,
      borderBottomWidth: Sizes.z,
      borderTopLeftRadius: Sizes.xm,
      borderBottomLeftRadius: Sizes.xm,
      borderTopColor: colors.orient16,
      borderLeftColor: colors.orient16,
      borderBottomColor: colors.orient16,
      width: Sizes.x9l,
      height: Sizes.x9l - Sizes.x,
    },
    image3: {
      marginLeft: -Sizes.x58,
      zIndex: -Sizes.x,
    },
    itemsView: {
      borderRadius: Sizes.xm,
      height: Sizes.x9l,
      width: Sizes.x9l,
      borderWidth: Sizes.z,
      borderColor: colors.orient16,
    },
    itemsSubView: {
      flex: Sizes.x,
      backgroundColor: colors.jacksonsPurple,
      height: Sizes.x9l,
      width: Sizes.x9l,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    cartQtyView: {
      flex: Sizes.x,
      marginLeft: Sizes.s,
    },
    fRow: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    cartQtySub: {
      flex: Sizes.x,
      marginRight: Sizes.s,
    },
    productView: {
      paddingHorizontal: Sizes.xms,
    },
    deliveryStatusView: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-end',
    },
    registrationView: {
      borderRadius: Sizes.mx,
      backgroundColor: colors.background,
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.xms,
      marginHorizontal: Sizes.xm,
    },
    registrationSubView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    registerBtn: {
      height: Sizes.xxxl,
    },
    redeemView: {
      marginHorizontal: Sizes.xm,
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.m,
      backgroundColor: colors.background,
      borderRadius: Sizes.xm,
      borderColor: colors.grey8,
      borderWidth: Sizes.x,
    },
    redeemSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectedView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    paymentView: {
      marginHorizontal: Sizes.xm,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.xms,
      backgroundColor: colors.background,
      borderRadius: Sizes.mx,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
    },
    paymentSubView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xm,
      marginTop: Sizes.l,
    },
    freeProductGif: {width: Sizes.xl, height: Sizes.xx},
    payCashView: {
      width: Sizes.ex90,
      height: Sizes.l,
    },
    tncSelectedView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.xxl,
    },
    checkoutSection: {
      paddingHorizontal: Sizes.xm,
    },
    btnLabel: {
      fontFamily: Fonts.Medium,
    },
    bottomBtnView: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.whiteColor,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: Sizes.s,
      paddingVertical: Sizes.xm,
      paddingHorizontal: Sizes.l,
    },
    bottomPad: {
      marginBottom: Sizes.s,
    },
    flex: {
      flex: Sizes.x,
    },
    successTitle: {
      fontSize: Sizes.xl,
    },
    inputBox: {
      height: Sizes.x46,
      flex: Sizes.x,
      color: colors.text,
      fontFamily: Fonts.Medium,
    },
    registerView: {
      backgroundColor: colors.background3,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.m,
      flexDirection: 'row',
      alignItems: 'center',
    },
    alignEnd: {
      alignItems: 'flex-end',
    },
    registerTxt: {
      textAlign: 'justify',
    },
    uparrowStyle: {
      width: '30%',
      alignItems: 'flex-end',
      alignSelf: 'flex-end',
    },
    titleStyle: {
      fontSize: Sizes.xx,
      fontFamily: Fonts.SemiBold,
    },
    desStyle: {
      fontSize: Sizes.l,
      fontFamily: Fonts.SemiBold,
    },
    downStyle: {
      tintColor: colors.categoryTitle,
    },
    upDownStyle: {
      transform: [{rotate: '180deg'}],
    },
    infoStyle: {
      marginTop: -Sizes.x,
    },
  });
export default styles;

import {Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet, Platform} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    itemContainer: {
      borderRadius: Sizes.xm,
      width: (DeviceWidth - Sizes.x4l) / Sizes.xs,
      height: Sizes.ex270 - Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    img: {
      flex: Sizes.x,
      width: '100%',
      height: '100%',
      borderRadius: Sizes.xm,
    },
    gradient: {
      position: 'absolute',
      zIndex: Sizes.s,
      bottom: 0,
      width: '100%',
      height: Sizes.ex2l,
      borderRadius: Sizes.xm,
    },
    titlesView: {
      position: 'absolute',
      bottom: Sizes.xm,
      paddingHorizontal: Sizes.m,
    },
    textCap: {
      textTransform: 'capitalize',
    },
    reelIcon: {
      position: 'absolute',
      right: Sizes.xms,
      bottom: Sizes.ex,
    },
    countTxt: {
      color: colors.whiteColor,
      fontSize: Sizes.m,
    },
    hitSlopStyle:{
      top: 10, 
      bottom: 10, 
      left: 10, 
      right: 10
    },
    shareButton:{
      padding: 10
    }

  });

export default styles;

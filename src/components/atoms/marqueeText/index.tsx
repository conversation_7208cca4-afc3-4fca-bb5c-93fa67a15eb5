import React, {useState, useRef, useEffect} from 'react';
import {Animated, View, ViewProps, Dimensions} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  text: string;
  baseSpeed: number;
  style?: ViewProps['style'];
  textStyle?: ViewProps['style'];
};


const MarqueeText = ({text, baseSpeed = 8, style, textStyle}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const translateX = useRef(new Animated.Value(0)).current;
  const [textWidth, setTextWidth] = useState(0);
  const screenWidth = Dimensions.get('window').width;

  useEffect(() => {
    if (textWidth === 0) return;
    const duration = (textWidth / baseSpeed) * 800; // Speed is pixels/second

    const startAnimation = () => {
      translateX.setValue(screenWidth);
      Animated.loop(
        Animated.timing(translateX, {
          toValue: -textWidth,
          duration,
          useNativeDriver: true,
        }),
      ).start();
    };

    startAnimation();
  }, [textWidth, screenWidth, baseSpeed, translateX]);

  return (
    <View style={[styles.container, style]}>
      <Animated.ScrollView horizontal={true}>
        <Animated.Text
          onLayout={e => setTextWidth(e.nativeEvent.layout.width)}
          style={[styles.marqueeText, textStyle, {transform: [{translateX}]}]}>
          {text}
        </Animated.Text>
      </Animated.ScrollView>
    </View>
  );
};

export default MarqueeText;

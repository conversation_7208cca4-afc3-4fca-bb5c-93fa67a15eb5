import {Sizes} from 'common';
import {Dimensions, StyleSheet} from 'react-native';
const {width: SCREEN_WIDTH} = Dimensions.get('window');
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    searchedProduct: {
        paddingHorizontal: Sizes.m,
    },
    searchedView: {
        borderWidth: Sizes.x,
        height: Sizes.x56,
        paddingHorizontal: Sizes.xl,
        marginHorizontal: Sizes.s,
        borderRadius: Sizes.xm,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderColor: colors.blue,
    },
    hotSellerProductView: {
        flex: Sizes.x,
        paddingTop: Sizes.m,
    },
  });
export default styles;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    categorySubView: {
      alignItems: 'center',
      marginRight: Sizes.xs,
    },
    viewAllContainer: {
      alignItems: 'center',
      width: checkDevice() ? Sizes.exl : Sizes.x70,
    },
    viewAllView: {
      width: checkDevice() ? Sizes.exl : Sizes.x66,
      height: checkDevice() ? Sizes.exl : Sizes.x66,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: Sizes.xms,
    },
    allView: {
      width: Sizes.x66,
      height: Sizes.x66,
    },
    categoryTitleView: {
      width: checkDevice() ? Sizes.exl : Sizes.x66,
      height: checkDevice() ? Sizes.exl : Sizes.x66,
      borderRadius: Sizes.xms,
      backgroundColor: colors.lightgrey,
      marginBottom: checkDevice() ? Sizes.s : Sizes.xm,
    },
    closeIcon: {
      position: 'absolute',
      top: -Sizes.ex92,
      left: checkDevice() ? Sizes.l : Sizes.m,
    },
    categoryAllView: {
      width: Sizes.x56,
      height: Sizes.x56,
      borderRadius: Sizes.x3l,
      backgroundColor: colors.modelBackgroundTransparent1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

export default styles;

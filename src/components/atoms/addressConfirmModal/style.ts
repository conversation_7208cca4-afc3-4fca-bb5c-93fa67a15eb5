import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    container: {
      borderRadius: Sizes.xms,
    },
    modalView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xms,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.sx,
      margin: Sizes.xms,
    },
    subView: {
      flexDirection: 'row',
    },
    productImgView: {
      borderRadius: Sizes.xm,
      borderColor: colors.placeholderColor,
      width: Sizes.exl,
      height: Sizes.exl,
      borderWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    removeImage: {
      width: Sizes.exl - Sizes.xs,
      height: Sizes.exl - Sizes.xs,
      borderRadius: Sizes.xm,
    },
    borderBottomView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    deleteCartItemView: {
      justifyContent: 'space-around',
      flexDirection: 'row',
      paddingVertical: Sizes.xms,
    },
    firstBtn: {
      flex: Sizes.x,
      alignItems: 'center',
    },
    rightBtn: {
      justifyContent: 'center',
      flexDirection: 'row',
      flex: Sizes.x,
    },
    outStock: {
      borderColor: colors.grey2,
      backgroundColor: colors.orient20,
      borderWidth: Sizes.x,
      height: Sizes.x4l,
      width: Sizes.ex90,
      borderRadius: Sizes.sx,
      paddingVertical: 0,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
    },
    labelBottom: {
      marginBottom: -Sizes.xs,
    },
    rowCenter: {
      justifyContent: 'center',
      flexDirection: 'row',
    },
    fOne: {
      flex: 1,
    },
  });

export default styles;

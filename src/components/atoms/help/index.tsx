import React, {useMemo} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {openWhatsApp} from 'utils/utils';
import {useSelector} from 'react-redux';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import { RootState } from '@types/local';

const Help = () => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {whatsAppLink} = useSelector((state: RootState) => state.app);

  if (!whatsAppLink?.app_link) {
    return <View />;
  }

  return (
    <View>
      <TouchableOpacity
        style={styles.chatView}
        activeOpacity={1}
        onPress={() => openWhatsApp(whatsAppLink?.app_link)}>
        <FastImage
          resizeMode="contain"
          style={styles.icon}
          source={Icons.whatsapp1}
        />
        <Spacer size="xm" type="Horizontal" />
        <Label
          text={t('otherText.help')}
          size="m"
          color="islandGreen"
          fontFamily="Medium"
          style={styles.helpTxt}
        />
      </TouchableOpacity>
    </View>
  );
};

export default Help;

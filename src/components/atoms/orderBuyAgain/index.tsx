import React, {useCallback, useState, useMemo} from 'react';
import {TouchableOpacity, View, Pressable, ImageBackground} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {
  Label,
  OrderImageModal,
  ImageIcon,
  Spacer,
  Separator,
} from 'components/atoms';
import {Button} from 'components/molecules';
import {RootStackParamsList} from 'routes';
import {snakeToTitleCase} from 'utils/formatter';
import getImageUrl from 'utils/imageUrlHelper';
import {
  orderStatusColor,
  formatDate,
  sAllDevice,
  coinInfoShow,
  orderStatusLabel,
} from 'utils/utils';
import moment from 'moment';
import LinearGradient from 'react-native-linear-gradient';
import Clipboard from '@react-native-community/clipboard';
import {showSuccessMessage} from 'utils/show_messages';

type Props = {
  index: number;
  item: OrderResponse;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  totalPrize: boolean;
  type: string;
  currency: string;
  setModalVisible: (val: boolean) => void;
  dropDownIndex: number;
  setDropDownIndex: (val: number | null) => void;
  canCancel: CanCancel;
  setOrderId: (id: string) => void;
  orderId: string;
  onSelectMethod: (orderId: number, retry: boolean) => void;
  circularProgress: number;
  onInfoClick: (id: string) => void;
  onCancelOrder: (item: OrderResponse, i: number) => void;
  checkCancelLoader: boolean;
};

const OrderCard = ({
  index,
  item,
  totalPrize,
  type,
  navigation,
  currency,
  setModalVisible,
  setDropDownIndex,
  dropDownIndex,
  canCancel,
  setOrderId,
  orderId,
  onSelectMethod,
  circularProgress,
  onInfoClick,
  onCancelOrder,
  checkCancelLoader,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [modalVisibleImage, setModalVisibleImage] = useState<boolean>(false);
  const [productModalData, setProductModalData] = useState<OrderProductItem[]>(
    item?.data?.items || [],
  );
  const [activeOrderId, setActiveOrderId] = useState<string | null>(null);
  const statusObj = orderStatusColor(item?.data?.status?.label, colors);

  const handleOpenModal = useCallback(() => {
    setProductModalData(item?.data?.items || []);
    setModalVisibleImage(true);
  }, [item?.data?.items]);

  const handleToggle = (id: string) => {
    setActiveOrderId(prevOrderId => (prevOrderId === id ? null : id));
  };

  const productIds = item?.data?.items?.map(
    (data: OrderProductItem) => data?.product_id,
  );

  const calculateEarnedCoin = (data: OrderProductItem) => {
    return data?.reduce(
      (total: any, val: OrderProductItem) =>
        total + val?.reward_earned_coins * val?.ordered_qty,
      0,
    );
  };

  const copyToClipboard = orderID => {
    Clipboard.setString(orderID);
    showSuccessMessage(t('toastMassages.orderCopiedText'));
  };

  const totalEarnedCoin = calculateEarnedCoin(productModalData);

  const getEstimateStatus = () => {
    if (!item?.data) return '';
    let statusColor = 'text2';
    let dateColor = 'green2';
    let expectedDate = '';
    let status = '';
    let estimateDate;
    let icon = '';
    if (item.data.dates?.estimated_delivery_date && !returnShow) {
      status = t('orderTrack.estOn');
      statusColor = 'limeGreen1';
      estimateDate = item.data.dates?.estimated_delivery_date;
      icon = 'arrivalRound';
    } else if (item.data.dates?.delivered?.date) {
      status = t('orderListing.deliveredOn');
      statusColor = 'limeGreen1';
      estimateDate = item.data.dates?.delivered?.date;
      icon = 'checkRound';
    } else if (item.data.dates?.cancelled?.date && !returnShow) {
      status = t('orderTrack.cancelOn');
      estimateDate = item.data.dates?.cancelled?.date;
      statusColor = 'persianRed';
      dateColor = 'persianRed';
      icon = 'cancelRound';
    } else if (item.data.dates?.returned?.date && !returnShow) {
      status = t('orderListing.returnedOn');
      estimateDate = item.data.dates?.returned?.date;
      statusColor = 'persianRed';
      dateColor = 'persianRed';
      icon = 'returnRound';
    }
    if (!estimateDate) return '';
    expectedDate = formatDate(estimateDate, 'MMM D, YYYY');
    return {status, expectedDate, statusColor, dateColor, icon};
  };

  const estimate = getEstimateStatus();
  const statusValue = item?.data?.status?.label;
  const returnShow = [
    orderStatusLabel.Delivered,
    orderStatusLabel.PartiallyDelivered,
  ].includes(statusValue);
  const cancelShow = [
    orderStatusLabel.PaymentPending,
    orderStatusLabel.PartiallyPacked,
    orderStatusLabel.Packed,
    orderStatusLabel.OrderPlaced,
  ].includes(statusValue);
  return (
    <Pressable
      key={index}
      onPress={() => navigation.navigate('OrderDetail', {item, type})}>
      <View style={[styles.box, {marginTop: index == 0 ? 8 : 0}]}>
        <View style={styles.fRow}>
          <View style={styles.durationsBox}>
            {estimate && estimate?.expectedDate && (
              <View style={styles.orderTxtView}>
                {estimate?.icon && (
                  <>
                    <ImageIcon
                      sourceType="local"
                      icon={estimate?.icon}
                      resizeMode="contain"
                      size="xx"
                    />
                    <Spacer type="Horizontal" size="s" />
                  </>
                )}
                <Label
                  fontFamily="SemiBold"
                  weight="600"
                  color={estimate?.statusColor}
                  text={estimate?.status}
                  size="m">
                  <Spacer type="Horizontal" size="s" />
                  <Label
                    size="m"
                    weight="600"
                    text={estimate?.expectedDate}
                    fontFamily="SemiBold"
                    color={estimate?.dateColor}
                  />
                </Label>
              </View>
            )}
          </View>
          <View style={styles.fRow}>
            <ImageIcon
              sourceType="local"
              icon="statusCav"
              resizeMode="contain"
              tintColor={statusObj.rColor}
              style={styles.roundStyle}
            />
            <View
              style={[
                styles.rCStyle,
                {backgroundColor: statusObj?.bgLColor[0]},
              ]}
            />
            <LinearGradient
              colors={statusObj?.bgLColor}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              style={styles.badge}>
              <Label
                size="m"
                color="whiteColor"
                weight="500"
                text={
                  statusObj?.status
                    ? statusObj?.status
                    : snakeToTitleCase(
                        item?.data?.status?.label,
                        'Approved',
                        'Request Approved',
                      )
                }
                fontFamily="Medium"
              />
            </LinearGradient>
          </View>
        </View>
        <Spacer size="s" />
        <View style={styles.rowCenter}>
          <View style={styles.rowView}>
            <TouchableOpacity
              style={styles.rowCenter}
              onPress={() => copyToClipboard(item?.data?.order_id)}>
              <View style={styles.orderNote}>
                <ImageIcon
                  resizeMode="contain"
                  size="m"
                  sourceType="local"
                  icon="note"
                />
              </View>
              <Spacer type="Horizontal" size="s" />
              <Label
                size="m"
                color="text2"
                text={`${t('orderListing.order')} #`}
                fontFamily="Medium"
                weight="500"
              />
              <Label
                text={item?.data?.order_id}
                fontFamily="Medium"
                size="m"
                color="text2"
                weight="500"
              />
              <Spacer type="Horizontal" size="sx" />
              <ImageIcon
                resizeMode="contain"
                size="mx"
                sourceType="local"
                icon="copyGrey"
              />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="sx" />
            <View style={styles.dotStyle} />
            <Spacer type="Horizontal" size="s" />
            <Label
              size="m"
              color="text2"
              text={moment(item?.data?.dates?.ordered?.date).format(
                'MMM D, YYYY',
              )}
              fontFamily="Medium"
              weight="500"
            />
          </View>
          <Spacer type="Horizontal" size="s" />
          <ImageIcon
            resizeMode="contain"
            size="l"
            sourceType="local"
            icon="rightArrowGrey"
          />
        </View>
        <Spacer type="Vertical" size="s" />
        <View style={styles.rowCenter}>
          <Label
            size="m"
            color="text2"
            weight="500"
            text={`${t('orderReturn.qty1')}${
              item?.data?.order_summary?.total_product_qty < 9 ? '0' : ''
            }${item?.data?.order_summary?.total_product_qty}`}
            fontFamily="Medium"
          />
          <Spacer type="Horizontal" size="xm" />
          <View style={styles.lineStyle} />
          <Spacer type="Horizontal" size="xm" />
          <Label
            color="text"
            weight="600"
            text={`${
              currency?.symbol ? currency?.symbol : t('PDP.rupee')
            }${parseFloat(item?.data?.order_summary?.order_amount).toFixed(2)}`}
            fontFamily="SemiBold"
            size="l"
          />
          {type === 'order' && (
            <>
              {item?.data?.order_summary?.total_savings > 0 && (
                <View style={styles.rowCenter}>
                  <Spacer type="Horizontal" size="xm" />
                  <View style={styles.lineStyle} />
                  <Spacer type="Horizontal" size="xm" />
                  <Label
                    size="xms"
                    color="skyBlue23"
                    text={t('orderListing.savedRs')}
                    weight="500"
                    fontFamily="Medium">
                    <Label
                      size="xms"
                      color="skyBlue23"
                      weight="600"
                      text={`  ${t('otherText.rupee')}${
                        item?.data?.order_summary?.total_savings
                      }`}
                      fontFamily="SemiBold"
                    />
                  </Label>
                </View>
              )}

              {(item?.data?.rewards?.earned_points > 0 ||
                item?.data?.rewards?.spent_points > 0) && (
                <View style={styles.rowCenter}>
                  {item?.data?.rewards?.earned_points > 0 && (
                    <View style={styles.rowCenter}>
                      <Spacer type="Horizontal" size="xm" />
                      <View style={styles.lineStyle} />
                      <Spacer type="Horizontal" size="xm" />
                      <ImageIcon
                        resizeMode="contain"
                        size="xm"
                        sourceType="local"
                        icon="plusGold"
                      />
                      <Label
                        size="m"
                        color="brown"
                        weight="500"
                        text={item?.data?.rewards?.earned_points}
                        fontFamily="Medium"
                      />
                      <Spacer type="Horizontal" size="s" />
                      <ImageIcon
                        resizeMode="contain"
                        style={styles.rewardIconSmall}
                        sourceType={
                          item?.data?.rewards?.reward_icon ? 'url' : 'local'
                        }
                        icon="coin"
                        source={getImageUrl(
                          item?.data?.rewards?.reward_icon,
                          'coin',
                        )}
                      />
                      {coinInfoShow(item?.data?.status?.label) && (
                        <Pressable onPress={() => onInfoClick()}>
                          <ImageIcon
                            size="xl"
                            icon="infoCircle"
                            tintColor="text2"
                            style={styles.infoStyle}
                          />
                        </Pressable>
                      )}
                    </View>
                  )}
                  {item?.data?.rewards?.spent_points > 0 && (
                    <View style={styles.rowCenter}>
                      <Spacer type="Horizontal" size="xm" />
                      <View style={styles.lineStyle} />
                      <Spacer type="Horizontal" size="xm" />
                      <View style={styles.minusStyle} />
                      <Label
                        size="mx"
                        color="brown"
                        weight="500"
                        text={item?.data?.rewards?.spent_points}
                        fontFamily="Medium"
                      />
                      <Spacer type="Horizontal" size="s" />
                      <ImageIcon
                        resizeMode="contain"
                        style={styles.rewardIconSmall}
                        sourceType={
                          item?.data?.rewards?.reward_icon ? 'url' : 'local'
                        }
                        icon="coin"
                        source={getImageUrl(
                          item?.data?.rewards?.reward_icon,
                          'coin',
                        )}
                      />
                    </View>
                  )}
                </View>
              )}
            </>
          )}
        </View>

        <Spacer size="xm" />
        <View style={styles.fRow}>
          {item?.data?.items ? (
            item?.data?.items?.slice(0, 5).map((data: any, i: number) => (
              <TouchableOpacity
                key={i}
                style={styles.image}
                onPress={handleOpenModal}>
                {i === 4 && (
                  <View style={styles.underPrizeIconView}>
                    <TouchableOpacity
                      onPress={() => setModalVisibleImage(true)}>
                      <Label
                        text={`+${item?.data?.items?.length - 1}`}
                        size="mx"
                        color="whiteColor"
                        fontFamily="Medium"
                      />
                    </TouchableOpacity>
                  </View>
                )}
                <ImageIcon
                  resizeMode="contain"
                  style={styles.imageSize}
                  sourceType="url"
                  source={getImageUrl(data?.thumbnail, 'product')}
                />
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.image}>
              <ImageIcon
                resizeMode="contain"
                style={styles.imageSize}
                sourceType={item?.image ? 'url' : 'local'}
                icon="dentalkartLogo"
                source={item?.image ? item?.image : 'lockIcon'}
              />
            </View>
          )}
        </View>
        {statusValue === orderStatusLabel.Delivered &&
          item?.data?.rate_info &&
          (item?.data?.rate_info?.is_full_rated ||
            item?.data?.rate_info?.rated_count > 0) && (
            <>
              <Spacer size="xm" />
              <View style={styles.rateView}>
                <ImageIcon
                  icon="starFillGreen"
                  size="xx"
                  tintColor="green2"
                  resizeMode="cover"
                />
                <Spacer size="xm" type="Horizontal" />
                <Label
                  fontFamily="Medium"
                  weight="500"
                  color="text"
                  text={t('rate.ratingThank')}
                  size="m"
                  style={styles.flex}
                />
                <Spacer size="xm" type="Horizontal" />
                {item?.data?.rate_info?.is_full_rated ? (
                  <TouchableOpacity
                    style={styles.rowCenter}
                    activeOpacity={0.7}
                    onPress={() =>
                      navigation.navigate('RateReview', {
                        orderId: item?.data?.order_id,
                      })
                    }>
                    <Label
                      text={t('manageAddress.edit')}
                      size="mx"
                      color="newSunnyOrange"
                      fontFamily="Medium"
                      weight="500"
                      textTransform="capitalize"
                    />
                    <Spacer size="xm" type="Horizontal" />
                    <ImageIcon
                      size="m"
                      icon="editPencil"
                      tintColor="newSunnyOrange"
                    />
                  </TouchableOpacity>
                ) : (
                  <Label
                    fontFamily="SemiBold"
                    weight="600"
                    color="green2"
                    text={`${item?.data?.rate_info?.rated_count} ${t(
                      'rate.outOf',
                    )} ${item?.data?.rate_info?.total_count} ${t(
                      'rate.rated',
                    )}`}
                    size="m"
                  />
                )}
              </View>
            </>
          )}
        <Spacer size="xm" />
        <View style={styles.imageBox}>
          <View style={styles.buttonStyle}>
            {type === 'return' ? null : (
              <>
                {/* {item?.data?.status?.label ==
                  orderStatusLabel.PaymentPending && (
                  <>
                    <Button
                      onPress={() => {}}
                      text={t('buttons.modifyOrder')}
                      type="secondary"
                      paddingHorizontal="m"
                      labelColor="whiteColor"
                      weight="500"
                      labelSize={sAllDevice ? 'm' : 'mx'}
                      radius="xms"
                      size="extra-small"
                      iconLeft="exchange"
                      tintColor="whiteColor"
                      iconSize="l"
                      styleLeftIcon={styles.btnLeftIcon}
                      style={styles.orangeBtn}
                    />
                    <Spacer
                      type="Horizontal"
                      size={sAllDevice ? 'sx' : 'xms'}
                    />
                  </>
                )} */}
                {item?.data?.status?.label ===
                orderStatusLabel.PaymentPending ? (
                  <>
                    <Button
                      onPress={() => {
                        onSelectMethod(item?.data?.order_id, true);
                      }}
                      text={
                        circularProgress !== item?.data?.order_id
                          ? t('retryPaymentButton.retryPayment')
                          : t('retryPaymentButton.Retrying')
                      }
                      paddingHorizontal="m"
                      labelColor="text"
                      weight="500"
                      labelSize={sAllDevice ? 'm' : 'mx'}
                      radius="xms"
                      size="extra-small"
                      type="bordered"
                      borderColor="grey2"
                      iconLeft="retry"
                      tintColor="text"
                      iconSize="l"
                      styleLeftIcon={styles.btnLeftIcon}
                      style={styles.btnStyle}
                    />
                    <Spacer
                      type="Horizontal"
                      size={sAllDevice ? 'sx' : 'xms'}
                    />
                  </>
                ) : (
                  <>
                    <Button
                      onPress={() =>
                        navigation?.navigate('BuyAgainScene', {
                          productIds: productIds,
                          item,
                        })
                      }
                      text={t('orderListing.buyAgain')}
                      paddingHorizontal="m"
                      labelColor="whiteColor"
                      weight="500"
                      labelSize={sAllDevice ? 'm' : 'mx'}
                      radius="xms"
                      size="extra-small"
                      style={styles.orangeBtn}
                    />
                    <Spacer
                      type="Horizontal"
                      size={sAllDevice ? 'sx' : 'xms'}
                    />
                  </>
                )}
              </>
            )}
            {cancelShow && item?.data?.can_cancel && (
              <>
                <Button
                  onPress={() => onCancelOrder(item?.data?.order_id, index)}
                  text={t('buttons.cancel')}
                  type="bordered"
                  paddingHorizontal="m"
                  labelColor="text"
                  weight="500"
                  labelSize={sAllDevice ? 'm' : 'mx'}
                  radius="xms"
                  size="extra-small"
                  borderColor="grey2"
                  loading={dropDownIndex === index ? checkCancelLoader : false}
                  loaderColor={colors.black}
                  iconLeft="crossBlack"
                  tintColor="text"
                  iconSize="l"
                  styleLeftIcon={styles.btnLeftIcon}
                  style={styles.btnStyle}
                />
                <Spacer type="Horizontal" size={sAllDevice ? 'sx' : 'xms'} />
              </>
            )}
            {returnShow && item?.data?.can_return && (
              <View style={styles.rowCenter}>
                <Button
                  onPress={() =>
                    navigation.navigate('OrderReturnListScene', {
                      orderId: item?.data?.order_id,
                    })
                  }
                  text={t('orderTrack.return')}
                  type="bordered"
                  paddingHorizontal="m"
                  labelColor="text"
                  weight="500"
                  labelSize={sAllDevice ? 'm' : 'mx'}
                  radius="xms"
                  size="extra-small"
                  borderColor="grey2"
                  iconLeft="backward"
                  tintColor="text"
                  iconSize="l"
                  styleLeftIcon={styles.btnLeftIcon}
                  style={styles.btnStyle}
                />
                <Spacer type="Horizontal" size={sAllDevice ? 'sx' : 'xms'} />
              </View>
            )}
            {statusValue === orderStatusLabel.Delivered &&
              !item?.data?.rate_info?.is_full_rated &&
              item?.data?.rate_info && (
                <View style={styles.rowCenter}>
                  <Button
                    onPress={() =>
                      navigation.navigate('RateReview', {
                        orderId: item?.data?.order_id,
                      })
                    }
                    text={
                      item?.data?.rate_info?.rated_count > 0
                        ? t('buttons.rateMore')
                        : t('rate.rateOrder')
                    }
                    type="bordered"
                    paddingHorizontal="m"
                    labelColor="text"
                    weight="500"
                    labelSize={sAllDevice ? 'm' : 'mx'}
                    radius="xms"
                    size="extra-small"
                    borderColor="grey2"
                    iconLeft="starOutline"
                    tintColor="text"
                    iconSize="l"
                    styleLeftIcon={styles.rateIcon}
                    style={styles.btnStyle}
                  />
                </View>
              )}
          </View>
        </View>

        <Spacer type="Vertical" size="xm" />
        {modalVisibleImage && (
          <OrderImageModal
            visible={modalVisibleImage}
            onClose={() => setModalVisibleImage(false)}
            data={productModalData}
            item={item}
            totalPrice={item?.data?.order_summary?.order_amount || 0}
            navigation={navigation}
            totalEarnedCoin={totalEarnedCoin}
            spentPoint={item?.data?.rewards?.spent_points}
            type="orderBuyAgain"
          />
        )}
      </View>
    </Pressable>
  );
};

export default OrderCard;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    roundImage: {
      borderWidth: Sizes.x,
      borderColor: colors.border,
      width: Sizes.x6l + Sizes.xl,
      height: Sizes.x6l + Sizes.xl,
      borderRadius: Sizes.x4l,
      alignItems: 'center',
      justifyContent: 'center',
      margin: Sizes.xms,
    },
    imageCecile: {height: Sizes.x6l + Sizes.s, width: Sizes.x6l + Sizes.s},
    label: {
      alignItems: 'center',
      maxWidth: Sizes.x6l + Sizes.l,
      textAlign: 'center',
      paddingVertical: Sizes.xs,
    },
    topBrandsAlign: {
      alignItems: 'center',
      alignSelf: 'flex-start',
      width: '20%',
    },
    view: {backgroundColor: 'red'},
  });

export default styles;

import React, {useEffect, useRef, useState} from 'react';
import {View, Animated, Dimensions} from 'react-native';
import styles from './style';
import Label from '../label';

const {height: WINDOW_HEIGHT} = Dimensions.get('window');

const RollingText = () => {
  const translateY = useRef(new Animated.Value(WINDOW_HEIGHT)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const [currentIndex, setCurrentIndex] = useState(0);

  const texts = ['Products...', 'Brands...', 'Category...'];

  useEffect(() => {
    const interval = setInterval(() => {
      opacity.setValue(1);

      Animated.sequence([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),

        Animated.delay(1000),

        Animated.parallel([
          Animated.timing(translateY, {
            toValue: -15,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        setCurrentIndex(prevIndex => (prevIndex + 1) % texts.length);

        translateY.setValue(WINDOW_HEIGHT);
      });
    }, 4000);

    return () => clearInterval(interval);
  }, [translateY, opacity, texts.length]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[styles.animatedText, {transform: [{translateY}], opacity}]}>
        <Label
          text={texts[currentIndex]}
          size="l"
          weight="400"
          color="text2"
          lineHeight="xsl"
        />
      </Animated.View>
    </View>
  );
};

export default RollingText;

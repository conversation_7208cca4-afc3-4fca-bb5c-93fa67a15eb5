import React, {useMemo} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer} from 'components/atoms';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {Button} from 'components/molecules';
import {sDevice, mDevice} from 'utils/utils';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';

type Props = {
  navigation: any;
  onClose: () => void;
  orderId: string;
  reload: () => void;
};

const RateUs = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {onClose, navigation, orderId, reload} = props;

  const onRateReview = () => {
    navigation.navigate('RateReview', {orderId, reload: () => reload()});
  };

  const onCloseReview = async () => {
    global.showRate = true;
    onClose();
  };

  return (
    <View style={styles.rateView}>
      <FastImage
        source={Icons.rateGif}
        style={styles.starStyle}
        resizeMode="cover"
      />
      <Spacer size="xm" type="Horizontal" />
      <View style={styles.fOne}>
        <Label
          text={t('rate.rateTitle')}
          weight="600"
          fontFamily="SemiBold"
          size="mx"
          color="text"
        />
        <Spacer size="xs" />
        <Label
          text={t('rate.rateDes')}
          fontFamily="Regular"
          weight="400"
          size="m"
          color="text"
        />
      </View>

      <Spacer size="sx" type="Horizontal" />
      <Button
        onPress={() => onRateReview()}
        text={t('buttons.rateUs').toUpperCase()}
        labelColor="whiteColor"
        weight="500"
        labelSize={sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
        radius="xm"
        size="extra-small"
        style={styles.orangeBtn}
        labelStyle={styles.btnLabel}
      />
      <Spacer size="sx" type="Horizontal" />
      <TouchableOpacity onPress={onCloseReview}>
        <ImageIcon
          size="xxl"
          tintColor="text"
          icon="closeIcons"
          resizeMode="contain"
        />
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(RateUs);

import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import {Label, Separator} from 'components/atoms';
import {Sizes} from 'common';

import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  text: string;
  onPress: () => void;
};

const ProfileListRow = ({text, onPress}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <TouchableOpacity onPress={onPress}>
      <View style={styles.container}>
        <Label text={text} weight={'600'} />
        <Icon name="chevron-right" size={Sizes.l} />
      </View>
      <Separator />
    </TouchableOpacity>
  );
};

export default ProfileListRow;

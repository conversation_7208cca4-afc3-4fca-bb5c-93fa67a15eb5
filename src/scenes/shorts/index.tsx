import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {
  Image,
  View,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import Icons from 'common/icons';
import {t} from 'i18next';
import {
  CategoriesItem,
  Label,
  Separator,
  Spacer,
  ShortsItem,
  ImageIcon,
} from 'components/atoms';
import {AnalyticsEvents, FilterAndSortModal} from 'components/organisms';
import {homePageSections} from 'services/home';
import {Header} from 'components/molecules';
import {getShorts, addFeedLike, getFeedDetail} from 'services/shorts';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {ShortListLoader} from 'skeletonLoader';
import ErrorHandler from 'utils/ErrorHandler';
import {useSelector, useDispatch} from 'react-redux';
import {RootState} from '@types/local';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getShortsList} from 'app-redux-store/slice/appSlice';

type Props = {
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
};

const Shorts = ({navigation}: Props) => {
  const TAG = 'Shorts';
  const [loading, setLoading] = useState<boolean>(false);
  const [reelsData, setReelsData] = useState<Shorts[]>([]);
  const [modalType, setModalType] = useState<'filter' | 'sort'>('filter');
  const [visible, setVisible] = useState<boolean>(false);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [sections, setSections] = useState<Section[]>([]);
  const [adsBanner, setAdsBanner] = useState<Section[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<string>('Newest');
  const [selectedCategory, setSelectedCategory] = useState<
    SectionElement | undefined
  >();
  const [, setState] = useState({});
  const [pageNo, setPageNo] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [pageLimit, setPageLimit] = useState<number>(16);
  const [moreLoading, setMoreLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const insets = useSafeAreaInsets();
  const dispatch = useDispatch();

  useEffect(() => {
    getVideos(1, selectedFilter);
    getAllSections();
    AnalyticsEvents('SHORTS', 'Shorts page viewed', {}, userInfo, isLoggedIn);
    dispatch(getShortsList());
  }, []);

  useEffect(() => {
    if (pageNo > 1) {
      getVideos(pageNo, selectedFilter);
    }
  }, [pageNo]);

  const getAllSections = async () => {
    // const {status, data} = await homePageSections();
    // if (status) {
    //   setSections(data?.sections);
    // }
    let params = {'page-type': 'shorts'};
    const response = await homePageSections(params);
    if (response?.status) {
      const adsData = response?.data?.sections;
      if (adsData?.length > 0) {
        setAdsBanner(adsData[0]?.elements);
      }
    }
  };

  const getVideos = async (
    currentPage: number,
    sort: string,
    refresh = false,
    hide = false,
  ) => {
    if (currentPage === 1) {
      if (refresh) {
        setRefreshing(true);
      } else {
        setLoading(hide ? false : true);
      }
    } else {
      setMoreLoading(true);
    }
    const response = await getShorts(
      currentPage,
      pageLimit,
      sort?.toLowerCase(),
    );
    setLoading(false);
    setMoreLoading(false);
    setRefreshing(false);
    const {status, data} = response;
    if (status && data.status === 'success') {
      let shortsData = [];
      if (currentPage === 1) {
        setTotalPages(data?.data?.totalPages);
        shortsData = data?.data?.rows;
      } else {
        shortsData = [...reelsData, ...data?.data?.rows];
      }
      setReelsData(shortsData);
    }
  };

  const onAddLike = async (item: Shorts, i: number, check) => {
    const index = check ? i + 4 : i;
    let tempData = reelsData;
    tempData[index].isLiked = item.isLiked == 0 ? 1 : 0;
    tempData[index].like_count =
      item.isLiked == 0 ? item.like_count - 1 : item.like_count + 1;
    setReelsData(tempData);
    setState({});
    const {status, data} = await addFeedLike(item.id);
    if (status) {
      const response = await getFeedDetail(item?.id);
      const {status, data} = response;
      const videoItem = {
        ...tempData[index],
        like_count: data?.data?.like_count,
        isLiked: data?.data?.isLiked,
      };
      tempData[index] = videoItem;
      setReelsData(tempData);
      setState({});
    }
  };

  const renderItems = (item: Shorts, index: number, check = false) => {
    return (
      <ErrorHandler
        componentName={`${TAG} ShortsItem`}
        onErrorComponent={<View />}>
        <ShortsItem
          item={item}
          index={index}
          navigation={navigation}
          callBack={() => getVideos(1, selectedFilter)}
          onAddLike={(data, i) => onAddLike(data, i, check)}
        />
      </ErrorHandler>
    );
  };

  const category = sections?.filter(
    item => item?.layout_type?.code === 'top_categories',
  );

  const onClear = () => {
    setSelectedCategory(undefined);
    setState({});
  };

  const fetchMoreData = useCallback(() => {
    if (!loading && !moreLoading && pageNo < totalPages) {
      setPageNo(pageNo + 1);
    }
  }, [loading, moreLoading, pageNo, totalPages]);

  const renderFooter = () => {
    return moreLoading ? (
      <View style={styles.loaderStyle}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    ) : null;
  };

  const onSelectFilter = (value: string) => {
    setPageNo(1);
    setSelectedFilter(value);
    getVideos(1, value);
  };

  return (
    <SafeAreaView style={[styles.screenContainer, {paddingTop: insets.top}]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header navigation={navigation} backButton text={t('shorts.title')} />
      </ErrorHandler>
      <View style={styles.screenContainer}>
        {loading ? (
          <ShortListLoader />
        ) : (
          <View style={styles.flex}>
            <FlatList
              data={['']}
              keyExtractor={(_, index) => index.toString()}
              refreshing={refreshing}
              onRefresh={() => getVideos(1, selectedFilter, true)}
              renderItem={() => (
                <View style={styles.subView}>
                  <Image
                    resizeMode="stretch"
                    style={styles.bannerImage}
                    source={
                      adsBanner?.length > 0
                        ? {uri: adsBanner[0]?.media?.mobile_image}
                        : Icons.shortsBanner
                    }
                  />
                  {/* <View>
                    <View style={styles.container}>
                      <Label
                        fontFamily="Medium"
                        size="mx"
                        style={styles.text}
                        color="text"
                        text={t('shorts.category')}
                      />
                      <View style={styles.line} />
                    </View>
                    <Spacer size="m" />
                    <FlatList
                      showsHorizontalScrollIndicator={false}
                      style={styles.listView}
                      horizontal
                      data={category?.length > 0 ? category[0].elements : []}
                      renderItem={({
                        item,
                        index,
                      }: {
                        item: SectionElement;
                        index: number;
                      }) => (
                        <CategoriesItem
                          index={index}
                          item={item}
                          navigation={navigation}
                          filter={true}
                          selectedCategory={selectedCategory}
                          onSelect={(data: SectionElement) =>
                            setSelectedCategory(data)
                          }
                          onClear={() => onClear()}
                        />
                      )}
                      keyExtractor={(_, index) => index.toString()}
                    />
                  </View> */}
                  <Spacer size="l" />
                  <TouchableOpacity
                    style={styles.sortBox}
                    onPress={() => {
                      setModalType('sort');
                      setVisible(!visible);
                    }}>
                    <View style={styles.fCenter}>
                      <ImageIcon size="xxl" tintColor="text" icon="sortBy" />
                      <Spacer size="xms" />
                      <Label
                        text={t('filters.sortBy')}
                        size="mx"
                        fontFamily="Medium"
                        color="text"
                      />
                    </View>
                    {selectedFilter && (
                      <Label
                        text={' - ' + selectedFilter}
                        size="m"
                        fontFamily="Regular"
                        color="text2"
                        style={styles.filterSelectText}
                      />
                    )}
                  </TouchableOpacity>
                  <Spacer size="l" />
                  {reelsData.length > 0 ? (
                    <>
                      <FlatList
                        columnWrapperStyle={styles.videoFList}
                        numColumns={2}
                        data={reelsData?.slice(0, 4)}
                        renderItem={({
                          item,
                          index,
                        }: {
                          item: Shorts;
                          index: number;
                        }) => renderItems(item, index)}
                        keyExtractor={(item, index) => index?.toString()}
                        ItemSeparatorComponent={() => <Spacer size="m" />}
                      />
                      <Spacer size="m" />
                      <Image
                        resizeMode="stretch"
                        style={styles.bannerImage}
                        source={
                          adsBanner?.length > 1
                            ? {uri: adsBanner[1]?.media?.mobile_image}
                            : Icons.shortsBanner
                        }
                      />
                      <Spacer size="m" />
                      <FlatList
                        columnWrapperStyle={styles.videoFList}
                        numColumns={2}
                        data={reelsData.slice(4, reelsData?.length)}
                        renderItem={({
                          item,
                          index,
                        }: {
                          item: Shorts;
                          index: number;
                        }) => renderItems(item, index, true)}
                        keyExtractor={(item, index) => index?.toString()}
                        ItemSeparatorComponent={() => <Spacer size="m" />}
                        ListFooterComponent={renderFooter()}
                        onEndReachedThreshold={0.8}
                        onEndReached={fetchMoreData}
                        initialNumToRender={reelsData?.length}
                        maxToRenderPerBatch={reelsData?.length}
                        removeClippedSubviews={true}
                      />
                    </>
                  ) : (
                    <View style={styles.notFound}>
                      <Image
                        style={styles.notFounds}
                        resizeMode="contain"
                        source={Icons.notFoundIcon}
                      />
                      <Label size="l" text={t('shorts.videoNotFound')} />
                    </View>
                  )}
                </View>
              )}
            />
            {/* <View style={styles.filterView}>
              <TouchableOpacity
                style={styles.filterBox}
                onPress={() => navigation.navigate('SavedShorts')}>
                <ImageIcon size="xxl" tintColor="text" icon="saveOutline" />
                <Spacer size="xms" />
                <Label
                  text={t('shorts.saved')}
                  size="mx"
                  fontFamily="Medium"
                  color="text"
                />
              </TouchableOpacity>
              <View style={styles.lineView} />
              <TouchableOpacity
                style={styles.filterBox}
                onPress={() => {
                  setModalType('sort');
                  setVisible(!visible);
                }}>
                <View>
                  <View style={styles.fCenter}>
                    <ImageIcon size="xxl" tintColor="text" icon="sortBy" />
                    <Spacer size="xms" />
                    <Label
                      text={t('filters.sortBy')}
                      size="mx"
                      fontFamily="Medium"
                      color="text"
                    />
                  </View>
                  {selectedFilter && (
                    <Label
                      text={selectedFilter}
                      size="m"
                      fontFamily="Regular"
                      color="text2"
                      style={styles.filterSelectText}
                    />
                  )}
                </View>
              </TouchableOpacity>
            </View> */}

            {visible && (
              <ErrorHandler
                componentName={`${TAG} FilterAndSortModal`}
                onErrorComponent={<View />}>
                <FilterAndSortModal
                  visible={visible}
                  onClose={setVisible}
                  applySortType={(value: string) => onSelectFilter(value)}
                  filterOptions={['']}
                  sortType={selectedFilter}
                  modalType={modalType}
                  pageType="shorts"
                  onClear={() => onSelectFilter('')}
                  totalProduct={0}
                />
              </ErrorHandler>
            )}
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

export default Shorts;

import {View} from 'react-native';
import React from 'react';
import ModalComponent from 'components/atoms/modalComponent';
import Button from '../button';
import WebView from 'react-native-webview';
import stylesWithOutColor from './styles';
import {useTheme} from '@react-navigation/native';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {ButtonProps} from 'react-native-share';
import {useMemo} from 'react';

type Props = {
  onCloseMethod?: () => void;
  IsVisible?: () => void;
  ButtonPress?: () => void;
  route: any;
  text?: string;
  NewsLink?: string;
  icon?: keyof typeof Icons | null;
  onPress?: () => void;
  style?: ButtonProps['style'];
  type: 'news' | 'save';
  likeShare: (item: News) => React.ReactElement;
};
const NewsWebModal = ({
  onCloseMethod,
  IsVisible,
  ButtonPress,
  NewsLink,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <ModalComponent
      style={styles.container}
      visible={IsVisible}
      onClose={onCloseMethod}>
      <Button
        ghost
        onPress={ButtonPress}
        iconRight="closeIcons"
        style={styles.button}
      />
      <View style={styles.flex}>
        <WebView source={{uri: NewsLink}} startInLoadingState={true} />
      </View>
    </ModalComponent>
  );
};

export default NewsWebModal;

import getEnvVars from './apiEnv';
import {WEBSITE_URL} from './environment';

const {
  BASE_URL,
  FEEDS_API_BASE_URL,
  X_API_KEY,
  IMAGE_BASE_URL,
  Version,
  REFERRAL_X_API_KEY,
  SUB_IMAGE_BASE_URL,
  ADMIN_URL,
} = getEnvVars();

const apiRoute = {
  customers: `${BASE_URL}customer/api/v1/`,
  interface: `${BASE_URL}interface/api/v1/`,
  api: `${BASE_URL}api/v1/`,
  cart: `${BASE_URL}cart/api/v1/`,
  serverless: `${BASE_URL}serverless/api/v1/`,
  nodeServerless: `${BASE_URL}node_svlss/api/v1/`,
  return: `${BASE_URL}return/api/v1/`,
  orderMg: `${BASE_URL}order-mg/api/v1/`,
  nodeBkAdmin: `${BASE_URL}node_bk_admn/`,
  reward: `${BASE_URL}reward/api/v1/`,
  review: `${BASE_URL}review/api/v1/`,
  utilityPy: `${BASE_URL}utility-py/api/v1/`,
  order: `${BASE_URL}order/api/v1/`,
  wishlist: `${BASE_URL}wishlist/api/v1/`,
  referral: `${BASE_URL}referral/api/v1/`,
  feed: `${BASE_URL}feed/api/v1/`,
  search: `${BASE_URL}search/api/v1/`,
  sale: `${ADMIN_URL}sale-template/`,
  tracking: `${BASE_URL}order-tracking/api/v1/`,
  updateProfile: `${BASE_URL}customer/api/v1/`,
  initialUpdateProfile: `${BASE_URL}customer/api/v2/`,
  customerAddress: `${BASE_URL}/customer-address/api/v1/`,
};

const apiEndpoints = {
  profile: 'customers/me',
  signIn: 'customers/auth/send-otp',
  customerMe: 'customers/me',
  appConfig: 'app-config/version',
  password: 'customers/me/password',
  verify: 'customers/auth/verify',
  socialLogin: 'customers/auth/social-login',
  recentView: 'customer/recent-and-previous-products',
  recentlyViewed: 'recently-viewed',
  createUser: 'customers',
  homePageSections: 'homepage-sections',
  urlResolver: 'url-resolver',
  brands: 'brands',
  payment: 'payment-methods/',
  carts: 'carts',
  customerOrders: 'customer-orders',
  categories: 'categories',
  cancelOrders: 'cancel-orders',
  cancelReasons: 'cancel-reasons',
  products: 'products',
  product: 'product',
  bulkOrder: 'bulk-order',
  returns: 'returns',
  customerAddress: 'customer-addresses',
  faqItems: 'faq/items',
  content: 'content',
  faq: 'faq',
  memberships: 'memberships',
  chat: 'chat',
  customerMemberships: 'customer-memberships/me',
  question: 'question',
  reviews: 'reviews',
  rewards: 'rewards',
  news: 'news',
  countries: 'countries',
  customerRegistrations: 'customer-registrations',
  delivery: 'delivery',
  orders: 'orders',
  magazines: 'magazines',
  wishlists: 'wishlists',
  feeds: 'feeds',
  feedLike: 'feeds-action/like',
  feedsComment: 'feeds-action/comment',
  feedsComments: 'feeds-action/comments',
  history: 'history',
  engagement: 'engagement',
  videos: 'videos',
  referrals: 'referrals',
  searchQuery: 'query',
  sellOnDentalKart: 'sell-on-dentalkart',
  addToWishList: 'wishlists/default/products',
  itemPromotions: 'item-promotions',
  pinCode: 'pincode-district',
  mappls: 'mappls',
  notifyMe: 'subscribe/stock-alert',
};

const subEndpoint = {
  returns: {
    reasonsAndActions: 'reasons-and-actions',
    orders: 'orders',
    failedPickups: 'failed-pickups',
    fileUpload: 'file/upload',
    previousReturns: 'previous-returns',
    trackDetail: 'track-detail',
    returnableItems: 'returnable-items',
  },
  products: {
    details: 'details',
    suggestion: 'suggestion',
    creditCard: 'credit_card',
    attachments: 'attachments',
    attributeMetaData: 'attribute-meta-data',
    children: 'children',
    recommendedProducts: 'recommended-products',
    feedback: 'feedback',
    reviews: 'reviews',
    frequentlyBought: 'frequently-bought',
  },
  bulkOrder: {
    requests: 'requests',
  },
  rewards: {
    summary: 'summary',
    transactions: 'transactions',
  },
  categories: {
    filters: 'filters',
    details: 'details',
  },
  carts: {
    merge: 'merge',
    items: 'items',
    addresses: 'addresses',
    shippingRates: 'shipping-rates',
    applyDiscountElement: 'apply-discount-element',
    removeCoupon: 'remove-coupon',
    coupons: 'coupons',
    amount: 'amount-promotions',
    rewardPoints: 'reward-points',
    buyNow: 'buy-now',
  },
  delivery: {
    cartPaymentMethods: 'cart-payment-methods',
    productDeliveryOptions: 'product-delivery-options',
  },
  order: {
    payments: 'payments',
    modifyPayments: 'modify-payment-method',
    cancelable: 'cancelable',
    cancel: 'cancel',
    summary: 'summary',
    shipments: 'shipments',
    shipmentInvoice: 'shipment-invoice',
  },
  magazines: {
    banners: 'banners',
    publishers: 'publishers',
    detail: 'detail',
  },
  question: {
    add: 'add',
    get: 'get',
    edit: 'edit',
  },
  wishlists: {
    default: 'default',
    friends: 'friends',
    moveProduct: 'move-product',
  },
  searchQuery: {
    results: 'results',
  },
  referrals: {
    validate: 'validate',
    summary: 'summary',
  },
  feeds: {
    likes: 'likes',
  },
  sellOnDentalKart: {
    request: 'requests',
  },
  helpCenter: {
    category: 'category',
    whatsAppConfig: 'whatsapp-config',
  },
  review: {
    validateSubmission: 'validate_submission',
  },
  address: {
    validations: 'validations',
    validateGST: 'validate-gstin',
  },
  mappls: {
    token: 'token',
    search: 'location/search',
  },
  app: {
    version: 'app-version',
    platform: 'platform=',
    android: 'platform=android',
    ios: 'platform=ios',
  },
  rating: {
    latestRating: 'latest-delivered-rating',
    rate: 'rate',
  },
};

const ImagePath = {
  product: `${IMAGE_BASE_URL}media/catalog/product`,
  featureProduct: `${SUB_IMAGE_BASE_URL}/media/catalog/product`,
  media: `${IMAGE_BASE_URL}media/`,
  category: `${IMAGE_BASE_URL}dt-media/category-icons/`,
  dentalkart: `${WEBSITE_URL}dentalkarticon.png`,
  imageUrl: SUB_IMAGE_BASE_URL,
};

export {
  apiRoute,
  apiEndpoints,
  subEndpoint,
  BASE_URL,
  FEEDS_API_BASE_URL,
  X_API_KEY,
  IMAGE_BASE_URL,
  Version,
  ImagePath,
  REFERRAL_X_API_KEY,
};

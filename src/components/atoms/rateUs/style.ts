import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    rateView: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: Sizes.xm,
      shadowColor: colors.blackColor,
      shadowOffset: {
        width: 0,
        height: Sizes.x,
      },
      shadowOpacity: 0.25,
      elevation: Sizes.s,
      backgroundColor: colors.whiteBlue1,
    },
    starStyle: {
      height: Sizes.xx4l,
      width: Sizes.xx4l,
    },
    fOne: {
      flex: Sizes.x,
    },
    orangeBtn: {
      backgroundColor: colors.softOrange,
      height: Sizes.x3l,
      paddingVertical: 0,
    },
    btnLabel: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const Styles = (colors: Theme['colors']) => {
  return StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.blackTransparent,
    },
    modalView: {
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
      paddingHorizontal: Sizes.s,
      shadowColor: colors.blackColor,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.25,
      shadowRadius: Sizes.s,
      elevation: Sizes.s,
      maxHeight: Sizes.screenHeight * 0.8,
      flexShrink: Sizes.x,
    },
    modalContent: {
      flexGrow: Sizes.x,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    modalflex: {flex: Sizes.x},
  });
};
export default Styles;

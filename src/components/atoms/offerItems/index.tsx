import Icons from 'common/icons';
import {t} from 'i18next';
import React from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import Label from '../label';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {productDummyImage} from 'utils/imageUrlHelper';
import {useMemo} from 'react';


type Props = {
  icon?: keyof typeof Icons;
};
const {colors} = useTheme();
const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
const data = [
  {
    id: 1,
    img: Icons.medicalGowns,
    title: 'offerItems.off',
    color: colors.offerTitleBlue,
    subTitle: t(t('offerItems.label')),
    bgcolor: colors.offerItemCartBg,
  },
  {
    id: 2,
    img: Icons.medicalGowns,
    title: 'offerItems.off',
    color: colors.offerTitleGreen,
    subTitle: t('offerItems.label'),
    bgcolor: colors.offerTitleGreenBg,
  },
  {
    id: 3,
    img: Icons.medicalGowns,
    title: 'offerItems.off',
    color: colors.offerTitleGolden,
    subTitle: t('offerItems.label'),
    bgcolor: colors.offerTitleGoldenBg,
  },
];

const OfferItems = ({icon}: Props) => {
  return (
    <View style={styles.mapView}>
      {data.map((item, index) => {
        return (
          <TouchableOpacity
            style={[styles.offerList, {backgroundColor: item.bgcolor}]}
            key={index}>
            <View style={styles.lableView}>
              <Label
                style={styles.offerText}
                text={t(item.title)}
                color={item.color}
              />
              <Label style={styles.offerLeb} text={t(item.subTitle)} />
            </View>
            <View style={styles.imageContioner}>
              <Image
                onError={e => {
                  e.target.uri = productDummyImage;
                }}
                source={Icons.medicalGowns}
              />
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default OfferItems;

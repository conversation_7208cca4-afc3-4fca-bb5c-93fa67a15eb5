import React, { useMemo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { t } from 'i18next';
import { ImageIcon, Label, Spacer, Separator } from 'components/atoms';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import Modal from 'react-native-modal';
import Icons from 'common/icons';
import { btnClickCallBack } from 'utils/utils';
import stylesWithOutColor from './style';
type Props = {
    visible: boolean;
    onClose: () => void;
};

const MobileNumberModal = (props: Props) => {
    const { visible, onClose } = props;
    const { colors } = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

    return (
        <Modal
            onBackButtonPress={onClose}
            onBackdropPress={onClose}
            isVisible={visible}
            animationIn="fadeIn"
            animationOut="fadeOut"
            animationInTiming={75}
            animationOutTiming={75}
            backdropOpacity={0.25}
            style={styles.modalStyle}>
            <View style={styles.removeModalView}>
                <View style={styles.headerView}>
                    <Label
                        text={t('login.signUpWith')}
                        size="l"
                        fontFamily="Medium"
                        color="categoryTitle"
                    />
                    <TouchableOpacity onPress={onClose}>

                    <ImageIcon
                        icon="mobileModalCrossIcon"
                        size="xl"
                    />
                    </TouchableOpacity>
                </View>

                <View style={styles.mobileNumberSubView}>
                    <ImageIcon
                        icon="mobileNumberIcon"
                        size="x6l"
                    />
                    <Label
                        text="+91-44434343444"
                        size="l"
                        fontFamily="Medium"
                        color="categoryTitle"
                    />
                </View>

                <Label
                    text={t('login.noneOfTheAbove')}
                    size="mx"
                    fontFamily="Medium"
                    color="skyBlue23"
                />
            </View>
        </Modal>
    );
};

export default React.memo(MobileNumberModal);

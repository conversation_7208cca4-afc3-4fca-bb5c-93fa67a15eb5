import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import stylesWithOutColor from './style';
import {Keyboard, TouchableOpacity} from 'react-native';
import Spacer from '../spacer';
import Label from '../label';
import ImageIcon from '../imageIcon';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {useMemo} from 'react';
import { debugLog } from 'utils/debugLog';
import { useDispatch } from 'react-redux';
import { setLoading } from 'app-redux-store/slice/appSlice';

type Props = {
  getUserData: (data: any) => Promise<void>;
  // setLoading: () => void;
};

const LoginGoogle = ({
  getUserData,
  isWhatsapp,
  onPressWhatsapp,
  textStyle,
}: // setLoading,
Props) => {
  const {colors} = useTheme();
  const dispatch = useDispatch()
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const googleSignIn = async () => {
    Keyboard.dismiss( )
    // setLoading();
    GoogleSignin.configure({
      scopes: ['email', 'profile'],
      webClientId:
      '285108782909-ouh6alnd146f1het75vn5lvim26tuvpm.apps.googleusercontent.com',
      forceConsentPrompt: true,
    });
    try {
      const hasServices = await GoogleSignin.hasPlayServices();
      if (hasServices) {
        await GoogleSignin.signOut();
        dispatch(setLoading(true));
        const userInfo = await GoogleSignin.signIn();
        getUserData(userInfo);
      }
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        dispatch(setLoading(false));
      } else if (error.code === statusCodes.IN_PROGRESS) {
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        dispatch(setLoading(false));
      } else {
        dispatch(setLoading(false));
        debugLog(error);
      }
    }
  };

  return (
    // eslint-disable-next-line react/react-in-jsx-scope
    <TouchableOpacity
    testID='tOGoogleLogin'
      onPress={() => (isWhatsapp ? {onPressWhatsapp} : googleSignIn())}
      style={styles.whatsapp}>
      <ImageIcon size={'xxxl'} icon={isWhatsapp ? 'whatsapp' : 'googleLogo'} />
      <Spacer size="m" />
      <Label
        color="cobaltblue"
        size="mx"
        style={[styles.whatsAppText, textStyle]}
        text={isWhatsapp ? t('login.whatsapp') : t('googleSignup.googleSignUp')}
        weight="500"
      />
    </TouchableOpacity>
  );
};

export default LoginGoogle;

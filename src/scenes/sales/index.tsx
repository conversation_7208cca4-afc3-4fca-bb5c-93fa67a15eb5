import React, { useMemo, useEffect, useState } from 'react';
import {
  View,
  SafeAreaView,
  Text,
  ActivityIndicator,
  FlatList,
  TouchableOpacity,
  InteractionManager,
} from 'react-native';
import { t } from 'i18next';
import { Header } from 'components/molecules';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamsList } from 'routes';
import ErrorHandler from 'utils/ErrorHandler';
import stylesWithOutColor from './style';
import { useTheme } from '@react-navigation/native';
import { fetchSaleDetails } from 'services/sales';
import { DeviceWidth as Width } from 'config/environment';
import FastImage from 'react-native-fast-image';
import { resolveUrl } from 'utils/resolveUrl';
import { showErrorMessage } from 'utils/show_messages';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  route: {params:{saleId: string}};
};

type SaleDetailsT = {
  templateName: string
}

const Sales = ({ navigation, route }: Props) => {
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const TAG = 'Sales';
  const insets = useSafeAreaInsets();
  // Extract saleId from route.params
  const { saleId } = route.params || {};
  
  // State for sale details
  const [saleDetails, setSaleDetails] = useState<SaleDetailsT | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const getCategoryIdFromUrl = (url) => {
    const match = url.match(/[?&]id=(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  };
  // Helper function for navigation
  const handleNavigation =  (item) => {
    if (item.landingURL && item.landingURL.includes('membership')) {
      navigation?.navigate('MembershipPage');
    } else if (item.landingURL && item.landingURL.includes('brands')) {
      const categoryId = getCategoryIdFromUrl(item.landingURL);
      InteractionManager.runAfterInteractions(() => {
        if (categoryId) {
          navigation?.navigate('CategoryDetail', { categoryId });
        } else {
          resolveUrl({ urlKey:  item.landingURL, navigation })
        }
      });
    } else if (item.landingURL) {
      if (item.landingURL === '/' || item.landingURL === '//') {
        navigation?.navigate('Tab', {screen: 'Shop'});
      } else {
        InteractionManager.runAfterInteractions(() => {
          const urlKey = item.landingURL;
          resolveUrl({urlKey, navigation});
        });
      }
    } else {
      InteractionManager.runAfterInteractions(() => {
        navigation?.navigate('Tab', {screen: 'Shop'});
      });
    }
  };
  
  // Separate function to handle API call
  const getSaleDetails = async (id: string) => {
    if (!id) return;
    try {
      setIsLoading(true);
      const response = await fetchSaleDetails(id);
      setSaleDetails(response?.data);
      if(response?.data?.statusCode === 404){
        navigation?.navigate('Tab', {screen: 'Shop'});
      }
    } catch (err) {
      showErrorMessage('Error fetching sale details:'+ err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Call the function when component mounts or saleId changes
  useEffect(() => {
    getSaleDetails(saleId);
  }, [saleId]);

  // Render sale details
  const renderSaleDetails = () => {
    if (isLoading) {
      return (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    }

    if (!saleDetails) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {saleId ? t('sales.noDetailsFound') : t('sales.noSaleSelected')}
          </Text>
        </View>
      );
    }

    const checkValidity = (startDate: string, expiryDate: string) => {
      let startTime = new Date(startDate).getTime();
      let endTime = new Date(expiryDate).getTime();
      let currentTime = new Date().getTime() - new Date().getTimezoneOffset() * 60 * 1000;
      return currentTime > startTime && currentTime < endTime;
    };

    if (saleDetails?.startDate && saleDetails?.expiryDate) {
      const validity = checkValidity(saleDetails?.startDate, saleDetails?.expiryDate);
      if (!validity) {
        // Navigate home if sale is no longer valid
        navigation?.navigate('Tab', {screen: 'Shop'});
        return null;
      }
    }
    // Render the template data similar to your old component
    return (
        <FlatList
          data={saleDetails?.rows || []} 
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => {
            let totalWidth = 0;
            item.column.map(cn => (totalWidth = totalWidth + Number(cn.width)));
            
            return (
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <FlatList
                  data={item.column}
                  renderItem={({ item }) => {
                    if (item.landingURL) {
                      return (
                        <TouchableOpacity 
                        activeOpacity={1}
                        onPress={() => handleNavigation(item)}
                        >
                          <View>
                            <FastImage
                              source={{ uri: item.mimgURL }}
                              style={{
                                width: (Number(item.width) / totalWidth) * Width,
                                height:
                                  ((Number(item.width) / totalWidth) *
                                    Width *
                                    Number(item.height)) /
                                  Number(item.width),
                              }}
                              resizeMode='cover'
                            />
                          </View>
                        </TouchableOpacity>
                      );
                    } else {
                      return (
                        <View>
                          <FastImage
                            source={{ uri: item.mimgURL }}
                            style={{
                              width: (Number(item.width) / totalWidth) * Width,
                              height:
                                ((Number(item.width) / totalWidth) *
                                  Width *
                                  Number(item.height)) /
                                Number(item.width),
                            }}
                            resizeMode='cover'
                          />
                        </View>
                      );
                    }
                  }}
                  keyExtractor={(item, index) => `column-${item.mimgURL || index}`}
                  horizontal={true}
                />
              </View>
            );
          }}
          keyExtractor={(item, index) => `row-${item.row_id || index}`}
          initialNumToRender={8}
        />
    );
  };

  return (
    <SafeAreaView style={[styles.screenContainer, {paddingTop: insets.top}]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header navigation={navigation} backButton text={saleDetails?.templateName} searchIcon={true}
          bagIcon={true}/>
      </ErrorHandler>
        {renderSaleDetails()}
    </SafeAreaView>
  );
};

export default Sales;
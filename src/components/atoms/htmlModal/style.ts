import {Sizes, Fonts} from 'common';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    imageModelMain: {
      backgroundColor: colors.blueLagoon,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
      marginBottom: Platform.OS == 'ios' ? Sizes.xl : Sizes.x,
    },
    subImageView: {
      backgroundColor: colors.whiteColor,
      width: '100%',
      marginTop: '40%',
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x6l,
    },
    pageTitleView: {
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.sx,
    },
    readMoreDataView: {
      paddingHorizontal: Sizes.l,
      paddingBottom: Sizes.xxl,
      paddingRight: Sizes.xl,
    },
    descText: {
      color: colors.text,
      fontFamily: Fonts.Regular,
    },
    htmlRenderStyle: {
      p: {color: colors.text2, fontFamily: Fonts.Medium, margin: 0, padding: 0},
      li: {color: colors.text2, fontFamily: Fonts.Medium},
      ul: {color: colors.text2, fontFamily: Fonts.Medium},
      div: {color: colors.text2, fontFamily: Fonts.Medium},
      body: {
        color: colors.text2,
        whiteSpace: 'normal',
        fontFamily: Fonts.Medium,
      },
      h1: {color: colors.text2, fontFamily: Fonts.Medium},
      span: {color: colors.text2, fontFamily: Fonts.Medium},
      strong: {color: colors.text2, fontFamily: Fonts.SemiBold},
    },
  });

export default styles;

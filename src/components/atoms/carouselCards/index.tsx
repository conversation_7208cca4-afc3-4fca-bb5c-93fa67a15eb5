import React, {useRef, useState} from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {ViewProps, ViewStyle} from 'react-native';
import {CarouselPagination} from 'components/atoms';
import {Sizes} from 'common';
import Carousel from 'react-native-snap-carousel';

type Props = {
  pagination?: boolean;
  fullscreen?: boolean;
  renderItem: ({item, index}: {item: any; index: number}) => React.ReactElement;
  data: any[];
  endDot?: boolean;
  currentIndex?: number;
  carouselRef?: React.MutableRefObject<null>;
  onSnapToItem?: (index: any) => void;
  dotStyle?: ViewProps['style'];
  dotsLength?: number;
  style?: ViewStyle;
  onPress?: () => void;
};
const CarouselCards = ({
  pagination,
  renderItem,
  fullscreen = true,
  data,
  style,
  dotStyle,
  currentIndex,
  carouselRef,
  onSnapToItem,
  endDot,
  dotsLength = 3,
}: Props) => {
  const [index, setIndex] = useState(currentIndex || 0);
  const isCarousel = useRef(null);
  // eslint-disable-next-line no-shadow
  const onChangeIndex = (index: number) => {
    if (onSnapToItem) {
      onSnapToItem(index);
    }
    setIndex(index);
  };

  return (
    <>
      <Carousel
        layout={'default'}
        layoutCardOffset={9}
        loop={false}
        autoplay={false}
        enableSnap
        ref={carouselRef || isCarousel}
        data={data}
        renderItem={renderItem}
        sliderWidth={Sizes.screenWidth}
        itemWidth={
          fullscreen ? Sizes.screenWidth : Sizes.screenWidth - Sizes.xl * 3
        }
        onSnapToItem={onChangeIndex}
        useScrollView={true}
      />
      {pagination === true ? (
        <CarouselPagination
          endDot={endDot}
          style={style}
          index={index}
          dotsLength={dotsLength}
          dotStyle={dotStyle}
        />
      ) : null}
    </>
  );
};

export default CarouselCards;

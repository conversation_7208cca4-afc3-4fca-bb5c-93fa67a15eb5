import Icons from 'common/icons';
import FastImage from 'react-native-fast-image';
import { AnalyticsEvents } from 'components/organisms';
import { useSelector } from 'react-redux';
import { RootState } from '@types/local';
import { View, Platform, AppState } from 'react-native';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useNavigation, useTheme } from '@react-navigation/native';
import stylesWithOutColor from './style';
import { useDispatch } from 'react-redux';
import {
  getUserInfo,
  setIsLoggedIn,
  initializeAppData,
} from 'app-redux-store/slice/appSlice';
import localStorage from 'utils/localStorage';
import tokenClass from 'utils/token';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamsList } from 'routes';
import appsFlyer from 'react-native-appsflyer';
import { debugError, debugLog } from 'utils/debugLog';
import { resolveUrl } from 'utils/resolveUrl';
import messaging from '@react-native-firebase/messaging';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

function SplashScreen() {
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const navigation = useNavigation()
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);

  const setInitialScreen = useCallback(async () => {
    const isFirstTime = await localStorage.get('firstTime');
    const isLoggedIn = await tokenClass.loginStatus();
    dispatch(setIsLoggedIn(isLoggedIn));
    const appState = AppState.currentState;
    debugLog("AppState on mount:", appState);
    if(appState === 'background') {
      return;
    }
    if (isLoggedIn) {
      dispatch(getUserInfo());
    }
    if (isLoggedIn || isFirstTime) {
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Tab',
            state: {
              routes: [
                {
                  name: 'Shop',
                },
              ],
            },
          },
        ],
      });
    } else {
      localStorage.set('firstTime', 'installed great!');
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Tab',
            state: {
              routes: [
                {
                  name: 'Shop',
                },
              ],
            },
          },
        ],
      });
    }
    dispatch(initializeAppData());
  }, [dispatch, navigation]);

  const handleInitialScreen = useCallback(
    async (res: any) => {
      debugLog('handleInitialScreen--------->', res);
      let urlKey: any;
      if (Platform.OS === 'ios' && res?.data?.urlKey) {
        const fullUrl = res.data.link;
        debugLog('Full URL:', fullUrl);
        urlKey = res?.data?.urlKey;
        debugLog('Extracted Pathname:', urlKey);
        if (urlKey) {
          return setTimeout(async () => {
            setInitialScreen();
            await resolveUrl({ urlKey, navigation })
            return true;
          }, 100);

        }
      } else if (res?.data?.host === 'www.dentalkart.com' || res?.data?.link?.startsWith('https://www.dentalkart.com')|| res?.data?.link?.startsWith('http://www.dentalkart.com')
      ) {
        if (res?.data?.path) {
          urlKey = res?.data?.path;
          return setTimeout(async () => {
            const referralCode = res?.data?.referralCode;
            const referType = res?.data?.referType;
            const productSku = res?.data?.productSku;
            const link = res?.data?.link;
            debugLog('link****', link)
            setInitialScreen();
            await resolveUrl({ urlKey, referralCode, referType, productSku, navigation, link })
            return true;
          }, 100);
        }
        else {
          const extractPath = (url: string): string => {
            const match = url.match(/^https?:\/\/[^/]+(\/[^?]+)/);
            return match ? match[1] : '';
          };

          const path = extractPath(res?.data?.link);
          debugLog('path*****', path);
          if (!path) {
            setInitialScreen();
            return navigation?.navigate('Tab', { screen: 'Shop' });

          }
          return setTimeout(async () => {
            const referralCode = res?.data?.referralCode;
            const referType = res?.data?.referType;
            const productSku = res?.data?.productSku;
            const link = res?.data?.link;
            setInitialScreen();
            await resolveUrl({ urlKey: path, referralCode, referType, productSku, navigation, link })
            return true;
          }, 100);
        }
      } else {
        urlKey = res?.data?.urlKey;
        if (!!res?.data?.deep_link_value) {
          setInitialScreen();
          if (res?.data?.deep_link_value === 'shorts') {
            const videoId = res?.data?.videoId;
            if (Platform.OS === 'ios') {
              return navigation.navigate('Tab', { screen: 'Shop' })
            }
            return navigation.navigate('ShortVideos', {
              videoId: videoId,
            });
          } else if (res?.data?.deep_link_value === 'cart') {
            return navigation.navigate('Cart');
          } else if (res?.data?.deep_link_value === 'products') {
            return setTimeout(async () => {
              urlKey = '/' + urlKey;
              const referralCode = res?.data?.referralCode;
              const referType = res?.data?.referType;
              await resolveUrl({ urlKey, referralCode, referType, navigation })
              return true;
            }, 100);
          } else if (res?.data?.deep_link_value === 'profile') {
            return navigation.navigate('ProfileDetails');
          } else if (res?.data?.deep_link_value === 'order-list') {
            if (isLoggedIn) {
              return navigation.navigate('OrderList');
            } else {
              return navigation.navigate('Login', {
                nextScreenNameLink: 'OrderList',
                nextScreenParams: {},
              });
            }
          } else if (res?.data?.deep_link_value === 'sale') {
            setTimeout(() => {
              const id = urlKey.split('/').pop();
              return navigation.navigate('Sales', { saleId: id })
            }, 500);
          } else if (res?.data?.deep_link_value === 'home') {
            return navigation.navigate('HomePage');
          }
        }
      }

      if (!!urlKey) {
        setInitialScreen();
        await resolveUrl({ urlKey, navigation })
        return true;
      }
      return setInitialScreen();
    },
    [navigation, setInitialScreen],
  );

  const handleNotificationNavigation = useCallback(
    async (res: any) => {
          const urlKey = res?.url
          resolveUrl({ urlKey,navigation })
      },
    [navigation, setInitialScreen],
  ); 

  useEffect(() => {
    AnalyticsEvents('SPLASH_SCREEN', 'Splash screen', {}, userInfo, isLoggedIn);
    global.showRate = false;
  }, [])

  useEffect(() => {
    let isDeeplink = false;
    let isScreenSet = false;
    let deepLinkListener: { (): void; remove?: any };
    const timeoutId = setTimeout(() => {
      const initAppsFlyer = async () => {
        try {
          // Initialize SDK
          appsFlyer.initSdk(
            {
              devKey: '56HVuQCdooe9EBMnNkzPnC',
              isDebug: __DEV__,
              appId: Platform.OS === 'android' ? 'com.vasadental.dentalkart' : '1382207992',
              onDeepLinkListener: true,
            },
            result => debugLog('appsFlyer init result:', result),
            error => debugError('appsFlyer init error:', error)
          );
          appsFlyer.startSdk();

          // Listen to deep links
          deepLinkListener = appsFlyer.onDeepLink(async res => {
            debugLog('appsFlyer.onDeepLink:', res);
            const isValidDeepLink = res?.deepLinkStatus !== 'NOT_FOUND' && res?.status !== 'failure';
            if (isValidDeepLink) {
              isDeeplink = true;
              await handleInitialScreen(res);
            } else if (!isScreenSet) {
              isScreenSet = true;
              setInitialScreen();
            }
          });          
          // Handle taps on notifications
          messaging().onNotificationOpenedApp(remoteMessage => {
            debugLog('Notification opened from background state:', remoteMessage);
            isDeeplink = true;
            handleNotificationNavigation(remoteMessage?.data);
          });
          
          messaging()
            .getInitialNotification()
            .then(remoteMessage => {
              if (remoteMessage) {
                debugLog('Notification opened from quit state:', remoteMessage);
                isDeeplink = true;
                handleNotificationNavigation(remoteMessage?.data);
              }
            });

          setTimeout(() => {
            if (!isDeeplink && !isScreenSet) {
              isScreenSet = true;
              setInitialScreen();
            }
          }, 1000)
        } catch (error) {
          debugError('AppsFlyer init error:', error);
          if (!isScreenSet) {
            isScreenSet = true;
            setInitialScreen();
          }
        }
      };
      initAppsFlyer();
    }, 1000);

    return () => {
      clearTimeout(timeoutId); // clean up the timer
      deepLinkListener?.remove?.(); // clean up the listener
    };
  }, []);

  return (
    <View style={styles.container}>
      <FastImage
        style={styles.image}
        resizeMode="contain"
        source={Icons.splashAnimated}
      />
    </View>
  );
}
export default SplashScreen;

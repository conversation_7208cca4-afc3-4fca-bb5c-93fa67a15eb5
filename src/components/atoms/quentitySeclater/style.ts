import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      height: Sizes.x34,
      // width: Sizes.ex112,
    },
    decrementBtn: {
      paddingHorizontal: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      borderTopLeftRadius: Sizes.s,
      borderBottomLeftRadius: Sizes.s,
    },
    inputBox: {
      textAlign: 'center',
      justifyContent: 'center',
      width: Sizes.x7l,
      height: Sizes.x34,
      color: colors.categoryTitle,
      fontSize: Sizes.mx,
      padding: 0,
      margin: 0,
    },
    incrementBtn: {
      paddingHorizontal: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      borderTopRightRadius: Sizes.s,
      borderBottomRightRadius: Sizes.s,
    },
    onUpdateView: {
      flexDirection: 'row',
      backgroundColor: colors.categoryTitle,
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
    },
    onUpdateSubView: {
      backgroundColor: colors.categoryTitle,
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.sx,
      justifyContent: 'center',
    },
    valueView: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.s,
      alignItems: 'center',
      justifyContent: 'center',
      width: Sizes.x5l,
    },
    plusView: {
      backgroundColor: colors.categoryTitle,
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.sx,
      justifyContent: 'center',
    },
    disableBG: {
      backgroundColor: colors.grey2,
    },
  });

export default styles;

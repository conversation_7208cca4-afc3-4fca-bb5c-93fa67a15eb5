package com.vasadental.dentalkart;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.Intent;
import android.content.IntentSender;

import androidx.annotation.NonNull;
import com.google.android.gms.common.api.ApiException;
import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.google.android.gms.auth.api.identity.GetPhoneNumberHintIntentRequest;
import com.google.android.gms.auth.api.identity.Identity;
import com.google.android.gms.auth.api.identity.SignInClient;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;

public class HintPickerModule extends ReactContextBaseJavaModule implements ActivityEventListener {

    private static final int HINT_PICKER_REQUEST = 1001;
    private Promise pickerPromise;
    private SignInClient signInClient;

    public HintPickerModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(this);
        signInClient = Identity.getSignInClient(reactContext);
    }

    @NonNull
    @Override
    public String getName() {
        return "HintPicker";
    }

    @ReactMethod
    public void showHintPicker(Promise promise) {
        Activity currentActivity = getCurrentActivity();
        if (currentActivity == null) {
            promise.reject("NO_ACTIVITY", "No activity attached");
            return;
        }

        pickerPromise = promise;

        GetPhoneNumberHintIntentRequest request = GetPhoneNumberHintIntentRequest.builder().build();

        Task<PendingIntent> pendingIntentTask = signInClient.getPhoneNumberHintIntent(request);

        pendingIntentTask.addOnSuccessListener(currentActivity, pendingIntent -> {
            try {
                currentActivity.startIntentSenderForResult(
                        pendingIntent.getIntentSender(),
                        HINT_PICKER_REQUEST,
                        null,
                        0,
                        0,
                        0,
                        null
                );
            } catch (IntentSender.SendIntentException e) {
                pickerPromise.reject("INTENT_FAILED", "Failed to show hint picker");
                pickerPromise = null;
            }
        }).addOnFailureListener(currentActivity, e -> {
            pickerPromise.reject("INTENT_FAILED", "Failed to show hint picker: " + e.getMessage());
            pickerPromise = null;
        });
    }

    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (requestCode == HINT_PICKER_REQUEST && pickerPromise != null) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                try {
                    String phoneNumber = Identity.getSignInClient(activity)
                            .getPhoneNumberFromIntent(data);
                    pickerPromise.resolve(phoneNumber);
                } catch (ApiException e) {
                    pickerPromise.reject("API_EXCEPTION", "Failed to get phone number: " + e.getMessage());
                }
            } else {
                pickerPromise.resolve(null);
            }
            pickerPromise = null;
        }
    }

    @Override
    public void onNewIntent(Intent intent) {}
}

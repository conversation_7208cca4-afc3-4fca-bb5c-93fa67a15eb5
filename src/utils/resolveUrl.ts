import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamsList } from '../routes';
import localStorage from './localStorage';
import { urlResolver } from 'services/home';
import { debugError, debugLog } from './debugLog';
import { Linking, Platform } from 'react-native';
import { news } from 'api';
import { openInBrowser } from './utils';
import tokenClass from './token';

type ResolveUrlParams = {
  urlKey: string;
  referralCode?: string;
  referType?: string;
  productSku?: string;
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  link?: string;
};

export const resolveUrl = async ({
  urlKey,
  referralCode,
  referType,
  productSku,
  navigation,
  link,
}: ResolveUrlParams) => {
  try {
    // Common function to handle authenticated navigation
    const navigateWithAuth = (targetScreen, params = {}) => {
      if (isLoggedIn) {
        return navigation.navigate(targetScreen, params);
      } else {
        return navigation.navigate('Login', {
          nextScreenNameLink: targetScreen,
          nextScreenParams: params,
        });
      }
    }
    const urlCacheResponse = await localStorage.get(urlKey);
    let urlData;
    const isLoggedIn = await tokenClass.loginStatus();

    if (urlCacheResponse) {
      urlData = JSON.parse(urlCacheResponse);
    } else {
      const response: any = await urlResolver(urlKey);
      const { status, data } = response;
      if (status && data?.status !== 404) {
        localStorage.set(urlKey, JSON.stringify(data));
        urlData = data;
      }
    }

    debugLog("urlData", urlData)

    if (urlData) {
      if (urlData.type === 'CATEGORY') {
        navigation.push('CategoryDetail', {
          categoryId: urlData.id,
        });
      } else if (urlData.type === 'PRODUCT') {
        navigation.push('ProductDetail', {
          productId: urlData.id,
          referralCode,
          referType,
          productSku,
        });
      }
    } else {
      if(urlKey === '/'){
        return navigation.navigate('Tab', { screen: 'Shop' })
      }
      else if(urlKey === '/brands'){
        return navigation.navigate('Tab', { screen: 'Brands' })
      } 
      else if(urlKey === '/categories'){
        return navigation.navigate('Categories')
      }
      else if(urlKey === '/membership'){
          return navigateWithAuth('Membership')
      }
      else if(urlKey.includes('/sale')){
        const id = urlKey.split('/').pop();
        return navigation.navigate('Sales', { saleId: id })
      }
      else  if (urlKey.startsWith('/account') && urlKey.includes('wishlist') && urlKey.includes('friend')) {
        const id = link.split('=')[1]
        if(isLoggedIn){
          return navigation.navigate('WishList',{pages: 'Friend', id: id})
        }else{
          const prevRoutes = navigation.getState().routes;
          return navigation.navigate('Login', {
            nextRouterState: {
              index: 0,
              routes: prevRoutes,
            },
          });
        }
      }
      else if (urlKey.startsWith('/account')) {
        if (urlKey.includes('Login')) {
          return navigation.navigate('HelpOrder', { item: 'Login & My Account' });
        } else if (urlKey.includes('profile')) {
          return navigateWithAuth('ProfileDetails');
        }else if (urlKey.includes('manage-address')) {
          return navigateWithAuth('AddressList')
        }
        else if (urlKey.includes('reward-history')) {
          return navigateWithAuth('MyRewords')
        }else if (urlKey.includes('reward-zone')) {
          return navigateWithAuth('RewardZoneScene')
        }else if (urlKey.includes('wishlist')) {
          return navigateWithAuth('WishList')
        }else if (urlKey.includes('my-order-list')) {
          return navigateWithAuth('OrderList')
        }
        else if (urlKey.includes('return')) {
          return navigateWithAuth('OrderList', {pageType: 'Returns'})
        }
        else if (urlKey.includes('coupons')) {
          return navigateWithAuth('CouponsScene')
        }
        else if (urlKey.includes('my-membership')) {
          return navigateWithAuth('MembershipPage')
        } 
        else if (urlKey.includes('refer-and-earn')) {
          return navigateWithAuth('MyReferral')
        }
        else if (urlKey.includes('help')) {
          return navigation.navigate('HelpCenter');
        }
      } 
      else if (urlKey.startsWith('/help-center')) {
        if (urlKey.includes('Login')) {
          return navigation.navigate('HelpOrder', { item: 'Login & My Account' });
        } else if (urlKey.includes('Membership')) {
          return navigation.navigate('HelpOrder', { item: 'Membership' });
        }else if (urlKey.includes('Delivery')) {
          return navigation.navigate('HelpOrder', { item: 'Delivery' });
        }
        else if (urlKey.includes('Payment')) {
          return navigation.navigate('HelpOrder', { item: 'Payments' });
        } 
        else if (urlKey.includes('Returns')) {
          return navigation.navigate('HelpOrder', { item: 'Returns' });
        }
        else if (urlKey.includes('Rewards')) {
          return navigation.navigate('HelpOrder', { item: 'Rewards' });
        }
        else if (urlKey.includes('Tracking')) {
          return navigation.navigate('HelpOrder', { item: 'Tracking' });
        } 
        else if (urlKey.includes('Refunds')) {
          return navigation.navigate('HelpOrder', { item: 'Refunds' });
        }
        else if (urlKey.includes('Product')) {
          return navigation.navigate('HelpOrder', { item: 'Product Issues' });
        }
        else if (urlKey.includes('Orders')) {
          return navigation.navigate('HelpOrder', { item: 'Orders' });
        }
         else {
          return navigation.navigate('HelpCenter');
        }
      }
      else if (urlKey === '/shorts') {
        if (Platform.OS === 'ios') {
          return navigation.navigate('Tab', { screen: 'Shop' })
        }
        return navigation.navigate('Tab', { screen: 'Shorts' })
      } else if (urlKey === '/cart') {
        return navigateWithAuth('Cart')
      } else if (urlKey.includes('/checkout') ) {
        return navigateWithAuth('Cart')
      }else if (urlKey.startsWith('/track-page')) {
        const id = urlKey.split('/track-page/')[1]
        return navigateWithAuth('OrderDetail',  { order_id: id })
      }
      else {
        console.log(link, 'linkss***');
        openInBrowser(link)
      }
    }
  } catch (error) {
    debugError('Error resolving URL:', error)
    navigation.push('ItemNotFound');
  }
};

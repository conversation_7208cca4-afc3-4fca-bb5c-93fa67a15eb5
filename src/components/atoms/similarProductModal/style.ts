import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
      backgroundColor: colors.modalShadow,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    viewProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
    },
    offerZoneBannerView: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
    },
    excludeView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    fRow:{
      flexDirection: 'row',
    },
    productView: {
      paddingVertical: Sizes.xm,
    },
    cardList: {
      height: 470,
    },
  });

export default styles;

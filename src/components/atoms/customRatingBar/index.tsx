import React, {useMemo} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {ImageIcon} from 'components/atoms';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';

const CustomRatingBar = props => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {maxRating, selectRate, onPress, style, startStyle} = props;
  return (
    <View style={[styles.customRatingBarStyle, style]}>
      {maxRating.map((item, key) => {
        return (
          <TouchableOpacity
            activeOpacity={0.7}
            key={key}
            onPress={() => onPress(item)}>
            <ImageIcon
              size="xl"
              style={[styles.starImageStyle, startStyle]}
              tintColor="green2"
              icon={item <= selectRate ? 'starFillGreen' : 'starOutlineGreen'}
            />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default CustomRatingBar;

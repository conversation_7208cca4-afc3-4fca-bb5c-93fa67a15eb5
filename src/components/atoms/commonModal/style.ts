import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalAccordion: {
      paddingTop: Sizes.l,
      paddingHorizontal: Sizes.l,
    },
    modalHeader: {
      paddingTop: Sizes.xxl,
      paddingHorizontal: Sizes.xxl,
    },
    modalTextContainer: {
      paddingBottom: Sizes.xxl,
      paddingHorizontal: Sizes.xxl,
    },
    accordionView: {
      borderTopColor: colors.grey5,
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.xm,
    },
    rightSideView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    numList: {
      flexDirection: 'row',
      paddingVertical: Sizes.xs,
    },
    paddingH16: {
      paddingVertical: Sizes.l,
    },
    renderList: {
      paddingLeft: Sizes.xm,
      paddingBottom: Sizes.xl,
    },
  });

export default styles;

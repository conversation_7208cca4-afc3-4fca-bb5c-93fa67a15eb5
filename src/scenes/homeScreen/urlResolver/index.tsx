import React, { useCallback, useEffect, useState } from 'react';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp, useTheme } from '@react-navigation/native';
import { ActivityIndicator, View } from 'react-native';
import { RootStackParamsList } from '../../../routes';
import localStorage from 'utils/localStorage';
import stylesWithOutColor from './style';
import { urlResolver } from 'services/home';
import { useMemo } from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'UrlResolver'>;
};

// -------- Sliders and  banners api ---------------------------
const UrlResolverScene = ({ route, navigation }: Props) => {
  const { urlKey, referralCode, referType, productSku } = route.params;
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const [loading, setLoading] = useState(true);

  const getPageType = useCallback(async () => {
    setLoading(true);
    const urlCacheResponse = await localStorage.get(urlKey);

    let urlData;
    if (urlCacheResponse) {
      urlData = JSON.parse(urlCacheResponse);
    } else {
      const response: any = await urlResolver(urlKey);
      const { status, data } = response;
      if (status && data?.status !== 404) {
        localStorage.set(urlKey, JSON.stringify(data));
        urlData = data;
      }
    }

    return urlData;
  }, [urlKey]);

  useEffect(() => {
    (async () => {
      const urlData = await getPageType();
      const prevRoutes = navigation.getState().routes;

      setTimeout(() => {
        if (!!urlData) {
          if (urlData?.type === 'CATEGORY') {
            navigation.reset({
              index: 0,
              routes: [
                ...prevRoutes.slice(0, prevRoutes.length - 1),
                {
                  name: 'CategoryDetail',
                  params: { categoryId: urlData?.id },
                },
              ],
            });
          } else {
            navigation.reset({
              index: 0,
              routes: [
                ...prevRoutes.slice(0, prevRoutes.length - 1),
                {
                  name: 'ProductDetail',
                  params: {
                    productId: urlData?.id,
                    referralCode,
                    referType,
                    productSku,
                  },
                },
              ],
            });
          }
        } else {
          navigation.reset({
            index: 0,
            routes: [
              ...prevRoutes.slice(0, prevRoutes.length - 1),
              {
                name: 'ItemNotFound',
              },
            ],
          });
        }
        setLoading(false);
      }, 1500);
    })();
  }, []);

  return (
    <View style={styles.loader}>
      <ActivityIndicator animating={loading} color={colors.primary} size="large" />
    </View>
  );
};

export default UrlResolverScene;
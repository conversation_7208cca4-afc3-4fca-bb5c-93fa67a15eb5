import React from 'react';
import {
  View,
  TouchableOpacity,
  Alert,
  Text,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Dropdown} from 'react-native-element-dropdown';
import Modal from 'react-native-modal';
import {t} from 'i18next';
import {Button, PhoneInputText} from 'components/molecules';
import {
  ImageIcon,
  Label,
  FastImagesItem,
  Spacer,
  Quantity,
} from 'components/atoms';
import stylesWithOutColor from './style';
import {ImagePath} from 'config/apiEndpoint';
import {isVideoUrl} from 'utils/utils';
import {useMemo} from 'react';
import Config from 'react-native-config';

type Props = {
  visible?: boolean;
  onClose?: () => void;
  selectedItemForReturn?: OrderItems;
  currentReturnForm?: ItemsInput;
  returnReasons: ReturnReasons[];
  error?: ReasonError;
  subReasons?: SubReason[];
  action?: ReturnAction[];
  onSaveReturnForm?: () => void;
  removeDocument?: (i: number) => void;
  handleDocument?: () => void;
  requestCameraPermission?: () => void;
  onUpdateQty?: (count: number, change: boolean) => void;
  onChangeReasons?: (item: ReturnReasons) => void;
  onChangeSubReasons?: (item: SubReason) => void;
  onChangeAction?: (item: ReturnAction) => void;
  onClearForm?: () => void;
  onChangeDescription?: () => void;
  fileError?: string;
};

const OrderReturnDetailModal = (props: Props) => {
  const {
    visible,
    onClose,
    selectedItemForReturn,
    currentReturnForm,
    returnReasons,
    error,
    subReasons,
    action,
    onSaveReturnForm,
    removeDocument,
    handleDocument,
    requestCameraPermission,
    onUpdateQty,
    onChangeReasons,
    onChangeSubReasons,
    onChangeAction,
    onClearForm,
    onChangeDescription,
    fileError,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const onErrorView = (error: string) => {
    return (
      <>
        <Spacer size="sx" />
        <Label
          text={String(t(error) || '')}
          size="m"
          weight="400"
          color="textError"
        />
      </>
    );
  };

  const renderDropDown = (text: string) => {
    return (
      <View style={styles.reasonView}>
        <Label text={text} size="m" weight="400" color="categoryTitle" />
      </View>
    );
  };

  const dropdown1Disable =
    (selectedItemForReturn.is_tat_expired &&
      selectedItemForReturn.max_qty_returnable > 0) ||
    selectedItemForReturn?.non_returnable ||
    selectedItemForReturn.is_free_product
      ? true
      : false;

  const dropdown2Disable =
    selectedItemForReturn?.non_returnable ||
    (subReasons.length === 0 && currentReturnForm.reason_id);

  const dropdown3Disable =
    dropdown1Disable || (action.length === 0 && currentReturnForm.reason_id);

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <View style={styles.modalView}>
          <View style={styles.subContainer}>
            <TouchableWithoutFeedback onPress={() => onClose()}>
              <View style={styles.modalCloseView}>
                <TouchableOpacity
                  onPress={() => onClose()}
                  style={styles.modalCloseButton}>
                  <ImageIcon icon="close" size="xx4l" />
                </TouchableOpacity>
              </View>
            </TouchableWithoutFeedback>
            <Spacer size="xms" />
            <Label
              text={t('orderReturn.fillDetails')}
              size="mx"
              weight="500"
              color="text"
            />
            <View style={styles.imageSubView}>
              <View style={styles.image}>
                <ImageIcon
                  resizeMode="contain"
                  style={styles.imageSize}
                  sourceType="url"
                  source={ImagePath.product + selectedItemForReturn?.image}
                />
              </View>
              <Spacer size="xms" type="Horizontal" />
              <View style={styles.nameSubView}>
                <Label
                  text={selectedItemForReturn?.name}
                  size="m"
                  fontFamily="Medium"
                  color="text"
                  numberOfLines={2}
                  ellipsizeMode="tail"
                />
                <View style={styles.qtyView}>
                  <Label
                    text={`${t('orderReturn.totalQty')} - ${
                      selectedItemForReturn?.delivered_qty
                    }`}
                    size="m"
                    fontFamily="Medium"
                    color="categoryTitle"
                  />
                  <View style={styles.lineStyle} />
                  <Label
                    text={`${t('orderReturn.returnableQty')} - ${
                      selectedItemForReturn?.max_qty_returnable
                    }`}
                    size="m"
                    fontFamily="Medium"
                    color="categoryTitle"
                  />
                </View>
              </View>
            </View>
            <Spacer size="m" />
            <View style={styles.returnView}>
              <View>
                <Label
                  text={t('orderReturn.returnQuantity')}
                  size="m"
                  fontFamily="Medium"
                  color="text"
                />
                <Spacer size="s" />
                <Quantity
                  min={1}
                  max={selectedItemForReturn?.max_qty_returnable}
                  value={currentReturnForm?.qty || 1}
                  disabled={selectedItemForReturn?.non_returnable === 0}
                  onUpdate={(count, change) => onUpdateQty(count, change)}
                  disabledLeft={currentReturnForm?.qty === 1}
                  disabledRight={
                    currentReturnForm?.qty ===
                    selectedItemForReturn?.max_qty_returnable
                  }
                />
              </View>
              <Spacer size="xxl" type="Horizontal" />
              <View style={styles.selectReasonView}>
                <Label
                  text={t('orderReturn.selectReason')}
                  size="m"
                  fontFamily="Medium"
                  color="text"
                />
                <Spacer size="s" />
                <Dropdown
                  iconStyle={styles.dropDownIcon}
                  itemTextStyle={styles.dropDownTextStyle}
                  renderItem={item => renderDropDown(item.reason)}
                  selectedTextStyle={[
                    styles.dropDownSelected,
                    dropdown1Disable && {
                      color: colors.whiteColor,
                    },
                  ]}
                  disable={dropdown1Disable}
                  style={[
                    styles.dropdown,
                    dropdown1Disable && {
                      backgroundColor: colors.grey2,
                    },
                  ]}
                  renderRightIcon={() => (
                    <ImageIcon
                      size="l"
                      tintColor={dropdown1Disable ? 'whiteColor' : 'text'}
                      icon="downArrow1"
                      resizeMode="contain"
                    />
                  )}
                  placeholderStyle={[
                    styles.placeholderStyle,
                    {
                      color: dropdown1Disable ? colors.whiteColor : colors.text2,
                    },
                  ]}
                  data={returnReasons}
                  value={returnReasons.find(
                    (e: any) => e?.id === currentReturnForm.reason_id,
                  )}
                  maxHeight={300}
                  labelField="reason"
                  valueField="reason"
                  placeholder={t('otherText.reason')}
                  onChange={item => onChangeReasons(item)}
                />
                {error?.reason_id && onErrorView(error?.reason_id)}
              </View>
            </View>
            <Spacer size="xm" />
            <View>
              <Label
                text={t('orderReturn.selectSubReason')}
                size="m"
                fontFamily="Medium"
                color="text"
              />
              <Spacer size="s" />
              <Dropdown
                iconStyle={styles.dropDownIcon}
                itemTextStyle={styles.dropDownTextStyle}
                renderItem={item => renderDropDown(item.reason)}
                selectedTextStyle={[
                  styles.dropDownSelected,
                  dropdown2Disable && {
                    color: colors.whiteColor,
                  },
                ]}
                disable={dropdown2Disable}
                style={[
                  styles.dropdown,
                  dropdown2Disable && {
                    backgroundColor: colors.grey2,
                  },
                ]}
                placeholderStyle={[
                  styles.placeholderStyle,
                  {
                    color: dropdown2Disable ? colors.whiteColor : colors.text2,
                  },
                ]}
                maxHeight={300}
                renderRightIcon={() => (
                  <ImageIcon
                    size="l"
                    tintColor={dropdown2Disable ? 'whiteColor' : 'text'}
                    icon="downArrow1"
                    resizeMode="contain"
                  />
                )}
                labelField="reason"
                valueField="reason"
                data={subReasons}
                value={subReasons?.find(
                  (e: any) => e.id === currentReturnForm.sub_reason_id,
                )}
                onChange={item => onChangeSubReasons(item)}
              />
              {error?.sub_reason_id && onErrorView(error?.sub_reason_id)}
            </View>

            <Spacer size="xm" />
            <View style={{width: '50%'}}>
              <Label
                text={t('orderReturn.selectAction')}
                size="m"
                fontFamily="Medium"
                color="text"
              />
              <Spacer size="sx" />
              <Dropdown
                iconStyle={styles.dropDownIcon}
                itemTextStyle={styles.dropDownTextStyle}
                renderItem={item => renderDropDown(item.action)}
                selectedTextStyle={[
                  styles.dropDownSelected,
                  dropdown3Disable && {
                    color: colors.whiteColor,
                  },
                ]}
                style={[
                  styles.dropdown,
                  dropdown3Disable && {
                    backgroundColor: colors.grey2,
                  },
                ]}
                disable={dropdown3Disable}
                placeholderStyle={[
                  styles.placeholderStyle,
                  {
                    color: dropdown3Disable ? colors.whiteColor : colors.text2,
                  },
                ]}
                data={action}
                value={action?.find(
                  (e: any) => e.id === currentReturnForm.action_id,
                )}
                renderRightIcon={() => (
                  <ImageIcon
                    size="l"
                    tintColor={dropdown3Disable ? 'whiteColor' : 'text'}
                    icon="downArrow1"
                    resizeMode="contain"
                  />
                )}
                maxHeight={300}
                labelField="action"
                valueField="action"
                placeholder={'Action'}
                onChange={item => onChangeAction(item)}
              />
              {error?.action_id && onErrorView(error?.action_id)}
            </View>
            <Spacer size="xm" />
            <View>
              <Label
                text={t('orderReturn.attachment')}
                size="m"
                fontFamily="Medium"
                color="text"
              />
              <Spacer size="sx" />
              <View style={styles.attachmentView}>
                {Array.from({length: 3}, (v, i) => i).map(i => {
                  return !!currentReturnForm?.attachments?.[i] ? (
                    <View style={styles.imageContainer} key={i.toString()}>
                      <View
                        style={
                          isVideoUrl(currentReturnForm?.attachments?.[i]) &&
                          styles.imgView
                        }>
                        <FastImagesItem
                          FastImageStyle={
                            isVideoUrl(currentReturnForm?.attachments?.[i])
                              ? styles.imag1
                              : styles.imag
                          }
                          source={{
                            uri: isVideoUrl(currentReturnForm?.attachments?.[i])
                              ? Config.videoThumbnails
                              : currentReturnForm?.attachments?.[i],
                          }}
                        />
                      </View>
                      <TouchableOpacity
                        onPress={() => removeDocument(i)}
                        style={styles.imageContainerClose}>
                        <ImageIcon icon="close" style={styles.closeStyle} />
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <TouchableOpacity
                      key={i.toString()}
                      onPress={() =>
                        Alert.alert(
                          t('orderReturn.attachmentTitle'),
                          t('orderReturn.attachmentDes'),
                          [
                            {
                              text: t('orderReturn.browseFiles'),
                              onPress: () => handleDocument(),
                            },
                            {
                              text: t('orderReturn.useCamera'),
                              onPress: () => requestCameraPermission(),
                            },
                            {
                              text: t('buttons.cancel'),
                              onPress: () => {},
                            },
                          ],
                          {cancelable: false},
                        )
                      }
                      style={[
                        styles.imageContainer,
                        {alignItems: 'center', justifyContent: 'center'},
                      ]}>
                      <Text style={styles.plus}>+</Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
              {error?.attachments && onErrorView(error?.attachments)}
            </View>
            {/* <Spacer size="xm" />
            <View>
              <Label
                text={'Comment, If Any'}
                size="m"
                weight="400"
                color="text"
              />
              <Spacer size="sx" />
              <PhoneInputText
                style={styles.descriptionView}
                placeholder={'Describe your concern'}
                value={currentReturnForm?.description || ''}
                onChangeText={text => onChangeDescription(text)}
              />
            </View>
            <Spacer type="Vertical" size={'mx'} /> */}
            {fileError && onErrorView(fileError)}
            <Spacer size="x4l" />
            <View style={styles.resetView}>
              {/* <Button
                text="Reset"
                style={styles.resetBtn}
                onPress={onClearForm}
                labelSize="l"
                labelColor="whiteColor"
              />
              <Spacer type="Vertical" size={'xl'} /> */}
              <Button
                radius="m"
                disabled={Object.keys(error).length > 0}
                type={Object.keys(error).length > 0 ? 'disabled' : 'secondary'}
                text={t('buttons.save')}
                onPress={onSaveReturnForm}
                labelColor="whiteColor"
                labelSize="mx"
                size="large"
                style={styles.saveBtnView}
                labelStyle={styles.labelBtnStyle}
              />
            </View>
            <Spacer size="x4l" />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default OrderReturnDetailModal;

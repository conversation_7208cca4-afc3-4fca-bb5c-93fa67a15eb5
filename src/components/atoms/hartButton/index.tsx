import React, {useCallback} from 'react';
import {TouchableOpacity, View, ViewProps} from 'react-native';
import stylesWithOutColor from './style';
import ImageIcon from '../imageIcon';
import {useTheme} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {addToWishListThunk} from 'app-redux-store/slice/appSlice';
import {useMemo} from 'react';

type Props = {
  item: any;
  style?: ViewProps['style'];
  wishListStyle?: ViewProps['style'];
  binStyle?: ViewProps['style'];
  bin?: boolean;
  onDelete?: () => void;
  componentStyle?: boolean;
};

const HartButton = ({
  bin = false,
  onDelete,
  wishListStyle,
  binStyle,
  style,
  componentStyle = true,
  item,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  // const wishListData = productId => {
  //   dispatch(addToWishListThunk({productId, wishlistId: ''}));
  // };
  const addToWishListItem = useCallback(
    async productId => {
      dispatch(addToWishListThunk({productId, wishlistId: ''}));
    },
    [dispatch],
  );
  return (
    <View
      style={[
        componentStyle && styles.binHeart,
        bin && styles.binStyle,
        style,
      ]}>
      {bin && (
        <TouchableOpacity testID='tOHartButtonOnDelete' style={binStyle} onPress={onDelete}>
          <ImageIcon icon="binIcon" />
        </TouchableOpacity>
      )}
      <TouchableOpacity
      testID='tOHartButtonAddToWishList'
        style={wishListStyle}
        onPress={() => addToWishListItem(item.product.id)}>
        <ImageIcon icon="heartIcon" />
      </TouchableOpacity>
    </View>
  );
};

export default HartButton;

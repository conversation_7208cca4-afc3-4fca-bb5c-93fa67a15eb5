import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    mainContainer: {
      width: '100%',
      padding: Sizes.m,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
    },
    border: {
      borderBottomWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      padding: Sizes.m,
    },
    labelText: {
      flex: Sizes.x,
      color: colors.text2,
    },
    titleView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    upView: {
      width: Sizes.xl,
      height: Sizes.xl,
      transform: [{rotate: '180deg'}],
    },
    fastImageView: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    answerView: {
      marginTop: Sizes.xms,
      paddingTop: Sizes.xms,
    },
    subContainer: {},
    descText: {
      color: colors.text2,
      fontFamily: Fonts.Regular,
      fontSize: Sizes.m,
    },
    pasteButton: {
      marginLeft: Sizes.s
    },
    htmlRenderStyle: {
      p: {
        color: colors.text2,
        fontSize: Sizes.m,
        fontFamily: Fonts.Medium,
        margin: 0,
        padding: 0,
      },
      li: {
        color: colors.text2,
        fontSize: Sizes.m,
        fontFamily: Fonts.Regular,
      },
      ul: {
        color: colors.text2,
        fontSize: Sizes.m,
        fontFamily: Fonts.Regular,
        margin: 0,
      },
      div: {color: colors.text2, fontSize: Sizes.m, fontFamily: Fonts.Regular},
      body: {
        color: colors.text2,
        fontSize: Sizes.m,
        fontFamily: Fonts.Regular,
      },
      h1: {color: colors.text2, fontSize: Sizes.m, fontFamily: Fonts.Regular},
      span: {
        color: colors.text2,
        fontSize: Sizes.m,
        fontFamily: Fonts.Regular,
        margin: 0,
        padding: 0,
      },
      strong: {
        color: colors.text2,
        fontSize: Sizes.m,
        fontFamily: Fonts.SemiBold,
        margin: 0,
        padding: 0,
      },
      a: {
        color: colors.linkText,
        fontSize: Sizes.m,
        fontFamily: Fonts.Regular,
        margin: 0,
        padding: 0,
      },
    },
  });

export default styles;

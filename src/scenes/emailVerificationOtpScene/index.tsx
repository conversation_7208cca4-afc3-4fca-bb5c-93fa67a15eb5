import React, { useCallback, useState, useMemo, useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamsList } from '../../../routes';
import { AnalyticsEvents, LoginWithOtp } from 'components/organisms';
import stylesWithOutColor from './style';
import tokenClass from 'utils/token';
import { RouteProp, useFocusEffect, useTheme } from '@react-navigation/native';
import OtpScene from 'components/organisms/otpScene';
import { useDispatch } from 'react-redux';
import {
  generateNewCart,
  getReferralLink,
  getUserInfo,
  setIsLoggedIn,
} from 'app-redux-store/slice/appSlice';
import { setLoading } from 'app-redux-store/slice/appSlice';
import { showErrorMessage, showSuccessMessage } from 'utils/show_messages';
import { updateProfile, userLogin, verifyOTP } from 'services/auth';
import ErrorHandler from 'utils/ErrorHandler';
import { View } from 'react-native';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'Login'>;
};

const EmailVerificationOtpScene = ({ navigation, route }: Props) => {
  const TAG = 'Login';
  const [pageType, setPageType] = useState('');
  const [actionType, setActionType] = useState('');
  const [showPassInput, setShowPassInput] = useState(false);
  const [editableAccess, setEditableAccess] = useState(false);
  const [phoneFocus, setPhoneFocus] = useState(false);
  const dispatch = useDispatch();
  const [emailPhoneNumber, setEmailPhoneNumber] = useState('');
  const [apiErrorOtp, setApiErrorOtp] = useState(null);
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isVerify, setIsVerify] = useState(false);
  const [shouldShow, setShouldShow] = useState(true);
  const profileData = route?.params?.values

  const navigateToLastScreen = useCallback(() => {
    let lastScreen = route.params?.nextScreenName;
    let goBack = route.params?.back;
    let nextRouterState = route.params?.nextRouterState;
    if (nextRouterState?.routes === 'ProductDetail') {
      return navigation.navigate('ProductDetail', {
        productId: nextRouterState?.productId,
      });
    }

    try {
      if (goBack) {
        return navigation.goBack();
      } else if (lastScreen) {
        setTimeout(() => {
          return navigation.navigate(
            lastScreen,
            route.params?.nextScreenParams,
          );
        }, 1000);
      } else if (
        nextRouterState &&
        nextRouterState.routes &&
        Array.isArray(nextRouterState.routes)
      ) {
        return navigation.reset({
          index: nextRouterState.index || 0,
          routes: nextRouterState.routes.map(route => ({
            key: route.key,
            name: route.name,
            params: route.params || {},
          })),
        });
      } else {
        return navigation.reset({
          index: 0,
          routes: [{ name: 'Tab', params: { screen: 'Shop' } }],
        });
      }
    } catch (e) {
      return navigation.navigate('Tab', { screen: 'Shop' });
    }
  }, [navigation, route]);

  const verifyOtp = useCallback(
    async (values: string) => {
      dispatch(setLoading(true));
      setEditableAccess(true);
      const { data, status } = await verifyOTP({
        recipient: profileData?.email,
        action: "email_update",
        verification_type: 'otp',
        authentication_type: 'email',
        credential: values,
      });
      dispatch(setLoading(false));
      
      if (status) {
        const { data, status } = await updateProfile({
          name: profileData?.fullName,
          password: profileData?.password,
          speciality: profileData?.speciality?.value,
          referral_code: profileData?.referral,
          registration_id: profileData?.registrationId,
          registration_state: profileData?.state?.value,
        });
        if (data && status) {
          dispatch(getUserInfo());
          trackEvent('COMPLETE_REGISTRATION',{
            registrationId: data?.id,
            params: { customer_id: data?.customer_id }
          })
          AnalyticsEvents(
            'SIGN_UP_EMAIL_PHONE',
            'Sign Up-Email/Phone',
            data,
            {},
            false,
          );
          return navigation.reset({
            index: 0,
            routes: [{ name: 'Tab', params: { screen: 'Shop' } }],
          });
        }
        else {
          console.log('error');
        }
      }
    },
    [emailPhoneNumber, navigateToLastScreen, actionType, profileData],
  );

  const LoginWithOtpResend = useCallback(async () => {
    dispatch(setLoading(true));
    const { data, status } = await userLogin({
      recipient: emailPhoneNumber,
      action: 'm_login_signup',
      authentication_type: emailPhoneNumber.includes('@') ? 'email' : 'mobile',
    });
    dispatch(setLoading(false));
    if (data && status) {
      setShouldShow(true);
      showSuccessMessage(data?.message);
    } else {
      showErrorMessage(data?.message);
    }
  }, [emailPhoneNumber, dispatch]);

  const completeOtpTimer = () => {
    setShouldShow(false);
  };


  useFocusEffect(
    useCallback(() => {
      if (route?.params?.values) {
        const email = route?.params?.values?.email;
        setEmailPhoneNumber(email);
      }
      return () => {
        // cleanup here if needed
      };
    }, [route?.params])
  );

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler
        componentName={`${TAG} OtpScene`}
        onErrorComponent={<View />}>
        <OtpScene
          setOtpLogin={val => {
            navigation.goBack();
            setEditableAccess(false);
          }}
          setShowPassInput={setShowPassInput}
          setPhoneFocus={setPhoneFocus}
          pageType={pageType}
          source={'login'}
          showPass={showPassInput}
          emailPhoneNumber={emailPhoneNumber}
          navigation={navigation}
          route={route}
          onSubmit={verifyOtp}
          isVerify={isVerify}
          apiErrorOtp={apiErrorOtp}
          onResend={LoginWithOtpResend}
          shouldShow={shouldShow}
          completeOtpTimer={completeOtpTimer}
          onEditPress={() => {
            setOtpLogin('otpLogin');
            setEditableAccess(false);
          }}
          setApiErrorOtp={setApiErrorOtp}
          editableAccess={editableAccess}
        />
      </ErrorHandler>
    </SafeAreaView>
  );
};

export default EmailVerificationOtpScene;

import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import {
  ActivityIndicator,
  FlatList,
  TextInput,
  TouchableOpacity,
  View,
  Dimensions,
  ScrollView,
  Animated,
  LayoutChangeEvent,
  BackHandler,
  Pressable,
  Keyboard,
  InteractionManager,
  Platform,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useTheme, useIsFocused} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {useDispatch, useSelector} from 'react-redux';
import FastImage from 'react-native-fast-image';
import {t} from 'i18next';
import {RootStackParamsList} from 'routes';
import {Header, Button} from 'components/molecules';
import {
  ImageIcon,
  Label,
  ListView,
  OrderCard,
  ProductCardVertical,
  Separator,
  Spacer,
  WithGradient,
  OrderFilter,
  OrderCancelModal,
  ReturnCard,
  DeliveryInfoModal,
  RetryPaymentModal,
  SuccessModal,
  HorizontalScrollBar,
} from 'components/atoms';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import {
  CanCancelOrder,
  ordersList,
  ordersTrackingList,
  cancelOrdersApi,
  cancelRegion,
  OrderReturnListApi,
  RetryPayment,
} from 'services/orders';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {orderFilterData, returnFilterData, statusMapping} from 'staticData';
import {getDefaultWishlist} from 'services/wishlist';
import {getCategoryDetails} from 'services/category';
import {OrderLoader} from 'skeletonLoader';
import {fetchPayment} from 'services/checkout';
import {ImagePath} from 'config/apiEndpoint';
import RazorpayCheckout from 'react-native-razorpay';
import {debounce, paymentOnlineEvent} from 'utils/utils';
import {AnalyticsEvents} from 'components/organisms';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import {addToCart, setLoading} from 'app-redux-store/slice/appSlice';
import orderDetail from '../orderDetail';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';
import {debugLog} from 'utils/debugLog';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'OrderList'>;
  totalPrize?: boolean;
  reOrder?: boolean;
};

export const EmptyOrders = prop => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <>
      <Spacer size="x8l" />
      <View style={styles.mainContainer}>
        <View
          style={
            prop?.currentTab === 'order'
              ? styles.orderImageView
              : styles.returnImageView
          }>
          <FastImage
            resizeMode="contain"
            style={
              prop?.currentTab === 'order'
                ? styles.orderBackground
                : styles.returnBackground
            }
            source={
              prop?.currentTab === 'order'
                ? Icons.emptyOrderBG
                : Icons.emptyReturnBG
            }
          />
          <FastImage
            style={styles.image}
            source={
              prop?.currentTab === 'order'
                ? Icons.emptyOrder
                : Icons.emptyReturn
            }
          />
        </View>
        <Spacer size="mx" />
        <View>
          <Label
            color="text"
            size="l"
            align="center"
            fontFamily="Medium"
            text={` ${
              prop?.currentTab === 'order'
                ? t('orderListing.noOrder')
                : t('orderListing.noReturn')
            }`}
          />
          <Spacer size="mx" />
          {prop?.currentTab === 'order' ? (
            <Button
              onPress={() =>
                prop?.navigation.navigate('Tab', {screen: 'homePage'})
              }
              style={styles.orderButton}
              radius="xm"
              type="secondary"
              labelSize="mx"
              labelColor="whiteColor"
              text={t('otherText.orderNow')}
            />
          ) : null}
        </View>
      </View>
    </>
  );
};

const OrderListScene = ({
  navigation,
  reOrder = false,
  totalPrize = true,
  route,
}: Props) => {
  const dispatch = useDispatch();
  const {colors} = useTheme();
  const TAG = 'OrderListScreen';
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [orderList, setOrderList] = useState<OrderResponse[] | []>([]);
  const [orderReturnList, setOrderReturnList] = useState<OrderReturn[] | []>(
    [],
  );
  const [modalVisibleFilter, setModalVisibleFilter] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageLimit, setPageLimit] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalReturnPages, setTotalReturnPages] = useState<number>(1);
  const [moreLoader, setMoreLoader] = useState<boolean>(false);
  const [currentTab, setCurrentTab] = useState<string>('order');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [cancelLoader, setCancelLoader] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [dropDownIndex, setDropDownIndex] = useState();
  const [canCancel, setCanCancel] = useState<CanCancel>();
  const [orderId, setOrderId] = useState<string>('');
  const [selectedSubReason, setSelectedSubReason] = useState<
    CancelOrderInput | undefined
  >();
  const [regions, setRegions] = useState<CancelOrderInput[]>([]);
  const [filterData, setFilterData] = useState<OrderFilterOption[]>([]);
  const [tempSelectedFilters, setTempSelectedFilters] = useState<
    OrderFilterOption | any
  >({Time: ['Last Year'], status: ['All']});
  const [retryModalVisible, setRetryModalVisible] = useState<boolean>(false);
  const [finalSelectedFilters, setFinalSelectedFilters] = useState<
    OrderFilterOption | any
  >({Time: ['Last Year'], status: ['All']});
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState([]);
  const [circularProgress, setCircularProgress] = useState(null);
  const [orderSuccessModel, setOrderSuccessModel] = useState(false);
  const [defaultWishList, setDefaultWishList] = useState<ProductData[]>([]);
  const [bestDeal, setBestDeal] = useState<ProductData[]>([]);
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const {width: screenWidth} = Dimensions.get('window');
  const flatListRef = useRef(null);
  const [scrollX, setScrollX] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);
  const [listWidth, setListWidth] = useState(0);
  const [scrollBarWidth, setScrollBarWidth] = useState(0);
  const scrollX1 = useRef(new Animated.Value(0)).current;
  const ITEM_WIDTH = screenWidth * 0.47;
  const ITEMS_TO_SCROLL = 2;
  const bestDealId = 2567;
  const scrollViewRef = useRef(null);
  const [newOrder, setNewOrder] = useState<boolean>(true);
  const [hideOldOrder, setHideOldOrder] = useState<boolean>(false);
  const [hideLatest, setHideLatest] = useState<boolean>(false);
  const [infoModel, setInfoModel] = useState(false);
  const [checkCancelLoader, setCheckCancelLoader] = useState(false);
  const isFocus = useIsFocused();

  const handleScroll = useCallback(
    Animated.event([{nativeEvent: {contentOffset: {x: scrollX1}}}], {
      useNativeDriver: false,
      listener: (event: any) => {
        const offsetX = event.nativeEvent.contentOffset.x;
        setScrollX(offsetX);
      },
    }),
    [scrollX1],
  );

  const dataMap = useMemo(() => {
    return {
      Wishlist: defaultWishList,
      Bestdeal: bestDeal,
    };
  }, [defaultWishList, bestDeal]);

  const [selectedTab, setSelectedTab] = useState('');

  useEffect(() => {
    if (isFocus) {
      if (currentTab === 'Returns') {
        getOrderReturnList(pageNumber, '', finalSelectedFilters);
        setCurrentTab('Returns');
      } else {
        getOrderListData(
          pageNumber,
          searchTerm,
          false,
          finalSelectedFilters,
          newOrder,
        );
      }
      scrollToTopView();
    }
  }, [pageNumber, isFocus]);

  useEffect(() => {
    filterTab();
  }, [defaultWishList]);

  useEffect(() => {
    if (canCancel?.is_cancelable) {
      getCancelResignList();
    }
  }, [getCancelResignList, canCancel]);

  useLayoutEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (route.params?.pageType === 'Returns') {
        setCurrentTab('Returns');
        await getOrderReturnList(pageNumber, '', finalSelectedFilters);
      }
      getDefaultWishList();
      getBestDealData();
      setIsLoading(false);
    };
    fetchData();
  }, []);

  const checkCancelOrder = (oId, index) => {
    setOrderId(oId);
    setDropDownIndex(index);
    canCancelOrders(oId);
  };

  const getTimeOptions = order => {
    if (!order) {
      return [
        'Last 30 Days',
        'Last 3 Months',
        'Last 6 Months',
        'Last Year',
        'All',
      ];
    }
    const currentDate = new Date();
    const fixedStartDate = new Date('2025-01-01');
    const monthsDiff =
      currentDate.getMonth() -
      fixedStartDate.getMonth() +
      12 * (currentDate.getFullYear() - fixedStartDate.getFullYear());

    const options = ['Last 30 Days'];

    if (monthsDiff >= 3) options.push('Last 3 Months');
    if (monthsDiff >= 6) options.push('Last 6 Months');
    if (monthsDiff >= 12) options.push('Last Year');

    options.push('All');
    return options;
  };

  useEffect(() => {
    if (currentTab === 'order') {
      const updatedFilterData = [...orderFilterData];
      updatedFilterData[1] = {
        ...updatedFilterData[1],
        data: getTimeOptions(newOrder),
      };
      setFilterData(updatedFilterData);
    } else if (currentTab === 'Returns') {
      setFilterData(returnFilterData);
    }
  }, [currentTab, newOrder]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const filterTab = () => {
    if (dataMap.Wishlist.length > 0) {
      setSelectedTab('Wishlist');
    } else {
      setSelectedTab('Bestdeal');
    }
  };

  const scrollToEnd = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollX + ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [ITEM_WIDTH, scrollX]);
  const scrollToStart = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollX - ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [ITEM_WIDTH, scrollX]);

  const parseTimeFilter = useCallback((timeFilter: any) => {
    const timeFilterMap: {[key: string]: [number, string]} = {
      'Last 30 Days': [30, 'day'],
      'Last 3 Months': [3, 'month'],
      'Last 6 Months': [6, 'month'],
      'Last Year': [1, 'year'],
      All: [60, 'month'],
    };
    return timeFilterMap[timeFilter] || [0, ''];
  }, []);

  const onReplaceStatus = (filter, order) => {
    const firstStatus = filter?.status?.[0];
    if (firstStatus === 'All') {
      return '';
    }
    return firstStatus === 'Delivered'
      ? order
        ? firstStatus
        : firstStatus.toLowerCase()
      : filter?.status?.join(',');
  };

  const getOrderListData = useCallback(
    async (pageNumber = 1, search = '', hide = false, filter, order) => {
      if (!hide) {
        if (pageNumber === 1) {
          setIsLoading(true);
        } else {
          setMoreLoader(true);
        }
      }
      const filters: any = {
        page_no: pageNumber,
        page_size: pageLimit,
        sort_by: 'order_date,desc',
      };

      if (filter?.status && filter?.status.length > 0) {
        const statusVal = onReplaceStatus(filter, order);
        if (statusVal) {
          filters.status = statusVal;
        }
      }
      if (filter?.Time && filter?.Time.length > 0) {
        const timeFilter = filter?.Time[0];
        const [timeValue, timeUnit] = parseTimeFilter(timeFilter);
        if (timeValue > 0 && timeUnit) {
          filters.time_value = timeValue;
          filters.time_unit = timeUnit;
        }
      }
      if (search) {
        const orderIdRegex = /^[\d-]+$/;
        if (orderIdRegex.test(search)) {
          filters.order_increment_id = search;
        } else {
          filters.item_name = search;
        }
      }
      const response = await (order
        ? ordersTrackingList(filters)
        : ordersList(filters));
      const {status, data} = response;
      if (status) {
        if (order) {
          setHideOldOrder(data?.has_old_orders);
        }
        const orders = Array.isArray(data?.orders) ? data?.orders : [];
        const maxPages = Math.ceil(data?.total_order_count / pageLimit);
        if (
          orders.length === 0 &&
          order &&
          pageNumber === 1 &&
          search.length === 0 &&
          data.has_old_orders &&
          !filters?.status
        ) {
          onSwitchYear();
          setHideLatest(true);
        }
        setOrderList(orders);
        if (pageNumber === 1) {
          setTotalPages(maxPages);
        } else {
          // setOrderList(prev => [...prev, ...orders]);
        }
      }
      setIsLoading(false);
      setMoreLoader(false);
    },
    [pageLimit, parseTimeFilter, currentTab],
  );

  // =====================Return List Api=====================
  const onReplaceReturnStatus = filter => {
    const statusMap = {
      'Return Raised': 'request_raised',
      'Return In process': 'in_process',
      'Return Completed': 'complete',
      All: '',
    };

    const firstStatus = filter?.status?.[0];
    return statusMap[firstStatus] ?? filter?.status?.join(',');
  };

  const getOrderReturnList = useCallback(
    async (pageNumber = 1, search = '', filter) => {
      if (pageNumber === 1) {
        setIsLoading(true);
      } else {
        setMoreLoader(true);
      }
      const filters: any = {
        page_no: pageNumber,
        row_per_page: pageLimit,
      };

      if (filter?.status && filter?.status?.length > 0) {
        if (onReplaceReturnStatus(filter)) {
          filters.status = onReplaceReturnStatus(filter);
        }
      }
      if (filter?.Time && filter?.Time.length > 0) {
        const timeFilter = filter?.Time[0];
        const [timeValue, timeUnit] = parseTimeFilter(timeFilter);
        if (timeValue > 0 && timeUnit) {
          filters.time_value = timeValue;
          filters.time_unit = timeUnit;
        }
      }
      if (search) {
        const orderIdRegex = /^[\d-]+$/;
        if (orderIdRegex.test(search)) {
          filters.return_increment_id = search;
        } else {
          filters.item_name = search;
        }
      }
      const response = await OrderReturnListApi(filters);
      const {status, data} = response;
      if (status) {
        const returns = Array.isArray(data?.result) ? data.result : [];
        const maxPages = Math.ceil(data?.count / pageLimit);
        setOrderReturnList(returns);
        if (pageNumber === 1) {
          setTotalReturnPages(maxPages);
        } else {
          // setOrderReturnList(prevList => [...prevList, ...returns]);
        }
      }
      setIsLoading(false);
      setMoreLoader(false);
    },
    [pageLimit, parseTimeFilter],
  );

  const getDefaultWishList = useCallback(async () => {
    const {data, status}: any = await getDefaultWishlist();
    if (data && status) {
      setDefaultWishList(data?.items);
    }
  }, []);

  const getBestDealData = useCallback(async () => {
    const {data, status}: any = await getCategoryDetails(bestDealId);
    if (data && status) {
      setBestDeal(data?.categoryProducts?.products);
    }
  }, []);

  const handleButtonClick = (buttonName: string) => {
    setSelectedTab(buttonName);
  };

  // Create a curried version of handleButtonClick
  const handleButtonClickCurried = useCallback(
    (buttonName: string) => () => {
      handleButtonClick(buttonName);
    },
    [],
  );

  // ====================Can Cancel Api================
  const getCancelResignList = useCallback(async () => {
    const {data}: any = await cancelRegion();
    if (data) {
      setRegions(data?.reasons);
    }
  }, []);

  // ==================== Cancel Order Api================
  const canCancelOrders = useCallback(async (order_Id: string) => {
    setCheckCancelLoader(true);
    const {data, status}: any = await CanCancelOrder(order_Id);
    setCheckCancelLoader(false);
    if (status && data) {
      setCanCancel(data?.order);
      setModalVisible(data?.order?.is_cancelable ? true : false);
    } else {
      setCanCancel(undefined);
      setModalVisible(false);
      getOrderListData(
        pageNumber,
        searchTerm,
        true,
        finalSelectedFilters,
        newOrder,
      );
    }
  }, []);

  // ==============Available Payment Method =================
  const checkPayment = useCallback(
    async (order_Id: string, retryCheck: boolean) => {
      setCircularProgress(order_Id);
      const {data, status}: any = await fetchPayment({order_id: order_Id});
      if (data && status) {
        setAvailablePaymentMethods(data);
        setCircularProgress(null);
        setRetryModalVisible(true);
        if (retryCheck) {
          AnalyticsEvents(
            'RETRY_PAYMENT',
            'Retry Payment',
            data,
            userInfo,
            isLoggedIn,
          );
        } else {
          if (data && data?.status === 'success') {
            setRetryModalVisible(false);
            onPaymentSuccess(data);
            getOrderListData(
              pageNumber,
              searchTerm,
              true,
              finalSelectedFilters,
              newOrder,
            );
          }
        }
      }
    },
    [],
  );

  const navigateToOrderSuccess = async (payload, cod = false) => {
    setOrderSuccessModel(cod ? false : true);
    setTimeout(
      () => {
        setOrderSuccessModel(false);
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'ThankYouPage',
              params: {order_id: payload},
            },
          ],
        });
      },
      cod ? 0 : 5000,
    );
  };

  const confirmPayment = useCallback(async payload => {
    try {
      const {rzp_signature, ...cleanedPayload} = payload;
      const {data} = await fetchPayment(cleanedPayload);
      dispatch(setLoading(false));
      if (data && data?.order_id) {
        if (data && data?.status === 'success') {
          onPaymentSuccess(data);
        }
      }
      getOrderListData(
        pageNumber,
        searchTerm,
        true,
        finalSelectedFilters,
        newOrder,
      );
    } catch (e) {
      dispatch(setLoading(false));
      getOrderListData(
        pageNumber,
        searchTerm,
        true,
        finalSelectedFilters,
        newOrder,
      );
    }
  }, []);

  const onPaymentSuccess = data => {
    setOrderSuccessModel(true);
    const paymentData = {
      ...data,
    };
    try {
      paymentOnlineEvent(paymentData, userInfo, isLoggedIn);
      setTimeout(() => {
        setOrderSuccessModel(false);
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'ThankYouPage',
              params: {order_id: data?.order_id},
            },
          ],
        });
      }, 5000);
    } catch (error) {
      debugLog('error', error);
    } finally {
      setRetryModalVisible(false);
    }
  };

  const initiateRazorPay = useCallback(
    (payload, rzpData) => {
      const options = {
        ...rzpData,
        description: '',
        image: ImagePath.dentalkart,
        name: 'Dentalkart',
        prefill: {
          email: userInfo.email,
          contact: userInfo?.mobile ? userInfo.mobile : '',
          name: `${userInfo.firstname} ${userInfo.lastname}`,
        },
        theme: {color: ''},
      };

      RazorpayCheckout.open(options)
        .then(response => {
          // handle success
          dispatch(setLoading(true));
          const rzpres = {
            rzp_payment_id: response.razorpay_payment_id,
            rzp_order_id: response.razorpay_order_id,
            rzp_signature: response.razorpay_signature,
          };
          const RzpPayload = {...payload, ...rzpres};

          setTimeout(() => {
            confirmPayment(payload);
          }, 5000);
        })
        .catch(error => {
          dispatch(setLoading(true));
          setRetryModalVisible(false);
          setTimeout(() => {
            confirmPayment(payload);
          }, 5000);

          showErrorMessage(
            `${
              error.error.description && error.error.description !== 'undefined'
                ? error.error.description
                : t('payment.payFail')
            }. ${t('payment.tryAgain')}`,
          );
        });
    },
    [userInfo?.email, userInfo?.firstname, userInfo?.lastname],
  );

  const onSelectedPaymentMethod = useCallback(
    async (PayData: string, paymentMethod: string) => {
      setRetryModalVisible(false);
      if (paymentMethod === 'cashondelivery') {
        dispatch(setLoading(true));
        const {data, status}: any = await RetryPayment({
          order_id: PayData?.order_id,
          payment_method_code: paymentMethod,
        });
        dispatch(setLoading(false));
        if (data && status) {
          const matchedOrder =
            orderList?.find(order => order?.data?.order_id === orderId) || null;
          const finalData = matchedOrder
            ? {
                ...data,
                order_data: matchedOrder?.data ?? {},
                tracking_data: matchedOrder?.tracking ?? {},
              }
            : {...data, order_data: {}, tracking_data: {}};
          const paymentSuccessData = {
            'Order Id': finalData?.order_id ?? 'Unknown Order ID',
            'Payment Mode': 'cashondelivery', // No direct mapping in provided JSON
            Currency: 'INR', // Hardcoded since it's missing from order data
            Coupon:
              finalData?.order_data?.order_summary?.coupon_code?.code ??
              'No Coupon', // Assuming coupon code field
            'Total Amount':
              finalData?.order_data?.order_summary?.order_amount ?? 0,
            Brand: finalData?.order_data?.items?.map(item => 'Unknown Brand'), // Brand missing in JSON
            Category: finalData?.order_data?.items?.map(() => 'Dental'), // Assuming dental category
            SKU: finalData?.order_data?.items?.map(
              item => item?.sku ?? 'Unknown SKU',
            ),
            'Parent SKU': finalData?.order_data?.items?.map(
              item => item?.sku ?? 'Unknown Parent SKU',
            ),
            'Product Name': finalData?.order_data?.items?.map(
              item => item?.name ?? 'Unknown Product',
            ),
            'Total Price': finalData?.order_data?.items?.reduce(
              (total, item) => total + (item?.price ?? 0),
              0,
            ),
            'Product Details': finalData?.order_data?.items ?? [], // Assuming full item details
            'Total Quantity':
              finalData?.order_data?.order_summary?.total_product_qty ?? 1,
            'Total MRP': finalData?.order_data?.order_summary?.total_mrp ?? 0, // Assuming MRP field
            'Order Date':
              finalData?.order_data?.dates?.ordered?.date ??
              new Date().toISOString(),
            'No. Of Products': finalData?.order_data?.items?.length ?? 1,
            'Reward Earned': finalData?.order_data?.items?.reduce(
              (total, item) => total + (item?.reward_earned_coins ?? 0),
              0,
            ),
            'Coupon Value':
              finalData?.order_data?.order_summary?.coupon_value ?? 0, // Assuming coupon discount
            Address:
              finalData?.order_data?.customer_address ?? 'No Address Provided', // Assuming address field
            'Reward Used':
              finalData?.order_data?.order_summary?.reward_used ?? 0, // Assuming reward used
            'Product Price': finalData?.order_data?.items?.map(
              item => item?.price ?? 0,
            ),
            'Total Amount Saved':
              finalData?.order_data?.order_summary?.total_savings ?? 0,
            'Product Id': finalData?.order_data?.items?.map(
              item => item?.product_id ?? 'Unknown Product ID',
            ),
            'Discount Amount':
              finalData?.order_data?.order_summary?.discount_amount ?? 0, // Assuming discount field
            'Shipping Charge':
              finalData?.order_data?.order_summary?.shipping_charge ?? 0, // Assuming shipping field
            'Tax Amount': finalData?.order_data?.order_summary?.tax_amount ?? 0, // Assuming tax field
          };

          // appsFlyer COD Retry Payment OrderData
          const appsFlyerOrderData = {
            estimatedRevenue: paymentSuccessData['Total Amount'],
            totalAmount: paymentSuccessData['Total Amount'],
            sku: paymentSuccessData['SKU'],
            productIds: paymentSuccessData['Product Id'],
            productNames: paymentSuccessData['Product Name'],
            currency: paymentSuccessData['Currency'],
            productQuantities: paymentSuccessData['No. Of Products'],
            orderId: paymentSuccessData['Order Id'],
            receiptId: paymentSuccessData['Order Id'],
          };
          appsFlyerEvent('CheckoutCompleted', appsFlyerOrderData);

          AnalyticsEvents(
            'CHECKOUT_COMPLETED',
            'Checkout Completed',
            paymentSuccessData,
            userInfo,
            isLoggedIn,
          );
          setRetryModalVisible(false);
          navigateToOrderSuccess(data?.order_id, true);
        }
      } else {
        const payload = {order_id: PayData?.order_id};
        const rzpData = {
          amount: PayData?.amount,
          order_id: PayData?.reference_number,
          key: PayData?.merchant_id,
          currency: PayData?.currency,
        };
        setTimeout(
          () => {
            initiateRazorPay(payload, rzpData);
          },
          Platform.OS === 'ios' ? 100 : 0,
        );
      }
    },
    [
      initiateRazorPay,
      isLoggedIn,
      navigateToOrderSuccess,
      orderId,
      orderList,
      userInfo,
    ],
  );

  // ====================Can Cancel Api================
  const cancelOrders = useCallback(async () => {
    setCancelLoader(true);

    const {data}: any = await cancelOrdersApi(orderId, {
      reason_id: selectedSubReason?.id,
      is_full_cancel: true,
    });
    setCancelLoader(false);
    if (data) {
      setSelectedSubReason(undefined);
      setCanCancel(data?.order);
      setModalVisible(false);
      showSuccessMessage(t('toastMassages.orderCancel'));
      getOrderListData(
        pageNumber,
        searchTerm,
        true,
        finalSelectedFilters,
        newOrder,
      );
      const matchedOrder =
        orderList?.find(order => order?.data?.order_id === orderId) || null;
      let analyticsData = {
        'Product Name': matchedOrder?.data?.items
          ? matchedOrder.data.items.map(item => item?.name).join(', ')
          : '',
        'order id': matchedOrder?.data?.order_id
          ? String(matchedOrder.data.order_id)
          : '',

        quantity: matchedOrder?.data?.items
          ? matchedOrder.data.items.map(item => item?.ordered_qty)
          : [],

        sku: matchedOrder?.data?.items
          ? matchedOrder.data.items.map(item => item?.sku).join(',')
          : '',

        'Total Quantity':
          matchedOrder?.data?.order_summary?.total_product_qty ?? 0,

        'Total Amount': matchedOrder?.data?.order_summary?.order_amount ?? 0,

        'Parent SKU': matchedOrder?.data?.items
          ? matchedOrder.data.items.map(item => item?.sku)
          : [],

        Reason:
          regions.find(region => region.id === data?.reason)?.reason ||
          data?.reason ||
          '',
      };

      // appsFlyer Cancel Order Data
      const appsFlyerCancelOrderData = {
        totalAmount: analyticsData['Total Amount'],
        productNames: analyticsData['Product Name'],
        currency: 'INR',
        quantity: analyticsData['Total Quantity'],
        orderId: analyticsData['order id'],
      };
      appsFlyerEvent('CancelProduct', appsFlyerCancelOrderData);
      AnalyticsEvents(
        'ORDER_CANCELLED',
        'Order Cancelled',
        analyticsData,
        userInfo,
        isLoggedIn,
      );
      // navigation.navigate('OrderList');
    } else {
      showErrorMessage(data?.message);
    }
  }, [
    finalSelectedFilters,
    getOrderListData,
    orderId,
    orderList,
    pageNumber,
    selectedSubReason?.id,
    searchTerm,
    newOrder,
  ]);

  const onSwitchYear = () => {
    setSearchTerm('');
    setNewOrder(prev => !prev);
    setPageNumber(1);
    setOrderList([]);
    Keyboard.dismiss();
    InteractionManager.runAfterInteractions(() => {
      getOrderListData(1, '', false, finalSelectedFilters, !newOrder);
    });
  };

  const ListFooter = useCallback(() => {
    return (
      <View style={styles.loading}>
        <ActivityIndicator size="large" color={colors.blue} />
      </View>
    );
  }, []);

  const handleTempFilterSelection = useCallback(
    (title: string, item: OrderFilterOption) => {
      setTempSelectedFilters(prevState => {
        const newFilters = {...prevState};
        newFilters[title] = [item];
        return newFilters;
      });
    },
    [],
  );

  const hideFilterModal = useCallback(() => {
    setModalVisibleFilter(false);
  }, []);

  const handleApplyFilters = useCallback(() => {
    setFinalSelectedFilters(tempSelectedFilters);
    setPageNumber(1);
    hideFilterModal();
    currentTab === 'Returns'
      ? getOrderReturnList(1, searchTerm, tempSelectedFilters)
      : getOrderListData(1, searchTerm, false, tempSelectedFilters, newOrder);
  }, [
    currentTab,
    getOrderListData,
    getOrderReturnList,
    hideFilterModal,
    searchTerm,
    tempSelectedFilters,
    newOrder,
  ]);

  const handleClearFilters = useCallback(() => {
    let clearFilter = {Time: ['Last Year'], status: ['All']};
    setTempSelectedFilters(clearFilter);
    setFinalSelectedFilters(clearFilter);
    hideFilterModal();
    setOrderList([]);
    setOrderReturnList([]);
    setPageNumber(1);
    currentTab === 'Returns'
      ? getOrderReturnList(1, searchTerm, clearFilter)
      : getOrderListData(1, searchTerm, false, clearFilter, newOrder);
  }, [
    currentTab,
    getOrderListData,
    getOrderReturnList,
    hideFilterModal,
    searchTerm,
    newOrder,
  ]);

  // Handle next Button
  const handleNext = useCallback(() => {
    if (moreLoader) {
      return;
    }
    if (pageNumber < totalPages) {
      setPageNumber(pageNumber + 1);
    }
  }, [moreLoader, pageNumber, totalPages]);

  const handleNextRetrunList = useCallback(() => {
    if (moreLoader) {
      return;
    }
    if (pageNumber < totalReturnPages) {
      setPageNumber(pageNumber + 1);
    }
  }, [moreLoader, pageNumber, totalReturnPages]);

  // Handle Previous Button
  const handlePrevious = useCallback(() => {
    if (moreLoader) {
      return;
    }
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1);
    }
  }, [moreLoader, pageNumber]);

  const handlePageNumber = useCallback(
    (pageNum: number) => {
      if (moreLoader) {
        return;
      }
      setPageNumber(pageNum);
    },
    [moreLoader],
  );
  // Handle number page Button
  const getPageNumbers = useCallback(() => {
    if (totalPages <= 3) {
      return [...Array(totalPages).keys()].map(num => num + 1);
    } else if (pageNumber === 1) {
      return [1, 2, 3];
    } else if (pageNumber === totalPages) {
      return [totalPages - 2, totalPages - 1, totalPages];
    } else {
      return [pageNumber - 1, pageNumber, pageNumber + 1];
    }
  }, [pageNumber, totalPages]);

  const getPageNumberReturnList = useCallback(() => {
    if (totalReturnPages <= 3) {
      return [...Array(totalReturnPages).keys()].map(num => num + 1);
    } else if (pageNumber === 1) {
      return [1, 2, 3];
    } else if (pageNumber === totalReturnPages) {
      return [totalReturnPages - 2, totalReturnPages - 1, totalReturnPages];
    } else {
      return [pageNumber - 1, pageNumber, pageNumber + 1];
    }
  }, [pageNumber, totalReturnPages]);

  const onChangeTab = useCallback(
    (name: string) => {
      let filter = {Time: ['Last Year'], status: ['All']};
      setNewOrder(true);
      setPageNumber(1);
      setTotalPages(1);
      setTotalReturnPages(1);
      setCurrentTab(name);
      setOrderReturnList([]);
      setOrderList([]);
      setSearchTerm('');
      setTempSelectedFilters(filter);
      setFinalSelectedFilters(filter);
      name === 'Returns'
        ? getOrderReturnList(1, '', filter)
        : getOrderListData(1, '', false, filter, true);
    },
    [getOrderListData, getOrderReturnList],
  );

  const debouncedUpdate = useCallback(
    debounce((text: string, filter: OrderFilterOption, order) => {
      setPageNumber(1);
      setTotalPages(1);
      setTotalReturnPages(1);
      setOrderReturnList([]);
      setOrderList([]);
      currentTab === 'Returns'
        ? getOrderReturnList(1, text, filter)
        : getOrderListData(1, text, false, filter, order);
    }, 500),
    [],
  );

  const onSearch = useCallback(
    (text: string) => {
      setSearchTerm(text);
      debouncedUpdate(text, finalSelectedFilters, newOrder);
    },
    [debouncedUpdate, finalSelectedFilters, newOrder],
  );

  const scrollToTopView = () => {
    scrollViewRef.current?.scrollToOffset({offset: 0, animated: true});
  };

  const handleContentSizeChange = useCallback(
    (contentWidth: any) => {
      setListWidth(contentWidth);
      if (screenWidth > 0 && contentWidth > screenWidth) {
        const calculatedScrollBarWidth =
          (screenWidth / contentWidth) * (screenWidth / 4);
        setScrollBarWidth(Math.max(calculatedScrollBarWidth, 20));
      }
    },
    [screenWidth],
  );

  const scrollBarPosition = scrollX1.interpolate({
    inputRange: [0, Math.max(0, listWidth - screenWidth)],
    outputRange: [0, Math.max(0, screenWidth / 6 - scrollBarWidth)],
    extrapolate: 'clamp',
  });

  const onBackPress = useCallback(() => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
  }, [navigation, route.params]);

  const EarnNoteView = useMemo(() => {
    return (
      <LinearGradient
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        style={styles.linerColor}
        colors={[colors.skyBlue15, colors.red1]}>
        <Label
          text={t('orderListing.earnNote')}
          fontFamily="Medium"
          size="m"
          color="whiteColor"
        />
      </LinearGradient>
    );
  }, [colors.red1, colors.skyBlue15, styles.linerColor]);

  const showFilterModal = useCallback(() => {
    setModalVisibleFilter(true);
  }, []);

  const activateOrderTab = useCallback(() => {
    onChangeTab('order');
  }, [onChangeTab]);

  const activateReturnTab = useCallback(() => {
    onChangeTab('Returns');
  }, [onChangeTab]);

  const keyExtractor = useCallback((_, i) => i.toString(), []);

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const setCancelModalVisible = useCallback(
    (val: boolean) => {
      setModalVisible(val);
    },
    [setModalVisible],
  );

  const orderListItem = useCallback(
    ({item, index}: {item: OrderResponse; index: number}) => {
      return (
        <OrderCard
          index={index}
          navigation={navigation}
          totalPrize={totalPrize}
          item={item}
          currency={orderList?.currency}
          setModalVisible={setCancelModalVisible}
          modalVisible={modalVisible}
          setDropDownIndex={setDropDownIndex}
          dropDownIndex={dropDownIndex}
          canCancel={canCancel}
          setOrderId={setOrderId}
          orderId={orderId}
          type="order"
          onSelectMethod={checkPayment}
          circularProgress={circularProgress}
          onInfoClick={() => setInfoModel(!infoModel)}
          onCancelOrder={(oId, i) => checkCancelOrder(oId, i)}
          checkCancelLoader={checkCancelLoader}
        />
      );
    },
    [
      checkPayment,
      canCancel,
      circularProgress,
      dropDownIndex,
      modalVisible,
      navigation,
      orderId,
      orderList?.currency,
      setCancelModalVisible,
      totalPrize,
      checkCancelLoader,
    ],
  );

  const renderReturnListItem = useCallback(
    ({item, index}: {item: OrderReturn; index: number}) => {
      return (
        <ReturnCard
          item={item}
          type="Returns"
          index={index}
          navigation={navigation}
        />
      );
    },
    [navigation],
  );

  const memoizedEmptyListData = useMemo(() => {
    return [''];
  }, []);

  const listViewItemSeparator = useMemo(() => {
    return <Spacer type="Vertical" size="m" />;
  }, []);

  const emptyOrders = useMemo(() => {
    return <EmptyOrders currentTab={currentTab} navigation={navigation} />;
  }, [currentTab, navigation]);

  const listFooter = useMemo(() => {
    return moreLoader ? <ListFooter /> : <View />;
  }, [ListFooter, moreLoader]);

  const pageNumberStyle = useCallback(
    (pageNum: number) => {
      return [
        styles.pageBox,
        {
          backgroundColor:
            pageNumber === pageNum ? colors.categoryTitle : colors.whiteColor,
        },
      ];
    },
    [colors.categoryTitle, colors.whiteColor, pageNumber, styles.pageBox],
  );

  const handleContainerWidthChange = useCallback((event: LayoutChangeEvent) => {
    setContainerWidth(event.nativeEvent.layout.width);
  }, []);

  const productsContainerGradientColors = useMemo(() => {
    return [colors.cerise0, colors.cerise24];
  }, [colors.cerise0, colors.cerise24]);

  const productTabBarRenderItem = useCallback(() => {
    return (
      <View style={styles.buttonView}>
        {defaultWishList.length > 0 ? (
          <Button
            text={t('wishList.wishList')}
            type={selectedTab === 'Wishlist' ? 'secondary' : 'bordered'}
            radius="sx"
            labelColor={
              selectedTab === 'Wishlist' ? 'background' : 'categoryTitle'
            }
            labelSize="mx"
            size="extra-small"
            paddingHorizontal="xx"
            weight="500"
            onPress={handleButtonClickCurried('Wishlist')}
            style={styles.btnStyle}
            labelStyle={styles.btnTxt}
          />
        ) : null}
        <Spacer type="Horizontal" size="s" />
        <Button
          text={t('orderListing.bestDeal')}
          type={selectedTab === 'Bestdeal' ? 'secondary' : 'bordered'}
          radius="sx"
          labelColor={
            selectedTab === 'Bestdeal' ? 'background' : 'categoryTitle'
          }
          labelSize="mx"
          size="extra-small"
          paddingHorizontal="xx"
          weight="500"
          onPress={handleButtonClickCurried('Bestdeal')}
          style={styles.btnStyle}
          labelStyle={styles.btnTxt}
        />
      </View>
    );
  }, [
    defaultWishList.length,
    selectedTab,
    styles.btnStyle,
    styles.btnTxt,
    styles.buttonView,
    handleButtonClickCurried,
  ]);

  const productTabData = useMemo(() => {
    return dataMap[selectedTab] || ['Wishlist'];
  }, [dataMap, selectedTab]);

  const onProuctFlatListContentSizeChange = useCallback(
    (contentWidth: number) => {
      setContentWidth(contentWidth);
      handleContentSizeChange(contentWidth);
    },
    [handleContentSizeChange],
  );

  const memoEmptyProductList = useMemo(() => {
    return [];
  }, []);

  const renderProductItem = useCallback(
    ({item, index}: {item: any; index: number}) => {
      return (
        <ScrollView horizontal key={index}>
          <ProductCardVertical
            index={index}
            actionBtn={item?.action_btn}
            skuId={item?.sku}
            size="large"
            maxWidth={0.47}
            skuId={item?.sku}
            item={item}
            productType={item?.type}
            inStock={item.is_in_stock}
            maxSaleQty={item?.max_sale_qty}
            demoAvailable={item?.demo_available}
            msrp={item?.msrp}
            image={item?.media?.mobile_image}
            name={item?.name}
            rewardPoint={item?.reward_point_product}
            description={item?.short_description}
            rating={(item?.rating === 'null' || item.average_rating === null
              ? 0
              : Number(item?.rating) || Number(item?.average_rating)
            ).toFixed(1)}
            ratingCount={
              !!item?.rating_count ? `(${item?.rating_count})` : '(0)'
            }
            price={item?.price}
            sellingPrice={item?.selling_price}
            currencySymbol={item?.currency_symbol}
            discount={item?.discount?.label}
            navigation={navigation}
            freeProducts={memoEmptyProductList}
            showWishlist={true}
          />
        </ScrollView>
      );
    },
    [memoEmptyProductList, navigation],
  );

  const renderItem = useCallback(() => {
    return (
      <>
        {currentTab === 'order' && orderList && (
          <ErrorHandler
            componentName={`${TAG} OrderList`}
            onErrorComponent={<View />}>
            <ListView
              style={styles.flex}
              keyExtractor={listViewKeyExtractor}
              data={orderList}
              extraData={orderList}
              scrollEnabled={false}
              renderItem={orderListItem}
              ItemSeparatorComponent={listViewItemSeparator}
              ListEmptyComponent={emptyOrders}
              ListFooterComponent={listFooter}
            />
          </ErrorHandler>
        )}
        {currentTab === 'Returns' && orderReturnList && (
          <ErrorHandler
            componentName={`${TAG} ReturnsList`}
            onErrorComponent={<View />}>
            <ListView
              style={styles.listView}
              keyExtractor={listViewKeyExtractor}
              data={orderReturnList}
              extraData={orderReturnList}
              renderItem={renderReturnListItem}
              ItemSeparatorComponent={listViewItemSeparator}
              ListEmptyComponent={emptyOrders}
              ListFooterComponent={listFooter}
            />
          </ErrorHandler>
        )}
        {orderList?.length > 0 && currentTab === 'order' && totalPages > 1 ? (
          <ErrorHandler
            componentName={`${TAG} Pagination`}
            onErrorComponent={<View />}>
            <View style={styles.paginationView}>
              <TouchableOpacity
                style={styles.pageBox}
                onPress={handlePrevious}
                disabled={pageNumber === 1}>
                <ImageIcon
                  icon="doubleLeftArrow"
                  size="xl"
                  tintColor={pageNumber === 1 ? 'grey2' : 'text'}
                />
              </TouchableOpacity>
              {getPageNumbers().map((pageNum, index) => {
                const handlePageNumberPress = () => {
                  handlePageNumber(pageNum);
                };
                return (
                  <TouchableOpacity
                    key={index}
                    style={pageNumberStyle(pageNum)}
                    onPress={handlePageNumberPress}>
                    <Label
                      text={String(pageNum)}
                      size="mx"
                      fontFamily="Medium"
                      color={pageNumber === pageNum ? 'whiteColor' : 'text'}
                    />
                  </TouchableOpacity>
                );
              })}
              <TouchableOpacity
                style={styles.pageBox}
                onPress={handleNext}
                disabled={pageNumber === totalPages}>
                <ImageIcon
                  icon="doubleRightArrow"
                  size="xl"
                  tintColor={pageNumber === totalPages ? 'grey2' : 'text'}
                />
              </TouchableOpacity>
            </View>
          </ErrorHandler>
        ) : (
          <View />
        )}
        {orderReturnList?.length > 0 &&
        currentTab === 'Returns' &&
        totalReturnPages > 1 ? (
          <ErrorHandler
            componentName={`${TAG} Pagination`}
            onErrorComponent={<View />}>
            <View style={styles.paginationView}>
              <TouchableOpacity
                style={styles.pageBox}
                onPress={handlePrevious}
                disabled={pageNumber === 1}>
                <ImageIcon
                  icon="doubleLeftArrow"
                  size="xl"
                  tintColor={pageNumber === 1 ? 'grey2' : 'text'}
                />
              </TouchableOpacity>
              {getPageNumberReturnList().map((pageNum, index) => {
                const handlePageNumberPress = () => {
                  handlePageNumber(pageNum);
                };
                return (
                  <TouchableOpacity
                    key={index}
                    style={pageNumberStyle(pageNum)}
                    onPress={handlePageNumberPress}>
                    <Label
                      text={String(pageNum)}
                      size="mx"
                      fontFamily="Medium"
                      color={pageNumber === pageNum ? 'whiteColor' : 'text'}
                    />
                  </TouchableOpacity>
                );
              })}
              <TouchableOpacity
                style={styles.pageBox}
                onPress={handleNextRetrunList}
                disabled={pageNumber === totalReturnPages}>
                <ImageIcon
                  icon="doubleRightArrow"
                  size="xl"
                  tintColor={pageNumber === totalReturnPages ? 'grey2' : 'text'}
                />
              </TouchableOpacity>
            </View>
          </ErrorHandler>
        ) : (
          <View />
        )}
        <View onLayout={handleContainerWidthChange}>
          <Spacer size="xm" />
          <WithGradient
            gradientColors={productsContainerGradientColors}
            gradientStyle={styles.productView}
            gradientAngle={0}>
            <View style={styles.productTopView}>
              <View style={styles.forgetView}>
                <View style={styles.flex}>
                  <View style={styles.didYouForgotView}>
                    <Label
                      text={t('orderListing.brandProduct')}
                      size="l"
                      fontFamily="Medium"
                      color="categoryTitle"
                    />
                    <View style={styles.excludeSubView}>
                      {scrollX > 0 && (
                        <TouchableOpacity onPress={scrollToStart}>
                          <ImageIcon icon="Include" size="x4l" />
                        </TouchableOpacity>
                      )}
                      <Spacer size="xms" />
                      {scrollX + containerWidth <= contentWidth && (
                        <TouchableOpacity onPress={scrollToEnd}>
                          <ImageIcon icon="Exclude" size="x4l" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>
              </View>
              <Spacer size="xm" />
              <ErrorHandler
                componentName={`${TAG} WishListTabbar`}
                onErrorComponent={<View />}>
                <FlatList
                  data={memoizedEmptyListData}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  renderItem={productTabBarRenderItem}
                />
              </ErrorHandler>
            </View>
            <Spacer size="xl" />
            <ErrorHandler
              componentName={`${TAG} WishList`}
              onErrorComponent={<View />}>
              <OptimizedFlatList
                horizontal
                data={productTabData}
                extraData={productTabData}
                ref={flatListRef}
                onScroll={handleScroll}
                showsHorizontalScrollIndicator={false}
                style={styles.productListView}
                keyExtractor={keyExtractor}
                scrollEventThrottle={16}
                onContentSizeChange={onProuctFlatListContentSizeChange}
                renderItem={renderProductItem}
              />
            </ErrorHandler>
            <Spacer size="l" />
            {scrollBarWidth > 0 && (
              <ErrorHandler
                componentName={`${TAG} WishList HorizontalScrollBar`}
                onErrorComponent={<View />}>
                <HorizontalScrollBar
                  activeColor={colors.smoothPink}
                  scrollBarWidth={scrollBarWidth}
                  scrollBarPosition={scrollBarPosition}
                />
              </ErrorHandler>
            )}
          </WithGradient>
          <Spacer size="xx" />
        </View>
      </>
    );
  }, [
    colors.smoothPink,
    containerWidth,
    contentWidth,
    currentTab,
    emptyOrders,
    getPageNumbers,
    handleContainerWidthChange,
    handleNext,
    handlePageNumber,
    handlePrevious,
    handleScroll,
    keyExtractor,
    listFooter,
    listViewItemSeparator,
    listViewKeyExtractor,
    memoizedEmptyListData,
    onProuctFlatListContentSizeChange,
    orderList,
    orderListItem,
    orderReturnList,
    pageNumber,
    pageNumberStyle,
    productTabBarRenderItem,
    productTabData,
    productsContainerGradientColors,
    renderProductItem,
    renderReturnListItem,
    scrollBarPosition,
    scrollBarWidth,
    scrollToEnd,
    scrollToStart,
    scrollX,
    styles.didYouForgotView,
    styles.excludeSubView,
    styles.flex,
    styles.forgetView,
    styles.listView,
    styles.pageBox,
    styles.paginationView,
    styles.productListView,
    styles.productTopView,
    styles.productView,
    totalPages,
    totalReturnPages,
  ]);

  const hideRetryModal = useCallback(() => {
    setRetryModalVisible(false);
  }, []);

  const onCloseCancelModal = useCallback(() => {
    setModalVisible(false);
    setSelectedSubReason(undefined);
  }, []);

  const onSetSelectedSubReason = useCallback((item: CancelOrderInput) => {
    setSelectedSubReason(item);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton={true}
          navigation={navigation}
          text={
            currentTab === 'order'
              ? t('orderListing.orderSummary')
              : t('orderTrack.return')
          }
          searchIcon={true}
          bagIcon={true}
          onPressNavigation={onBackPress}
        />
      </ErrorHandler>
      <View style={styles.flex}>
        {/* {EarnNoteView} */}
        <Spacer type="Vertical" size="xms" />
        <View style={styles.searchMainView}>
          <View style={styles.searchView}>
            {currentTab === 'order' ? (
              <View style={styles.searchSubView}>
                <ImageIcon icon="searchIcon" tintColor="text2" size="l" />
                <Spacer type="Horizontal" size="xms" />
                <Separator color="text2" Vertical height="xl" />
                <Spacer type="Horizontal" size="xm" />
                <TextInput
                  testID="txtOrderListingSearch"
                  placeholderTextColor={colors.text2}
                  keyboardType="default"
                  placeholder={
                    currentTab === 'order'
                      ? t('orderListing.searchProductName')
                      : t('orderListing.searchOrder')
                  }
                  value={searchTerm}
                  onChangeText={onSearch}
                  style={styles.textInputView}
                  allowFontScaling={false}
                />
                <Spacer type="Horizontal" size="sx" />
                {searchTerm?.length > 0 ? (
                  <TouchableOpacity onPress={() => onSearch('')}>
                    <ImageIcon icon="crossIcon" tintColor="text2" size="l" />
                  </TouchableOpacity>
                ) : (
                  <View />
                )}
              </View>
            ) : (
              <View style={styles.flex} />
            )}
            <TouchableOpacity style={styles.filter} onPress={showFilterModal}>
              <ImageIcon
                icon="filterIcons"
                style={styles.filterIcon}
                tintColor="text2"
              />
              <Spacer type="Horizontal" size="xms" />
              <Label
                text={t('orderListing.filter')}
                color="text2"
                size="m"
                fontFamily="Medium"
              />
            </TouchableOpacity>
          </View>
          {currentTab === 'order' && hideOldOrder && !hideLatest ? (
            <>
              <Spacer size="xms" />
              {newOrder ? (
                <View style={styles.orderCard}>
                  <Label
                    text={t('orderListing.fromOrder')}
                    color="text2"
                    size="m"
                    weight="400"
                    style={styles.flex}>
                    <Label
                      text={t('orderListing.jan25')}
                      color="text"
                      size="m"
                      weight="600"
                    />
                  </Label>
                  <Spacer size="xms" type="Horizontal" />
                  <TouchableOpacity
                    style={styles.orderCardBtn}
                    onPress={() => onSwitchYear()}>
                    <Label
                      text={t('orderListing.viewPrevious')}
                      color="newSunnyOrange"
                      size="m"
                      fontFamily="Medium"
                    />
                    <Spacer type="Horizontal" size="xm" />
                    <ImageIcon
                      icon="nextBlueIcon"
                      style={styles.rightArrow}
                      tintColor="newSunnyOrange"
                    />
                    <Spacer type="Horizontal" size="s" />
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.orderCard}>
                  <TouchableOpacity
                    style={styles.orderCardBtn}
                    onPress={() => onSwitchYear()}>
                    <ImageIcon
                      icon="nextBlueIcon"
                      style={styles.leftArrow}
                      tintColor="newSunnyOrange"
                    />
                    <Spacer type="Horizontal" size="xm" />
                    <Label
                      text={t('orderListing.viewLatest')}
                      color="newSunnyOrange"
                      size="m"
                      fontFamily="Medium"
                    />
                  </TouchableOpacity>
                  <Spacer size="mx" type="Horizontal" />
                  <Label
                    text={t('orderListing.tillOrder')}
                    color="text2"
                    size="m"
                    weight="400"
                    style={styles.flex}>
                    <Label
                      text={t('orderListing.dec24')}
                      color="text"
                      size="m"
                      weight="600"
                    />
                  </Label>
                </View>
              )}
            </>
          ) : (
            <View />
          )}
          {/* <Spacer size="xms" />
          <Pressable onPress={() => navigation.navigate('MembershipPage')}>
            <FastImage
              resizeMode="stretch"
              style={styles.imageCoupon}
              source={Icons?.orderBannerGif}
            />
          </Pressable> */}
          <Spacer size="xms" />
          <View style={styles.orderTebView}>
            <TouchableOpacity onPress={activateOrderTab}>
              <Label
                text={t('orderListing.myOrder')}
                color={currentTab === 'order' ? 'newSunnyOrange' : 'text2'}
                size="l"
                fontFamily="Medium"
              />
            </TouchableOpacity>
            <View style={styles.lineStyle} />
            <TouchableOpacity onPress={activateReturnTab}>
              <Label
                text={t('orderReturn.returns')}
                color={currentTab === 'Returns' ? 'newSunnyOrange' : 'text2'}
                size="l"
                fontFamily="Medium"
              />
            </TouchableOpacity>
          </View>
          <Spacer size="xms" />
        </View>
        <View style={styles.flex}>
          {isLoading || !filterData ? (
            <OrderLoader />
          ) : (
            <FlatList
              ref={scrollViewRef}
              showsVerticalScrollIndicator={false}
              keyExtractor={keyExtractor}
              data={memoizedEmptyListData}
              renderItem={renderItem}
            />
          )}
        </View>
      </View>
      {/* =========================order filter section====================== */}
      {modalVisibleFilter && (
        <ErrorHandler
          componentName={`${TAG} OrderFilter`}
          onErrorComponent={<View />}>
          <OrderFilter
            data={filterData}
            visible={modalVisibleFilter}
            onClose={hideFilterModal}
            clearFilter={handleClearFilters}
            applyFilter={handleApplyFilters}
            selectedFilters={tempSelectedFilters}
            onSelectFilter={handleTempFilterSelection}
          />
        </ErrorHandler>
      )}
      {/* =========================Retry Payment Modal====================== */}
      {retryModalVisible && (
        <ErrorHandler
          componentName={`${TAG} RetryPaymentModal`}
          onErrorComponent={<View />}>
          <RetryPaymentModal
            data={availablePaymentMethods}
            visible={retryModalVisible}
            onClose={hideRetryModal}
            onSelectMethod={onSelectedPaymentMethod}
            checkPayment={() =>
              checkPayment(availablePaymentMethods?.order_id, false)
            }
          />
        </ErrorHandler>
      )}
      {/* =========================Order Success Modal====================== */}
      {orderSuccessModel && (
        <ErrorHandler
          componentName={`${TAG} SuccessModal`}
          onErrorComponent={<View />}>
          <SuccessModal
            visible={orderSuccessModel}
            type="orderSuccess"
            title={t('checkOut.paymentDone')}
            titleStyle={styles.successTitle}
            onClose={() => setOrderSuccessModel(!orderSuccessModel)}
          />
        </ErrorHandler>
      )}
      {/* =========================cancel region list modal====================== */}
      {modalVisible && (
        <ErrorHandler
          componentName={`${TAG} OrderCancelModal`}
          onErrorComponent={<View />}>
          <OrderCancelModal
            visible={modalVisible}
            regions={regions}
            cancelLoader={cancelLoader}
            onClose={onCloseCancelModal}
            selectedSubReason={selectedSubReason}
            setSelectedSubReason={onSetSelectedSubReason}
            cancelOrders={cancelOrders}
          />
        </ErrorHandler>
      )}
      {/* =========================DeliveryInfo modal====================== */}
      {infoModel && (
        <DeliveryInfoModal
          visible={infoModel}
          onClose={() => setInfoModel(false)}
          infoIcon="earnCoin"
        />
      )}
    </SafeAreaView>
  );
};

export default React.memo(OrderListScene);

/**
 * Dentalkart End User App
 * https://github.com/facebook/react-native
 *
 *
 * @format
 */

import React, {useCallback, useEffect, useState} from 'react';
import {
  Platform,
  useColorScheme,
  PermissionsAndroid,
  ActivityIndicator,
  Text,
  TextInput,
  StatusBar,
  Linking,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {ApolloProvider} from '@apollo/react-hooks';
import Routes from './src/routes';
import './src/constants/i18n';
import light from './src/constants/themes/light.json';
import dark from './src/constants/themes/dark.json';
import Toast, {BaseToastProps} from 'react-native-toast-message';
import {CustomToast, Help } from 'components/atoms';
import NetInfo from '@react-native-community/netinfo';
import ErrorFallback from 'components/atoms/errorBoundary';
import Spinner from 'components/atoms/spinner';
import {navigationRef} from 'utils/navigationRef';
import {client} from 'api/apollo_client';
import {Provider} from 'react-redux';
import {store} from 'app-redux-store/store';
import codePush from 'react-native-code-push';
import messaging from '@react-native-firebase/messaging';
import {
  LogLevel,
  initialize,
  setCurrentScreenName,
} from '@microsoft/react-native-clarity';
import {enabledPushNotification} from 'components/organisms/analytics-Events/analyticsCallsWebAndFire';
import ErrorBoundary from 'react-native-error-boundary';
import {checkDeviceSecurity} from 'utils/jailMonkey';
import JailBroken from 'components/atoms/jailBroken';
import Orientation from 'react-native-orientation-locker';
import ReactMoE from 'react-native-moengage';
import {
  MoEInitConfig,
  MoEPushConfig,
  MoEngageLogConfig,
  MoEngageLogLevel,
} from 'react-native-moengage';
import DeviceInfo from 'react-native-device-info';
import {clarity} from 'config/environment';
import Config from 'react-native-config';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Sizes} from 'common';
// import SplashScreen from 'scenes/splash';
import NetworkLoggerProvider from './src/NetworkLogger/NetworkLoggerProvider';
import {ScrollProvider} from 'scenes/homeScreen/homePage/contextApi/scrollContext';
import {NativeModules} from 'react-native';
import {debugError, debugLog} from 'utils/debugLog';
import UpgradeModal from 'components/molecules/upgradeModal';
import {getForceUpdateVersion} from 'services/account';
import {compareSemanticVersions} from 'utils/compareVersions';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Settings} from 'react-native-fbsdk-next';
import {requestMultiple, PERMISSIONS} from 'react-native-permissions';
import {isIos} from 'utils/utils';
import OfflineNotice from 'components/atoms/offlineNotice';

const clarityConfig = {
  logLevel: LogLevel.Verbose,
};

if (Text.defaultProps == null) {
  Text.defaultProps = {};
  Text.defaultProps.allowFontScaling = false;
}

if (TextInput.defaultProps == null) {
  TextInput.defaultProps = {};
  TextInput.defaultProps.allowFontScaling = false;
}

const App = () => {
  // Group all useState hooks together at the top
  const scheme = useColorScheme();
  const [deviceChecked, setDeviceChecked] = useState<Boolean | null>(false);
  const [isAppReady, setAppReady] = useState(false);
  const [showUpgrade, setShowUpgrade] = useState(false);
  const [appConfig, setAppConfig] = useState({});
  const [connected, setConnected] = useState(true);

  // Define constants outside of render
  const LightTheme = {
    dark: false,
    colors: light,
  };

  const DarkTheme = {
    dark: true,
    colors: dark,
  };

  const toastProps = {
    text1Props: {
      numberOfLines: 4,
    },
    text2Props: {
      numberOfLines: 4,
    },
  };

  async function checkPermission() {
    // const enabled = await messaging().hasPermission();
    // if (!enabled) {
    //   await messaging().requestPermission();
    // }
    // if (Platform.OS === 'android') {
    //   await PermissionsAndroid.request(
    //     PermissionsAndroid.PERMISSIONS.POST_NOTIFICATION,
    //   );
    //   enabledPushNotification();
    // }
    ReactMoE.requestPushPermissionAndroid();
    requestMultiple(
      Platform.OS === 'ios'
        ? [PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]
        : [PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION],
    ).then(result => {});
  }
  const testEIEvent = () => {
    const {EISDKModule} = NativeModules;
    if (!EISDKModule) {
      debugError('❌ EISDKModule is undefined. Check if the module is linked.');
      return;
    }

    EISDKModule.initializeSDK('dentalkart', 'optional_user_id')
      .then(res => debugLog(res))
      .catch(err => debugError(err));
  };

  // Group all useEffects together
  useEffect(() => {
    Settings.initializeSDK();
    Settings.setAutoLogAppEventsEnabled(true);
    Settings.setAdvertiserIDCollectionEnabled(true);
    initialize(clarity, clarityConfig);
    checkPermission();
    const moEInitConfig = new MoEInitConfig(
      MoEPushConfig.defaultConfig(),
      new MoEngageLogConfig(MoEngageLogLevel.VERBOSE, true),
    );
    ReactMoE.initialize('WK6FCL243291LP34PE424WUH', moEInitConfig);
    ReactMoE.showInApp();
    if (Platform.OS === 'android') {
      testEIEvent();
    }
  }, []);

  const handleUpgrade = () => {
    Linking.openURL(appConfig?.store_url);
  };

  async function checkForceUpgrade() {
    try {
      const platform = Platform.OS;
      const currentVersion = DeviceInfo.getVersion();
      const {data} = await getForceUpdateVersion(platform);
      if (data) {
        setAppConfig(data);
        const shouldUpgrade =
          compareSemanticVersions(currentVersion, data.min_supported_version) <
          0;
        setShowUpgrade(shouldUpgrade);
      } else {
        setShowUpgrade(false);
      }
    } catch (error) {
      debugError('Error checking app version:', error);
      setShowUpgrade(false);
    }
  }

  useEffect(() => {
    Orientation.lockToPortrait();
    return () => Orientation.unlockAllOrientations();
  }, []);

  // useEffect(() => {
  //   const verifyDeviceSecurity = async () => {
  //     const isSecure = await checkDeviceSecurity();
  //     setDeviceChecked(!isSecure);
  //   };
  //   // verifyDeviceSecurity();
  // }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAppReady(true);
    }, 2040);
    return () => clearTimeout(timer);
  }, []);

  const handleConnectivityChange = useCallback(
    ({isConnected}) => {
      setConnected(isConnected);
    },
    [connected],
  );

  useEffect(() => {
    if (!isIos) {
      const unsubscribe = NetInfo.addEventListener(handleConnectivityChange);
      return () => {
        unsubscribe();
      };
    }
  }, []);

  const retry = () => {
    NetInfo.fetch().then(handleConnectivityChange);
  };

  useEffect(() => {
    checkForceUpgrade();
  }, []);

  // Handle loading states
  if (deviceChecked === null) {
    return <ActivityIndicator size={'large'} />;
  }

  if (deviceChecked === true) {
    return <JailBroken />;
  }

  const toastConfig = {
    warn: props => <CustomToast {...props} {...toastProps} />,
    cart: props => <CustomToast {...props} {...toastProps} />,
    wishList: props => <CustomToast {...props} {...toastProps} />,
    success: props => <CustomToast {...props} {...toastProps} />,
    error: props => <CustomToast {...props} {...toastProps} />,
    info: props => <CustomToast {...props} {...toastProps} />,
    visibilityTime: 8000,
  };

  return (
    <GestureHandlerRootView style={{flex: Sizes.x}}>
      <StatusBar barStyle={'dark-content'} backgroundColor="#fff" translucent />
      <SafeAreaProvider>
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <ApolloProvider client={client}>
              <NavigationContainer
                ref={navigationRef}
                onNavigationStateChange={(_: any, newState: any) => {
                  setCurrentScreenName(newState.routes[0].routeName);
                }}
                theme={LightTheme}>
                  <Provider store={store}>
                <>
                  <NetworkLoggerProvider>
                    <Spinner />
                    <ScrollProvider>
                      <Routes isAppReady={isAppReady} />
                      {connected && showUpgrade && (
                        <UpgradeModal
                          visible={showUpgrade}
                          onUpgradePress={handleUpgrade}
                          onSkipPress={() => setShowUpgrade(false)}
                          appConfig={appConfig}
                        />
                      )}
                    </ScrollProvider>
                    <Toast config={toastConfig} swipeable={false} />
                    {/* {isAppReady && <Help />} */}
                    {!connected && <OfflineNotice retry={retry} />}
                  </NetworkLoggerProvider>
                </>
            </Provider>
              </NavigationContainer>
          </ApolloProvider>
        </ErrorBoundary>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

// Choose one export method
export default App;

import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    cardContainer: {
      flexDirection: 'row',
      padding: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: '#e0e0e0',
      borderRadius: Sizes.mx,
      backgroundColor: colors.background,
    },
    itemView: {
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: Sizes.z,
      borderRadius: Sizes.xms,
      borderColor: colors.grey2,
      padding: Sizes.sx,
      flex: checkDevice() ? null : Sizes.x,
    },
    productImage: {
      height: Sizes.exl,
      width: Sizes.exl,
    },
    productDetails: {
      flex: Sizes.xs,
      justifyContent: 'space-between',
    },
    productTitle: {
      textTransform: 'capitalize',
    },
    coinContainer: {
      flexDirection: 'row',
      paddingVertical: Sizes.s,
      alignItems: 'center',
    },
    coinView1: {
      justifyContent: 'center',
    },
    coinView2: {
      marginTop: Sizes.s,
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },

    container: {
      flex: Sizes.x,
    },
    header: {
      paddingHorizontal: Sizes.mx,
      paddingVertical: Sizes.xm,
      backgroundColor: colors.background,
      marginHorizontal: Sizes.xs,
      flex: Sizes.x,
      width: '80%',
    },
    descriptionKey: {
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xm,
      marginHorizontal: -Sizes.l,
      shadowColor: '#000',
      backgroundColor: 'white',
      shadowOffset: {
        width: 0,
        height: 0,
      },
      shadowOpacity: 0.18,
      shadowRadius: Sizes.x,
      elevation: Sizes.x,
    },
    section1: {paddingHorizontal: Sizes.l, flex: Sizes.x},
    section2: {paddingRight: Sizes.l, flex: Sizes.xs + Sizes.z},
    descText: {
      color: colors.text2,
      fontFamily: Fonts.Regular,
    },
    tags: {
      p: {
        color: colors.text2,
        fontFamily: Fonts.Medium,
      },
      li: {color: colors.text2, fontFamily: Fonts.Medium},
      ul: {color: colors.text2, fontFamily: Fonts.Medium},
      div: {color: colors.text2, fontFamily: Fonts.Medium},
      body: {
        color: colors.text2,
        whiteSpace: 'normal',
        fontFamily: Fonts.Medium,
      },
      h1: {color: colors.text2, fontFamily: Fonts.Medium},
      h3: {
        color: colors.textLight,
        fontSize: Sizes.mx,
        fontFamily: Fonts.Medium,
      },
      span: {color: colors.textLight, fontFamily: Fonts.Medium},
      strong: {
        color: colors.text2,
        fontSize: Sizes.mx,
        fontFamily: Fonts.Medium,
      },
      td: {color: colors.textLight, fontFamily: Fonts.Medium},
    },
    tagsStyle: {
      body: {
        whiteSpace: 'normal',
        color: colors.primary,
        fontSize: Sizes.l + Sizes.xs,
      },
    },
    listComponents: {backgroundColor: colors.background, flex: Sizes.x},
    searchBarStyle: {
      width: '90%',
      height: Sizes.xx4l,
      borderRadius: Sizes.xxl,
      borderColor: colors.border,
      alignSelf: 'center',
    },
    NoQuesAvailText: {
      padding: Sizes.xm,
      color: colors.textLight,
    },
    viewAllQuestions: {
      borderTopWidth: 0.5,
      paddingVertical: Sizes.xm,
      paddingLeft: Sizes.s,
    },
    viewAllText: {
      color: colors.primary,
    },
    quesErrorText: {
      color: colors.textError,
      paddingTop: Sizes.s,
      fontSize: Sizes.m,
    },
    postQuesButton: {
      marginTop: Sizes.xm,
      width: '50%',
      height: Sizes.xx4l,
      alignSelf: 'center',
      backgroundColor: colors.sunnyOrange,
    },
    postQuesButtonText: {
      color: colors.textLight,
      backgroundColor: colors.sunnyOrange,
      height: Sizes.xx4l,
    },
    faqTabContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: '4%',
      borderTopWidth: 0.5,
      borderBottomWidth: 0.5,
    },
    noDataText: {
      fontWeight: '600',
      color: colors.textLight,
    },
    closeButton: {
      alignSelf: 'flex-end',
      padding: Sizes.xm,
    },

    postQuesHead: {
      padding: Sizes.xms,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 0.5,
      paddingBottom: '5%',
    },

    postQuesTextInput: {
      borderWidth: 0.5,
      borderRadius: Sizes.xm,
      alignItems: 'flex-start',
      flex: Sizes.x,
      maxHeight: '50%',
    },

    fAQ: {color: colors.primary},
    headerText: {
      alignSelf: 'flex-start',
    },
    modelView: {
      width: '100%',
      paddingBottom: Sizes.m,
      backgroundColor: colors.background,
      maxHeight: '95%',
    },
    questionsFlex: {
      flex: Sizes.x,
      paddingBottom: Sizes.xm,
    },
    loader: {
      alignItems: 'center',
      flex: Sizes.x,
      justifyContent: 'center',
    },

    postQuestionsModel: {
      paddingHorizontal: Sizes.l,
      backgroundColor: colors.background,
      height: Sizes.ex3l,
    },
    separater: {backgroundColor: colors.smoothBlue, height: Sizes.s},
    mainView: {flex: Sizes.x},
  });

export default styles;

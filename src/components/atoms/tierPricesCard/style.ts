import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    priceView: {
      padding: Sizes.m,
      paddingBottom: 0,
    },
    transformedTierPriceView: {
      flex: Sizes.x,
      borderColor: colors.sunnyOrange2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.sx,
    },
    transformedTierView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    fRow: {
      flexDirection: 'row',
    },
    separator: {width: 5},
    tierPrices: {
      marginBottom: Sizes.sx,
      borderRadius: Sizes.xl,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: Sizes.sx,
      paddingHorizontal: Sizes.l,
    },
    listContainer: {
      borderColor: colors.grey5,
      borderWidth: 1.5,
      borderRadius: Sizes.xm,
    },
    deliveryCont: {
      flexDirection: 'row',
      backgroundColor: colors.categoryTitle,
      borderTopLeftRadius: Sizes.xm,
      borderTopRightRadius: Sizes.xm,
    },
    thLeft: {
      flex: 1.5,
      height: 33,
      justifyContent: 'center',
      alignItems: 'center',
      borderRightColor: colors.grey5,
      borderRightWidth: Sizes.x,
    },
    thRight: {
      flex: Sizes.x,
      height: 33,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tableItemLeft: {
      flex: 1.5,
      borderTopColor: colors.grey5,
      borderRightColor: colors.grey5,
      height: Sizes.x3l,
      borderTopWidth: Sizes.x,
      borderRightWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tableItemRight: {
      flex: Sizes.x,
      borderTopColor: colors.grey5,
      height: Sizes.x3l,
      borderTopWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

export default styles;

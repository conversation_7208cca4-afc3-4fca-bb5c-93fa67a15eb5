import React, {useMemo} from 'react';
import {
  TouchableOpacity,
  View,
  FlatList,
  TouchableWithoutFeedback,
} from 'react-native';
import Modal from 'react-native-modal';
import {useTheme} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {t} from 'i18next';
import {ImageIcon, Label, Separator, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import moment from 'moment';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import {Button} from 'components/molecules';

type Props = {
  visible?: boolean;
  onClose?: () => void;
  data?: OrderDetailsV1[];
  onSelectReturn: (sku: string, returnId: number) => void;
};
const PreviousOrderReturn = (props: Props) => {
  const {visible, onClose, data, onSelectReturn} = props;
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const renderItem = (item, index) => {
    return (
      <View key={index} style={styles.historyView}>
        <Button
          onPress={() => {
            onClose();
            onSelectReturn(item?.sku, item.return_id);
          }}
          radius="xms"
          size="zero-height"
          selfAlign="flex-end"
          weight="500"
          text={t('buttons.returnDetails')}
          type={'bordered'}
          labelColor={'categoryTitle'}
          paddingHorizontal="m"
          style={styles.trackBtn}
        />
        <Spacer size="xm" />
        <Label
          text={moment(item.created_at).format('YYYY-MM-DD, hh:mm')}
          fontFamily="SemiBold"
          size="mx"
          color="text"
        />
        <Spacer size="xm" />
        <Label
          text={`${t('orderTrack.orderedID')} - ${item.order_id}`}
          fontFamily="Medium"
          size="mx"
          color="text2"
        />
        <Spacer size="xm" />
        <View style={styles.row}>
          <Label
            text={`${t('orderReturn.returnId')} - ${item?.return_id}`}
            fontFamily="Medium"
            size="mx"
            color="text2"
          />
          <Spacer size="l" type="Horizontal" />
          <Separator height="xxl" color="grey" Vertical />
          <Spacer size="l" type="Horizontal" />
          <Label
            text={`${t('orderTrack.sku')} - ${item?.sku}`}
            fontFamily="Medium"
            size="mx"
            color="text2"
          />
        </View>
        <Spacer size="xm" />
        <Label
          text={`${t('orderTrack.totalItem')} - ${item?.qty}`}
          fontFamily="Medium"
          size="mx"
          color="categoryTitle2"
        />
        <Spacer size="xm" />
        <View style={styles.imageView}>
          <FastImage
            resizeMode="contain"
            style={styles.imageSizeMain}
            source={{
              uri: getImageUrl(item?.image, 'product'),
            }}
          />
        </View>
      </View>
    );
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.3}
      style={styles.modalStyle}>
      <View style={styles.imageModel} pointerEvents="box-none">
        <View style={[styles.subImageView, {paddingBottom: insets.bottom}]}>
          <TouchableWithoutFeedback onPress={() => onClose()}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={() => onClose()}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x5l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <View style={styles.modelSubView}>
            <FlatList
              keyExtractor={(item, index) => index.toString()}
              data={data}
              renderItem={({item, index}) => renderItem(item, index)}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PreviousOrderReturn;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    processingContinuer: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    orderSubTrack: {
      backgroundColor: colors.background,
    },
    row: {
      flexDirection: 'row',
      alignContent: 'center',
    },
    itemCenter: {
      alignItems: 'center',
    },
    trackLineInactive: {
      height: Sizes.xxxl + Sizes.xs,
      backgroundColor: colors.green2,
      width: Sizes.xs,
    },
    trackLine: {
      height: Sizes.xxxl + Sizes.xs,
      backgroundColor: colors.green2,
      width: Sizes.xs,
    },
    subHeadingShoed: {
      color: colors.primary,
    },
    mainImage: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-start',
    },
    image: {
      borderWidth: Sizes.x,
      borderColor: colors.textTransparent,
      borderRadius: Sizes.xm,
      justifyContent: 'center',
      marginRight: Sizes.xms,
      height: Sizes.x5l,
      width: Sizes.x5l,
    },
    imageSubView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      backgroundColor: colors.textTransparent,
      borderColor: colors.textTransparent,
      justifyContent: 'center',
      marginRight: Sizes.xms,
      width: Sizes.x5l,
      height: Sizes.x5l,
      alignItems: 'center',
    },
    imageSizeMain: {
      width: Sizes.ex,
      height: Sizes.ex,
      borderRadius: Sizes.xm,
    },
    trackImageSize: {
      width: Sizes.x34,
      height: Sizes.x34,
      borderRadius: Sizes.xm,
    },
    imageSizeReturn: {
      width: Sizes.x9l,
      height: Sizes.x9l,
    },
    imageView: {
      alignSelf: 'flex-start',
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
    },
    mainRejected: {
      paddingTop: Sizes.sx,
      flex: Sizes.x,
    },
    rejectedImage: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      padding: Sizes.s,
      borderColor: colors.textTransparent,
    },
    historyView: {
      padding: Sizes.m,
      borderRadius: Sizes.xms,
      backgroundColor: colors.background,
      marginTop: Sizes.m,
    },
    trackBtn: {
      height: Sizes.x26,
    },
    trackOrderBtn: {
      height: Sizes.x5l,
    },
    subHeading: {
      color: colors.green2,
    },
    renderSubCards: {
      marginTop: Sizes.m,
      padding: Sizes.m,
      borderRadius: Sizes.xm,
      backgroundColor: colors.whiteColor,
      width: '100%',
    },
    reqView: {
      flexDirection: 'row',
      width: '100%',
      alignItems: 'center',
    },
    reasonTextView: {
      width: '50%',
    },
    estimateView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    flexOne: {
      flex: Sizes.x,
    },
    flexTwo: {
      flex: Sizes.xs,
    },
    fRow: {
      flexDirection: 'row',
    },
    durationsBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    returnDisable: {
      backgroundColor: colors.grey70,
      height: Sizes.xl,
      width: Sizes.xl,
      borderRadius: Sizes.xms,
      position: 'absolute',
      zIndex: Sizes.x,
    },
  });

export default styles;

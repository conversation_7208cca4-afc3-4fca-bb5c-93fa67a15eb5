import React from 'react';
import {TouchableOpacity, View} from 'react-native';

import EditWishList from '../edit-Wishlist';
import {useTheme} from '@react-navigation/native';

type Props = {
  onUpdate?: () => void;
  modalType?: 'create' | 'edit';
};

const WishListHeader = ({onUpdate, modalType}: Props) => {
  return <EditWishList modalType={modalType} onUpdate={onUpdate} />;
};

export default WishListHeader;

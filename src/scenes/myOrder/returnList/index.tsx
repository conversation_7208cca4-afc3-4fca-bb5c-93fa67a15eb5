import {useMemo, useRef} from 'react';
import React, {useCallback, useEffect, useState} from 'react';
import {
  FlatList,
  Platform,
  Text,
  TouchableOpacity,
  View,
  Alert,
} from 'react-native';
import {RouteProp, useIsFocused, useTheme} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import {
  ImageIcon,
  Label,
  Spacer,
  CheckBox,
  OrderReturnDetailModal,
} from 'components/atoms';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {
  GetPresignedUrl,
  ReturnReasonList,
  ReturnRequestApi,
  ReturnableItemsList,
} from 'services/orders';
import {<PERSON><PERSON>, <PERSON>er, InputBox} from 'components/molecules';
import {RootStackParamsList} from 'routes';
import {setLoading} from 'app-redux-store/slice/appSlice';
import stylesWithOutColor from './style';
import {ImagePath} from 'config/apiEndpoint';
import {AnalyticsEvents} from 'components/organisms';
import {
  check,
  request,
  PERMISSIONS,
  openSettings,
} from 'react-native-permissions';
import DeviceInfo from 'react-native-device-info';
import {
  handleErrMsg,
  videoSizeLimit,
  imageSizeLimit,
  isVideoUrl,
  uriToBlob,
} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import {OrderLoader} from 'skeletonLoader';
import {useDebouncedCallback} from 'use-debounce';
import {debugLog} from 'utils/debugLog';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList>;
};

const OrderReturnListScene = ({navigation, route}: Props) => {
  const TAG = 'OrderReturnListScreen';
  const orderId = route.params?.orderId;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const [orderDetail, setOrderDetail] = useState<OrderDetailsV1 | null>(null);
  const [returnReasons, setReturnReasons] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [subReasons, setSubReasons] = useState([]);
  const [error, setError] = useState('');
  const [action, setAction] = useState([]);
  const [currentReturnForm, setCurrentReturnForm] = useState({});
  const currentReturnFormRef = useRef(currentReturnForm);
  const [returnFormItems, setReturnFormItems] = useState([]);
  const [selectedItemForReturn, setSelectedItemForReturn] = useState({});
  const isFocused = useIsFocused();
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const [apiLevel, setApiLevel] = useState(0);
  const [fileError, setFileError] = useState('');
  const [description, setDescription] = useState('');
  const [loader, setLoader] = useState(false);

  const getOrderDetailsData = useCallback(async () => {
    const {data} = await ReturnableItemsList(orderId);
    setOrderDetail(data);
  }, []);

  const getReturnReasons = useCallback(async () => {
    const {data} = await ReturnReasonList();
    if (data && data?.reasons_actions_list?.length > 0) {
      setReturnReasons(data?.reasons_actions_list);
    }
  }, []);

  const updateReturnFormForUIAndRef = useCallback((returnForm: {}) => {
    setCurrentReturnForm(returnForm);
    currentReturnFormRef.current = returnForm;
  }, []);

  useEffect(() => {
    DeviceInfo.getApiLevel().then(level => {
      setApiLevel(level);
    });
    AnalyticsEvents('RETURN', 'Return screen', {}, userInfo, isLoggedIn);
  }, []);

  useEffect(() => {
    setLoader(true);
    Promise.all([getReturnReasons(), getOrderDetailsData()]).then(() => {
      setLoader(false);
    });
  }, [isFocused]);

  const onSaveReturnForm = async () => {
    handleUpdateQty.flush();
    let checkValid = await validate();
    if (checkValid) {
      const items = returnFormItems.filter(
        form => form.sku !== currentReturnFormRef.current?.sku,
      );
      setReturnFormItems([...items, currentReturnFormRef.current]);
      updateReturnFormForUIAndRef({});
      setModalVisible(false);
    }
  };

  const onClearForm = useCallback(() => {
    const items = returnFormItems.filter(
      form => form.sku !== currentReturnFormRef.current?.sku,
    );
    setReturnFormItems([...items]);
    updateReturnFormForUIAndRef({});
    setModalVisible(false);
  }, [returnFormItems, updateReturnFormForUIAndRef]);

  const requestCameraPermission = async type => {
    const photoPermissions =
      Platform.OS === 'android'
        ? apiLevel < 33
          ? PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE
          : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
        : PERMISSIONS.IOS.PHOTO_LIBRARY;
    const cameraPermissions =
      Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.CAMERA
        : PERMISSIONS.IOS.CAMERA;
    const permission =
      type === 'gallery' ? photoPermissions : cameraPermissions;
    request(permission).then(statuses => {
      check(permission).then(status => {
        if (status === 'granted') {
          handleDocumentSelection(type);
        } else {
          request(permission).then(result => {
            if (result === 'granted') {
              handleDocumentSelection(type);
            } else {
              setTimeout(() => {
                Alert.alert(
                  t('validations.permissionAllow'),
                  t('validations.permissionCameraMsg'),
                  [
                    {text: 'No'},
                    {
                      text: 'Yes',
                      style: 'cancel',
                      onPress: () => openSettings().catch(() => {}),
                    },
                  ],
                );
              }, 200);
            }
          });
        }
      });
    });
  };

  const handleDocumentSelection = async type => {
    try {
      let response;
      if (type === 'camera') {
        await launchCamera({mediaType: 'photo'}, data => {
          if (data.didCancel) {
            debugLog('User cancelled image picker');
          } else if (data.errorCode) {
            debugLog('ImagePicker Error: ', data.errorMessage);
          } else {
            response = data?.assets?.[0];
          }
        });
      } else {
        const result = await launchImageLibrary({mediaType: 'mixed'});
        if (result?.errorCode) {
          setFileError(t('validations.fileNotSupport'));
          setTimeout(() => {
            setFileError('');
          }, 4000);
          return;
        }
        response = result?.assets?.[0];
      }
      if (
        response?.fileSize >
        (isVideoUrl(response?.uri) ? videoSizeLimit : imageSizeLimit)
      ) {
        setFileError(t('validations.fileSizeReq'));
        setTimeout(() => {
          setFileError('');
        }, 4000);
        return;
      }
      dispatch(setLoading(true));
      if (response) {
        const {uri, fileName, type} = response;
        const formData = new FormData();
        formData.append('file', {
          uri: uri,
          name: fileName,
          type: type,
        });
        const {data} = await GetPresignedUrl(formData);
        let fileBlob = await uriToBlob(uri);
        if (data && data?.file_url) {
          const newReturnForm = {
            ...currentReturnFormRef.current,
            attachments: [
              ...(currentReturnFormRef.current?.attachments || []),
              data?.file_url,
            ],
          };
          updateReturnFormForUIAndRef(newReturnForm);
        } else {
          setFileError(handleErrMsg(data));
          setTimeout(() => {
            setFileError('');
          }, 4000);
        }
      } else {
        showErrorMessage(t('validations.someThingWrong'));
      }
      dispatch(setLoading(false));
    } catch (err) {
      dispatch(setLoading(false));
      console.warn(err);
    }
  };

  const removeDocument = index => {
    const newAttachment =
      currentReturnFormRef.current?.attachments?.length === 1
        ? []
        : currentReturnFormRef.current?.attachments?.filter(
            (_, i) => i !== index,
          );

    const newReturnForm = {
      ...currentReturnFormRef.current,
      attachments: newAttachment,
    };
    updateReturnFormForUIAndRef(newReturnForm);
    validate();
  };

  const onCardPress = useCallback(
    (item: OrderItems) => {
      setSelectedItemForReturn(item);
      const selectedForm = returnFormItems?.filter(
        data => data?.sku === item?.sku,
      );
      if (selectedForm?.length > 0) {
        const {reason_id} = selectedForm[0];
        if (reason_id) {
          const reason = returnReasons
            ?.filter(data => data?.enable)
            ?.filter(data => data?.id === reason_id);
          if (reason.length !== 0) {
            const {sub_reasons, return_actions} = reason[0];
            setSubReasons(sub_reasons);
            setAction(return_actions);
          }
          updateReturnFormForUIAndRef(selectedForm[0]);
        } else {
          onEditReturnItem(item);
        }
      } else {
        onEditReturnItem(item);
      }
      setModalVisible(true);
      setError('');
    },
    [returnReasons, currentReturnForm, returnFormItems],
  );

  const onEditReturnItem = item => {
    const dropdown1Disable =
      (item.is_tat_expired && item.max_qty_returnable > 0) ||
      item?.non_returnable ||
      item.is_free_product
        ? true
        : false;
    let obj = {
      qty: 1,
      sku: item?.sku,
    };
    if (dropdown1Disable) {
      const reason = returnReasons
        ?.filter(data => data?.enable)
        ?.filter(data => data?.id === 2);
      if (reason.length > 0) {
        const {sub_reasons, return_actions, id} = reason[0];
        const actionList = return_actions?.filter(data => data?.id === 3);
        setSubReasons(sub_reasons);
        setAction(return_actions);
        obj['reason_id'] = id;
        obj['action_id'] = actionList.length > 0 ? actionList[0].id : '';
      }
    } else {
      setSubReasons([]);
      setAction([]);
    }

    const newReturnForm = {
      ...currentReturnFormRef.current,
      ...obj,
    };
    updateReturnFormForUIAndRef(newReturnForm);
  };

  const submitReturnRequest = async () => {
    handleUpdateQty.flush();
    let formItems = [];
    const data = returnFormItems.filter(item => !item?.action_id);
    if (data?.length === 0) {
      returnFormItems.map(item => {
        if (item?.action_id) {
          if (!item.sub_reason_id) {
            delete item.sub_reason_id;
          }
          formItems.push(item);
        }
      });
      let formattedData = {
        order_id: orderId,
        description: description.trim(),
        items: formItems,
        source: 'app',
      };
      try {
        if (orderId) {
          dispatch(setLoading(true));
          const {data, status} = await ReturnRequestApi(formattedData);
          dispatch(setLoading(false));
          if (status && data) {
            AnalyticsEvents(
              'RETURN_ORDER',
              'Return Order',
              data,
              userInfo,
              isLoggedIn,
            );
            updateReturnFormForUIAndRef({});
            setReturnFormItems([]);
            showSuccessMessage(t('orderReturn.returnSuccess'));
            navigation.navigate('OrderReturnTermsScene', {orderId});
          } else {
            showErrorMessage(handleErrMsg(data));
            getOrderDetailsData();
          }
        } else {
          debugLog('error oderId..................');
        }
      } catch (err) {
        showErrorMessage(err);
      }
    } else {
      showErrorMessage(t('validations.fillAllForm'));
    }
  };

  const editPress = useCallback(
    item => {
      let data = returnFormItems?.find(e => e?.sku === item?.sku);

      const newReturnForm = {
        ...currentReturnFormRef.current,
        ...data,
      };
      updateReturnFormForUIAndRef(newReturnForm);
      onCardPress(item);
    },
    [returnFormItems],
  );

  useEffect(() => {
    if (error !== '') {
      validate();
    }
  }, [currentReturnForm]);

  const validate = () => {
    const errors = {};
    let isValid = true;
    const dropdown1Disable =
      (selectedItemForReturn.is_tat_expired &&
        selectedItemForReturn.max_qty_returnable > 0) ||
      selectedItemForReturn?.non_returnable ||
      selectedItemForReturn.is_free_product
        ? true
        : false;
    const dropdown2Disable =
      selectedItemForReturn?.non_returnable ||
      (subReasons.length === 0 && currentReturnFormRef.current.reason_id);

    const dropdown3Disable =
      dropdown1Disable ||
      (action.length === 0 && currentReturnFormRef.current.reason_id);
    const validations = [
      {
        field: 'reason_id',
        message: t('validations.selectReason'),
        required: !dropdown1Disable,
      },
      {
        field: 'sub_reason_id',
        message: t('validations.selectSubReason'),
        required: !dropdown2Disable,
      },
      {
        field: 'action_id',
        message: t('validations.selectAction'),
        required: !dropdown3Disable,
      },
      {
        field: 'attachments',
        message: t('validations.attachmentReq'),
        min: 2,
        required: true,
        matchMessage: t('validations.attachmentsMin2'),
      },
    ];

    for (const {field, message, min, required, matchMessage} of validations) {
      const value = field
        ?.split('.')
        .reduce((obj, key) => obj?.[key], currentReturnFormRef.current);

      if (
        (!value && required) ||
        (field === 'attachments' && value.length === 0)
      ) {
        errors[field] = message;
        isValid = false;
      } else if (min > 0 && Array.isArray(value) && min && value.length < min) {
        errors[field] = matchMessage;
        isValid = false;
      }
    }
    setError(errors);
    return isValid;
  };

  const handleCheckboxChange = (item, newValue) => {
    if (newValue) {
      const newReturnForm = {
        ...currentReturnFormRef.current,
        item,
      };
      updateReturnFormForUIAndRef(newReturnForm);

      setReturnFormItems(
        returnFormItems?.filter(data => data.sku !== item?.sku),
      );
    } else {
      setReturnFormItems([...returnFormItems, item]);
    }
  };

  const renderItem = ({item, index}: {item: OrderItems; index: number}) => {
    const isSelected = returnFormItems.some(data => data.sku === item.sku);
    const selectedItem = returnFormItems?.find(data => data?.sku == item?.sku);
    return (
      <View key={index} style={styles.itemView}>
        {returnFormItems?.length > 0 ? (
          <View style={styles.checkBoxView}>
            {item.non_returnable ? (
              <View style={styles.checkBoxStyle} />
            ) : (
              <CheckBox
                key={item.sku}
                value={isSelected}
                onValueChange={newValue => handleCheckboxChange(item, newValue)}
                borderColor="text"
                selected={isSelected}
                style={styles.checkBoxStyle}
                disabled={
                  item?.error && item.error === 'Non-returnable item'
                    ? true
                    : false
                }
              />
            )}
          </View>
        ) : null}
        <View style={styles.cardView}>
          <TouchableOpacity
            disabled={item?.error || selectedItem?.reason_id ? true : false}
            onPress={() => {
              setModalVisible(true);
              setError('');
              onCardPress(item);
            }}
            style={styles.subCardView}>
            <View style={styles.imageBox}>
              <ImageIcon
                resizeMode="contain"
                style={styles.imageSize}
                sourceType="url"
                source={ImagePath.product + item?.image}
              />
            </View>
            <Spacer size="xm" type="Horizontal" />
            <View style={styles.nameView}>
              <View style={styles.flexOne}>
                <Label
                  numberOfLines={1}
                  text={item?.name}
                  fontFamily="Medium"
                  size="m"
                  color="text"
                  ellipsizeMode="tail"
                />
                <Spacer size="s" />
                <Label
                  text={`${t('orderReturn.orderAmount')} ${item?.price}`}
                  fontFamily="Medium"
                  size="m"
                  color="text"
                />
                <Spacer size="s" />
                <View style={styles.qtyView}>
                  <Label
                    text={`${t('orderReturn.qty1')}${item?.qty_ordered}`}
                    fontFamily="Medium"
                    size="m"
                    color="text"
                  />
                  <View style={styles.lineStyle} />
                  <Label
                    text={`${t('orderReturn.returnableQty')} - ${
                      item?.max_qty_returnable
                    }`}
                    size="m"
                    fontFamily="Medium"
                    color="text"
                  />
                </View>

                {item.error || (isSelected && !selectedItem?.reason_id) ? (
                  <Text style={styles.error}>
                    {item?.error ? item?.error : t('validations.fillAllForm')}
                  </Text>
                ) : null}
              </View>
              {!item.error ? (
                selectedItem?.reason_id ? (
                  <View style={styles.savedView}>
                    <View style={styles.reqSave}>
                      <Label
                        text={t('orderReturn.requestSaved')}
                        weight="500"
                        size="m"
                        color="newSunnyOrange"
                      />
                    </View>
                    <TouchableOpacity
                      onPress={() => editPress(item)}
                      style={styles.editView}>
                      <ImageIcon icon={'edit'} size="xl" tintColor="black" />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <ImageIcon
                    icon={'arrowLeft'}
                    style={styles.rightArrow}
                    size="xl"
                    tintColor="blackColor"
                  />
                )
              ) : null}
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const handleUpdateQty = useDebouncedCallback((count: number) => {
    onChangeQty(count);
  }, 400);

  const onChangeQty = count => {
    const previousReturnForm = currentReturnFormRef.current;
    const newReturnForm = {
      ...previousReturnForm,
      qty: Number(count),
    };
    updateReturnFormForUIAndRef(newReturnForm);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton={true}
          navigation={navigation}
          text={t('orderTrack.return')}
          bagIcon={true}
          searchIcon={true}
        />
      </ErrorHandler>
      {loader ? (
        <OrderLoader />
      ) : (
        <View style={styles.subView}>
          <View style={styles.flatListView}>
            <Spacer type="Vertical" size="sx" />
            <Label
              text={t('orderReturn.selectProduct')}
              style={styles.titleProduct}
              weight="500"
              size="mx"
            />
            <Spacer type="Vertical" size="xms" />
            <View style={styles.listView}>
              <ErrorHandler
                componentName={`${TAG} ReturnList`}
                onErrorComponent={<View />}>
                <FlatList
                  data={orderDetail?.items}
                  renderItem={renderItem}
                  keyExtractor={(_, index) => index.toString()}
                />
              </ErrorHandler>
              <Spacer type="Vertical" size="s" />
              <Label
                color="text"
                text={t('orderReturn.description')}
                size="mx"
                fontFamily="Medium"
              />
              <InputBox
                textStyle={styles.inputDescription}
                placeholder={t('orderReturn.addDescription')}
                numberOfLines={4}
                style={styles.addressMainView}
                onChangeText={text => setDescription(text.trimStart())}
                value={description}
                placeholderTextColor={colors.text2}
                multiline={true}
                keyboardType="name-phone-pad"
              />
              <Spacer type="Vertical" size="xm" />
            </View>
          </View>
          <View style={styles.buttonView}>
            <Button
              type="primary"
              onPress={submitReturnRequest}
              disabled={returnFormItems.length === 0}
              text={t('buttons.submitRequest')}
              style={[
                styles.subReqBtn,
                returnFormItems.length === 0
                  ? {backgroundColor: 'lightgrey'}
                  : {},
              ]}
              labelSize="l"
              labelColor="whiteColor"
              selfAlign="stretch"
              size="large"
            />
          </View>
        </View>
      )}
      {/* *******************************modal************************************************** */}
      {modalVisible && (
        <ErrorHandler
          componentName={`${TAG} OrderReturnDetailModal`}
          onErrorComponent={<View />}>
          <OrderReturnDetailModal
            visible={modalVisible}
            selectedItemForReturn={selectedItemForReturn}
            currentReturnForm={currentReturnForm}
            returnReasons={returnReasons}
            error={error}
            fileError={fileError}
            subReasons={subReasons}
            action={action}
            onSaveReturnForm={onSaveReturnForm}
            onClearForm={onClearForm}
            onClose={() => {
              setModalVisible(false);
              setError('');
              updateReturnFormForUIAndRef({});
            }}
            removeDocument={(index: number) => removeDocument(index)}
            requestCameraPermission={() => requestCameraPermission('camera')}
            handleDocument={() => requestCameraPermission('gallery')}
            onUpdateQty={(count, change) => {
              if (change) {
                handleUpdateQty(count);
              } else {
                onChangeQty(count);
              }
            }}
            onChangeReasons={(item: ReturnReasons) => {
              setSubReasons(item?.sub_reasons);
              setAction(item?.return_actions);
              let obj = {
                reason_id: item.id,
                action_id: '',
                sub_reason_id: '',
              };

              const newReturnForm = {
                ...currentReturnFormRef.current,
                ...obj,
              };
              updateReturnFormForUIAndRef(newReturnForm);
            }}
            onChangeSubReasons={(item: SubReason) => {
              const newReturnForm = {
                ...currentReturnFormRef.current,
                sub_reason_id: item.id,
              };
              updateReturnFormForUIAndRef(newReturnForm);
            }}
            onChangeAction={(item: ReturnAction) => {
              const newReturnForm = {
                ...currentReturnFormRef.current,
                action_id: item.id,
              };
              updateReturnFormForUIAndRef(newReturnForm);
            }}
            onChangeDescription={(text: string) => {
              const newReturnForm = {
                ...currentReturnFormRef.current,
                description: text,
              };
              updateReturnFormForUIAndRef(newReturnForm);
            }}
          />
        </ErrorHandler>
      )}
    </SafeAreaView>
  );
};

export default OrderReturnListScene;

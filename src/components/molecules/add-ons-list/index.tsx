import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {Popable} from 'react-native-popable';
import Icon from 'react-native-vector-icons/FontAwesome';
import {Label, Spacer} from 'components/atoms';
import {Sizes} from 'common';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {useMemo} from 'react';

type Props = {
  selected?: Array<AddOnsType['id']>;
  onPress: () => void;
} & AddOnsType;

const AddOnsList = ({
  id,
  name,
  price,
  description,
  selected,
  onPress,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <TouchableOpacity onPress={onPress}>
      <View style={styles.container}>
        <View style={styles.leftContainer}>
          <View
            style={[
              styles.checkBoxContainer,
              selected?.includes(id) && styles.checkBoxActive,
            ]}>
            {selected?.includes(id) && (
              <Icon name="check" color={styles.checkBoxActive.borderColor} />
            )}
          </View>
          <Spacer type="Horizontal" size="l" />
          <Label text={name} size={'m'} />
          <Spacer type="Horizontal" size="s" />
          <Label text={`(+₹${price})`} size={'m'} color={'primary'} />
        </View>
        <Popable content={description}>
          <Icon name="info-circle" size={Sizes.l} color={colors.blackIcons} />
        </Popable>
      </View>
    </TouchableOpacity>
  );
};

export default React.memo(AddOnsList);

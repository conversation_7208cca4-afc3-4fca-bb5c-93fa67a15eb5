import {
  View,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  BackHandler,
  ScrollView,
  ImageBackground,
} from 'react-native';
import React, { useCallback, useEffect, useState, useRef } from 'react';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Formik } from 'formik';
import { RootStackParamsList } from '../../../routes';
import { ConnectWithUs } from 'components/molecules';
import stylesWithOutColor from './style';
import { Label, Spacer, ImageIcon } from 'components/atoms';
import { useTranslation } from 'react-i18next';
import Icons from 'common/icons';
import CountDown from 'react-native-countdown-component';
import { useTheme } from '@react-navigation/native';
import { otpValidationSchema } from 'utils/validationError';
import { useSelector } from 'react-redux';
import {
  getHash,
  startOtpListener,
  removeListener,
} from 'react-native-otp-verify';
import ErrorHandler from 'utils/ErrorHandler';
import { useMemo } from 'react';
import { debugError, debugLog } from 'utils/debugLog';
import { OtpInput, OtpInputRef } from 'react-native-otp-entry';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  showPass: boolean;
  setShowPassInput: boolean;
  isVerify: boolean;
  onSubmit?: (code: string) => void;
  onResend?: () => void;
  onEditPress?: () => void;
  apiErrorOtp?: string | number;
  source?: string;
  emailPhoneNumber?: string;
  pageType?: string;
  editableAccess: boolean;
  setPhoneFocus?: (check: boolean) => void;
  setLoginType?: (check: string) => void;
  actionType?: string;
};

const OtpScene = ({
  setOtpLogin,
  onSubmit,
  isVerify,
  apiErrorOtp,
  source,
  emailPhoneNumber,
  onResend,
  showPass,
  shouldShow,
  completeOtpTimer,
  onEditPress,
  setApiErrorOtp,
  pageType,
  setShowPassInput,
  editableAccess,
  setPhoneFocus,
  setLoginType,
  actionType,
}: Props) => {
  const TAG = 'OtpScreen';
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const { t } = useTranslation();
  const [otp, setOtp] = useState('');
  const otpInputRef = useRef<OtpInputRef>(null);

  useEffect(() => {
    if (Platform.OS === 'android') {
      getHash()
        .then(hash => {
          debugLog('Hash retrieved:=======', hash);
        })
        .catch(error => {
          debugError('Error retrieving hash:', error);
        });

      try {
        startOtpListener(message => {
          const extractedOtp = /(\d{6})/g.exec(message)?.[1];
          if (extractedOtp) {
            otpInputRef.current?.setValue(extractedOtp);
            setOtp(extractedOtp);
          }
        });
      } catch (error) {
        debugError('Error in OTP listener:', error);
      }

      return () => {
        removeListener();
      };
    }
  }, [otp]);

  useEffect(() => {
    if (otp.length === 6) {
      onSubmit?.(otp);
    }
  }, [otp]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    setOtpLogin('otpLogin');
    return true;
  };

  const onBack = (check = false) => {
    setOtpLogin('otpLogin');
    if (emailPhoneNumber?.includes('@')) {
      setLoginType && setLoginType('email');
    } else {
      setLoginType && setLoginType('phone');
    }
    if (pageType) {
      setShowPassInput(showPass ? true : false);
      setPhoneFocus(check);
    }
  };

  // Enhanced function to handle OTP code processing with paste support
  const processOtpCode = useCallback((code: string, setFieldValue: Function) => {
    const sanitizedCode = code.replace(/[^0-9]/g, '');
    const finalCode = sanitizedCode.slice(0, 6);

    setFieldValue('otp', finalCode);
    setOtp(finalCode);

    // Clear API errors when user starts typing/pasting
    if (source !== 'registerWithMobile') {
      setApiErrorOtp('');
    }
  }, [source, setApiErrorOtp]);

  return (
    <>
      {isVerify && (
        <View style={styles.verifyPopup}>
          <Image source={Icons.verifyImage} />
          <Spacer size="xxl" />
          <Label text={t('otp.verified')} size={'xl'} />
        </View>
      )}
      <TouchableOpacity style={styles.crossIconStyle} onPress={() => onBack()}>
        <ImageIcon icon="cancelClose" size="xl" tintColor="black" />
      </TouchableOpacity>
      <ImageBackground source={Icons.bgImgSign} style={styles.backgroundImage}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollViewContent}
            bounces={false}
            keyboardShouldPersistTaps="handled">
            <View style={styles.imageBackGroundOTP}>
              <ImageIcon
                style={styles.logoView}
                icon="dentalKartFull"
                size="ex176"
              />
              <Spacer size="xxl" />
              {actionType !== 'forgot_password' && (
                <View style={{ flexDirection: 'row' }}>
                  <Label
                    color="text2"
                    size="m"
                    weight="500"
                    text={'step 2 of 3 : Verify Your '}
                    style={styles.alignSelf}
                  />
                  <Label
                    text={
                      emailPhoneNumber?.includes('@')
                        ? t('otp.emailID')
                        : t('otp.PhoneNumber')
                    }
                    size="m"
                    color="text2"
                    weight="500"
                  />
                </View>
              )}
              <Formik
                initialValues={{ otp: '', email: '', newPassword: '' }}
                validationSchema={otpValidationSchema(showPass)}
                onSubmit={onSubmit}>
                {({ handleChange, values, errors, setFieldValue }) => {
                  return (
                    <>
                      <View style={styles.body}>
                        {source === 'registerWithMobile' ||
                          source === 'registerWithEmail' ? (
                          <View style={styles.rAlign}>
                            <View style={styles.mostTextView}>
                              <Label
                                text={t('registerForm.almostThere')}
                                color="text"
                                size="xx"
                                weight="500"
                                align="center"
                              />
                            </View>
                            <TouchableOpacity
                              style={styles.crossIconStyle}>
                            </TouchableOpacity>
                          </View>
                        ) : (
                          <TouchableOpacity
                            style={styles.crossIconStyle}
                          >
                          </TouchableOpacity>
                        )}
                        <Spacer size="xxl" />
                        <View style={styles.otpContainer}>
                          <Label
                            color="text2"
                            size=""
                            text={t('otp.mobileOtp1')}
                            style={styles.otpDes}
                            weight="400"
                            align="center"
                          />
                          <Label
                            color="text2"
                            size="mx"
                            text={t('otp.mobileOtp2')}
                            style={styles.otpDes}
                            weight="600"
                            align="center"
                          />
                          <Label
                            color="text2"
                            size="mx"
                            text={`${emailPhoneNumber?.includes('@')
                              ? t('otp.mobileOtp3Email')
                              : t('otp.mobileOtp3')
                              }`}
                            style={styles.otpDes}
                            weight="400"
                            align="center"
                          />
                          <Label
                            color="text2"
                            size="mx"
                            text={
                              emailPhoneNumber?.includes('@')
                                ? `${String(emailPhoneNumber).replace(
                                  /^(.{3})(.*)(@.*)$/,
                                  (_, a, b, c) => a + '..........' + c,
                                )}`
                                : `${String(emailPhoneNumber).replace(
                                  /.(?=.{4})/g,
                                  '*',
                                )}`
                            }
                            style={styles.otpDes}
                            weight="600"
                            align="center"
                          />
                          <Label
                            color="text2"
                            size="mx"
                            text={t('otp.mobileOtp4')}
                            style={styles.otpDes}
                            weight="400"
                            align="center"
                          />
                        </View>
                        <Spacer size="xxl" />
                        <Label
                          color="text"
                          size="l"
                          weight="500"
                          text={t('otp.DigitText')}
                          style={styles.alignSelf}
                        />
                        <Spacer size="xxl" />
                        <ErrorHandler
                          componentName={`${TAG} OTPInputView`}
                          onErrorComponent={<View />}>
                          <OtpInput
                            ref={otpInputRef}
                            numberOfDigits={6}
                            focusColor={colors.grey}
                            value={otp}
                            focusStickBlinkingDuration={500}
                            onTextChange={code => processOtpCode(code, setFieldValue)}
                            onFilled={code => processOtpCode(code, setFieldValue)}
                            textInputProps={{
                              accessibilityLabel: "One-Time Password",
                            }}
                            theme={{
                              containerStyle: styles.otpInputContainer,
                              inputsContainerStyle: styles.inputsContainer,
                              pinCodeContainerStyle: [
                                styles.otpInput,
                                errors.otp && styles.error,
                              ],
                              pinCodeTextStyle: styles.pincodeStyle,
                            }}
                            type='numeric'
                          />
                        </ErrorHandler>
                        {errors && (
                          <>
                            <Spacer size="sx" />
                            <Label
                              style={styles.otpErrorLabel}
                              text={t(
                                errors.otp
                                  ? (errors.otp as string)
                                  : apiErrorOtp
                                    ? String(apiErrorOtp).replace(
                                      'Error: GraphQL error:',
                                      '',
                                    )
                                    : '',
                              )}
                              color="textError"
                              size="m"
                            />
                          </>
                        )}
                        <Spacer size="sx" />

                        <Spacer size="xms" />

                        <Spacer size="xm" />
                        {shouldShow ? (
                          <View style={styles.resendOtpTimer}>
                            <ErrorHandler
                              componentName={`${TAG} CountDown`}
                              onErrorComponent={<View />}>
                              <CountDown
                                until={30}
                                size={12}
                                onFinish={completeOtpTimer}
                                digitStyle={styles.digitView}
                                digitTxtStyle={styles.digitTxtView}
                                timeToShow={['M', 'S']}
                                timeLabels={{ m: null, s: null }}
                                showSeparator={true}
                                separatorStyle={styles.separatorView}
                              />
                            </ErrorHandler>
                          </View>
                        ) : (
                          <></>
                        )}
                        <Spacer size="m" />
                        <View style={styles.onResendView}>
                          <TouchableOpacity
                            onPress={() => {
                              onResend();
                              if (otpInputRef.current) {
                                otpInputRef.current.clear();
                              }
                              setFieldValue('otp', '');
                              setOtp('');
                            }}
                            disabled={shouldShow}>
                            <Label
                              text={t('otp.ResendOtp')}
                              style={[
                                shouldShow
                                  ? styles.resendOtpText
                                  : { color: colors.newSunnyOrange },
                              ]}
                              size="mx"
                            />
                          </TouchableOpacity>
                        </View>
                        <Spacer size="m" />
                        <View style={styles.shouldShowView}>
                          <Label text={t('login.or')} size="mx" weight="400" />
                        </View>
                        <Spacer size="m" />
                        <TouchableOpacity
                          onPress={
                            source === 'registerWithMobile' ||
                              source === 'registerWithEmail'
                              ? onEditPress
                              : () => onBack(true)
                          }
                          style={styles.shouldShowView}>
                          <Label
                            text={
                              emailPhoneNumber?.includes('@')
                                ? t('otp.editMail')
                                : t('otp.editNumber')
                            }
                            size="mx"
                            color={
                              editableAccess
                                ? 'lightDisabled'
                                : 'newSunnyOrange'
                            }
                            weight="400"
                          />
                        </TouchableOpacity>
                        <Spacer size="m" />
                        <ConnectWithUs />
                      </View>
                    </>
                  );
                }}
              </Formik>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </ImageBackground>
    </>
  );
};

export default OtpScene;
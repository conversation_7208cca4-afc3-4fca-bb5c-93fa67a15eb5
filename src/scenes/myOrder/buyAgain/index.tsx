import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  FlatList,
  TextInput,
  TouchableOpacity,
  View,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {RouteProp, useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {
  ImageIcon,
  Label,
  ProductCardVertical,
  Separator,
  Spacer,
  WishlistButton,
} from 'components/atoms';
import {RootStackParamsList} from 'routes';
import {getMultipleProductBuyNow} from 'services/productDetail';
import getImageUrl from 'utils/imageUrlHelper';
import {buyNow} from 'services/productDetail';
import {showErrorMessage} from 'utils/show_messages';
import {SearchLoader} from 'skeletonLoader';
import Icons from 'common/icons';
import Modal from 'react-native-modal';
import {AnalyticsEvents} from 'components/organisms';
import {handleErrMsg} from 'utils/utils';
import {getProductDetail} from 'services/productDetail';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {urlResolver} from 'services/home';
import {myMemberShipsData} from 'services/mambership';
import ErrorHandler from 'utils/ErrorHandler';
import {sDevice, mDevice} from 'utils/utils';
import {useMemo} from 'react';
import {KeyboardAwareFlatList} from 'react-native-keyboard-aware-scroll-view';
import {useDebouncedCallback} from 'use-debounce';
import { debugError, debugLog } from 'utils/debugLog';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';
import { appsFlyerEvent } from 'components/organisms/analytics-Events/appsFlyerEvent';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList>;
};

const BuyAgainScene = ({navigation, route}: Props) => {
  const ProductData = route.params;
  const TAG = 'BuyAgainScreen';
  const productIds = ProductData?.productIds;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [productModal, setProductModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [productData, setProductData] = useState<ProductData[] | null>([]);
  const [cartItemsForUI, setCartItemsForUI] = useState<BuyAgainCart[] | null>(
    [],
  );
  const cartItemsRef = useRef<BuyAgainCart[] | null>([]);
  const [filteredDataForUI, setFilteredDataForUI] = useState<
    ProductData[] | null
  >([]);
  const filteredDataRef = useRef<ProductData[] | null>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [state, setState] = useState({});
  const dispatch = useDispatch();
  const {isLoggedIn, userInfo} = useSelector((state: RootState) => state.app);

  const updateFilteredDataRefAndUI = useCallback((data: ProductData[]) => {
    filteredDataRef.current = data;
    setFilteredDataForUI(data);
  }, []);

  const updateCartItemsRefAndUI = useCallback((data: BuyAgainCart[]) => {
    cartItemsRef.current = data;
    setCartItemsForUI(data);
  }, []);

  const getProductData = useCallback(async () => {
    setIsLoading(true);
    const response = await myMemberShipsData();
    const planActive =
      response?.status && response?.data && response?.data?.memberships
        ? !response?.data?.memberships?.some(item => item.is_active)
        : false;
    const {data, status} = await getMultipleProductBuyNow({id: productIds});
    if (status && data) {
      let newProductData = [];
      if (ProductData?.item?.data?.items?.length > 0) {
        let items = [];
        for (let item of data?.products) {
          const selectData = ProductData?.item?.data?.items?.filter(
            d => d.product_id === item.product_id,
          );
          const qty = item?.is_in_stock
            ? selectData?.length > 0
              ? selectData[0].ordered_qty
              : 1
            : 0;
          if (
            !['Plus Membership Plan-II', 'Plus Membership Plan-I'].includes(
              item?.name?.trim(),
            ) ||
            planActive
          ) {
            const isFreeProduct =
              selectData.length > 0 && selectData[0]?.is_free_product;
            if (!isFreeProduct) {
              newProductData.push({
                ...item,
                quantity: item?.is_in_stock ? qty : 0,
              });
              items.push({
                data: {product: item, quantity: qty},
              });
            }
          }
        }
        updateCartItemsRefAndUI(items);
      } else {
        newProductData = data?.products;
      }
      setProductData(newProductData);
      updateFilteredDataRefAndUI(newProductData);
    }
    setIsLoading(false);
  }, [dispatch]);

  useEffect(() => {
    getProductData();
  }, [getProductData]);

  const handleSearch = (text: string) => {
    setSearchText(text);
    const textData = text?.trim()?.toLowerCase().split(' ');
    const filtered = productData.filter(item =>
      textData.every(term => item?.name?.toLowerCase().includes(term)),
    );
    updateFilteredDataRefAndUI(filtered);
  };

  const memoizedSeparator = useCallback(() => {
    return <Spacer size="m" />;
  }, []);

  const pendingQuantityUpdatesRef = useRef<
    Record<
      string,
      {
        timeout: NodeJS.Timeout;
        params: {product: ProductData; qty: number};
      }
    >
  >({});

  const addToCartButton = useCallback(
    (product: ProductData, qty: number) => {

      // Use functional updates to ensure we're working with latest state
      const prevData = filteredDataRef.current;
      if (!prevData) return prevData;

      // Find the product index
      const index1 = prevData.findIndex(item => item?.sku === product?.sku);

      // If product not found, return unchanged data
      if (index1 === -1) {
        debugLog('Product not found in filteredData', product?.sku);
        return prevData;
      }

      // Create a new array with the updated item
      const newData = [...prevData];
      newData[index1] = {...newData[index1], quantity: qty};

      debugLog('mbl newData', newData);
      updateFilteredDataRefAndUI(newData);

      // Similar approach for cartItems
      const prevItems = cartItemsRef.current;
      if (!prevItems) return prevItems;

      const index = prevItems.findIndex(
        item => item?.data?.product?.sku === product?.sku,
      );

      const newItems = [...prevItems];

      if (index !== -1) {
        newItems[index] = {data: {product, quantity: qty}};
      } else {
        newItems.push({
          data: {product, quantity: qty},
        });
      }

      if (newItems.length === 0) {
        setProductModal(false);
      }
      updateCartItemsRefAndUI(newItems);

      setState({});
    },
    [updateFilteredDataRefAndUI, updateCartItemsRefAndUI], // Reduced dependencies
  );

  const debouncedAddToCart = useCallback(
    (product: ProductData, qty: number) => {
      const sku = product?.sku;

      // Clear any existing timeout for this SKU
      if (pendingQuantityUpdatesRef.current[sku]) {
        clearTimeout(pendingQuantityUpdatesRef.current[sku].timeout);
      }

      // Set a new timeout for this SKU
      pendingQuantityUpdatesRef.current[sku] = {
        timeout: setTimeout(() => {
          // Call the actual update function
          addToCartButton(product, qty);
          // Remove from pending updates
          delete pendingQuantityUpdatesRef.current[sku];
        }, 400),
        params: {product, qty},
      };
    },
    [addToCartButton],
  );

  const flushDebouncedAddToCart = useCallback(() => {
    Object.values(pendingQuantityUpdatesRef.current).forEach(
      ({params, timeout}) => {
        clearTimeout(timeout);
        addToCartButton(params.product, params.qty);
      },
    );
    pendingQuantityUpdatesRef.current = {};
  }, [addToCartButton]);

  useEffect(() => {
    return () => {
      Object.values(pendingQuantityUpdatesRef.current).forEach(({timeout}) => {
        clearTimeout(timeout);
      });
    };
  }, []);

  const onPressBuyNow = useCallback(async () => {
    flushDebouncedAddToCart();
    let cartData = [];
    for (item of cartItemsRef.current) {
      if (item?.data?.quantity > 0) {
        cartData.push({
          data: {...item?.data, sku: item?.data?.product?.sku},
        });
      }
    }
    if (cartData?.length > 0) {
      let obj = {
        cart_items: cartData,
        country_code: 'IN',
      };
      setIsLoading(true);
      const {data, status} = await buyNow(obj);
      AnalyticsEvents(
        'BUY_AGAIN',
        'Buy Again',
        data?.cart,
        userInfo,
        isLoggedIn,
      ); 
      trackEvent('INITIATE_CHECKOUT', {
        // contentId: cartData?.sku,
        // contentType: 'product',
        // currency: 'INR',
        // value: cartData?.selling_price,
        params: { cart_id: data?.cart?.cart_id }
      });

      // appsFlyer buy again (INITIATE_CHECKOUT) Event
      const productIds = cartData?.map(item => item?.data?.product.product_id).join(',');
      const categories = cartData?.map(item => item?.data?.product.name).join(', ');
      appsFlyerEvent('CheckoutStarted', {
        totalPrice: data?.cart?.pricing_details?.grand_total?.amount?.value,
        productIds: productIds,
        category: categories,
        currency: 'INR',
        totalQuantity: data?.cart?.total_quantity,
      });       
      setIsLoading(false);
      if (status) {
        setProductModal(false);
        // setCartItems([]);
        navigation.navigate('PaymentPage', {
          cart: data?.cart,
          buyNow: true,
        });
      } else {
        showErrorMessage(handleErrMsg(data));
      }
    }
  }, [cartItemsForUI]);

  const bottomBtnView = () => {
    return (
      <ErrorHandler
        componentName={`${TAG} BottomBtnView`}
        onErrorComponent={<View />}>
        <View style={styles.bottomBtnView}>
          <TouchableOpacity
            onPress={() => setProductModal(!productModal)}
            style={styles.saveFormBtn}>
            {cartItemsForUI?.length > 1 && <View style={styles.image1} />}
            {cartItemsForUI?.length > 2 && <View style={styles.image2} />}
            <View
              style={[
                styles.image,
                cartItemsForUI?.length > 1 && styles.image3,
              ]}>
              <FastImage
                resizeMode="contain"
                style={styles.imageSizeMain}
                source={
                  cartItemsForUI[cartItemsForUI?.length - 1]?.data?.product
                    ?.media?.mobile_image
                    ? {
                        uri: getImageUrl(
                          cartItemsForUI[cartItemsForUI?.length - 1]?.data
                            ?.product?.media?.mobile_image,
                        ),
                      }
                    : Icons.defaultImage
                }
              />
            </View>
            <Spacer type="Horizontal" size="sx" />
            <Label
              text={`${cartItemsForUI?.reduce(
                (sum, item) => sum + (item?.data?.quantity || 0),
                0,
              )} ${t('buyAgain.itemsAdded')}`}
              fontFamily="Medium"
              size={sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
              color="categoryTitle"
            />
            <Spacer type="Horizontal" size="sx" />
            <ImageIcon
              icon={!productModal ? 'arrowUp' : 'arrowBottom'}
              size="l"
            />
          </TouchableOpacity>
          <Spacer size="xl" />
          <TouchableOpacity onPress={() => onPressBuyNow()}>
            <LinearGradient
              style={styles.subReqBtn}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 1}}
              colors={[colors.coral, colors.persimmon]}>
              <Label
                text={t('orderListing.buyAgain')}
                size="mx"
                color="whiteColor"
                fontFamily="Medium"
              />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ErrorHandler>
    );
  };

  const navigationAction = (productId: string, item: Product) => {
    navigation.navigate('ProductDetail', {
      productId: productId,
      ProductItems: item,
    });
  };

  // const onNavigate = async (item: Product) => {
  //   const parentId = item?.parent_id;
  //   if (parentId) {
  //     navigationAction(parentId, item);
  //   } else {
  //     dispatch(setLoading(true));
  //     const {status, data} = await urlResolver(item?.url_key + '.html');
  //     if (status && data?.status !== 404) {
  //       navigationAction(data?.id, item);
  //       dispatch(setLoading(false));
  //     } else {
  //       const {status, data} = await getProductDetail(item?.product_id);
  //       dispatch(setLoading(false));
  //       if (status && data?.statusCode !== 404) {
  //         navigationAction(item?.product_id, item);
  //       } else {
  //         const prevRoutes = navigation?.getState().routes;
  //         navigation.reset({
  //           index: 0,
  //           routes: [
  //             ...prevRoutes.slice(0, prevRoutes.length - 1),
  //             {
  //               name: 'ItemNotFound',
  //             },
  //           ],
  //         });
  //       }
  //     }
  //   }
  // };

  // Helper function to handle Item Not Found navigation
  const redirectToItemNotFound = () => {
    try {
      const prevRoutes = navigation?.getState()?.routes || [];

      if (prevRoutes.length > 1) {
        // Remove the last route and replace it with ItemNotFound
        const newRoutes = prevRoutes
          .slice(0, prevRoutes.length - 1)
          .map(route => ({
            key: route.key,
            name: route.name,
            params: route.params
              ? JSON.parse(JSON.stringify(route.params))
              : undefined, // Avoid passing functions
          }));

        newRoutes.push({name: 'ItemNotFound'});

        setTimeout(() => {
          navigation.reset({
            index: newRoutes.length - 1,
            routes: newRoutes,
          });
        }, 500);
      } else {
        // Fallback: If only one route exists, navigate instead of resetting
        navigation.navigate('ItemNotFound');
      }
    } catch (error) {
      debugError('Navigation reset error:', error);
    }
  };

  const onNavigate = async (item: Product) => {
    try {
      const parentId = item?.parent_id;
      if (parentId) {
        setProductModal(false);
        setTimeout(() => navigationAction(parentId, item), 100);
        return;
      }

      dispatch(setLoading(true));

      // First API call
      const {status, data} = await urlResolver(item?.url_key + '.html');

      if (status && data?.status !== 404 && data?.id) {
        setProductModal(false);
        setTimeout(() => navigationAction(data.id, item), 100);
        return;
      }
      try {
        const productDetail = await getProductDetail(
          item?.data?.product?.product_id,
        );
        if (
          productDetail.status &&
          productDetail?.data?.statusCode !== 404 &&
          productDetail?.data?.product_id
        ) {
          setProductModal(false);
          setTimeout(
            () => navigationAction(productDetail?.data.product_id, item),
            100,
          );
          return;
        }
      } catch (error) {
        setProductModal(false);
        debugError('Error fetching product details:', error);
      }

      // If productDetail fails or returns 404, go to ItemNotFound
      redirectToItemNotFound();
    } catch (error) {
      setProductModal(false);
      debugError('Navigation error:', error);
    } finally {
      setProductModal(false);
      dispatch(setLoading(false));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton={true}
          navigation={navigation}
          bagIcon={true}
          text={t('profile.buyAgain')}
          searchIcon={true}
        />
      </ErrorHandler>
      {isLoading ? (
        <SearchLoader />
      ) : (
        <View style={styles.subContainer}>
          {/* <Spacer size="m" />
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            style={styles.linerColor}
            colors={[colors.skyBlue15, colors.red1]}>
            <Label
              text={t('profile.purchase')}
              fontFamily="Medium"
              size="m"
              color="whiteColor"
            />
          </LinearGradient> */}
          <Spacer size="m" />
          <View style={styles.searchIconView}>
            <ImageIcon icon="searchIcon" tintColor="text2" size="xl" />
            <Spacer type="Horizontal" size="xm" />
            <Separator color="text2" Vertical height="xl" />
            <Spacer type="Horizontal" size="xm" />
            <TextInput
              testID="txtBuyAgainSearchByName"
              placeholderTextColor={colors.text2}
              keyboardType="default"
              placeholder={t('buyAgain.searchByName')}
              onChangeText={handleSearch}
              value={searchText}
              style={styles.textInputView}
              allowFontScaling={false}
            />
          </View>
          <Spacer size="xl" />
          <View style={styles.filteredDataView}>
            <ErrorHandler
              componentName={`${TAG} FilteredData`}
              onErrorComponent={<View />}>
              <KeyboardAwareFlatList
                data={filteredDataForUI}
                extraData={filteredDataForUI}
                style={styles.flex}
                keyExtractor={(_, i) => i.toString()}
                renderItem={({item, index}) => (
                  <ProductCardVertical
                    index={index}
                    actionBtn={item?.action_btn}
                    skuId={item?.sku}
                    size="medium"
                    hideAddToCart={true}
                    imageWithBorder={true}
                    maxWidth={0.48}
                    item={item}
                    productType={item?.type}
                    inStock={item.is_in_stock}
                    maxSaleQty={item?.max_sale_qty}
                    demoAvailable={item?.demo_available}
                    msrp={item?.msrp}
                    image={item?.media?.mobile_image}
                    name={item?.name}
                    defaultQty={item?.quantity}
                    rewardPoint={item?.reward_points}
                    description={item?.short_description}
                    rating={(item?.rating === 'null' ||
                    item?.average_rating === null
                      ? 0
                      : Number(item?.rating) || Number(item?.average_rating)
                    ).toFixed(1)}
                    ratingCount={
                      !!item?.rating_count ? `(${item?.rating_count})` : '(0)'
                    }
                    price={item?.price}
                    sellingPrice={item?.selling_price}
                    currencySymbol={item?.currency_symbol}
                    discount={item?.discount?.label}
                    onPress={() => onNavigate(item)}
                    navigation={navigation}
                    onCartPress={qty => debouncedAddToCart(item, qty)}
                    onPressShare={() => null}
                    freeProducts={[]}
                    showWishlist={true}
                    hideCart={true}
                    hideCartBtn={true}
                  />
                )}
                numColumns={2}
                ItemSeparatorComponent={memoizedSeparator}
                keyboardShouldPersistTaps="always"
                enableOnAndroid={true}
                extraScrollHeight={-Platform.select({ios: 100, android: 100})}
              />
            </ErrorHandler>
          </View>
          {cartItemsForUI?.length > 0 &&
            !cartItemsForUI.every(item => item.data.quantity === 0) &&
            bottomBtnView()}
        </View>
      )}

      {/* ====================product modal==================== */}
      <Modal
        onBackButtonPress={() => setProductModal(false)}
        isVisible={productModal}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={75}
        animationOutTiming={75}
        backdropOpacity={0.01}
        style={styles.modalStyle}>
        <View style={styles.modalOverlay}>
          <View style={styles.subView}>
            <TouchableWithoutFeedback
              onPress={() => {
                setProductModal(false);
              }}>
              <View style={styles.modalCloseBtnContainer}>
                <TouchableOpacity
                  onPress={() => {
                    setProductModal(false);
                  }}>
                  <ImageIcon icon="close" size="x6l" />
                </TouchableOpacity>
              </View>
            </TouchableWithoutFeedback>
            <View style={styles.modalSubView}>
              <Spacer size="xxl" />
              <Label
                text={t('buyAgain.itemAdded')}
                size="l"
                fontFamily="Medium"
              />
              <Spacer size="m" />
              <View style={styles.listView}>
                <ErrorHandler
                  componentName={`${TAG} Product Modal`}
                  onErrorComponent={<View />}>
                  <KeyboardAwareFlatList
                    horizontal
                    data={
                      cartItemsForUI.length == 1
                        ? [cartItemsForUI[0], {}]
                        : cartItemsForUI
                    }
                    keyExtractor={(_, i) => i.toString()}
                    renderItem={({item, index}) => {
                      if (Object.keys(item).length > 0) {
                        return (
                          <ProductCardVertical
                            index={index}
                            actionBtn={item?.data?.product?.action_btn}
                            skuId={item?.data?.product?.sku}
                            size="medium"
                            hideAddToCart={true}
                            imageWithBorder={true}
                            maxWidth={0.49}
                            item={item?.data?.product}
                            productType={item?.data?.product?.type}
                            inStock={item?.data?.product?.is_in_stock}
                            maxSaleQty={item?.data?.product?.max_sale_qty}
                            minSaleQty={0}
                            defaultQty={item?.data?.quantity}
                            demoAvailable={item?.data?.product?.demo_available}
                            msrp={item?.data?.product?.msrp}
                            image={item?.data?.product?.media?.mobile_image}
                            name={item?.data?.product?.name}
                            rewardPoint={item?.data?.product?.reward_points}
                            description={item?.data?.product?.short_description}
                            rating={(item?.data?.product?.rating_count ===
                              'null' ||
                            item?.data?.product?.average_rating === null
                              ? 0
                              : Number(item?.data?.product?.rating) ||
                                Number(item?.data?.product?.average_rating)
                            ).toFixed(1)}
                            ratingCount={
                              !!item?.data?.product?.rating_count
                                ? `(${item?.data?.product?.rating_count})`
                                : '(0)'
                            }
                            price={item?.data?.product?.price}
                            sellingPrice={item?.data?.product?.selling_price}
                            currencySymbol={
                              item?.data?.product?.currency_symbol
                            }
                            discount={item?.data?.product?.discount?.label}
                            onPress={() => {
                              onNavigate(item);
                            }}
                            navigation={navigation}
                            onCartPress={qty =>
                              debouncedAddToCart(item?.data?.product, qty)
                            }
                            freeProducts={[]}
                            showWishlist={true}
                            hideCart={true}
                            hideCartBtn={true}
                            onClose={() => setProductModal(false)}
                          />
                        );
                      } else {
                        return <View style={styles.halfWidth} />;
                      }
                    }}
                    ItemSeparatorComponent={memoizedSeparator}
                  />
                </ErrorHandler>
              </View>
              <Spacer size="m" />
            </View>
            {cartItemsForUI?.length > 0 &&
              !cartItemsForUI.every(item => item.data.quantity === 0) &&
              bottomBtnView()}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default BuyAgainScene;

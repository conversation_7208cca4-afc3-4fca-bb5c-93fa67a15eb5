import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalView: {
      margin: 0,
      padding: Sizes.xl,
    },
    mainView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xl,
      padding: Sizes.xl,
    },
    excitingView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    centerView: {
      flex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    imageView: {
      height: Sizes.ex176,
      alignSelf: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    renderView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    renderRow: {
      flex: Sizes.x,
      flexDirection: 'row',
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      paddingVertical: Sizes.sx,
      alignItems: 'center',
      height: Sizes.x46,
    },
    inputBox: {
      height: Sizes.x46,
      flex: Sizes.x,
      color: colors.text,
    },
    saveBtn: {
      height: Sizes.x4l,
      width: Sizes.x70,
      paddingVertical: 0,
    },
    fRow: {
      flexDirection: 'row',
    },
    addInput: {
      flex: Sizes.x,
      flexDirection: 'row',
      borderColor: colors.grey2,
      borderWidth: Sizes?.x,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.sx,
      height: Sizes.x46,
      alignItems: 'center',
    },
    addInputStyle: {
      flex: Sizes.x,
      height: Sizes.x46,
      paddingVertical: 0,
      marginTop: -Sizes.xs,
      color: colors.text,
    },
    modalCloseView: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x70,
    },
  });

export default styles;

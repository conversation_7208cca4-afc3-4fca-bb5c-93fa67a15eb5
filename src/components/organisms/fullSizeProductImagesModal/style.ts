import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const Styles = (colors: Theme['colors']) => {
  return StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    flexContainer: {
      flex: Sizes.x,
    },
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.background,
    },
    modelBg: {
      flex: Sizes.x,
    },
    p16: {
      padding: Sizes.l,
    },
    alignItemsCenter: {
      alignItems: 'center',
    },
    modalCloseButton: {
      padding: Sizes.l,
    },
    imageContainer: {
      width: Sizes.x4l * Sizes.xs,
      height: Sizes.x4l * Sizes.xs,
      borderWidth: Sizes.xs,
      margin: Sizes.xs,
      borderRadius: Sizes.l,
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: colors.grey2,
      backgroundColor: colors.background,
    },
    active: {
      borderColor: colors.newSunnyOrange,
    },
    productImageSmall: {
      width: Sizes.x7l,
      height: Sizes.x7l,
    },
  });
};

export default Styles;

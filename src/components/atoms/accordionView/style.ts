import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    accordionMainView: {
      borderTopWidth: Sizes.x,
      borderTopColor: colors.grey2,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
    },
    accordionLine: {
      borderTopWidth: Sizes.x,
      borderTopColor: colors.grey2,
    },
    accordionView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.m,
    },
    childList: {
      flexDirection: 'row',
      paddingBottom: Sizes.xms,
      marginHorizontal: Sizes.l,
    },
    flex: {
      flex: Sizes.x,
    },
  });

export default styles;

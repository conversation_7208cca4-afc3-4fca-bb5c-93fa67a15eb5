import React, {memo} from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {Label, ImageIcon, Spacer, Link} from 'components/atoms';
import stylesWithOutColor from './style';
import {Linking} from 'react-native';
import {useMemo} from 'react';

const PaymentSupportCart = () => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <>
      <View style={styles.acceptView}>
        <Label
          text={t('checkOut.weAccept')}
          size="m"
          fontFamily="Medium"
          color="text2"
        />
        <ImageIcon
          icon="paymentOptions"
          size="xxl"
          style={styles.paymentOption}
        />
      </View>
      <Spacer size="xm" />
      <View style={{alignItems: 'center'}}>
        <Label
          text={t('login.needHelp')}
          color="grey"
          size="m"
          align="center"
          fontFamily="Regular">
          <Label
            onPress={() => Linking.openURL('mailto:<EMAIL>')}
            text={t('login.supportEmail')}
            color="grey"
            size="m"
            align="center"
            fontFamily="Regular"
            textDecorationLine="underline"
          />
        </Label>
      </View>
      <Spacer size="x3l" />
      <View style={styles.securePaymentView}>
        <View style={styles.paymentSubView}>
          <ImageIcon icon="securePayments" size="xxl" />
          <Spacer size="xm" />
          <Label
            text={t('checkOut.secPay')}
            size="m"
            color="grey"
            fontFamily="Regular"
          />
        </View>
        <ImageIcon icon="dot" size="sx" />
        <View style={styles.easyReturnView}>
          <ImageIcon icon="easyReturn" size="xxl" />
          <Spacer size="xm" />
          <Label
            text={t('checkOut.easyReturn')}
            size="m"
            color="grey"
            fontFamily="Regular"
          />
        </View>
        <ImageIcon icon="dot" size="sx" />
        <View style={styles.easyReturnView}>
          <ImageIcon icon="genuineProduct" size="xxl" />
          <Spacer size="xm" />
          <Label
            text={t('checkOut.genuinePro')}
            size="m"
            color="grey"
            fontFamily="Regular"
          />
        </View>
      </View>
    </>
  );
};

export default memo(PaymentSupportCart);

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  View,
  Share as ShareRN,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {RootStackParamsList} from '../../routes';
import {
  Button,
  ChildProductModal,
  Header,
  SuggestProduct,
} from 'components/molecules';
import stylesWithOutColor from './style';
import {
  Spacer,
  Label,
  ProductCardVertical,
  FooterButton,
  Separator,
  Tag,
  ImageIcon,
  WishlistButton,
  SuccessModal,
} from 'components/atoms';
import {config} from 'utils/algoliaConfig';
import objectToQueryString from 'utils/objectToQueryString';
import {searchMapper} from 'utils/searchMapper';
import {showErrorMessage} from 'utils/show_messages';
import {addToCart, setLoading} from 'app-redux-store/slice/appSlice';
import {AnalyticsEvents, FilterAndSortModal} from 'components/organisms';
import {SearchLoader} from 'skeletonLoader';
import {searchProducts} from 'services/productDetail';
import {addProductSuggestion} from 'services/home';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {checkDevice} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import {debounce} from 'utils/utils';
import {useMemo} from 'react';
import {setCurrentScreenName} from '@microsoft/react-native-clarity';
import {ScrollView} from 'react-native-gesture-handler';
import {debugLog} from 'utils/debugLog';
import {Sizes} from 'common';
import {trackEvent} from 'components/organisms/appEventsLogger/FacebookEventTracker';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  gethomepagebrands: gethomepageBrandsItemProps;
  index?: number;
};
export const useIsMount = () => {
  const isMountRef = useRef(true);
  useEffect(() => {
    isMountRef.current = false;
  }, []);
  return isMountRef.current;
};

const SearchProductScene = ({navigation, index, route}: Props) => {
  const TAG = 'SearchProductScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const [viewChildModule, setViewChildModule] = useState(false);
  const ITEM_HEIGHT = checkDevice() ? Sizes.ex416 : Sizes.ex405;
  const [inputFocus, setInputFocus] = useState(false);
  const [searchData, setSearchData] = useState({
    products: [],
    searchInfo: {},
  });
  // const {
  //   flatListRefTop,
  //   scrollToTop: scrollToTopButton,
  //   showScrollToTopButton: showToTop,
  //   handleScroll,
  // } = useContext(ScrollContext);
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [priceLoader, setPriceLoader] = useState(false);
  const [pageNumber, setPageNumber] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [successModel, setSuccessModel] = useState(false);
  const [showScrollToTopButton, setShowScrollToTopButton] = useState(false);
  const [totalProduct, setTotalProduct] = useState(0);
  const previousOffsetY = useRef(0);
  const [filtersReady, setFiltersReady] = useState(false);
  const flatListRefTop = useRef(null);
  const prevFilters = useRef(null);
  const [priceValue, setPriceValue] = useState({min: 0, max: 0});
  const [filters, setFilters] = useState({
    query: '',
    applyFilters: {},
    sortBy: '',
    page: 0,
  });
  const [filterOptions, setFilterOptions] = useState<
    Array<{
      key: string;
      value: string;
      type: string;
      min: number;
      max: number;
      isSearch: boolean;
      options: Array<{key: string; value: string}>;
    }>
  >([]);
  const [isModelType, setIsModelType] = useState<'filter' | 'sort'>('sort');
  const [modelVisible, setModelVisible] = useState(false);
  const [suggestionModal, setSuggestionModal] = useState(false);
  const [isReset, setIsReset] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const isMount = useIsMount();
  const [localLoading, setLocalLoading] = useState(false);
  const [moreLoading, setMoreLoading] = useState(false);
  const [searchInitiated, setSearchInitiated] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [searchAlertText, setSearchAlertText] = useState(false);
  const dispatch = useDispatch();

  const openSuggestProductModel = useCallback(() => {
    setSuggestionModal(true);
  }, []);

  const handleScroll = useCallback(event => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setShowScrollToTopButton(
      offsetY > 0 && offsetY < previousOffsetY.current ? true : false,
    );
    setTimeout(() => {
      previousOffsetY.current = offsetY;
    }, 300);
  }, []);

  const handleScrollWithKeyboardDismiss = event => {
    if (!inputFocus) {
      Keyboard.dismiss();
    } else {
      setTimeout(() => {
        setInputFocus(false);
      }, 2000);
    }
    handleScroll(event);
  };

  const openSortModel = useCallback(() => {
    setIsModelType('sort');
    setModelVisible(true);
  }, []);

  const openFilterModel = useCallback(() => {
    setIsModelType('filter');
    setModelVisible(true);
  }, []);

  const onSubmitProductSuggestion = useCallback(
    async (formData: {
      productName: string;
      brandName?: string;
      comment?: string;
      url?: string;
      email?: string;
    }) => {
      if (formData?.productName) {
        try {
          dispatch(setLoading(true));
          const {data, status} = await addProductSuggestion({
            searched_key: '',
            product_name: formData?.productName,
            brand: formData?.brandName,
            comment: formData?.comment,
            url: formData?.url,
            user: !!formData?.email
              ? formData?.email
              : isLoggedIn
              ? userInfo?.email
              : 'guest_user',
          });
          dispatch(setLoading(false));
          if (status) {
            setIsReset(true);
            setSuggestionModal(false);
            setSuccessModel(true);
            setTimeout(() => {
              setSuccessModel(false);
            }, 3500);
          }
        } catch (err) {
          debugLog(err);
          dispatch(setLoading(false));
        }
      } else {
        showErrorMessage(t('searchProduct.errorProduct'));
      }
    },
    [dispatch, isLoggedIn, userInfo?.email],
  );

  const fetchSearchProducts = useCallback(
    async (type: string) => {
      try {
        type === 'search' ? (filters.page = 0) : (filters.page = pageNumber);
        if (!priceLoader) {
          if (pageNumber == 0) {
            setLocalLoading(true);
          } else {
            setMoreLoading(true);
          }
        }
        let searchConfig = config;

        debugLog(
          filters?.applyFilters,
          objectToQueryString(filters),
          'search filters',
        );
        const analyticsData = {
          'Search Keyword': filters?.query,
        };

        AnalyticsEvents(
          'PRODUCT_SEARCHED',
          'Product searched',
          analyticsData,
          userInfo,
          isLoggedIn,
        );
        let filtersObj;
        if (filters.applyFilters?.rating) {
          const updatedData = {
            ...filters,
            applyFilters: {
              ...filters?.applyFilters,
              rating: (filters?.applyFilters?.rating ?? 0) * 20,
            },
          };
          filtersObj = updatedData;
        } else {
          filtersObj = filters;
        }
        setSearchInitiated(true);
        trackEvent('SEARCH', {
          searchString: filtersObj?.query,
          // params: { item_name: cartData?.name }
        });
        const query = objectToQueryString(filtersObj);
        const response = await searchProducts(query);
        const {status, data} = response;

        // appsFlyer Search Event
        if (filtersObj?.query) {
          const ids = data?.hits?.hits?.map(item => item.objectID);
          appsFlyerEvent('ProductSearched', {
            searchTerm: filtersObj?.query,
            contentIds: ids,
          });
        }
        if (data?.hits?.facets_stats) {
          const priceStats = data?.hits?.facets_stats;
          const priceMax = priceStats['price.INR.default']?.max || 0;
          const priceMin = priceStats['price.INR.default']?.min || 0;
          setPriceValue({min: priceMin, max: priceMax});
        }
        setTotalProduct(data?.hits?.nbHits);
        if (pageNumber == 0) {
          setLocalLoading(false);
        } else {
          setMoreLoading(false);
        }
        if (status) {
          const mappedData = searchMapper(searchConfig, data);
          if (mappedData?.searchdata) {
            const {searched_items, ...resObject} = mappedData.searchdata;
            if (type === 'search') {
              setTotalPages(resObject?.total_pages);
              setPageNumber(0);
            }
            const filterKey = {
              categories: 'Categories',
              manufacturer: 'Manufacturer',
              rating_summary: 'Rating',
            };
            const filterValue = {
              categories: 'category',
              manufacturer: 'manufacturer',
              rating_summary: 'rating',
            };
            let apiFilters = resObject?.filters?.map(item => ({
              key: filterKey[item?.key] || 'Price Range',
              value: filterValue[item?.key] || 'price',
              type: item?.key === 'rating_summary' ? 'rating' : 'checkBox',
              isSearch: false,
              options:
                item?.key === 'rating_summary'
                  ? [1, 2, 3, 4].map(rating => ({
                      label: 5 - rating + '+',
                      value: 5 - rating,
                    }))
                  : item?.value?.map(e => ({label: e, value: e})),
            }));
            setFilterOptions([
              ...apiFilters,
              {
                key: 'Price Range',
                value: 'price',
                type: 'range',
                min: 0,
                max: 500000,
                isSearch: false,
                options: [],
              },
            ]);
            if (type === 'search') {
              setInputFocus(true);
              scrollToTop();
              setTimeout(() => {
                setInputFocus(false);
              }, 2000);
              setSearchData(prev => ({
                ...prev,
                products: [...searched_items],
                searchInfo: resObject,
              }));
            } else {
              setSearchData(prev => ({
                ...prev,
                products: [...prev.products, ...searched_items],
                searchInfo: resObject,
              }));
            }
          }
        }
        if (
          Object.keys(filters.applyFilters).length === 0 &&
          filters.page === 0 &&
          filters.query === '' &&
          filters.sortBy === ''
        ) {
          setShowSearchBar(true);
        }
      } catch (e) {
        setLocalLoading(false);
        setMoreLoading(false);
      }
    },
    [pageNumber, filters, searchData],
  );

  const applyFilters = useCallback(
    filterValues => {
      setFilters(prev => ({
        ...prev,
        applyFilters: {...filters.applyFilters, ...filterValues},
      }));
    },
    [filters],
  );

  const applySort = useCallback(
    value => {
      setFilters(prev => ({...prev, sortBy: value}));
    },
    [filters],
  );

  // Debounced search function with minimum 3 characters requirement
  const debouncedSearch = useCallback(
    debounce(text => {
      const trimmed = text.trim();
      if (trimmed.length >= 2 || trimmed.length == 0) {
        // Call with actual query
        setFilters(prev => {
          if (prev.query === trimmed) return prev;
          return {...prev, query: trimmed};
        });
        setSearchAlertText(false);
      } else {
        setSearchAlertText(true);
      }
    }, 150),
    [],
  );

  // const handleSearch = useCallback((text: string) => {
  //   const trimmed = text.trim();
  //     setFilters(prev => {
  //       if (prev.query === trimmed) return prev;
  //       return {...prev, query: trimmed};
  //     });
  // }, []);

  const handleSearch = useCallback(
    text => {
      debouncedSearch(text);
    },
    [debouncedSearch],
  );

  const scrollToTop = useCallback(() => {
    if (flatListRefTop.current) {
      flatListRefTop.current.scrollToOffset({offset: 0, animated: true});
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      const queryName = route?.params?.queryName;
      if (queryName) {
        setFilters(prev => ({...prev, query: queryName}));
        setShowSearchBar(true);
      }
      setFiltersReady(true);
      return () => {
        setFiltersReady(false);
      };
    }, [route?.params?.queryName]),
  );

  useEffect(() => {
    setCurrentScreenName('Search page');
    return () => {
      setCurrentScreenName('Clarity event');
    };
  }, []);

  useEffect(() => {
    if (pageNumber > 0 && !isMount && pageNumber <= totalPages - 1) {
      fetchSearchProducts('loadMore');
    }
  }, [isMount, pageNumber, totalPages]);

  useEffect(() => {
    setShowScrollToTopButton(false);
    fetchSearchProducts('search');
  }, [filters]);

  const RenderFooter = useMemo(() => {
    return moreLoading ? (
      <View style={styles.loading}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    ) : null;
  }, [colors.primary, moreLoading, styles.loading]);

  const memoizedSeparator = useCallback(() => {
    return <Separator Vertical color="grey2" />;
  }, []);

  const resetAllFilters = useCallback(() => {
    setFilters(prev => ({...prev, applyFilters: {}, sortBy: ''}));
  }, []);

  const removeFilter = useCallback(
    (key: string, value?: string) => {
      let filterValues = filters?.applyFilters;
      if (!Array.isArray(filterValues[key])) {
        delete filterValues[key];
        setFilters(prev => ({...prev, applyFilters: filterValues}));
      } else {
        filterValues[key] = filterValues[key]?.filter(e => e !== value);
        setFilters(prev => ({...prev, applyFilters: filterValues}));
      }
    },
    [filters],
  );

  const fetchMoreData = useCallback(() => {
    const hasMore = pageNumber < totalPages - 1;
    const isIdle = !localLoading && !moreLoading;

    if (hasMore && isIdle) {
      setPageNumber(prev => prev + 1);
    }
  }, [localLoading, moreLoading, pageNumber, totalPages]);

  const hasActiveFilters = useMemo(() => {
    return Object.keys(filters.applyFilters).some(key => {
      const value = filters?.applyFilters[key];
      if (Array.isArray(value)) {
        return value.length > 0;
      } else if (typeof value === 'object' && Object.keys(value).length > 0) {
        return true;
      } else if (value !== '' && value !== null && value !== undefined) {
        return true;
      }
      return false;
    });
  }, [filters.applyFilters]);

  const keyExtractor = useCallback((_, idx) => idx.toString(), []);

  const onPressProduct = useCallback(
    (item: ProductData) => {
      return () => {
        navigation.navigate('ProductDetail', {
          productId: item?.object_id,
          ProductItems: {
            action_btn: {
              // action: 'add_to_cart',
              text: item?.action_btn,
            },
            average_rating: null,
            currency_symbol: item?.price?.regularPrice?.amount?.currency_symbol,
            discount: {
              label:
                item?.price?.regularPrice?.amount?.value >
                item?.price?.minimalPrice?.amount?.value ? (
                  <>
                    {(
                      100 -
                      (item.price.minimalPrice.amount.value * 100) /
                        item.price.regularPrice.amount.value
                    ).toFixed(2) + '% off'}
                  </>
                ) : (
                  '0.0% off'
                ),
            },

            is_in_stock: item?.is_in_stock,

            media: {
              mobile_image: item?.thumbnail_url,
            },

            name: item?.name,
            price: item?.price?.regularPrice?.amount?.value,
            product_id: item?.object_id,
            rating_count: !!item?.rating_count
              ? `(${item?.rating_count})`
              : '(0)',
            reward_points: item?.reward_points,
            selling_price: item?.price?.minimalPrice?.amount?.value,
            short_description: item?.short_description,
            sku: item?.sku,
            tags: [
              {
                code: null,
                type: null,
                url: null,
              },
            ],
            type: item?.type_id,
            url_key: item?.url_key,
          },
        });
      };
    },
    [navigation],
  );

  const memoizedFilterOptions = useMemo(() => {
    if (
      priceValue !== undefined &&
      priceValue !== null &&
      priceValue?.min >= 0 &&
      priceValue?.max !== 0
    ) {
      return filterOptions.map(item =>
        item.value === 'price'
          ? {
              ...item,
              min: priceValue.min,
              max: priceValue.max + (priceValue.max === priceValue.min ? 1 : 0),
            }
          : item,
      );
    }
    return filterOptions;
  }, [filterOptions, priceValue?.min, priceValue?.max]);

  const maxWidth = useMemo(() => (checkDevice() ? 0.24 : 0.46), []);

  const renderProductItem = useCallback(
    ({item, index}) => {
      const handlePress = onPressProduct(item);

      const ratingValue = (
        item?.rating_count === 'null' || item.average_rating === null
          ? 0
          : Number(item?.rating) || Number(item?.average_rating) || 0
      ).toFixed(1);

      const ratingCount = !!item?.rating_count
        ? `(${item?.rating_count})`
        : '(0)';

      const regularPrice = item?.price?.regularPrice?.amount?.value;
      const minimalPrice = item?.price?.minimalPrice?.amount?.value;
      const currencySymbol = item?.price?.regularPrice?.amount?.currency_symbol;

      const discount =
        regularPrice > minimalPrice
          ? `${(100 - (minimalPrice * 100) / regularPrice).toFixed(2)}% off`
          : '0.0% off';

      return (
        <ErrorHandler
          componentName={`${TAG} ProductCardVertical`}
          onErrorComponent={<View />}>
          <ProductCardVertical
            index={index}
            skuId={item?.sku}
            maxWidth={maxWidth}
            item={item}
            actionBtn={item?.action_btn}
            image={item?.thumbnail_url}
            name={item?.name}
            inStock={item?.is_in_stock}
            rewardPoint={item?.reward_points}
            demoAvailable={item?.demo_available}
            msrp={item?.msrp}
            description={item?.short_description}
            productType={item?.type_id}
            rating={ratingValue}
            ratingCount={ratingCount}
            price={regularPrice}
            sellingPrice={minimalPrice}
            currencySymbol={currencySymbol}
            discount={discount}
            onPress={handlePress}
            navigation={navigation}
            showWishlist={true}
            isSearchPage={true}
            onFocus={() => setInputFocus(true)}
          />
        </ErrorHandler>
      );
    },
    [navigation, onPressProduct],
  );

  const emptySearchResults = useCallback(() => {
    return !localLoading ? (
      <View style={styles.flexOne}>
        <ScrollView
          contentContainerStyle={styles.emptyMainView}
          keyboardShouldPersistTaps="handled">
          <Spacer size="ex0" />
          <View>
            <View style={styles.emptySearchWrapper}>
              <Label
                color="text"
                size="xxl"
                fontFamily="Medium"
                text={t('searchProduct.find')}
                align="center"
              />
            </View>

            <Spacer size="xx" />

            <View style={styles.letUsView}>
              <Label
                size="xx"
                color="grey"
                fontFamily="Medium"
                text={t('searchProduct.filling')}
              />

              <FastImage
                resizeMode="contain"
                style={styles.emptyImage}
                source={Icons.editIconGif}
                tintColor={colors.grey}
              />
              <Label
                size="xx"
                color="grey"
                fontFamily="Medium"
                text={t('searchProduct.fillingdetails')}
              />
            </View>
          </View>

          <View style={styles.suggestProductBtn}>
            <ErrorHandler
              componentName={`${TAG} Button`}
              onErrorComponent={<View />}>
              <Button
                onPress={() => {
                  openSuggestProductModel();
                  setIsReset(false);
                }}
                radius="xms"
                size="large"
                paddingHorizontal="x44"
                selfAlign="center"
                text={t('buttons.suggestProduct')}
                type="bordered"
              />
            </ErrorHandler>
          </View>
        </ScrollView>
      </View>
    ) : null;
  }, [
    colors.grey,
    localLoading,
    openSuggestProductModel,
    styles.emptyImage,
    styles.emptyMainView,
    styles.emptySearchWrapper,
    styles.letUsView,
  ]);

  const productItemSeparator = useCallback(() => {
    return <Spacer size="xm" />;
  }, []);

  const onPressSearchIcon = useCallback(() => {
    setShowSearchBar(!showSearchBar);
  }, [showSearchBar]);

  const setSearchBar = useCallback((val: boolean) => {
    setShowSearchBar(val);
  }, []);
  const hasNonEmptyFilters = filters?.applyFilters;
  Object.values(filters?.applyFilters).some(
    arr => Array.isArray(arr) && arr.length > 0,
  );

  const productList = useMemo(
    () => searchData?.products || [],
    [searchData?.products],
  );
  const numColumns = useMemo(() => (checkDevice() ? 4 : 2), []);
  const initialNumToRender = useMemo(() => (checkDevice() ? 6 : 3), []);
  const maxToRenderPerBatch = useMemo(() => (checkDevice() ? 4 : 2), []);
  const windowSize = useMemo(() => (checkDevice() ? 7 : 5), []);

  return (
    <>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? -25 : 0} // Adjust dynamically
        style={styles.flexOne}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.flexOne}>
            <View key={index} style={styles.container}>
              <Spacer type="Vertical" size="m" />
              <ErrorHandler
                componentName={`${TAG} Header`}
                onErrorComponent={<View />}>
                <Header
                  onPressSearchIcon={onPressSearchIcon}
                  useInsets
                  onChangeSearched={handleSearch}
                  value={filters.query}
                  searchAutoFocus={true}
                  // backButton={true}
                  navigation={navigation}
                  searchBar={showSearchBar}
                  setSearchBar={setSearchBar}
                  searchPage
                  // searchIcon
                  // bagIcon
                  // heartIcon
                  style={styles.searchHeader}
                  placeHolder={t('homePage.searchProducts')}
                  // placeHolderText={true}
                />
              </ErrorHandler>
              <Spacer type="Vertical" size="sx" />
              {localLoading && filters?.query.length === 0 ? (
                <SearchLoader />
              ) : (
                <View style={styles.flexOne}>
                  <View style={styles.clearFilterBtnRow} />
                  {searchAlertText ? (
                    <View style={styles.searchStyle}>
                      <Label
                        color="text2"
                        size="mx"
                        fontFamily="Medium"
                        text={t('searchProduct.searchAlertText')}
                      />
                      <Spacer size="sx" />
                    </View>
                  ) : (
                    filters?.query?.length > 0 && (
                      <View style={styles.searchStyle}>
                        <Label
                          color="grey"
                          size="mx"
                          fontFamily="Medium"
                          textTransform="capitalize"
                          text={t('searchProduct.showing')}>
                          {searchData?.searchInfo?.total_hits ? (
                            <Label
                              color="text"
                              size="mx"
                              fontFamily="Medium"
                              textTransform="capitalize"
                              text={` ${searchData?.searchInfo?.total_hits} `}
                            />
                          ) : (
                            <Label
                              color="text"
                              size="mx"
                              fontFamily="Medium"
                              textTransform="capitalize"
                              text={` 0 `}
                            />
                          )}
                          <Label
                            color="grey"
                            size="mx"
                            fontFamily="Medium"
                            textTransform="capitalize"
                            text={t('searchProduct.results')}
                          />
                          <Label
                            color="text"
                            size="mx"
                            fontFamily="Medium"
                            textTransform="capitalize"
                            text={` " ${filters?.query || ''} "`}
                          />
                        </Label>
                        <Spacer size="sx" />
                      </View>
                    )
                  )}
                  <View style={styles.filterStyle}>
                    {hasActiveFilters &&
                    hasNonEmptyFilters &&
                    (filters?.applyFilters?.category?.length > 0 ||
                      filters?.applyFilters?.manufacturer?.length > 0 ||
                      filters?.applyFilters?.price ||
                      filters?.applyFilters?.rating) ? (
                      <Button
                        labelStyle={styles.clearText}
                        onPress={resetAllFilters}
                        ghost
                        size="zero-height"
                        text={t('buttons.clear')}
                      />
                    ) : (
                      <View />
                    )}
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      keyboardShouldPersistTaps="always">
                      <View style={styles.filtersListRow}>
                        {filters?.applyFilters?.category?.length > 0
                          ? filters?.applyFilters?.category?.map(
                              (item, i: number) => (
                                <View
                                  style={styles.filterItemBetweenSpace}
                                  key={i.toString()}>
                                  <ErrorHandler
                                    componentName={`${TAG} Tag`}
                                    onErrorComponent={<View />}>
                                    <Tag
                                      onPress={() =>
                                        removeFilter('category', item)
                                      }
                                      label={item}
                                      labelStyle={styles.filterItemLabel}
                                      style={styles.filterItemTag}
                                      isRightIcon
                                      icon="cross"
                                      iconSize="l"
                                      color="transparentColor"
                                      borderColor="categoryTitle"
                                    />
                                  </ErrorHandler>
                                </View>
                              ),
                            )
                          : null}
                        {filters?.applyFilters?.manufacturer?.length > 0
                          ? filters?.applyFilters?.manufacturer?.map(
                              (item: string, i: number) => (
                                <View
                                  style={styles.filterItemBetweenSpace}
                                  key={i.toString()}>
                                  <ErrorHandler
                                    componentName={`${TAG} Tag`}
                                    onErrorComponent={<View />}>
                                    <Tag
                                      onPress={() =>
                                        removeFilter('manufacturer', item)
                                      }
                                      label={item}
                                      labelStyle={styles.filterItemLabel}
                                      style={styles.filterItemTag}
                                      isRightIcon
                                      icon="cross"
                                      iconSize="l"
                                      color="transparentColor"
                                      borderColor="categoryTitle"
                                    />
                                  </ErrorHandler>
                                </View>
                              ),
                            )
                          : null}
                        {filters?.applyFilters?.price ? (
                          <View style={styles.filterItemBetweenSpace}>
                            <ErrorHandler
                              componentName={`${TAG} Tag`}
                              onErrorComponent={<View />}>
                              <Tag
                                onPress={() => removeFilter('price')}
                                label={
                                  '₹' +
                                  filters?.applyFilters?.price?.min +
                                  '- ₹' +
                                  filters?.applyFilters?.price?.max
                                }
                                labelStyle={styles.filterItemLabel}
                                style={styles.filterItemTag}
                                isRightIcon
                                icon="cross"
                                iconSize="l"
                                color="transparentColor"
                                borderColor="categoryTitle"
                              />
                            </ErrorHandler>
                          </View>
                        ) : null}
                        {filters?.applyFilters?.rating ? (
                          <View style={styles.filterItemBetweenSpace}>
                            <ErrorHandler
                              componentName={`${TAG} Tag`}
                              onErrorComponent={<View />}>
                              <Tag
                                onPress={() => removeFilter('rating')}
                                label={`${t('reviewRating.rating')} ${
                                  filters?.applyFilters?.rating
                                }+ `}
                                labelStyle={styles.filterItemLabel}
                                style={styles.filterItemTag}
                                isRightIcon
                                icon="cross"
                                iconSize="l"
                                color="transparentColor"
                                borderColor="categoryTitle"
                              />
                            </ErrorHandler>
                          </View>
                        ) : null}
                      </View>
                    </ScrollView>
                  </View>
                  <View style={styles.listView}>
                    {showScrollToTopButton && (
                      <TouchableOpacity
                        activeOpacity={0.7}
                        style={styles.topIconView}
                        onPress={() => {
                          scrollToTop();
                        }}>
                        <ImageIcon icon="backToArrowTop" size="mx" />
                        <Spacer size="s" type="Horizontal" />
                        <Label
                          text={t('otherText.gotoTop')}
                          size="m"
                          weight="500"
                          color="whiteColor"
                          style={styles.goTop}
                        />
                      </TouchableOpacity>
                    )}
                    {searchInitiated ? (
                      productList?.length > 0 ? (
                        <FlatList
                          numColumns={numColumns}
                          data={productList}
                          keyExtractor={keyExtractor}
                          ref={flatListRefTop}
                          keyboardShouldPersistTaps="handled"
                          showsVerticalScrollIndicator={false}
                          onScroll={handleScrollWithKeyboardDismiss}
                          ItemSeparatorComponent={productItemSeparator}
                          renderItem={renderProductItem}
                          onEndReachedThreshold={0.7}
                          ListFooterComponent={RenderFooter}
                          onEndReached={fetchMoreData}
                          // getItemLayout={(data, index) => ({
                          //   length: ITEM_HEIGHT,
                          //   offset: ITEM_HEIGHT * index,
                          //   index,
                          // })}
                          initialNumToRender={initialNumToRender}
                          removeClippedSubviews={false}
                          scrollEventThrottle={16}
                          onMomentumScrollBegin={() => setIsScrolling(true)}
                          onMomentumScrollEnd={() => setIsScrolling(false)}
                        />
                      ) : (
                        emptySearchResults()
                      )
                    ) : null}
                  </View>
                </View>
              )}
            </View>
            {viewChildModule == true && (
              <ErrorHandler
                componentName={`${TAG} ChildProductModal`}
                onErrorComponent={<View />}>
                <ChildProductModal
                  viewChildModule={viewChildModule}
                  onClose={setViewChildModule}
                />
              </ErrorHandler>
            )}
            {!localLoading && searchData?.products?.length > 0 && (
              <ErrorHandler
                componentName={`${TAG} FooterButton`}
                onErrorComponent={<View />}>
                <FooterButton
                  shadowProps={true}
                  useInsets={true}
                  separator={memoizedSeparator}
                  buttons={[
                    {
                      text: t('filters.filters'),
                      onPress: () =>
                        searchData?.products?.length > 0
                          ? openFilterModel()
                          : {},
                      ghost: true,
                      iconLeft: 'filterIcons',
                      labelSize: 'l',
                      weight: '400',
                      labelColor: 'text',
                      size: 'large',
                      selfAlign: 'stretch',
                      hidden: false,
                    },
                    {
                      text: t('filters.sort'),
                      subText: filters?.sortBy ? filters?.sortBy : 'rcm',
                      height: 42,
                      iconLeft: 'sortIcons',
                      onPress: () =>
                        searchData?.products?.length > 0 ? openSortModel() : {},
                      ghost: true,
                      labelSize: 'l',
                      weight: '400',
                      labelColor: 'text',
                      selfAlign: 'stretch',
                      hidden: false,
                    },
                  ]}
                />
              </ErrorHandler>
            )}
            <ErrorHandler
              componentName={`${TAG} FilterAndSortModal`}
              onErrorComponent={<View />}>
              <FilterAndSortModal
                visible={modelVisible}
                onClose={setModelVisible}
                // filterOptions={memoizedFilterOptions}
                filterOptions={filterOptions}
                appliedFilter={filters?.applyFilters}
                applyFilters={applyFilters}
                resetApplyFilters={resetAllFilters}
                sortType={filters?.sortBy}
                applySortType={applySort}
                modalType={isModelType}
                pageType="search"
                totalProduct={totalProduct}
                onChange={data => {
                  setPriceLoader(true);
                  applyFilters(data);
                  setTimeout(() => {
                    setPriceLoader(false);
                  }, 1000);
                }}
              />
            </ErrorHandler>
            {suggestionModal && (
              <ErrorHandler
                componentName={`${TAG} SuggestProduct`}
                onErrorComponent={<View />}>
                <SuggestProduct
                  visible={suggestionModal}
                  modelClose={() => setSuggestionModal(false)}
                  isReset={isReset}
                  onSubmit={onSubmitProductSuggestion}
                  navigation={navigation}
                  search={false}
                />
              </ErrorHandler>
            )}
            {successModel && (
              <ErrorHandler
                componentName={`${TAG} SuccessModal`}
                onErrorComponent={<View />}>
                <SuccessModal
                  visible={successModel}
                  title={t('feedback.feedbackSuccess')}
                  onClose={() => setSuccessModel(!successModel)}
                />
              </ErrorHandler>
            )}
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </>
  );
};
export default SearchProductScene;

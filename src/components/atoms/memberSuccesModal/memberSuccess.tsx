import React, { useEffect } from 'react';
import { 
    View, 
    Modal
} from "react-native";
import FastImage from 'react-native-fast-image';
import { useTheme } from '@react-navigation/native';
import stylesWithOutColor from './style';
import Icons from 'common/icons';

type props = {
    modalVisible: boolean;
    setModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

function SuccessModal({ modalVisible, setModalVisible }: props) {
    const { colors } = useTheme();
    const styles = stylesWithOutColor(colors);

    useEffect(() => {
        if (modalVisible) {
            // Auto-close modal after 2.5 seconds
            const timer = setTimeout(() => {
                setModalVisible(false);
            }, 2800);

            return () => clearTimeout(timer);
        }
    }, [modalVisible, setModalVisible]);

    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={modalVisible}
            onRequestClose={() => setModalVisible(false)}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContainer}>
                    <FastImage 
                        source={Icons.dentalSuccess} 
                        style={styles.image}
                        resizeMode={FastImage.resizeMode.contain}
                    />
                    <FastImage
                      resizeMode="contain"
                      source={Icons.successGif}
                      style={styles.image2}
                    />
                </View>
            </View>
        </Modal>
    );
}

export default SuccessModal;
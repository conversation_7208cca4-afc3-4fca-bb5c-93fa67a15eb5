import {ApolloClient} from 'apollo-client';
import {createHttpLink} from 'apollo-link-http';
import {setContext} from 'apollo-link-context';
import {
  InMemoryCache,
  IntrospectionFragmentMatcher,
} from 'apollo-cache-inmemory';
import {persistCache} from 'apollo-cache-persist';
import tokenClass from 'utils/token';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Platform} from 'react-native';
import {onError} from 'apollo-link-error';
import {from} from 'apollo-link';
import {clientError} from 'utils/sendData';
import localStorage from 'utils/localStorage';
import { debugLog } from 'utils/debugLog';
import Config from 'react-native-config';
const introspectionQueryResultData = {
  __schema: {
    types: [],
  },
};

const fragmentMatcher = new IntrospectionFragmentMatcher({
  introspectionQueryResultData,
});

const errorInterceptor = onError(data => {
  const {response} = data;
  try {
    if (response?.data) {
      const responseOptions = {
        query: Object.keys(response.data).join(','),
        created_at: new Date(),
        error: response?.errors ? JSON.stringify(response?.errors) : 'success',
        platform: Platform.OS,
        page: '',
      };
      manageErrorData(responseOptions);
    }
  } catch (e) {
    debugLog(e);
  }
});

const manageErrorData = async responseOptions => {
  const userInfo = await localStorage.get('userInfoData');
  (responseOptions.customer_id = userInfo?.customer?.email
    ? userInfo?.customer?.email
    : null),
    clientError(responseOptions);
};

const httpLink = createHttpLink({
  uri: Config.GraphQL_URL, // magento
  credentials: 'same-origin',
});

const customer = createHttpLink({
  uri: Config.GraphQL_URL,
  credentials: 'same-origin',
});

const cartLink = createHttpLink({
  uri: Config.GraphQL_URL,
  credentials: 'same-origin',
});

const stagingReturn = createHttpLink({
  uri: Config.GraphQL_URL,
  credentials: 'same-origin',
});

const staging = createHttpLink({
  uri: Config.GraphQL_URL,
  credentials: 'same-origin',
});

const authLink = setContext(async (_, {headers}) => {
  const token = await tokenClass.getToken();
  // const country = await getCountry();

  return {
    headers: {
      ...headers,
      'x-api-key': 'da2-b7vqajjrfbgbvjf4fbesbavuhq',
      authorization: token ? `Bearer ${token}` : '',
      platform: Platform.OS,
      version: '97.0.0',
    },
  };
});

const cache = new InMemoryCache({
  dataIdFromObject: o => (o._id ? `${o.__typename}:${o._id}` : null),
  fragmentMatcher,
});

persistCache({
  cache,
  storage: AsyncStorage as any,
  trigger: 'background',
});

export const client = new ApolloClient({
  link: from([authLink, errorInterceptor, httpLink]),
  cache,
  defaultOptions: {
    watchQuery: {},
  },
});

export const customerClient = new ApolloClient({
  link: from([authLink, errorInterceptor, customer]),
  cache,
});

export const cartClient = new ApolloClient({
  link: from([authLink, errorInterceptor, cartLink]),
  cache,
});

export const orderReturnClient = new ApolloClient({
  link: from([authLink, errorInterceptor, stagingReturn]),
  cache,
});

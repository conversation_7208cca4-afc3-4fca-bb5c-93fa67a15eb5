import React, {useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {RouteProp, useTheme} from '@react-navigation/native';
import {Button, PhoneInputText} from 'components/molecules';
import stylesWithOutColor from './style';
import {Label, Spacer} from 'components/atoms';
import {t} from 'i18next';
import {View} from 'react-native';
import OtpScene from 'components/organisms/otpScene';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ChangeEmailOrMobile'>;
};

const VerifyEmailScene = ({navigation, route}: Props) => {
  const TAG = 'VerifyEmailScreen';
  const [shouldShow, setShouldShow] = useState(true);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const [error, setError] = useState({});
  const [otpView, setOtpView] = useState(true);

  const [values, setValues] = useState({
    emailOrPhone: '',
  });

  return (
    <>
      {otpView ? (
        <SafeAreaView style={styles.container}>
          <Spacer size="ex0" />
          <View style={styles.mainView}>
            <Label
              text={t('registerForm.emailVerify')}
              size="mx"
              weight="400"
              lineHeight="xxl"
            />
          </View>

          <Spacer size="x6l" />
          <View style={styles.subContainer}>
            <ErrorHandler
              componentName={`${TAG} PhoneInputText`}
              onErrorComponent={<View />}>
              <PhoneInputText
                testID="txtVerifyEmailId"
                style={styles.inputView}
                inputStyle={styles.inputTextView}
                placeholderColor={colors.text}
                keyboardType={'email-address'}
                placeholder={t('login.enterMailId')}
                tintColor="categoryTitle"
                onChangeText={text =>
                  setValues(prev => ({...prev, emailOrPhone: text}))
                }
                value={values.emailOrPhone}
                error={String(error?.emailOrPhone?.[0] || '')}
              />
            </ErrorHandler>
            <Spacer size="x6l" />
            <ErrorHandler
              componentName={`${TAG} Button`}
              onErrorComponent={<View />}>
              <Button
                style={styles.subCautionerBox}
                text={t('profileDetails.verify')}
                onPress={() => setOtpView(false)}
                labelSize="l"
                type={Object.keys(error).length > 0 ? 'disabled' : 'secondary'}
                radius="sx"
                labelColor="whiteColor"
                weight="500"
              />
            </ErrorHandler>
          </View>
        </SafeAreaView>
      ) : (
        <>
          <OtpScene shouldShow={shouldShow} />
        </>
      )}
    </>
  );
};

export default VerifyEmailScene;

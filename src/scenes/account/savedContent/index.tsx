import React, {useCallback, useEffect, useState} from 'react';
import {Dimensions, Image, Linking, View} from 'react-native';
import {ImageIcon, Label, Separator, Spacer} from 'components/atoms';
import FastImage from 'react-native-fast-image';
import {SafeAreaView} from 'react-native-safe-area-context';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {FlatList, TouchableOpacity} from 'react-native-gesture-handler';
import {Sizes} from 'common';
import LinearGradient from 'react-native-linear-gradient';
import {
  magazine,
  blogs,
  buttons,
  news,
  shorts,
  months,
} from 'staticData';

import {Button, Header} from 'components/molecules';
import {t} from 'i18next';
import {useMemo} from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@types/local';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const SavedContent = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [tab, setTab] = useState('News');
  const [numColumns, setNumColumns] = useState(2);
  const [key, setKey] = useState(1);

  const width = Dimensions.get('window').width;
  const itemMargin = width - 300 - 32;

  const renderNews = ({item}: any) => {
    return (
      <>
        <Spacer size="m" />
        <View style={styles.cardMainView}>
          <View style={styles.flex}>
            <View style={styles.viewData}>
              <View style={styles.nameLabel}>
                <Label color="categoryTitle" size="m" text={item.name} />
                <Spacer type="Horizontal" size="m" />
                <Separator
                  Vertical
                  thickness="x"
                  height="xms"
                  color="categoryTitle"
                />
                <Spacer type="Horizontal" size="m" />
                <Label color="categoryTitle" size="m" text={item.type} />
              </View>
              <View>
                <Label text={item.title} color="categoryTitle" size="mx" />
              </View>
            </View>
            <Spacer size="xm" />
            <View style={styles.dateLabel}>
              <Spacer type="Horizontal" size="sx" />
              <Label color="grey" size="m" text={item.date} />
              <Spacer type="Horizontal" size="xm" />
              <Label weight="600" size="m" text="•" color="grey" />
              <Spacer type="Horizontal" size="xm" />
              <Label color="grey" size="m" text={item.readTime} />
            </View>
            <Spacer size="xm" />
            <View style={styles.iconContainer}>
              <TouchableOpacity>
                <ImageIcon
                  tintColor="text2"
                  icon="newBookmarkIcon"
                  size="xxl"
                />
              </TouchableOpacity>
              <Spacer type="Horizontal" size="l" />
              <TouchableOpacity>
                <ImageIcon tintColor="text2" icon="newShare" size="xxl" />
              </TouchableOpacity>
            </View>
          </View>
          <Spacer type="Horizontal" size="m" />
          <View>
            <FastImage
              source={item.image}
              resizeMode="contain"
              style={styles.cardImage1}
            />
          </View>
        </View>
      </>
    );
  };

  const renderMagazine = useCallback(
    ({item}) => {
      const date = new Date(item?.date_of_publish);
      const monthIndex = date.getMonth();
      const year = date.getFullYear();

      const month = months[monthIndex]?.label || 'Unknown';

      return (
        <View style={styles.magCardView}>
          <View style={styles.title}>
            <Label
              color="text"
              size="m"
              weight="500"
              text={`${item?.magazine_name} ${year}`}
            />
            <ImageIcon icon="downloadButtonIcon" size="xl" />
          </View>
          <Label
            color="text2"
            style={styles.monnthText}
            size="xms"
            align="left"
            text={month}
          />
          <Spacer size="xm" />
          <FastImage
            style={styles.magImage}
            source={{uri: item?.coverPage_url}}
            resizeMode="cover"
          />
          <Spacer size="xm" />
          <View style={styles.footerCard}>
            <View style={styles.subFooterCard}>
              <TouchableOpacity
                onPress={() => Linking.openURL(item?.magazine_url)}
                style={styles.readBtn}>
                <Label
                  size="m"
                  weight="400"
                  text="Read"
                  color="categoryTitle"
                />
              </TouchableOpacity>
            </View>
            <Spacer type="Horizontal" size="x3l" />
            <View style={styles.subFooterCard}>
              <TouchableOpacity>
                <ImageIcon size="xxl" icon="newBookmarkIcon" tintColor="text" />
              </TouchableOpacity>
              <Spacer type="Horizontal" size="xm" />
              <Separator color="grey2" Vertical />
              <Spacer type="Horizontal" size="xm" />
              <TouchableOpacity>
                <ImageIcon size="xxl" icon="share" tintColor="text" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      );
    },
    [styles, months],
  );

  const renderShorts = useCallback(
    ({item, index}: any) => {
      return (
        <View style={styles.itemContainer}>
          <Spacer size="m" />
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('ShortVideos', {
                videoId: item.id,
                source: 'THUMBNAIL',
              });
            }}
            key={index}>
            <Image
              style={styles.img}
              resizeMode="contain"
              source={{uri: item.thumbnail_url}}
            />
            <LinearGradient
              colors={[colors.blackTransparent, colors.black0]}
              style={styles.gradient}
              start={{x: 0, y: 1}}
              end={{x: 0, y: 0}}>
              <View style={styles.reelIcon}>
                <TouchableOpacity>
                  <ImageIcon size="xl" tintColor="white1" icon="newShare" />
                </TouchableOpacity>
                <Spacer size="m" />
                <TouchableOpacity>
                  <ImageIcon
                    size="xl"
                    tintColor="white1"
                    icon="newBookmarkIcon"
                  />
                </TouchableOpacity>
              </View>
              <Label
                text={item.title}
                color="background"
                style={[styles.titles]}
                numberOfLines={3}
                size="mx"
                ellipsizeMode="tail"
              />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      );
    },
    [itemMargin, navigation, styles],
  );
  const renderBlogs = ({item}: any) => (
    <>
      <View style={styles.mainContainer}>
        <View>
          <FastImage
            source={item.icon}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
        <Spacer size="m" />
        <View style={styles.subContainer}>
          <View style={styles.flex}>
            <Label
              color="sunnyOrange6"
              size="mx"
              weight="500"
              text={item.title}
            />
          </View>
          <View style={styles.icons}>
            <TouchableOpacity>
              <ImageIcon icon="newShareIcon" size="xl" />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xm" />
            <TouchableOpacity>
              <ImageIcon icon="newBookmarkIcon" size="xl" />
            </TouchableOpacity>
          </View>
        </View>
        <Spacer size="xm" />
        <View>
          <Label size="l" weight="600" color="text2" text={item.text} />
        </View>
        <Spacer size="xm" />
        <View>
          <Label size="mx" weight="500" color="text2" text={item.name} />
        </View>
      </View>
      <Spacer size="l" />
    </>
  );

  const changeColumns = () => {
    const newNumColumns = numColumns === 2 ? 3 : 2;
    setNumColumns(newNumColumns);
    setKey(key + 1);
  };

  return (
    <SafeAreaView style={styles.mainView}>
      <Header
        navigation={navigation}
        backButton={true}
        tintColorGOBack="text"
        text={t('otherText.savedContent')}
      />
      <View style={styles.buttonView}>
        <FlatList
          data={buttons}
          horizontal
          renderItem={({item}) => (
            <Button
              labelColor={item.data === tab ? 'whiteColor' : 'categoryTitle'}
              labelSize="mx"
              style={[
                styles.buttons,
                item.data === tab
                  ? {backgroundColor: colors.categoryTitle}
                  : null,
              ]}
              weight="400"
              onPress={() => setTab(item.data)}
              text={item.data}
            />
          )}
        />
      </View>
      <View>
        {tab == 'Blogs' ? (
          <FlatList
            showsVerticalScrollIndicator={false}
            data={blogs}
            renderItem={renderBlogs}
          />
        ) : tab == 'Shorts' ? (
          <FlatList
            columnWrapperStyle={styles.listStyle}
            numColumns={numColumns}
            key={key}
            showsVerticalScrollIndicator={false}
            data={shorts?.slice(0, 6)}
            renderItem={renderShorts}
            keyExtractor={(item, index) => item?.id?.toString() + '_' + index}
            ItemSeparatorComponent={() => <Spacer size="m" />}
          />
        ) : tab == 'Magazine' ? (
          <FlatList
            style={styles.cardFlatList}
            columnWrapperStyle={styles.listStyle}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <Spacer size="mx" />}
            contentContainerStyle={{paddingVertical: Sizes.xm}}
            numColumns={numColumns}
            data={magazine}
            key={key}
            renderItem={renderMagazine}
            keyExtractor={(item, index) => `${item?.id}_${index}`}
          />
        ) : tab == 'News' ? (
          <FlatList
            data={news}
            showsVerticalScrollIndicator={false}
            renderItem={renderNews}
            keyExtractor={({item}) => item?.id}
          />
        ) : null}
      </View>
    </SafeAreaView>
  );
};

export default SavedContent;

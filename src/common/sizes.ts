import {Dimensions} from 'react-native';
const window = Dimensions.get('window');
const screen = Dimensions.get('screen');

const Sizes = {
  z: 0.5,
  x: 1,
  xs: 2,
  s: 4,
  sx: 6,
  xm: 8,
  xms: 10,
  m: 12,
  mx: 14,
  l: 16,
  xx: 18,
  xl: 20,
  xsl: 22,
  xxl: 24,
  x26: 26,
  xxxl: 28,
  x3l: 30,
  x4l: 32,
  x34: 34,
  x5l: 36,
  xx4l: 38,
  x6l: 40,
  x42: 42,
  x44: 44,
  x46: 46,
  x7l: 48,
  x8l: 50,
  x52: 52,
  x54: 54,
  x56: 56,
  x58: 58,
  x60: 60,
  x9l: 64,
  x66: 66,
  x68: 68,
  x70: 70,
  x72: 72,
  x74: 74,
  x76: 76,
  x78: 78,
  ex: 80,
  ex82: 82,
  ex84: 84,
  ex88: 88,
  ex90: 90,
  ex92: 92,
  ex96: 96,
  exl: 100,
  ex104: 104,
  ex106: 106,
  ex110: 110,
  ex112: 112,
  ex114: 114,
  ex116: 116,
  ex0: 120,
  ex122: 122,
  ex124: 124,
  ex126: 126,
  ex130: 130,
  ex134: 134,
  ex148: 148,
  ex1: 150,
  ex154: 154,
  ex156: 156,
  ex160: 160,
  ex166: 166,
  ex170: 170,
  ex172: 172,
  ex176: 176,
  ex180: 180,
  ex190: 190,
  ex2l: 200,
  ex210: 210,
  ex218: 218,
  ex226: 226,
  ex234: 234,
  ex248: 248,
  ex250: 250,
  ex270: 270,
  ex290: 290,
  ex3l: 300,
  ex330: 330,
  ex350: 350,
  ex374: 374,
  ex380: 380,
  ex382: 382,
  ex388: 388,
  ex4l: 400,
  ex405: 405,
  ex410: 410,
  ex416: 416,
  ex5l: 500,
  ex510: 510,
  windowWidth: window.width,
  windowHeight: window.height,
  screenWidth: screen.width,
  screenHeight: screen.height,
};

export default Sizes;

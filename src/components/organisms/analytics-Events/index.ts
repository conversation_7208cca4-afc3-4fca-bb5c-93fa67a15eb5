import base64 from 'react-native-base64';
import {LogEvents} from './AnalyticsEvent';
import {Version, WEBSITE_URL} from 'config/environment';
import { debugError } from 'utils/debugLog';
// import {webEngageInAppEvents} from './webEngageInAppEvents';

const AnalyticsEvents = (actionName, eventName, data, userInfo, isLoggedIn) => {
  let eventID;
  if (userInfo?.email) {
    eventID = base64.encode(userInfo?.email.toString());
  } else {
    eventID = base64.encode(
      userInfo?.email?.mobile.toString() + '@dentalkart.user',
    );
  }
  let updatedUserInfo = {
    ...userInfo,
    customer_id: eventID ?? userInfo?.customer_id,
  };
  try {
    let eventData = {
      Owner: 'BMT',
      'Customer Id': updatedUserInfo?.customer_id,
      'App Version': Version,
      'Is User Logged': isLoggedIn,
    };

    switch (actionName) {
      case 'BRAND_VIEW': {
        eventData = {
          ...eventData,
          ...data,
          Source: 'Website',
        };
        break;
      }
      case 'VIEW_PAGE': {
        eventData = {
          ...eventData,
          ...data,
          Source: 'Website',
        };
        break;
      }
      case 'CATEGORY_VIEW': {
        eventData = {
          ...eventData,
          ...data,
          Source: 'Website',
        };
        break;
      }

      case 'PRODUCT_SEARCHED': {
        eventData = {
          ...eventData,
          ...data,
          Source: 'Website',
        };
        break;
      }

      case 'PRODUCT_SCREEN': {
        eventData = {
          ...eventData,
          Brand: data?.brand?.name ?? 'Unknown Brand',
          Category:
            data?.categories?.map(category => category?.name).join(', ') ??
            'Unknown Category',
          SKU: data?.sku ?? 'Unknown SKU',
          'Product Name': data?.name ?? 'Unknown Product',
          Price: data?.pricing?.selling_price ?? 0,
          'Image Url': data?.media?.[0]?.file
            ? `https://images1.dentalkart.com/media/catalog/product/${data?.media?.[0]?.file}`
            : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
          'Product Url': data?.seo?.url_key
            ? `https://www.dentalkart.com/${data?.seo?.url_key}.html`
            : 'https://www.dentalkart.com',
          // Currency: data?.pricing?.currency_symbol ?? '₹',
          Currency: 'INR',
          MRP: data?.pricing?.price ?? 0,
          'Product Id': data?.product_id ?? 'Unknown Product ID',
          'Total Price': Number(
            (data?.pricing?.selling_price ?? 0) * (data?.quantity ?? 1),
          ),
          Quantity: data?.quantity ?? 1,
          'Stock Status': data?.inventory?.is_in_stock
            ? 'In Stock'
            : 'Out of Stock',
          'Product Type': data?.type ?? 'Unknown Type',
          Source: 'Website',
        };
        break;
      }

      case 'CART_VIEWED': {
        const items = data?.items ?? [];

        eventData = {
          ...eventData,
          Quantity: items.map(item => item?.quantity ?? 1),
          SKU: items.map(item => item?.product?.sku ?? 'Unknown SKU'),
          'Parent SKU': items.map(
            item => item?.product?.sku ?? 'Unknown Parent SKU',
          ),
          'Product Name': items.map(
            item => item?.product?.name ?? 'Unknown Product',
          ),
          Currency: 'INR',
          'Total Quantity': data?.total_quantity ?? 1,
          // 'Total Price': data?.pricing_details?.grand_total?.amount?.value ?? 0,
          'Total Price': items?.reduce((sum, item) => {
            return (
              sum +
              (item?.item_pricing_details?.row_total_including_tax?.amount
                ?.value || 0)
            );
          }, 0),
          'COD Status': items.every(item => item?.product?.is_cod === 1)
            ? 'Available'
            : 'Not Available',
          ETA: 'Not Available',
          'Product Url': items.map(item =>
            item?.product?.url_key
              ? `https://www.dentalkart.com/${item?.product?.url_key}.html`
              : 'https://www.dentalkart.com',
          ),
          'Image Url': items.map(item =>
            item?.product?.image?.url
              ? `https://images1.dentalkart.com/media/catalog/product/${item?.product?.image?.url}`
              : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
          ),
          'No. Of Products': items.length,
          'Total Amount':
            data?.pricing_details?.grand_total?.amount?.value ?? 0,
          'Coupon Value':
            data?.pricing_details?.discounts?.find(d => d?.code === 'coupon')
              ?.amount?.value ?? 0,
          'Total Amount Saved':
            data?.pricing_details?.total_savings?.amount?.value ?? 0,
          'Total MRP':
            data?.pricing_details?.item_total_regular_price?.amount?.value ?? 0,
          Coupon: data?.coupon_code?.code ?? 'No Coupon',
          eta:
            data?.deliveryStatusData?.delivery_info[0]
              ?.max_delivery_days_text ?? 'Not Available',
        };

        break;
      }

      case 'USER_LOGGED_IN': {
        let eventID;
        if (data?.customer?.email) {
          eventID = base64.encode(data?.customer?.email.toString());
        } else {
          eventID = base64.encode(
            data?.customer?.mobile.toString() + '@dentalkart.user',
          );
        }
        eventData = {
          'First Name': data?.customer?.firstname ?? 'Unknown',
          'Last Name': data?.customer?.lastname ?? 'Unknown',
          Email: data?.customer?.email ?? 'Unknown',
          'Phone Number': data?.customer?.mobile ?? 'Unknown',
          Source: 'Website',
          'Customer Id': eventID ?? 'Unknown',
        };
        break;
      }

      case 'VIEW_ALL_HOMEPAGE': {
        eventData = {
          ...eventData,
          ...data,
        };
        break;
      }

      case 'PAYMENT_FAILURE': {
        try {
          const items = data?.items ?? [];
          eventData = {
            ...eventData,
            SKU: items.map(item => item?.product?.sku ?? 'Unknown SKU'),
            'Product Name': items.map(
              item => item?.product?.name ?? 'Unknown Product',
            ),
            Currency: data?.cart_currency?.currency_symbol ?? '₹',
            'Total Quantity': data?.total_quantity ?? 1,
            'No. Of Products': items.length,
            'Total Amount':
              data?.pricing_details?.grand_total?.amount?.value ?? 0,
            'Payment Mode': data?.payment_method ?? 'razorpay',
            'Order Id': data?.orderId ?? 'Unknown Order ID',
            Reward: items.reduce(
              (total, item) =>
                total +
                (item?.reward_point_product ?? 0) * (item?.quantity ?? 1),
              0,
            ),
            'Coupon Code': data?.coupon_code?.code ?? 'No Coupon',
            'Coupon Value':
              data?.pricing_details?.discounts?.find(d => d?.code === 'coupon')
                ?.amount?.value ?? 0,
            'Total MRP':
              data?.pricing_details?.item_total_regular_price?.amount?.value ??
              0,
            'Total Amount Saved':
              data?.pricing_details?.total_savings?.amount?.value ?? 0,
            'Total Price':
              data?.pricing_details?.item_total_selling_price?.amount?.value ??
              0,
            Reason: (() => {
              try {
                const parsedReason = JSON.parse(data?.reason ?? '{}');
                return parsedReason?.error?.description ?? 'No Reason Provided';
              } catch (error) {
                return data?.reason ?? 'No Reason Provided';
              }
            })(),
          };
        } catch (error) {
          debugError('Error processing PAYMENT_FAILURE event:', error);
          eventData = {Error: 'Failed to process payment failure event'};
        }
        break;
      }

      case 'CHECKOUT_STARTED': {
        const cartData = data?.data?.cart ?? {};
        const items = cartData?.items ?? [];
        const pricingDetails = cartData?.pricing_details ?? {};

        eventData = {
          ...eventData,
          SKU: items.map(item => item?.product?.sku ?? 'Unknown SKU'),
          'Product Name': items.map(
            item => item?.product?.name ?? 'Unknown Product',
          ),
          // 'Total Price': pricingDetails?.grand_total?.amount?.value ?? 0,
          'Total Price': items?.reduce((sum, item) => {
            return (
              sum +
              (item?.item_pricing_details?.row_total_including_tax?.amount
                ?.value || 0)
            );
          }, 0),
          'Total Quantity': cartData?.total_quantity ?? 1,
          'No. Of Products': items.length ?? 1,
          'Total Amount': pricingDetails?.grand_total?.amount?.value ?? 0,
          'Membership Status': items.some(item => item?.is_member_ship_product)
            ? 'Member'
            : 'Non-Member',
          'Reward Earned': items.reduce(
            (acc, item) =>
              acc + (item?.reward_point_product ?? 0) * (item?.quantity || 1),
            0,
          ),
          Coupon: cartData?.coupon_code?.code ?? 'No Coupon',
          'Coupon Value':
            pricingDetails?.discounts?.find(d => d?.code === 'coupon')?.amount
              ?.value ?? 0,
          'COD Status': items.every(item => item?.product?.is_cod === 1)
            ? 'Available'
            : 'Not Available',
          ETA: data?.eta ?? 'Not Available',
          'Total Amount Saved':
            pricingDetails?.total_savings?.amount?.value ?? 0,
          'Total MRP':
            pricingDetails?.item_total_regular_price?.amount?.value ?? 0,
          Currency: cartData?.cart_currency?.currency_symbol ?? '₹',
          'Product ID': items.map(
            item => item?.product?.id ?? 'Unknown Product ID',
          ),
          'Parent SKU': items.map(
            item => item?.product?.sku ?? 'Unknown Parent SKU',
          ),
          'Shipping Charge':
            pricingDetails?.shipping_charges?.amount?.value ?? 0,
          'Product Price': items.map(
            item => item?.product?.price?.minimalPrice?.amount?.value ?? 0,
          ),
          Brand: items.map(() => 'Waldent'), // Assuming fixed brand name
          Quantity: items.map(item => item?.quantity ?? 1),
          'Image Url': items.map(item =>
            item?.product?.image?.url
              ? `https://images1.dentalkart.com/media/catalog/product/${item?.product?.image?.url}`
              : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
          ),
          'Product Url': items.map(item =>
            item?.product?.url_key
              ? `https://www.dentalkart.com/${item?.product?.url_key}.html`
              : 'https://www.dentalkart.com',
          ),
        };
        break;
      }

      case 'CHECKOUT_VIEW': {
        eventData = {
          ...eventData,
          SKU:
            data?.items?.map(item => item?.product?.sku ?? 'Unknown SKU') ?? [],
          'Product Name':
            data?.items?.map(
              item => item?.product?.name ?? 'Unknown Product',
            ) ?? [],
          'Total Price': data?.items?.reduce((sum, item) => {
            return (
              sum +
              (item?.item_pricing_details?.row_total_including_tax?.amount
                ?.value || 0)
            );
          }, 0),
          'Total Quantity': data?.total_quantity ?? 1,
          'No. Of Products': data?.items?.length ?? 1,
          'Total Amount':
            data?.pricing_details?.grand_total?.amount?.value ?? 0,
          'Membership Status': data?.items?.some(
            item => item?.is_member_ship_product,
          )
            ? 'Member'
            : 'Non-Member',
          'Reward Earned': data?.items?.reduce(
            (acc, item) =>
              acc + (item?.reward_point_product ?? 0) * (item?.quantity || 1),
            0,
          ),
          Coupon: data?.coupon_code?.code ?? 'No Coupon',
          'Coupon Value':
            data?.pricing_details?.discounts?.find(d => d?.code === 'coupon')
              ?.amount?.value ?? 0,
          'COD Status': data?.items?.every(item => item?.product?.is_cod === 1)
            ? 'Available'
            : 'Not Available',
          ETA: data?.eta ?? 'Not Available',
          'Total Amount Saved':
            data?.pricing_details?.total_savings?.amount?.value ?? 0,
          'Total MRP':
            data?.pricing_details?.item_total_regular_price?.amount?.value ?? 0,
          Currency: 'INR',
          'Product Id':
            data?.items?.map(
              item => item?.product?.id ?? 'Unknown Product ID',
            ) ?? [],
          'Parent SKU':
            data?.items?.map(
              item => item?.product?.sku ?? 'Unknown Parent SKU',
            ) ?? [],
          'Shipping Charge':
            data?.pricing_details?.shipping_charges?.amount?.value ?? 0,
          'Product Price':
            data?.items?.map(
              item => item?.product?.price?.minimalPrice?.amount?.value ?? 0,
            ) ?? [],
          Brand:
            data?.items?.map(
              item => item?.product?.manufacturer ?? 'Unknown Brand',
            ) ?? [],
          Quantity: data?.items?.map(item => item?.quantity ?? 1) ?? [],
          'Image Url':
            data?.items?.map(item =>
              item?.product?.image?.url
                ? `https://images1.dentalkart.com/media/catalog/product/${item?.product?.image?.url}`
                : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
            ) ?? [],
          'Product Url':
            data?.items?.map(item =>
              item?.product?.url_key
                ? `https://www.dentalkart.com/${item?.product?.url_key}.html`
                : 'https://www.dentalkart.com',
            ) ?? [],
        };
        break;
      }

      case 'CHECKOUT_COMPLETED': {
        eventData = {
          ...eventData,
          ...data,
        };
        break;
      }

      case 'SHIPPING_DETAILS_SELECTED': {
        eventData = {
          'Country Code': data?.country_id ?? 'Unknown Country',
          City: data?.city ?? 'Unknown City',
          State: data?.region?.region ?? 'Unknown State',
          Region: data?.region?.region_code ?? 'Unknown Region',
          Locality: data?.region?.region ?? 'Unknown Locality',
          Postcode: data?.postcode ?? 'Unknown Postcode',
          'Phone Number': data?.telephone ?? 'Unknown Phone Number',
          'First Name': data?.firstname ?? 'Unknown First Name',
          'Last Name': data?.lastname ?? 'Unknown Last Name',
          Street: data?.street?.[0] ?? 'Unknown Street',
          'Customer Id': updatedUserInfo?.customer_id ?? 'Unknown Customer ID',
        };
        break;
      }

      case 'SHIPPING_DETAILS_UPDATED': {
        eventData = {
          'First Name': data?.firstname?.trim?.() ?? 'Unknown',
          'Last Name': data?.lastname?.trim?.() ?? 'Unknown',
          'Country Code': data?.country_id ?? 'Unknown',
          State: data?.region?.region ?? 'Unknown',
          City: data?.city?.trim?.() ?? 'Unknown',
          'Shipping Address': data?.street?.join?.(' ')?.trim?.() ?? 'Unknown',
          'Phone Number': data?.telephone?.trim?.() ?? 'Unknown',
          'Customer Id': updatedUserInfo?.customer_id ?? 'Unknown',
        };

        break;
      }

      case 'ADDED_TO_CART': {
        eventData = {
          ...eventData,
          Brand: data?.landing_page_entity?.category_id ?? 'Unknown Brand',
          SKU: data?.sku ?? 'Unknown SKU',
          Category: data?.landing_page_entity?.category_id
            ? `${data.landing_page_entity.category_id}`
            : 'Unknown Category',
          'Product Name': data?.name ?? 'Unknown Product',
          // Price: data?.price,
          Price: data?.selling_price ?? 0,
          'Image Url': data?.media?.web_image
            ? `https://images1.dentalkart.com/media/catalog/product/${data?.media?.web_image}`
            : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
          // Currency: data?.currency_symbol ?? '₹',
          Currency: 'INR',
          'Product Url': data?.url_key
            ? `https://www.dentalkart.com/${data?.url_key}.html`
            : 'https://www.dentalkart.com',
          'Total Price': (data?.selling_price ?? 0) * (data?.qty ?? 1),
          'Total Quantity': data?.qty ?? 1,
          Quantity: data?.qty ?? 1,
          'Total MRP':
            (data?.price ?? data?.price?.regularPrice?.amount?.value ?? 0) *
            (data?.qty ?? 1),
          ETA: data?.eta ?? 'Not Available',
          'COD Status': data?.is_salable ? 'Available' : 'Not Available',
          'Total Amount Saved':
            ((data?.price ?? 0) - (data?.selling_price ?? 0)) *
            (data?.qty ?? 1),
          MRP: data?.price ?? data?.price?.regularPrice?.amount?.value ?? 0,
          'Product Id': data?.product_id ?? 'Unknown Product ID',
          'Parent SKU': data?.sku ?? 'Unknown Parent SKU',
          'No. Of Products': 1,
          Source: 'Website',
        };
        break;
      }
      case 'ADDED_TO_CART_GROUP': {

        const {
          filteredProducts,
          product,
          selectedGroupProducts,
          deliveryStatusData,
        } = data; // Extract sources

        if (!filteredProducts || !product || !selectedGroupProducts) {
          debugError('Missing required data sources.');
          break;
        }

        // Extract selected SKUs and their quantities
        // const selectedSKUs = selectedGroupProducts.map(item => item.data.sku);
        const selectedSKUs = (selectedGroupProducts || []).map(
          item => item.data.sku,
        );

        const skuQuantities = Object.fromEntries(
          selectedGroupProducts.map(item => [
            item.data.sku,
            item.data.quantity,
          ]),
        );

        // Filter products from `filteredProducts` that match selected SKUs
        const selectedProducts = filteredProducts
          .filter(item => selectedSKUs.includes(item.sku))
          .map(item => ({
            SKU: item.sku,
            Quantity: skuQuantities[item.sku] ?? 1,
            ParentSKU: product.sku ?? 'Unknown Parent SKU',
            Brand: product?.brand?.name?.trim() || 'Unknown Brand',
            Category:
              product?.categories
                ?.map(category => category?.name ?? 'Unknown Category')
                .join(', ') || 'Unknown Category',
            'Product Name': item?.name?.trim() ?? 'Unknown Product',
            Price: item?.pricing?.selling_price ?? 0,
            'Image Url': product?.media?.find(image => !image?.disabled)?.file
              ? `https://s3.ap-south-1.amazonaws.com/dentalkart-media${
                  product?.media?.find(image => !image?.disabled)?.file
                }`
              : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
            Currency: 'INR',
            'Product Url': item?.attributes?.url_key
              ? `https://www.dentalkart.com/${item.attributes.url_key}.html`
              : 'https://www.dentalkart.com',

            // ✅ Correcting calculations
            // 'Total Price': items?.reduce((sum, item) => {
            //   return (
            //     sum +
            //     (item?.item_pricing_details?.row_total_including_tax?.amount
            //       ?.value || 0)
            //   );
            // }, 0),
            'No. Of Products': selectedGroupProducts.length,
            'Total Quantity': skuQuantities[item.sku] ?? 1,
            'Total MRP':
              (item?.pricing?.price ?? 0) * (skuQuantities[item.sku] ?? 1),
            'Total Amount Saved':
              ((item?.pricing?.price ?? 0) -
                (item?.pricing?.selling_price ?? 0)) *
              (skuQuantities[item.sku] ?? 1),
            MRP: item?.pricing?.price ?? 0,

            ETA: item?.attributes?.dispatch_days ?? 'Not Available',
            'COD Status':
              item?.attributes?.is_cod === '1' ? 'Available' : 'Not Available',

            'Product Id': item?.product_id ?? 'Unknown Product ID',
            'Parent SKU': product.sku ?? 'Unknown Parent SKU',
            Source: 'Website',
          }));
        // ✅ Ensuring correct summation of values
        eventData = {
          Quantity: selectedProducts.map(item => item.Quantity).join(', '),
          Brand: selectedProducts.map(p => p.Brand).join(', '),
          SKU: selectedProducts.map(p => p.SKU).join(', '),
          Category: selectedProducts.map(p => p.Category).join(', '),
          'Product Name': selectedProducts
            .map(p => p['Product Name'])
            .join(', '),
          Price: selectedProducts.map(p => p.Price).join(', '),
          'Image Url': selectedProducts.map(p => p['Image Url']).join(', '),
          Currency: selectedProducts[0]?.Currency ?? '₹',
          'Product Url': selectedProducts.map(p => p['Product Url']).join(', '),

          // ✅ Fixing `Total Price` Calculation
          'Total Price': selectedProducts.reduce(
            (sum, p) => sum + (p.Price ?? 0) * p.Quantity,
            0,
          ),
          'Total Price': selectedProducts.reduce(
            (sum, p) => sum + (p.Price ?? 0) * p.Quantity,
            0,
          ),
          'No. Of Products': selectedProducts.length,

          // ✅ Fixing `Total Quantity`
          'Total Quantity': selectedProducts.reduce(
            (sum, p) => sum + p.Quantity,
            0,
          ),

          // ✅ Fixing `Total MRP` Calculation
          'Total MRP': selectedProducts.reduce(
            (sum, p) => sum + (p.MRP ?? 0) * p.Quantity,
            0,
          ),

          ETA: selectedProducts[0]?.ETA ?? 'Not Available',
          'COD Status': selectedProducts[0]?.['COD Status'] ?? 'Not Available',

          // ✅ Fixing `Total Amount Saved`
          'Total Amount Saved': selectedProducts.reduce(
            (sum, p) => sum + ((p.MRP ?? 0) - (p.Price ?? 0)) * p.Quantity,
            0,
          ),

          // ✅ Fixing `MRP` Calculation
          MRP: selectedProducts.map(item => item?.MRP).join(', '),

          'Product Id': selectedProducts.map(p => p['Product Id']).join(', '),
          'Parent SKU': selectedProducts.map(p => p.SKU).join(', '),
          Source: 'Website',
          eta:
            deliveryStatusData?.delivery_info[0]?.max_delivery_days_text ??
            'Not Available',
        };

        break;
      }
      case 'ADDED_TO_CART_SIMPLE': {
        eventData = {
          ...eventData,
          Brand: data?.brand?.name?.trim() || 'Unknown Brand',
          SKU: data?.sku || 'Unknown SKU',
          Category:
            data?.categories
              ?.map(category => category?.name?.trim() || 'Unknown Category')
              .join(', ') || 'Unknown Category',
          'Product Name': data?.name?.trim() || 'Unknown Product',
          Price: data?.pricing?.selling_price ?? 0,

          // ✅ Get the first available image dynamically
          'Image Url': data?.media?.find(image => !image?.disabled)?.file
            ? `https://s3.ap-south-1.amazonaws.com/dentalkart-media${
                data?.media?.find(image => !image?.disabled)?.file
              }`
            : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',

          Currency: 'INR',
          'Product Url': data?.seo?.url_key
            ? `https://www.dentalkart.com/${data.seo.url_key}.html`
            : 'https://www.dentalkart.com',

          // ✅ Fix `Total Price` to include quantity
          'Total Price': (data?.pricing?.selling_price ?? 0) * (data?.qty ?? 1),
          'No. Of Products': 1,
          'Total Quantity': data?.qty ?? 1,
          Quantity: data?.qty ?? 1,

          // ✅ Fix `Total MRP` to include quantity
          'Total MRP': (data?.pricing?.price ?? 0) * (data?.qty ?? 1),
          ETA: data?.attributes?.dispatch_days ?? 'Not Available',
          'COD Status':
            data?.attributes?.is_cod === '1' ? 'Available' : 'Not Available',

          // ✅ Fix `Total Amount Saved` to include quantity
          'Total Amount Saved':
            ((data?.pricing?.price ?? 0) -
              (data?.pricing?.selling_price ?? 0)) *
            (data?.qty ?? 1),
          MRP: (data?.pricing?.price ?? 0) * (data?.qty ?? 1),

          'Product Id': data?.product_id ?? 'Unknown Product ID',
          'Parent SKU': data?.sku ?? 'Unknown Parent SKU',
          Source: 'Website',
          eta:
            data?.deliveryStatusData?.delivery_info[0]
              ?.max_delivery_days_text ?? '',
        };

        break;
      }
      case 'ADDED_TO_CART_SEARCH': {
        eventData = {
          Brand: data?.manufacturer ?? 'Unknown Brand',
          SKU: data?.sku ?? 'Unknown SKU',
          Category:
            data?.categories_without_path?.join(', ') || 'Unknown Category',
          'Product Name': data?.name?.trim() ?? 'Unknown Product',
          Price:
            data?.special_price ??
            data?.price?.minimalPrice?.amount?.value ??
            0,

          // ✅ Get Product Image
          'Image Url': data?.thumbnail_url
            ? data.thumbnail_url
            : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',

          Currency: 'INR',
          'Product Url': data?.url ?? 'https://www.dentalkart.com',

          // ✅ Fix `Total Price` to include `qty`
          'Total Price':
            (data?.special_price ??
              data?.price?.minimalPrice?.amount?.value ??
              0) * (data?.qty ?? 1),
          'No. Of Products': 1,
          'Total Quantity': data?.qty ?? 1,
          Quantity: data?.qty ?? 1,

          // ✅ Fix `Total MRP` to include `qty`
          'Total MRP':
            (data?.price?.regularPrice?.amount?.value ?? 0) * (data?.qty ?? 1),

          ETA:
            data?.demo_available === 'No' ? 'Not Available' : 'Available Soon',
          'COD Status':
            data?.international_active === 'No' ? 'Available' : 'Not Available',

          // ✅ Fix `Total Amount Saved`
          'Total Amount Saved':
            ((data?.price?.regularPrice?.amount?.value ?? 0) -
              (data?.special_price ??
                data?.price?.minimalPrice?.amount?.value ??
                0)) *
            (data?.qty ?? 1),

          MRP: data?.price?.regularPrice?.amount?.value ?? 0,

          'Product Id': data?.objectID ?? 'Unknown Product ID',
          'Parent SKU': data?.sku ?? 'Unknown Parent SKU',
          Source: 'Website',
        };
        break;
      }

      case 'BUY_NOW': {
        // Extract cart items (ensuring it's always an array)
        const items = Array.isArray(data?.items) ? data.items : [];

        // Compute totals for multiple products
        const totalAmount =
          data?.pricing_details?.grand_total?.amount?.value ?? 0;
        const totalPrice =
          data?.pricing_details?.subtotal_excluding_tax?.amount?.value ?? 0;
        const totalMRP =
          data?.pricing_details?.item_total_regular_price?.amount?.value ?? 0;
        const totalQuantity = data?.total_quantity ?? 0;
        const Quantity = items.map(item => item?.quantity ?? 1).join(', ');
        // Extract product details as comma-separated values
        const productNames = items
          .map(item => item?.product?.name ?? 'Unknown Product')
          .join(', ');
        const skus = items
          .map(item => item?.product?.sku ?? 'Unknown SKU')
          .join(', ');
        const productIds = items
          .map(item => item?.product?.id ?? 'Unknown Product ID')
          .join(', ');
        const productUrls = items
          .map(item =>
            item?.product?.url_key
              ? `${WEBSITE_URL}${item?.product?.url_key}.html`
              : 'https://www.dentalkart.com',
          )
          .join(', ');
        const parentSKUs = items
          .map(item => item?.product?.sku ?? 'N/A')
          .join(', ');

        // Fix for Product Price (comma-separated values)
        const productPrices = items
          .map(item => item?.product?.special_price ?? 0)
          .join(', ');

        // Fix for Brand (Manufacturer field)
        const brandNames = items
          .map(item => item?.product?.manufacturer ?? 'Unknown Manufacturer')
          .join(', ');

        // Get first available image from multiple products
        const imageUrls = items
          .map(item =>
            item?.product?.image?.url
              ? `https://images1.dentalkart.com/media/catalog/product/${item.product.image.url}`
              : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
          )
          .join(', ');

        // Ensure correct values for membership, rewards, and COD status
        const isMember =
          data?.rewards?.total_coins > 0 ? 'Member' : 'Non-Member';
        const rewardEarned = data?.rewards?.total_coins ?? 0;
        const codStatus = items.some(item => item?.product?.is_cod === 1)
          ? 'Available'
          : 'Not Available';

        // Shipping details
        const shippingCharge =
          data?.pricing_details?.shipping_charges?.amount?.applicable_charges ??
          0;
        // Construct final event data
        eventData = {
          'Total Amount': totalAmount,
          Brand: brandNames,
          Quantity: Quantity,
          'Total Price': totalPrice,
          'Product Price': productPrices,
          'Image Url': imageUrls,
          'Total Quantity': totalQuantity,
          'Product Name': productNames,
          SKU: skus,
          'Product Url': productUrls,
          'Product Id': productIds,
          'Parent SKU': parentSKUs,
          'No. Of Products': items.length,
          'Total MRP': totalMRP,
          Currency: data?.cart_currency?.currency_symbol ?? '₹',
          'Membership Status': isMember,
          'Reward Earned': rewardEarned,
          'COD Status': codStatus,
          ETA: data?.eta ?? 'N/A',
          'Shipping Charge': shippingCharge,
        };

        break;
      }

      case 'ORDER_CANCELLED': {
        eventData = {
          ...eventData,
          ...data,
          // SKU: data?.data?.items?.map(item => item?.sku ?? 'Unknown SKU') ?? [],
          // 'Product Name':
          //   data?.data?.items?.map(item => item?.name ?? 'Unknown Product') ??
          //   [],
          // Quantity:
          //   data?.data?.items?.map(item => item?.ordered_qty ?? 1) ?? [],
          // 'Total Quantity': data?.data?.order_summary?.total_product_qty ?? 1,
          // 'Order Id': data?.data?.order_id ?? 'Unknown Order ID',
          // 'Total Amount': data?.data?.order_summary?.order_amount ?? 0,
          // 'Parent SKU':
          //   data?.data?.items?.map(item => item?.parent_id ?? 'N/A') ?? [],
          // Reason: data?.reason ?? 'No Reason Provided', // Now correctly fetching "Duplicate Order Placed"
        };
        break;
      }

      case 'RETRY_PAYMENT': {
        eventData = {
          ...eventData,
          ...data,
        };
        break;
      }

      case 'BANNER_CLICKED': {
        eventData = {
          'Banner Id': data?.landing_page_entity?.product_id ?? 'Unknown',
          'Banner Name': data?.title ?? 'Unknown',
          'Banner Link':
            `${WEBSITE_URL}${data?.landing_page_entity?.url}` ?? 'Unknown',
          'Page Name': data?.PageName ?? 'Unknown',
          Category: data?.landing_page_entity?.category_id ?? 'Unknown',
          'Banner Position': data?.sort_order ?? 'Unknown',
          Source: 'Website',
          Section: data?.Section ?? 'Unknown',
          'Banner Title': data?.Description ?? 'Unknown'
        };
        break;
      }

      case 'SIGN_UP_LOGIN_GOOGLE': {
        let eventID;
        if (data?.customer?.email) {
          eventID = base64.encode(data?.customer?.email.toString());
        } else {
          eventID = base64.encode(
            data?.customer?.mobile.toString() + '@dentalkart.user',
          );
        }
        eventData = {
          ...eventData,
          Method: 'Google',
          Status: data?.customer?.status ?? 'Unknown Status',
          'Customer Id': eventID ?? 'Unknown User ID',
          Email: data?.customer?.email ?? 'Unknown Email',
          'First Name': data?.customer?.firstname ?? 'Unknown Name',
          'Last Name': data?.customer?.lastname ?? 'Unknown lastName',
          'Sign Up Date': data?.customer?.updated_at ?? 'Unknown Date',
          Source: 'Website',
          'User Type':
            data?.customer?.craeted_at !== data?.customer?.updated_at
              ? 'Old user'
              : 'New User',
        };
        break;
      }

      case 'SIGN_UP_EMAIL_PHONE': {
        let eventID;
        if (data?.email) {
          eventID = base64.encode(data?.email.toString());
        } else {
          eventID = base64.encode(data?.mobile.toString() + '@dentalkart.user');
        }
        eventData = {
          'First Name': data?.firstname ?? 'Unknown',
          'Last Name': data?.lastname ?? 'Unknown',
          Email: data?.email ?? 'Unknown',
          'Phone Number': data?.mobile ?? 'Unknown',
          Source: 'Website',
          'Customer Id': eventID ?? 'Unknown',
        };
        break;
      }

      case 'REMOVE_FROM_CART': {
        eventData = {
          Brand: data?.product?.manufacturer ?? 'Unknown',
          Category: 'Unknown', // Not available in JSON
          SKU: data?.product?.sku ?? 'Unknown',
          'Product Name': data?.product?.name ?? 'Unknown',
          Price:
            data?.product?.special_price ??
            data?.product?.price?.regularPrice?.amount?.value ??
            0,
          'Image Url': data?.product?.image?.url
            ? `https://images1.dentalkart.com/media/catalog/product/${data?.product?.image?.url}`
            : 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Web+Icon+%26+Skeleton+Gif/Default-Product-Image-Place-Holder.png',
          'Total Price':
            (data?.product?.special_price ??
              data?.product?.price?.regularPrice?.amount?.value ??
              0) * (data?.quantity ?? 0),
          Currency: 'INR',
          Quantity: data?.quantity ?? 0,
          'Product Url': data?.product?.url_key
            ? `https://www.dentalkart.com/${data?.product?.url_key}.html`
            : 'https://www.dentalkart.com',
          'Product Id': data?.product?.id ?? 'Unknown',
          'COD Status':
            data?.product?.is_cod === 1 ? 'Available' : 'Not Available',
          ETA: data?.product?.dispatch_days
            ? `${data?.product?.dispatch_days} Days`
            : 'Unknown',
          'Parent SKU': data?.product?.sku ?? 'Unknown',
          eta: data?.deliveryInfo ?? 'N/A',
        };
        break;
      }

      case 'BUY_AGAIN': {
        const items = data?.items ?? [];
        const pricing = data?.pricing_details ?? {};

        eventData = {
          // Source: data?.addresses?.country?.name ?? 'Unknown',
          Source: 'Website',
          'Last Order Date': 'N/A',
          Currency: data?.cart_currency?.code ?? 'Unknown',
          'Total Amount': pricing?.subtotal_including_tax?.amount?.value ?? 0,
          Brand: items
            .map(item => item?.product?.manufacturer ?? 'Unknown')
            .join(', '),
          SKU: items.map(item => item?.product?.sku ?? 'Unknown').join(', '),
          'Parent SKU': items
            .map(item => item?.product?.sku ?? 'Unknown')
            .join(', '),
          'Product Name': items
            .map(item => item?.product?.name ?? 'Unknown')
            .join(', '),
          'Total Price': items?.reduce((sum, item) => {
            return (
              sum +
              (item?.item_pricing_details?.row_total_including_tax?.amount
                ?.value || 0)
            );
          }, 0),
          'Total Quantity': data?.total_quantity ?? 0,
          'Total MRP': pricing?.item_total_regular_price?.amount?.value ?? 0,
          'Order Date': new Date().toISOString(),
          'No. Of Products': items.length,
          Reward: data?.rewards?.total_coins ?? 0,
          'Product Price': items
            .map(
              item =>
                item?.product?.special_price ??
                item?.product?.price?.regularPrice?.amount?.value ??
                0,
            )
            .join(', '),
          'Product Id': items
            .map(item => item?.product?.id ?? 'Unknown')
            .join(', '),
          'Discount Amount': pricing?.total_savings?.amount?.value ?? 0,
          'Shipping Charge':
            pricing?.overweight_delivery_charges?.amount?.value ?? 0,
          'Tax Amount':
            pricing?.applied_taxes?.reduce(
              (acc, tax) => acc + (tax.amount?.value ?? 0),
              0,
            ) || 'N/A',
          'Previous Order Id': 'N/A',
          'Previous Order Status': 'N/A',
        };

        break;
      }

      case 'MEMBERSHIP_ADD_TO_CART': {
        eventData = {
          Plan: data?.plan ?? 'Unknown',
          Source: 'Website',
        };
        break;
      }

      case 'TRACK_ORDER': {
        try {
          const safeData = data || {};

          const orderSummary = Array.isArray(
            safeData?.orderDetail?.OrderSummary,
          )
            ? safeData.orderDetail.OrderSummary
            : [];
          const priceDetail = Array.isArray(safeData?.priceDetail)
            ? safeData.priceDetail
            : [];

          const getOrderValue = code => {
            const foundItem = orderSummary.find(item => item?.code === code);
            return foundItem && !isNaN(parseFloat(foundItem.value))
              ? parseFloat(foundItem.value)
              : 0;
          };

          const getPriceDetail = key => {
            const foundItem = priceDetail.find(item => item?.key === key);
            return foundItem && !isNaN(parseFloat(foundItem.value))
              ? parseFloat(foundItem.value)
              : 0;
          };

          const items = Array.isArray(safeData?.item)
            ? safeData.item
            : safeData?.item
            ? [safeData.item]
            : [];
          const totalQuantity = items.reduce(
            (sum, item) => sum + (parseInt(item?.qty) || 0),
            0,
          );
          const noOfProducts = safeData?.item?.items?.length ?? 0;

          eventData = {
            // Assign to existing eventData variable
            Status: safeData?.status ?? 'Pending',
            'Order Id': safeData?.orderDetail?.order_id ?? 'Unknown',
            'Purchase Date': safeData?.orderDetail?.order_date ?? 'Unknown',
            'Shipping Address': safeData?.orderDetail?.shipping_address
              ? `${safeData.orderDetail.shipping_address.name ?? ''}, ${
                  safeData.orderDetail.shipping_address.street ?? ''
                }, ${safeData.orderDetail.shipping_address.city ?? ''}, ${
                  safeData.orderDetail.shipping_address.region ?? ''
                }, ${safeData.orderDetail.shipping_address.country_id ?? ''}, ${
                  safeData.orderDetail.shipping_address.postcode ?? ''
                }`
              : 'No Shipping Address',
            'Total Amount':
              getOrderValue('grand_total') || getPriceDetail('Grand Total'),
            'Total Price':
              getOrderValue('subtotal') || getPriceDetail('Subtotal'),
            'Total Quantity': totalQuantity || 'N/A',
            'Total MRP':
              getOrderValue('discount') + getOrderValue('subtotal') ||
              getPriceDetail('DiscountP97QOT3MI6') + getPriceDetail('Subtotal'),
            'No. Of Products': noOfProducts || 'N/A',
          };
        } catch (error) {
          debugError('Error processing TRACK_ORDER:', error);
        }
        break; // Execution jumps out of switch-case here
      }
      case 'RETURN_ORDER': {
        eventData = {
          Reason: data?.returns?.map(item => item?.reason) ?? [],
          'Order Id': data?.order_id ?? 'Unknown',
          'Payment Mode': 'Unknown',
          Currency: 'INR',
          'Total Amount': 'Unknown',
          Brand: data?.returns?.map(() => 'Unknown') ?? [],
          Category: data?.returns?.map(() => 'Unknown') ?? [],
          SKU: data?.returns?.map(item => item?.sku ?? 'Unknown') ?? [],
          'Parent SKU': data?.returns?.map(() => 'Unknown') ?? [],
          'Product Name':
            data?.returns?.map(item => item?.name ?? 'Unknown') ?? [],
          'Total Price': data?.returns?.map(() => 'Unknown') ?? [],
          'Total Quantity': data?.returns?.map(item => item?.qty ?? 0) ?? [],
          'Total MRP': data?.returns?.map(() => 'Unknown') ?? [],
          'Purchase Date': data?.created_at ?? 'Unknown',
          'No. Of Products': data?.returns?.length ?? 0,
          'Product Price': data?.returns?.map(() => 'Unknown') ?? [],
          'Product Id': data?.returns?.map(() => 'Unknown') ?? [],
        };
        break;
      }

      case 'USER_UPDATE': {
        eventData = {
          'First Name': data?.firstname ?? 'Unknown',
          'Last Name': data?.lastname ?? 'Unknown',
          Email: data?.email ?? 'Unknown',
          'Phone Number': data?.mobile ?? 'Unknown',
          Occupation: data?.type ?? 'Unknown',
          Specialization: data?.speciality ?? 'Unknown',
          Gender: data?.gender ?? 'Unknown',
          'Date Of Birth': data?.dob ?? 'Unknown',
          Anniversary: data?.anniversary ?? 'Unknown',
          'Registration No.': data?.taxvat ?? 'Unknown',
          'GST No.': 'Unknown', // Not available
          'Business Name': data?.business_name ?? 'Unknown',
        };
        break;
      }

      case 'CATEGORY_VIEWED': {
        eventData = {
          'Category Name':
            data?.categoryProducts?.category?.name ?? 'Unknown Category',
          'Category Id': data?.categoryProducts?.category?.id ?? 'Unknown ID',
          'Top 5 Product Url':
            data?.categoryProducts?.products
              ?.slice(0, 5)
              .map(product =>
                product?.url_key
                  ? `https://www.dentalkart.com/${product?.url_key}.html`
                  : 'https://www.dentalkart.com',
              ) ?? [],
          Source: '',
        };
        break;
      }
      default: {
        eventData = {...data};
        break;
      }
    }

    // Finally, log the event.
    LogEvents(eventName, eventData, actionName);

    return;
  } catch (err) {
    debugError('Error in AnalyticsEvents function:', err);
  }
};

export default AnalyticsEvents;

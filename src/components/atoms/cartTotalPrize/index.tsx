import React, {useCallback, useMemo} from 'react';
import {View, TouchableOpacity, Linking} from 'react-native';
import Label from '../label';
import Spacer from '../spacer';
import ListView from '../listView';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {OrderDetailsV1} from '@types/local';
import {t} from 'i18next';
import ImageIcon from '../imageIcon';
import {supportEmail} from 'config/environment';
import {useSelector} from 'react-redux';
import {supportNumber} from 'config/environment';
import { debugLog } from 'utils/debugLog';

type Props = {
  data: OrderDetailsV1;
  rewards?: RewardsSummary;
  type: string;
};

const CartTotalPrize = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {data, rewards, type} = props;
  const {whatsAppLink} = useSelector((state: RootState) => state.app);

  const discount = useMemo(() => {
    return data.find((o: {key: string}) => o.code === 'discount');
  }, [data]);

  const renderItem = useCallback(
    ({item, index}: {item: OrderDetailsV1; index: any}) => {
      const {code} = item;
      if (Number(item?.value) === 0) {
        return <View />;
      } else {
        return (
          <>
            <View key={index} style={styles.amountList}>
              <Label
                size="mx"
                fontFamily="Medium"
                text={
                  code === 'discount'
                    ? `${t('orderTrack.totalDiscount')} - `
                    : item.key + ' - '
                }
                color="text2"
                textTransform="capitalize"
              />
              <Spacer size="s" />
              <Label
                size="mx"
                fontFamily="Medium"
                color="text2"
                text={`${
                  code === 'discount' || code === 'reward_discount' ? '- ' : ''
                }${item?.currency} ${
                  item?.toFixed ? Number(item?.value).toFixed(2) : item?.value
                }`}
              />
            </View>
            <Spacer size="xm" />
          </>
        );
      }
    },
    [],
  );

  const openCallPicker = () => {
    try {
      const url = `tel:${supportNumber}`;
      Linking.openURL(url)
        .then(supported => {
          if (!supported) {
            debugLog("Can't handle url: " + url);
          } else {
            return Linking.openURL(url);
          }
        })
        .catch(err => debugLog('An error occurred', err));
    } catch (error) {
      debugLog('An error occurred', error);
    }
  };

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  return (
    <View>
      <Label
        fontFamily="Medium"
        color="text"
        text={t('orderTrack.orderTotal')}
        size="l"
      />
      <Spacer size="xm" />
      <ListView
        keyExtractor={listViewKeyExtractor}
        data={data.filter(
          (o: {key: string}) => o.key !== 'Grand Total',
          // && o.key !== 'Discount' &&
          // && o.key !== 'Overweight delivery charges',
        )}
        renderItem={renderItem}
      />
      <View style={styles.amountList}>
        <Label
          size="mx"
          fontFamily="Medium"
          color="green2"
          text={
            data.filter((o: {key: string}) => o.key === 'Grand Total')?.[0]?.key
          }
        />
        <Label
          size="mx"
          fontFamily="Medium"
          color="green2"
          text={
            data.filter((o: {key: string}) => o.key === 'Grand Total')?.[0]
              ?.currency +
            ' ' +
            data.filter((o: {key: string}) => o.key === 'Grand Total')?.[0]
              ?.value
          }
        />
      </View>
      {discount?.value > 0 && (
        <>
          <Spacer size="xm" />
          <View style={[styles.amountList, {justifyContent: 'flex-end'}]}>
            <Label
              size="mx"
              fontFamily="Medium"
              color="green2"
              text={`${t('checkOut.saved')} ${discount?.currency} ${
                discount?.toFixed
                  ? Number(discount?.value).toFixed(2)
                  : discount?.value
              }`}
            />
          </View>
        </>
      )}
      {(rewards?.rewardpoints_used > 0 || rewards?.rewardpoints_earned > 0) && (
        <>
          <View style={styles.lineStyle} />
          <Label
            size="l"
            fontFamily="Medium"
            color="text"
            text={t('orderTrack.coins')}
          />
          <Spacer size="xm" />
          <View style={styles.rowCenter}>
            {rewards?.rewardpoints_earned > 0 && (
              <>
                <ImageIcon size="xx" icon="coin" />
                <Spacer size="s" type="Horizontal" />
                <Label
                  fontFamily="Medium"
                  color="text2"
                  text={`${t('orderTrack.earned')} - ${
                    rewards?.rewardpoints_earned
                  }`}
                  size="mx"
                />
                <Spacer size="mx" type="Horizontal" />
              </>
            )}
            {rewards?.rewardpoints_used > 0 && (
              <>
                <ImageIcon size="xx" icon="coin" />
                <Spacer size="s" type="Horizontal" />
                <Label
                  fontFamily="Medium"
                  text={`${t('orderTrack.spent')} ${
                    rewards?.rewardpoints_used
                  }`}
                  size="mx"
                  color="text2"
                />
              </>
            )}
          </View>
        </>
      )}
      <View style={styles.lineStyle} />
      <Label
        text={t('orderTrack.helpCenter')}
        size="l"
        fontFamily="Medium"
        color="text"
      />
      <Spacer size="xm" />
      <View style={styles.emailView}>
        <Label
          fontFamily="Medium"
          text={t('orderTrack.helpDes')}
          size="mx"
          color="text2"
        />
        <TouchableOpacity
          testID='tOSupportEmail'
          style={styles.rowCenter}
          onPress={() => Linking.openURL(`mailto:${supportEmail}`)}>
          <Label
            fontFamily="Medium"
            text={t('login.supportEmail')}
            size="mx"
            color="text2"
            textDecorationLine="underline"
          />
        </TouchableOpacity>
      </View>
      <Spacer size="xm" />
      <TouchableOpacity 
      testID='tOOpenCallPicker'
      style={styles.rowCenter} onPress={openCallPicker}>
        <ImageIcon size="xxl" icon={'callIcon'} tintColor="text2" />
        <Spacer size="s" type="Horizontal" />
        <Label
          color="text2"
          fontFamily="Medium"
          text={'+91 728 9999 459'}
          size="mx"
        />
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(CartTotalPrize);

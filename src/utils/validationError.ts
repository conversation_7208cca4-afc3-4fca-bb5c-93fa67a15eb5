import * as yup from 'yup';
import {phoneReg} from './utils';

const {object, string} = yup;

export const loginValidationSchema = yup.object().shape({
  phoneNumber: yup
    .string()
    .required('validations.enterEmailPhone')
    .test(
      'is-email-or-number',
      'validations.validEmailPhone',
      function (value) {
        if (/@/.test(value)) {
          return yup.string().email().isValidSync(value);
        } else {
          return yup
            .string()
            .matches(phoneReg, 'validations.number')
            .min(10, 'validations.characters')
            .isValidSync(value);
        }
      },
    ),
});
export const getValidationSchema = (field: 'email' | 'phoneNumber' | null) => {
  return yup.object().shape({
    phoneNumber: field === 'phoneNumber'
      ? yup
          .string()
          .required('validations.enterPhoneValidation')
          .matches(phoneReg, 'validations.validPhone')
          .min(10, 'validations.validPhone')
          .max(10, 'validations.validPhone')
      : yup.string().nullable(),

    email: field === 'email'
      ? yup
          .string()
          .required('validations.enterEmailValidation')
          .email('validations.invalidEmail')
      : yup.string().nullable(),
  });
};

export const getUsePsswordValidationSchema = (field: 'email' | 'phoneNumber' | null) => {
  return yup.object().shape({
    phoneNumber: field === 'phoneNumber'
      ? yup
          .string()
          .required('validations.enterPhoneValidation')
          .matches(phoneReg, 'validations.validPhone')
          .min(10, 'validations.validPhone')
          .max(10, 'validations.validPhone')
      : yup.string().nullable(),

    email: field === 'email'
      ? yup
          .string()
          .required('validations.enterEmailValidation')
          .email('validations.validEmailPhone')
      : yup.string().nullable(),
        password: yup
    .string()
    .required('validations.password')
    .min(6, 'validations.passwordLimit'),
  });
};

export const loginPasswordValidationSchema = yup.object().shape({
  phoneNumber: yup
    .string()
    .required('validations.enterPhoneValidation')
    .test(
      'is-email-or-number',
      'validations.validEmailPhone',
      function (value) {
        if (/@/.test(value)) {
          return yup.string().email().isValidSync(value);
        } else {
          return yup
            .string()
            .matches(phoneReg, 'validations.number')
            .min(10, 'validations.characters')
            .isValidSync(value);
        }
      },
    ),
  password: yup
    .string()
    .required('validations.password')
    .min(6, 'validations.passwordLimit'),
});

export const signUpValidationSchema = yup.object().shape({
  emailPhoneNumber: yup
    .string()
    .required('validations.validPhone')
    .test('is-email-or-number', 'validations.validPhone', function (value) {
      if (/@/.test(value)) {
        return yup.string().email().isValidSync(value);
      } else {
        return yup
          .string()
          .matches(phoneReg, 'validations.number')
          .min(10, 'validations.characters')
          .isValidSync(value);
      }
    }),
});

export const getSignUpValidationSchema=(field: 'email' | 'phoneNumber' | null) => {
  return yup.object().shape({
    emailPhoneNumber: field === 'phoneNumber'
      ? yup
          .string()
          .required('validations.enterPhoneValidation')
          .matches(phoneReg, 'validations.validPhone')
          .min(10, 'validations.validPhone')
      : yup.string().nullable(),

    email: field === 'email'
      ? yup
          .string()
          .required('validations.enterEmailValidation')
          .email('validations.validEmailPhone')
      : yup.string().nullable(),
  });
};



export const otpValidationSchema = (showPass: boolean) =>
  yup.object().shape({
    otp: yup
      .string()
      .min(6, 'validations.OTPRequired')
      .max(6, 'validations.OTPRequired')
      .required('validations.OTPRequired'),
    newPassword: showPass
      ? yup
          .string()
          .min(6, 'validations.otpValid')
          .required('validations.password')
      : yup.string(),
  });

export const editProfileValidationSchema = yup.object().shape({
  firstname: yup.string().required('validations.firstNameReq'),
  lastname: yup.string().required('validations.lastNameReq'),
  type: yup.string().required('validations.typeReq'),
  gender: yup.string().required('validations.genderReq'),
  dob: yup.string().required('validations.dobReq'),
  email: yup
    .string()
    .email('validations.emailValid')
    .required('validations.emailReq'),
  mobile: yup
    .string()
    .required('validations.mobileRequired')
    .test('is-email-or-number', 'validations.validPhone', function (value) {
      return yup
        .string()
        .matches(/^[56789][0-9]+$/, 'validations.number')
        .min(10, 'validations.characters')
        .isValidSync(value);
    }),
});

export const bulkFormValidations: any = yup.object().shape({
  userName: yup
    .string()
    .required('validations.nameField')
    .matches(/^[a-zA-Z\s]+$/, 'validations.nameAlphabets'),
  userPhone: yup
    .string()
    .required('validations.phoneField')
    .matches(/^\d{10}$/, 'validations.phoneCorrect'),
  userEmail: yup
    .string()
    .required('validations.emailField')
    .email('validations.emailCorrect'),
  userPincode: yup
    .string()
    .required('validations.pinCode')
    .length(6, 'validations.pinCodeValid'),
  address: yup.string().required('validations.addressField'),
  expectedPrice: yup.number().required('validations.exPrice'),
  bulkQuantity: yup.number().required('validations.quantityField'),
});

export const productFeedbackSchema: any = yup.object().shape({
  quality: yup
    .string()
    .required('validations.qualityReq')
    .matches(/^[a-zA-Z ]+$/, 'validations.qualityAlphabets'),
  price: yup
    .number()
    .required('validations.priceReq')
    .positive('validations.pricePosNum')
    .integer('validations.priceInt')
    .typeError('validations.priceNum'),
  feedback: yup
    .string()
    .required('validations.feedbackReq')
    .min(1, 'validations.feedbackLong'),
});

export const validateRatingSchema: any = yup.object().shape({
  rating: yup
    .number()
    .required('validations.selectRating')
    .min(1, 'validations.minRatingReq')
    .max(5, 'validations.maxRatingReq'),
  title: yup.string().trim().required('validations.titleReq'),
  content: yup.string().trim().required('validations.descriptionReq'),
});

export const validateSellDentalkart = () => {
  const phoneRegExp =
    /^((\+[1-9]{1,4}[ \-]*)|(\([0-9]{2,3}\)[ \-]*)|([0-9]{2,4})[ \-]*)*?[0-9]{3,4}?[ \-]*[0-9]{3,4}?$/;
  const gstRegExp = /^\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}$/;
  return yup.object().shape({
    companyName: yup.string().required('validations.companyName'),
    companyType: yup.string().required('validations.companyRegister'),
    gstNumber: yup
      .string()
      .required('validations.gstNumber')
      .matches(gstRegExp, 'validations.gstRequired'),
    mobile: yup
      .string()
      .required('validations.mobileRequired')
      .matches(phoneRegExp, 'validations.phoneNumberRequired'),
    emailId: yup
      .string()
      .email('validations.emailInvalid')
      .required('validations.emailRequried'),
    city: yup
      .string()
      .required('validations.city')
      .matches(/^[a-zA-Z ]*$/, 'validations.city'),
    address: yup.string().required('validations.addressIsRequired'),
    productName: yup.string().required('validations.productName'),
    catalogue: yup.string().required('validations.uploadCatalogue'),
  });
};

export const forgotValidationSchema: any = yup.object().shape({
  password: yup
    .string()
    .required('validations.password')
    .min(6, 'validations.passwordLimit'),

  confirmPassword: yup
    .string()
    .required('validations.confirmPassword')
    .oneOf([yup.ref('password')], 'validations.passwordsMustMatch'),
});

export const resetPassValidationSchema = yup.object().shape({
  newPassword: yup
    .string()
    .required('validations.enterPassword')
    .min(6, 'validations.sortPassword'),
  confirmPassword: yup
    .string()
    .required('validations.confirmPassword')
    .oneOf([yup.ref('newPassword')], 'validations.passwordMatch'),
});

export const registerValidationSchema = () => {
  const phoneRegExp =
    /^((\+[1-9]{1,4}[ \-]*)|(\([0-9]{2,3}\)[ \-]*)|([0-9]{2,4})[ \-]*)*?[0-9]{3,4}?[ \-]*[0-9]{3,4}?$/;
  return yup.object().shape({
    mobileNumber: yup
      .string()
      .required('validations.mobileRequired')
      .min(10, 'validations.characters')
      .max(10, 'validations.characters')
      .matches(phoneRegExp, 'validations.phoneNumberRequired'),
    email: yup
      .string()
      .email('validations.emailInvalid')
      .required('validations.emailRequried'),
    firstname: yup
      .string()
      .max(30, 'validations.firstnameLimit')
      .required('validations.firstname')
      .matches(/^[a-zA-Z ]+$/, 'validations.onlyString'),
    lastname: yup
      .string()
      .max(30, 'validations.firstnameLimit')
      .required('validations.lastNameRequired')
      .matches(/^[a-zA-Z ]+$/, 'validations.onlyString'),
    password: yup
      .string()
      .min(6, 'validations.passwordLimit')
      .required('validations.password'),
  });
};

export const changeEmailValidationSchema = () => {
  const phoneRegExp =
    /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
  return object({
    type: string(),
    emailOrPhone: string().when('type', {
      is: val => val === 'email',
      then: schema =>
        schema.required('validations.address').email('validations.validEmail'),
      otherwise: schema =>
        schema
          .min(10, 'validations.phoneNumberRequired')
          .required('validations.telephone')
          .matches(phoneRegExp, 'validations.notValid'),
    }),
  });
};

export const otpVerifyValidationSchema = yup.object().shape({
  otp: yup
    .string()
    .min(6, 'validations.OTPRequired')
    .max(6, 'validations.OTPRequired')
    .required('validations.OTPRequired'),
});

import React, {useCallback, useState} from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {TouchableOpacity, View, ViewProps} from 'react-native';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import {likeNews} from 'api';
import Share from 'react-native-share';
import stylesWithOutColor from './style';
import {showSuccessMessage} from 'utils/show_messages';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import Icons from 'common/icons';
import {useMemo} from 'react';
import { debugLog } from 'utils/debugLog';

type Props = {
  likeText?: string;
  shareText?: string;
  saveText?: string;
  likeIcon?: keyof typeof Icons | ['likeIcon'];
  shareIcon?: keyof typeof Icons;
  saveIcon?: keyof typeof Icons;
  onPress?: () => void;
  onSavePress?: () => void;
  news: News;
  style?: ViewProps['style'];
  isSaved: boolean;
};

const LikeShare = ({
  likeText,
  shareText,
  saveText,
  style,
  news,
  onSavePress,
  isSaved,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [likedNewsId, setLikedNewsId] = useState<News['id'][]>([]);

  // -----------like news------
  const likeNewsSave = useCallback(async () => {
    const {data} = await likeNews({id: news.id});
    if (data?.likeNews) {
      setLikedNewsId([...likedNewsId, news.id]);
      showSuccessMessage(t('toastMassages.likeSuccess'));
    } else {
      debugLog('error');
    }
  }, [likedNewsId, news]);

  // ----------share ------------
  let sharePressed = () => {
    let options = {
      url: news.source_link,
      title: news.title,
      message: '',
    };
    Share.open(options)
      .then(res => {
        debugLog(res);
      })
      .catch(err => {
        err && debugLog(err);
      });
  };

  return (
    <View style={[styles.socialBody, style]}>
      <TouchableOpacity onPress={likeNewsSave} style={styles.directions}>
        <ImageIcon
          tintColor="textLight"
          icon={likedNewsId.includes(news?.id) ? 'likeIcon' : 'likeBlank'}
        />
        <Spacer type="Horizontal" size="xm" />
        <Label text={likeText} />
      </TouchableOpacity>
      <TouchableOpacity onPress={sharePressed} style={styles.directions}>
        <ImageIcon tintColor="textLight" icon={'shareFillIcon'} />
        <Spacer type="Horizontal" size="xm" />
        <Label text={shareText} />
      </TouchableOpacity>
      <TouchableOpacity onPress={onSavePress} style={styles.directions}>
        <ImageIcon
          tintColor="textLight"
          icon={isSaved ? 'saveIcon' : 'bookmarkIcon'}
        />
        <Spacer type="Horizontal" size="xm" />
        <Label text={saveText} />
      </TouchableOpacity>
    </View>
  );
};

export default LikeShare;

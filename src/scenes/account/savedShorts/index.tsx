import React, {useState, useEffect} from 'react';
import {FlatList, View} from 'react-native';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Header} from 'components/molecules';
import {Spacer, ShortsItem} from 'components/atoms';
import {getShorts} from 'services/shorts';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {ShortsLoader} from 'skeletonLoader';
import {useMemo} from 'react';

type Props = {
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
};

const SavedShorts = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [loading, setLoading] = useState<boolean>(false);
  const [reelsData, setReelsData] = useState<Shorts[]>([]);

  useEffect(() => {
    getVideos();
  }, []);

  const getVideos = async () => {
    setLoading(true);
    const response = await getShorts();
    const {status, data} = response;
    if (status && data.status === 'success') {
      setReelsData(data?.data?.rows);
    }
    setLoading(false);
  };

  const renderItems = ({item, index}: any) => {
    return <ShortsItem item={item} index={index} navigation={navigation} />;
  };

  return (
    <View style={styles.mainContainer}>
      <Header
        backButton
        navigation={navigation}
        searchIcon={true}
        bagIcon={true}
        text={t('shorts.savedShorts')}
      />
      <View style={styles.container}>
        {loading ? (
          <ShortsLoader />
        ) : (
          <FlatList
            columnWrapperStyle={styles.videoFList}
            numColumns={2}
            data={reelsData}
            renderItem={renderItems}
            keyExtractor={(item, index) => item?.id?.toString() + '_' + index}
            ItemSeparatorComponent={() => <Spacer size="xm" />}
          />
        )}
      </View>
    </View>
  );
};

export default SavedShorts;

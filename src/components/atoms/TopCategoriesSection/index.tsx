import React, {useCallback, useMemo} from 'react';
import {FlatList, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {checkDevice} from 'utils/utils';
import {Spacer} from 'components/atoms';
import <PERSON>rrorHandler from 'utils/ErrorHandler';
import stylesWithoutColor from './style';
import CategoriesItem from '../categoriesItem';
import { Sizes } from 'common';

type Props = {
  sectionData: SectionElement[]; // sectionDataMap[section.id]
  navigation: any;
  filter?: boolean;
  selectedCategory?: SectionElement;
  onSelect?: (item: SectionElement) => void;
  onClear?: () => void;
};

const TopCategoriesSection: React.FC<Props> = ({
  sectionData,
  navigation,
  filter = false,
  selectedCategory,
  onSelect,
  onClear,
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithoutColor(colors), [colors]);

  const renderCategoryItem = useCallback(
    ({item, index}) => (
      <ErrorHandler
        componentName="TopCategoriesItem"
        onErrorComponent={<View />}>
        <CategoriesItem
          index={index}
          item={item}
          navigation={navigation}
          filter={filter}
          selectedCategory={selectedCategory}
          onSelect={onSelect}
          onClear={onClear}
        />
      </ErrorHandler>
    ),
    [navigation, filter, selectedCategory, onSelect, onClear],
  );

  const keyExtractor = useCallback((_, index) => index.toString(), []);
    const initialNumToRender = useMemo(() => checkDevice() ? 8 : 5, []);
    const maxToRenderPerBatch = useMemo(() => checkDevice() ? 8 : 5, []);
    const windowSize = useMemo(() => checkDevice() ? 7 : 5, []);

  return (
    <FlatList
      showsHorizontalScrollIndicator={false}
      style={styles.viewList}
      bounces={false}
      horizontal
      data={sectionData}
      extraData={sectionData}
      renderItem={renderCategoryItem}
      keyExtractor={keyExtractor}
      removeClippedSubviews
      updateCellsBatchingPeriod={50}
      initialNumToRender={initialNumToRender}
      maxToRenderPerBatch={maxToRenderPerBatch}
      windowSize={windowSize}
      decelerationRate="fast"
      getItemLayout={(_, index) => ({
        length: checkDevice() ? Sizes.exl : Sizes.x70,
        offset: (checkDevice() ? Sizes.exl : Sizes.x70) * index,
        index,
      })}
      ItemSeparatorComponent={() => <Spacer size="s" />}
    />
  );
};

export default React.memo(TopCategoriesSection);

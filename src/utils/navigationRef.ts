import {NavigationContainerRef} from '@react-navigation/native';
import * as React from 'react';
import {RootStackParamsList} from 'routes';

export const navigationRef =
  React.createRef<NavigationContainerRef<RootStackParamsList>>();

export function reset(name: string) {
  navigationRef?.current?.reset(name);
}

export function navigate(...params: any) {
  navigationRef?.current?.navigate(...params);
}

export function resetNavigation(name: string) {
  navigationRef?.current?.reset({
    index: 0,
    routes: [{ name }], // This ensures proper stack reset
  });
}

export function goBack() {
  if (navigationRef?.current?.canGoBack()) {
    navigationRef?.current?.goBack();
  }
}

export function push(name: string, params?: any) {
  navigationRef?.current?.push(name, params);
}

export function replace(name: string, params?: any) {
  navigationRef?.current?.replace(name, params);
}

export function isReady() {
  return navigationRef?.current?.isReady();
}

// Helper function to safely navigate (checks if ready)
export function safeNavigate(...params: any) {
  if (navigationRef?.current?.isReady()) {
    navigationRef?.current?.navigate(...params);
  }
}

// Helper function to safely reset navigation
export function safeResetNavigation(name: string) {
  if (navigationRef?.current?.isReady()) {
    navigationRef?.current?.reset({
      index: 0,
      routes: [{ name }],
    });
  }
}

// Export default object for easy importing
export default {
  navigate,
  safeNavigate,
  reset,
  resetNavigation,
  safeResetNavigation,
  goBack,
  push,
  replace,
  isReady,
};
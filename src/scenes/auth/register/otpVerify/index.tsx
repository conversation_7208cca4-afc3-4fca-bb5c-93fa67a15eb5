import React, { useCallback, useEffect, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Formik } from 'formik';
import { Button, PhoneInputText } from 'components/molecules';
import stylesWithOutColor from './style';
import {
  Label,
  Spacer,
  Link,
  Separator,
  LoginGoogle,
  ImageIcon,
} from 'components/atoms';
import { RootStackParamsList } from 'routes';
import OtpScene from 'components/organisms/otpScene';
import tokenClass from 'utils/token';
import { showErrorMessage, showSuccessMessage } from 'utils/show_messages';
import { useDispatch, useSelector } from 'react-redux';
import {
  generateNewCart,
  getUserInfo,
  setIsLoggedIn,
} from 'app-redux-store/slice/appSlice';
import { setLoading } from 'app-redux-store/slice/appSlice';
import { t } from 'i18next';
import { useTheme, RouteProp } from '@react-navigation/native';
import { socialLogin, userLogin, verifyOTP } from 'services/auth';
import { checkReferralCode } from 'services/account';
import { getSignUpValidationSchema, getValidationSchema, signUpValidationSchema } from 'utils/validationError';
import { FlatList, Platform, StatusBar, TouchableOpacity, View } from 'react-native';
import ErrorHandler from 'utils/ErrorHandler';
import { useMemo } from 'react';
import { debugError } from 'utils/debugLog';
import MobileNumberModal from 'components/organisms/login-with-otp/mobileNumberModal/MobileNumberModal';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import TextInputBox from 'components/molecules/textInputBox';
import { RootState } from '@types/local';
import { Sizes } from 'common';
import { openWhatsApp } from 'utils/utils';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  onResend: () => void;
  route: RouteProp<RootStackParamsList, 'RegisterOtp'>;
};
const RegisterOtpScene = ({ navigation, onResend, route }: Props) => {
  const TAG = 'RegisterOtpScreen';
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [otpScene, setOtpScene] = useState(false);
  const [editableAccess, setEditableAccess] = useState(false);
  const dispatch = useDispatch();
  const [emailPhoneNumber, setEmailPhoneNumber] = useState(
    /^[0-9]+$/.test(route?.params?.email) ? route?.params?.email : '',
  );
  const {whatsAppLink} = useSelector((state: RootState) => state.app);

  const [type, setType] = useState('email');
  const [apiError, setApiError] = useState(null);
  const [apiErrorOtp, setApiErrorOtp] = useState(null);
  const [isVerify, setIsVerify] = useState(false);
  const [shouldShow, setShouldShow] = useState(true);
  const [referCode, setReferCode] = useState('');
  const [enableRefer, setEnableRefer] = useState(false);
  const [mobileNumberModal, setMobileNumberModal] = useState(true);
  const [showEmail, setShowEmail] = useState(false);
  const [activeField, setActiveField] = useState<'email' | 'phoneNumber' | null>('phoneNumber');

  useEffect(() => {
    if (!otpScene) {
      setEditableAccess(false);
    }
  }, [otpScene]);

  const LoginWithOtpResend = useCallback(async () => {
    dispatch(setLoading(true));
    const { data, status } = await userLogin({
      recipient: emailPhoneNumber,
      action: 'signup',
      authentication_type: emailPhoneNumber.includes('@') ? 'email' : 'mobile',
    });
    dispatch(setLoading(false));
    if (data && status) {
      setShouldShow(true);
      showSuccessMessage(data?.message);
    } else {
      showErrorMessage(data?.message);
    }
  }, [emailPhoneNumber, dispatch]);

  const userSignUp = useCallback(async (values: any) => {
    if (values?.refer) {
      const { data, status } = await checkReferralCode(values?.refer);
      if (data && status) {
        setReferCode(values?.refer);
        onRegister(values);
      } else {
        showErrorMessage(data?.message);
      }
    } else {
      onRegister(values);
    }
  }, []);
  useEffect(() => {
    console.log(activeField, 'activeFilesd')
  }, [activeField])
  const onRegister = async values => {
    dispatch(setLoading(true));
    console.log(values, 'values****')
    const currentValue = values?.emailPhoneNumber ? values?.emailPhoneNumber : values?.email
    console.log(activeField, 'activeField')
    const { data, status } = await userLogin({
      recipient: currentValue,
      action: "m_login_signup",
      authentication_type: currentValue.includes('@')
        ? 'email'
        : 'mobile',
    });
    dispatch(setLoading(false));
    if (data && status) {
      showSuccessMessage(data?.message);
      setEmailPhoneNumber(currentValue);
      setOtpScene(true);
    } else {
      setApiError(data?.message);
      showErrorMessage(data?.message);
    }
  };

  const verifyOtp = useCallback(
    async (values: any) => {
      dispatch(setLoading(true));
      setEditableAccess(true);
      console.log(values, 'values****')
      const { data, status } = await verifyOTP({
        recipient: emailPhoneNumber,
        action: "m_login_signup",
        verification_type: 'otp',
        authentication_type: emailPhoneNumber.includes('@')
          ? 'email'
          : 'mobile',
        credential: values,
        new_password: '',
      });
      dispatch(setLoading(false));
      if (data && status) {
        setApiErrorOtp(null);
        navigation.navigate('ProfileCompletion', {
          phoneNumber: emailPhoneNumber,
          source: 'registerWithMobile'
        });
        console.log("Resiger Complete");
        
        showSuccessMessage(data?.message);
      } else {
        const match = data?.message?.match(
          /(\d+) wrong attempts, (\d+) more left/,
        );
        if (match?.length > 1 && parseInt(match[1]) === 5) {
          navigation.pop(2);
        }
        setEditableAccess(false);
        setApiErrorOtp(data.message);
      }
    },
    [emailPhoneNumber],
  );

  // ================google login====================
  const getUserData = useCallback(
    async (googleResponse: any) => {
      try {
        dispatch(setLoading(true));

        const { data } = await socialLogin({
          entity_type: 'google',
          token: googleResponse.idToken,
        });

        if (data?.token) {
          setApiError(null);

          await Promise.all([
            tokenClass.setToken(data.token),
            dispatch(setIsLoggedIn(true)),
            dispatch(getUserInfo()),
            dispatch(generateNewCart()),
          ]);

          showSuccessMessage('Login Success');
          navigation.navigate('Tab', { screen: 'Shop' });
        } else {
          setApiError('Invalid login response');
        }
      } catch (error) {
        debugError('Login Error:', error);
        setApiError('Login failed. Please try again.');
      } finally {
        dispatch(setLoading(false));
      }
    },
    [navigation, dispatch]
  );


  const completeOtpTimer = () => {
    setShouldShow(false);
  };

  const onSetLoginBack = () => {
    setOtpScene(false);
    setShouldShow(true);
    if (enableRefer) {
      setEnableRefer(true);
      setReferCode(referCode);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
     <>
            {otpScene ? (
              <ErrorHandler
                componentName={`${TAG} OtpScene`}
                onErrorComponent={<View />}>
                <OtpScene
                  emailPhoneNumber={emailPhoneNumber}
                  onSubmit={verifyOtp}
                  navigation={navigation}
                  apiErrorOtp={apiErrorOtp}
                  isVerify={isVerify}
                  onResend={LoginWithOtpResend}
                  shouldShow={shouldShow}
                  completeOtpTimer={completeOtpTimer}
                  onEditPress={() => setOtpScene(false)}
                  setApiErrorOtp={setApiErrorOtp}
                  source={
                    type === 'mobile'
                      ? 'registerWithMobile'
                      : 'registerWithEmail'
                  }
                  setOtpLogin={() => onSetLoginBack()}
                  editableAccess={editableAccess}
                />
              </ErrorHandler>
            ) : (
              <View style={{flex:1,height:'100%'}}>
              <View style={{
                // backgroundColor:'red',
                height: '30%'
              }}>
              <FastImage
                        source={Icons.auth}
                        style={styles.thankYouImage}
                      />
                       <TouchableOpacity
                        style={styles.crossIconStyle}
                        onPress={() => navigation.goBack()}>
                        <ImageIcon icon="cancelClose" size="xxl" tintColor="" />
                      </TouchableOpacity>
              </View>
              <View style={{
                // backgroundColor:'yellow',
                height: '70%'
              }}>
                <Formik
                  initialValues={{
                    emailPhoneNumber: emailPhoneNumber,
                    mobileNumber: '',
                    refer: referCode || '',
                    email: '',
                  }}
                  validationSchema={getSignUpValidationSchema(activeField)}
                  // validationSchema={signUpValidationSchema}
                  onSubmit={userSignUp}>
                  {({
                    handleSubmit,
                    handleChange,
                    setFieldValue,
                    values,
                    touched,
                    errors,
                    isValid,
                  }) => (
                      <View style={styles.body}>
                        <View style={styles.headerContent}>
                          <View style={styles.imageFullWidth}>
                            <ImageIcon
                              style={styles.logoView}
                              icon="dentalkartUpdatedIcon"
                            // size="ex176"
                            />

                            <View style={styles.signUpLogin}>
                              <Label
                                text={t('login.logIn')}
                                color="skyBlue23"
                                fontFamily="SemiBold"
                                size="mx"
                              />
                              <Label
                                text={t('signUp.Or')}
                                size="mx"
                                color="skyBlue23"
                                fontFamily="Medium"
                              />
                              <Link
                                onPress={() =>
                                  navigation.navigate('RegisterOtp', {
                                    email: values.phoneNumber,
                                  })
                                }
                                text={'Sign Up'}
                                size="mx"
                                color="skyBlue23"
                                fontFamily="SemiBold"
                              />
                            </View>
                            <View style={{ flexDirection: "row", justifyContent: 'center', marginVertical: 5, alignItems:'center' }}>
                              <Label
                                text={t('login.earnMsg1')}
                                color="skyBlue23"
                                // fontFamily="SemiBold"
                                size="mx"
                              />
                              <Label
                                text={t('login.earnMsg2')}
                                color="skyBlue23"
                                fontFamily="SemiBold"
                                size="mx"
                              />
                              <FastImage
                                source={Icons.loginCoin}
                                style={styles.coinImage}
                              />
                              <Label
                                text={t('login.earnMsg3')}
                                size="mx"
                                color="skyBlue23"
                                fontFamily="SemiBold"
                              />
                            </View>
                          </View>
                        </View>
                        <Spacer type="Vertical" size="xxxl" />
                        <Spacer size="s" />
                        <ErrorHandler
                          componentName={`${TAG} PhoneInputText`}
                          onErrorComponent={<View />}>
                          {/* {showEmail ?
                        (
                          <TextInputBox
                            label={t('login.emailPlace')}
                            value={values.email}
                            // onChangeText={handleChange('email')}
                            onChangeText={(value) => {
    handleChange('email')(value);
    setActiveField('email');
  }}

                            errorText={errors.email}
                            fieldMandatory
                            description=""
                            error={
                              touched.email && errors.email
                                ? String(errors.email)
                                : undefined
                            }
                          />
                        ) : */}
                          <TextInputBox
                            label={t('login.mobileNumberPlace')}
                            value={values.emailPhoneNumber}
                            countryCode="+91"
                            iconI="phoneIcon"
                            fieldMandatory
                            description=""
                            errorText={errors.emailPhoneNumber}
                            // onChangeText={handleChange('emailPhoneNumber')}
                            onChangeText={(value) => {
                              handleChange('emailPhoneNumber')(value);
                              setActiveField('phoneNumber');
                            }}
                            onEndEditing={() => { }}
                            // onFocus={() => setActiveField('phoneNumber')}
                            error={
                              touched.emailPhoneNumber && errors.emailPhoneNumber
                                ? String(errors.emailPhoneNumber)
                                : undefined
                            }
                            keyboardType="number-pad"
                          />
                          {/* } */}
                        </ErrorHandler>

                        <Spacer type="Vertical" size="l" />

                        {/* <View style={styles.loginSubTitle}>
                        {
                          showEmail ? (
                            <TouchableOpacity
                              onPress={() => {
                                setShowEmail(false);
                                setActiveField('phoneNumber');
                                setFieldValue('email', '');
                              }}>
                              <Label
                                text={t('login.useMobileNumber')}
                                fontFamily="Medium"
                                size="mx"
                                color="categoryTitle"
                              />
                            </TouchableOpacity>
                          ) :
                            <TouchableOpacity
                              onPress={() => {
                                setShowEmail(true);
                                setActiveField('email');
                                setFieldValue('phoneNumber', '');
                              }}>
                              <Label
                                text={t('login.useEmail')}
                                fontFamily="Medium"
                                size="mx"
                                color="categoryTitle"
                              />
                            </TouchableOpacity>
                        }
                      </View> */}
                        <Spacer size="s" />
                        <Spacer type="Vertical" size="xms" />
                        <ErrorHandler
                          componentName={`${TAG} Button`}
                          onErrorComponent={<View />}>
                          <Button
                            radius="m"
                            // disabled={!isValid}
                            // disabled={!(values.phoneNumber || values.email)}
                            type={'secondary'}
                            text={t('signUp.sendOtp')}
                            onPress={handleSubmit}
                            selfAlign="stretch"
                            labelColor="whiteColor"
                            labelSize="mx"
                            size="large"
                          />
                        </ErrorHandler>
                        <Spacer type="Vertical" size="l" />
                        <View style={styles.itemsCenter}>
                          <Separator style={styles.separatorLine} />
                          <Label
                            text={t('login.Or')}
                            size="mx"
                            color="text2"
                            style={styles.labelText}
                          />
                          <Separator style={styles.separatorLine} />
                        </View>
                        <Spacer type="Vertical" size="l" />
                        <>
                          <ErrorHandler
                            componentName={`${TAG} LoginGoogle`}
                            onErrorComponent={<View />}>
                            <LoginGoogle
                              textStyle={styles.googleBtn}
                              getUserData={getUserData}
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="m" />
                          <TouchableOpacity
                            style={styles.helpView}
                            onPress={() =>
                              navigation.navigate('HelpCenter', { login: true })
                            }>
                            <Label
                              color="categoryTitle"
                              size="mx"
                              text={t('login.needHelp')}
                              fontFamily="Medium"
                            />
                            <TouchableOpacity
                            style={{padding:Sizes.x}}
                             onPress={() => openWhatsApp(whatsAppLink?.app_link)}>
                              <ImageIcon size="l" icon="whatsapp" />
                            </TouchableOpacity>
                          </TouchableOpacity>
                        </>
                      

                      {/* {mobileNumberModal && Platform.OS === 'android' && (
                        <ErrorHandler
                          componentName={`${TAG} CartRemoveModal`}
                          onErrorComponent={<View />}>
                          <MobileNumberModal
                            visible={mobileNumberModal}
                            onClose={() => setMobileNumberModal(false)}
                          />
                        </ErrorHandler>
                      )} */}
                    </View>
                  )}
                </Formik>
                </View>
              </View>
            )}
          </>
    </SafeAreaView>
  );
};

export default RegisterOtpScene;

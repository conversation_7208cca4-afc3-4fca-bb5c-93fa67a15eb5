import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {t} from 'i18next';
import useStyle from './style';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Label, Separator, Spacer, Tag} from 'components/atoms';
import FastImage from 'react-native-fast-image';
import getImageUrl, { productDummyImage } from 'utils/imageUrlHelper';
import LinearGradient from 'react-native-linear-gradient';

type Props = {
  item: Partial<ProductData>;
  onPress?: () => void;
  onPressCart?: () => void;
  onPressWishlist?: () => void;
  onPressShare?: () => void;
};

const ProductCardHorizontal = ({
  item,
  onPress,
  onPressCart,
  onPressWishlist,
  onPressShare,
}: Props) => {
  const {colors} = useTheme();
  const styles = useStyle(colors);

  return (
    <TouchableOpacity onPress={onPress}>
      <View style={styles.container}>
        <View style={styles.imageSection}>
          <Spacer size="s" />
          <View style={styles.productImageContainer}>
            <ImageIcon icon="bestSeller" style={styles.bestSellerTag} />
            <TouchableOpacity onPress={onPressShare}>
              <ImageIcon icon="share" size="xxxl" />
            </TouchableOpacity>
          </View>
          <FastImage
            resizeMode="contain"
            style={styles.image}
            onError={e => {
              e.target.uri = productDummyImage
            }}
            source={{
              uri: getImageUrl(item?.image_url),
            }}
          />
        </View>
        <View style={styles.detailSection}>
          <View style={styles.productDetailRatingRow}>
            <View style={styles.rowCentered}>
              <ImageIcon icon="coin" size="xx" />
              <Spacer size="xm" type="Horizontal" />
              <Label
                text={item?.reward_point_product || '0'}
                size="l"
                weight="500"
                color="orange"
              />
              <Spacer size="xm" type="Horizontal" />
              <Separator Vertical color="grey2" />
              <Spacer size="xm" type="Horizontal" />
              <Tag
                style={styles.tagView}
                label={(item.average_rating === 'null'
                  ? 0
                  : Number(item.average_rating)
                ).toFixed(1)}
                icon="starIcon"
                color="green2"
              />
              <Spacer size="xm" type="Horizontal" />
              <Label
                weight="400"
                size="mx"
                color="grey"
                text={!!item?.rating_count ? `(${item?.rating_count})` : 0}
              />
            </View>
            <TouchableOpacity onPress={onPressWishlist}>
              <ImageIcon icon="heart" size="xxl" />
            </TouchableOpacity>
          </View>
          <Spacer size="sx" />
          <View style={{flex: 1}}>
            <Label text={item?.name} color="text" size="xx" weight="500" />
            <Spacer size="s" />
            <Label
              text={item.short_description}
              weight="400"
              size="l"
              color="grey"
              ellipsizeMode={'tail'}
              numberOfLines={2}
            />
          </View>

          {!item?.msrp ? (
            <View style={styles.pricingRow}>
              {item.price?.regularPrice?.amount?.value !==
                item?.price?.minimalPrice?.amount?.value &&
              item.price?.regularPrice?.amount?.value > 0 ? (
                <>
                  <Label
                    weight="500"
                    size="m"
                    color="grey3"
                    textDecorationLine="line-through"
                    text={
                      item.price?.regularPrice.amount.currency_symbol +
                      '' +
                      item.price?.regularPrice?.amount?.value
                    }
                  />
                  <Spacer size="s" type="Horizontal" />
                </>
              ) : null}
              <Label
                color="text"
                weight="600"
                size="m"
                text={
                  item?.price?.minimalPrice?.amount?.currency_symbol +
                  '' +
                  item?.price?.minimalPrice?.amount?.value
                }
              />
              {item.price?.regularPrice?.amount?.value > 0 &&
              item.price?.regularPrice?.amount?.value !==
                item?.price?.minimalPrice?.amount?.value ? (
                <LinearGradient
                  style={styles.priceOfferContainer}
                  useAngle
                  angle={270}
                  colors={[
                    'rgba(16, 134, 77, 0.32)',
                    'rgba(16, 134, 77, 0.00)',
                  ]}>
                  <Label
                    color="green2"
                    weight="500"
                    size="m"
                    align="right"
                    text={
                      (
                        100 -
                        (Number(item?.price?.minimalPrice?.amount?.value) *
                          100) /
                          Number(
                            item?.price?.regularPrice?.amount?.value ||
                              item?.price?.minimalPrice?.amount?.value,
                          )
                      ).toFixed(2) + t('productItemsHorizontal.off')
                    }
                  />
                </LinearGradient>
              ) : null}
            </View>
          ) : null}
          <View style={styles.deliveryOfferRow}>
            <Tag
              style={styles.tagView}
              labelStyle={styles.tagLabel}
              label={'Express Delivery'}
              color="skyBlue2"
            />
            {item.type_id === 'grouped' ? null : (
              <TouchableOpacity onPress={onPressCart}>
                <ImageIcon icon="cart" size="xxl" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ProductCardHorizontal;

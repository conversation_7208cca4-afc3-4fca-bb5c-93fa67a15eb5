import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const Styles = (colors: Theme['colors']) => {
  return StyleSheet.create({
    modalStyle: {margin: 0},
    flexContainer: {flex: Sizes.x},
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    modelBg: {
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
  });
};

export default Styles;

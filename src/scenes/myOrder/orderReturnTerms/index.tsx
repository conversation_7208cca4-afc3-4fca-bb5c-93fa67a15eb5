import React, {useEffect, useMemo} from 'react';
import {ScrollView, TouchableOpacity, View, FlatList} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {SafeAreaView} from 'react-native-safe-area-context';
import FastImage from 'react-native-fast-image';
import {RootStackParamsList} from 'routes';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {RouteProp, useTheme} from '@react-navigation/native';
import Icons from 'common/icons';
import {Label, Spacer} from 'components/atoms';
import {returnPolicy, productPolicy, productCondition} from 'staticData';
import {t} from 'i18next';
import {RootState} from '@types/local';
import {useSelector} from 'react-redux';
import {AnalyticsEvents} from 'components/organisms';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList>;
};

const OrderReturnTermsScene = ({navigation, route}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const orderId = route.params?.orderId;
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);

  useEffect(() => {
    AnalyticsEvents('RETURN_POLICY', 'Return Policy', {}, userInfo, isLoggedIn);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <Header
        backButton={true}
        navigation={navigation}
        text={t('returnPolicy.return')}
        bagIcon={true}
        searchIcon={true}
      />
      <ScrollView style={styles.subContainer}>
        <View style={styles.thumbsUpVIew}>
          <FastImage
            resizeMode="contain"
            style={styles.imageView}
            source={Icons.termsGif}
          />
          <Spacer size="xm" />
          <Label
            fontFamily="Medium"
            size="m"
            text={t('returnPolicy.requestSubmitted')}
            color="text"
          />
          <Spacer size="xm" />
          <Label
            fontFamily="Regular"
            size="m"
            text={t('returnPolicy.reqNote')}
            color="grey"
            align="center"
          />
          <Spacer size="xm" />
          <Button
            type="primary"
            onPress={() =>
              navigation.navigate('OrderList', {pageType: 'Returns'})
            }
            text={t('returnPolicy.submitRequest')}
            style={styles.subReqBtn}
            labelSize="mx"
            labelColor="whiteColor"
            selfAlign="stretch"
          />
          <Spacer size="xms" />
          <Label
            fontFamily="Regular"
            size="mx"
            text={t('returnPolicy.or')}
            color="grey"
            align="center"
          />
          <Spacer size="xms" />
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('OrderReturnListScene', {
                orderId: orderId,
              })
            }>
            <Label
              fontFamily="Medium"
              size="m"
              text={t('returnPolicy.returnMoreProducts')}
              color="categoryTitle"
            />
          </TouchableOpacity>
          <Spacer size="xxl" />
        </View>
        <View style={styles.policy}>
          <Label
            fontFamily="SemiBold"
            size="m"
            text={t('returnPolicy.returnsPolicy')}
            color="categoryTitle"
          />
          <Spacer size="xm" />
          <FlatList
            data={returnPolicy}
            keyExtractor={(_, i) => i.toString()}
            ItemSeparatorComponent={() => <Spacer size="xm" />}
            renderItem={({item, index}) => {
              return (
                <View key={index} style={styles.texts}>
                  <Label
                    fontFamily="SemiBold"
                    size="m"
                    text={'•'}
                    color="grey"
                  />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    fontFamily="Regular"
                    size="m"
                    text={item}
                    color="grey"
                    style={styles.flex}
                  />
                </View>
              );
            }}
          />
        </View>
        <Spacer size="xm" />

        <FlatList
          data={productPolicy}
          keyExtractor={(_, i) => i.toString()}
          ItemSeparatorComponent={() => <Spacer size="xm" />}
          renderItem={({item, index}) => {
            return (
              <View key={index}>
                <View style={styles.sectionView}>
                  <Label
                    fontFamily="Medium"
                    size="m"
                    text={item.title}
                    color="categoryTitle"
                  />
                  {item?.description && (
                    <>
                      <Spacer size="xms" />
                      <Label
                        fontFamily="Medium"
                        size="m"
                        text={item?.description}
                        color="grey"
                      />
                    </>
                  )}
                </View>
                {item?.data?.length > 0 && (
                  <FlatList
                    data={item?.data}
                    style={styles.childListView}
                    keyExtractor={(_, i) => i.toString()}
                    ItemSeparatorComponent={() => <Spacer size="xm" />}
                    renderItem={({item: items, index: i}) => {
                      return (
                        <View key={i} style={styles.productsView}>
                          <View style={styles.productsSubView}>
                            <Label
                              fontFamily="Medium"
                              size="mx"
                              text={items.title}
                              color="categoryTitle"
                            />
                          </View>
                          <Spacer size="xms" />
                          {items?.data?.map((rowData, index1) => {
                            return (
                              <View key={index1} style={styles.childTexts}>
                                <Label
                                  fontFamily="SemiBold"
                                  size="m"
                                  text={'•'}
                                  color="grey"
                                />
                                <Spacer size="sx" type="Horizontal" />
                                <Label
                                  fontFamily="Regular"
                                  size="m"
                                  text={rowData}
                                  color="grey"
                                  style={styles.flex}
                                />
                              </View>
                            );
                          })}
                        </View>
                      );
                    }}
                  />
                )}
              </View>
            );
          }}
        />
        <Spacer size="xm" />
        <View style={styles.sectionView}>
          <Label
            fontFamily="Medium"
            size="m"
            text={t('returnPolicy.productUs')}
            color="categoryTitle"
          />
        </View>
        <FlatList
          data={productCondition}
          keyExtractor={(_, i) => i.toString()}
          style={styles.boxView}
          ItemSeparatorComponent={() => <View style={styles.sectionLine} />}
          // eslint-disable-next-line react/no-unstable-nested-components
          ListHeaderComponent={() => {
            return (
              <View style={styles.sectionHeaderView}>
                <View style={styles.sectionLeft}>
                  <Label
                    fontFamily="Medium"
                    size="m"
                    text={t('returnPolicy.category')}
                    color="categoryTitle"
                  />
                </View>
                <Spacer type="Horizontal" size="xm" />
                <Label
                  fontFamily="Medium"
                  size="m"
                  text={t('returnPolicy.conditions')}
                  color="categoryTitle"
                  style={styles.flex}
                />
              </View>
            );
          }}
          renderItem={({item, index}) => {
            return (
              <View key={index} style={styles.sectionMainView}>
                <View style={styles.sectionLeft}>
                  <Label
                    fontFamily="Medium"
                    size="m"
                    text={item.title}
                    color="grey"
                    align="center"
                  />
                </View>
                <Spacer type="Horizontal" size="xm" />
                <View style={styles.fRow}>
                  <Label
                    fontFamily="SemiBold"
                    size="m"
                    text={'•'}
                    color="grey"
                  />
                  <Spacer size="sx" type="Horizontal" />
                  <Label
                    fontFamily="Regular"
                    size="m"
                    text={item.des}
                    color="grey"
                    style={styles.flex}
                  />
                </View>
              </View>
            );
          }}
        />

        <View style={styles.sectionView}>
          <Label
            fontFamily="Regular"
            size="m"
            text={t('returnPolicy.notes')}
            color="grey"
          />
        </View>
        <Spacer size="xxxl" />
      </ScrollView>
    </SafeAreaView>
  );
};

export default OrderReturnTermsScene;

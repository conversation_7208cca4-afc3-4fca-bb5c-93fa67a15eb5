import React, {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import {
  Dimensions,
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  FlatList,
  SafeAreaView,
  Platform,
  Share,
  TouchableWithoutFeedback,
} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import Carousel from 'react-native-reanimated-carousel';
import RBSheet from 'react-native-raw-bottom-sheet';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import moment from 'moment/moment';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {showInfoMessage} from 'utils/show_messages';
import {Sizes} from 'common';
import Icons from 'common/icons';
import {useSelector, useDispatch} from 'react-redux';
import {VideoPlayer} from 'components/organisms';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import {Label, ImageIcon, Spacer, CommonModal} from 'components/atoms';
import {
  getShorts,
  getFeedDetail,
  addFeedLike,
  addFeedsComment,
  getFeedsComment,
  addFeedHistory,
  addFeedEngagement,
} from 'services/shorts';
import {getMultipleProductList} from 'services/productDetail';
import {ShortsDetailLoader} from 'skeletonLoader';
import getImageUrl from 'utils/imageUrlHelper';
import ErrorHandler from 'utils/ErrorHandler';
import LinearGradient from 'react-native-linear-gradient';
import {debounce} from 'utils/utils';
import {debugLog} from 'utils/debugLog';
import {getShortsList} from 'app-redux-store/slice/appSlice';

const ShortVideos = ({route, navigation}: any) => {
  const TAG = 'ShortVideos';
  const referredVideoId = route?.params?.videoId;
  const source = route?.params?.source;
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {isLoggedIn, shortsAllVideo, shortsLoader} = useSelector(
    (state: RootState) => state.app,
  );
  const refRBSheet = useRef([]);
  const refRBProductSheet = useRef([]);
  const width = Dimensions.get('window').width;
  const height = Dimensions.get('window').height;
  const [muteVideo, setMuteVideo] = useState<boolean>(false);
  const [comment, setComment] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [reelsData, setReelsData] = useState<Shorts[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [commentsData, setCommentsData] = useState<any[]>([]);
  const [currentVideoData, setCurrentVideoData] = useState<any>(null);
  const [showDisclaimerModal, setShowDisclaimerModal] = useState(false);
  const [featuredProductLoading, setFeaturedProductLoading] =
    useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [lastProgress, setLastProgress] = useState<number>(0);
  const [getProductData, setGetProductData] = useState<any[]>([]);
  const isFocused = navigation.isFocused();
  const flatListRef = useRef(null);
  const [videoEnd, setVideoEnd] = useState(false);
  const [previousVideoId, setPreviousVideoId] = useState();
  const [state, setState] = useState({});
  const dispatch = useDispatch();

  const VideosApi = useCallback(
    async (withLoader = true) => {
      setLoading(withLoader);
      const response = await getShorts();
      setLoading(false);
      const {status, data} = response;
      if (status && data.status === 'success') {
        setReelsData(data.data.rows);
        const videoIndex = data.data.rows.findIndex(
          (item: Shorts) => item.id === referredVideoId,
        );
        if (videoIndex > -1) {
          setCurrentIndex(videoIndex);
          setTimeout(() => {
            flatListRef?.current?.scrollToIndex({
              index: videoIndex,
              animated: true,
            });
          }, 10);
        }
      }
      if (route?.params?.videoItem?.featured_product) {
        getFeaturedProduct(route?.params?.videoItem?.featured_product);
      }
    },
    [referredVideoId],
  );

  const GetCommentsApi = useCallback(async (videoId: any) => {
    const response = await getFeedsComment(videoId);
    const {status, data} = response;
    if (status && data.status === 'success') {
      setCommentsData(data.data.rows);
    }
  }, []);

  useEffect(() => {
    if (shortsAllVideo && shortsAllVideo?.length > 0) {
      setReelsData(shortsAllVideo);
      const videoIndex = shortsAllVideo.findIndex(
        (item: Shorts) => item.id === referredVideoId,
      );
      if (videoIndex > -1) {
        setCurrentIndex(videoIndex);
        setTimeout(() => {
          flatListRef?.current?.scrollToIndex({
            index: videoIndex,
            animated: true,
          });
        }, 10);
      }
    }
  }, [shortsAllVideo, referredVideoId]);

  useEffect(() => {
    // VideosApi();
    dispatch(getShortsList());
    if (route?.params?.videoItem?.featured_product) {
      getFeaturedProduct(route?.params?.videoItem?.featured_product);
    }
  }, []);

  const baseOptions = {
    vertical: true,
    width: width,
    height: height,
  };

  const getStreamVideo = useCallback(async () => {
    const videoId = reelsData[currentIndex]?.id;
    if (videoId) {
      const response = await getFeedDetail(videoId);
      const {status, data} = response;
      if (status && data.status === 'success') {
        setCurrentVideoData(data.data);
      }
    }
  }, [setCurrentVideoData, currentIndex, reelsData]);

  const debouncedCall = callBack => {
    debouncedUpdate(callBack);
  };

  const debouncedUpdate = useCallback(
    debounce(callBack => {
      callBack();
    }, 500),
    [],
  );

  const AddLikeApi = async (videoId: number, index: number) => {
    let reelsList = reelsData;
    const likeCount =
      currentVideoData.isLiked === 1
        ? reelsList[index].like_count - 1
        : reelsList[index].like_count + 1;
    setCurrentVideoData({
      ...currentVideoData,
      isLiked: currentVideoData.isLiked === 1 ? 0 : 1,
      like_count: likeCount,
    });
    reelsList[index].like_count = likeCount;
    setReelsData(reelsList);
    setState({});
    const response = await addFeedLike(videoId);
    const {status, data} = response;
    if (status && data.status === 'success') {
      reloadVideoList(videoId, index);
      if (route?.params?.callBack) {
        route?.params?.callBack();
      }
    }
  };

  const reloadVideoList = async (videoId, index) => {
    const response = await getFeedDetail(videoId);
    const {status, data} = response;
    if (status && data.status === 'success') {
      setReelsData(prevReelsData => {
        if (!prevReelsData?.[index]) return prevReelsData;
        const updatedVideoItem = {
          ...prevReelsData[index],
          ...data.data,
        };
        const updatedReelsData = [...prevReelsData];
        updatedReelsData[index] = updatedVideoItem;

        setCurrentVideoData(updatedVideoItem);
        return updatedReelsData;
      });
      setState({});
    }
  };

  const AddCommentApi = useCallback(
    async (videoId: any, comment: string) => {
      setComment('');
      const response = await addFeedsComment({
        video_id: videoId,
        comment: comment,
      });
      const {status, data} = response;
      if (status && data.status === 'success') {
        GetCommentsApi(videoId);
        getStreamVideo();
      }
    },
    [GetCommentsApi, getStreamVideo],
  );

  const renderCommentItem = useCallback(
    ({item}: any) => {
      return (
        <View style={styles.continuer}>
          <View style={styles.left}>
            <Label
              style={styles.userNameText}
              text={item.customer_name}
              fontFamily="SemiBold"
            />
            <Label
              text={moment(item.created_at).format('MMM DD, YYYY hh:mm a')}
              style={styles.commentDate}
              fontFamily="Medium"
              size="xms"
            />
            <Label
              text={item.comment}
              style={styles.comments}
              size="l"
              fontFamily="Regular"
            />
          </View>
        </View>
      );
    },
    [styles],
  );

  useEffect(() => {
    getStreamVideo();
  }, [getStreamVideo]);

  const splitData = useMemo(() => {
    const beforePosition = currentIndex - 1;
    const afterPosition = currentIndex + 1;
    let loadingIndexs = [];
    if (beforePosition >= 0 && afterPosition >= 0) {
      loadingIndexs = [beforePosition, currentIndex, afterPosition];
    } else if (beforePosition <= 0) {
      loadingIndexs = [currentIndex, afterPosition];
    } else {
      loadingIndexs = [beforePosition, currentIndex];
    }
    return loadingIndexs;
  }, [currentIndex]);

  const updateHistory = useCallback(
    async (videoId: string, progress: PlaybackProgress, index: number) => {
      let videoPlayInitialSource = '';
      if (videoId === referredVideoId) {
        videoPlayInitialSource = !!source ? source : 'RECOMMENDED';
      } else {
        videoPlayInitialSource = 'SCROLL';
      }
      await addFeedHistory({
        video_id: videoId,
        watch_time: progress,
        source: videoPlayInitialSource,
      });
      reloadVideoList(videoId, index);
      if (route?.params?.callBack) {
        route?.params?.callBack();
      }
    },
    [referredVideoId, source],
  );

  const updateEngagement = useCallback(async () => {
    await addFeedEngagement({section_id: 1});
  }, []);

  useEffect(() => {
    updateEngagement();

    return () => {
      updateEngagement();
    };
  }, [updateEngagement]);

  const getChildProgress = useCallback(
    (progress: PlaybackProgress, videoId: string, index: number) => {
      if (!!progress?.playableDuration) {
        updateHistory(videoId, progress.playableDuration, index);
      }
    },
    [updateHistory],
  );

  const getFeaturedProduct = useCallback(async (productId: string) => {
    setGetProductData([]);
    try {
      setFeaturedProductLoading(true);
      const productsId = JSON.parse(productId);
      const response = await getMultipleProductList({id: productsId});
      const {status, data} = response;
      setFeaturedProductLoading(false);
      if (status) {
        setGetProductData(data?.products);
      }
    } catch (error) {
      setFeaturedProductLoading(false);
    }
  }, []);

  const onShare = (item: Shorts) => {
    Share.share(
      {
        message: `${item.title} \n\n${item.video_url} \n\n${item?.caption}`,
      },
      {dialogTitle: 'Share on ..', tintColor: 'green'},
    )
      .then(() => setIsPlaying(true))
      .catch(err => debugLog(err));
  };

  const RenderFeaturedProduct = useCallback(
    ({item, index}: {item: ProductData; index: number}) => {
      return (
        <TouchableOpacity
          key={index}
          style={styles.productContainer}
          onPress={() => {
            setIsPlaying(true);
            navigation.navigate('ProductDetail', {
              productId: item?.product_id,
              ProductItems: item,
            });
          }}>
          <View style={styles.productBody}>
            <ImageIcon
              source={getImageUrl(item?.media?.mobile_image, 'featureProduct')}
              resizeMode="cover"
              sourceType="url"
              size="x76"
            />
          </View>
          <View style={styles.flexView}>
            <Label
              text={item.name}
              fontFamily="Medium"
              size="mx"
              color="text"
              numberOfLines={2}
            />
            <Spacer size="s" />
            <View style={styles.productPriceContainer}>
              {item?.reward_points && Number(item?.reward_points) > 0 && (
                <View style={styles.rowAlignCenter}>
                  <ImageIcon icon="coin" size="l" />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    text={item?.reward_points || '0'}
                    size="mx"
                    style={styles.coinNumber}
                    fontFamily="Medium"
                    color="orange"
                  />
                  <Spacer size="xm" type="Horizontal" />
                </View>
              )}
              <Label
                fontFamily="Medium"
                size="m"
                color="baliHaiLightGray"
                textDecorationLine="line-through"
                text={item?.currency_symbol + ' ' + item.price}
              />
              <Spacer size="xm" type="Horizontal" />
              <Label
                color="text"
                fontFamily="SemiBold"
                size="mx"
                text={item?.currency_symbol + ' ' + item.selling_price}
              />
            </View>
          </View>
        </TouchableOpacity>
      );
    },
    [navigation, styles],
  );

  const renderProductSeparator = () => {
    return <View style={styles.saprater} />;
  };

  const renderItem = useCallback(
    ({item, index}: {item: Shorts; index: number}) => {
      return (
        <View
          style={[styles.videoPlayerContainer, {paddingBottom: insets.bottom}]}>
          {index === currentIndex ? (
            <ErrorHandler
              componentName={`${TAG} VideoPlayer`}
              onErrorComponent={<View />}>
              <VideoPlayer
                id={item?.id}
                videoURL={
                  Platform.OS === 'android'
                    ? item?.streaming_url
                    : item?.video_url
                }
                paused={currentIndex !== index || isPlaying}
                getChildProgress={(progress, id) =>
                  getChildProgress(progress, id, index)
                }
                muteVideo={muteVideo}
                item={item}
                videoEnd={videoEnd}
                previousVideoId={previousVideoId}
                onVideoEnd={(value: boolean, id: number) => {
                  setVideoEnd(value);
                  setTimeout(() => {
                    setPreviousVideoId(id);
                  }, 500);
                }}
                onPause={() => setIsPlaying(!isPlaying)}
              />
            </ErrorHandler>
          ) : (
            <ErrorHandler
              componentName={`${TAG} FastImage`}
              onErrorComponent={<View />}>
              <FastImage
                style={styles.videoPlayerContainer}
                source={{uri: item?.thumbnail_url}}
              />
            </ErrorHandler>
          )}
          <TouchableWithoutFeedback onPress={() => setIsPlaying(!isPlaying)}>
            <LinearGradient
              style={styles.gradient}
              colors={[colors.black0, colors.black]}>
              <View
                style={[
                  styles.itemView,
                  {
                    bottom:
                      getProductData?.length > 0 ? 130 : 70 + insets.bottom,
                  },
                ]}>
                <View style={styles.likeContainer}>
                  <ImageIcon size="xxl" tintColor="white1" icon="eyeWhite" />
                  <Label
                    text={item?.totalWatchCount}
                    style={styles.countTxt}
                    align="center"
                  />
                  <Spacer size="xm" />
                  <TouchableOpacity
                    onPress={() =>
                      debouncedCall(() => {
                        if (isLoggedIn) {
                          AddLikeApi(item.id, index);
                        } else {
                          showInfoMessage(t('shorts.loginLikeVideo'));
                          navigation.navigate('Login');
                        }
                      })
                    }>
                    <ErrorHandler
                      componentName={`${TAG} FastImage`}
                      onErrorComponent={<View />}>
                      <FastImage
                        style={styles.iconSize}
                        tintColor={
                          currentVideoData?.isLiked
                            ? colors.redIcon
                            : colors.white1
                        }
                        source={
                          currentVideoData?.isLiked
                            ? Icons.heartLikedIcon
                            : Icons.heartNotLikedIcon
                        }
                      />
                    </ErrorHandler>
                    <Label
                      text={item?.like_count}
                      align="center"
                      size="mx"
                      fontFamily="Medium"
                      color="white1"
                    />
                  </TouchableOpacity>
                  <Spacer size="xm" />
                  {/* <TouchableOpacity onPress={() => onShare(item)}>
                    <ImageIcon size="xxl" tintColor="white1" icon="newShare" />
                  </TouchableOpacity> */}
                  {/* <Spacer size="mx" />
              <TouchableOpacity>
                <ImageIcon
                  size="xxl"
                  tintColor="white1"
                  icon={false ? 'saved' : 'newBookmarkIcon'}
                />
              </TouchableOpacity> */}
                  {/* <View style={[styles.actionBox, {marginTop: Sizes.xms}]}>
                  <TouchableOpacity
                    onPress={() => {
                      if (isLoggedIn) {
                        setCommentsData([]);
                        GetCommentsApi(item.id);
                        refRBSheet.current[item.id].open();
                      } else {
                        showInfoMessage(t('shorts.loginCommentVideo'));
                      }
                    }}>
                    <FastImage
                      style={styles.iconSize}
                      source={Icons.commentIcon}
                    />
                    <Text style={[styles.fontStyle]}>
                      {currentVideoData?.comment_count}
                    </Text>
                  </TouchableOpacity>
                </View> */}
                </View>

                <View style={styles.descriptionContainer}>
                  <TouchableOpacity
                    style={styles.musicIcon}
                    onPress={() => setMuteVideo(!muteVideo)}>
                    <ImageIcon
                      size="xxl"
                      tintColor="white1"
                      resizeMode="contain"
                      icon={muteVideo ? 'musicStop' : 'music'}
                    />
                  </TouchableOpacity>
                  <Spacer size="l" />
                  {/* <View style={styles.profileContainer}>
                  <FastImage
                    style={styles.profileImage}
                    source={{uri: reelsData[index]?.author_image}}
                  />
                  <Text style={styles.profileName}>
                    {reelsData[index]?.author_name}
                  </Text>
                </View> */}
                  <Label
                    text={reelsData[index]?.title.trim()}
                    size="mx"
                    fontFamily="Regular"
                    color="white1"
                    numberOfLines={2}
                    style={styles.description}
                  />
                  {/* {reelsData[index]?.featured_product !== null ? (
                  <TouchableOpacity
                    onPress={() => {
                      refRBProductSheet.current[item.id].open();
                      setFeaturedProductLoading(true);
                      setTimeout(() => {
                        getFeaturedProduct(
                          reelsData[index]?.featured_product,
                          item.id,
                        );
                      }, 500);
                    }}
                    style={styles.featuredProductContainer}>
                    <Text style={styles.featuredProductText}>
                      {t('shorts.featuredProducts')}
                    </Text>
                    <FastImage
                      style={styles.featuredProductArrowIcon}
                      source={Icons.downArrowIcon}
                    />
                  </TouchableOpacity>
                ) : null} */}
                </View>
              </View>
              {getProductData?.length > 0 && (
                <View style={styles.productView}>
                  <FlatList
                    style={styles.featuredProductContentContainer}
                    horizontal={true}
                    data={getProductData}
                    renderItem={({item: childItems, index: i}) => {
                      return (
                        <ErrorHandler
                          componentName={`${TAG} RenderFeaturedProduct`}
                          onErrorComponent={<View />}>
                          <RenderFeaturedProduct item={childItems} index={i} />
                        </ErrorHandler>
                      );
                    }}
                    ItemSeparatorComponent={renderProductSeparator}
                    ListEmptyComponent={
                      <ActivityIndicator
                        style={styles.featuredLoading}
                        size={'large'}
                      />
                    }
                  />
                </View>
              )}
            </LinearGradient>
          </TouchableWithoutFeedback>
          <TouchableWithoutFeedback onPress={() => setIsPlaying(!isPlaying)}>
            <LinearGradient
              style={styles.headerTopView}
              colors={[colors.black, colors.black0]}>
              <View
                style={styles.headerView}>
                <TouchableOpacity              
                  onPress={() => {
                  setIsPlaying(true);
                    navigation.goBack();
                  }}
                  activeOpacity={0.7}
                >
                  <ErrorHandler
                    componentName={`${TAG} FastImage`}
                    onErrorComponent={<View />}>
                    <FastImage
                      resizeMode="contain"
                      style={styles.goBackIcon}
                      tintColor={colors.whiteColor}
                      source={Icons.arrowLeft}
                    />
                  </ErrorHandler>
                </TouchableOpacity>
                <Label
                  text={item?.author_name}
                  numberOfLines={1}
                  size="l"
                  fontFamily="SemiBold"
                  ellipsizeMode="tail"
                  color="whiteColor"
                  align="center"
                  style={styles.headerTitle}
                />
                 <TouchableOpacity              
                  onPress={() => {
                    setShowDisclaimerModal(true);
                  }}
                  activeOpacity={0.7}
                >
                <ImageIcon
                  icon="infoCircle"
                  size="x26"
                  tintColor="background"
                  />
                </TouchableOpacity>
              </View>
            </LinearGradient>
          </TouchableWithoutFeedback>
          <RBSheet
            gestureEnabled={true}
            height={Sizes.ex4l}
            ref={el => (refRBSheet.current[item.id] = el)}
            closeOnDragDown={false}
            closeOnPressMask={false}>
            <SafeAreaView style={styles.commentHeadingMainCont}>
              <View style={styles.commentHeadingCont}>
                <Text style={styles.commentHeading}>
                  {t('shorts.comments')}
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    refRBSheet.current[item.id].close();
                  }}>
                  <ErrorHandler
                    componentName={`${TAG} FastImage`}
                    onErrorComponent={<View />}>
                    <FastImage
                      style={styles.closeIcon}
                      source={Icons.closeIcons}
                    />
                  </ErrorHandler>
                </TouchableOpacity>
              </View>

              <FlatList
                style={styles.list}
                data={commentsData}
                renderItem={renderCommentItem}
              />

              <View style={styles.commentInput}>
                <TextInput
                  testID="txtShortVideosAddComment"
                  value={comment}
                  onChangeText={setComment}
                  style={styles.input}
                  placeholder={t('shorts.addComment') as string}
                  allowFontScaling={false}
                />
                <TouchableOpacity
                  onPress={() => AddCommentApi(item.id, comment)}>
                  <FastImage style={styles.sendIcon} source={Icons.sendIcon} />
                </TouchableOpacity>
              </View>
            </SafeAreaView>
          </RBSheet>

          <RBSheet
            gestureEnabled={true}
            height={450}
            ref={el => (refRBProductSheet.current[item.id] = el)}
            closeOnDragDown={false}
            closeOnPressMask={false}>
            <View style={styles.commentHeadingMainCont}>
              <>
                <View style={styles.commentHeadingCont}>
                  <Text style={styles.commentHeading}>
                    {t('shorts.allProducts')} (
                    {featuredProductLoading ? '0' : getProductData.length})
                  </Text>
                  <TouchableOpacity
                    onPress={() => {
                      refRBProductSheet.current[item.id].close();
                    }}>
                    <FastImage
                      style={styles.closeIcon}
                      source={Icons.closeIcons}
                    />
                  </TouchableOpacity>
                </View>
                <FlatList
                  style={styles.featuredProductContentContainer}
                  data={getProductData}
                  renderItem={({item: childItems}) => {
                    return (
                      <ErrorHandler
                        componentName={`${TAG} RenderFeaturedProduct`}
                        onErrorComponent={<View />}>
                        <RenderFeaturedProduct
                          item={childItems}
                          index={item.id}
                        />
                      </ErrorHandler>
                    );
                  }}
                  ItemSeparatorComponent={renderProductSeparator}
                  ListEmptyComponent={
                    <ActivityIndicator
                      style={styles.featuredLoading}
                      size={'large'}
                    />
                  }
                />
              </>
            </View>
          </RBSheet>
        </View>
      );
    },
    [
      splitData,
      reelsData,
      currentIndex,
      isPlaying,
      getChildProgress,
      currentVideoData?.isLiked,
      currentVideoData?.like_count,
      currentVideoData?.comment_count,
      commentsData,
      renderCommentItem,
      comment,
      featuredProductLoading,
      getProductData,
      renderProductSeparator,
      getFeaturedProduct,
      AddLikeApi,
      isLoggedIn,
      GetCommentsApi,
      navigation,
      AddCommentApi,
    ],
  );

  const setWatchProgress = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  useEffect(() => {
    getStreamVideo();
  }, [currentIndex]);

  const getVideoProgress = useCallback(
    (progress: any) => {
      const videoProgress = Math.abs(progress);
      if (videoProgress !== lastProgress) {
        setIsPlaying(true);
      } else {
        setIsPlaying(false);
      }
      setLastProgress(videoProgress);
    },
    [lastProgress],
  );

  useEffect(() => {
    if (isFocused) {
      setIsPlaying(false);
    }
  }, [isFocused]);

  // Auto-play the video currently in view
  const onViewableItemsChanged = useRef(({viewableItems}) => {
    if (viewableItems.length > 0) {
      setGetProductData([]);
      const data = viewableItems[0];
      setCurrentVideoData(data.item);
      setCurrentIndex(data?.index);
      setIsPlaying(false);
      setVideoEnd(false);
      setPreviousVideoId(undefined);
      if (data?.item?.featured_product) {
        getFeaturedProduct(data?.item?.featured_product);
      }
    }
  });

  // Viewability configuration for lazy loading
  const viewAbilityConfig = {
    waitForInteraction: true,
    itemVisiblePercentThreshold: 50, // Only consider video "in view" if 50% visible
  };

  const getItemLayout = (data, index) => ({
    length: height,
    offset: height * index,
    index,
  });

  return (
    <SafeAreaView style={[styles.flexView, {paddingTop: insets.top}]}>
      {loading || (shortsAllVideo?.length > 0 ? false : shortsLoader) ? (
        <ShortsDetailLoader />
      ) : reelsData.length > 0 ? (
        <View style={styles.screenContainer}>
          {/* <GestureHandlerRootView>
            <Carousel
              {...baseOptions}
              loop={false}
              autoPlay={false}
              data={reelsData}
              onSnapToItem={setWatchProgress}
              renderItem={renderItem}
              defaultIndex={currentIndex}
              windowSize={1}
              onProgressChange={getVideoProgress}
            />
          </GestureHandlerRootView> */}
          <FlatList
            ref={flatListRef}
            data={reelsData}
            renderItem={renderItem}
            keyExtractor={(item, index) => `${index}`}
            snapToAlignment="start"
            decelerationRate="fast"
            snapToInterval={height} // Each video takes up the full screen
            showsVerticalScrollIndicator={false}
            pagingEnabled={true} // Fullscreen swipe behavior
            onViewableItemsChanged={onViewableItemsChanged.current}
            // onViewableItemsChanged={onViewableItemsChanged}
            viewabilityConfig={viewAbilityConfig}
            onEndReachedThreshold={0.5} // Trigger fetch when 50% from the end
            windowSize={5} // Increase window size for smoother experience
            initialNumToRender={5} // Render only 10 videos initially
            maxToRenderPerBatch={5} // Render only 5 videos per batch
            removeClippedSubviews={true} // Remove videos that are out of view from memory
            getItemLayout={getItemLayout} // Optimization for flatlist rendering
          />
        </View>
      ) : null}
      {showDisclaimerModal && (
          <ErrorHandler
            componentName={`${TAG} DisclaimerModal`}
            onErrorComponent={<View />}>
              <CommonModal
                setIsShowFooterModal={setShowDisclaimerModal}
                visible={showDisclaimerModal}
                modalText={ t('shorts.disclaimer')}
                onClose={() => setShowDisclaimerModal(false)}
              />
          </ErrorHandler>
        )}
    </SafeAreaView>
  );
};

export default ShortVideos;

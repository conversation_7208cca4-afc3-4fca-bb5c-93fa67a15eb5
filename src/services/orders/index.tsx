import {apiRoute, apiEndpoints, subEndpoint} from 'config/apiEndpoint';
import Api from 'services';
const API = new Api();

const objectToQueryString = params => {
  return Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
};

export const ordersList = async data => {
  const query = objectToQueryString(data);
  const url = `${apiRoute.nodeServerless}${apiEndpoints.customerOrders}?${query}&recent_order_view=false`;
  const resp = await API.get(url);
  return resp;
};

export const ordersTrackingList = async data => {
  const query = objectToQueryString(data);
  const url = `${apiRoute.tracking}${apiEndpoints.orders}?${query}`;
  const resp = await API.get(url);
  return resp;
};

export const CanCancelOrder = async orderId => {
  const url = `${apiRoute.orderMg}${apiEndpoints.cancelOrders}/${orderId}/${subEndpoint.order.cancelable}`;
  const resp = await API.get(url);
  return resp;
};
export const cancelRegion = async () => {
  const url = `${apiRoute.orderMg}${apiEndpoints.cancelReasons}`;
  const resp = await API.get(url);
  return resp;
};
export const cancelOrdersApi = async (orderID: number, data: string) => {
  const url = `${apiRoute.orderMg}${apiEndpoints.cancelOrders}/${orderID}/${subEndpoint.order.cancel}`;
  const resp = await API.post(url, data);
  return resp;
};
export const NewGetOrderDetails = async (orderID: number) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.customerOrders}/${orderID}/${subEndpoint.order.summary}`;
  const resp = await API.get(url);
  return resp;
};
export const TrackShipmentApi = async (orderID: number) => {
  const url = `${apiRoute.tracking}${apiEndpoints.orders}/${orderID}/${subEndpoint.order.shipments}`;
  const resp = await API.get(url);
  return resp;
};
export const InvoiceLink = async data => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.customerOrders}/${subEndpoint.order.shipmentInvoice}`;
  const resp = await API.post(url, data);
  return resp;
};
// ======================order Return section========================

export const OrderReturnListApi = async data => {
  const query = objectToQueryString(data);
  const url = `${apiRoute.return}${apiEndpoints.returns}?${query}&sort_by=desc`;
  const resp = await API.get(url);
  return resp;
};

export const TrackReturnApi = async (productSku: string, returnId: number) => {
  const url = `${apiRoute.return}${apiEndpoints.returns}/${returnId}/items/${productSku}/${subEndpoint.returns.trackDetail}`;
  const resp = await API.get(url);
  return resp;
};

export const PreviousOrderReturnList = async (
  productSku: string,
  returnId: number,
) => {
  const url = `${apiRoute.return}${apiEndpoints.returns}/${subEndpoint.returns.orders}/${returnId}/items/${productSku}/${subEndpoint.returns.previousReturns}`;
  const resp = await API.get(url);
  return resp;
};

export const ReturnPickupDateUpdateApi = async data => {
  const url = `${apiRoute.return}${apiEndpoints.returns}/${subEndpoint.returns.failedPickups}`;
  const resp = await API.put(url, data);
  return resp;
};

// ======================Create Order Return ========================

export const ReturnableItemsList = async (orderId: number) => {
  const url = `${apiRoute.return}${apiEndpoints.returns}/${subEndpoint.returns.orders}/${orderId}/${subEndpoint.returns.returnableItems}`;
  const resp = await API.get(url);
  return resp;
};

export const ReturnReasonList = async () => {
  const url = `${apiRoute.return}${apiEndpoints.returns}/${subEndpoint.returns.reasonsAndActions}`;
  const resp = await API.get(url);
  return resp;
};

export const ReturnRequestApi = async data => {
  const url = `${apiRoute.return}${apiEndpoints.returns}`;
  const resp = await API.post(url, data);
  return resp;
};

export const GetPresignedUrl = async data => {
  const url = `${apiRoute.return}${apiEndpoints.returns}/${subEndpoint.returns.fileUpload}`;
  const resp = await API.post(url, data);
  return resp;
};

// ====================== Retry payment ========================
export const RetryPayment = async data => {
  const url = `${apiRoute.order}${apiEndpoints.orders}/${subEndpoint.order.modifyPayments}`;
  const resp = await API.post(url, data);
  return resp;
};

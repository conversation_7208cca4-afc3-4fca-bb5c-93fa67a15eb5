import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    normalButtonStyle: {
      backgroundColor: colors.background,
      borderWidth: Sizes.x,
      paddingHorizontal: Sizes.x7l,
      paddingVertical: Sizes.xms,
      borderColor: colors.categoryTitle,
    },
    pressedButtonStyle: {
      backgroundColor: colors.categoryTitle,
      borderWidth: Sizes.x,
      paddingHorizontal: Sizes.x7l,
      paddingVertical: Sizes.xms,
      borderColor: colors.categoryTitle,
    },
    imageView: {
      width: '100%',
      height: 250,
    },
    singleCard: {
      width: 220,
      height: 350,
    },
    iconContainer: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    sec4LabelView: {
      flexDirection: 'row',
      justifyContent: 'center',
    },
    sec4View1: {flex: 2.5, overflow: 'hidden'},
    sec4View2: {flex: Sizes.x},
    card4: {
      flex: Sizes.x,
      flexDirection: 'row',
      height: 200,
      borderWidth: Sizes.x,
      paddingHorizontal: Sizes.m,
      backgroundColor: '#fff',
      borderRadius: Sizes.m,
      borderColor: colors.grey2,
      paddingVertical: Sizes.l,
      // elevation: 3,
      shadowColor: colors.black,
      shadowOpacity: 0.1,
      shadowRadius: 5,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      elevation: Sizes.xs,
      //* remove Margin////////////////////////////////
      // marginBottom: Sizes.m,
    },
    labelBorder: {paddingHorizontal: Sizes.sx},
    cardIconView: {
      backgroundColor: '#fff',
      padding: Sizes.sx,
      position: 'absolute',
      right: 10,
      bottom: 13,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: colors.black,
      elevation: Sizes.xs,
      borderRadius: 50,
    },
    cardText: {
      width: '100%',
      height: 100,
      padding: Sizes.m,
      borderBottomLeftRadius: Sizes.xms,
      borderBottomRightRadius: Sizes.xms,
      backgroundColor: colors.skyBlue16,
    },
    textLabel: {
      overflow: 'hidden',
      marginBottom: Sizes.m,
    },
    cardImage: {
      width: '100%',
      height: '100%',
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    cardImage1: {
      borderWidth: Sizes.x,
      width: '100%',
      height: '100%',
      borderRadius: Sizes.xms,
    },
    card: {
      backgroundColor: '#fff',
      borderRadius: Sizes.xms,
      padding: Sizes.xl,
      // margin: 15,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: Sizes.x,
      },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
      elevation: Sizes.s,
    },
    actionRow: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    labelContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      flexWrap: 'wrap',
    },
    textContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      textAlign: 'center',
      textTransform: 'capitalize',
    },
    cardView: {
      shadowColor: colors.tabInActive,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowRadius: 2.62,
      elevation: Sizes.x,
      paddingHorizontal: Sizes.xm,
    },
    section2: {
      paddingTop: Sizes.xm,
    },
    section3: {
      paddingBottom: Sizes.xxl,
      borderWidth: Sizes.x,
      width: '60%',
      height: '80%',
      // flex: Sizes.x,
    },
    card3View1: {
      flex: Sizes.xs,
      backgroundColor: 'red',
    },
    card3View2: {
      backgroundColor: 'blue',
      flex: Sizes.x,
    },
    image: {
      padding: Sizes.xm,
      borderRadius: Sizes.xl,
      width: '100%',
      height: 150,
      resizeMode: 'contain',
    },
    image2: {
      padding: Sizes.xm,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xl,
      borderColor: colors.blueLagoon,
      width: '100%',
      height: 150,
      resizeMode: 'contain',
    },

    actions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
    },
    maincontainer: {
      paddingHorizontal: Sizes.m,
      flex: Sizes.x,
      // borderWidth: Sizes.x,
    },
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: Sizes.l,
    },
    container1: {
      // marginBottom: -Sizes.s,
    },
    line: {
      flex: Sizes.x,
      height: 1,
      color: colors.grey2,
      backgroundColor: '#ccc',
    },
    text: {
      paddingHorizontal: Sizes.sx,
      color: colors.text2,
    },

    mainContinuer: {
      borderWidth: Sizes.x,
      borderColor: colors.border,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      padding: Sizes.xm,
      width: '100%',
    },
    body: {width: '69%', marginHorizontal: Sizes.xm},
    directions: {flexDirection: 'row', alignItems: 'center'},

    flexContainer: {marginHorizontal: Sizes.l},
    dotStyles: {
      backgroundColor: colors.lightGray,
      width: Sizes.l,
      height: Sizes.s + Sizes.xs,
      marginHorizontal: -Sizes.s,
      bottom: Sizes.xxl,
    },

    loaderStyle: {
      alignItems: 'center',
      flex: Sizes.x,
      justifyContent: 'center',
    },
    flex: {flex: Sizes.x},
    newsCarousel: {
      marginVertical: Sizes.m,
      width: Sizes.l,
      height: Sizes.s + Sizes.xs,
      backgroundColor: colors.transparentColor,
    },
  });

export default styles;

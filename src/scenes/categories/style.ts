import {Fonts, Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {Platform, StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.grey7,
    },
    subContainer: {
      flex: Sizes.x,
    },
    lineIcon: {width: Sizes.x70, transform: [{rotate: '180deg'}]},
    mainContainer: {
      backgroundColor: colors.background,
      borderTopRightRadius: Sizes.mx,
      borderTopLeftRadius: Sizes.mx,
      flex: Sizes.x,
    },
    lineImage: {width: Sizes.x70},
    listView: {
      width: '30%',
    },
    listViewItems: {
      width: '70%',
    },
    categoriesLabel: {
      fontWeight: '400',
      fontSize: Sizes.m,
      width: '100%',
    },
    marginH12: {
      marginHorizontal: Sizes.m,
    },
    subCategoryView: {
      justifyContent: 'space-between',
    },
    renderView: {
      width: (DeviceWidth - Sizes.x7l) / Sizes.s,
      flexDirection: 'row',
    },
    labelView: {
      height: Sizes.x7l,
      width: '100%',
      paddingHorizontal: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    activeBg: {
      backgroundColor: colors.primary,
    },
    separates: {
      width: '100%',
      height: Sizes.xs,
      backgroundColor: colors.borderSaprater,
    },
    continuerBlue: {
      backgroundColor: colors.topItemsBg,
    },
    textAdd: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      paddingLeft: Sizes.xm,
    },
    subCategoriesList: {
      fontWeight: '400',
      fontSize: Sizes.m,
    },
    categoryMainView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.m,
      flex: Sizes.x,
    },

    flex: {flex: Sizes.x},
    categoryRenderView: {
      alignItems: 'center',
    },
    categorySubView: {
      alignItems: 'center',
      paddingVertical: Sizes.xms,
    },
    categoryView: {
      borderWidth: Sizes.x + Sizes.z,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      padding: Sizes.s,
    },
    categoryTitleView: {
      width: Math.ceil((DeviceWidth - Sizes.x7l) / Sizes.s),
      height: Sizes.ex82,
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x + Sizes.z,
      borderColor: colors.grey2,
    },
    textView: {
      height: Sizes.xx4l,
      justifyContent: 'flex-start',
    },
    categoryChildView: {
      width: '100%',
      alignSelf: 'center',
      height: Sizes.x66,
      borderRadius: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    categoryTitleSubView: {
      width: Sizes.x9l,
      height: Sizes.x9l,
      borderRadius: Sizes.x70,
      borderWidth: Sizes.x + Sizes.z,
      borderColor: colors.grey2,
    },
    goBackImage: {
      width: Sizes.m,
    },
    sectionHeader: {
      justifyContent: 'center',
      alignSelf: 'center',
      flexDirection: 'row',
      alignItems: 'center',
    },
    flexHalf: {
      flex: Sizes.z,
      marginRight: Sizes.xm,
    },
    itemView: {
      flex: Sizes.x + Sizes.z,
    },
    bothSectionContainer: {
      flexDirection: 'row',
      flex: Sizes.x,
      backgroundColor: colors.grey7,
    },
    firstIndex: {
      borderTopRightRadius: Sizes.mx,
      borderTopLeftRadius: Sizes.mx,
    },
    leftSection: {
      width: Sizes.ex96,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    leftList: {
      width: Sizes.ex96,
    },
    rightSection: {
      flex: Sizes.xs + Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      backgroundColor: colors.background,
      elevation: Sizes.s,
      paddingHorizontal: Sizes.xm,
      paddingTop: Sizes.m,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: Sizes.s,
      },
    },
    categoryOptionWarper: {
      height: Sizes.xl + Sizes.sx,
      borderWidth: Sizes.x + Sizes.z,
      borderRadius: Sizes.sx,
      borderColor: colors.grey2,
      alignSelf: 'center',
      justifyContent: 'center',
      paddingHorizontal: Sizes.sx,
    },
    categoryOptionsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    textCap: {
      textTransform: 'capitalize',
    },
    titleStyle: {
      margin: Sizes.sx,
    },
    searchView: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.text2,
      borderRadius: Sizes.sx,
      height: Sizes.x6l,
      margin: Sizes.xms,
    },
    inputBox: {
      width: DeviceWidth - Sizes.ex0,
      color: colors.text2,
      fontFamily: Fonts.Medium,
      marginBottom: -5,
    },
    headerView: {
      backgroundColor: colors.background,
      borderBottomLeftRadius: Sizes.mx,
      borderBottomRightRadius: Sizes.mx,
    },
    backStyle: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.s,
    },
    rightArrow: {
      transform: [{rotate: '180deg'}],
    },
    textWrapper: {
      flex: Sizes.x,
      marginRight: Sizes.s,
    },
    optionTxt: {
      marginBottom: -Sizes.xs,
    },
    leftArrow: {
      transform: [{rotate: '90deg'}],
    },
    sTitle: {
      height: Sizes.x34,
      overflow: 'visible',
    },
    notFoundImg: {
      width: Sizes.ex3l,
      height: Sizes.ex3l,
    },
    emptyView: {
      marginTop: Sizes.ex,
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    buttonStyle: {
      borderRadius: Sizes.xm,
      height: Sizes.x7l,
      width: Sizes.ex248,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });
export default styles;

import React, {memo, useCallback, useState, useEffect, useMemo} from 'react';
import {View, TouchableOpacity, TextInput} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {FaqItem, ImageIcon, Label, Separator, Spacer} from 'components/atoms';
import {ActivityIndicator} from 'react-native';
import {FlatList} from 'react-native';
import DashedLine from 'react-native-dashed-line';
import {Button} from 'components/molecules';
import {t} from 'i18next';
import Toast from 'react-native-toast-message';
import useToastConfig from 'components/atoms/toastConfig/toastConfig';

type Props = {
  useInsets?: boolean;
  visible: boolean;
  onClose?: (visible: boolean) => void;
  onPressPostQuestion: () => void;
  content?: React.ReactElement;
  flex?: number;
  review: string;
  faqQuestions: QuestionsOutput | null;
  faqLoading: boolean;
  getFaqLoading: boolean;
  onUpdate?: (postId: string, newLikes: number, newDislikes: number) => void;
  handleTextChange: (text: string) => void;
  editQuestion?: () => void;
  setSearchQuery?:React.Dispatch<React.SetStateAction<string>>
};

const FaqListModal = ({
  visible,
  onClose,
  useInsets = false,
  flex = 1,
  onUpdate,
  review,
  faqLoading,
  getFaqLoading,
  faqQuestions,
  handleTextChange,
  onPressPostQuestion,
  editQuestion,
  setSearchQuery: setParentSearchQuery,
}: Props) => {
  // Keep all Hooks at the top level and in the same order
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const [searchQuery, setSearchQuery] = useState('');
  const {toastConfig} = useToastConfig()
  // Move the styles outside useMemo to maintain consistent Hook order
  const styles = stylesWithOutColor(colors);

  // Include all dependencies
  const onPostQuestion = useCallback(() => {
    if (onClose) {
      onClose(!visible);
    }
    setTimeout(() => {
      onPressPostQuestion();
    }, 1000);
  }, [visible, onClose, onPressPostQuestion]);

  const handleSearchText = useCallback(
    text => {
      setSearchQuery(text);
      // Call the parent handler if it exists
      if (handleTextChange) {
        handleTextChange(text);
      }
    },
    [handleTextChange],
  );

  useEffect(() => {
    if (!visible) {
      setSearchQuery('');
    }
  }, [visible]);

  // Compute filtered questions from props
  const filteredQuestions = useMemo(() => {
    return (
      faqQuestions?.result?.filter(item =>
        item.question.toLowerCase().includes(searchQuery.toLowerCase()),
      ) || []
    );
  }, [faqQuestions, searchQuery]);

  // Use the onUpdate prop directly instead of duplicating logic
  const handleItemUpdate = useCallback(
    (postId, newLikes, newDislikes) => {
      if (onUpdate) {
        onUpdate(postId, newLikes, newDislikes);
      }
    },
    [onUpdate],
  );

  // Render optimized item to reduce re-renders
  const renderFaqItem = useCallback(
    ({item}) => (
      <FaqItem
        question={item.question}
        answer={item?.answer?.value}
        createdAt={item?.created_at}
        customerName={item?.customer_name}
        dislikeCount={item?.dislike}
        likeCount={item?.like}
        faqDislikeLoading={faqLoading}
        postId={item?._id}
        onUpdate={handleItemUpdate}
        faqLikeLoading={faqLoading}
        isQuestionTag={true}
      />
    ),
    [faqLoading, handleItemUpdate],
  );

  // Stable key extractor function
  const keyExtractor = useCallback(
    (item, index) => item?._id || `faq-item-${index}`,
    [],
  );

  return (
    <Modal
      onBackButtonPress={() => {onClose?.(!visible)
        setParentSearchQuery &&
        setParentSearchQuery('')
      }}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={[styles.flexContainer]}>
        <View style={styles.centeredView}>
          <View style={styles.modalCloseBtnContainer} />
          <View
            style={[
              styles.modelBg,
              {flex: flex},
              useInsets && {paddingBottom: insets.bottom},
            ]}>
            <View style={styles.mainQuestionsView}>
              <View style={styles.questionsView}>
                <View>
                  <Label
                    text={t('PDP.questionsAndAns')}
                    size="l"
                    fontFamily="Medium"
                    color="text"
                  />

                  {review?.review_meta?.verified_buyers && (
                    <>
                      <Spacer size="s" />
                      <Label
                        text={`${!!review?.review_meta?.verified_buyers} ${t(
                          'PDP.verifiedBuyers',
                        )}`}
                        size="mx"
                        fontFamily="Regular"
                        color="text2"
                      />
                    </>
                  )}
                </View>
                <TouchableOpacity onPress={() => {onClose?.(!visible)
                  setParentSearchQuery &&
                  setParentSearchQuery('')
                }}>
                  <ImageIcon icon="cross" size="xxxl" />
                </TouchableOpacity>
              </View>
              <Spacer size="m" />
              <DashedLine
                dashLength={2}
                dashThickness={1}
                dashColor={colors.grey5}
              />
              <Spacer size="m" />
              <View style={styles.searchView}>
                <ImageIcon icon="search" size="xxl" />
                <Spacer size="xm" type="Horizontal" />
                <Separator color="grey5" Vertical />
                <Spacer size="sx" type="Horizontal" />
                <TextInput
                  testID="txtFaqSearchQuestion"
                  onChangeText={handleSearchText}
                  placeholder={t('PDP.searchQuestion')}
                  placeholderTextColor={colors.text2}
                  style={styles.inputBox}
                  allowFontScaling={false}
                  value={searchQuery}
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity onPress={() => {setSearchQuery('')
                    setParentSearchQuery &&
                    setParentSearchQuery('')
                  }}>
                    <Spacer size="xm" type="Horizontal" />
                    <ImageIcon icon="cross" size="xsl" tintColor="text2" />
                  </TouchableOpacity>
                )}
                {!faqLoading && getFaqLoading ? (
                  <ActivityIndicator size="small" />
                ) : null}
              </View>
              <View style={styles.flexView}>
                <FlatList
                  data={filteredQuestions}
                  extraData={faqLoading}
                  keyExtractor={keyExtractor}
                  renderItem={renderFaqItem}
                  showsVerticalScrollIndicator={false}
                  ListFooterComponent={
                    <>
                      <Spacer size="m" />
                      <View style={styles.centerAlign}>
                        <Label
                          text={t('PDP.doubtsProduct')}
                          size="mx"
                          fontFamily="Medium"
                          color="text2"
                        />
                        <Spacer size="l" />
                        <Button
                          borderColor="categoryTitle"
                          type="bordered"
                          onPress={onPostQuestion}
                          text={t('PDP.postQuestions').toLowerCase()}
                          labelSize="mx"
                          paddingHorizontal="m"
                          radius="xm"
                          labelColor="categoryTitle"
                          size="extra-small"
                          style={styles.productSuggestionBtn}
                          labelStyle={styles.textCap}
                        />
                      </View>
                    </>
                  }
                />
              </View>
            </View>
          </View>
        </View>
      </View>
      <Toast config={toastConfig} swipeable={false} />
    </Modal>
  );
};

export default memo(FaqListModal);

import React, {useState} from 'react';
import {
  FlatList,
  TouchableOpacity,
  View,
  ActivityIndicator,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import ImageIcon from '../imageIcon';
import Label from '../label';
import Spacer from '../spacer';
import {downloadFile} from 'utils/utils';
import {useMemo} from 'react';

type ProductAttachment = {
  title: string;
  url: string;
};

type ProductData = {
  name: string;
};

type Props = {
  pdfFiles: ProductAttachment[];
  product: ProductData | null;
};

const DownloadCatalogue: React.FC<Props> = ({pdfFiles, product}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [loaderState, setLoaderState] = useState({});

  const renderItem = ({
    index,
    item,
  }: {
    item: ProductAttachment;
    index: number;
  }) => (
    <TouchableOpacity
      testID='tODownloadCatalogue'
      key={index}
      onPress={() =>
        loaderState && loaderState[index]
          ? {}
          : downloadFile(item?.url, item?.title, (value: boolean) => {
              setLoaderState({...loaderState, [index]: value});
            })
      }
      style={styles.catalogueView}>
      <Spacer size="l" />
      <Label text={item?.title} weight="500" size="mx" color="pumpkinOrange" />
      <Spacer size="xm" type="Horizontal" />
      <View style={styles.imageView}>
        {loaderState && loaderState[index] ? (
          <ActivityIndicator size="small" color={colors.categoryTitle} />
        ) : (
          <ImageIcon
            icon="downloadButtonIcon"
            tintColor="pumpkinOrange"
            size="l"
          />
        )}
      </View>
      <Spacer size="l" type="Horizontal" />
      <Spacer size="l" />
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={pdfFiles}
      horizontal
      style={styles.container}
      showsHorizontalScrollIndicator={false}
      renderItem={renderItem}
    />
  );
};

export default React.memo(DownloadCatalogue);

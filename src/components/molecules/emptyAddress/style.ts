import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    wrapper: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    mainView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
    },
    bgImage: {
      width: Sizes.ex3l,
      height: Sizes.ex2l,
    },
    imageView: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyImage: {
      position: 'absolute',
      width: Sizes.exl,
      height: Sizes.ex1,
    },
    labelStyle: {
      fontFamily: Fonts.Medium,
      paddingVertical: Sizes.sx,
      paddingHorizontal: Sizes.x7l,
      textTransform: 'capitalize',
    },
  });

export default styles;

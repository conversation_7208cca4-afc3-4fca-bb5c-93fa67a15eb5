import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const RatingReview = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1000}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="10" rx="5" ry="5" width={width - 20} height="150" />
        <Rect x="10" y="170" rx="5" ry="5" width={width - 20} height="30" />
        <Rect x="10" y="210" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x="10" y="320" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x="10" y="430" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x="10" y="540" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x="10" y="650" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x="10" y="760" rx="5" ry="5" width={width - 20} height="100" />
        <Rect x="10" y="870" rx="5" ry="5" width={width - 20} height="100" />
      </ContentLoader>
    </ScrollView>
  );
};

export default RatingReview;

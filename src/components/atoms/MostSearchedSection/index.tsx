import React, {useCallback, useMemo} from 'react';
import {View, FlatList} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {checkDevice} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import ProductCardVertical from '../productCardVertical';
import TypingEffect from '../typingEffect';
import ImageIcon from '../imageIcon';
import Spacer from '../spacer';
import stylesWithOutColor from './style';
import {getProductCardProps} from 'utils/productProps';

const TAG = 'MostSearchedSection';

interface MostSearchedSectionProps {
  section: any;
  userInfo: any;
  isLoggedIn: any;
  navigation: any;
}

const MostSearchedSection: React.FC<MostSearchedSectionProps> = ({
  section,
  userInfo,
  isLoggedIn,
  navigation,
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const EMPTY_FREE_PRODUCTS = useMemo(() => [], []);

  const renderMostSearchedItem = useCallback(
    ({item, index}: {item: ProductData; index: number}) => (
      <ErrorHandler
        componentName={`${TAG} ProductCardVertical`}
        onErrorComponent={<View />}>
        <ProductCardVertical
          {...getProductCardProps(item)}
          index={index}
          size="large"
          imageWithBorder
          maxWidth={checkDevice() ? 0.24 : undefined}
          showWishlist
          navigation={navigation}
          freeProducts={EMPTY_FREE_PRODUCTS}
        />
      </ErrorHandler>
    ),
    [getProductCardProps, navigation],
  );

  const keyExtractor = useCallback((_, index) => index.toString(), []);
  const itemSeparator = useMemo(() => <Spacer size="xm" />, []);

  return (
    <View style={styles.searchedProduct}>
      <View style={styles.searchedView}>
        <TypingEffect title={section?.title} />
        <ImageIcon icon="mostSerchedproduct" size="xxl" />
      </View>

      <View style={styles.hotSellerProductView}>
        <FlatList
          numColumns={checkDevice() ? null : 2}
          horizontal={checkDevice()}
          data={section?.elements}
          keyExtractor={keyExtractor}
          ItemSeparatorComponent={itemSeparator}
          renderItem={renderMostSearchedItem}
          onEndReachedThreshold={0.8}
          removeClippedSubviews
          windowSize={5}
          maxToRenderPerBatch={5}
          updateCellsBatchingPeriod={50}
        />
      </View>

      <Spacer size="m" />
    </View>
  );
};

export default React.memo(MostSearchedSection);

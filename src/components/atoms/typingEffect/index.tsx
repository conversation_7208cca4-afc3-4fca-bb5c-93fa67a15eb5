import React, { useEffect, useState, useRef } from 'react';
import { View } from 'react-native';
// import Animated, {
//   useSharedValue,
//   withTiming,
//   Easing,
// } from 'react-native-reanimated';
import styles from './style';
import Label from '../label';

type Props = {
  title: string;
};

const TypingEffect = ({ title }: Props) => {
  // const [displayedText, setDisplayedText] = useState('');
  // const typingIndex = useSharedValue(0);
  // const isTyping = useRef(true);

  // useEffect(() => {
  //   let intervalId: NodeJS.Timeout;

  //   const typeCharacter = () => {
  //     if (isTyping.current) {
  //       setDisplayedText(title.slice(0, Math.round(typingIndex.value)));

  //       typingIndex.value = withTiming(typingIndex.value + 1, {
  //         duration: 20, // Go as fast as possible.
  //         easing: Easing.linear,
  //       });

  //       if (Math.round(typingIndex.value) > title.length) {
  //         isTyping.current = false; // Stop typing.
  //         clearInterval(intervalId); // Clear the existing interval.

  //         setTimeout(() => {
  //           typingIndex.value = withTiming(0, { duration: 0 }); // Reset
  //           isTyping.current = true; //restart
  //           intervalId = setInterval(typeCharacter, 5); // Restart the interval
  //         }, 1000);
  //       }
  //     }
  //   };

  //   intervalId = setInterval(typeCharacter, 0); // Start the interval

  //   return () => clearInterval(intervalId); // Cleanup
  // }, [title]);

  return (
    <View style={styles.container}>
      <Label 
        // text={displayedText} 
        text={title}
        color="text2" 
        size="l" 
        fontFamily="Medium" 
      />
    </View>
  );
};

export default React.memo(TypingEffect);
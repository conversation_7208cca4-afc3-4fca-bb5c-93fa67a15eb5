import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    paginationContainer: {
      width: Sizes.screenWidth * 0.195,
      alignItems: 'center',
    },
    pdpPaginationContainer: {
      maxWidth: Sizes.screenWidth * 0.195,
      position: 'absolute',
      bottom: 30,
      alignSelf: 'center',
      backgroundColor: colors.grey11,
      // paddingHorizontal: Sizes.xs,
      borderRadius: Sizes.sx,
      paddingVertical: Sizes.xs,
    },
    // customPaginationContainer: {
    //   position: 'absolute',
    //   bottom: 30,
    //   padding: 10,
    //   borderRadius: 10,
    // },
    paginationCategoryContainer: {
      padding: Sizes.xs,
      backgroundColor: colors.bgPaginate,
      alignItems: 'center',
      maxHeight: Sizes.xms,
      borderRadius: Sizes.xm,
    },
  });

export default styles;

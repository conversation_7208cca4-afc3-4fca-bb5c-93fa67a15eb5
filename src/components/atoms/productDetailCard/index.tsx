import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer, Quantity} from 'components/atoms';
import stylesWithOutColor from './style';
import {Button} from 'components/molecules';
import {t} from 'i18next';
import {useMemo} from 'react';

type Props = {
  product: ProductData | null;
  isAddToCartPressed?: boolean;
  qty?: number | any;
  setQty: (count: number) => void;
  addCart: () => void;
};

const ProductDetailCard = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {product, isAddToCartPressed, qty, setQty, addCart} = props;

  return (
    <View style={styles.childProductView}>
      <Label
        fontFamily="SemiBold"
        numberOfLines={2}
        ellipsizeMode={'tail'}
        color="text"
        size="mx"
        text={product?.name}
      />
      {product?.type === 'grouped' ? (
        <>
          <Spacer size="xm" />
          <View style={styles.fRow}>
            <Label
              fontFamily="Medium"
              numberOfLines={2}
              ellipsizeMode={'tail'}
              color="text2"
              size="m"
              text={product?.attributes?.short_description}
              style={styles.flex}
            />
            <Spacer size="xm" />
            <Label
              fontFamily="Medium"
              color="text"
              size="l"
              text={`${t('PDP.startingAt')} : `}>
              <Label
                fontFamily="SemiBold"
                color="text"
                size="l"
                text={` ₹${product?.pricing?.selling_price}`}
              />
            </Label>
          </View>
        </>
      ) : (
        <>
          <Spacer size="s" />
          <View style={styles.amountView}>
            <Label
              fontFamily="SemiBold"
              color="text"
              size="xl"
              text={`₹${product?.pricing?.selling_price}`}
            />
            <Spacer size="xm" type="Horizontal" />
            <Label
              fontFamily="Medium"
              textDecorationLine="line-through"
              color="text3"
              size="mx"
              text={product?.pricing?.price}
            />
            <Spacer size="xm" type="Horizontal" />
            {product?.pricing?.discount?.label &&
              !product?.pricing?.discount?.label?.startsWith('0') && (
                <Label
                  fontFamily="Medium"
                  color="green2"
                  size="mx"
                  text={product?.pricing?.discount?.label}
                />
              )}
          </View>

          {product?.attributes?.reward_points &&
            Number(product?.attributes?.reward_points) > 0 && (
              <>
                <Spacer size="s" />
                <View style={styles.centerAlign}>
                  <ImageIcon icon="coin" size="xx" />
                  <Spacer size="xm" type="Horizontal" />
                  <Label
                    fontFamily="Medium"
                    color="orange"
                    size="l"
                    text={`${product?.attributes?.reward_points}`}
                  />
                </View>
              </>
            )}
          <Spacer size="s" />
          <View style={styles.fRow}>
            <View style={styles.expiryView}>
              <Label
                fontFamily="Medium"
                numberOfLines={3}
                ellipsizeMode={'tail'}
                color="text2"
                size="m"
                text={product?.attributes?.short_description?.substr(0, 40)}
              />
              <Spacer size="s" />
              <View style={styles.expiryDateView}>
                <Label
                  fontFamily="Medium"
                  color="categoryTitle"
                  size="m"
                  text={`${t('PDP.expiryDate')} : `}
                />
                <Label
                  fontFamily="Medium"
                  color="text2"
                  size="m"
                  text={product?.attributes?.expiry_date?.label}
                />
                <Spacer size="s" type="Horizontal" />
                <TouchableOpacity style={styles.infoIcon}>
                  <ImageIcon
                    tintColor="categoryTitle"
                    size="l"
                    icon="informationIcon"
                  />
                </TouchableOpacity>
              </View>
            </View>
            <View style={styles.expireEndView}>
              {product?.inventory?.is_in_stock ? (
                <View>
                  {!isAddToCartPressed ? (
                    <Button
                      type="bordered"
                      onPress={() => addCart()}
                      text={t('buttons.add').toUpperCase()}
                      radius="sx"
                      size="extra-small"
                      selfAlign="stretch"
                      borderColor={'newSunnyOrange'}
                      labelColor={'newSunnyOrange'}
                      style={styles.addBtnView}
                    />
                  ) : (
                    <Quantity
                      min={product?.inventory?.min_sale_qty ?? 1}
                      max={product?.inventory?.max_sale_qty ?? 1}
                      value={qty}
                      onUpdate={(count: number) => setQty(count)}
                      backgroundColor="newSunnyOrange"
                    />
                  )}
                </View>
              ) : null}
            </View>
          </View>
        </>
      )}
    </View>
  );
};

export default ProductDetailCard;

import {Sizes, Fonts} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const Styles = (colors: Theme['colors']) => {
  const insets = useSafeAreaInsets();
  return StyleSheet.create({
    modalStyle: {margin: 0},
    flexContainer: {flex: Sizes.x},
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    modelBg: {
      flex: Sizes.s,
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
      paddingHorizontal: Sizes.s,
    },
    modelView: {
      flex: Sizes.x,
    },
    filterListLeftContainer: {
      flex: Sizes.xs,
      backgroundColor: colors.background,
    },
    filterListLeft: {
      flex: Sizes.x,
      backgroundColor: colors.grey4,
      borderTopRightRadius: Sizes.m,
    },
    listItemLeft: {
      paddingVertical: Sizes.mx,
      paddingHorizontal: Sizes.xx,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
    },
    searchInput: {
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    filterListRightContainer: {
      flex: 3,
      paddingLeft: Sizes.x7l,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    modalHeader: {
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.xl,
    },
    flexRow: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    filterSectionHeader: {
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.xx,
    },
    rangeSliderStyle: {
      paddingTop: '25%',
      paddingBottom: '4%',
    },
    sortContainer: {
      flex: Sizes.x,
      padding: Sizes.l,
    },
    rowCenteredItems: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    footerSection: {
      borderTopColor: colors.grey2,
      borderTopWidth: 1,
      paddingBottom: insets.bottom,
    },
    footerClearBtn: {
      fontSize: Sizes.mx,
      fontWeight: '500',
      color: colors.newSunnyOrange,
    },
    footerApplyBtn: {
      fontSize: Sizes.l,
      fontWeight: '500',
      color: colors.text2,
    },
    rangeLabelContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingTop: '1%',
      paddingBottom: '5%',
    },
    ratingCheckboxRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.sx,
    },
    starIconRow: {
      paddingBottom: Sizes.s,
      flexDirection: 'row',
    },
    questionsView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    mainQuestionsView: {
      padding: Sizes.m,
      flex: Sizes.x,
    },
    searchView: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      borderRadius: Sizes.sx,
      height: Sizes.x6l,
      paddingLeft: Sizes.xms,
    },
    inputBox: {
      width: '80%',
      color: colors.text2,
    },
    questionItemView: {
      borderBottomColor: colors.grey2,
      borderBottomWidth: Sizes.x,
      paddingVertical: Sizes.m,
    },
    questionSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    questionMainView: {
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.sx,
      backgroundColor: colors.green3,
      borderRadius: Sizes.s,
    },
    answerView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    answerSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    likeDisLikeView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    flexView: {
      flex: Sizes.x,
    },
    productSuggestionBtn: {
      fontFamily: Fonts.Medium,
      height: Sizes.x6l,
    },
    centerAlign: {
      alignItems: 'center',
    },
    textCap: {
      textTransform: 'capitalize',
    },
  });
};

export default Styles;

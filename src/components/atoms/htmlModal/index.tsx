import React from 'react';
import {
  TouchableOpacity,
  View,
  ScrollView,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import RenderHTML from 'react-native-render-html';
import Modal from 'react-native-modal';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {useMemo} from 'react';

const {width} = Dimensions.get('window');

type Props = {
  visible?: boolean;
  onClose?: () => void;
  title?: string;
  htmlCode?: any;
};

const htmlModal = (props: Props) => {
  const {visible, onClose, title, htmlCode} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={styles.imageModelMain}>
        <View style={styles.subImageView}>
          <TouchableWithoutFeedback onPress={onClose}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={() => onClose()}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x5l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <Spacer size="xms" />
          <View style={styles.pageTitleView}>
            <Label
              text={title || ''}
              size="l"
              weight="600"
              color="text"
              lineHeight="xxl"
            />
          </View>
          <Spacer size="sx" />
          <ScrollView style={styles.readMoreDataView}>
            <RenderHTML
              contentWidth={width}
              source={{html: htmlCode}}
              baseFontStyle={styles.descText}
              tagsStyles={styles.htmlRenderStyle}
            />
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default htmlModal;

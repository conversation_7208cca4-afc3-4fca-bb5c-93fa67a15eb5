import { debugLog } from './debugLog';
import {postRequest} from './get_request';

export const clientError = async info => {
  let headers = {
    'x-api-key': 'cyUi8JOplpParc8RqoeQcYkQiFNzOjePQJZMLAYNi',
  };
  await postRequest(
    'https://8berd09w4d.execute-api.ap-south-1.amazonaws.com/prod/api/V1/query_error',
    info,
    headers,
  )
    .then(async res => {
      debugLog('then res clientError', res);
      debugLog('then res clientError', await res.json());
    })
    .then(data => {
      debugLog('then data clientError', data);
    })
    .catch(error => debugLog('error clientError', error));
};

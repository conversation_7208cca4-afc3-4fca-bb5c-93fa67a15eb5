import Label from 'components/atoms/label';
import React, {useCallback, useState} from 'react';
import {TouchableOpacity, View, Text} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import moment from 'moment';
import ImageIcon from '../imageIcon';
import {t} from 'i18next';
import {useMemo} from 'react';

type Props = {
  items?: any;
  status?: null;
  editQuestion?: any;
};

const Faqs = ({items, status, editQuestion}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [showMore, setShowMore] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [disLike, setDisLike] = useState(0);
  const [isDisabled, setIsDisabled] = useState(false);

  const disLikeCLick = () => {
    setIsDisabled(true);
    setTimeout(() => {
      setIsDisabled(false);
    }, 800);

    if (disLike === 0) {
      setDisLike(disLike + 1);
      let data = {
        likes: likeCount === 1 ? items?.like - 1 : items?.like,
        dislikes: items?.dislike + 1,
        questionId: items?._id,
      };
      editQuestion(data);
    } else {
      setDisLike(disLike - 1);
      let data = {
        likes: items?.like,
        dislikes: items?.dislike - 1,
        questionId: items?._id,
      };
      editQuestion(data);
    }
    setLikeCount(0);
  };

  const LikeCLick = () => {
    setIsDisabled(true);

    if (likeCount === 0) {
      setLikeCount(likeCount + 1);
      let data = {
        likes: items?.like + 1,
        dislikes: disLike === 1 ? items?.dislike - 1 : items?.dislike,
        questionId: items?._id,
      };
      editQuestion(data);
    } else {
      setLikeCount(likeCount - 1);
      let data = {
        likes: items?.like - 1,
        dislikes: items?.dislike,
        questionId: items?._id,
      };
      editQuestion(data);
    }
    setDisLike(0);
    setIsDisabled(false);
  };

  return (
    <View style={styles.questionAndAnswer}>
      <View style={styles.questionTextView}>
        <Label
          weight="600"
          color="textLight"
          style={styles.paddingSpace}
          text={t('faqs.question')}
        />
        <Label
          weight="600"
          color="textLight"
          style={styles.paddingSpace}
          text={items?.question}
        />
      </View>

      <View style={styles.answerText}>
        <Label
          weight="600"
          color="textLight"
          style={styles.paddingSpace}
          text={t('faqs.answer')}
        />

        <Text style={styles.questionAndAnswerSubText}>
          {showMore
            ? items?.answer?.value
            : items?.answer?.value.length > 60
            ? items?.answer?.value.slice(0, 60) + '... '
            : items?.answer?.value}
          {items?.answer?.value.length > 60 && (
            <Label
              color="primary"
              weight="500"
              onPress={() => setShowMore(!showMore)}
              text={showMore ? t('faqs.readLess') : t('faqs.readMore')}
            />
          )}
        </Text>
      </View>

      <View
        style={[
          styles.dateView,
          styles.alignRowCenter,
          status ? styles.spaceJustify : styles.itemsEnd,
        ]}>
        {status ? (
          <View style={[styles.likeDislikeView, styles.alignRowCenter]}>
            <View style={styles.alignRowCenter}>
              <TouchableOpacity
              testID='tOFaqLikeClick'
                disabled={isDisabled}
                style={styles.likeCountBtn}
                onPress={LikeCLick}>
                {likeCount === 0 ? (
                  <ImageIcon tintColor="textLight" icon="likeBlank" />
                ) : (
                  <ImageIcon tintColor="textLight" icon="likeIcon" />
                )}
              </TouchableOpacity>
              <Label text={items?.like} />
            </View>

            <View style={[styles.disLikeView, styles.alignRowCenter]}>
              <TouchableOpacity
              testID='tOFaqDislikeClick'
                disabled={isDisabled}
                style={styles.dislikeBtn}
                onPress={disLikeCLick}>
                {disLike === 0 ? (
                  <ImageIcon
                    tintColor="textLight"
                    style={styles.likeImage}
                    icon="likeBlank"
                  />
                ) : (
                  <ImageIcon
                    tintColor="textLight"
                    style={styles.likeImage}
                    icon="likeIcon"
                  />
                )}
              </TouchableOpacity>
              <Label text={items?.dislike} />
            </View>
          </View>
        ) : null}
        <Label text={moment(items?.created_at).format('DD-MM-YYYY')} />
      </View>
    </View>
  );
};

export default Faqs;

import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  View,
  ActivityIndicator,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useTheme} from '@react-navigation/native';
import RenderHTML from 'react-native-render-html';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import {Button, Header} from 'components/molecules';
import {RootStackParamsList} from '../../routes';
import stylesWithOutColor from './style';
import {Fonts} from 'common';
import {AnalyticsEvents, FilterAndSortModal} from 'components/organisms';
import {
  FooterButton,
  ImageIcon,
  Label,
  ProductCardVertical,
  ReanimatedCarousel,
  Separator,
  Spacer,
  Tag,
  HtmlModal,
  WishlistButton,
  FlatListCarousel,
} from 'components/atoms';
import {getCategoryDetails, getFilterProductData} from 'services/category';
import {CategoryDetailLoader} from 'skeletonLoader';
import {homePageSections} from 'services/home';
import {checkDevice, defaultPrice} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import {ButtonProps} from 'components/atoms/footer';
// import {ScrollContext} from '../homeScreen/homePage/contextApi/scrollContext';
import {RootState} from '@types/local';
import {setCurrentScreenName} from '@microsoft/react-native-clarity';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'CategoryDetail'>;
};

const CategoryDetail = ({route, navigation}: Props) => {
  const TAG = 'CategoryDetail';
  const categoryId = route.params.categoryId;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const [pageNumber, setPageNumber] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [showScrollToTopButton, setShowScrollToTopButton] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const flatListRefTop = useRef(null);
  const [selectedFilters, setSelectedFilters] = useState<SelectCategoryFilters>(
    {
      page_no: pageNumber,
      applyFilters: {},
      sort_by: 'rcm',
    },
  );
  const {price_range, rating} = selectedFilters;
  // const {
  //   flatListRefTop,
  //   scrollToTop: scrollToTopButton,
  //   showScrollToTopButton: showToTop,
  //   handleScroll,
  // } = useContext(ScrollContext);
  const previousOffsetY = useRef(0);
  const [products, setProducts] = useState<Array<ProductData>>([]);
  const [pageTitle, setPageTitle] = useState('');
  const [carouselData, setCarouselData] = useState([]);
  const [getFilterData, setGetFilterData] = useState([{}]);
  const [sortDescription, setSortDescription] = useState('');
  const [productCount, setProductCount] = useState(0);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isLoadMore, setIsLoadMore] = useState(false);
  const [modalType, setModalType] = useState<'filter' | 'sort'>('filter');
  const [filterOptions, setFilterOptions] = useState<Array<CategoryFilters>>(
    [],
  );
  const [filterLoader, setFilterLoader] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const contentWidth = useMemo(() => Dimensions.get('window').width, []);
  const [viewHeight, setViewHeight] = useState(0);
  const [priceValue, setPriceValue] = useState({min: 0, max: 0});
  const [totalProduct, setTotalProduct] = useState(0);
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);

  const handleLayout = useCallback(event => {
    const {height} = event?.nativeEvent?.layout;
    setTimeout(() => {
      setViewHeight(height || 0);
    }, 500);
  }, []);

  useEffect(() => {
    setCurrentScreenName('Category page');
    return () => {
      setCurrentScreenName('Clarity event');
    };
  }, []);

  useEffect(() => {
    AnalyticsEvents(
      'DIRECTORY_FOR_CATEGORY',
      'Full store directory viewed',
      {},
      userInfo,
      isLoggedIn,
    );
    Promise.all([
      getCategoryFilters(),
      getProductsData(1, selectedFilters),
      getCategoryAds(),
    ]).then(() => {});
  }, []);

  const plusSign = useMemo(() => {
    let sign = false;
    const ratingData = filterOptions?.filter(item => item?.value === 'rating');
    if (ratingData?.length > 0) {
      sign = ratingData[0]?.options.every(option => option.label.includes('+'));
    }
    return sign;
  }, [filterOptions]);

  const getProductsData = useCallback(
    async (
      pageNo: number,
      filter: SelectCategoryFilters,
      hideLoader = false,
    ) => {
      if (categoryId) {
        if (!hideLoader) {
          if (pageNo === 1) {
            setLoading(true);
          } else {
            setIsLoadMore(true);
          }
        }
        const {data} = await getCategoryDetails(categoryId, {
          ...filter,
          page_no: pageNo,
        });
        setLoading(false);
        setIsLoadMore(false);
        let listData = data?.categoryProducts;
        setProductCount(listData?.pagination?.total_results);
        if (listData) {
          const {category} = listData;
          setSortDescription(category.description);
          setPageTitle(category?.name);
          const productData =
            pageNo === 1
              ? listData?.products ?? []
              : [...products, ...listData?.products];
          setProducts(productData);

          setTotalPages(
            Math.ceil(
              listData?.pagination?.total_results /
                listData?.pagination?.results_per_page,
            ),
          );
          setTotalProduct(listData?.pagination?.total_results);
          setPriceValue(listData?.filters?.price_stats);
        }
        AnalyticsEvents('PRODUCT_SCREEN', 'Product Viewed', data);

        // appflyer list view event
        appsFlyerEvent('CategoryViewed', {
          listName: data?.categoryProducts?.category?.name,
          contentIds: categoryId,
        });
      }
    },
    [categoryId, products],
  );

  const getCategoryAds = useCallback(async () => {
    let params = {'page-type': 'category'};
    const response = await homePageSections(params);
    if (response?.status) {
      const adsData = response?.data?.sections;
      if (adsData?.length > 0) {
        setCarouselData(adsData[0]?.elements);
      }
    }
  }, []);

  const getCategoryFilters = useCallback(async () => {
    if (categoryId) {
      setFilterLoader(true);
      const {data} = await getFilterProductData(categoryId);
      setFilterLoader(false);
      setGetFilterData(data?.filters);
      if (data?.filters) {
        let apiFilters = data?.filters
          ?.filter((item: CategoryFilters) => item?.options?.length > 0)
          ?.map((item: CategoryFilters) => ({
            key: item?.label,
            value:
              item?.code === Number(1)
                ? 'brand_ids'
                : item?.code === Number(2)
                ? 'price_range'
                : 'rating',
            type:
              item?.code === Number(1)
                ? 'checkBox'
                : item?.code === Number(2)
                ? 'range'
                : '',
            isSearch: item?.code === '1' || item?.code === 1 ? true : false,
            options: item?.options,
            min:
              item?.code === '2'
                ? Number(item?.options?.[0]?.value?.split(' - ')?.[0])
                : 0,
            max:
              item?.code === '2'
                ? Number(
                    item?.options?.[item?.options?.length - 1]?.value?.split(
                      ' - ',
                    )?.[1],
                  )
                : defaultPrice,
          }));
        if (apiFilters?.length > 0) {
          const priceList = apiFilters?.filter(
            (item: CategoryFilters) => item?.value === 'price_range',
          );
          if (priceList?.length === 0) {
            apiFilters = [
              ...apiFilters,
              {
                key: 'Price Range',
                value: 'price_range',
                type: 'range',
                isSearch: false,
                min: 0,
                max: defaultPrice,
              },
            ];
          }
        }
        setFilterOptions(apiFilters);
      }
    }
  }, [categoryId]);

  const fetchMoreData = useCallback(async () => {
    if (!loading && !isLoadMore && pageNumber <= totalPages) {
      setPageNumber(pageNumber + 1);
      await getProductsData(pageNumber + 1, selectedFilters);
    }
  }, [
    loading,
    isLoadMore,
    pageNumber,
    totalPages,
    getProductsData,
    selectedFilters,
  ]);

  const listEmptyComponent = useCallback(() => {
    return (
      <>
        {!loading ? (
          <View
            style={[
              styles.emptyView,
              Object.keys(selectedFilters)?.length === 0
                ? styles.justifyCenter
                : styles.justifyLeft,
            ]}>
            <ImageIcon
              resizeMode="contain"
              style={styles.notFoundImg}
              icon="noDatafoundIcon"
            />
            <Label
              text={
                Object.keys(selectedFilters)?.length === 0
                  ? t('categoryDetail.noProduct')
                  : t('categoryDetail.selectedProduct')
              }
            />
            <Label text={t('categoryDetail.adjustFilter')} />
          </View>
        ) : (
          <View style={styles.listFooterLoader}>
            <ActivityIndicator size="large" />
          </View>
        )}
      </>
    );
  }, [
    loading,
    selectedFilters,
    styles.emptyView,
    styles.justifyCenter,
    styles.justifyLeft,
    styles.listFooterLoader,
    styles.notFoundImg,
  ]);

  const renderFooter = useCallback(() => {
    return pageNumber <= totalPages && isLoadMore ? (
      <ActivityIndicator size="large" />
    ) : null;
  }, [pageNumber, totalPages, isLoadMore]);

  const verticalSeparator = useCallback(() => {
    return <Separator Vertical color="grey2" />;
  }, []);

  const resetAllFilters = useCallback(() => {
    const clearFilter = {
      page_no: 1,
      applyFilters: {},
      sort_by: 'rcm',
    };
    setSelectedFilters(clearFilter);
    setProducts([]);
    getProductsData(1, clearFilter);
    setProductCount(0);
  }, [getProductsData]);

  const brandIds = useMemo(
    () => selectedFilters.brand_ids || [],
    [selectedFilters.brand_ids],
  );
  const brandOptions = useMemo(
    () => getFilterData?.find(d => d.label === 'Brands')?.options || [],
    [getFilterData],
  );
  const matchingBrands = useMemo(
    () =>
      brandOptions.filter((option: CategoryFiltersOptions) =>
        brandIds.includes(option.value),
      ),
    [brandIds, brandOptions],
  );

  const removeFilter = useCallback(
    (key: string, value: string) => {
      const updatedFilters = {...selectedFilters};

      if (key === 'brand_ids') {
        if (Array.isArray(updatedFilters[key])) {
          updatedFilters[key] = updatedFilters[key].filter(
            (item: string) => item !== value,
          );

          if (updatedFilters[key].length === 0) {
            delete updatedFilters[key];
          }
        }
      } else if (key === 'price_range' || key === 'rating') {
        delete updatedFilters[key];
      }

      setSelectedFilters(updatedFilters);
      setProducts([]);
      getProductsData(1, updatedFilters);
      setProductCount(0);
    },
    [getProductsData, selectedFilters],
  );

  const getFirst50Characters = useCallback((html: string) => {
    let charCount = 0;
    let truncatedHTML = '';
    let insideTag = false;
    let truncated = false;

    for (let i = 0; i < html?.length; i++) {
      const char = html[i];
      if (char === '<') {
        insideTag = true;
      }
      if (!insideTag) {
        charCount++;
      }
      truncatedHTML += char;
      if (char === '>') {
        insideTag = false;
      }
      if (charCount >= 100) {
        truncated = true;
        break;
      }
    }
    return truncated ? truncatedHTML + '...' : truncatedHTML;
  }, []);
  const truncatedHTML = useMemo(
    () => getFirst50Characters(sortDescription),
    [sortDescription, getFirst50Characters],
  );

  const renderHtml = useMemo(() => {
    return (
      <RenderHTML
        contentWidth={contentWidth}
        source={{html: truncatedHTML}}
        baseFontStyle={styles.descText}
        tagsStyles={styles.htmlRenderStyle}
        allowFontScaling={false}
      />
    );
  }, [contentWidth, truncatedHTML, styles.descText, styles.htmlRenderStyle]);

  const onFilter = useCallback(
    (filter: SelectCategoryFilters) => {
      setSelectedFilters(filter);
      setProducts([]);
      getProductsData(1, filter);
      setProductCount(0);
    },
    [getProductsData],
  );

  const scrollToTop = useCallback(() => {
    if (flatListRefTop.current) {
      flatListRefTop.current.scrollToOffset({offset: 0, animated: true});
    }
  }, [flatListRefTop]);

  const handleModalToggle = useCallback(() => {
    setVisible(prev => !prev);
  }, []);

  const handleFaqModalToggle = useCallback(() => {
    setModalVisible(prev => !prev);
  }, []);

  const openFilterModal = useCallback(() => {
    setModalType('filter');
    setVisible(true);
  }, []);

  const openSortModal = useCallback(() => {
    setModalType('sort');
    setVisible(true);
  }, []);

  const renderItem = useCallback(
    ({item, index}) => (
      <>
        <ErrorHandler
          componentName={`${TAG} ProductCardVertical`}
          onErrorComponent={<View />}>
          <ProductCardVertical
            index={index}
            skuId={item?.sku}
            actionBtn={item?.action_btn}
            productType={item?.type}
            maxWidth={checkDevice() ? 0.24 : 0.48}
            item={item}
            imageWithBorder={true}
            maxSaleQty={item?.max_sale_qty}
            image={item?.media?.mobile_image}
            name={item?.name}
            inStock={item.is_in_stock}
            rewardPoint={item?.reward_points}
            description={item?.short_description}
            rating={(item?.rating_count === 'null' ||
            item.average_rating === null
              ? 0
              : Number(item?.rating) || Number(item?.average_rating)
            ).toFixed(1)}
            price={item?.price}
            sellingPrice={item?.selling_price}
            currencySymbol={item?.currency_symbol}
            discount={item?.discount?.label}
            ratingCount={item?.rating_count ? `(${item?.rating_count})` : '(0)'}
            navigation={navigation}
            showWishlist={true}
          />
        </ErrorHandler>
      </>
    ),
    [navigation],
  );

  const memoizedFilterOptions = useMemo(() => {
    if (
      priceValue?.min &&
      priceValue?.max &&
      priceValue?.min !== 0 &&
      priceValue?.max !== 0
    ) {
      return filterOptions.map(item =>
        item.value === 'price_range'
          ? {
              ...item,
              min: priceValue.min,
              max: priceValue.max + (priceValue.max === priceValue.min ? 1 : 0),
            }
          : item,
      );
    }
    return filterOptions;
  }, [filterOptions, priceValue?.min, priceValue?.max]);

  const footerButtons: ButtonProps[] = useMemo(
    () => [
      {
        labelSize: 'l',
        fontFamily: Fonts.Regular,
        labelColor: 'categoryTitle',
        size: 'large',
        selfAlign: 'stretch',
        text: t('filters.filters'),
        iconLeft: 'filterIcons',
        tintColor: 'categoryTitle',
        onPress: openFilterModal,
        hidden: false,
        key: 'openFilterButton',
      },
      {
        text: t('filters.sortBy'),
        subText: selectedFilters?.sort_by ? selectedFilters?.sort_by : 'rcm',
        iconLeft: 'sortIcons',
        onPress: openSortModal,
        labelSize: 'l',
        fontFamily: Fonts.Regular,
        labelColor: 'categoryTitle',
        size: 'large',
        selfAlign: 'stretch',
        hidden: false,
        tintColor: 'categoryTitle',
        key: 'sortByFilter',
      },
    ],
    [selectedFilters?.sort_by, openFilterModal, openSortModal],
  );

  const keyExtractor = useCallback((_, index) => index.toString(), []);

  const itemSeparator = useCallback(() => {
    return <Spacer size="m" />;
  }, []);

  const carouselCustomColor = useMemo(() => {
    return {
      active: colors.text,
      inActive: colors.grey2,
    };
  }, [colors]);

  const clearViewStyle = useMemo(() => {
    return [
      styles.clearView,
      (((price_range?.min === 0 || price_range?.min > 0) && price_range?.max) ||
        matchingBrands?.length > 0 ||
        rating) &&
        styles.selectClearView,
    ];
  }, [
    styles.clearView,
    styles.selectClearView,
    price_range?.min,
    price_range?.max,
    matchingBrands?.length,
    rating,
  ]);

  const handleScroll = useCallback(event => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setShowScrollToTopButton(
      offsetY > 0 && offsetY < previousOffsetY.current ? true : false,
    );
    setTimeout(() => {
      previousOffsetY.current = offsetY;
    }, 300);
  }, []);

  const memoizedRenderHtml = useMemo(() => renderHtml, [sortDescription]);

  const hasMatchingBrands = useMemo(
    () => matchingBrands?.length > 0,
    [matchingBrands?.length],
  );
  const hasPriceRange = useMemo(
    () => (price_range?.min === 0 || price_range?.min > 0) && price_range?.max,
    [price_range?.min, price_range?.max],
  );
  const shouldShowReadMore = useMemo(
    () => sortDescription?.length > 80,
    [sortDescription?.length],
  );
  const listHeaderComponent = useMemo(() => {
    return (
      <View style={styles.headerList}>
        <View onLayout={handleLayout}>
          <ErrorHandler
            componentName={`${TAG} ReanimatedCarousel`}
            onErrorComponent={<View />}>
            <FlatListCarousel
              data={carouselData}
              height="small"
              loop
              carouselHeight={!!viewHeight && viewHeight}
              customColor={carouselCustomColor}
              autoPlay
              carouselCardItemStyle={styles.carouselCardItemStyle}
              carouselStyle={styles.sliderSubItemView}
              bannerImgStyle={styles.sliderSubItemView}
              pagination={viewHeight > 0}
              paginationType="capsule"
              screenName={'Category Page'}
            />
          </ErrorHandler>
        </View>
        <View style={styles.collapsedText}>
          <Label
            text={pageTitle}
            size="m"
            fontFamily="SemiBold"
            color="text"
            weight="600"
          />
          {sortDescription && <Spacer size="s" />}
          <ErrorHandler
            componentName={`${TAG} RenderHTML`}
            onErrorComponent={<View />}>
            {memoizedRenderHtml}
          </ErrorHandler>
          {shouldShowReadMore ? (
            <>
              <Spacer size="s" />
              <TouchableOpacity
                style={styles.filtersListRow}
                onPress={handleFaqModalToggle}>
                <Label
                  text={t('faqs.readMore')}
                  size="mx"
                  weight="500"
                  color="categoryTitle"
                  style={styles.readTxt}
                />
                <Spacer size="xm" type="Horizontal" />
                <ImageIcon
                  icon="arrowRight"
                  size="xxl"
                  tintColor="categoryTitle"
                />
              </TouchableOpacity>
            </>
          ) : null}
        </View>

        <Spacer size="m" />
        <View style={clearViewStyle}>
          {hasPriceRange || hasMatchingBrands || rating ? (
            <Button
              labelStyle={styles.clearText}
              onPress={resetAllFilters}
              ghost
              text={t('buttons.clear')}
            />
          ) : (
            <View />
          )}

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            keyboardShouldPersistTaps="always">
            <View style={styles.filtersListRow}>
              {matchingBrands?.length > 0 ? (
                matchingBrands.map(
                  (brand: CategoryFiltersOptions, index: number) => (
                    <View style={styles.filterItemBetweenSpace} key={index}>
                      <ErrorHandler
                        componentName={`${TAG} Tag`}
                        onErrorComponent={<View />}>
                        <Tag
                          onPress={removeFilter.bind(
                            null,
                            'brand_ids',
                            brand.value,
                          )}
                          label={brand?.label}
                          labelStyle={styles.filterItemLabel}
                          style={styles.filterItemTag}
                          isRightIcon
                          icon="cancel"
                          iconSize="xms"
                          tintColor="categoryTitle"
                          color="transparentColor"
                          borderColor="categoryTitle"
                        />
                      </ErrorHandler>
                    </View>
                  ),
                )
              ) : (
                <View />
              )}
              {(price_range?.min === 0 || price_range?.min > 0) &&
              price_range?.max ? (
                <View style={styles.filterItemBetweenSpace}>
                  <ErrorHandler
                    componentName={`${TAG} Tag`}
                    onErrorComponent={<View />}>
                    <Tag
                      label={`₹ ${price_range?.min} - ₹ ${price_range?.max}`}
                      labelStyle={styles.filterItemLabel}
                      style={styles.filterItemTag}
                      isRightIcon
                      icon="cancel"
                      iconSize="xms"
                      tintColor="categoryTitle"
                      color="transparentColor"
                      borderColor="categoryTitle"
                      onPress={removeFilter.bind(null, 'price_range')}
                    />
                  </ErrorHandler>
                </View>
              ) : (
                <View />
              )}
              {rating ? ( // Check if rating filter is applied
                <View style={styles.filterItemBetweenSpace}>
                  <Tag
                    label={`${t('reviewRating.rating')} ${rating}${
                      plusSign ? '+' : ''
                    }`}
                    labelStyle={styles.filterItemLabel}
                    style={styles.filterItemTag}
                    isRightIcon
                    icon="cancel"
                    iconSize="xms"
                    tintColor="categoryTitle"
                    color="transparentColor"
                    borderColor="categoryTitle"
                    onPress={removeFilter.bind(null, 'rating')}
                  />
                </View>
              ) : (
                <View />
              )}
            </View>
          </ScrollView>
        </View>
      </View>
    );
  }, [
    styles.headerList,
    styles.carouselCardItemStyle,
    styles.sliderSubItemView,
    styles.collapsedText,
    styles.filtersListRow,
    styles.readTxt,
    styles.clearText,
    styles.filterItemBetweenSpace,
    styles.filterItemLabel,
    styles.filterItemTag,
    handleLayout,
    carouselData,
    viewHeight,
    carouselCustomColor,
    pageTitle,
    memoizedRenderHtml,
    shouldShowReadMore,
    handleFaqModalToggle,
    clearViewStyle,
    hasPriceRange,
    hasMatchingBrands,
    rating,
    resetAllFilters,
    matchingBrands,
    price_range?.min,
    price_range?.max,
    removeFilter,
    plusSign,
  ]);

  return (
    <>
      <View style={styles.container}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <Header
            hadarLogo={false}
            backButton
            customIcon={true}
            navigation={navigation}
            text={pageTitle || ''}
            subTitle={productCount ? `${productCount} Items` : ''}
            searchIcon
            heartIcon
            bagIcon
            useInsets={true}
            bottomShadow
          />
        </ErrorHandler>
        <Spacer size="xm" />
        {loading ? (
          <CategoryDetailLoader />
        ) : (
          <KeyboardAvoidingView
            style={styles.flex}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
            <View style={styles.listContainer}>
              {showScrollToTopButton && (
                <TouchableOpacity
                  style={styles.topIconView}
                  activeOpacity={0.7}
                  onPress={() => {
                    scrollToTop();
                  }}>
                  <ImageIcon icon="arrowTop" tintColor="background" size="mx" />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    text={t('otherText.gotoTop')}
                    size="m"
                    weight="500"
                    color="whiteColor"
                    style={styles.goTop}
                  />
                </TouchableOpacity>
              )}

              <OptimizedFlatList
                ref={flatListRefTop}
                onScroll={handleScroll}
                numColumns={checkDevice() ? 4 : 2}
                keyboardShouldPersistTaps="always"
                ListHeaderComponent={listHeaderComponent}
                keyExtractor={keyExtractor}
                ItemSeparatorComponent={itemSeparator}
                data={products}
                extraData={products}
                onEndReachedThreshold={0.8}
                renderItem={renderItem}
                ListFooterComponent={renderFooter}
                ListEmptyComponent={listEmptyComponent}
                onEndReached={fetchMoreData}
                onMomentumScrollBegin={() => setIsScrolling(true)}
                onMomentumScrollEnd={() => setIsScrolling(false)}
              />
            </View>
          </KeyboardAvoidingView>
        )}
      </View>
      {!loading && !filterLoader && (
        <ErrorHandler
          componentName={`${TAG} FooterButton`}
          onErrorComponent={<View />}>
          <FooterButton
            shadowProps={true}
            useInsets={true}
            separator={verticalSeparator}
            buttons={footerButtons}
          />
        </ErrorHandler>
      )}
      {visible && (
        <ErrorHandler
          componentName={`${TAG} FilterAndSortModal`}
          onErrorComponent={<View />}>
          <FilterAndSortModal
            visible={visible}
            onClose={handleModalToggle}
            filterOptions={memoizedFilterOptions}
            appliedFilter={selectedFilters}
            applyFilters={onFilter}
            resetApplyFilters={resetAllFilters}
            modalType={modalType}
            sortType={selectedFilters?.sort_by}
            pageType="category"
            totalProduct={totalProduct}
            onChange={filter => {
              const obj = {...selectedFilters, ...filter};
              setSelectedFilters(obj);
              getProductsData(1, obj, true);
            }}
          />
        </ErrorHandler>
      )}
      {modalVisible && (
        <ErrorHandler
          componentName={`${TAG} HtmlModal`}
          onErrorComponent={<View />}>
          <HtmlModal
            visible={modalVisible}
            onClose={handleFaqModalToggle}
            title={pageTitle}
            htmlCode={sortDescription}
          />
        </ErrorHandler>
      )}
    </>
  );
};

export default React.memo(CategoryDetail);

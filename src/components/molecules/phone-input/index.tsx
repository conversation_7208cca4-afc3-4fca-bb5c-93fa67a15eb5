import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {
  TextInput,
  View,
  TextInputProps,
  Image,
  TouchableOpacity,
  Text,
} from 'react-native';
import {ImageIcon, Label, Link, Spacer, WithGradient} from 'components/atoms';
import {LinearGradientProps} from 'react-native-linear-gradient';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import {Sizes} from 'common';
import {useMemo} from 'react';


const debounce = (func: any, delay: any) => {
  let timeoutId: any;
  return (...args: any) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};

type Props = {
  testID?: string;
  placeholder?: string;
  secureTextEntry?: TextInputProps['secureTextEntry'];
  onChangeText: (text: string) => void;
  onEndEditing: (text: string) => void;
  value?: string | undefined;
  withDebounce?: boolean;
  error?: string;
  isClearIconSize?: boolean;
  textAlignVertical?: string;
  countryCode?: string;
  LinkText?: boolean;
  type?: TextInputProps['keyboardType'];
  maxLength?: TextInputProps['maxLength'];
  icon?: keyof typeof Icons | undefined;
  iconI?: keyof typeof Icons | undefined;
  clearIcon?: keyof typeof Icons | undefined;
  dropArrow?: boolean;
  editable?: TextInputProps['editable'];
  scrollEnabled?: TextInputProps['scrollEnabled'];
  onFocus?: TextInputProps['onFocus'];
  onBlur?: TextInputProps['onBlur'];
  style?: TextInputProps['style'];
  inputStyle?: TextInputProps['style'];
  searchIconStyle?: TextInputProps['style'];
  clearIconStyle?: TextInputProps['style'];
  linkStyle?: TextInputProps['style'];
  autoFocus?: TextInputProps['autoFocus'];
  multiline?: boolean;
  numberOfLine?: number;
  onClear?: () => void;
  onSearch?: () => void;
  dropArrowList?: () => void;
  defaultValue?: TextInputProps['defaultValue'];
  numberOfLines?: TextInputProps['numberOfLines'];
  gradient?: boolean;
  gradientColors?: LinearGradientProps['colors'];
  gradientAngle?: LinearGradientProps['angle'];
  gradientStyle?: LinearGradientProps['style'];
  placeholderTextColor?: string;
  tintColor?: keyof Theme['colors'];
  tintColorIconRight?: keyof Theme['colors'];
  onScroll?: TextInputProps['onScroll'];
  autoComplete?: any;
  pointerEvents?: string;
  rightIconStyle?: TextInputProps['style'];
};

const PhoneInputText = forwardRef(
  (
    {
      testID,
      placeholder,
      secureTextEntry,
      onChangeText,
      onEndEditing,
      isClearIconSize = false,
      value,
      withDebounce = false,
      error,
      countryCode,
      gradient = false,
      gradientColors,
      gradientStyle,
      type = 'default',
      maxLength = undefined,
      icon,
      iconI,
      clearIcon,
      dropArrow = false,
      LinkText,
      editable = true,
      onBlur,
      onFocus,
      onClear,
      dropArrowList,
      onSearch,
      style,
      autoFocus = false,
      multiline = false,
      defaultValue,
      numberOfLine,
      searchIconStyle,
      clearIconStyle,
      numberOfLines,
      placeholderTextColor,
      inputStyle,
      textAlignVertical,
      tintColor,
      tintColorIconRight,
      autoComplete,
      scrollEnabled,
      onScroll,
      pointerEvents,
      rightIconStyle,
    }: Props,
    ref,
  ) => {
    const {colors} = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
    const debouncedTextChange = debounce(onChangeText, 500);
    // const [text, setText] = useState('');
    const inputRef = useRef<TextInput>(null);

    // useEffect(() => {
    //   setText(value);
    // }, [value]);

    useImperativeHandle(ref, () => ({
      focus: () => {
        inputRef.current?.focus();
      },
    }));

    const handleTextChange = useCallback(
      (textValue: string) => {
        // setText(textValue);
        onChangeText(textValue);
        if (withDebounce) {
          debouncedTextChange(textValue);
        } else {
          onChangeText(textValue);
        }
      },
      [withDebounce],
    );

    return (
      <>
        <View style={[styles.container, error && styles.errorBorder, style]}>
          {countryCode && (
            <View style={styles.countryCodeContainer}>
              <Label text={countryCode} />
              <Label text="-" style={styles.dashLine} />
            </View>
          )}
          <View style={styles.inputContainer}>
            {icon && (
              <TouchableOpacity onPress={onSearch}>
                <ImageIcon
                  style={[styles.icons, searchIconStyle]}
                  size="xx"
                  icon={icon}
                  tintColor={tintColor ? tintColor : undefined}
                />
              </TouchableOpacity>
            )}
            <View style={styles.gradientView}>
              <View style={styles.gradientSubView}>
                {gradient && gradientColors && (
                  <WithGradient
                    gradientStyle={gradientStyle}
                    gradientColors={gradientColors}>
                    {iconI && (
                      <Image
                        style={[styles.icons, searchIconStyle]}
                        source={Icons[iconI]}
                      />
                    )}
                  </WithGradient>
                )}
                <TextInput
                
                  testID={testID}
                  ref={inputRef}
                  onScroll={onScroll}
                  scrollEnabled={scrollEnabled}
                  autoComplete={autoComplete}
                  editable={editable}
                  placeholderTextColor={
                    placeholderTextColor ? placeholderTextColor : colors.text2
                  }
                  selectionColor={colors.text2}
                  style={[
                    styles.input,
                    numberOfLine ? {height: Sizes.xxl * numberOfLine} : {},
                    inputStyle,
                  ]}
                  keyboardType={type}
                  maxLength={maxLength}
                  placeholder={placeholder}
                  onChangeText={handleTextChange}
                  onEndEditing={onEndEditing}
                  value={value?.trimStart() || ''}
                  secureTextEntry={secureTextEntry}
                  onBlur={onBlur}
                  onFocus={onFocus}
                  pointerEvents={pointerEvents}
                  autoFocus={autoFocus}
                  multiline={multiline}
                  defaultValue={defaultValue}
                  numberOfLines={numberOfLines}
                  textAlignVertical={textAlignVertical}
                  allowFontScaling={false}
                />
              </View>
              {clearIcon && (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={onClear}
                  style={[styles.marginL2, clearIconStyle]}>
                  <ImageIcon
                    size={isClearIconSize == true ? 'xl' : 'xxl'}
                    tintColor={
                      tintColorIconRight ? tintColorIconRight : undefined
                    }
                    icon={clearIcon}
                    style={rightIconStyle}
                  />
                </TouchableOpacity>
              )}
            </View>
            {dropArrow && (
              <TouchableOpacity
                onPress={dropArrowList}
                style={[styles.marginL2, clearIconStyle]}>
                <ImageIcon tintColor="textLight" icon="upArrowIcon" />
              </TouchableOpacity>
            )}
            {LinkText && (
              <View style={styles.marginL2}>
                <Link text={LinkText} />
              </View>
            )}
          </View>
        </View>
        {error && (
          <View style={styles.errorContainer}>
            <Label color="red2" size="m" text={error} />
          </View>
        )}
      </>
    );
  },
);

export default PhoneInputText;

import {StyleSheet} from 'react-native';
import {Sizes} from 'common';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    mainContainer: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
    },
    subContainer: {
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.xsl,
      backgroundColor: colors.offWhite,
      borderRadius: Sizes.mx,
    },
    cardView: {
      paddingBottom: Sizes.s,
      paddingTop: Sizes.x5l,
      paddingLeft: Sizes.m,
      borderWidth: Sizes.x,
      borderRadius: Sizes.mx,
      borderColor: colors.skyBlue19,
      flexDirection: 'row',
    },
    miniCard: {
      paddingVertical: Sizes.xsl,
      backgroundColor: colors.offWhite1,
      paddingHorizontal: Sizes.xxxl,
      borderRadius: Sizes.mx,
      shadowColor: colors.black,
      elevation: Sizes.x,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
    },
    inputBorder: {
      borderColor: colors.grey2,
      backgroundColor: colors.whiteColor,
    },
    faqMainView: {
      backgroundColor: colors.offWhite2,
      padding: Sizes.m,
      borderRadius: Sizes.mx,
      paddingBottom: Sizes.s,
      marginBottom: Sizes.xl,
    },
    commonBackground: {
      position: 'absolute',
      width: '100%',
      height: '100%',
    },
    commonDentalKartText: {
      flexDirection: 'row',
      alignSelf: 'center',
    },
    dentalKartIcon: {
      width: Sizes.exl,
      alignSelf: 'center',
      marginLeft: Sizes.s,
    },
    heading: {
      width: '100%',
      paddingHorizontal: Sizes.l,
      alignSelf: 'center',
    },
    text: {
      paddingHorizontal: Sizes.m,
    },
    label: {
      textTransform: 'capitalize',
    },
    registerLabel: {
      paddingVertical: Sizes.sx,
    },
    termAndConditionModal: {
      borderTopWidth: 0,
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
      padding: Sizes.xm,
      borderBottomRightRadius: Sizes.m,
      borderBottomLeftRadius: Sizes.m,
    },
    sellerView: {
      flex: Sizes.x,
      backgroundColor: colors.offWhite1,
      borderRadius: Sizes.mx,
    },
    sellerImage: {
      width: Sizes.exl,
      height: Sizes.exl,
    },
    imageContainer: {
      width: Sizes.ex2l,
      height: Sizes.ex2l,
      alignSelf: 'center',
    },
    videoView: {
      width: Sizes.ex2l + Sizes.x70,
      height: Sizes.ex1,
      alignSelf: 'center',
      borderRadius: Sizes.m,
    },
    comma: {
      bottom: Sizes.xxl,
    },
    imageCrossIcon: {
      position: 'absolute',
      right: 0,
      top: 0,
    },
    form: {
      borderRadius: Sizes.mx,
      flex: Sizes.x,
      backgroundColor: colors.offWhite1,
      paddingVertical: Sizes.xl,
      paddingHorizontal: Sizes.xx,
    },
    regButtonView: {
      flexDirection: 'row',
    },
    regButton: {
      borderRadius: Sizes.xms,
      height: Sizes.xx4l,
      width: Sizes.ex124,
      alignItems: 'center',
      justifyContent: 'center',
    },
    regBtn1: {
      height: Sizes.x7l,
      width: 201,
    },
    terms: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: Sizes.xms,
      borderTopLeftRadius: Sizes.m,
      borderTopRightRadius: Sizes.m,
      paddingHorizontal: Sizes.xsl,
    },
    storyView: {
      paddingBottom: Sizes.xl,
      paddingTop: Sizes.m,
      borderBottomLeftRadius: Sizes.mx,
      borderBottomRightRadius: Sizes.m,
    },
    storyImageView: {
      alignItems: 'center',
      paddingHorizontal: Sizes.xxxl,
    },
    storyHeading: {
      paddingVertical: Sizes.mx,
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
    },
    storyViewContent: {
      alignSelf: 'center',
      paddingRight: Sizes.x70 + Sizes.s,
      paddingLeft: Sizes.x7l,
      flexDirection: 'row',
    },
    regButtonForm: {
      borderRadius: Sizes.xms,
      paddingVertical: Sizes.mx,
      paddingHorizontal: Sizes.x52 + Sizes.sx,
    },
    regButtonCatalogue: {
      borderRadius: Sizes.xms,
      height: Sizes.x7l,
      width: 205,
      justifyContent: 'center',
      alignItems: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    doubleFlex: {
      flex: Sizes.xs,
    },
    rowDirection: {
      flexDirection: 'row',
    },
    columnDirection: {
      flexDirection: 'column',
    },
    iosInput: {
      height: Sizes.x7l,
    },

    // modal Style................
    modalStyle: {
      margin: 0,
    },
    modalBackground: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.blackTransparent, // Semi-transparent background
    },
    selectedCatalogue: {
      borderRadius: Sizes.sx,
      marginRight: Sizes.xm,
      width: Sizes.x70,
      height: Sizes.x60,
    },
    cardRadius: {
      borderRadius: Sizes.mx,
    },
    thankYouImage: {
      width: Sizes.ex1 + Sizes.xl,
      height: Sizes.ex1,
      alignSelf: 'center',
    },
    cardImage: {
      width: Sizes.ex1,
      height: Sizes.ex1,
      alignSelf: 'flex-end',
    },
    modalContainer: {
      padding: Sizes.mx,
      width: '90%',
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
    },
    cardDes: {
      fontSize: 9,
    },
  });

export default styles;

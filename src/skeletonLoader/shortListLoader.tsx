import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ShortListLoader = () => {
  const {colors} = useTheme();
  const boxWidth = (width - 30) / 2;
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={2030}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="10" rx="5" ry="5" width={width - 20} height="140" />
        <Rect x={10} y="160" rx="5" ry="5" width={190} height="50" />

        <Rect x={10} y="220" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="220"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />

        <Rect x={10} y="580" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="580"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />

        <Rect x={10} y="940" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="940"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />

        <Rect x={10} y="1300" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="1300"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />

        <Rect x={10} y="1660" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="1660"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
      </ContentLoader>
    </ScrollView>
  );
};

export default ShortListLoader;

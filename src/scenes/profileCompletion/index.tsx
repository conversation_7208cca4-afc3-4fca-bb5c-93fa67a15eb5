import React, {use<PERSON><PERSON>back, useEffect, useState} from 'react';
import {
  View,
  // SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {RouteProp, useTheme} from '@react-navigation/native';
import {Formik} from 'formik';
import * as Yup from 'yup';
import {
  TextInputBox,
  PhoneInputText,
  Button,
  ConnectWithUs,
} from 'components/molecules';
import {
  Spacer,
  Label,
  CheckBox,
  ImageIcon,
  DropDown,
  Link,
  WebViewModal,
} from 'components/atoms';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
import {useMemo} from 'react';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {t} from 'i18next';
import {speciality} from 'staticData';
import ErrorHandler from 'utils/ErrorHandler';
import {userLogin} from 'services/auth';
import {SafeAreaView} from 'react-native-safe-area-context';

import {CommonModal} from 'components/atoms';
import {Sizes} from 'common';
import {truncateText} from 'utils/utils';
import { useDispatch,useSelector} from 'react-redux';
import {RootState} from '@types/local';
import { getUserInfo } from 'app-redux-store/slice/appSlice';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';
import { privacyPolicy, termsAndConditions } from 'config/environment';
type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'RegisterUser'>;
};

const ProfileCompletionSchema = Yup.object().shape({
  fullName: Yup.string()
    .required('Enter your Full Name'),
    // .test(
    //   'has-first-last',
    //   'Please enter both first and last name',
    //   function (value) {
    //     if (!value) return false;
    //     const trimmedValue = value.trim();
    //     const names = trimmedValue.split(/\s+/).filter(name => name.length > 0);
    //     return names.length >= 2;
    //   },
    // ),
  phone: Yup.string().required('Enter your Phone Number'),
  // state: Yup.object().required('Enter State').optional(),
  registrationId: Yup.string()
    .required('Enter your Registration ID')
    .optional(),
  speciality: Yup.object().nullable().required('Select Speciality'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    // .matches(
    //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/,
    //   'Must contain uppercase, lowercase, number and special character',
    // )
    .required('Enter your Password'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Enter your Email'),
  confirmPassword: Yup.string()
    .required('Enter your Confirm Password')
    .oneOf([Yup.ref('password'), null], 'Password must match'),
  agreeCheckbox: Yup.boolean().oneOf(
    [true],
    'You must agree to the terms and conditions',
  ),
});

const ProfileCompletionScene = ({navigation, route}: Props) => {
  const {colors} = useTheme();
  const TAG = 'ProfileCompletionScene';
  const dispatch = useDispatch();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);
  const [showOtpScene, setShowOtpScene] = useState(false);
  const [registrationField, setRegistrationField] = useState(false);
  const [registrationState, setRegistrationState] = useState(false);
  const [referral, setReferral] = useState(false);
  const [referralModal, setReferralModal] = useState(false);
  const [modalText, setModalText] = useState('');
  const [termsModal, setTermsModal] = useState(false);
  const [policyModal, setPolicyModal] = useState(false);
  
  const handleTermsModalClose = useCallback(() => {
    setTermsModal(false);
  }, []);
  const handlePolicyModalClose = useCallback(() => {
    setPolicyModal(false);
  }, []);
  const emailPhoneNumber = route?.params?.phoneNumber;
  const customerId = route?.params?.customerId;
  const handleSubmit = async (values: any) => {
    const {data, status} = await userLogin({
      recipient: values?.email,
      action: 'email_update',
      authentication_type: 'email',
    });
    if (data && status) {
      setShowOtpScene(true);
      navigation.navigate('EmailVerification', {back: true, values});
    }
  };

  const onPressRegistrationShow = () => {
    setRegistrationField(true);
  };
  const onPressRegistrationState = () => {
    setRegistrationState(true);
  };
  const addReferral = () => {
    setReferral(true);
  };

  const renderItem = (item, selected) => {
    return (
      <View style={styles.dropDownItem}>
        <Label color="text2" text={item.label} size="mx" fontFamily="Medium" />
      </View>
    );
  };
  const {countryListings} = useSelector((state: RootState) => state.app);
  const transformedStates = useMemo(() => {
    const list = countryListings.find(item => item.country_id === 'IN');

    if (!list || !list.states) {
      return [];
    }

    // Transform the states array to match dropdown format
    const transformed = list.states.map(
      ({state_name}: {state_name: string}) => ({
        label: state_name,
        value: state_name,
        id: state_name,
      }),
    );

    // Add "Other" option at the end if needed
    transformed.push({
      label: 'Other',
      value: 'Other',
      id: 'Other',
    });

    return transformed;
  }, [countryListings]);

  const onErrorView = (error: string) => {
    return (
      <>
        <Spacer size="sx" />
        <Label
          text={String(t(error) || '')}
          size="m"
          weight="400"
          color="red3"
        />
      </>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidView}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollViewContent}
          bounces={false}
          keyboardShouldPersistTaps="handled">
          <Formik
            initialValues={{
              fullName: '',
              phone: emailPhoneNumber || '',
              state: null,
              registrationId: '',
              speciality: null,
              password: '',
              referral: '',
              email: '',
              confirmPassword: '',
              agreeCheckbox: true,
            }}
            validationSchema={ProfileCompletionSchema}
            onSubmit={handleSubmit}>
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              setFieldValue,
              errors,
              touched,
            }) => (
              <>
                <View style={styles.logoContainer}>
                  <ImageIcon style={styles.logoView} icon="dentalKartFull" />
                  <Spacer size="s" />
                </View>
                <View style={styles.formContainer}>
                  <View style={styles.headingContainer}>
                    <Label
                      text={t('profileCompletion.complete')}
                      size="mx"
                      color="text"
                      style={styles.headerText}
                      textTransform="capitalize"
                    />
                    <Label
                      text={t('profileCompletion.yourProfile')}
                      size="mx"
                      color="text"
                      style={styles.headerText}
                      weight="600"
                    />
                    <Label
                      text={t('profileCompletion.and')}
                      size="mx"
                      color="text"
                      style={styles.headerText}
                    />
                    <Label
                      text={t('profileCompletion.personalizedOffers')}
                      size="mx"
                      color="text"
                      style={styles.headerText}
                      weight="600"
                    />
                  </View>
                  <Spacer size="xm" />
                  <TextInputBox
                    testID="txtAddressFullName"
                    label={t('manageAddress.fullName')}
                    containerStyle={{color: colors.text}}
                    onChangeText={handleChange('fullName')}
                    onBlur={handleBlur('fullName')}
                    value={values?.fullName}
                    error={touched.fullName && !!errors?.fullName}
                    errorText={touched.fullName && errors?.fullName}
                    returnKeyType="next"
                    leftIcon={Icons.user}
                    allowOnlyAlphabets
                  />

                  <Spacer size="xm" />
                  <View style={styles.parent}>
                    <TextInputBox
                      testID="txtTelePhoneNumber"
                      label={t('manageAddress.telephoneNumber')}
                      editable={false}
                      containerStyle={{color: colors.text, fontWeight: '500'}}
                      onChangeText={handleChange('phone')}
                      value={values?.phone}
                      error={touched.phone && !!errors?.phone}
                      errorText={touched.phone && errors?.phone}
                      returnKeyType="next"
                      maxLength={10}
                      keyboardType="phone-pad"
                      leftIcon={Icons.phone1}
                    />

                    <View style={styles.verifiedTag}>
                      <ImageIcon size="m" icon="checkGreen" />
                      <Label text="Verified" size="m" color="green" />
                    </View>
                  </View>

                  <Spacer size="xm" />
                  <View style={styles.fieldContainer}>
                    {!registrationField && (
                      <View
                        style={{flexDirection: 'row', alignItems: 'center'}}>
                        <TouchableOpacity
                          style={styles.expandField}
                          onPress={onPressRegistrationShow}>
                          <ImageIcon
                            size="l"
                            icon="plus"
                            tintColor="categoryTitle"
                          />
                          <Label
                            color="skyBlue23"
                            text={t('profileCompletion.registrationId')}
                            size="m"
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => {
                            setModalText(
                              t('profileCompletion.registrationMessage'),
                            );
                            setReferralModal(true);
                          }}>
                          <ImageIcon
                            size="xl"
                            tintColor="categoryTitle"
                            icon="infoCircle"
                          />
                        </TouchableOpacity>
                      </View>
                    )}
                    {!registrationState && (
                      <View
                        style={{flexDirection: 'row', alignItems: 'center'}}>
                        <TouchableOpacity
                          style={styles.expandField}
                          onPress={onPressRegistrationState}>
                          <ImageIcon
                            size="l"
                            icon="plus"
                            tintColor="categoryTitle"
                          />
                          <Label
                            text={t('profileCompletion.registrationState')}
                            size="m"
                            color="skyBlue23"
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => {
                            setModalText(t('profileCompletion.stateMessage'));
                            setReferralModal(true);
                          }}>
                          <ImageIcon
                            size="xl"
                            tintColor="categoryTitle"
                            icon="infoCircle"
                          />
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      flex: 1,
                      gap: 6,
                      alignItems: 'center',
                    }}>
                    {registrationField && (
                      <View style={{flex: 1}}>
                        <TextInputBox
                          testID="txtRegistration"
                          label="Registration ID"
                          value={values.registrationId}
                          containerStyle={{color: colors.text}}
                          onChangeText={handleChange('registrationId')}
                          onBlur={handleBlur('registrationId')}
                          leftIcon={Icons.location1}
                          error={
                            touched.registrationId && !!errors?.registrationId
                          }
                          errorText={
                            touched.registrationId && errors?.registrationId
                          }
                        />
                        {(!registrationField ||
                          values.registrationId?.length === 0) && (
                          <TouchableOpacity
                            style={styles.closeIcons}
                            onPress={() => setRegistrationField(false)}>
                            <ImageIcon
                              size="l"
                              tintColor="text"
                              icon="closeIcons"
                              resizeMode="contain"
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    )}
                    {registrationState && (
                      <>
                        <View style={{flex: 1, marginBottom: Sizes.xs}}>
                          {/* <Spacer size="sx" /> */}
                          <DropDown
                            testID="txtRegistrationState"
                            heading={
                              values.state
                                ? t('profileCompletion.registrationState')
                                : ''
                            }
                            selectedTextStyle={styles.dropDownTextStyle}
                            itemTextStyle={styles.dropDownTextStyle}
                            styleDropDown={styles.styleDropDown}
                            value={values.state}
                            onChange={item => setFieldValue('state', item)}
                            renderItem={renderItem}
                            returnValueOnly={false}
                            data={transformedStates}
                            search={true}
                            placeholder={t(
                              'profileCompletion.registrationState',
                            )}
                            placeholderStyle={styles.placeTextStyle}
                            labelField="label"
                            valueField="value"
                            text={String(errors?.state || '')}
                            renderRightIcon={(focus: boolean) => (
                              <ImageIcon
                                size="xl"
                                tintColor="text"
                                icon="arrowUp"
                                resizeMode="contain"
                                style={focus && styles.downImg}
                              />
                            )}
                          />
                          {!values.registrationState &&
                            values.state == null && (
                              <TouchableOpacity
                                style={styles.closeIcons}
                                onPress={() => setRegistrationState(false)}>
                                <ImageIcon
                                  size="l"
                                  tintColor="text"
                                  icon="closeIcons"
                                  resizeMode="contain"
                                />
                              </TouchableOpacity>
                            )}
                          {touched.state &&
                            errors?.state &&
                            onErrorView(errors?.state || '')}
                        </View>
                      </>
                    )}
                  </View>
                  <Spacer size="xm" />

                  <DropDown
                    testID="txtSpeciality"
                    heading={
                      values.speciality ? t(t('profile.speciality')) : ''
                    }
                    selectedTextStyle={styles.inputTextView}
                    itemTextStyle={styles.dropDownTextStyle}
                    styleDropDown={styles.styleDropDown}
                    value={values.speciality}
                    onChange={item => setFieldValue('speciality', item)}
                    renderItem={renderItem}
                    returnValueOnly={false}
                    placeholderStyle={styles.placeTextStyle}
                    data={speciality}
                    search={false}
                    placeholder={t('profile.speciality')}
                    labelField="label"
                    valueField="value"
                    text={String(errors?.speciality || '')}
                    renderRightIcon={(focus: boolean) => (
                      <ImageIcon
                        size="xl"
                        tintColor="text"
                        icon="arrowUp"
                        resizeMode="contain"
                        style={focus && styles.downImg}
                      />
                    )}
                    renderLeftIcon={() => (
                      <ImageIcon
                        size="xl"
                        tintColor="text"
                        icon="plus1"
                        resizeMode="contain"
                        style={styles.dropDownLeft}
                      />
                    )}
                  />
                  {touched.speciality &&
                    errors?.speciality &&
                    onErrorView(errors?.speciality || '')}
                  <Spacer size="xm" />
                  <TextInputBox
                    testID="txtEmail"
                    label={'Enter Email'}
                    containerStyle={{color: colors.text}}
                    onChangeText={handleChange('email')}
                    onBlur={handleBlur('email')}
                    value={values?.email}
                    error={touched.email && !!errors?.email}
                    errorText={touched.email && errors?.email}
                    returnKeyType="next"
                    leftIcon={Icons.email}
                  />
                  <Spacer size="sx" />

                  <TextInputBox
                    testID="txtPassword"
                    label="Create Password"
                    value={values.password}
                    error={touched.password && !!errors?.password}
                    errorText={touched.password && errors?.password}
                    containerStyle={{color: colors.text}}
                    onChangeText={handleChange('password')}
                    onBlur={handleBlur('password')}
                    leftIcon={Icons.lock}
                    returnKeyType="next"
                    secureTextEntry={!isPasswordVisible}
                    rightIcon={
                      !isPasswordVisible ? Icons.eyeHideIcon : Icons.eyeShowIcon
                    }
                    onRightIconPress={() =>
                      isPasswordVisible
                        ? setIsPasswordVisible(false)
                        : setIsPasswordVisible(true)
                    }
                  />
                  <Spacer size="xms" />

                  <TextInputBox
                    testID="txtCPassword"
                    label="Confirm Password"
                    value={values.confirmPassword}
                    containerStyle={{color: colors.text}}
                    onChangeText={handleChange('confirmPassword')}
                    onBlur={handleBlur('password')}
                    error={touched.confirmPassword && !!errors?.confirmPassword}
                    errorText={
                      touched.confirmPassword && errors?.confirmPassword
                    }
                    leftIcon={Icons.lock}
                    returnKeyType="done"
                    secureTextEntry={!isConfirmPasswordVisible}
                    rightIcon={
                      !isConfirmPasswordVisible
                        ? Icons.eyeHideIcon
                        : Icons.eyeShowIcon
                    }
                    onRightIconPress={() =>
                      isConfirmPasswordVisible
                        ? setIsConfirmPasswordVisible(false)
                        : setIsConfirmPasswordVisible(true)
                    }
                  />
                  <Spacer size="s" />
                  {/* <Label
                    text={t('profileCompletion.validationMessage')}
                    size="xms"
                    color="text2"
                    style={styles.passwordHelp} /> */}

                  {referral && (
                    <>
                      <TextInputBox
                        label="Referral Code"
                        value={values.referral}
                        containerStyle={{color: colors.text}}
                        onChangeText={handleChange('referral')}
                        onBlur={handleBlur('referral')}
                        error={touched.referral && !!errors?.referral}
                        errorText={touched.referral && errors?.referral}
                      />
                    </>
                  )}
                  <View style={styles.checkboxContainer}>
                    <CheckBox
                      selected={values.agreeCheckbox}
                      onValueChange={() =>
                        setFieldValue('agreeCheckbox', !values.agreeCheckbox)
                      }
                      style={styles.checkboxStyle}
                    />
                    <View style={styles.termsTextContainer}>
                      <Label
                        text={t('profileCompletion.agreeMessage1')}
                        size="m"
                        color="text"
                        weight="500"
                      />
                      <Link
                        onPress={() =>
                          setTermsModal(true)
                        }
                        isUnderlined
                        color="skyBlue23"
                        size="m"
                        text={t('login.footerTermService')}
                      />
                      <Label
                        text={t('profileCompletion.and')}
                        size="m"
                        color="text"
                        style={styles.pHor}
                        weight="500"
                      />
                      <Link
                        onPress={() =>
                         setPolicyModal(true)
                        }
                        isUnderlined
                        color="skyBlue23"
                        size="m"
                        text={t('login.footerPolicy')}
                      />
                    </View>
                  </View>
                  {touched.agreeCheckbox && errors?.agreeCheckbox && (
                    <Label
                      color="red2"
                      text={String(errors?.agreeCheckbox || '')}
                      size="m"
                      weight="400"
                    />
                  )}
                  <Spacer size="xl" />

                  <View style={styles.buttonsContainer}>
                    <Button
                      text="Skip For Now"
                      type="bordered"
                      onPress={() => {
                        trackEvent('COMPLETE_REGISTRATION',{
                          registrationId: customerId,
                          // params: { cart_id: data?.cart?.cart_id }
                        })
                        dispatch(getUserInfo());
                        navigation.navigate('Tab', {screen: 'Shop'})
                      }}
                      style={[
                        styles.skipButton,
                        {
                          borderColor: !values.agreeCheckbox
                            ? colors.lightgrey
                            : colors.categoryTitle,
                        },
                      ]}
                      labelColor={
                        !values.agreeCheckbox ? 'lightgrey' : 'categoryTitle'
                      }
                      disabled={!values.agreeCheckbox}
                    />
                    <Spacer size="s" />
                    <Button
                      text="Complete Profile"
                      type="primary"
                      onPress={handleSubmit}
                      labelColor="background"
                      disabled={!values.agreeCheckbox}
                      style={[
                        {
                          backgroundColor: colors.categoryTitle,
                        },
                        styles.completeButton,
                        {
                          backgroundColor: !values.agreeCheckbox
                            ? colors.lightgrey
                            : colors.categoryTitle,
                        },
                      ]}
                    />
                  </View>
                </View>
                <View style={styles.helpContainer}>
                  <View style={styles.footerContainer}>
                    {!referral && (
                      <View style={styles.referralContainer}>
                        <TouchableOpacity
                          style={styles.footerContainer}
                          onPress={addReferral}>
                          <Label
                            text={t('profileCompletion.referral')}
                            size="m"
                            color="skyBlue23"
                            weight="500"
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={styles.footerContainer}
                          onPress={() => {
                            setModalText(
                              t('profileCompletion.referralMessage'),
                            );
                            setReferralModal(true);
                          }}>
                          <ImageIcon
                            size="xl"
                            tintColor="skyBlue23"
                            icon="infoCircle"
                          />
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </View>
                <Spacer type="Vertical" size="sx" />
                <ConnectWithUs />
              </>
            )}
          </Formik>

          <Spacer size="xl" />

          {referralModal && (
            <ErrorHandler
              componentName={`${TAG} ReferralModal`}
              onErrorComponent={<View />}>
              <CommonModal
                setIsShowFooterModal={setReferralModal}
                visible={referralModal}
                modalText={modalText}
                onClose={() => setReferralModal(false)}
              />
            </ErrorHandler>
          )}
          {termsModal && (
            <ErrorHandler
              componentName={`${TAG} WebViewModal`}
              onErrorComponent={<View />}>
              <WebViewModal
                visible={termsModal}
                url={termsAndConditions}
                onClose={handleTermsModalClose}
              />
            </ErrorHandler>
          )}
          {policyModal && (
            <ErrorHandler
              componentName={`${TAG} WebViewModal`}
              onErrorComponent={<View />}>
              <WebViewModal
                visible={policyModal}
                url={privacyPolicy}
                onClose={handlePolicyModalClose}
              />
            </ErrorHandler>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ProfileCompletionScene;

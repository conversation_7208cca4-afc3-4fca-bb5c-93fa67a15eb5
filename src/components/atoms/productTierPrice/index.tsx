import React from 'react';
import {View, TouchableOpacity, FlatList} from 'react-native';
import {Label, ImageIcon, Spacer, Separator} from 'components/atoms';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

interface TierPrice {
  text: string;
  qty: number;
  qtyLimit: number;
  savings: number;
  active: boolean;
}

interface TierPricingDetailsProps {
  transformedTierPrices: string;
  totalTierAmount: number;
  totalOriginalAmount: number;
  totalItemCount: number;
  tierPriceIndex: number;
  setTierPriceIndex: (index: number) => void;
  onLayout?: any;
  setReturnInfoModel: (value: boolean) => void;
  setInfoIcon: (icon: string) => void;
  findFirstActiveIndex: (prices: any) => string | undefined;
}

const TierPricingDetails: React.FC<TierPricingDetailsProps> = ({
  transformedTierPrices,
  totalTierAmount,
  totalOriginalAmount,
  totalItemCount,
  tierPriceIndex,
  setTierPriceIndex,
  setReturnInfoModel,
  setInfoIcon,
  findFirstActiveIndex,
  onLayout,
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={styles.subProductNameView} onLayout={onLayout}>
      <Spacer size="l" />
      {transformedTierPrices &&
      Object.values(transformedTierPrices).length > 0 ? (
        <View style={styles.optionalView}>
          <View style={styles.transformedTierView}>
            <Label text={t('PDP.subtotal')} color="text2" size="l" weight="500">
              <Label
                text={`${t('PDP.rupee')} ${totalTierAmount.toFixed(2)}`}
                color="text"
                size="l"
                weight="600"
              />
            </Label>
            <Separator color="grey2" Vertical style={styles.seperator} />

            <Label
              weight="600"
              color="categoryTitle"
              size="mx"
              text={`${t('PDP.item')} - ${totalItemCount}`}
            />
          </View>
          <View style={styles.flexRow}>
            <Label
              text={`${t('PDP.mrp')} - ${t('otherText.rupee')}`}
              color="grey3"
              size="mx"
              weight="500">
              <Label
                text={totalOriginalAmount}
                color="grey3"
                size="mx"
                textDecorationLine="line-through"
                weight="500"
              />
              <Label
                text={` ${
                  isNaN(
                    (
                      100 -
                      (totalTierAmount * 100) / totalOriginalAmount
                    ).toFixed(2),
                  )
                    ? '0% OFF'
                    : (
                        100 -
                        (totalTierAmount * 100) / totalOriginalAmount
                      ).toFixed(2) + '% OFF'
                }`}
                color="green2"
                size="mx"
                weight="500"
              />
            </Label>
            <Spacer size="s" type="Horizontal" />
            <TouchableOpacity
              style={styles.infoIcon}
              onPress={() => {
                setReturnInfoModel(true);
                setInfoIcon('priceDetails');
              }}>
              <ImageIcon size="xl" icon="infoCircle" />
            </TouchableOpacity>
          </View>

          <Spacer size="xs" />
          <View>
            {Object.keys(transformedTierPrices)?.length > 0 ? (
              <>
                <Spacer size="s" />
                <FlatList
                  data={Object.keys(transformedTierPrices)}
                  horizontal
                  // ItemSeparatorComponent={() => (
                  //   <Spacer size="xm" type="Horizontal" />
                  // )}
                  renderItem={({item}) => {
                    const isActiveRow = transformedTierPrices?.[
                      item
                    ]?.rows?.some(j => j.active === true);

                    return isActiveRow ? (
                      <TouchableOpacity
                        onPress={() => {
                          setTierPriceIndex(item);
                        }}>
                        <View
                          style={[
                            styles.tierPrices,
                            tierPriceIndex === item
                              ? {backgroundColor: colors.categoryTitle}
                              : {},
                          ]}>
                          <Label
                            weight="500"
                            color={
                              tierPriceIndex === item ? 'whiteColor' : 'text'
                            }
                            text={item?.split('_')[0]}
                            align="center"
                          />
                        </View>
                      </TouchableOpacity>
                    ) : null;
                  }}
                />
              </>
            ) : null}
            <View style={styles.listContainer}>
              <View style={styles.deliveryCont}>
                <View style={styles.thLeft}>
                  <Label
                    color="whiteColor"
                    size="m"
                    text={t('PDP.offer')}
                    weight="600"
                    textTransform="capitalize"
                  />
                </View>
                <View style={styles.thRight}>
                  <Label
                    weight="600"
                    color="whiteColor"
                    size="m"
                    text={t('PDP.addSavings')}
                    textTransform="capitalize"
                  />
                </View>
              </View>
              <View>
                {Object.keys(transformedTierPrices)?.length > 0 &&
                  transformedTierPrices?.[
                    tierPriceIndex ||
                      findFirstActiveIndex(transformedTierPrices)
                  ]?.rows?.map((item, index) => {
                    return (
                      <View style={styles.row} key={index}>
                        <View
                          style={[
                            styles.tableItemLeft,
                            item?.qty >= item.qtyLimit && totalItemCount > 0
                              ? {backgroundColor: colors.fadeOrange}
                              : {},
                          ]}>
                          <Label
                            color="text"
                            size="m"
                            textTransform="capitalize"
                            weight="500"
                            text={item.text}
                          />
                        </View>
                        <View
                          style={[
                            styles.tableItemRight,
                            item?.qty >= item?.qtyLimit && totalItemCount > 0
                              ? {backgroundColor: colors.fadeOrange}
                              : {},
                          ]}>
                          <Label
                            color="text"
                            size="m"
                            weight="500"
                            text={`${item.savings}%`}
                          />
                        </View>
                      </View>
                    );
                  })}
              </View>
            </View>
          </View>
        </View>
      ) : null}
    </View>
  );
};

export default TierPricingDetails;

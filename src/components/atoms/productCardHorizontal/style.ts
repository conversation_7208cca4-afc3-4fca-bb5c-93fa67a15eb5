import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    image: {
      width: Sizes.ex1,
      height: Sizes.ex1,
      borderRadius: Sizes.m,
    },
    tagView: {
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.s,
    },
    tagLabel: {
      color: colors.text2,
    },
    deliveryOfferRow: {
      flexDirection: 'row',
      flex: Sizes.x,
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    priceOfferContainer: {
      flex: Sizes.x,
      borderRadius: Sizes.s,
      justifyContent: 'center',
      paddingHorizontal: Sizes.sx,
      paddingVertical: Sizes.s,
    },
    pricingRow: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.m,
    },
    rowCentered: {flexDirection: 'row', alignItems: 'center'},
    productDetailRatingRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    detailSection: {flex: Sizes.x, paddingLeft: Sizes.m},
    bestSellerTag: {height: Sizes.xxl, width: Sizes.ex, marginLeft: -5},
    productImageContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    imageSection: {
      borderWidth: Sizes.x,
      borderColor: '#2053753D',
      borderRadius: Sizes.m,
    },
    container: {
      flex: Sizes.x,
      borderWidth: Sizes.x,
      borderColor: '#2053753D',
      marginVertical: Sizes.xm,
      borderRadius: Sizes.m,
      flexDirection: 'row',
      padding: Sizes.xm,
    },
  });

export default styles;

import Icons from 'common/icons';
import React from 'react';
import {TouchableOpacity, ViewProps, TextProps, View} from 'react-native';
import Label from '../label';
import stylesWithOutColor from './style';
import {ImageIcon, Spacer} from 'components/atoms';
import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';
import {useMemo} from 'react';

type Props = {
  style?: ViewProps['style'];
  labelStyle?: TextProps['style'];
  label?: any;
  onPress?: any;
  icon?: keyof typeof Icons;
  iconSize?: keyof typeof Sizes;
  tintColor?: keyof Theme['colors'];
  color?: keyof Theme['colors'];
  borderColor?: keyof Theme['colors'];
  isRightIcon?: boolean;
};
const Tag = ({
  label,
  icon,
  iconSize = 'xms',
  style,
  labelStyle,
  onPress,
  tintColor,
  color,
  isRightIcon = false,
  borderColor,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View
      style={[
        styles.container,
        style,
        color ? {backgroundColor: colors[color]} : {},
        borderColor
          ? {borderColor: colors[borderColor]}
          : {borderColor: colors[color]},
      ]}
      >
      {!isRightIcon && icon && (
        <>
          <TouchableOpacity onPress={onPress}>
            <ImageIcon
              tintColor={tintColor ? tintColor : undefined}
              icon={icon}
              size={iconSize}
            />
          </TouchableOpacity>
          <Spacer type="Horizontal" size="s" />
        </>
      )}
      <Label
        size="m"
        color="whiteColor"
        fontFamily="Regular"
        style={[labelStyle, styles.title]}
        text={label}
        textTransform="capitalize"
      />
      {isRightIcon && icon && (
        <>
          <Spacer type="Horizontal" size="s" />
          <TouchableOpacity style={styles.iconView} onPress={onPress}>
            <ImageIcon
              tintColor={tintColor ? tintColor : undefined}
              icon={icon}
              size={iconSize}
            />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

export default Tag;

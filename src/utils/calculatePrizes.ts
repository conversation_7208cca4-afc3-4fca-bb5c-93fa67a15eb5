const itemCount = (orderDetail: OrderDetailsV1) => {
  const order = orderDetail?.packages;
  return order?.reduce(
    (prev: any, curr: {items: string | any[]}) => prev + curr.items.length,
    0,
  );
};
const totalWeight = (cart: {items: any[]}) => {
  return cart?.items?.reduce(
    (prev: any, curr: {product: {weight: number}; quantity: number}) =>
      prev + curr?.product?.weight * curr?.quantity,
    0,
  );
};

const calculatePrizes = (orderDetail: OrderDetailsV1) => {
  const arr = [];
  const orderSummary = orderDetail?.order_summary;
  arr.push({
    key: 'Total item',
    value: itemCount(orderDetail),
  });
  orderSummary?.map(o => {
    arr.push({key: o.label, value: o.value, color: o.color});
  });
  return arr;
};

export default calculatePrizes;

const getShippingAmount = (address: ShippingCartAddress[]) => {
  return address?.[0]?.available_shipping_methods?.[0]?.amount?.value;
};

export const getCartTotalWithKeys = (cartData: Cart, isLoggedIn: boolean) => {
  const shippingAmount = getShippingAmount(cartData?.shipping_addresses);
  const cartPrices = cartData?.pricing_details;
  const currency = cartData?.cart_currency?.currency_symbol;
  const couponDiscount = cartPrices?.discounts?.find(
    discount => discount?.code === 'coupon',
  );
  const deliveryValue =
    cartPrices?.shipping_charges?.amount?.value === 0
      ? cartPrices?.shipping_charges?.amount?.applicable_charges
      : cartPrices?.shipping_charges?.amount?.value;
  return {
    // total_quantity: {
    //   key: 'Total Quantity',
    //   value: cartData?.total_quantity,
    //   currency: '',
    //   visibility: true,
    //   modal: false,
    //   keyColor: 'text2',
    //   valueColor: 'grey',
    //   keyWeight: '400',
    //   valueWeight: '400',
    //   borderTop: false,
    //   borderBottom: false,
    // },
    // total_weight: {
    //   key: 'Total Weight',
    //   value: cartData?.total_weight + ' Kg',
    //   currency: '',
    //   visibility: true,
    //   modal: false,
    //   keyColor: 'text2',
    //   valueColor: 'grey',
    //   keyWeight: '400',
    //   valueWeight: '400',
    //   borderTop: false,
    //   borderBottom: false,
    // },
    item_total_selling_price: {
      key: 'Item Total',
      value: cartPrices?.item_total_selling_price?.amount?.value,
      regularValue: cartPrices?.item_total_regular_price?.amount?.value,
      savedAmount: `Saved ${currency}${cartPrices?.saving_on_regular_price?.amount?.value}`,
      currency: currency,
      visibility: true,
      modal: false,
      keyColor: 'text2',
      valueColor: 'grey',
      keyWeight: '400',
      valueWeight: '400',
      borderTop: false,
      borderBottom: false,
    },
    shipping: {
      key: 'Delivery Partner Fee',
      value: deliveryValue ? deliveryValue : 0,
      deliveryValue:
        cartPrices?.shipping_charges?.amount?.applicable_charges === 0 ||
        cartPrices?.shipping_charges?.amount?.value === null
          ? 0
          : cartPrices?.shipping_charges?.amount?.value,
      currency: currency,
      modal: false,
      modal_title: '',
      visibility:
        cartPrices?.shipping_charges?.amount?.value ||
        cartPrices?.shipping_charges?.amount?.value == null ||
        cartPrices?.shipping_charges?.amount?.value === 0
          ? true
          : false,
      keyColor: 'text2',
      valueColor: 'grey',
      keyWeight: '400',
      valueWeight: '400',
      borderTop: false,
      borderBottom: false,
    },
    overweight_delivery_charges: {
      key: 'Overweight delivery charges',
      value: cartPrices?.overweight_delivery_charges?.amount?.value,
      currency: currency,
      visibility:
        cartPrices?.overweight_delivery_charges?.amount?.value > 0
          ? true
          : false,
      modal: false,
      keyColor: 'text2',
      valueColor: 'text',
      keyWeight: '400',
      valueWeight: '400',
      borderTop: false,
      borderBottom: false,
    },
    discount: {
      key: 'Coupon Discount',
      value:
        !couponDiscount || Number(couponDiscount?.amount?.value) === 0
          ? 'Apply Coupon'
          : couponDiscount?.amount?.value ?? 0,
      currency:
        !couponDiscount || Number(couponDiscount?.amount?.value) === 0
          ? ''
          : currency,
      visibility:
        !!couponDiscount || Number(couponDiscount?.amount?.value) !== 0,
      modal: false,
      keyColor: 'text2',
      valueColor: 'skyBlue5',
      keyWeight: '400',
      valueWeight: '500',
      borderTop: false,
      borderBottom: false,
    },
    // total_savings: {
    //   key: 'Total Savings',
    //   value: cartPrices?.total_savings?.amount?.value,
    //   currency: currency,
    //   modal: false,
    //   visibility: true,
    //   keyColor: 'green2',
    //   valueColor: 'green2',
    //   keyWeight: '600',
    //   valueWeight: '600',
    //   borderTop: false,
    //   borderBottom: false,
    // },
    rewardDiscount: {
      key: 'Reward Discount',
      value:
        cartPrices?.discounts.find(discount => discount?.code === 'reward')
          ?.amount?.value ?? 0,
      currency: currency,
      visibility:
        Number(
          cartPrices?.discounts?.find(discount => discount?.code === 'reward')
            ?.amount?.value,
        ) > 0
          ? true
          : false,
      modal: false,
      keyColor: 'green2',
      valueColor: 'green2',
      keyWeight: '400',
      valueWeight: '400',
      borderTop: false,
      borderBottom: false,
    },
    grand_total: {
      key: 'Grand Total',
      subTitle: 'Inclusive Of All Taxes',
      value: cartPrices?.grand_total?.amount?.value,
      currency: currency,
      visibility: true,
      modal: false,
      keyColor: 'text',
      valueColor: 'text',
      keyWeight: '600',
      valueWeight: '600',
      borderTop: true,
      borderBottom: true,
    },
  };
};

import {Fonts, Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
      marginTop: Sizes.ex,
    },
    logoView: {
      alignItems: 'center',
      paddingTop: Sizes.s,
      marginRight: Sizes.xm,
      marginBottom: -50,
    },
    body: {
      flex: Sizes.x,
    },
    otpInputContainer: {
      width: '100%',
      height: Sizes.x7l + Sizes.xs,
    },
    otpInputFocused: {
      borderColor: colors.grey2, // Keep same color as normal state
      borderWidth: 1,
      // You can add other styles you want when focused
      // For example, you might want a subtle indicator like:
      // backgroundColor: colors.inputBackgroundFocused || '#F9F9F9',
    },
    otpInput: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      color: colors.text,
      fontSize: Sizes.xl,
      fontWeight: '700',
      height: Sizes.x7l + Sizes.xs,
      backgroundColor: colors.background,
      left: Sizes.xms,
    },
    // error: {
    //   borderColor: colors.red,
    // },
    otpErrorLabel: {
      alignSelf: 'flex-start',
      paddingTop: Sizes.xs,
      left: Sizes.xms,
    },
    pincodeStyle: {
      fontSize: 18, 
      color: '#000',
      fontWeight: '600',
    },
    imageBackGroundOTP: {
      flex: Sizes.x,
      padding: Sizes.xl,
      justifyContent: 'center',
      alignItems: 'center',
    },
    scrollViewContent: {
      flexGrow: 1,
      // paddingHorizontal: Sizes.l,
      paddingTop: Sizes.x3l,
      // paddingBottom: Platform.OS === 'ios' ? Sizes.xxl : 0,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    verifyPopup: {
      position: 'absolute',
      zIndex: Sizes.xs,
      backgroundColor: colors.verifyPopupBackGround,
      height: '100%',
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    resendOtpTimer: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'center',
    },
    resendOtpText: {
      color: colors.lightDisabled,
    },
    coinImage: {
      width: Sizes.l,
      height: Sizes.l,
    },
    editLabel: {
      color: colors.newSunnyOrange,
    },
    inputSubView: {
      fontSize: Sizes.mx,
      color: colors.newPrimary,
    },
    buttonView: {
      fontSize: Sizes.mx,
      padding: Sizes.xm,
    },
    digitTxtView: {
      color: colors.text,
      fontWeight: '400',
      fontSize: Sizes.xl,
      fontFamily: Fonts.Medium,
    },
    digitView: {
      backgroundColor: colors.whiteColor,
      // width: '100%'
      // backgroundColor: 'red',
    },
    separatorView: {
      fontSize: 15,
      color: colors.lightDisabled,
      width: 5,
    },
    onResendView: {
      alignSelf: 'center',
      alignItems: 'center',
    },
    shouldShowView: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    needHelpView: {
      flex: 0.3,
      justifyContent: 'flex-end',
    },
    needSubView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    alignSelf: {
      alignSelf: 'center',
    },
    otpDes: {
      paddingHorizontal: Sizes.xs,
    },
    otpContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 0,
      justifyContent: 'center',
    },
    backView: {
      padding: Sizes.s,
      alignSelf: 'flex-end',
    },
    rAlign: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    mostTextView: {
      flex: Sizes.x,
      alignSelf: 'center',
      marginLeft: Sizes.xl,
    },
    crossIconStyle: {
      height: Sizes.x3l,
      width: Sizes.x3l,
      position: 'absolute',
      top: 80,
      right: 10,
      zIndex: 2,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xl,
      backgroundColor: colors.background,
    },
    otpInputBox: {
      borderColor: colors.grey,
      borderWidth: 1,
      backgroundColor: colors.whiteColor,
    },
  });

export default styles;

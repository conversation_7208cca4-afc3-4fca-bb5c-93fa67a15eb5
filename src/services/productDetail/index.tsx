import Api from 'services';
import {apiRoute, apiEndpoints, subEndpoint} from 'config/apiEndpoint';

const API = new Api();

export const getProductDetail = async (productId: number) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${productId}/`;
  const resp = await API.get(url);
  return resp;
};
export const getAttributesByProductId = async (productId: number) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${productId}/${subEndpoint.products.attachments}`;
  const resp = await API.get(url);
  return resp;
};

export const getAllAttributesData = async (productId: number) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${productId}/${subEndpoint.products.attributeMetaData}`;
  const resp = await API.get(url);
  return resp;
};

export const getChildProductsData = async (productId: number) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${productId}/${subEndpoint.products.children}`;
  const resp = await API.get(url);
  return resp;
};
export const getFaqsData = async (productId: number, searchText: string) => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.question}/${subEndpoint.question.get}?product_id=${productId}&search=${searchText}`;
  const resp = await API.get(url);
  return resp;
};
export const getReviewsData = async (
  productId: number,
  sortBy: string,
  pageNo: number,
  limit: number,
  child_ids: Array<number>,
) => {
  const query = `sort_by=${sortBy}&page=${pageNo}&limit=${limit}${
    child_ids.length > 0
      ? '&' + child_ids?.map(e => 'child_ids=' + e).join('&')
      : ''
  }`;
  const url = `${apiRoute.review}${apiEndpoints.products}/${productId}/${apiEndpoints.reviews}?${query}`;
  const resp = await API.get(url);
  return resp;
};
export const buyNow = async (data: {
  cart_items: Array<{
    data: {
      sku: string;
      quantity: number;
    };
  }>;
  country_code: string;
}) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${subEndpoint.carts.buyNow}`;
  const resp = await API.post(url, data);
  return resp;
};
export const addNewFaq = async (data: any) => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.question}/${subEndpoint.question.add}`;
  const resp = await API.post(url, data);
  return resp;
};
export const likeFaqs = async (data: any) => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.question}/${subEndpoint.question.edit}`;
  const resp = await API.post(url, data);
  return resp;
};

export const allRecommendedProducts = async (id: any) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${id}/${subEndpoint.products.recommendedProducts}`;
  const resp = await API.get(url);
  return resp;
};

export const productFeedback = async (data: any) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${subEndpoint.products.feedback}`;
  const resp = await API.post(url, data);
  return resp;
};

export const bulkProducts = async (data: any) => {
  const url = `${apiRoute.orderMg}${apiEndpoints.bulkOrder}/${subEndpoint.bulkOrder.requests}`;
  const resp = await API.post(url, data);
  return resp;
};

export const getMultipleProductList = async data => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${subEndpoint.products.details}`;
  const resp = await API.post(url, data);
  return resp;
};

export const getMultipleProductBuyNow = async data => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${subEndpoint.products.details}/${subEndpoint.carts.buyNow}`;
  const resp = await API.post(url, data);
  return resp;
};

export const searchProducts = async (query: any) => {
  const url = `${apiRoute.search}${apiEndpoints.searchQuery}/${subEndpoint.searchQuery.results}?${query}`;
  const resp = await API.get(url);
  return resp;
};
export const rateAndReviews = async (data: any, id: any) => {
  const url = `${apiRoute.review}${apiEndpoints.products}/${id}/${subEndpoint.products.reviews}`;
  const resp = await API.post(url, data);
  return resp;
};

export const checkCanReviewForProduct = async (id: any) => {
  const url = `${apiRoute.review}${apiEndpoints.reviews}/${subEndpoint.review.validateSubmission}/${id}/`;
  const resp = await API.get(url);
  return resp;
};

export const getFrequentlyBought = async (productId: any) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.product}/${subEndpoint.products.frequentlyBought}?id=${productId}`;
  const resp = await API.get(url);
  return resp;
};
export const getSimpleFreeBie = async (skuId: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${apiEndpoints.itemPromotions}?sku=${skuId}`;
  const resp = await API.get(url);
  return resp;
};
export const getGroupedFreeBie = async (parentId: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${apiEndpoints.itemPromotions}?parent_id=${parentId}`;
  const resp = await API.get(url);
  return resp;
};

export const notifyProduct = async (id: number) => {
  const url =
    'https://serverless-prod.dentalkart.com/api/v1/subscribe/stock-alert';
  const resp = await API.post(url, {productid: id});
  return resp;
};

export const getCreditCard = async () => {
  const url = `${apiRoute.interface}${apiEndpoints.payment}/${subEndpoint.products.creditCard}`;
  const resp = await API.get(url);
  return resp;
};

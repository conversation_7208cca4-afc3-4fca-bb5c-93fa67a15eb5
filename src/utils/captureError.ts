import crashlytics from '@react-native-firebase/crashlytics';
import { debugError, debugLog } from './debugLog';
const logErrors=true

const TAG = 'Crashlytics: ';

export function initCrashlytics(): void {
    debugLog('Crashlytics: Initializing...');
  
    // Enable crash collection
    crashlytics().setCrashlyticsCollectionEnabled(true);
  
    // Log app start
    crashlytics().log('App started');
  
    // Handle unhandled JS errors
    globalThis.ErrorUtils.setGlobalHandler((error: any, isFatal: boolean) => {
      debugError('Uncaught Error:', error);
      crashlytics().recordError(error);
    });
  
    // Handle unhandled promise rejections
    globalThis.onunhandledrejection = (event) => {
      debugError('Unhandled Promise Rejection:', event.reason);
      crashlytics().recordError(event.reason);
    };
}

export function logError(error: unknown, context: string = 'General Error'): void {
  if (!logErrors) return;
  debugLog(TAG, `Logging error in Crashlytics - Context: ${context}`);
  try {
    const exception = error instanceof Error ? error : new Error(String(error));
    
    crashlytics().setAttribute('ErrorContext', context);
    crashlytics().recordError(exception);
  } catch (e) {
    debugLog(TAG, `Failed to log error: ${e}`);
    const fallbackError = e instanceof Error ? e : new Error(String(e));
    crashlytics().setAttribute('ErrorContext', 'Error Logging Failure');
    crashlytics().recordError(fallbackError);
  }
}

export function logApiError(error: unknown, apiEndpoint: string = 'Unknown API'): void {
  if (!logErrors) return;
  debugLog(TAG, `Logging API error for ${apiEndpoint}`);
  try {
    const exception = error instanceof Error ? error : new Error(String(error));
    
    crashlytics().setAttribute('API Endpoint', apiEndpoint);
    crashlytics().recordError(exception);
  } catch (e) {
    debugLog(TAG, `Failed to log API error: ${e}`);
    const fallbackError = e instanceof Error ? e : new Error(String(e));
    crashlytics().setAttribute('ErrorContext', 'API Error Logging Failure');
    crashlytics().recordError(fallbackError);
  }
}

export function logErrorWithTag(tag: string, error: unknown, message: string = 'Error Occurred'): void {
debugLog("!logErrors",!logErrors)
  if (!logErrors) return;
  debugLog(TAG, `Logging error with tag: ${tag}`);
  try {
    const exception = error instanceof Error ? error : new Error(String(error));

    crashlytics().setAttribute('ErrorTag', tag);
    crashlytics().setAttribute('ErrorMessage', message);
    crashlytics().recordError(exception);
  } catch (e) {
    debugLog(TAG, `Failed to log tagged error: ${e}`);
    const fallbackError = e instanceof Error ? e : new Error(String(e));
    crashlytics().setAttribute('ErrorContext', 'Tagged Error Logging Failure');
    crashlytics().recordError(fallbackError);
  }
}

export function logComponentError(error: unknown, fileName: string): void {
  logErrorWithTag('ComponentError', error, fileName);
}

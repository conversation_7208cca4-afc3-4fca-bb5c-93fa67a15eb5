import appsFlyer from 'react-native-appsflyer';
import { debugLog } from 'utils/debugLog';

export const appsFlyerEvent = async (eventName = '', data = {}) => {
  let appsflyerEvent;
  let appsflyerData = {};
  debugLog('eventName',eventName);
  debugLog('data',data);
  switch (eventName) {

    case 'Signup': {
      appsflyerEvent = 'af_complete_registration';
      appsflyerData = {
        af_registration_method: data?.mobileNumber,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'Login': {
      appsflyerEvent = 'af_login';
      appsFlyerCall(appsflyerEvent);
      break;
    }

    case 'ProductSearched': {
      appsflyerEvent = 'af_search';
      appsflyerData = {
        af_search_string: data?.searchTerm,
        af_content_list: data?.contentIds,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }
    
    case 'ProductViewed': {
      appsflyerEvent = 'af_content_view';
      appsflyerData = {
        af_price: data?.price,
        af_content: data?.name,
        af_content_id: data?.p_id,
        // af_content_type: data?.brands,  // optional
        af_currency: data?.currency,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'CategoryViewed': {
      appsflyerEvent = 'af_list_view';
      appsflyerData = {
        af_content_type: data?.listName,
        af_content_list: data?.contentIds,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'AddToWishlist': {
      appsflyerEvent = 'af_add_to_wishlist';
      appsflyerData = {
        af_price: data?.price,
        af_content_id: data?.productId,
        af_content_type: data?.category,
        af_currency: 'INR',
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'AddToCart': {
      appsflyerEvent = 'af_add_to_cart';
      appsflyerData = {
        af_price: data?.price,
        af_content: data?.sku,
        af_content_id: data?.productId,
        af_content_type: data?.category,
        af_currency: data?.currency,
        af_quantity: data?.quantity,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'RemoveFromCart': {
      appsflyerEvent = 'remove_from_cart';
      appsflyerData = {
        // af_price: data?.price,
        // af_content: data?.name,
        af_content_id: data?.p_id,
        af_content_type: data?.category,
        // af_currency: data?.currency,
        // af_quantity: data?.quantity,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'CheckoutStarted': {
      appsflyerEvent = 'af_initiated_checkout';
      appsflyerData = {
        af_price: data?.totalPrice,
        af_content_id: data?.productIds,
        af_content_type: data?.category,
        af_currency: data?.currency,
        af_quantity: data?.totalQuantity,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'CheckoutCompleted': {
      appsflyerEvent = 'af_purchase';
      appsflyerData = {
        af_revenue: data?.estimatedRevenue,
        af_price: data?.totalAmount,
        af_content: data?.sku,
        af_content_id: data?.productIds,
        af_content_type: data?.productNames,
        af_currency: data?.currency || 'INR',
        af_quantity: data?.productQuantities,
        af_order_id: data?.orderId,
        af_receipt_id: data?.receiptId
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }

    case 'CancelProduct': {
      appsflyerEvent = 'af_cancel_purchase';
      appsflyerData = {
        af_revenue: -Math.abs(data?.totalAmount),
        af_price: -Math.abs(data?.totalAmount),
        af_content_type: data?.productNames,
        af_currency: data?.currency || 'INR',
        af_quantity: data?.quantity,
        af_order_id: data?.orderId,
      };
      appsFlyerCall(appsflyerEvent, appsflyerData);
      break;
    }
  }
};

const appsFlyerCall = (eventName, data = {}) => {
  appsFlyer.logEvent(
    eventName,
    data,

    res => {
      debugLog('res====of===appsflyer==!!!', res);
    },
    err => {
      debugLog('error====of===appsflyer', err);
    },
  );
};

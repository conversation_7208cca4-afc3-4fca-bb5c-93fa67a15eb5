import {
  API_BASE_URL,
  FEEDS_API_BASE_URL,
  X_API_KEY,
  IMAGE_BASE_URL,
  Version,
  REFERRAL_X_API_KEY,
  SUB_IMAGE_BASE_URL,
  API_STAGE_BASE_URL,
  API_STAGE_ADMIN_URL,
} from 'config/environment';

const environment = 'production';
// api mode production, development, staging

const ENV = {
  production: {
    BASE_URL: API_BASE_URL,
    FEEDS_API_BASE_URL,
    IMAGE_BASE_URL,
    X_API_KEY,
    Version,
    REFERRAL_X_API_KEY,
    SUB_IMAGE_BASE_URL,
    ADMIN_URL: API_STAGE_ADMIN_URL,
  },
  staging: {
    BASE_URL: API_STAGE_BASE_URL,
    FEEDS_API_BASE_URL,
    IMAGE_BASE_URL,
    X_API_KEY,
    Version,
    REFERRAL_X_API_KEY,
    SUB_IMAGE_BASE_URL,
    ADMIN_URL: API_STAGE_ADMIN_URL,
  },
  development: {
    BASE_URL: API_BASE_URL,
    FEEDS_API_BASE_URL,
    IMAGE_BASE_URL,
    X_API_KEY,
    Version,
    REFERRAL_X_API_KEY,
    SUB_IMAGE_BASE_URL,
    ADMIN_URL: API_STAGE_ADMIN_URL,
  },
};

const getEnvVars = () => {
  if (environment === 'production') {
    return ENV.production;
  } else if (environment === 'staging') {
    return ENV.staging;
  } else {
    return ENV.development;
  }
};

export default getEnvVars;
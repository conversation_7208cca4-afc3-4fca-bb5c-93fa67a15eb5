import {Platform, Linking, Alert, NativeModules} from 'react-native';
import {
  PERMISSIONS,
  requestMultiple,
  openSettings,
  checkMultiple,
} from 'react-native-permissions';
import FileViewer from 'react-native-file-viewer';
import DeviceInfo from 'react-native-device-info';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {t} from 'i18next';
import RNFS from 'react-native-fs';
import moment from 'moment';
import {DeviceWidth, WEBSITE_URL} from 'config/environment';
import {debugLog} from './debugLog';
import Geolocation from 'react-native-geolocation-service';
import {addressTag} from 'staticData';
import localStorage from 'utils/localStorage';
import {checkValidateGst} from 'services/address';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {trackEvent} from 'components/organisms/appEventsLogger/FacebookEventTracker';
import {Settings} from 'react-native-fbsdk-next';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';
import {AnalyticsEvents} from 'components/organisms';
import {getIcon, resetIcon} from 'react-native-change-icon';
import {MEMBERSHIP_TYPE} from 'components/atoms/changeIconModal/constants';
const {RNChangeIcon} = NativeModules;

let disable = true;
const defaultPrice = 500000;
const stringReg = /^[a-zA-Z ]*$/;
const fullNameReg = /^[a-zA-Z]+( [a-zA-Z]+)+$/;
const emailRegExp = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,4})+$/;
const phoneReg = /^[56789][0-9]{9}$/;
const pinCodeRegExp = /^\d{6}$/;
const numberRegExp = /^[0-9]+$/;
const decimalNumRegExp = /^[0-9.]+$/;
const videoSizeLimit = 25 * 1024 * 1024;
const imageSizeLimit = 5 * 1024 * 1024;
const sAllDevice = DeviceWidth <= 380;
const mDevice = DeviceWidth > 320 && DeviceWidth <= 380;
const sDevice = DeviceWidth <= 320;
const urlReg = /^(https?:\/\/)?(www\.)?([\w-]+\.)+[a-zA-Z]{2,}([/?#].*)?$/;
const htmlReg = /<\/?[a-z][\s\S]*>/i;
const fullNameRegex = /^[a-zA-Z]+\s+[a-zA-Z ]+$/;
const isIos = Platform.OS === 'ios';

const getFileExtension = (fileUrl: string) => {
  return /[.]/.exec(fileUrl) ? /[^.]+$/.exec(fileUrl) : undefined;
};

const formatDate = (date, format) => {
  return moment(date).format(format);
};

const formatBytes = (bytes: any) => {
  if (bytes < 1024) {
    return `${bytes} Bytes`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(2)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  }
};

const coinInfoShow = (status: string) => {
  return status === orderStatusLabel.Delivered ||
    status === orderStatusLabel.PartiallyDelivered ||
    status === orderStatusLabel.OrderPlaced ||
    status === orderStatusLabel.OrderConfirmed ||
    status === orderStatusLabel.Packed ||
    status === orderStatusLabel.PartiallyPacked ||
    status === orderStatusLabel.Shipped ||
    status === orderStatusLabel.PartiallyShipped
    ? true
    : false;
};

const orderStatusLabel = {
  Lost: 'Lost',
  Damaged: 'Damaged',
  CancellationFailed: 'Cancellation Failed',
  Cancelled: 'Cancelled',
  Returned: 'Returned',
  OrderClosed: 'Order Closed',
  PaymentFailed: 'Payment Failed',
  CancellationRequested: 'Cancellation Requested',
  Delivered: 'Delivered',
  PartiallyDelivered: 'Partially Delivered',
  OrderPlaced: 'Order Placed',
  OrderConfirmed: 'Order Confirmed',
  PartiallyShipped: 'Partially Shipped',
  Shipped: 'Shipped',
  Packed: 'Packed',
  PartiallyPacked: 'Partially Packed',
  PaymentPending: 'Payment Pending',
  StatusPending: 'STATUS_PENDING',
  rejected: 'rejected',
  in_process: 'in_process',
  complete: 'complete',
  request_raised: 'request_raised',
  RequestRaised: 'Request Raised',
  Pending: 'Pending',
  Rejected: 'Rejected',
  InProcess: 'In Process',
  Complete: 'Complete',
  ReturnRaised: 'Return Raised',
};

const orderStatusColor = (title: string, colors: any) => {
  switch (title) {
    case orderStatusLabel.Lost:
    case orderStatusLabel.Damaged:
    case orderStatusLabel.CancellationFailed:
    case orderStatusLabel.Cancelled:
    case orderStatusLabel.Returned:
    case orderStatusLabel.OrderClosed:
    case orderStatusLabel.PaymentFailed:
      return {
        bgColor: colors.lightRed1,
        bgLColor: [colors.lightRed2, colors.strongRed1],
        rColor: colors.strongRed2,
        color: 'persianRed',
        status:
          title === orderStatusLabel.OrderClosed
            ? orderStatusLabel.Cancelled
            : title,
      };
    case orderStatusLabel.CancellationRequested:
      return {
        bgColor: colors.lightRed1,
        bgLColor: [colors.lightBlue, colors.strongBlue],
        rColor: colors.strongBlue1,
        color: 'persianRed',
        status: title,
      };
    case orderStatusLabel.Delivered:
    case orderStatusLabel.PartiallyDelivered:
      return {
        bgColor: colors.green4,
        bgLColor: [colors.lightLimeGreen, colors.darkLimeGreen],
        rColor: colors.darkLimeGreen1,
        color: 'green2',
        status: title,
      };
    case orderStatusLabel.OrderPlaced:
    case orderStatusLabel.OrderConfirmed:
      return {
        bgColor: colors.green4,
        bgLColor: [colors.lightLimeGreen, colors.darkLimeGreen],
        rColor: colors.darkLimeGreen1,
        color: 'green2',
        status: orderStatusLabel.OrderPlaced,
      };
    case orderStatusLabel.PartiallyShipped:
    case orderStatusLabel.Shipped:
      return {
        bgColor: colors.oasis,
        bgLColor: [colors.lightLimeGreen, colors.darkLimeGreen],
        rColor: colors.darkLimeGreen1,
        color: 'darkGoldenrod',
        status: title,
      };
    case orderStatusLabel.Packed:
    case orderStatusLabel.PartiallyPacked:
      return {
        bgColor: colors.softPeach,
        bgLColor: [colors.lightLimeGreen, colors.darkLimeGreen],
        rColor: colors.darkLimeGreen1,
        color: 'buccaneer',
        status: title,
      };
    case orderStatusLabel.PaymentPending:
    case orderStatusLabel.StatusPending:
      return {
        bgColor: colors.solitude,
        bgLColor: [colors.lightBlue, colors.strongBlue],
        rColor: colors.strongBlue1,
        color: 'cobalt',
        status: orderStatusLabel.PaymentPending,
      };
    case orderStatusLabel.rejected:
      return {
        bgColor: colors.cosmos,
        color: 'persianRed',
        status: orderStatusLabel.Rejected,
      };
    case orderStatusLabel.in_process:
      return {
        bgColor: colors.skyBlue22,
        color: 'whiteColor',
        status: orderStatusLabel.InProcess,
      };
    case orderStatusLabel.complete:
      return {
        bgColor: colors.green3,
        color: 'britishRacingGree',
        status: orderStatusLabel.Complete,
      };
    case orderStatusLabel.request_raised:
      return {
        bgColor: colors.whiteIce1,
        color: 'britishRacingGreen',
        status: orderStatusLabel.ReturnRaised,
      };
    default:
      return {
        bgColor: colors.green4,
        color: 'green2',
        status: title || '',
        rColor: colors.darkLimeGreen1,
        bgLColor: [colors.lightLimeGreen, colors.darkLimeGreen],
      };
  }
};

const getOrderStatusIcon = (
  status: string,
  isBeforeCurrent: boolean,
  isCurrent: boolean,
) => {
  const statusIcons: OrderStatusIcons = {
    'Request Raised': 'requestRaise',
    'Request Approved': 'requestApproved',
    Approved: 'requestApproved',
    'Pickup Complete': 'pickupR',
    'Under Inspection': 'searchR',
    Repaired: 'repaired',
    Delivered: 'delivered',
    'Replacement Approved': 'replaceApproval',
    'Replacement Shipped': 'replaceShipped',
    Replaced: 'replaceR',
    'Refund In Progress': 'refundProcess',
    'Refund Initiated': 'refundProcess',
    'Refund Complete': 'refundCompleted',
    'Refund Approved': 'requestApproved',
    'Compensation Approved': 'compensationApproval',
    'Compensation In Progress': 'compensationProgress',
    Compensated: 'compensated',
    'Approved for Free Replacement': 'requestApproved',
    'Approved for Free Replacements': 'freeR',
    'Spare Part Shipped': 'partShipped',
    'Spare Part Delivered': 'partDelivery',
    'Chargeable Spare Part Shipped': 'partShipped',
    'Approved for Chargeable Repair': 'requestApproved',
    'Approved for Free of Charge Repair': 'requestApproved',
    'Approved for Refund': 'requestApproved',
    'Approved for Free Spare Part': 'requestApproved',
    'Approved for Chargeable Spare Part': 'requestApproved',
    Resolved: 'resolved',
    Cancelled: 'cancelledR',
    Pending: 'pending',
  };
  const defaultIcon = 'tracknullIcon';
  return statusIcons[status] ? statusIcons[status] : defaultIcon;
};

const onDownload = async (fileUrl: string, title?: string, callBack?: any) => {
  const fileName = fileUrl?.substring(fileUrl?.lastIndexOf('/') + 1);
  const downloadPath = `${
    Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.ExternalDirectoryPath
  }/${fileName}`;
  const fileExists = await RNFS.exists(downloadPath);
  if (fileExists) {
    FileViewer.open(downloadPath, {
      showOpenWithDialog: true,
      showAppsSuggestions: true,
    });
  } else {
    try {
      callBack(true);
      const downloadResult = await RNFS.downloadFile({
        fromUrl: fileUrl,
        toFile: downloadPath,
      }).promise;
      callBack(false);
      if (downloadResult.statusCode === 200) {
        if (title) {
          showSuccessMessage(`${title} ${t('otherText.fileSaved')}`);
        } else {
          showSuccessMessage(t('otherText.invoiceSave'));
        }
        FileViewer.open(downloadPath, {
          showOpenWithDialog: true,
          showAppsSuggestions: true,
        });
      }
    } catch (e) {
      callBack(false);
      FileViewer.open(downloadPath, {
        showOpenWithDialog: true,
        showAppsSuggestions: true,
      });
    }
  }
};

const downloadFile = async (
  fileUrl: string,
  title?: string,
  callBack?: any,
) => {
  if (disable) {
    disable = false;
    try {
      onDownload(fileUrl, title, callBack);
      // if (Platform.OS === 'ios') {
      //   onDownload(fileUrl, title, callBack);
      // } else {
      //   const deviceAPI = await DeviceInfo.getApiLevel();
      //   let permission =
      //     deviceAPI >= 33
      //       ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
      //       : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
      //   requestMultiple([
      //     PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
      //     permission,
      //   ]).then(statuses => {
      //     if (
      //       'granted' ===
      //         statuses[PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE] ||
      //       'granted' === statuses[permission]
      //     ) {
      //       onDownload(fileUrl, title, callBack);
      //     } else {
      //       Alert.alert(
      //         t('validations.permissionAllow'),
      //         t('validations.permissionMsg'),
      //         [
      //           {text: 'No'},
      //           {
      //             text: 'Yes',
      //             style: 'cancel',
      //             onPress: () => openSettings().catch(() => {}),
      //           },
      //         ],
      //       );
      //     }
      //   });
      // }
    } catch (e) {
      debugLog('file check error ===', e);
    }
    setTimeout(() => {
      disable = true;
    }, 2500);
  }
};

const handleErrMsg = (res: any) => {
  let errMsg = 'Something went wrong!';

  // Check if res contains an array of errors
  if (Array.isArray(res?.errors)) {
    errMsg = res.errors[0]?.message || res.errors[0] || errMsg;
  }
  // Check if res.message is an array
  else if (Array.isArray(res?.message)) {
    errMsg = res.message[0] || errMsg;
  }
  // Check if res.message or res.errors are strings
  else if (
    typeof res?.message === 'string' ||
    typeof res?.errors === 'string'
  ) {
    errMsg = res.message || res.errors || errMsg;
  }
  // Check if res contains an object with nested errors
  else if (typeof res === 'object' && res !== null) {
    const errorKey = Object.keys(res)[0];
    if (Array.isArray(res[errorKey])) {
      errMsg = res[errorKey][0] || errMsg;
    } else if (typeof res[errorKey] === 'object') {
      const nestedKey = Object.keys(res[errorKey])[0];
      errMsg = res[errorKey][nestedKey]?.[0] || errMsg;
    }
  }

  return errMsg;
};

const debounce = (func: any, delay: number) => {
  let timeoutId: any;
  return (...args: any) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};

function getSectionPriority(section: Section) {
  const {code} = section.layout_type;
  return code;
}

const orderHomeData = (dataList: Section) => {
  const priorityOrder = [
    'notice',
    'top_categories',
    'main_slider',
    'top_brands',
    'recently_vw_carousel',
  ];
  const recentlyData = dataList?.filter(
    (item: Section) => item.layout_type?.code === 'recently_vw_carousel',
  );
  let recently = [
    {
      description: 'Recently Viewed Description',
      elements: [],
      header_type: 'tag',
      landing_url: null,
      layout_type: {
        code: 'recently_vw_carousel',
        label: 'RECENTLY_VW_CAROUSEL',
        total_elements: 20,
        type: 'product_carousel',
      },
      sort_order: 7,
      section_code: 'recently_viewed',
      title: 'Recently Viewed',
    },
  ];
  const data =
    recentlyData.length === 0 ? [...dataList, ...recently] : dataList;
  // Map sections to priority and filter out sections that don't match any priority
  const sortedData = data
    .filter((section: Section) =>
      priorityOrder.includes(getSectionPriority(section)),
    )
    .sort((a, b) => {
      const priorityA = priorityOrder.indexOf(getSectionPriority(a));
      const priorityB = priorityOrder.indexOf(getSectionPriority(b));
      return priorityA - priorityB;
    });
  const filteredData = data?.filter(
    (item: Section) =>
      item.layout_type?.code !== 'notice' &&
      item.layout_type?.code !== 'top_categories' &&
      item.layout_type?.code !== 'main_slider' &&
      item.layout_type?.code !== 'top_brands' &&
      item.layout_type?.code !== 'recently_vw_carousel',
  );
  return [...sortedData, ...filteredData];
};

const checkDevice = () => {
  const tablet = DeviceInfo.isTablet();
  const deviceModel = DeviceInfo.getDeviceId();
  return tablet || deviceModel?.startsWith('iPad') ? true : false;
};

const openWhatsApp = async (link: string) => {
  try {
    const url = 'whatsapp://send?text=Hello';

    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(link);
    } else {
      const storeUrl =
        Platform.OS === 'ios'
          ? 'https://apps.apple.com/app/whatsapp-messenger/id310633997'
          : 'https://play.google.com/store/apps/details?id=com.whatsapp';

      await Linking.openURL(storeUrl);
    }
  } catch (error) {
    debugLog('Error checking WhatsApp:', error);
  }
};

const isVideoUrl = (url: string) => {
  const indexOfTheAssetType = url.split('.').length - 1; // because the extension will always be at the end
  const assetType = url.split('.')[indexOfTheAssetType].toUpperCase();
  const isTheAssetVideoType = [
    'WEBM',
    'MPG',
    'MPEG',
    'MPE',
    'MP4',
    'M4P',
    'M4V',
    'AVI',
    'WMV',
    'MOV',
  ].includes(assetType);
  return isTheAssetVideoType;
};

const formaNumber = (x: string) => {
  x = x.toString();
  var lastThree = x.substring(x.length - 3);
  var otherNumbers = x.substring(0, x.length - 3);
  if (otherNumbers != '') {
    lastThree = ',' + lastThree;
  }
  return otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
};

const nameSplit = (str: string) => {
  const lastSpaceIndex = str.lastIndexOf(' ');
  const fname = lastSpaceIndex !== -1 ? str.substring(0, lastSpaceIndex) : str;
  const lname = lastSpaceIndex !== -1 ? str.substring(lastSpaceIndex + 1) : '';
  return {fname, lname};
};
function generateRandomString(length = 20) {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
const getWebLink = (str: string) => {
  if (!str) return WEBSITE_URL;
  if (str.startsWith('http')) return str;
  const path = str === '/magazine' ? 'magazine' : str.replace(/^\/+/, '');
  return `${WEBSITE_URL}${path}`;
};

const truncateText = (s: string | null | undefined, maxL: number): string => {
  if (typeof s !== 'string' || maxL <= 0) {
    return '';
  }
  return s.length <= maxL ? s : s.slice(0, maxL) + '...';
};

const btnClickCallBack = callBack => {
  if (disable) {
    disable = false;
    callBack();
    setTimeout(() => {
      disable = true;
    }, 2000);
  }
};

const toRadians = degrees => {
  var pi = Math.PI;
  return degrees * (pi / 180);
};

const distance = (origin, destination, unit) => {
  let lat1 = origin[0];
  let lat2 = destination[0];
  let lon1 = origin[1];
  let lon2 = destination[1];
  let radius = 6371; // earth radius in km
  // let radius = 6371000 // meters
  let dlat = toRadians(lat2 - lat1);
  let dlon = toRadians(lon2 - lon1);
  let a =
    Math.sin(dlat / 2) * Math.sin(dlat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dlon / 2) *
      Math.sin(dlon / 2);
  let c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  let km = radius * c;
  let miles = km * 0.621371;
  return unit === 'km'
    ? km
      ? km.toFixed(2)
      : 0
    : miles
    ? miles.toFixed(2)
    : 0;
};

const calculateDistance = (lat1, lat2, long1, long2) => {
  let p = 0.017453292519943295; // Math.PI / 180
  let c = Math.cos;
  let a =
    0.5 -
    c((lat1 - lat2) * p) / 2 +
    (c(lat2 * p) * c(lat1 * p) * (1 - c((long1 - long2) * p))) / 2;
  let dis = 12742 * Math.asin(Math.sqrt(a)); // 2 * R; R = 6371 km
  return dis;
};

const getCurrentLocation = (callBack, hide = true) => {
  checkMultiple(
    Platform.OS === 'ios'
      ? [PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]
      : [PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION],
  )
    .then(async result => {
      if (
        result[
          Platform.OS === 'ios'
            ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
            : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
        ] === 'granted'
      ) {
        try {
          if (Platform.OS === 'ios') {
            await Geolocation.requestAuthorization('whenInUse');
          }
          Geolocation.getCurrentPosition(
            position => callBack(position),
            async error => {},
            {enableHighAccuracy: false, timeout: 10000, maximumAge: 600000},
          );
        } catch (error) {}
      } else {
        if (hide) {
          setTimeout(() => {
            Alert.alert(t('address.locPerTitle'), t('address.locPerMsg'), [
              {text: t('buttons.notNow')},
              {
                text: t('buttons.openSetting'),
                style: 'cancel',
                onPress: () => openSettings(),
              },
            ]);
          }, 200);
        }
      }
    })
    .catch(() => {});
};

const getTag = (name: string) => {
  const title =
    name === 'Home' || name === 'Clinic' ? name : name ? 'Other' : '';
  const tag = addressTag?.find(item => item.title === title)?.image || 'home2';
  return tag;
};
const openInBrowser = async finalUrl => {
  if (!finalUrl) return;

  try {
    if (Platform.OS === 'android') {
      // Attempt to open in Chrome using the Chrome-specific URI scheme
      const chromeUrl = `googlechrome://navigate?url=${finalUrl}`;
      const supported = await Linking.canOpenURL(chromeUrl);
      if (supported) {
        await Linking.openURL(chromeUrl);
      } else {
        // Fallback to default browser
        await Linking.openURL(finalUrl);
      }
    } else if (Platform.OS === 'ios') {
      // Attempt to open in Chrome using the Chrome-specific URI scheme
      const chromeUrl = finalUrl.replace(/^https?:\/\//, 'googlechrome://');
      const supported = await Linking.canOpenURL(chromeUrl);
      if (supported) {
        await Linking.openURL(chromeUrl);
      } else {
        // Fallback to Safari
        await Linking.openURL(finalUrl);
      }
    } else {
      // For other platforms, open in default browser
      await Linking.openURL(finalUrl);
    }
  } catch (error) {
    console.error('An error occurred while opening the URL:', error);
    // Fallback to default browser
    await Linking.openURL(finalUrl);
  }
};

const cardTabType = {
  Bestdeal: 'Bestdeal',
  Wishlist: 'Wishlist',
};
const billingAdd = async (addressId, addresses) => {
  const shippingAddressId = await localStorage.get('shippingAddressId');
  const billingList = addresses?.filter(
    item => item?.default_billing && !item?.default_shipping,
  );
  const checkBillingAdd = billingList?.length > 0 ? true : false;
  let billing_address = null;
  if (checkBillingAdd && Number(shippingAddressId) == addressId) {
    const billingAddress = billingList[0];
    billing_address = {
      country_code: billingAddress?.country_code ?? billingAddress?.country_id,
      country_id: billingAddress?.country_id ?? 'IN',
      region_code: billingAddress?.region?.region_code,
      street: billingAddress.street,
      postcode: billingAddress.postcode,
      city: billingAddress.city,
      firstname: billingAddress.firstname,
      lastname: billingAddress.lastname,
      telephone: billingAddress.telephone,
      gst_id: billingAddress?.vat_id,
      same_as_billing: false,
      alternate_mobile:
        billingAddress?.custom_attributes?.find(
          attribute => attribute?.attribute_code === 'alternate_telephone',
        ).value ?? null,
      region_id: billingAddress?.region?.region_id ?? 0,
      region: billingAddress?.region?.region,
    };
  }
  return billing_address;
};
const onCheckGst = async (gst, name) => {
  const gstResp = await checkValidateGst({
    gstin: gst,
    state_name: name,
  });
  if (!gstResp?.data?.success) {
    const res = gstResp?.data?.data;
    const errMsg =
      res?.errors[0]?.message || res?.errors[0] || t('addressCard.gstValid');
    showErrorMessage(handleErrMsg(res));
    return {status: false, error: errMsg};
  }
  return {status: true, error: ''};
};

const fullNameText = address => {
  return address?.firstname === address?.lastname
    ? address?.firstname || ''
    : `${address?.firstname || ''} ${address?.lastname || ''}`.trim();
};

const recommendedTabType = {
  Similar: 'Similar Products',
  Related: 'Related Products',
  More: 'More From This Brand',
};
const checkMembershipStatus = async (membershipData: any) => {
  const modalStatus = await AsyncStorage.getItem('dk_icon');
  const currentIcon = await getIcon();

  if (
    membershipData?.memberships &&
    membershipData.memberships.some(ele => ele.is_active)
  ) {
    if (modalStatus !== 'true' && currentIcon === MEMBERSHIP_TYPE.DEFAULT) {
      return {shouldShowModal: true, membershipType: MEMBERSHIP_TYPE.DEFAULT};
    } else {
      if (currentIcon === MEMBERSHIP_TYPE.DEFAULT) {
        return {
          shouldShowModal: false,
          membershipType: MEMBERSHIP_TYPE.DEFAULT,
        };
      } else {
        return {
          shouldShowModal: false,
          membershipType: MEMBERSHIP_TYPE.ANDROID_PREMIUM,
        };
      }
    }
  } else if (membershipData?.memberships) {
    await AsyncStorage.setItem('dk_icon', 'false');

    if (currentIcon !== MEMBERSHIP_TYPE.DEFAULT) {
      setTimeout(async () => {
        if (Platform.OS === 'ios') {
          await resetIcon();
        } else {
          await RNChangeIcon.changeIcon(MEMBERSHIP_TYPE.DEFAULT);
        }
      }, 100);
    }

    return {shouldShowModal: false, membershipType: MEMBERSHIP_TYPE.DEFAULT};
  }

  return {shouldShowModal: false, membershipType: MEMBERSHIP_TYPE.DEFAULT};
};

const paymentOnlineEvent = (paymentData, userInfo, isLoggedIn) => {
  const addressParts = [
    paymentData?.addresses?.street?.join(', '),
    paymentData?.addresses?.city,
    paymentData?.addresses?.region?.label,
    paymentData?.addresses?.postcode,
    paymentData?.addresses?.country?.name,
  ].filter(Boolean);
  const paymentSuccessData = {
    'Order Id': paymentData?.order_number ?? paymentData?.order_id ?? 'Unknown Order ID',
    'Payment Mode': 'prepaid',
    Currency: 'INR',
    Coupon: paymentData?.coupon_code?.code ?? 'No Coupon',
    'Total Amount': paymentData?.amount / 100 ?? 0, // Convert from paisa to INR
    Brand: paymentData?.items?.map(
      item => item?.product?.manufacturer ?? 'Unknown Brand',
    ),
    Category: paymentData?.items?.map(item => 'Dental'), // No category provided, assuming Dental
    SKU: paymentData?.items?.map(item => item?.product?.sku ?? 'Unknown SKU'),
    'Parent SKU': paymentData?.items?.map(
      item => item?.product?.sku ?? 'Unknown Parent SKU',
    ),
    'Product Name': paymentData?.items?.map(
      item => item?.product?.name ?? 'Unknown Product',
    ),
    'Total Price':
      paymentData?.pricing_details?.item_total_selling_price?.amount?.value ??
      0,
    'Product Details': paymentData?.items ?? [], // Full item details
    'Total Quantity': paymentData?.total_quantity ?? 1,
    'Total MRP':
      paymentData?.pricing_details?.item_total_regular_price?.amount?.value ??
      0,
    'Order Date': new Date().toISOString(), // Assuming the current date as Order Date
    'No. Of Products': paymentData?.items?.length ?? 1,
    'Reward Earned': paymentData?.rewards?.total_coins ?? 0,
    'Coupon Value':
      paymentData?.pricing_details?.discounts?.find(d => d?.code === 'coupon')
        ?.amount?.value ?? 0,
    Address:
      addressParts.length > 0 ? addressParts.join(', ') : 'Unknown Address',
    'Reward Used': paymentData?.rewards?.total_coins_used ?? 0, // Assuming a key for reward usage, update if needed
    'Product Price': paymentData?.items?.map(
      item => item?.product?.price?.minimalPrice?.amount?.value ?? 0,
    ),
    'Total Amount Saved':
      paymentData?.pricing_details?.total_savings?.amount?.value ?? 0,
    'Product Id': paymentData?.items?.map(
      item => item?.product?.id ?? 'Unknown Product ID',
    ),
    'Discount Amount': paymentData?.pricing_details?.discounts?.reduce(
      (sum, d) => sum + (d?.amount?.value ?? 0),
      0,
    ),
    'Shipping Charge':
      paymentData?.pricing_details?.shipping_charges?.amount?.value ?? 0,
    'Tax Amount': paymentData?.pricing_details?.applied_taxes?.reduce(
      (sum, tax) => sum + Number(tax?.amount?.value ?? 0),
      0,
    ),
  };

  // appsFlyer COD Payment OrderData
  const appsFlyerOrderData = {
    estimatedRevenue: paymentSuccessData['Total Amount'],
    totalAmount: paymentSuccessData['Total Amount'],
    sku: paymentSuccessData['SKU']?.join(', '),
    productIds: paymentSuccessData['Product Id']?.join(', '),
    productNames: paymentSuccessData['Product Name']?.join(', '),
    currency: paymentSuccessData['Currency'],
    productQuantities: paymentSuccessData['No. Of Products'],
    orderId: paymentSuccessData['Order Id'],
    receiptId: paymentSuccessData['Order Id'],
  };
  console.log('appsFlyerOrderData====>>>', appsFlyerOrderData);

  appsFlyerEvent('CheckoutCompleted', appsFlyerOrderData);

  AnalyticsEvents(
    'CHECKOUT_COMPLETED',
    'Checkout Completed',
    paymentSuccessData,
    userInfo,
    isLoggedIn,
  );
  Settings.setAutoLogAppEventsEnabled(true);
  Settings.setAdvertiserIDCollectionEnabled(true);
  trackEvent('PURCHASE', {
    currency: paymentSuccessData?.Currency,
    amount: paymentSuccessData['Total Amount'],
    params: {
      fb_content_type: 'product',
      // fb_content_id: '',
      // num_items: 1,
    },
  });
};

const uriToBlob = uri => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.onload = function () {
      // return the blob
      resolve(xhr.response);
    };
    xhr.onerror = function () {
      reject(new Error('uriToBlob failed'));
    };
    xhr.responseType = 'blob';
    xhr.open('GET', uri, true);

    xhr.send(null);
  });
};

export {
  getFileExtension,
  formatBytes,
  orderStatusColor,
  getOrderStatusIcon,
  downloadFile,
  handleErrMsg,
  debounce,
  orderHomeData,
  checkDevice,
  openWhatsApp,
  isVideoUrl,
  formaNumber,
  defaultPrice,
  formatDate,
  stringReg,
  fullNameReg,
  emailRegExp,
  phoneReg,
  fullNameRegex,
  videoSizeLimit,
  imageSizeLimit,
  sAllDevice,
  mDevice,
  sDevice,
  urlReg,
  htmlReg,
  nameSplit,
  pinCodeRegExp,
  numberRegExp,
  decimalNumRegExp,
  generateRandomString,
  getWebLink,
  truncateText,
  btnClickCallBack,
  distance,
  getCurrentLocation,
  getTag,
  openInBrowser,
  isIos,
  coinInfoShow,
  cardTabType,
  billingAdd,
  onCheckGst,
  fullNameText,
  recommendedTabType,
  orderStatusLabel,
  checkMembershipStatus,
  paymentOnlineEvent,
  uriToBlob,
};

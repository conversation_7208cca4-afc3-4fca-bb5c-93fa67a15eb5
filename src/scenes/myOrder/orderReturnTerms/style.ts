import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
import {DeviceWidth} from 'config/environment';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    subContainer: {
      backgroundColor: colors.grey7,
      paddingTop: Sizes.xm,
    },
    imageView: {
      height: 131,
      width: 175,
    },
    thumbsUpVIew: {
      backgroundColor: colors.whiteColor,
      paddingHorizontal: Sizes.x6l,
      width: DeviceWidth - Sizes.x5l,
      alignSelf: 'center',
      alignItems: 'center',
      borderRadius: Sizes.m,
      marginHorizontal: Sizes.xx,
    },
    texts: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.s,
    },
    childTexts: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xm,
    },
    subReqBtn: {
      width: Sizes.ex2l,
      height: Sizes.x7l,
      alignSelf: 'center',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    sectionView: {
      backgroundColor: colors.whiteColor,
      padding: Sizes.xm,
      paddingHorizontal: Sizes.l,
    },
    policy: {
      backgroundColor: colors.whiteColor,
      marginTop: Sizes.m,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xm,
    },
    flex: {
      flex: Sizes.x,
    },
    childListView: {
      marginTop: Sizes.xm,
    },
    productsView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      borderColor: colors.grey2,
      backgroundColor: colors.whiteColor,
      paddingBottom: Sizes.m,
      marginHorizontal: Sizes.l,
    },
    productsSubView: {
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.sx,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
    },
    boxView: {
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
      marginVertical: Sizes.xm,
      marginHorizontal: Sizes.l,
    },
    sectionHeaderView: {
      padding: Sizes.xms,
      flexDirection: 'row',
      alignItems: 'center',
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
    },
    sectionMainView: {
      padding: Sizes.xms,
      flexDirection: 'row',
      alignItems: 'center',
    },
    sectionLeft: {
      width: Sizes.ex90,
      alignItems: 'center',
    },
    sectionLine: {
      height: Sizes.x,
      backgroundColor: colors.grey2,
    },
    fRow: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
  });

export default styles;

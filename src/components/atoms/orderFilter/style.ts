import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    imageModelMain: {
      backgroundColor: colors.modalShadow,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    imageModelMain1: {
      backgroundColor: colors.modalShadow,
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    subImageView: {
      backgroundColor: colors.whiteColor,
      width: '100%',
      padding: Sizes.xms,
      borderTopLeftRadius: Sizes.m,
      borderTopRightRadius: Sizes.m,
    },
    tabModal: {
      width: Sizes.ex350,
      borderRadius: Sizes.m,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x60,
    },
    btnStyle: {
      width: Sizes.ex110 + Sizes.s,
      height: Sizes.x46,
    },
    btnView: {
      flexDirection: 'row',
      paddingTop: Sizes.x56,
      paddingBottom: Sizes.x44,
      marginLeft: Sizes.xms,
    },
    filterListView: {
      paddingTop: Sizes.x26,
      marginLeft: Sizes.xms,
    },
    fRow: {
      flexDirection: 'row',
    },
    sectionView: {
      paddingVertical: Sizes.xl,
    },
    cleatBtn: {
      width: Sizes.ex1,
    },
    applyBtn: {
      width: Sizes.ex0,
    },
    titleStyle: {
      marginBottom: -Sizes.l,
    },
  });

export default styles;

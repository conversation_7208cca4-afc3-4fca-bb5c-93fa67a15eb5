import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const WishlistLoader = () => {
  const {colors} = useTheme();
  const boxWidth = (width - 30) / 2;
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={2000}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="20" rx="5" ry="5" width={width - 20} height="30" />

        <Rect x={10} y="60" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="60"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />

        <Rect x={10} y="300" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="300"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />

        <Rect x={10} y="540" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="540"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />

        <Rect x={10} y="780" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="780"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />

        <Rect x={10} y="1020" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="1020"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />

        <Rect x={10} y="1260" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="1260"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />

        <Rect x={10} y="1500" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="1500"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />

        <Rect x={10} y="1740" rx="5" ry="5" width={boxWidth} height="230" />
        <Rect
          x={boxWidth + 20}
          y="1740"
          rx="5"
          ry="5"
          width={boxWidth}
          height="230"
        />
      </ContentLoader>
    </ScrollView>
  );
};

export default WishlistLoader;

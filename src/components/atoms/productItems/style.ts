import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    pulsImgStyle: {
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: Sizes.s,
      borderColor: colors.itemsBorderColor,
      borderWidth: 0.3,
      padding: Sizes.xm,
      backgroundColor: colors.whiteColor,
      opacity: Sizes.x,
      height: Sizes.exl,
      width: Sizes.exl + Sizes.xms,
    },
    pulsImgStyleProduct: {
      height: Sizes.ex1 + Sizes.xl + Sizes.sx,
      width: '100%',
    },
    rewardIcon: {
      width: Sizes.l,
      height: Sizes.l,
    },
    pulsImgStyleSold: {
      position: 'absolute',
      zIndex: Sizes.xs,
      backgroundColor: colors.soldOutColor,
      height: Sizes.ex1 + Sizes.xl + Sizes.sx, //176
      width: Sizes.ex2l - Sizes.xms,
      alignItems: 'center',
      justifyContent: 'center',
    },
    pulsImgStyleSoldCont: {
      position: 'absolute',
      zIndex: Sizes.xs,
      backgroundColor: colors.soldOutColor,
      height: Sizes.exl,
      width: Sizes.exl + Sizes.xms, //110
      alignItems: 'center',
      justifyContent: 'center',
    },
    imgPlus: {
      width: Sizes.ex - Sizes.xms, //70
      height: Sizes.ex - Sizes.xs, //78
    },
    imgPlusProduct: {
      width: Sizes.exl + Sizes.l, // 117
      height: Sizes.ex0 + Sizes.x4l, //131,
    },
    prizeTextView: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
    },
    prizeTextProducts: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingTop: Sizes.s,
      width: '88%',
    },
    ratingView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingTop: Sizes.s,
    },
    raining: {
      marginLeft: Sizes.s,
    },
    containerSellers: {
      width: Sizes.exl + Sizes.xms,
      marginHorizontal: Sizes.s,
    },
    nullBox: {
      width: Sizes.exl + Sizes.xms,
      marginHorizontal: Sizes.s,
      backgroundColor: colors.lightGray,
      height: 180,
    },
    containerSellersProduct: {
      width: Sizes.exl + Sizes.xms,
      marginHorizontal: Sizes.s,
    },

    removePrize: {
      textDecorationLine: 'line-through',
      textDecorationColor: colors.textDecorationsColor,
      color: colors.removePrize,
      paddingHorizontal: Sizes.xs,
      fontSize: Sizes.m,
      fontWeight: '400',
      letterSpacing: 0.1,
    },
    directionView: {flexDirection: 'row', alignItems: 'center'},
    prizeText: {
      fontWeight: '700',
      marginRight: Sizes.s,
      color: colors.primary,
      fontSize: Sizes.mx,
      letterSpacing: 0.1,
    },
    paddingSpace: {
      paddingTop: Sizes.s,
    },
    descriptionTextSub: {
      fontSize: Sizes.m,
      fontWeight: '400',
      color: colors.lightGray,

      lineHeight: Sizes.mx,
    },
    successIcon: {width: 67, height: Sizes.xx},
    HorizontalsItemsStyles: {
      flexDirection: 'row',
      paddingVertical: Sizes.m,
      width: '100%',
    },
    items: {paddingHorizontal: Sizes.m, width: '68%'},
    prizeWithTag: {
      justifyContent: 'space-between',
      marginVertical: Sizes.m,
    },
    addToCart: {
      backgroundColor: colors.sunnyOrange,
      height: Sizes.x4l,
      width: '80%',
      alignItems: 'center',
      paddingHorizontal: Sizes.xx,
    },
    addToWishlist: {
      backgroundColor: colors.sunnyOrange,
      height: Sizes.x4l,
      width: '20%',
      elevation: Sizes.xm,
      borderWidth: 0.5,
      borderColor: colors.border,
      alignItems: 'center',
    },
    viewProduct: {
      backgroundColor: colors.sunnyOrange,
      height: Sizes.x4l,
      width: '100%',
      alignContent: 'stretch',
    },
    left: {left: Sizes.s},
    tag: {backgroundColor: colors.disabledButtonColor},
    viewContinuer: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-around',
      width: '100%',
    },
    opacity: {opacity: 0.33, backgroundColor: colors.lightGray},
    icons: {width: Sizes.l, height: Sizes.xx},
    buttonLeft: {
      left: Sizes.x,
    },
    buttonBackground: {backgroundColor: colors.whiteColor},
    addButtonText: {fontSize: Sizes.m, fontWeight: '600', letterSpacing: 0.1},
    imgView: {
      height: Sizes.xx,
      width: '60%',
    },
    freeView: {
      // marginLeft: Sizes.xm,
      // width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
    },

    space: {marginVertical: 3},
  });

export default styles;

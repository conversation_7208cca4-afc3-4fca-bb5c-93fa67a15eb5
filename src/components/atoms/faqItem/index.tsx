import React, {useState} from 'react';
import {View} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Label, Separator, Spacer} from 'components/atoms';
import {dateTimeFormat} from 'utils/formatter';
import {t} from 'i18next';
import LikeDislike from '../likeDisLikeButton';
import {Sizes} from 'common';
import {useMemo} from 'react';

type Props = {
  question?: string;
  questionViewStyle?: {};
  answer?: string;
  customerName?: string;
  createdAt?: string;
  likeCount?: number;
  postId?: string;
  dislikeCount?: number;
  onLikePress?: (count?: number) => void;
  onUpdate?: (postId: string, newLikes: number, newDislikes: number) => void;
  onDislikePress?: (count?: number) => void;
  faqLikeLoading?: boolean;
  faqDislikeLoading?: boolean;
  isQuestionTag?: boolean;
  hideLine?: boolean;
};

const FaqItem = ({
  question,
  questionViewStyle,
  answer,
  customerName,
  createdAt,
  likeCount,
  postId,
  onUpdate,
  dislikeCount,
  isQuestionTag,
  hideLine = false,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isReadMore, setIsReadMore] = useState(false);

  const handleLikeDislikeUpdate = async (postId, newLikes, newDislikes) => {
    if (onUpdate) {
      onUpdate(postId, newLikes, newDislikes);
    }
  };
  return (
    <View style={[styles.questionView, questionViewStyle]}>
      <View style={styles.tagView}>
        <View style={styles.questionTag}>
          <Label
            text={t('faqs.question')}
            size="mx"
            fontFamily="Medium"
            color={'whiteColor'}
          />
        </View>
        <Spacer size="m" type="Horizontal" />
        <View style={styles.flexView}>
          <Label
            text={`${question?.trim()}`}
            size="mx"
            fontFamily="Medium"
            color="text2"
          />
        </View>
      </View>
      <Spacer size="m" />
      <Label
        text={`${t('PDP.answer')}: `}
        size="mx"
        fontFamily="Medium"
        color="text">
        <Label
          text={
            answer.length > 80 && !isReadMore && !isQuestionTag
              ? answer.substring(0, 80) + '...'
              : answer
          }
          size="mx"
          fontFamily="Medium"
          color="text2">
          {answer.length > 80 && !isQuestionTag ? (
            <>
              {isReadMore ? (
                <Label
                  text={` ${t('faqs.readLess')}`}
                  size="mx"
                  color="text"
                  fontFamily="Medium"
                  onPress={() => setIsReadMore(!isReadMore)}
                />
              ) : (
                <Label
                  text={` ${t('faqs.readMore')}`}
                  size="mx"
                  fontFamily="Medium"
                  color="text"
                  onPress={() => setIsReadMore(!isReadMore)}
                />
              )}
            </>
          ) : undefined}
        </Label>
      </Label>
      <Spacer size="m" />
      <View style={styles.questionMainView}>
        <View style={styles.questionSubView}>
          <Label
            text={customerName?.substring(0, 20) ?? ''}
            size="mx"
            textTransform="capitalize"
            fontFamily="Regular"
            color="text2"
          />
          {customerName && customerName?.trim()?.length !== 0 && (
            <>
              <Spacer size="xm" type="Horizontal" />
              <Separator color="grey3" height={'l'} Vertical />
              <Spacer size="xm" type="Horizontal" />
            </>
          )}
          <Label
            text={dateTimeFormat(createdAt, 'd MMM yyyy')}
            size="mx"
            fontFamily="Regular"
            color="text2"
          />
        </View>
        <View style={styles.likeView}>
          <Spacer size="m" type="Horizontal" />
          <LikeDislike
            initialLikes={likeCount}
            initialDislikes={dislikeCount}
            postId={postId}
            updatePost={handleLikeDislikeUpdate}
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(FaqItem);

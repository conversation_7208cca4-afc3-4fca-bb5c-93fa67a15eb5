import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    amountList: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    box: {
      backgroundColor: colors.background,
      padding: Sizes.sx,
    },
    mainBox: {
      backgroundColor: colors.background,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    processingContinuer: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    lineStyle: {
      backgroundColor: colors.grey2,
      height: 1,
      marginVertical: Sizes.xm,
    },
    emailView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'center',
    },
  });
export default styles;

import React, {useMemo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Label, Spacer, Separator} from 'components/atoms';
import stylesWithOutColor from './style';
import Modal from 'react-native-modal';

type Props = {
  visible: boolean;
  onClose: () => void;
  onSkip: () => void;
  onMap: () => void;
  title: string;
  subTitle?: string;
  desc: string;
  btn1Text: string;
  btn2Text: string;
};

const AddressConfirmModal = (props: Props) => {
  const {
    visible,
    onClose,
    onSkip,
    onMap,
    title,
    subTitle,
    desc,
    btn1Text,
    btn2Text,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const onNext = () => {
    onClose();
    onMap();
  };

  const onNo = () => {
    onClose();
    onSkip();
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.25}
      style={styles.modalStyle}>
      <View style={styles.modalView}>
        <View>
          <View style={styles.rowCenter}>
            <Label
              text={title}
              size="l"
              weight="600"
              textTransform="capitalize"
              color="text"
              style={styles.fOne}>
              {subTitle && (
                <Label
                  text={subTitle}
                  size="mx"
                  weight="400"
                  textTransform="capitalize"
                  color="text"
                  style={styles.fOne}
                />
              )}
            </Label>
            <TouchableOpacity onPress={onClose}>
              <ImageIcon
                size="xxl"
                tintColor="text"
                icon="closeIcons"
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
          <Spacer size="xm" />
          <Label
            text={desc}
            size="mx"
            textTransform="capitalize"
            weight="500"
            color="text2"
          />
        </View>
        <Spacer size="xm" />
        <View style={styles.borderBottomView} />
        <Spacer size="xm" />
        <View style={styles.deleteCartItemView}>
          <TouchableOpacity style={styles.firstBtn} onPress={() => onNo()}>
            <Label text={btn1Text} size="mx" color="text" weight="500" />
          </TouchableOpacity>

          <Separator color="grey2" height="xxl" Vertical={true} thickness="x" />
          <Spacer size="xm" type="Horizontal" />
          <TouchableOpacity style={styles.rightBtn} onPress={() => onNext()}>
            <Label
              text={btn2Text}
              size="mx"
              textTransform="capitalize"
              color="text"
              weight="500"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default React.memo(AddressConfirmModal);

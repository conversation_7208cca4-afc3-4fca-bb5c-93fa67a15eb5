import React, {useCallback, useEffect, useState} from 'react';
import {View, TouchableOpacity, FlatList} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer, Separator, Radio} from 'components/atoms';
import {Button, PhoneInputText} from 'components/molecules';
import {IconWithBackground} from 'components/hoc';
import stylesWithOutColor from './style';
import DashedLine from 'react-native-dashed-line';
import {bankData, allBankName, debitCartData, payLaterData} from 'staticData';
import {t} from 'i18next';
import {getCreditCard} from 'services/productDetail';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  label: string;
};
const EmiModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose, label} = props;
  const [isShowEmiOtp, setIsShowEmiOtp] = useState(false);
  const [creditCard, setCreditCard] = useState<Array>([]);
  const [currentTab, setCurrentTab] = useState<'emiPlans' | 'payLater'>(
    'emiPlans',
  );
  const [currentCardTab, setCurrentCardTab] = useState<
    'creditCard' | 'debitCard'
  >('creditCard');
  const [selectedIndex, setSelectedIndex] = useState(null);

  const handleSelect = index => {
    setSelectedIndex(selectedIndex === index ? null : index);
  };
  const getCards = useCallback(async () => {
    const {data, status} = await getCreditCard();
    if (status && data) {
      setCreditCard(data?.data);
    }
  }, []);
  useEffect(() => {
    if (label === 'Pay Later') {
      setCurrentTab('payLater');
    } else if (label === ' EMI') {
      setCurrentCardTab('creditCard');
    }
    getCards();
  }, []);

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      height={0.8}
      onClose={() => onClose()}
      subViewStyle={styles.subViewStyle}
      content={
        <FlatList
          data={['']}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
          keyExtractor={(item, index) => index.toString()}
          renderItem={() => {
            return (
              <View style={styles.centeredView}>
                <View style={styles.bankView}>
                  <View style={styles.emiPlansView}>
                    <View style={styles.emiPlansSubView}>
                      <TouchableOpacity 
                        testID='tOEmiPlans'
                        onPress={() => {
                          setCurrentTab('emiPlans');
                          setSelectedIndex(null);
                        }}>
                        <Label
                          text={t('payment.emiPlan')}
                          size="xl"
                          fontFamily="Medium"
                          color={currentTab == 'emiPlans' ? 'text' : 'grey'}
                        />
                      </TouchableOpacity>
                      <Separator height="x8l" Vertical color="grey" />
                      <TouchableOpacity
                        testID='tOPayLater'
                        onPress={() => {
                          setCurrentTab('payLater');
                          setSelectedIndex(null);
                        }}>
                        <Label
                          text={t('payment.payLater')}
                          size="xl"
                          fontFamily="Medium"
                          color={currentTab == 'payLater' ? 'text' : 'grey'}
                        />
                      </TouchableOpacity>
                    </View>
                    <Spacer size="m" />
                    <View>
                      {currentTab === 'emiPlans' && (
                        <>
                          <View style={styles.creditCardView}>
                            <Button
                              onPress={() => {
                                setCurrentCardTab('creditCard');
                              }}
                              radius="m"
                              labelSize="m"
                              style={styles.flexShrink}
                              selfAlign="stretch"
                              weight="500"
                              text={t('payment.creditCardEMI')}
                              labelColor={
                                currentCardTab === 'creditCard'
                                  ? 'whiteColor'
                                  : 'categoryTitle'
                              }
                              paddingHorizontal="m"
                              type={
                                currentCardTab === 'creditCard'
                                  ? 'secondary'
                                  : 'bordered'
                              }
                            />
                            <Spacer size="m" type="Horizontal" />
                            <Button
                              onPress={() => {
                                setCurrentCardTab('debitCard');
                              }}
                              radius="m"
                              style={styles.flexShrink}
                              labelSize="m"
                              selfAlign="stretch"
                              weight="500"
                              text={t('payment.debitCardEMI')}
                              type={
                                currentCardTab === 'debitCard'
                                  ? 'secondary'
                                  : 'bordered'
                              }
                              labelColor={
                                currentCardTab === 'debitCard'
                                  ? 'whiteColor'
                                  : 'categoryTitle'
                              }
                              paddingHorizontal="m"
                            />
                          </View>
                          {currentCardTab === 'creditCard' && (
                            <View>
                              <Spacer size="l" />
                              <View style={styles.contentTypeMainView}>
                                {bankData.map((item, index: number) => (
                                  <View
                                    style={styles.contentTypeSubView}
                                    key={index.toString()}>
                                    <IconWithBackground
                                      icon={item.icon}
                                      backgroundColor={item.backgroundColor}
                                      borderRadius="xms"
                                      tintColor="text"
                                      size="x4l"
                                      style={styles.padding8}
                                    />
                                    <Spacer type="Vertical" size="m" />
                                    <Label
                                      align="center"
                                      text={item.label}
                                      size="m"
                                      fontFamily="Medium"
                                      color="text2"
                                      numberOfLines={3}
                                      style={{width: '70%'}}
                                    />
                                    <Spacer type="Horizontal" size="xxxl" />
                                  </View>
                                ))}
                              </View>
                              <Spacer size="m" />
                              <View style={styles.creditCardSubView}>
                                <Label
                                  text={t('payment.creditCardEMIs')}
                                  size="mx"
                                  fontFamily="SemiBold"
                                  color="text"
                                />
                                <Label
                                  align="center"
                                  text={t('payment.how')}
                                  size="m"
                                  fontFamily="Medium"
                                  color="text2"
                                />
                              </View>
                              <Spacer size="m" />
                              <View style={styles.paddingH4}>
                                <DashedLine
                                  dashLength={2}
                                  dashThickness={1}
                                  dashColor={colors.grey5}
                                />
                              </View>
                              <View onStartShouldSetResponder={() => true}>
                                <FlatList
                                  data={creditCard}
                                  scrollEnabled={false}
                                  nestedScrollEnabled={true}
                                  keyExtractor={(_, i) => i.toString()}
                                  renderItem={({item, index}) => {
                                    return (
                                      <View style={styles.paddingH8}>
                                        <TouchableOpacity
                                          testID='tOEmiModalHandleSelect'
                                          key={index}
                                          onPress={() => handleSelect(index)}
                                          style={styles.arrowRightView}>
                                          <View
                                            style={styles.arrowRightSubView}>
                                            <Label
                                              text={item?.entity_name}
                                              size="l"
                                              fontFamily="Medium"
                                              color="text2"
                                            />
                                          </View>
                                          <ImageIcon
                                            icon={
                                              index === selectedIndex
                                                ? 'arrowBottom'
                                                : 'arrowRight'
                                            }
                                            tintColor="grey"
                                            size="xxl"
                                          />
                                        </TouchableOpacity>
                                        {index === selectedIndex ? (
                                          <View>
                                            <View
                                              style={{
                                                flexDirection: 'row',
                                                backgroundColor:
                                                  colors.lightPurple,
                                              }}>
                                              <View style={styles.header}>
                                                <Label
                                                  color="text2"
                                                  size="mx"
                                                  textTransform="capitalize"
                                                  align="center"
                                                  text="Month"
                                                />
                                              </View>
                                              <View style={styles.header}>
                                                <Label
                                                  color="text2"
                                                  size="mx"
                                                  textTransform="capitalize"
                                                  align="center"
                                                  text="Emi"
                                                />
                                              </View>
                                              <View style={styles.header}>
                                                <Label
                                                  color="text2"
                                                  size="mx"
                                                  textTransform="capitalize"
                                                  align="center"
                                                  text="Minimum Eligible Amount"
                                                />
                                              </View>
                                            </View>
                                            <FlatList
                                              data={
                                                creditCard[index]
                                                  ?.payment_details
                                              }
                                              keyExtractor={(_, i) =>
                                                i.toString()
                                              }
                                              scrollEnabled={false}
                                              renderItem={({
                                                item: detail,
                                                index: i,
                                              }) => {
                                                return (
                                                  <View
                                                    key={i}
                                                    style={{
                                                      flexDirection: 'row',
                                                      justifyContent:
                                                        'space-around',
                                                    }}>
                                                    <Label
                                                      size="m"
                                                      style={styles.header}
                                                      align="center"
                                                      color="text2"
                                                      text={detail?.month}
                                                    />
                                                    <Label
                                                      style={styles.header}
                                                      align="center"
                                                      size="m"
                                                      color="text2"
                                                      text={
                                                        detail?.interest + '%'
                                                      }
                                                    />
                                                    <Label
                                                      size="m"
                                                      style={styles.header}
                                                      color="text2"
                                                      align="center"
                                                      text={
                                                        '₹' +
                                                        detail?.min_eligible_amount
                                                      }
                                                    />
                                                  </View>
                                                );
                                              }}
                                            />
                                          </View>
                                        ) : (
                                          <View />
                                        )}
                                      </View>
                                    );
                                  }}
                                />
                              </View>
                            </View>
                          )}
                          {currentCardTab === 'debitCard' && (
                            <>
                              <View style={styles.debitCardView}>
                                <Spacer size="l" />
                                {!isShowEmiOtp === true ? (
                                  <View style={styles.mobileNoView}>
                                    {/* <Label
                                      text={t('payment.checkOption')}
                                      size="mx"
                                      fontFamily="Medium"
                                      color="text2"
                                    />
                                    <Spacer size="m" />
                                    <PhoneInputText
                                      inputStyle={styles.subInput}
                                      searchIconStyle={styles.searchIconStyle}
                                      type="number-pad"
                                      style={styles.inputBorderStyle}
                                      onChangeText={text => {}}
                                      placeholder={t('payment.enterPhone')}
                                      placeholderTextColor={colors.text}
                                      clearIcon="arrowRight"
                                      value={'+91'}
                                      onClear={() => {
                                        setIsShowEmiOtp(true);
                                      }}
                                    />
                                    <Spacer size="m" /> */}
                                    <View style={styles.viewRight}>
                                      <Label
                                        text={t('payment.secure')}
                                        size="m"
                                        weight="500"
                                        color="text2"
                                      />
                                    </View>
                                    <Spacer size="m" />
                                  </View>
                                ) : (
                                  <View style={styles.enterView}>
                                    {/* <Label
                                      text={'Enter Otp '}
                                      size="mx"
                                      weight="500"
                                      color="text2"
                                    /> */}
                                    <Spacer size="m" />
                                    <View style={styles.viewRight}>
                                      <Label
                                        text={t('payment.resendOtp')}
                                        size="m"
                                        fontFamily="Medium"
                                        color="text2"
                                      />
                                    </View>
                                    <Spacer size="m" />
                                  </View>
                                )}
                              </View>
                              <Spacer size="m" />
                              <View style={styles.contentTypeMainView}>
                                {debitCartData.map((item, index: number) => (
                                  <TouchableOpacity
                                    testID='tOEmiModalcontentMainView'
                                    style={styles.contentTypeSubView}
                                    key={index.toString()}>
                                    <IconWithBackground
                                      icon={item.icon}
                                      backgroundColor={item.backgroundColor}
                                      borderRadius="xms"
                                      tintColor="text"
                                      size="x4l"
                                      style={styles.padding8}
                                    />
                                    <Spacer type="Vertical" size="m" />
                                    <Label
                                      align="center"
                                      text={item.label}
                                      size="m"
                                      fontFamily="Medium"
                                      color="text2"
                                      numberOfLines={3}
                                    />
                                    <Spacer type="Horizontal" size="xxxl" />
                                  </TouchableOpacity>
                                ))}
                              </View>
                              <Spacer size="l" />
                              <DashedLine
                                dashLength={2}
                                dashThickness={1}
                                dashColor={colors.grey5}
                              />
                              <Spacer size="m" />
                              <View style={styles.debitView}>
                                <Label
                                  text={t('payment.debitCardEMIs')}
                                  size="mx"
                                  fontFamily="SemiBold"
                                  color="text"
                                />
                                <Label
                                  text={t('payment.how')}
                                  size="m"
                                  fontFamily="Medium"
                                  color="text2"
                                />
                              </View>
                              <Spacer size="xm" />
                              <View style={styles.viewCenter}>
                                <Label
                                  text={t('payment.noEmi')}
                                  size="mx"
                                  fontFamily="Medium"
                                  color="text2"
                                />
                              </View>
                              <Spacer size="m" />
                              <DashedLine
                                dashLength={2}
                                dashThickness={1}
                                dashColor={colors.grey5}
                              />
                              <View style={styles.cardLessView}>
                                <Label
                                  text={t('payment.cardlessEMI')}
                                  size="mx"
                                  fontFamily="SemiBold"
                                  color="text"
                                />
                                <Spacer size="xm" />
                                <View style={styles.viewCenter}>
                                  <Label
                                    text={t('payment.noEmi')}
                                    size="mx"
                                    fontFamily="Medium"
                                    color="text2"
                                  />
                                </View>
                                <Label
                                  text={t('payment.otherBanks')}
                                  size="mx"
                                  fontFamily="SemiBold"
                                  color="text"
                                />
                              </View>
                              <DashedLine
                                dashLength={2}
                                dashThickness={1}
                                dashColor={colors.grey5}
                              />
                              <View style={styles.bankNameView}>
                                <View style={styles.stateView}>
                                  <Label
                                    text={t('payment.bankName')}
                                    size="l"
                                    fontFamily="Medium"
                                    color="text2"
                                  />
                                </View>
                              </View>
                            </>
                          )}
                        </>
                      )}
                      {currentTab === 'payLater' && (
                        <>
                          <Spacer size="m" />
                          <View onStartShouldSetResponder={() => true}>
                            <FlatList
                              data={payLaterData}
                              keyExtractor={(_, i) => i.toString()}
                              renderItem={({item, index}) => {
                                const isSelected = selectedIndex === index;
                                return (
                                  <View style={styles.paddingH8}>
                                    <TouchableOpacity
                                    testID='tOEmiModalHandleSelect'
                                      onPress={() => handleSelect(index)}
                                      style={styles.payLaterDataView}>
                                      <View style={styles.payLaterSubDataView}>
                                        <ImageIcon
                                          icon={item?.icon}
                                          size="xxl"
                                        />
                                        <Spacer size="m" type="Horizontal" />
                                        <Label
                                          text={item.label}
                                          size="l"
                                          fontFamily="Medium"
                                          color="text2"
                                        />
                                      </View>
                                      <Radio
                                        fillColor="categoryTitle"
                                        selected={isSelected}
                                        onPress={() => handleSelect(index)}
                                      />
                                    </TouchableOpacity>
                                  </View>
                                );
                              }}
                            />
                          </View>
                        </>
                      )}
                    </View>
                    <Spacer size="m" />
                  </View>
                </View>
              </View>
            );
          }}
        />
      }
    />
  );
};

export default React.memo(EmiModal);

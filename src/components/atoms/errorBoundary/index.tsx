import React from 'react';
import {View} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer} from 'components/atoms';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {TouchableOpacity} from 'react-native';
import {useMemo} from 'react';

type Props = {
  resetError: () => void;
};

const ErrorFallback = ({resetError}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View style={styles.container}>
      <View>
        <FastImage
          source={Icons.wentWrongGif}
          resizeMode="contain"
          style={styles.image}
        />
        <Spacer size="l" />
        <Label
          text={t('page404.somethingwentwrong')}
          size="xl"
          align="center"
          weight="600"
          textTransform="capitalize"
          color="text"
        />
      </View>
      <Spacer size="l" />
      <TouchableOpacity testID='tOErrorBoundaryGoback' onPress={resetError} style={styles.subContainer}>
        <Label
          text={t('buttons.goBack')}
          size="mx"
          weight="500"
          color="background"
          align="center"
        />
      </TouchableOpacity>
    </View>
  );
};

export default ErrorFallback;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    childProductView: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      padding: Sizes.m,
      backgroundColor: colors.background,
      marginBottom: Sizes.xm,
    },
    amountView: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    centerAlign: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    fRow: {
      flexDirection: 'row',
    },
    expiryView: {
      flex: Sizes.x,
      marginRight: Sizes.xm,
    },
    expiryDateView: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    infoIcon: {
      paddingHorizontal: Sizes.s,
    },
    expireEndView: {
      alignSelf: 'flex-end',
    },
    addBtnView: {
      height: 35,
      width: Sizes.ex96,
    },
    flex: {
      flex: Sizes.x,
    },
  });

export default styles;

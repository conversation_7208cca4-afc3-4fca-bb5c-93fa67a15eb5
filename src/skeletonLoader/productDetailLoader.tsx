import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect, Circle} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ProductDetailLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1600}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="20" rx="5" ry="5" width={width - 20} height="350" />
        <Rect x={10} y="360" rx="5" ry="5" width={width - 20} height="150" />
        <Rect x={10} y="500" rx="5" ry="5" width={width - 20} height="100" />

        <Rect x="10" y="610" rx="5" ry="5" width={150} height="25" />
        <Rect x={width - 35} y="610" rx="5" ry="5" width="25" height="25" />

        <Rect x="30" y="645" rx="5" ry="5" width="30" height="25" />
        <Rect x="130" y="645" rx="5" ry="5" width="30" height="25" />
        <Rect x="230" y="645" rx="5" ry="5" width="30" height="25" />
        <Rect x="330" y="645" rx="5" ry="5" width="30" height="25" />

        <Rect x="20" y="680" rx="5" ry="5" width="60" height="25" />
        <Rect x="120" y="680" rx="5" ry="5" width="60" height="25" />
        <Rect x="220" y="680" rx="5" ry="5" width="60" height="25" />
        <Rect x="320" y="680" rx="5" ry="5" width="60" height="25" />

        <Rect x={10} y="720" rx="5" ry="5" width={width - 20} height="100" />

        <Rect x={10} y="830" rx="5" ry="5" width={width / 2} height="350" />
        <Rect
          x={(width - 20) / 2}
          y="830"
          rx="5"
          ry="5"
          width={width / 2}
          height="350"
        />

        <Rect x={10} y="1190" rx="5" ry="5" width={width / 2} height="350" />
        <Rect
          x={(width - 20) / 2}
          y="1190"
          rx="5"
          ry="5"
          width={width / 2}
          height="350"
        />
      </ContentLoader>
    </ScrollView>
  );
};

export default ProductDetailLoader;

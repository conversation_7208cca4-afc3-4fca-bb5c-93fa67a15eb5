import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    imageModel: {
      backgroundColor: colors.blueLagoon,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    subImageView: {
      backgroundColor: colors.grey7,
      width: '100%',
      marginTop: '40%',
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x6l,
    },
    modelSubView: {
      padding: Sizes.m,
      paddingBottom: Sizes.l,
    },
    trackBtn: {
      height: Sizes.x26,
    },
    row: {
      flexDirection: 'row',
      alignContent: 'center',
    },
    imageView: {
      alignSelf: 'flex-start',
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
    },
    imageSizeMain: {
      width: Sizes.ex,
      height: Sizes.ex,
      borderRadius: Sizes.xm,
    },
    historyView: {
      padding: Sizes.m,
      borderRadius: Sizes.xms,
      backgroundColor: colors.whiteColor,
      marginTop: Sizes.m,
    },
  });

export default styles;

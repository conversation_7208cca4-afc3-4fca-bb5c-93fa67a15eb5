import React from 'react';
import {TouchableOpacity} from 'react-native';
import Icons from 'common/icons';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {ImagePath} from 'config/apiEndpoint';
import FastImage from 'react-native-fast-image';
import {useMemo} from 'react';

type Props = {
  icon?: keyof typeof Icons | null;
  index: number | string;
  item: getHomepageSuperListItemProps | homePageBannerItemProps;
  onPress?: () => void;
  active?: boolean;
};

const SubCategriesImage = ({item, onPress, index, active = false}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <TouchableOpacity
      key={index}
      style={[styles.imageContioner, active && styles.active]}
      onPress={onPress}>
      <FastImage
        resizeMode="contain"
        style={styles.imageCategries}
        source={{uri: ImagePath.product + item.file}}
      />
    </TouchableOpacity>
  );
};

export default SubCategriesImage;

import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {View, ViewStyle} from 'react-native';

import {AirbnbRating} from 'react-native-ratings';
import {Sizes} from 'common';

type Props = {
  selected?: boolean;
  style?: ViewStyle['style'];
  underStyles?: ViewStyle['style'];
  count: number;
  defaultRating: number | undefined;
  size?: keyof typeof Sizes;
  showRating: boolean;
  isDisabled: boolean;
  onFinishRating: () => void;
  reviews?: string;
};

const Ratings = ({
  style,
  count = 0,
  defaultRating,
  size,
  showRating,
  onFinishRating,
  reviews,
  isDisabled = false,
}: Props) => {
  return (
    <View>
      <AirbnbRating
        isDisabled={isDisabled}
        reviews={reviews}
        count={count}
        defaultRating={defaultRating}
        size={size}
        showRating={showRating}
        onFinishRating={onFinishRating}
        style={style}
      />
    </View>
  );
};

export default Ratings;

import {Sizes} from 'common';
import {Dimensions, StyleSheet} from 'react-native';
const {width: SCREEN_WIDTH} = Dimensions.get('window');
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    backImageView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: Sizes.l,
        paddingBottom: Sizes.m,
        alignItems: 'center',
    },
    endKingView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingVertical: Sizes.xms,
        paddingRight: Sizes.x9l,
    },
    backgroundImage: {
        resizeMode: 'cover',
        justifyContent: 'center',
    },
    countView: {
        flex: Sizes.x,
        paddingVertical: 0,
        paddingHorizontal: Sizes.m,
    },
  });
export default styles;

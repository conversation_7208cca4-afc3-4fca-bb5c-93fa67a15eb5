import {Fonts} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    descText: {
      color: colors.text2,
      fontFamily: Fonts.Regular,
    },
    htmlRenderStyle: {
      p: {color: colors.text2, fontFamily: Fonts.Medium, margin: 0, padding: 0},
      li: {color: colors.text2, fontFamily: Fonts.Medium},
      ul: {color: colors.text2, fontFamily: Fonts.Medium},
      div: {color: colors.text2, fontFamily: Fonts.Medium},
      body: {
        color: colors.text2,
        whiteSpace: 'normal',
        fontFamily: Fonts.Medium,
      },
      h1: {color: colors.text2, fontFamily: Fonts.Medium},
      h3: {color: colors.text2, fontFamily: Fonts.Medium},
      span: {color: colors.text2, fontFamily: Fonts.Medium},
      strong: {color: colors.text2, fontFamily: Fonts.Medium},
      td: {color: colors.text2, fontFamily: Fonts.Medium},
    },
  });

export default styles;

name: Deploy Android

on:
  push:
    branches:
      - master
      - staging
      - development

permissions:
  contents: write

jobs:
  cancel-previous-runs:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ secrets.PAT_TOKEN }}  # Use the new PAT_TOKEN secret
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up JDK 17 for Android
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'

    - name: Install Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18.20'

    - name: Install Yarn Dependencies
      run: yarn install

    - name: Cache Gradle dependencies
      uses: actions/cache@v3
      with:
        path: ~/.gradle/caches
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.1.2'
        bundler-cache: true

    - name: Install bundler
      run: |
        cd android && bundle install

    - name: Build and Deploy with Fastlane
      env:
        ANDROID_KEYSTORE_PATH: ${{ secrets.ANDROID_KEYSTORE_PATH }}
        ANDROID_KEYSTORE_ALIAS: ${{ secrets.ANDROID_KEYSTORE_ALIAS }}
        ANDROID_KEYSTORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
        ANDROID_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
      run: |
        cd android
        if [[ "${{ github.ref_name }}" == "master" ]]; then
          fastlane deploy_production
        elif [[ "${{ github.ref_name }}" == "staging" ]]; then
          fastlane deploy_staging
        elif [[ "${{ github.ref_name }}" == "development" ]]; then
          fastlane deploy_dev
        else
          echo "Branch does not match any flavor. Skipping deployment."
        fi
        mkdir -p ../artifacts
        # Move APKs for all architectures to the artifacts directory if they exist
        find app/build/outputs/apk/ -name "*.apk" -exec mv {} ../artifacts/ \; || echo "No APKs found"
        # Move AABs to the artifacts directory if they exist
        find app/build/outputs/bundle/ -name "*.aab" -exec mv {} ../artifacts/ \; || echo "No AABs found"

    - name: Upload APK and AAB as Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: App-Release-Builds
        path: |
          artifacts/*.apk
          artifacts/*.aab

    - name: Commit source code on success
      if: success()
      run: |
        git config --global user.name "Github"
        git config --global user.email "<EMAIL>"
        git fetch origin
        git checkout ${{ github.ref_name }}
        git pull origin ${{ github.ref_name }}
        git restore --staged package.json yarn.lock
        git restore package.json yarn.lock
        git add .
        git commit -m "Automated commit after deployment" || echo "No changes to commit"
        git push origin ${{ github.ref_name }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      continue-on-error: true

    - name: Push changes to GitLab
      if: success()
      run: |
        git config --global user.name "Github"
        git config --global user.email "<EMAIL>"
        git remote add gitlab https://oauth2:${{ secrets.GITLAB_TOKEN }}@gitlab.com/VASA-Denticity/********************.git
        git checkout ${{ github.ref_name }}
        git fetch gitlab
        git merge origin/${{ github.ref_name }}
        git push -u gitlab ${{ github.ref_name }}
      env:
        GITLAB_TOKEN: ${{ secrets.GITLAB_TOKEN }}
      continue-on-error: true

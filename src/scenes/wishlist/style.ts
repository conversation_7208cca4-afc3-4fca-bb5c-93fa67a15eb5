import {Fonts, Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.xs,
      backgroundColor: colors.offWhite,
    },
    listStyle: {
      flex: Sizes.xs,
      backgroundColor: colors.offWhite,
      marginHorizontal: Sizes.m,
    },
    img: {
      width: Sizes.x7l,
      height: Sizes.x9l,
    },
    fRow: {
      flexDirection: 'row',
    },
    grpImage: {
      width: Sizes.xms,
      height: Sizes.xms,
    },
    tabBtn: {
      width: DeviceWidth / Sizes.xs,
      paddingVertical: Sizes.m,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderLeftWidth: 0,
      borderRightWidth: 0,
    },
    footerView: {
      alignItems: 'center',
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingBottom: Sizes.xms,
    },
    list: {
      paddingVertical: Sizes.m,
      backgroundColor: colors.whiteColor,
      paddingLeft: Sizes.l,
      borderRadius: Sizes.xm,
      marginHorizontal: Sizes.m,
    },
    gradient: {
      flexDirection: 'row',
      alignItems: 'center',
      borderBottomRightRadius: Sizes.m,
      borderTopLeftRadius: Sizes.xl,
      paddingHorizontal: Sizes.m,
      alignSelf: 'flex-end',
    },
    products: {
      width: '44%',
      borderRadius: Sizes.m,
      alignItems: 'center',
      borderWidth: 0.6,
      borderColor: colors.hawkesBlue,
      backgroundColor: colors.offWhite,
      height: Sizes.x70,
      margin: Sizes.s,
      justifyContent: 'center',
    },
    fastImage: {
      flexWrap: 'wrap',
      flexDirection: 'row',
      justifyContent: 'space-between',
      height: Sizes.ex156,
      paddingHorizontal: Sizes.sx,
    },
    horizontalLine: {
      borderWidth: 0.6,
      height: Sizes.m,
      borderColor: colors.placeholderColor,
    },
    threeDotView: {
      height:Sizes.x8l,
      width:Sizes.x4l,
      justifyContent:'center',
    },
    threeDotImageView: {
      alignSelf:'center'
    },
    productView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    subViewProduct: {
      width: (DeviceWidth - Sizes.x4l) / Sizes.xs,
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      backgroundColor: colors.whiteColor,
    },
    topSection: {
      height:Sizes.x8l,
      alignItems: 'center',
      flexDirection: 'row',
    },
    mainProductView: {
      width: (DeviceWidth - Sizes.x4l) / Sizes.xs,
      flexDirection: 'row',
      justifyContent: 'space-around',
      flexWrap: 'wrap',
      minHeight: Sizes.ex226,
      backgroundColor: colors.offWhite,
    },
    buttonText: {
      color: colors.whiteColor,
      textTransform: 'capitalize',
      marginTop: -Sizes.xs,
    },
    modelBtn: {
      height: Sizes.x7l,
      width: '100%',
      marginTop: Sizes.xms,
      backgroundColor: colors.categoryTitle,
      borderRadius: Sizes.xms,
    },
    modelInputView: {
      borderColor: colors.grey2,
      paddingVertical: 0,
    },
    modelInputBox: {
      color: colors.text2,
      fontSize: Sizes.m,
    },
    modelEditInputBox: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    listNameView: {
      paddingHorizontal: Sizes.xl,
      paddingTop: Sizes.mx,
    },
    sureWantView: {
      paddingHorizontal: Sizes.xl,
      paddingTop: Sizes.x60,
    },
    currentListView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    wishlistNameView: {
      marginLeft: Sizes.xms,
    },
    sureView: {
      justifyContent: 'center',
      maxWidth: '64%',
      alignSelf: 'center',
    },
    allWishlistView: {
      paddingHorizontal: Sizes.xms,
      paddingTop: Sizes.xms,
    },
    createView: {
      position: 'absolute',
      right: Sizes.m,
      bottom: Sizes.x8l,
      zIndex: Sizes.x,
    },
    crossIconView: {
      position: 'absolute',
      top: 0,
      right: -Sizes.sx,
      padding: Sizes.xs,
    },
    threeDotIcon: {position: 'absolute', right: 0, top: 0},
    wishlistCardHeadingView: {
      paddingHorizontal: Sizes.m,
      paddingRight: Sizes.xm,
      flex: Sizes.x,
    },
    shareTopSection: {
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xms,
    },
    shareHeadingView: {
      flexDirection: 'row',
      flex: Sizes.x,
      paddingRight: Sizes.mx,
    },
    tabLabel: {
      fontFamily: Fonts.Medium,
      alignSelf: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    textCap: {
      textTransform: 'capitalize',
    },
    loading: {
      paddingTop: '50%',
    },
    emptyView: {
      height: Sizes.ex110,
      justifyContent: 'center',
      alignItems: 'center',
    },
    a1: {
      position: 'relative',
      width: '100%',
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      justifyContent: 'center',
    },
    input: {
      height: Sizes.x7l,
      borderColor: colors.grey2,
      paddingVertical: 0,
    },
    rightText: {
      position: 'absolute',
      right: Sizes.l,
      fontSize: Sizes.l,
      color: 'gray',
    },
    btnLabel: {
      textTransform: 'capitalize',
      marginTop: -Sizes.xs,
    },
    statusStyle: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

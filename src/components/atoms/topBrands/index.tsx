import React, {memo, useCallback, useMemo} from 'react';
import {TouchableOpacity, View, FlatList} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {AnalyticsEvents} from 'components/organisms';
import {ImageIcon, Label, Spacer, FastImagesItem} from 'components/atoms';
import stylesWithOutColor from './style';
import {navigate} from 'utils/navigationRef';
import {checkDevice} from 'utils/utils';
import {useSelector} from 'react-redux';
import {DeviceWidth, WEBSITE_URL} from 'config/environment';

type Props = {
  section?: Section;
};

const isTablet = checkDevice();

const TopBrands = (props: Props) => {
  const {section} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);

  const sectionTitle = useMemo(() => section?.title, [section?.title]);

  const brandElements = useMemo(
    () => section?.elements || [],
    [section?.elements],
  );

  const handleViewAllPress = useCallback(() => {
    const eventData = {
      'Page Link': `${WEBSITE_URL}/brands`,
      'Section Name': 'TopBrands',
      Section: 'Homepage Sections',
    };
    Promise.all([
      navigate('Brands'),
      AnalyticsEvents(
        'VIEW_ALL_HOMEPAGE',
        'View All Homepage',
        eventData,
        userInfo,
        isLoggedIn,
      ),
    ]);
  }, [userInfo, isLoggedIn]);

  const handleBrandPress = item => {
    const brandData = {
      Brand: item?.title,
      'Page URL': `${WEBSITE_URL}/${item?.landing_page_entity?.url}.html`,
    };

    Promise.all([
      navigate('CategoryDetail', {
        categoryId: item?.landing_page_entity?.category_id,
      }),
      AnalyticsEvents(
        'BRAND_VIEW',
        'Brand View',
        brandData,
        userInfo,
        isLoggedIn,
      ),
    ]);
  };

  const renderBrandItem = useCallback(
    ({item, index}) => {
      return (
        <TouchableOpacity
          key={index}
          style={styles.logoView}
          onPress={() => handleBrandPress(item)}>
          <View style={styles.circleBrand}>
            <FastImagesItem
              FastImageStyle={styles.imageCecile}
              resizeMode="contain"
              source={{uri: item?.media?.mobile_image}}
            />
          </View>
          {/* Commented section preserved as in original code */}
          {/* {isTablet ? <Spacer size="s" /> : null}
        <Label
          color="categoryTitle"
          size={isTablet ? 'mx' : 'm'}
          numberOfLines={2}
          fontFamily="Medium"
          text={item?.title}
          style={styles.titleStyle}
        /> */}
        </TouchableOpacity>
      );
    },
    [styles, userInfo, isLoggedIn, handleBrandPress],
  );

  const keyExtractor = useCallback(
    item =>
      item?.landing_page_entity?.category_id?.toString() ||
      item?.id?.toString() ||
      Math.random().toString(),
    [],
  );

  const ITEM_WIDTH = checkDevice()
    ? (DeviceWidth - 100) / 6
    : (DeviceWidth - 52) / (DeviceWidth <= 320 ? 4 : 5);

  const getItemLayout = useCallback(
    (_, index) => ({
      length: ITEM_WIDTH,
      offset: ITEM_WIDTH * index,
      index,
    }),
    [],
  );

  return (
    <View style={styles.container}>
      <View style={styles.brandsMainView}>
        <View style={styles.brandsSubView}>
          <Label
            fontFamily="Medium"
            text={sectionTitle}
            size={isTablet ? 'xxl' : 'l'}
            color="categoryTitle"
          />
          <Spacer size="sx" type="Horizontal" />
          <ImageIcon icon="brandsStar" size="xxl" />
        </View>
        <TouchableOpacity
          style={styles.rightArrowStyle}
          onPress={handleViewAllPress}>
          <ImageIcon size="xxl" icon="doubleArrowRightBlue" />
        </TouchableOpacity>
      </View>
      <FlatList
        data={brandElements}
        horizontal
        bounces={false}
        style={styles.brandView}
        showsHorizontalScrollIndicator={false}
        renderItem={renderBrandItem}
        keyExtractor={keyExtractor}
        getItemLayout={getItemLayout}
        removeClippedSubviews={true}
        initialNumToRender={5} // Render only what's visible initially
        maxToRenderPerBatch={3} // Process only a few items per batch
        windowSize={5} // Keep a small window of items in memory
        updateCellsBatchingPeriod={50} // Batch rendering updates
        snapToAlignment="start"
        decelerationRate="fast"
        scrollEventThrottle={16} // Optimize scroll performance
      />
    </View>
  );
};

export default memo(TopBrands);

import React, {useCallback, useState, useMemo} from 'react';
import {
  View,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {ImageIcon, Label, ListView, Separator, Spacer} from 'components/atoms';
import {t} from 'i18next';
import moment from 'moment';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import {Button} from 'components/molecules';
import {downloadFile, formatDate} from 'utils/utils';

type Props = {
  inx: number;
  item: Packages;
  type: string;
  estimate: estimateStatus;
  orderClose: boolean;
  orderDetail: OrderDetailsV1;
  orderStatusList: StatusHistory[];
  trackingData: trackingData[];
  itemData: OrderItems[];
  orderId: string;
  invoice: InvoiceLink;
  ItemScoped: boolean;
  showPreviousReturn: boolean;
  openImage: () => void;
  onTrackClick: (url: string) => void;
  pickUpModal: () => void;
  onTrackReturn: () => void;
};

const OrderShipmentCard = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {
    inx,
    item,
    type,
    estimate,
    orderClose,
    orderDetail,
    orderStatusList,
    trackingData,
    itemData,
    orderId,
    invoice,
    ItemScoped,
    showPreviousReturn,
    openImage,
    onTrackClick,
    pickUpModal,
    onTrackReturn,
  } = props;

  const [loaderState, setLoaderState] = useState({});

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const trackItem = useCallback(
    ({item, index}) => {
      const statusColor = ['Cancelled', 'Returned', 'Order Closed'].includes(
        item?.status,
      );
      const labelColor =
        type === 'Returns'
          ? item.isBeforeCurrent || item.isCurrent
            ? 'text2'
            : 'grey2'
          : statusColor
          ? 'persianRed'
          : item.icon === 'roundGreen'
          ? 'green2'
          : 'text2';
      return (
        <View
          key={index}
          style={[styles.processingContinuer, styles.orderSubTrack]}>
          <View style={styles.row}>
            <View style={styles.itemCenter}>
              <ImageIcon size="xl" icon={item.icon} />

              {item.showLine ? (
                item.lineActive === false ? (
                  <Separator style={styles.trackLineInactive} />
                ) : (
                  <Separator style={styles.trackLine} />
                )
              ) : null}
            </View>
            <Spacer type="Horizontal" size="xms" />
            <Label
              text={item.label}
              size="m"
              fontFamily="Medium"
              color={labelColor}
            />
          </View>
          <Label
            style={ItemScoped ? styles.subHeadingShoed : styles.subHeading}
            text={item.transit}
            size="m"
            fontFamily="Medium"
            color="text2"
          />
        </View>
      );
    },
    [ItemScoped],
  );

  const statusHistory = useCallback(() => {
    const statusList = item?.status_history?.filter(
      data => data.status?.toLowerCase() === item?.status?.toLowerCase(),
    );
    const statusShow =
      statusList?.length > 0 &&
      statusList[0].status &&
      statusList[0].status !== 'Order Placed' &&
      statusList[0].date;

    const orderCancel =
      statusList?.length > 0 &&
      statusList[0].status &&
      (statusList[0].status === 'Cancelled' ||
        statusList[0].status === 'Returned');
    return (
      <View style={styles.estimateView}>
        {statusShow ? (
          <Label
            fontFamily="Medium"
            color={orderCancel ? 'persianRed' : 'text'}
            text={`${statusList[0].status} ${t('orderTrack.on')}`}
            size="m">
            <Spacer type="Horizontal" size="s" />
            <Label
              size="m"
              text={formatDate(statusList[0].date, 'dddd, DD MMM YYYY')}
              fontFamily="Medium"
              color={orderCancel ? 'persianRed' : 'green2'}
            />
          </Label>
        ) : (
          <View />
        )}

        {item?.delivery_info?.label && !estimate?.expectedDate && (
          <>
            {statusShow && (
              <>
                <Spacer type="Horizontal" size="s" />
                <Separator height="l" color="grey" Vertical />
                <Spacer type="Horizontal" size="s" />
              </>
            )}
            <Label
              size="m"
              text={item?.delivery_info?.label}
              fontFamily="Medium"
              color="green2"
            />
          </>
        )}
      </View>
    );
  }, [item]);

  return (
    <View>
      <View style={styles.renderSubCards}>
        <View>
          {type === 'Returns' ? (
            <>
              {item?.return_id ? (
                <Label
                  fontFamily="Medium"
                  color="text2"
                  text={`${t('orderReturn.returnId')} - ${item?.return_id}`}
                  size="m"
                />
              ) : null}
              {item?.tracking_id ? (
                <Label
                  fontFamily="Medium"
                  color="text2"
                  text={`${t('orderTrack.awbNo')} -  ${
                    item?.tracking_id || ''
                  }`}
                  size="m"
                />
              ) : null}

              <View style={styles.reqView}>
                <Label
                  fontFamily="Medium"
                  color="text2"
                  text={`${t(
                    'orderListing.requestedOn',
                  )} - ${item?.request_at_date?.substr(0, 10)}`}
                  size="m"
                />
                <Spacer size="xm" type="Horizontal" />
                <Separator height="xxl" color="grey" Vertical />
                <Spacer size="xm" type="Horizontal" />
                <View style={styles.reasonTextView}>
                  <Label
                    fontFamily="Medium"
                    color="text2"
                    text={`${t('otherText.reason')} -  ${item?.reason}`}
                    size="m"
                  />
                </View>
              </View>
            </>
          ) : (
            <>
              {estimate?.expectedDate && !orderClose ? (
                <>
                  <View style={styles.estimateView}>
                    <Label
                      fontFamily="Medium"
                      color="text"
                      text={estimate?.status}
                      size="m">
                      <Spacer type="Horizontal" size="s" />
                      <Label
                        size="m"
                        text={estimate?.expectedDate}
                        fontFamily="Medium"
                        color="green2"
                      />
                    </Label>
                    {item?.delivery_info?.label && (
                      <>
                        <Spacer type="Horizontal" size="s" />
                        <Separator height="l" color="grey" Vertical />
                        <Spacer type="Horizontal" size="s" />
                        <Label
                          size="m"
                          text={item?.delivery_info?.label}
                          fontFamily="Medium"
                          color="green2"
                        />
                      </>
                    )}
                  </View>
                  <Spacer type="Horizontal" size="s" />
                </>
              ) : (
                item?.status === 'Delivered' &&
                !item?.delivery_info?.label?.includes(
                  t('orderListing.packageHand'),
                ) && (
                  <Label
                    fontFamily="Medium"
                    color="text"
                    text={t('orderListing.packageHand')}
                    size="m"
                    textTransform="capitalize"
                  />
                )
              )}
              {statusHistory()}
              {/* <Label
                fontFamily="Medium"
                color="text2"
                text={`${t(
                  'orderListing.orderedOn',
                )} - ${orderDetail?.order_date.substring(0, 10)}`}
                size="m"
              /> */}
              {/* {orderClose &&
                orderStatusList?.length > 0 &&
                orderStatusList[0]?.date && (
                  <Label
                    fontFamily="Medium"
                    color="persianRed"
                    text={`${t(
                      item?.status === 'Cancelled'
                        ? 'orderListing.cancelledOn'
                        : item?.status === 'Returned'
                        ? 'orderListing.returnedOn'
                        : 'orderListing.closedOn',
                    )} ${moment(orderStatusList[0]?.date).format(
                      'dddd, DD MMM YYYY',
                    )}`}
                    size="m"
                  />
                )} */}
            </>
          )}
        </View>
        {item?.status === 'rejected' ? (
          <View style={styles.mainRejected}>
            <Label
              fontFamily="Medium"
              color="red"
              textTransform="capitalize"
              text={`${t('orderTrack.requestFor')} ${item?.qty} ${t(
                'orderTrack.products',
              )}`}
              size="m"
            />
            <Spacer size="m" />
            <FlatList
              data={['']}
              renderItem={() => (
                <>
                  <View style={styles.fRow}>
                    <View style={[styles.itemCenter, styles.flexOne]}>
                      <Label
                        fontFamily="SemiBold"
                        color="text"
                        text={t('otherText.product')}
                        size="m"
                      />
                      <View style={styles.rejectedImage}>
                        <FastImage
                          resizeMode="contain"
                          style={styles.imageSizeReturn}
                          sourceType="url"
                          source={{uri: getImageUrl(item?.image)}}
                        />
                      </View>
                    </View>
                    <Spacer size="m" />

                    <View style={[styles.itemCenter, styles.flexTwo]}>
                      <Label
                        fontFamily="SemiBold"
                        color="text"
                        text={t('otherText.reason')}
                        size="m"
                      />
                      <View>
                        <Label
                          fontFamily="Medium"
                          color="text"
                          text={item?.reason}
                          size="m"
                        />
                      </View>
                    </View>
                    <Spacer size="m" />

                    <View style={[styles.itemCenter, styles.flexOne]}>
                      <Label
                        fontFamily="SemiBold"
                        color="text"
                        text={t('otherText.remark')}
                        size="m"
                      />
                      <View>
                        <Label
                          fontFamily="Medium"
                          color="text"
                          text={item?.remarks}
                          size="m"
                        />
                      </View>
                    </View>
                  </View>
                </>
              )}
            />
          </View>
        ) : (
          <>
            <Spacer size="xm" />
            <ListView
              keyExtractor={listViewKeyExtractor}
              renderItem={trackItem}
              data={trackingData}
            />
          </>
        )}

        <View>
          <Spacer size="xm" />
          {type === 'Returns' ? null : (
            <>
              <View style={styles.mainImage}>
                {Array.isArray(itemData) &&
                  itemData?.slice(0, 3).map((data: OrderItems, i: number) => {
                    const btnClick = [
                      'Plus Membership Plan-II',
                      'Plus Membership Plan-I',
                    ].includes(data?.name?.trim());
                    return (
                      <TouchableOpacity
                        key={i}
                        onPress={() => (!btnClick ? openImage() : {})}
                        activeOpacity={!btnClick ? 0 : 1}
                        style={styles.image}>
                        <ImageIcon
                          resizeMode="stretch"
                          style={styles.trackImageSize}
                          sourceType="url"
                          source={getImageUrl(data?.image, 'product')}
                        />
                      </TouchableOpacity>
                    );
                  })}
                {Array.isArray(itemData) && itemData?.length > 3 && (
                  <TouchableOpacity
                    onPress={() => openImage()}
                    style={styles.imageSubView}>
                    <Label
                      fontFamily="Medium"
                      color="white1"
                      text={`+${itemData.length - 3}`}
                      size="mx"
                    />
                  </TouchableOpacity>
                )}
                <View style={styles.flexOne} />
                {item?.tracking_url && (
                  <Button
                    onPress={() => onTrackClick(item?.tracking_url)}
                    radius="xms"
                    size="zero-height"
                    weight="500"
                    text={t('orderTrack.track')}
                    type="secondary"
                    labelColor="whiteColor"
                    paddingHorizontal="xx"
                    style={styles.trackOrderBtn}
                  />
                )}
              </View>
              <Spacer size="xm" />
              <View style={styles.durationsBox}>
                <View style={styles.flexOne}>
                  {item?.transporter && (
                    <>
                      <Label
                        fontFamily="Regular"
                        color="text2"
                        text={t('otherText.courier')}
                        size="m">
                        <Spacer type="Horizontal" size="s" />
                        <Label
                          size="m"
                          text={item?.transporter || ''}
                          fontFamily="Medium"
                          color="text2"
                        />
                      </Label>
                      <Spacer size="s" />
                    </>
                  )}
                  {item?.tracking_number && (
                    <Label
                      fontFamily="Regular"
                      color="text2"
                      text={t('otherText.trackingId')}
                      size="m">
                      <Spacer type="Horizontal" size="s" />
                      <Label
                        fontFamily="Medium"
                        color="text2"
                        text={item?.tracking_number || ''}
                        size="m"
                      />
                    </Label>
                  )}
                </View>
                {invoice?.pdf_link && (
                  <View>
                    <Spacer size="xm" />
                    {loaderState && loaderState[inx] ? (
                      <ActivityIndicator
                        size="small"
                        color={colors.categoryTitle}
                      />
                    ) : (
                      <Button
                        onPress={() =>
                          orderId &&
                          invoice?.pdf_link &&
                          downloadFile(
                            invoice?.pdf_link,
                            '',
                            (value: boolean) => {
                              setLoaderState({...loaderState, [inx]: value});
                            },
                          )
                        }
                        radius="xms"
                        size="zero-height"
                        weight="500"
                        text={t('myOrder.invoice')}
                        type="bordered"
                        labelColor="categoryTitle"
                        paddingHorizontal="l"
                        iconLeft="downloadButtonIcon"
                        tintColor="categoryTitle"
                        style={styles.trackBtn}
                      />
                    )}
                  </View>
                )}
              </View>
            </>
          )}
          {item?.status === 'pick_up_Failed' && (
            <>
              <TouchableOpacity onPress={() => pickUpModal()}>
                <Label
                  fontFamily="Medium"
                  color={colors.skyBlue22}
                  text={t('orderTrack.pickUpDate')}
                  size="m"
                  textDecorationLine="underline"
                />
              </TouchableOpacity>
            </>
          )}

          {type === 'Returns' ? (
            <View>
              {item?.courier && (
                <>
                  <Label
                    fontFamily="Regular"
                    color="text2"
                    text={t('otherText.courier')}
                    size="m">
                    <Spacer type="Horizontal" size="s" />
                    <Label
                      size="m"
                      text={item?.courier || ''}
                      fontFamily="Medium"
                      color="text"
                    />
                  </Label>
                  <Spacer size="sx" />
                </>
              )}
              {item.tracking_id && (
                <Label
                  fontFamily="Regular"
                  color="text2"
                  text={t('otherText.trackingId')}
                  size="m">
                  <Spacer type="Horizontal" size="s" />
                  <Label
                    fontFamily="Medium"
                    color="text"
                    text={item.tracking_id || ''}
                    size="m"
                  />
                </Label>
              )}
              {showPreviousReturn && (
                <Button
                  onPress={() => onTrackReturn()}
                  radius="xms"
                  size="zero-height"
                  selfAlign="flex-start"
                  weight="500"
                  text={t('buttons.viewMoreReturns')}
                  type={'bordered'}
                  labelColor={'categoryTitle'}
                  paddingHorizontal="m"
                  style={styles.trackBtn}
                />
              )}
            </View>
          ) : null}
        </View>
      </View>
      {type === 'Returns' ? (
        <View style={styles.historyView}>
          {item?.tracking_url && (
            <>
              <Button
                onPress={() => onTrackClick(item?.tracking_url)}
                radius="xms"
                size="zero-height"
                selfAlign="flex-end"
                weight="500"
                text={t('myOrder.track')}
                type={'bordered'}
                labelColor={'categoryTitle'}
                paddingHorizontal="m"
                style={styles.trackBtn}
              />
              <Spacer size="xm" />
            </>
          )}
          <Label
            text={moment(item.request_at_date).format('YYYY-MM-DD, hh:mm')}
            fontFamily="SemiBold"
            size="mx"
            color="text"
          />
          <Spacer size="xm" />
          <Label
            text={`${t('orderTrack.orderedID')} - ${orderId}`}
            fontFamily="Medium"
            size="mx"
            color="text2"
          />
          <Spacer size="xm" />
          <View style={styles.row}>
            <Label
              text={`${t('orderReturn.returnId')} - ${item?.return_id}`}
              fontFamily="Medium"
              size="mx"
              color="text2"
            />
            <Spacer size="l" type="Horizontal" />
            <Separator height="xxl" color="grey" Vertical />
            <Spacer size="l" type="Horizontal" />
            <Label
              text={`${t('orderTrack.sku')} - ${item?.sku}`}
              fontFamily="Medium"
              size="mx"
              color="text2"
            />
          </View>
          <Spacer size="xm" />
          <Label
            text={`${t('orderTrack.totalItem')} - ${item?.qty}`}
            fontFamily="Medium"
            size="mx"
            color="categoryTitle2"
          />
          <Spacer size="xm" />
          <View style={styles.imageView}>
            <FastImage
              resizeMode="contain"
              style={styles.imageSizeMain}
              source={{
                uri: getImageUrl(item?.image, 'product'),
              }}
            />
          </View>
        </View>
      ) : null}
    </View>
  );
};

export default React.memo(OrderShipmentCard);

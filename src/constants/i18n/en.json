{
  "translation": {
    "buttons": {
      "continue": "Continue ",
      "resend": "Resend",
      "verify": "Verify",
      "save": "Save",
      "logout": "Logout",
      "goToHome": "Go To Home",
      "goBack": "Go Back",
      "makePayment": "Make Payment",
      "moreInfo": "More Info",
      "bookNow": "Book Now",
      "reqPrice": "Request Price",
      // "register": "Register",
      "register": "Create Account",
      "login": "Login",
      "reset": "Reset",
      "bulkUpdate": "Get Bulk Quote",
      "write": "Write a Review",
      "checkout": "Checkout",
      "submit": "Submit",
      "Suggest": "Suggest Product",
      "addToCart": "Add to cart",
      "move": "Move",
      "addToWishList": "Add to WishList",
      "enroll": "ENROLL NOW",
      "renew": "RENEW",
      "track": "Track",
      "cancel": "Cancel",
      "download": "Download",
      "no": "No",
      "yes": "Yes",
      "reOrder": "Re - Order",
      "suggest": "SUGGEST A PRODUCT",
      "filter": "FILTER",
      "sort": "Sort",
      "cancelLower": "CANCEL",
      "applyLower": "APPLY",
      "continueShopping": "Continue Shopping",
      "check": "Check",
      "viewProduct": "VIEW PRODUCT",
      "buyNow": "Buy Now",
      "apply": "Apply",
      "applyFilter": "Apply Filter",
      "clear": "Clear All",
      "place": "Place Order",
      "add": "ADD",
      "remove": "Remove",
      "addWishlist": "Add to Wishlist",
      "ViewAllCoupon": "All Coupons",
      "clearFilter": "Clear Filter",
      "membership": "MEMBERSHIP",
      "getMembership": "GET MEMBERSHIP",
      "suggestProduct": "SUGGEST PRODUCT",
      "NetworkLogger": "Network Logger",
      "submitFeedback": "Submit Feedback",
      "createAWishlist": "Create a Wishlist",
      "confirm": "CONFIRM",
      "delete": "DELETE",
      "copy": "COPY",
      "done": "Done",
      "notifyme": "NOTIFY ME",
      "oK": "OK",
      "change": "CHANGE",
      "viewOrder": "View Order",
      "continue1": "continue",
      "deliverHere": "DELIVER HERE",
      "submitRequest": "Submit Request",
      "exploreNow": "Explore Now",
      "viewMoreReturns": "View More Returns",
      "returnDetails": "Return Details",
      "saveAddress": "Save Address",
      "tryNow": "Try Now",
      "openSetting": "Open Setting",
      "notNow": "Not now",
      "retry": "Retry",
      "modifyOrder": "Modify",
      "rateUs": "Rate Us",
      "rateMore": "Rate More",
      "rateNow": "Rate Now"
    },
    "validations": {
      "phoneNumberRequired": "Please enter valid phone number",
      "telephoneRequired": "Please enter phone number",
      "telephoneCorrect": "Please enter correct phone number",
      "alternateRequired": "Please enter receiver's phone number",
      "alternateCorrect": "Please enter correct receiver's phone number",
      "mobileRequired": "Mobile number is required",
      "emailPhoneNumberRequired": "Please enter phone number or email address",
      "OTPRequired": "Please enter valid OTP",
      "languageSelectionRequired": "Please select your pandit language",
      "nameRequired": "Please enter your name",
      "buildingRequired": "Please enter your house number",
      "streetRequired": "Please enter street name",
      "postalCodeRequired": "Please enter postal code",
      "stateRequired": "Please select state",
      "cityRequired": "Please select city",
      "cityReq": "Please enter city",
      "validEmail": "Please enter valid email",
      "requiredEmail": "Please Email is required",
      "alternativeNumber": "Alternative Number Required",
      "fullName": "Full Name is required",
      "lastName": "Last-name-Filled",
      "fullNameFilled": "Full Name is required",
      "companyRegister": "Enter Your Company Register as",
      "companyName": "Company Name is required",
      "afterSpace": "After Space Last Name is required",
      "password": "Password is required",
      "gstNumber": "GST Number is required",
      "gstRequired": "GST Number Must be at least 15 characters",
      "gstEnter": "Enter Your GST Number",
      "firstLast": "First and last name is required.",
      "alphabets": "This field can contain only alphabets.",
      "telephone": "Telephone number is required",
      "telephoneFormat": "telephone_format",
      "notValid": "Not a Valid Number",
      "postCode": "postcode_required",
      "postcodeRequired": "Please enter postcode",
      "validPostCode": "Please enter correct postcode",
      "alternateTelephone": "Receiver's phone number required",
      "requiredAlternateTelephone": "Alternate telephone number is required",
      "city": "City is required",
      "tax": "Please enter GSTIN",
      "validTex": "Please enter correct GSTIN",
      "texFormat": "tax_format",
      "texReq": "tax_required",
      "landmark": "Landmark is required",
      "street": "Please enter clinic name or house number",
      "requiredState": "state_required",
      "region": "Please select region",
      "postcodeState": "Postcode doesn't exist in this state",
      "country": "Country is required",
      "validEmailPhone": "Please Valid Email & Phone number",
      "emailRequried": "Email Id is required",
      "emailInvalid": "Email Id invalid",
      "wishListNameRequired": "WishList name is required",
      "wishlistName": "Wishlist name",
      "nameIsRequired": "Name is required.",
      "pinCodeIsRequired": "PinCode is required",
      "addressIsRequired": "Address is required",
      "requiredProduct": "requiredProduct",
      "selectProduct": "Please select product",
      "catalogueRequired": "Please upload catalouge ",
      "quantityRequired": "quantity is required",
      "fillPrice": "Please fill  price",
      "emailMobileRequired": "Email or Mobile number is required.",
      "emailMobileInvalid": "Email or Mobile number is invalid",
      "enterPassword": "Please enter your password",
      "sortPassword": "Your password is too short.",
      "confirmPassword": "Confirm your password.",
      "passwordMatch": "Your passwords do not match.",
      "enterPhoneValidation": "Enter your mobile number",
      "enterEmailValidation": "Please enter valid email",
      "number": "Must be a number",
      "characters": "Must be at least 10 characters",
      "address": "Must enter email address",
      "firstname": "Firstname is required",
      "lastNameRequired": "Lastname is required",
      "productName": "Please enter product name",
      "invalidProductName": "PLease enter valid product name",
      "fullNameReq": "Please enter your full name",
      "fullNameCorrect": "Please enter your correct full name",
      "firstNameReq": "Please enter first name",
      "firstNameCorrect": "Please enter correct firstname",
      "lastNameReq": "Please enter last name",
      "lastNameCorrect": "Please enter correct lastname",
      "typeReq": "Type is required",
      "secialityReq": "Speciality is required",
      "genderReq": "Gender is required",
      "dobReq": "Date of Birth is required",
      "anniversaryReq": "Anniversary Date is required",
      "emailValid": "Enter a valid email",
      "emailReq": "Email is required",
      "passwordLimit": "Password must be at least 6 characters",
      "otpValid": "minimum 6 digits required",
      "questionReq": "Please enter a Question.",
      "someThingWrong": "Something went wrong, please try again.",
      "nameField": "Name is required.",
      "nameAlphabets": "Please enter only alphabets.",
      "phoneField": "Phone number is required.",
      "phoneCorrect": "Please enter correct phone number.",
      "emailCorrect": "Please enter correct email.",
      "emailField": "Email is required.",
      "addressField": "Address is required .",
      "pinCode": "Please enter pincode.",
      "pinCodeValid": "Please enter valid pincode.",
      "exPrice": "Price per piece is required.",
      "quantityField": "Quantity is required.",
      "qualityReq": "Quality is required",
      "qualityAlphabets": "Quality must contain only alphabets",
      "priceReq": "Price is required",
      "pricePosNum": "Price must be a positive number",
      "priceInt": "Price must be an integer",
      "priceNum": "Price must be a number",
      "feedbackReq": "Feedback is required",
      "feedbackLong": "Feedback must be at least 1 character long",
      "selectRating": "Please select Rating",
      "minRatingReq": "Rating must be at least 1",
      "maxRatingReq": "Rating must be at most 5",
      "titleReq": "Title is required",
      "descriptionReq": "Description is required",
      "noSelectFilter": "Select a filter to see the data",
      "uploadCatalogue": "please upload catalogue",
      "fillAllForm": "Please fill the complete form!",
      "selectRegId": "Please select a registration ID.",
      "selectAddress": "Please select an address.",
      "noPayment": "There is no payment service available. Please choose a different address.",
      "permissionAllow": "Permission Allow",
      "permissionMsg": "We need access to storage permission for download files.",
      "permissionPhotoMsg": "We need access to storage permission for upload photo.",
      "permissionCameraMsg": "We need access to take photo of camera or storage permission.",
      "rootSorry": "Access Denied",
      "rootedDevices": "It seems that your device is jailbroken or rooted. Please restore your device to its original state to ensure the security of your data and access to this application.",
      "firstnameLimit": "Character should be less than 30",
      "onlyString": "Contains only alphabets",
      "selectReason": "Please select Reason",
      "selectSubReason": "Please select Sub Reason",
      "selectAction": "Please select Action",
      "attachmentReq": "Attachment is required",
      "attachmentsMin2": "At least 2 attachments are required",
      "invalidEmail": "Please enter a valid email address",
      "invalidUrl": "Please enter a valid URL",
      "passwordSame": "New password cannot be the same as the old one.",
      "confirmPassMatch": "New password and confirm password does not match.",
      "validPhone": "Please enter a valid phone number",
      "fileSizeReq": "Invalid file type or size. Images must be less then 5MB and video must be less then 25MB.",
      "bulkSuccessMsg": "Bulk order request submitted",
      "fileNotSupport": "An unexpected error occurred. Please try again.",
      "deliveryArea": "Please enter Area, street, sector, village",
      "tagReq": "Please select tag",
      "otherReq": "Please enter other tag",
      "deliverAddress": "Please select the delivery Address",
      "passwordsMustMatch": "Passwords don't match"
    },
    "login": {
      "welcome": "Welcome to Dentalkart",
      "getIn": "Give your phone / email to get 6-digit otp for verification ",
      "enterPhone": "Phone Number",
      "otherLogin": "Or login with",
      "emailClick": "email",
      "otpText": ", OTP",
      "registerMobileNumber": "Or login with email and password ",
      "loginLater": "l'll Login Later",
      "logIn": "Log In",
      "earnMsg1": "Sign up now and get",
      "earnMsg2": " 500",
      "earnMsg3": " Dentacoins",
      "signIn": "Sign In",
      "orCreateNewAccount": "Create New Account",
      "Or": "Or",
      "or": "or",
      "useEmai": "Use email Id",
      "useMobile": "Use mobile Number",
      "enterPhoneNumber": "Enter Phone Number",
      "enterMailId": "Enter Mail ID",
      "password": "Password",
      "forgotPassword": "Forgot Password",
      "continue": "Continue",
      "whatsapp": "Continue with WhatsApp",
      "footerTerm&Con": "By continue to login, you agree to DentalKart's ",
      "footerTermService": "Term of Services",
      "and": " and ",
      "footerPolicy": "Privacy Policy",
      "needHelp": "Need help? Contact us at ",
      "contactUs": " Connect with us",
      "supportEmail": "<EMAIL>",
      "mobileNumberPlace": "Mobile Number",
      "emailPlace": "Email ID",
      "emailPhoneLabel": "Email or Phone Number",
      "passwordPlace": "Enter New Password",
      "usePassword": "Use password",
      "useEmail": "Login with email",
      "useMobileNumber": "Login with phone number ",
      "useOTP": "Use OTP",
      "generateOTP": "Send OTP",
      "signUpWith": "Continue with",
      "noneOfTheAbove": "NONE OF THE ABOVE",
      "strongPasswordRulesHeading": "Create a strong password by following these rules:",
      "confirmPassword": "Confirm Password"
    },
    "signUp": {
      "signUp": "Sign Up",
      "otherSignUp": "Register with",
      "otpText": ", OTP",
      "orUse": "or use",
      "emailClick": "Email",
      "getIn": "Give your email address to get 6-digit otp for verification ",
      "otpClick": "OTP",
      "enterEmail": "Email address here",
      "mobileNumber": "Phone Number",
      "registerWith": " Or Register with",
      //========= ======New Design============//
      "createAccount": "Create an account",
      "Or": "or",
      "signInAcc": "sign in to your account",
      "useEmai": "Use email Id",
      "useMobile": "Use mobile Number",
      "enterPhoneNumber": "Enter Phone Number",
      "enterMailId": "Enter Mail ID",
      "ButtonCreateAccount": "Create Account",
      "referral": "Have a referral code?",
      "referralCode": "Enter referral code",
      "newPassword": "New Password",
      "confirmPassword": "Enter confirm password",
      "sendOtp": "Send OTP"
    },
    "registerForm": {
      "letStart": "Let’s Get Started",
      "alreadyAccount": "Already have an account? Sign In",
      "getStart": "Get Started As a Guest",
      "email": "Enter Email / Phone Number ",
      "emailAddress": "Enter Email",
      "firstName": "First Name",
      "lastName": "Last Name",
      "phone": "Phone number",
      "password": "Password ",
      "emailAlready": "email already exists",
      "NumberAlready": "A customer with the same mobile number already exists.",
      "alreadyExist": "Customer With These Credentials Already Exist",
      //=====================New Desig================//
      "almostThere": "Almost there ...",
      "nextbutton": "Next",
      "emailVerify": "Please enter your email ID for verification"
    },
    "otp": {
      "verification": "Please enter the 6 digit OTP sent to your ",
      "verifyDescription": "We’ve sent a code by SMS to Phone number / Email",
      "verifyEmail": "mail ID ",
      "didNotReceiveCode": "Resend code {{min}}:{{sec}}",
      "countryCode": "+91 {{phoneNumber}}",
      "editNumber": "Edit Number",
      "editMail": "Edit Email",
      "editName": "Edit| ",
      "DigitText": "OTP",
      "verified": "Verified",
      "or": "or",
      "ResendOtp": "Resend OTP",
      "mobileOtp": "Please enter the 6 digit OTP that we just sent on your ",
      "mobileOtp1": "We've sent a",
      "mobileOtp2": "6-digit verification code",
      "mobileOtp3": "to your phone number ending with",
      "mobileOtp3Email": "to your email ID ending with",
      "mobileOtp4": "Please enter the code below to continue.",
      "number": "number",
      "needHelp": "Need help? Connect with us",
      "email": "email",
      "emailID": "Email ID",
      "PhoneNumber": "Phone Number"
    },
    "profile": {
      "login": "Login",
      "you": "You",
      "title": "Personal Information",
      "orders": "Orders",
      "withOutLogin": "<EMAIL>",
      "guest": "Guest",
      "profileDetails": "Profile Details",
      "completeProfile": "Complete Profile",
      "manageAddress": "Manage Address",
      "myOrder": "My Order",
      "myWishList": "My Wishlist",
      "myRewards": "My Rewards",
      "helpCenter": "Help Center",
      "membership": "My Membership",
      "defaultAddresses": "Your default address",
      "default": "default",
      "coupons": "My Coupons",
      "profileHeader": "Personal Information",
      "firstname": "First Name",
      "lastname": "Last Name",
      "wishlist": "Wishlist",
      "account": "ACCOUNT",
      "version": "Version : ",
      "noteTitle": "Did You Know?",
      "noteDes": "You get Rs 50 everytime a friend places his first order(this is sample)",
      "buyAgain": "Buy Again",
      "purchase": "Earn 10 Dentacoins for every ₹500 purchase",
      "des": "Profile completion percentage.",
      "type": "Type",
      "dentistPlace": "Type",
      "specialityPlace": "Speciality",
      "dentist": "Dentist",
      "speciality": "Speciality",
      "bName": "Business Name",
      "bOptional": "Business name (optional)",
      "gender": "Gender",
      "anniversary": "Anniversary",
      "selectDate": "Select Date",
      "dob": "Date of Birth",
      "emailAddress": "Email Address",
      "changePassword": "Change Password",
      "setPassword": "Set Password",
      "createPassword": "Create Password",
      "currentPassword": "Current Password",
      "newPassword": "New Password",
      "confirmPassword": "Confirm Password",
      "passNote": "Your new password must:",
      "passNote1": "Password minimum length must be of 6 characters atleast.",
      "passNote2": "Not be same as your current password",
      "passNote3": "Not contain common passwords.",
      "updateYour": "Update Your",
      "steps": "(step 1 of 2)",
      "steps2": "(step 2 of 2)",
      "emailNewPlace": "Enter New Mail ID",
      "numberNewPlace": "Enter New Phone Number",
      "changePasswordStep": "Change Password (step 2 of 2)",
      "personalInfo": "Personal Information",
      "otpSend": "Enter the OTP Sent to",
      "emailID": "Email ID",
      "mobileNumber": "Phone Number",
      "verifyOTP": "Verify OTP",
      "enterPhone": "Enter your phone number",
      "couponEmptyTxt": "Unlock exclusive savings and coins today. Start saving now!",
      "rewardsEmptyTxt": "No rewards? Start earning now! Order to unlock exclusives"
    },
    "loginWithIdPassword": {
      "welcome": "Welcome to Dentalkart",
      "forgotPassword": "Forgot Password?",
      "otherLogin": "Or login with Phone / Email, OTP",
      "DentRegister": "Don’t have an account? Register",
      "password": "Password",
      "or": "OR",
      "whatsapp": "Sign in with whatsapp "
    },
    "forgetPassword": {
      "description": "Give your phone number or email for reset your password",
      "placeholder": "Phone number / Email",
      "backLogin": "Back to",
      "LogIn": " Login",
      "notValidEmail": "not a valid email.",
      "passwordChanged": "Password Changed Success"
    },
    "resetPassword": {
      "heading": "Reset your password",
      "description": "Enter your new password",
      "placeholder": "New password",
      "placeholderNew": "Confirm password",
      "apiErrors": " email doesn't exist ",
      "resetPassword": "Reset Password"
    },
    "categories": {
      "title": "See All Categories",
      "seeCategory": "See Category"
    },
    "offerItems": {
      "off": "60% off",
      "label": "Medical gowns"
    },
    "underPrize": {
      "tooth": "Tooth Under"
    },
    "bestSeller": {
      "fingerText": "Finger Pulse Oxim..",
      "heartText": "Heart rate,SpO2 PR ..",
      "headingSeller": "Best seller",
      "headingSuper": "Super Endo",
      "headingOrthodontics": "Orthodontics ",
      "headingEiTi": "EiTi ",
      "headingInstruments": "Instruments ",
      "headingTopcategories": "Top categories ",
      "headingTopBrand": "Top brands ",
      "headingFrankdental": "Frank dental ",
      "headingSalesproducts": "Sale on products ",
      "featuredBrands": "Featured Brands"
    },
    "soldProducts": {
      "soldOut": "Sold out"
    },
    "productSuggestModal": {
      "productName": "Let us know by filling in details below.",
      "productSuggest": "Didn't find what you are looking for?",
      "suggest": "Suggest a Product",
      "suggestProd": "Please suggest the products",
      "productEmail": "Enter Your Email Id (Optional)"
    },
    "homePage": {
      "view": "View",
      "headingTopBrand": "Top brands",
      "headingTopcategories": "Top categories",
      "viewAll": "All",
      "tryOffers": "Try Offers",
      "scissors": "Scissors",
      "praiseTitle": "Words of Praise from Our Customers",
      "praiseSubTitle": "Made things so much easier",
      "praiseDes": "“Ive been using DentalKar for all my dental care needs, and I couldnt be happier! The website is user-friendly, and the product selection is fantastic.“",
      "satisfiedShopper": "Satisfied Shopper",
      "lookingFor": "DIDN'T FIND WHAT YOU WERE LOOKING FOR?",
      "knowUs": "LET US KNOW BY ",
      "detailBelow": " IN DETAILS BELOW.",
      "suggestProduct": "SUGGEST A PRODUCT",
      "searchProduct": "Search Products...",
      "searchProducts": "Search Over 20,000 Dental Products"
    },
    "cartTotalPrize": {
      "Rewards": "Rewards points",
      "earned": "Earned:",
      "used": "Used:"
    },
    "googleSignup": {
      "googleSignUp": "Continue with Google "
    },
    "productItemsHorizontal": {
      "buy": "Buy",
      "above": "or above for",
      "each": "each and save",
      "off": "% Off",
      "expiry": "Expiry date:",
      "rate&review": "ratings & reviews",
      "starting": "Starting at:",
      "soldout": "Sold out"
    },
    "addressCard": {
      "defaultAddresses": "Your default address",
      "pinCodeTitle": "Use pincode to check delivery info",
      "deliveryAddress": "Delivery Address",
      "gstNo": "GSTIN NO",
      "defaultAdd": "DEFAULT ADDRESS",
      "saveAdd": "SAVED ADDRESSES",
      "addAddressNow": "ADD YOUR ADDRESS NOW",
      "billingSame": "Billing Address is same as Shipping Address",
      "addBillingAddress": "Add Billing Address",
      "updateBillingAddress": "Edit Billing Address",
      "billingTitle": "Remove Billing Address?",
      "confirmBDeleted": "Are you sure you want to remove this Address?",
      "billingMsg": "Billing address deleted",
      "billingUpdated": "Billing address updated successfully.",
      "billingAdded": "Billing address added successfully.",
      "shippingAddress": "Shipping Address",
      "billingAddress": "Billing Address",
      "addressDetails": "1. Address Details",
      "gstValid": "Address state & GSTIN state code do not match"
    },
    "deleteWishList": {
      "deleteWishlist": "Delete | ",
      "sureDelete": "Are you sure you want to delete",
      "delete": "Delete"
    },
    "editWishList": {
      "createList": "Create List",
      "newWishlist": "New Wishlist",
      "wishListType": "Wishlist type",
      "create": "Create",
      "confirm": "Confirm",
      "listName": "Name Your List",
      "done": "Done"
    },
    "filters": {
      "rate": "Ratings",
      "andAbove": "& above",
      "filters": "Filter",
      "clear": "Clear All",
      "price": "Price",
      "search": "Search",
      "quickFilters": "Quick Filters",
      "filter": "Filter",
      "filterSorting": "Filter and sorting",
      "sorting": "Sorting",
      "sortBy": "Sort By",
      "sort": "Sort",
      "selectAll": "Select All",
      "apply": "Apply",
      "clearTxt": "Clear",
      "recommended": "Recommended",
      "priceLH": "Price - Low to High",
      "priceHL": "Price - High to Low",
      "clearFilters": "Clear filters",
      "min": "Min",
      "max": "Max",
      "productsFound": "Products Found"
    },
    "shareWishList": {
      "share": "Share | ",
      "shareWith": "Share with",
      "shareText": "Share"
    },
    "retryPaymentButton": {
      "retryPayment": "Retry Payment",
      "Retrying": "Retrying... "
    },
    "helpOrderOptions": {
      "helpCenter": "Help Center",
      "support@dentalkart": "<EMAIL>",
      "unsatisfied": "Still unsatisfied, mail us at",
      "callTitle": "Want to chat now or get a call from us ?",
      "chatUs": "Chat with us",
      "talkUs": "Talk to us",
      "fvq": "Frequently visited question",
      "thingCare": "Here are few things you can take care of your own",
      "delivery": "Delivery",
      "offerText": "Earn 10 Dentacoins for every ₹500 Purchase",
      "offerText1": "Get Membership and get free delivery",
      "deleteQuestion": "How To Delete Your Account",
      "deleteAnswer": "To delete your account click on the delete button",
      "deleteAccount": "Delete Account",
      "deleteDesc": "Are You Sure You Want To Delete Your Account?",
      "cancel": "Cancel",
      "confirm": "Confirm",
      "deleteSuccess": "Your Account Has Been Deleted Successfully."
    },
    "myRewords": {
      "transactions": "No transactions yet",
      "shopNow": "Shop Now",
      "status": "status :",
      "orderNo": "Order No - ",
      "txtNo": "TXN No : #",
      "expiring": "Expiring on",
      "expires": "Expires on:",
      "total": "Total",
      "pending": "Pending :",
      "transact": "Transactions",
      "myRewards": "My Rewards",
      "monetary": "Monetary value",
      "spent": " Spent",
      "coinSpent": "Coins Spent: ",
      "earn": "Earned : ",
      "about": "To know more about Dentalkart Rewards and its functions. Click here.",
      "rewards": "Rewards",
      "transaction": "Transaction History",
      "expiryRewards": "Rewards Expiry",
      "moneyValue'": "Money Value",
      "coinsEarned": "Coins Earned :",
      "coinsLeft": "Coins Left",
      "coinsPending": "Coins Pending :",
      "expireDate": "Expiry Date - ",
      "expire2": "Expire Date: "
    },
    "addressList": {
      "deletedAddress": "Delete Address",
      "confirmDeleted": "Are you sure to delete this Address?",
      "defaultBilling": "default billing address and can not be deleted",
      "addressList": "Address Book",
      "addNewAddress": "+  Add new address",
      "addAddress": "Add your address",
      "saveAddress": "SAVED ADDRESSES",
      "country": "Country",
      "home": "Home"
    },
    "cart": {
      "viewDetails": "View Details",
      "free": "Free",
      "deliveryFee": "Delivery Fee",
      "manufacture": "Manufacture:  ",
      "unitProduct": "Unit product wt - ",
      "kg": "kg",
      "quantity": "Quantity :",
      "deleteWishlistSure": "Delete Wishlist Item",
      "delete": "Delete",
      "productsOutOfStock": "Some of products are out of stock",
      "serviceNotproviding": "We are not providing service at this postcode.",
      "empty": "It's Empty Here !",
      "offOnMrp": " %  off on MRP",
      "stockOut": "Stock out",
      "updatedCart": "Cart Updated Successfully.",
      "removeCart": "Item Removed Successfully",
      "cart": "Cart",
      "freeDelivery": "Free delivery across India on all orders above Rs 2500",
      "shippingCharges": "Shipping charges depend on weight of products",
      "ketormore": "Ketormore removed from cart",
      "undo": "Undo",
      "selected": "items selected",
      "applyCoupon": "From now onwards you can apply coupon and use rewards on the payment page.",
      "priceDetail": "Price detail",
      "secure": "'Safe and Secure Payments. Easy returns.",
      "authentic": "100% Authentic products.",
      "range": "Weight Range",
      "overall": "Note! Overall shipping charge depends on tha total items weight in tha cart.",
      "weightRange": "Weight-Range (in grams)",
      "shippingPrice": "Shipping Price",
      "product": "This product  is  stock out",
      "unavailable": "Either remove the unavailable item or replace with recommended similar item",
      "recommendations": "No recommendations available",
      "removeProduct": "Remove 1 product and proceed",
      "also": "You also may like",
      "priceDetails": "Price Details",
      "earn": "Earn",
      "pointText": "Points with products in the order.",
      "orderDelivery": "(Points to be added to wallet after successful order delivery)",
      "cartRemoveMsg": "Remove item from bag ?",
      "tipMsg": "TIP : Move to your wishlist to purchase it later!",
      "applyCoupons": "Apply Coupons",
      "bestCoupon": "Best Coupons For You",
      "typeCoupon": "Type a coupon code",
      "couponPlaceholder": "Type your coupon code here",
      "1CouponApplied": "1 Coupon Applied",
      "extra": "Extra ",
      "extraSavedOff": " OFF",
      "extraOff": "Extra ₹502 OFF",
      "offMsg": "30% OFF",
      "off": "% OFF",
      "codeHere": "CODEHERE",
      "couponHere": "Coupon Text here",
      "couponTitle": "We have exciting coupons for your next order....",
      "expired": "Expired",
      "couponEnd": "Coupons end here ....",
      "nextOrder": "We have exciting coupons for your next order",
      "totalQty": "Total Quantity",
      "yourTotalSaving": "Your Total Saving",
      "compliances": "Compliances",
      "CouponApplied": "Coupon applied successfully",
      "CouponRemoved": "Coupon Remove Successfully",
      "RewardApplied": "Reward Coins Applied",
      "RewardRemoved": "Reward Coins Removed",
      "Quantity": "Quantity",
      "youSaved": "You Saved",
      "yay": "Yay!",
      "applied": "applied",
      "thisOrder": "on this order"
    },
    "checkOut": {
      "checkOut": "Check Out",
      "delivery": "2. Items and Delivery",
      "noItems": "No. of Items",
      "orderTotal": "Order Total",
      "saved": "Saved",
      "redeemCoin": "Redeem Reward Coins",
      "applyCoin": "Apply Reward Coins.",
      "applyNote": "You can apply",
      "applyNote1": "points this order",
      "paymentMethod": "4. Payment Method",
      "goCashless": "Go Cashless ",
      "noService": "Service not available.",
      "placeOrder": "PLACE ORDER",
      "read": "I have read and agree to the ",
      "terms": "terms and condition.",
      "weAccept": "We accept",
      "needHelp": "Need help? contact <NAME_EMAIL>",
      "secPay": "Secure Payments",
      "easyReturn": "Easy Return",
      "genuinePro": "Genuine Product",
      "paymentDone": "Payment Complete !"
    },
    "categoryDetail": {
      "noProduct": "No product available",
      "selectedProduct": "No products found.",
      "adjustFilter": "Adjust your filters and try again!",
      "noSearchCategory": "No results found. Try a different category.",
      "noCategoryData": "No Category Found",
      "noSubCategoryData": "No SubCategory Found"
    },
    "getCartRewardsPoints": {
      "pointsRemoved": "Rewards points removed successfully.",
      "pointsApplied": "Rewards points applied successfully.",
      "couponRemoved": "Coupon removed successfully.",
      "couponAplied": "Coupon applied successfully.",
      "paymentMethod": "Payment Method",
      "shippingAddress": "Shipping Address",
      "recommended": "Recommended payment",
      "transferred": "[These rewards will be transferred to your account once the order is confirmed.]",
      "appliedCoupon": "Applied coupon",
      "coupon": "Coupon",
      "save": "You save",
      "removing": "Removing",
      "remove": "Remove",
      "apply": "Apply",
      "earned": "Earned Rewards",
      "products": "Coins with products in the order.",
      "reward": "Apply reward [You can only use upto",
      "transaction": "Coins in this order transaction.].",
      "read": "I have read and agree to the ",
      "terms": "terms & conditions",
      "total": "Total Payable Amount",
      "coupons": "Coupons",
      "select": "Select a coupon code to apply"
    },
    "searchProduct": {
      "showing": "Showing",
      "results": "results for",
      "find": "Didn't find what you were looking for ?",
      "filling": "Let us know by  ",
      "errorProduct": "Please enter Product Name",
      "searchProducts": "Search Products",
      "searchDentalProduct": "Search Over 20,000 Dental Products",
      "fillingdetails": "filling in details below",
      "Showingresults": "Showing 0 results for",
      "searchAlertText": "Please type at least 2 letters to search."
    },
    "otherText": {
      "corruptFile": "The file is empty or corrupted.",
      "errorDownloadFile": "An error occurred while downloading the file.",
      "reqPriceTitle": "Enter Price Quote",
      "view": "View",
      "help": "Help",
      "mrp": "Maximum Retail Price",
      "proceed": "PROCEED",
      "avlOff": "availavle offers",
      "reason": "Reason",
      "bestDeal": "Best Deal",
      "noCouponAvailable": "No Coupon Available",
      "remark": "Remark",
      "savedNews": "Saved news",
      "savedContent": "Saved Content",
      "seeAllCat": "SEE ALL CATEGORIES",
      "earned": "Earned:",
      "rate&review": "ratings & reviews",
      "fullName": "Full Name",
      "email": "Email",
      "exist": "File is Already Exist in Your Directory",
      "homePage": "Go To Home Page",
      "offerPage": "Go To Offer Page",
      "seemsLike": "seems like you have nothing in your cart",
      "suggForYou": "Suggested For You",
      "phoneNumber": "Phone Number",
      "alternativeNumber": "Alternative Phone Number",
      "password": "Password ",
      "gstNumber": "GST Number",
      "street": "Street",
      "postcode": "Postcode",
      "city": "City/District/Town",
      "region": "State - Region",
      "search": "search.....",
      "selectCountry": "Please Select Country",
      "country": "Country",
      "notFoundCountry": "Country not Found",
      "gst": "VAT/GSTIN",
      "landmark": "Landmark",
      "telephoneNumber": "Phone Number",
      "alternateTelephone": "Receiver's's Phone Number",
      "default": "Set as Default",
      "stored": "Information is encrypted and securely stored",
      "saveAddress": "SAVED ADDRESSES",
      "addNewAddress": "+  Add New Address",
      "addressList": "Address List",
      "es": "Default",
      "deletedAddress": "Delete Address",
      "confirmDeleted": "Are you sure to delete this Address?",
      "unsatisfied": "Still unsatisfied, mail us at",
      "support@dentalkart": "<EMAIL>",
      "shopNow": "Shop Now",
      "transactions": "No transactions yet",
      "membership": "My Membership",
      "downloadSuccess": "success Download",
      "package": "Package",
      "courier": "Courier ",
      "items": "items in it",
      "trackingNo": "Tracking No:",
      "trackingId": "Tracking ID ",
      "tracking": "Tracking",
      "product": "Product",
      "status": "status :",
      "placed": "Placed on ",
      "retryPayment": "Retry Payment",
      "shippingAddress": "Shipping Address",
      "summary": "Items summary ",
      "orderCancel": "Cancel order",
      "orderNow": "Order Now",
      "totalOrder": "Order total",
      "sureOrder": "Sure you want to cancel the order?.",
      "notPlaced": "You have not placed any order till now. Start ordering!",
      "s": "Order ID:",
      "finger": "Finger pulse Oximes",
      "quantity": "Quantity:",
      "two": "*2",
      "item": "item",
      "itemsIn": "items",
      "orderSummary": "Order summary",
      "buyAgain": "Buy Again",
      "memberShipPlans": "TWO MEMBERSHIP PLANS TO CHOOSE",
      "rewardBenefit": "Reward benefit",
      "freeDelivery": "Free delivery *",
      "month": "Month",
      "year": "Year",
      "plane": "plane",
      "memberBecome": "Become a member now",
      "monthOne": "₹5000/month",
      "monthTwo": "₹10000/month",
      "monthThree": "₹15000/month",
      "plusMember": "As a Plus Member, you will save upto.",
      "breakDown": "Break Down",
      "total": "Total",
      "assumingMonth": "*Assuming 1 order per month.",
      "txtNo": "Txn No.",
      "expiring": "Expiring on",
      "expires": "Expires on:",
      "pending": "Pending :",
      "spent": "Spent :",
      "earn": "Earned : ",
      "about": "To know more about Dentalkart Rewards and its functions. Click here.",
      "rewards": "Rewards",
      "transaction": "Transaction History",
      "expiryRewards": "Rewards Expiry",
      "transact": "Transactions",
      "wishlist": "wishList",
      "validMobile": "not a valid mobile no",
      "share": "Share | ",
      "shareWith": "Share with",
      "lorem": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,",
      "shareText": "Share",
      "createList": "Create List",
      "newWishlist": "Create a new wishlist",
      "wishListType": "Wishlist type",
      "create": "Create",
      "done": "Done",
      "deleteWishlist": "Delete | ",
      "sureDelete": "Are you sure you want to delete",
      "delete": "Delete",
      "deleteWishlistSure": "Delete Wishlist Item",
      "selectAll": "Select All",
      "addToCart": "Add to cart",
      "deleteAll": "Delete All",
      "emptyCart": "Your Cart is Empty",
      "looksLike": "Looks like you haven't added anything to your cart yet",
      "itemAdd": "ADD ITEMS",
      "wishList": "Wish List",
      "wishListOne": "Wish List 1",
      "orderConfirmed": "Order Confirmed",
      "orderSuccessfully": "Order placed successfully!",
      "orderIDs": "Order ID: #21520",
      "trackManage": "Track and manage your order easily",
      "orderView": "View Order",
      "continueShopping": "Continue Shopping",
      "expected": "Excpected price should not be less then Rs",
      "fileSaved": "file Saved in downloads.",
      "defaultAddresses": "Your default address",
      "errorProduct": "Please enter Product Name",
      "showing": "Showing",
      "results": "results for",
      "clear": "Clear All",
      "rate": "Ratings",
      "andAbove": "& above",
      "filters": "Filters",
      "price": "Price",
      "paymentMethod": "Payment Method",
      "recommended": "Recommended payment",
      "card": "Credit/Debit Card",
      "reward": "Apply reward",
      "applyCoupon": "Apply coupon code",
      "earnReward": "Earn reward 275 Points with products in the order.",
      "coupons": "Coupons",
      "selectCoupon": "Select a coupon code to apply",
      "Retrying": "Retrying... ",
      "addNewAddressTxt": "Add New Address",
      "changeAddress": "Change Address",
      "selectDeliveryAddress": "Select Delivery Address",
      "options": "Options",
      "invoiceSave": "Invoice Saved in download",
      "pdfDes": "Invoices download complete.",
      "expireDate": "Expire Date",
      "gotoTop": "Go To Top",
      "rupee": "₹",
      "takePhoto": "Take Photo",
      "gallery": "Gallery",
      "noImage": "Please select an image first"
    },
    "orderListing": {
      "orderSummary": "Order",
      "myOrder": "My Orders",
      "buyAgain": "Buy Again",
      "empty": "It's Empty Here !",
      "notPlaced": "You have not placed any order till now. Start ordering!",
      "orderId": "Order Id : ",
      "details": "View Details",
      "finger": "Finger pulse Oximes",
      "quantity": "Quantity:",
      "two": "*2",
      "item": "item",
      "itemsIn": "items",
      "filterTitle": "Filter Orders",
      "orderAmount": "Order Amount - ₹",
      "earnedCoins": "Earned Coins",
      "savedRs": "Saved",
      "earned": "Earned",
      "totalProducts": "Total Products",
      "orderID": "Order ID",
      "order": "Order",
      "orderedOn": "Ordered On",
      "requestedOn": "Requested On",
      "returnId": "Return ID",
      "earnNote": "Earn 10 Dentacoins for every ₹500 purchase",
      "searchProductName": "Search by order id, Product name...",
      "searchOrder": "Search by return id, order prod....",
      "filter": "Filter",
      "orderAmount1": "Order Amount",
      "cancelOrder": "Cancel Order",
      "cancelOrderMsg": "Are you sure you want to cancel this order?",
      "thankYouOrder": "Thank you for placing an order with us.",
      "noOrder": "No Orders Right Now",
      "noReturn": "No Return Found",
      "didForget": "Did You Forget",
      "brandProduct": "Brand/Product",
      "closedOn": "Closed On",
      "cancelledOn": "Cancelled On",
      "returnedOn": "Returned On",
      "deliveredOn": "Delivered On",
      "packageHand": "Package Was Handed To Resident",
      "bestDeal": "Best Deal",
      "fromOrder": "You are viewing orders from ",
      "jan25": "January 2025 to today",
      "viewPrevious": "View Previous Orders",
      "viewLatest": "View Latest Orders",
      "tillOrder": "You are viewing orders till ",
      "dec24": "December 2024",
      "coinNote": "Note: Reward coins will be credited only after the full order has been delivered."
    },
    "buyAgain": {
      "searchByName": "Search By Name",
      "itemsAdded": "Items Added",
      "itemAdded": "Item Added"
    },
    "manageAddress": {
      "address": "Address",
      "newAddress": "ADD NEW ADDRESS",
      "edit": "EDIT",
      "delete": "DELETE",
      "manageAddress": "Manage Address",
      "fullName": "Full Name",
      "pincode": "Pincode",
      "street": "Street Address",
      "postcode": "Postcode",
      "fullAddress": "Clinic Name - House / Building-Apartment",
      "city": "City",
      "region": "State - Region",
      "search": "search.....",
      "selectCountry": "Please Select Country",
      "country": "Country",
      "notFoundCountry": "Country not Found",
      "gst": "VAT/GSTIN (Optional)",
      "gstIn": "GSTIN (Optional)",
      "landmark": "Landmark (Optional)",
      "telephoneNumber": "Phone Number",
      "alternateTelephone": "Receiver's Phone Number",
      "default": "SET AS DEFAULT",
      "stored": "Information is encrypted and securely stored",
      "addressUpdated": "Address updated successfully.",
      "addressAdded": "Address added successfully.",
      "alternateNo": "Receiver's Phone number",
      "phoneNo": "Phone no",
      "addAddress": "Add Delivery Address",
      "editAddress": "Edit Address",
      "selectRegion": "Select State",
      "selectState": "State",
      "addAlternate": "Add Receiver's Phone number",
      "addLandmark": "Add Landmark",
      "addGSTIN": "Add GSTIN",
      "location": "Area, street, sector, village",
      "moreDetails": "Add More Details",
      "updateMoreDetails": "Update Delivery Address",
      "confirmLocationTitle": "Confirm Map Pin Location",
      "other": "Other",
      "updateDeliveryLocation": "Update Delivery Location"
    },
    "orderReturn": {
      "orderItem": "ORDER ITEMS",
      "returnedItem": "RETURNED ITEMS",
      "attachments": "Attachments",
      "items": "Items",
      "returnId": "Return ID",
      "select": "Select",
      "super": "SuperEndo Blue Files 25mm Assorted (Pack of 6)",
      "qty": "Qty *",
      "reason": "Reason *",
      "selectOption": "Select Option",
      "action": "Action *",
      "return": "Returned Item",
      "noReturn": "You don't have any return items",
      "wantGold": "Waldent Gold LED Special Edition Airotor (W-154)",
      "qty1": "Qty - ",
      "OrderNo": "Order no - ",
      "date": "Date - ",
      "rejected": "Status - ",
      "reasonProduct": "Reason - ",
      "actionProduct": "Action-",
      "remarks": "Remarks :",
      "dontHaveReturnItem": "You don't have any return items",
      "subReason": "Sub-Reason",
      "termsAndCondition": "Terms and condition for return",
      "iAgreeTermsAndCondition": "I agree with terms and condition.(Click here)",
      "termsAndConditionModal": "You can return/replace a product within 10 days after the date of delivery. In order to return the product you need to ensure that all the product packaging and contents should be available and not discarded. \n \nPlease note that once the product is returned back to our warehouse we will do a quality check to ensure that the product is unused and in mint condition, once approved only then the refund or replacement will be initiated else the same product will be returned back to you.",
      "selectProduct": "Select Product",
      "requestSaved": "Request Saved",
      "orderAmount": "Order Amount - Rs",
      "fillDetails": "Fill Details",
      "returnQuantity": "Return Quantity",
      "selectReason": "Select Reason",
      "selectSubReason": "Select Sub Reason",
      "selectAction": "Select Action",
      "attachment": "Attachment",
      "attachmentTitle": "Select Your Source",
      "attachmentDes": "Choose to upload from your gallery or take a new photo with your camera",
      "browseFiles": "Browse Files",
      "useCamera": "Use Camera",
      "returnSuccess": "Return request raised successfully.",
      "returns": "Returns",
      "totalQty": "Total Qty",
      "returnableQty": "Returnable Qty left",
      "amount": "Amount - ",
      "addDescription": "Add Description",
      "description": "Description"
    },
    "reviewRating": {
      "review": "Review",
      "reviews": "Reviews",
      "rateProduct": "Rate the product",
      "opinion": "Please share your opinion about DentalKart",
      "title": "Write your title here...",
      "description": "Write your description here...",
      "purchased": "Haven't purchased this product?",
      "allowed": "Sorry! You are not allowed to review this product since vou haven't bought it on Dentalkart.",
      "star": "star",
      "dateFormate": "DD MMM",
      "rating": "Rating"
    },
    "orderTrack": {
      "trackOrder": "Track Order",
      "package": "Package 1",
      "status": "Status:",
      "tracking": "Tracking No",
      "shipwrecked": "Sent by - Shipwrecked",
      "track": "Track Your Order",
      "return": "Return",
      "expected": "Expected date of Delivery ",
      "date": "- Dec 29, 2020",
      "items": "Items in this order",
      "forget": "Don’t forget to rate",
      "opinion": "Give your opinion about our app",
      "trackingStatus": "Tracking",
      "orderDetails": "Order Details",
      "tryAgainText": "Payment Failed",
      "retryNowText": "Retry Now.",
      "orderedID": "Ordered ID",
      "orderedPlacedOn": "Order Placed On",
      "mobileNo": "Mobile No",
      "gstIn": "GSTIN",
      "orderTotal": "Order Total",
      "coins": "Coins",
      "earned": "Earned",
      "spent": " Spent",
      "helpCenter": "Help Center",
      "helpDes": "Reach out to us at ",
      "downloadInvoice": "Download Invoice",
      "requestSubmitted": "Request Submitted",
      "enterDate": "Enter Date",
      "enterPickupDate": "Enter pickup date",
      "invalidDate": "Invalid date format. Use DD/MM/YY.",
      "diffDate": "Pick up date can be different then the given date due to external circumstances.",
      "noOfItems": "No. Of Items",
      "awbNo": "AWB No",
      "requestFor": "request has been rejected for",
      "products": "products",
      "pickUpDate": "Pick up Date Modal",
      "sku": "SKU",
      "totalItem": "Total Item",
      "shipment": "Shipment",
      "shipmentNote": "Note: Your Order is split into multiple shipment for faster delivery",
      "estOn": "Arriving On",
      "cancelOn": "Cancelled On",
      "closeOn": "Closed On",
      "totalDiscount": "Total Discount",
      "on": "On"
    },
    "newsDetails": {
      "showMore": "Show More",
      "like": "Like"
    },
    // "news": {"recent": "Recent Topic", "savedNews": "Saved news"},
    "orderConfirmed": {
      "orderConfirmed": "Order Confirmed",
      "orderSuccessfully": "Order placed successfully!",
      "orderIDs": "Order ID: #21520",
      "trackManage": "Track and manage your order easily",
      "orderView": "View Order",
      "continueShopping": "Continue Shopping"
    },
    "multimedia": {
      "multimedia": "Multimedia"
    },
    "profileDetails": {
      "verification": "Please enter the verification code sent to",
      "updatedProfileSuccessfully": "Successfully updated profile details!",
      "profileDetails": "Profile Details",
      "passwordUpdated": "Password Updated Successfully",
      "passwordNotUpdated": "Password Not Updated",
      "fullName": "Full Name",
      "email": "Email",
      "phoneNumber": "Phone Number",
      "gstNumber": "GST Number",
      "save": "Save",
      "confirmOtp": "Confirm Otp",
      "verify": "Verify",
      "emailVerification": "Email Verification",
      "mobileVerification": "Mobile Verification",
      "logoutMessage": "Are you sure you want to Logout this account?",
      "createPasswordMsg": "Your password has been created"
    },
    "chooseCountry": {
      "chooseCountry": "chooseCountry",
      "search": "Search..."
    },
    "allBrands": {
      "allBrands": "All Brands",
      "featuredBrands": "Featured Brands",
      "searchBrands": "Search for your favourite brands.",
      "featured": "Featured",
      "noData": "No result found for",
      "brands": "brands",
      "searchBrand": "Search Your Brand..."
    },
    "faqs": {
      "question": "Question",
      "answer": "Answer :",
      "readLess": "Read Less",
      "readMore": "Read More",
      "questionSuccess": "Thank You ! \nYour Question Has Been Successfully Updated"
    },
    "address": {
      "deliver": "Deliver To",
      "deliverIND": "Deliver to IN",
      "deliveryOptions": "Check Delivery Options",
      "change": "Change",
      "locations": "Choose your locations",
      "availability": "Check Service Availability",
      "selectDeliveryLocation": "Select a delivery location to see product availability and delivery options on payment page.",
      "enterPin": "Enter Pin",
      "editAddress": "Add/Edit an address",
      "seeAddress": "Login to see Your address",
      "or": "Or",
      "addYourAddress": "Add Your Address",
      "countrySelect": "Select your country",
      "deliveryDays": "Delivery Days mentioned here are valid for these products only.",
      "deliveryDays1": "Days required to actually dispatch an order from our warehouse may depend on various factors like",
      "deliveryDays2": "1. Other products in an order. (In case an order has 5 different  items together, actual dispatch date from warehouse may depend on the product with longest dispatch days.)",
      "deliveryDays3": "2. General Holidays & Shipping partner schedules.",
      "deliveryDays4": "3. Dental Exhibitions (Product supply from manufacturers is affected during or after exhibition.)",
      "deliveryDays5": "4. Natural Calamities (floods, earthquakes, landslides etc) or embargo situations.",
      "deliveryPartner": "Delivery Partner Fee is dependent on Item Total and Pincode.",
      "deliveryPartner1": "For non-members: Add products worth ₹2500 or more to your cart to qualify for free delivery.",
      "deliveryPartner2": "For plus members: Add products worth ₹499 or more to your cart to qualify for free delivery.",
      "cod": "COD enabled for select products only",
      "cod1": "If all items in your cart are COD enabled, then COD service will be available on checkout.",
      "cod2": "The eligible cart value for COD falls within the ₹200 to ₹20,000 range.",
      "compliances": "Update Your GST Information",
      "compliancesSub": "To facilitate accurate invoicing and maintain tax compliance, we kindly request that you provide your GST number.",
      "compliances1": "Why is this necessary?",
      "compliancesSub1": "Your GST number enables us to issue precise invoices for your order, ensuring adherence to tax regulations.",
      "compliances2": "Please fill out your Registration Number / ID",
      "compliancesSub2": "As you proceed with your order, kindly enter your Medical registration number or Medical ID.",
      "wrongPin1": "Delivery Days mentioned here are valid for these products only.",
      "wrongPin2": "Days required to actually dispatch an order from our warehouse may depend on various factors like:",
      "wrongPin3": "1. Other products in an order. (In case an order has 5 different items together, the actual dispatch date from the warehouse may depend on the product with the longest dispatch days.)",
      "wrongPin4": "2. General Holidays & Shipping partner schedules.",
      "wrongPin5": "3. Dental Exhibitions (Product supply from manufacturers is affected during or after exhibition.)",
      "wrongPin6": "4. Natural Calamities (floods, earthquakes, landslides etc) or embargo situations.",
      "numInfo": "Add an alternate contact to never miss an important update.",
      "numInfo1": "We’ll only use it if we can't reach your primary number your info stays 100% private.",
      "locationSearch": "Search for area , street name..",
      "currentLoc": "Use current location",
      "locationTitle": "Update Precise Location 📍",
      "locationMsg": "Use our map feature for accurate delivery locations and faster service.",
      "locPerTitle": "Enable location access",
      "locPerMsg": "We need access to your location to search near you",
      "deliverHere": "Deliver to here",
      "updateLocTitle": "Update Location-📍 ",
      "updateLocSubTitle": "Precision Unlocked!",
      "updateLocMsg": "Are you sure you want to skip adding precise delivery location?",
      "yesI": "Yes",
      "noI": "No",
      "youHere": "You Are Here",
      "kmWay": "Km Away",
      "orderHere": "Order Will Be Delivered Here",
      "placeMap": "Place The Pin Accurately On The Map",
      "enableLocation": "Enable location and tap 'Use Current Location'.",
      "manuallyLocation": "Enter Location Manually",
      "sAddress": "Selected address is",
      "cLocation": "km away from your current location.",
      "pLocation": "Pin location is"
    },
    "packageItem": {
      "quantity": "Quantity:",
      "unit": "Unit Price -",
      "total": "Total price:",
      "rewards": "Rewards:"
    },
    "productItems": {
      "starting": "Starting at:",
      "off": "% Off",
      "unavailable": "Unavailable",
      "check": "Check other product +",
      "freeProduct": "Enjoy Free Product"
    },
    "wishList": {
      "defaultList": "Default List",
      "addProducts": "+ Add Products",
      "wishList": "Wishlist",
      "shareWithMe": "Shared With Me",
      "shared": "Shared",
      "wishlistName": "Wishlist name",
      "delete": "Delete",
      "deleteWishlistSure": "Delete Wishlist Item",
      "addItemWishlist": "Add Items From Wishlist",
      "selectAll": "Select All",
      "addToCart": "Add to cart",
      "deleteAll": "Delete All",
      "shareText": "Share",
      "wishlist": "wishlist",
      "emptyCart": "Your Cart is Empty",
      "looksLike": "Looks like you haven't added anything to your cart yet",
      "itemAdd": "ADD ITEMS",
      "errorMsg": "This product has variants, please select specific product to be added in cart on detail page",
      "specific": "This product has variants, please select specific product to be added in cart on detail page",
      "edit": "Edit",
      "rename": "Rename",
      "make": "Make",
      "editWishList": "Rename",
      "yourLastName": "Change Your List Name",
      "deleteWishListMsg": "Are you sure want to delete \nthe wishlist?",
      "deleteProductMsg": "Are you sure you want to delete this product from wishlist?",
      "shareWishList": "Share Wishlist",
      "shareProduct": "Share Product",
      "moveAnotherWish": "Move To Another Wishlist",
      "myLists": "My Lists",
      "friends": "Friend's",
      "default": "Default",
      "items": "items",
      "yourList": "Your List",
      "noProductsAvailable": "Add items here",
      "productMoved": "Product Moved to",
      "confirmDefault": "Are you sure that you want",
      "confirmDefault1": "as default wishlist?",
      "noShareWishlist": "No wish list is shared with you"
    },
    "placeHolder": {
      "fullName": "Full Name",
      "emailId": "Enter Email",
      "phoneNumber": "Enter Number",
      "alternativeNumbers": "Alternative Numbers",
      "password": "Password ",
      "gstNumber": "Enter tax/GST number ",
      "emailMobile": "email / mobile no.",
      "shoppingList": "enter name ex. Shopping List",
      "selectProduct": "Select a List",
      "dateFormat": "YYYY-MM-DD"
    },
    "allText": {
      "Rewards": "Rewards points",
      "empty": "It's Empty Here !",
      "currentlyItem": "You have no items currently. Start adding!"
    },
    "toastMassages": {
      "copyLinkText": "Link Copied Successfully",
      "codeCopiedText": "Code Copied Successfully",
      "orderCopiedText": "Copied successfully",
      "suggestMsg": "Thank You ! \nYour suggestion is successfully Added",
      "saveSuccess": "Save Success",
      "loginInfo": "Please Login First",
      "likeSuccess": "Liked Success",
      "postedSuccess": "Question Posted Successfully",
      "loginMessage": "Please Login",
      "logOut": "You Have Been Logged Out Successfully",
      "informationUpdated": "Information updated successfully",
      "success": "success create",
      "addressDeleted": "Address deleted successfully",
      "orderCancel": "Order Cancelled SuccessFully",
      "shareSuccess": "share success",
      "createSuccess": "User wishlist created successfully",
      "updateSuccess": "User wishlist updated successfully",
      "removeSuccessfully": "Remove Successfully",
      "deleteProduct": "Delete Product",
      "defaultList": "Default List",
      "subscribed": "Subscribed successfully",
      "suggestionADD": "Thank You ! \nYour suggestion is successfully Added",
      "provide": "You must provide your rating to rate this product",
      "required": "Title is a required field.",
      "descriptionRequired": "Description is a required field.",
      "loginSuccess": "Login Successfully",
      "addedToWishlist": "Added to Wishlist",
      "accountSuccess": "Account created successfully",
      "socialMsg": "This App is not installed on your phone !",
      "referLogin": "You need to log in first before referring this product",
      "addressLogin": "You need to login first before select the address",
      "processOrder": "You need to login first before order proceed",
      "wishlistLogin": "You need to login first before adding to wishlist",
      "rateLogin": "You need to login first before rate this product",
      "feedbackMessage": "Thank you for your Feedback",
      "defaultMessage": "Your default address has been updated."
    },
    "suggestProduct": {
      "suggestProduct": "Suggest Product",
      "looking": "Didn't find what you are looking for? Let us know by filling in details below",
      "productName": "Product Name",
      "optional": "Brand Name (Optional)",
      "comment": "Comment (Optional)",
      "url": "URL (Optional)",
      "myWishList": " WishList",
      "productEmail": "Enter Your Email Id (Optional)",
      "existingProductPre": "We already have",
      "existingProductPost": "products with this name, ",
      "clickHere": "click here",
      "view": " to view them."
    },
    "memberShip": {
      "rewardBenefit": "Reward benefit",
      "freeDelivery": "Free delivery *",
      "membership": "Membership",
      "memberShipPlans": "TWO MEMBERSHIP PLANS TO CHOOSE",
      "month": "Month",
      "months": "Months",
      "year": "Year",
      "renew": "Renew Your Membership Now",
      "renewPlan": "Renew Plan",
      "plane": "plan",
      "frequency": "Frequenty Asked Questions",
      "memberBecome": "Become a member now",
      "monthOne": "₹5000/month",
      "monthTwo": "₹10000/month",
      "monthThree": "₹15000/month",
      "plusMember": "As a Plus Member, you will save upto.",
      "breakDown": "Break Down",
      "total": "Total",
      "assumingMonth": "*Assuming 1 order per month.",
      "q": "Q.",
      "conditions": "Terms & conditions",
      "plane1": "1. Plus membership duration begins from the date of purchase.",
      "plane2": "2. One Plus membership applicable to only one account.",
      "plane3": "3. Plus membership plan fee is non-refundable. Amount once paid will not be refunded under any circumstances.",
      "plane4": "4. Buyer can choose if Plus membership has to be renewed.Auto-renewal will not be implemented on its own.",
      "plane5": "5. Plus membership depend upon date of purchase and inventory availability.",
      "plane6": "6. Benefits will not be applicable on any purchase before the purchase of Plus membership or after membership gets over.",
      "plane7": "7. Plus benefits cannot be redeemed in any other way than mentioned.",
      "yah": "YAY!!",
      "become": "Become a DentalKart Plus",
      "member": "member and start saving more.",
      "currentPlan": "Current Plan",
      "no": "Oh no!",
      "membershipHasExpired": "We hate to see you go, but your membership has expired.",
      "savings": "Total savings till now",
      "premiumMember": "Be a Dentalkart premium member and enjoy multiple benfits like free shipping, double rewards value and many more.",
      "details": "View Details",
      "memberShipOrders": "MEMBERSHIP ORDERS",
      "active": "ACTIVE",
      "expired": "EXPIRED",
      "orderId": "Order Id:",
      "purchased": "Purchased On:",
      "validity": "Validity",
      "notes": "Elevate Your Dental Journey with DentalKart",
      "benefits": "BENEFITS",
      "freeShipping": "FREE SHIPPING",
      "freeShippingDes": "Free Shipping To Dentalkart Members On Every Order.",
      "doubleReward": "DOUBLE REWARD VALUE",
      "choosePlan": "Choose your Plan",
      "howWorks": "How it works",
      "coin1Rupee": "1 Coin = 1 Rupee",
      "plusMembership": "PLUS MEMBERSHIP",
      "plusMembershipDes": "Double your reward benefits with plus membership",
      "plusNonMembership": "NON-MEMBERS",
      "plusNonMembershipDes": "Reward Value gets half for non-members.",
      "coinHalfRupee": "1 Coin = 0.5 Rupee",
      "faqTitle": "Frequently Asked Questions",
      "showLess": "Show Less",
      "showMore": "Show More",
      "terms": "Terms and Conditions",
      "previousPlan": "Previous Plan",
      "daysLeft": "Days left",
      "validTill": "Membership Valid Till",
      "endOn": "Membership Ended On",
      "purchasedOn": "Purchased on",
      "savingNow": "Total Saving Till Now.",
      "history": "Membership History",
      "rs": "Rs",
      "status": "Status",
      "continueWith": "Continue with",
      "premiumTxt": "be a dentalkart premium member and enjoy multiple benefits like",
      "freeShipping1": "Free Shipping",
      "doubleRewardCoins": "Double Reward Coins",
      "valueAndManyMore": "Value And Many More."
    },
    "myOrder": {
      "downloadSuccess": "success Download",
      "package": "Package",
      "items": "items in it",
      "trackingNo": "Tracking No:",
      "status": "status :",
      "track": "Track",
      "retryPayment": "Retry Payment",
      "shippingAddress": "Shipping Address",
      "summary": "Items summary ",
      "inOrder": "in this order",
      "invoice": "Invoice",
      "totalOrder": "Order total",
      "orderCancel": "Cancel order",
      "sureOrder": "Sure you want to cancel the order?.",
      "orderId": "Order Id:",
      "myOrder": "My Orders",
      "expectedDelivery": "Expected delivery :",
      "placed": "Placed on "
    },
    "PDP": {
      "noQuesFound": "No Question Found",
      "productdetails": "Product Details",
      "offerUnlock": "Offer Unlocked",
      "greaterRs": "Or above",
      "enterYourPincode": "Enter Your Pincode",
      "saveingtext1": "Overall you save ₹ ",
      "related": "Related Product",
      "moreBrand": "More From This Brand",
      "notify": "Subscribed Successfully",
      "saveingtext2": " on this product",
      "finalPrice": "Special Price",
      "similar": "VIEW SIMILAR",
      "addedTax": "(incl. of all taxes)",
      "freeBie": "Get Freebie",
      "outStock": "Out Of Stock",
      "expected": "Excpected price should not be less then Rs",
      "enterQuestion": "Enter Your Question",
      "doubtsProduct": "Have doubts regarding this product?",
      "postQuestions": "Post Your Question",
      "description": "Description",
      "deliveryDetails": "Delivery Details",
      "overallPurchase": "Overall purchase must be greater than Rs. 10000",
      "storagePermission": "Storage Permission Required",
      "downloadFile": "Application needs access to your storage to download File",
      "storagePermissionGranted": "Storage Permission Not Granted",
      "fileDownloadComplete": "file download complete.",
      "productQuantity": "Select a product quantity",
      "selectQuantity": "Select quantity",
      "itemAddCart": "Item Added to Cart",
      "ViewCart": "View Cart",
      "off": "%",
      "soldOut": "Sold out",
      "buy": "Buy",
      "above": "or above for",
      "each": "each and save",
      "qty": "Qty",
      "notified": "Click here to get notified when the product is back in stock.",
      "login": "Please login !",
      "installed": "'This App is not installed in your phone !",
      "details": "Details",
      "ledScalers": "LED Scaler",
      "manufacturer": "Manufacture",
      "waldent": "Waldent",
      "rating": "Ratings ",
      "reviews": "Reviews",
      "quantity": "Quantity",
      "dispatchTIme": "Dispatch time",
      "workingDays": "working days",
      "smilerProduct": "Similar product",
      "weldedMax": "Waldent Max Piezo 3+ Ultrosonic Scaler",
      "bulkProduct": "Do you need bulk product?",
      "sizes": "Size",
      "productDescription": "Product description",
      "viewAll": "viewAll",
      "questions": "Question",
      "search": "search Questions",
      "postedQuestion": "Question post successfully",
      "desecration": "Waldent introduces Premium Taper Gold Rotary Files for enhancing the efficiency of dentists across the world. The especially designed files hold.",
      "ratingsReviews": "Ratings & Reviews",
      "rateProduct": "Rate Product",
      "verifiedBuyers": "Verified Buyers",
      "customerPhotos": "Customer Photos",
      "customerReview": "Customer Review",
      "mostrelevant": "Most Relevant",
      "mostRecent": "Most Recent",
      "viewAll1": "View all",
      "FAQs": "FAQs",
      "searchByName": "Search by name",
      "searchQuestion": "Search Question",
      "question": "Question",
      "answer": "Answer",
      "hotSeller": "Hot Seller",
      "frequentlyMsg": "Frequently bought together",
      "buyBoth": "Buy Both : ",
      "add2Cart": "ADD 2 ITEMS TO CART",
      "iCart": "ITEMS TO CART",
      "bulkNow": "GET BULK QUOTE NOW",
      "aboutProduct": "Would you like to tell us about the product?",
      "moreQuantity": "Want to buy even more quantity ?",
      "paymentOptions": "Payment Options",
      "dentalKartBenefits": "DentalKart Benefits",
      "knowMore": "Know More",
      "notifyMe": "Notify Me",
      "feedback": "Feedback",
      "viewAllDetails": "View all details",
      "productHighlights": "Product Highlights",
      "features": "Features",
      "offers": "OFFERS",
      "expiryDate": "Expiry Date",
      "startingAt": "Starting at",
      "referEarn": "Refer & Earn",
      "subtotal": "Subtotal : ",
      "mrp": "MRP",
      "off1": "OFF",
      "item": "Item",
      "offer": "Offer",
      "rupee": "₹ ",
      "addSavings": "Add On Savings",
      "questionAnswer": "Post Your Questions",
      "postQuestion": "Post your question",
      "questionSuccess": "Question posted successfully.",
      "questionsAndAns": "Questions and Answer",
      "ratingReviews": "Rating and Reviews",
      "helpCenter": "help center",
      "selectBought": "Select the product you have bought",
      "rateThisProduct": "Enter Title",
      "productRate": "Rate This Product",
      "reviewThisProduct": "Review this product",
      "rateNot": "Oops! You need to buy the product before rating it.",
      "rateLogin": "Please login to rate this product",
      "sampleText": "Sample Text",
      "similarProduct": "Similar Product",
      "addToCart": "ADD TO CART",
      "buyNow": "BUY NOW",
      "subTotal": "sub total",
      "expiryDateInfo": "The Expiration date mentioned here is based on our latest warehouse stock. Dentalkart tries to always deliver the latest lot (i.e longest expiry) of products directly procured from the manufacturer.",
      "expiryInfo": "In case of short expiry products, it is always mentioned clearly .",
      "sameDayDelivery": "Same Day Delivery",
      "CopiedSuccessfully": "Copied Successfully !",
      "selectedQuantity": "Please Select Quantity",
      "deliveryfee": "Delivery Fee",
      "viewMore": "View More",
      "viewLess": "View Less",
      "noDataFound": "No result found",
      "noResultFound": "No results found",
      "congratsMessage": "Congratulation Offer Unlock",
      "returnable": "10-Day Returnable",
      "nonReturnable": "Non Returnable",
      "recommended4U": "Recommended For You"
    },
    "bulk": {
      "bulkTitle": "Only for overall purchase values above ₹ 10,000",
      "namePlace": "Enter your name",
      "pricePlace": "Expected Price Per Piece",
      "emailPlace": "Enter your email",
      "phonePlace": "Enter your Phone Number",
      "pinCodePlace": "Enter your pincode",
      "addressPlace": "Enter your address",
      "quantityPlace": "Enter quantity",
      "bulkSuccess": "Bulk order request submitted successfully",
      "lessPrice": "Expected price should not be less than Rs",
      "greaterPrice": "or also not greater than Rs",
      "bulkSuccessMsg": "Thank You ! \nWe will get back to you within 48 hours You will get call from dentalkart team or either from Product Company"
    },
    "feedback": {
      "feedbackSuccess": "Thank You ! \nYour suggestion is successfully Added",
      "valuableFeedbackSuccess": "Thank You for Your Valuable Feedback",
      "valuableFeedback": "Valuable Feedback",
      "otherFeedback": "Product Feedback",
      "Feedback": "Enter Your Feedback",
      "productPrice": "Product Price",
      "productPriceFeedBack": "Enter Price",
      "productQuality": "Product Quality",
      "productQualityFeedback": "Enter Quality",
      "purchasedProduct": "Have You Recently Purchased This Product ?",
      "feedbackNote": "we'd love to here your thoughts and feedback!"
    },
    "payment": {
      "emiPlan": "EMI Plans",
      "payLater": "Pay Later",
      "creditCardEMI": "Credit Card EMI",
      "debitCardEMI": "Debit Card & Cardless EMI",
      "creditCardEMIs": "Credit Card EMIs",
      "debitCardEMIs": "Debit Card EMIs",
      "how": "How to avail?",
      "noEmi": "No EMI plans available on Debit Card",
      "cardlessEMI": "Cardless and Other EMIs",
      "checkOption": "Enter your phone number to check eligible option",
      "enterPhone": "Enter you phone number",
      "secure": "100% secure by Easypay",
      "resendOtp": "Resend Otp",
      "otherBanks": "Other Banks",
      "bankName": "State Bank of India",
      "selectPaymentMethod": "Select Payment method",
      "payFail": "Payment Failed to Process",
      "tryAgain": "Please try again.",
      "verifyingPayment": "Verifying your payment, please wait"
    },
    "shorts": {
      "title": "Shorts",
      "videoNotFound": "Videos not found",
      "viewDetails": "View Details",
      "featuredProducts": "Featured products",
      "loginLikeVideo": "You need to login first before liking the video",
      "loginCommentVideo": "You need to login first before commenting on the video",
      "comments": "Comments",
      "addComment": "Add a comment",
      "allProducts": "All Products",
      "category": "CATEGORY",
      "savedShorts": "Saved Shorts",
      "saved": "Saved",
      "disclaimer": "The content is intended solely for use by qualified dental professionals. It is not meant for home diagnosis, self-treatment, or use by non-professionals. Always consult a licensed dentist for any dental concerns. This information should not replace professional clinical judgment."
    },
    "myReferral": {
      "refer": "Refer and Earn",
      "copyLink": "Copy Link : ",
      "usersReferred": "Users Referred",
      "productsReferred": "Products Referred",
      "coinsEarned": "Coins Earned",
      "referRewards": "REFER AND \nEARN REWARDS",
      "introduce": "Introduce a friend to Dentalkart and you’ll be credited 250 reward coins in your account.",
      "shareReferral": "Share your referral link and introduce your friend to dentalkart.",
      "referralCode": "Refer By Code",
      "referralLink": "Referral Link",
      "or": "OR",
      "shareVia": "Share Via",
      "termsConditions": "Terms & Conditions",
      "afterReferring": "If you do not receive coins after referring a friend/product. please send us an email with your details and your friends detail.",
      "availability": "This offer is subject to availability until stocks last or during the offer period, whichever is earlier.",
      "discretion": "The offer may vary from time to time at the company’s discretion.",
      "material": "Your name and image may be used in any publicity material related to this offer.",
      "through": "The offer is valid only if the purchase is made through the referral link or unique personalized coupon code.",
      "rewardCoins": "Share this product with your friend and get reward coins.",
      "alsoShareVia": "You Can Also Share Via",
      "yourActivity": "Your Activity",
      "referralCode1": "Referral Code",
      "referNote1": "Get rewarded with a whopping ",
      "referNote2": " coins when a user makes a  successful purchase using your referral code !",
      "end": "END"
    },
    "page404": {
      "pageNotFound": "404 - PAGE NOT FOUND",
      "looking": "The page you are looking for might have been removed had its name changed or is temporarily unavailable.",
      "goToHome": "Go to home page",
      "goToOffer": "Go to offer page",
      "title": "Seems Like There Is Some Error..",
      "des": "Don’t Worry We Have Got You Covered",
      "somethingwentwrong": "Something went wrong",
      "tryagain": "Try Again"
    },
    "infoHeader": {
      "customer": "Customer Care: +91 728-9999-456"
    },
    "registrationId": {
      "registrationId": "Please Enter Registration ID.",
      "save": "Save",
      "edit": "Edit",
      "delete": "Delete",
      "default": "Save as default",
      "registration": "Registration Id",
      "change": "Change",
      "add": "Add",
      "ID": "ID",
      "selectReg": "Select Registration Id",
      "placeId": "Type your ID here",
      "addNewReg": "Add Your New Registration Id",
      "updateReg": "Registration Id Update Successfully",
      "addReg": "New Registration Id Created Successfully",
      "deleteReg": "Registration Id deleted Successfully"
    },
    "termsAndCondition": {
      "condition": "Terms and condition",
      "dentalkart": "Welcome to Dentalkart.com",
      "pragraph1": " With any purchase from Dentalkart.com website/app, customer unconditionally agrees to the terms and conditions mentioned below even if you have read them or not. Please read this agreement carefully before proceeding, as customer is bound to these conditions for accessing, browsing or purchase. If you (customer) do not agree and accept any of the terms, conditions and policies unconditionally, do not use this platform. According to new medical device rules (MDR) it is mandatory to buy from registered suppliers like dentalkart and sell to dental/medical council registered doctors.",
      "registered": " Dentalkart is registered with VASA Denticity Pvt. Ltd."
    },
    "productCardVertical": {
      "expressDelivery": "Express Delivery",
      "enjoyfreegift": "Enjoy Free gift"
    },
    "sellOnDentalkart": {
      "sellOnDentalkart": "Sell on Dentalkart",
      "registerd": "You have been registered successfully",
      "thankYou": "Thank You !",
      "terms": "terms and condition",
      "willContact": "Please wait will connect you as soon as possible",
      "uploadPhotos": "Upload Photos!",
      "selectImgMsg": "Drop your image here, or",
      "browse": "browse",
      "supportImg": "Supports: PNG, JPG, JPEG, WEBP",
      "importUrl": "Import from URL",
      "urlPlace": "Add file URL",
      "upload": "Upload",
      "helpCenter": "Help Center",
      "productToday": "Ready to sell your dental products today?",
      "become": "Become a SELLER PARTNER",
      "expand": "Expand your business today with DENTALKART!",
      "registerNow": "Register Now",
      "whyWith": "Why register with",
      "dentalkart": "Dentalkart?",
      "whoSell": "Who Can Become a Seller on",
      "welcomeNote": "Dentalkart welcomes individuals and businesses that sell genuine and new products related to the dental & healthcare industry. To become a seller on Dentalkart, you must submit all the required registration documents mentioned above. Please note that requirements can vary, and Dentalkart may have specific criteria or additional conditions based on their policies and the evolving marketplace. We recommend reviewing this page in detail for the most up-to-date information about becoming a seller.",
      "howWith": "How register with",
      "step": "Step 1",
      "sellOn": "Sell on",
      "accept": "We are accepting inquiries to sell your products on",
      "dentalkart1": "dentalkart",
      "enquiryForm": "Product listing enquiry form",
      "fillForm": "(Fill correct details in the form to submit your request)",
      "companyName": "Company Name",
      "companyNamePlace": "Enter Your Company Name",
      "companyAs": "Company Register As",
      "companyAsPlace": "Enter Your Company Register As",
      "mobileNumber": "Mobile Number",
      "mobileNumberPlace": "Enter Your Contact Number",
      "email": "E mail ID",
      "emailPlace": "Enter Your Email Id",
      "city": "City",
      "cityPlace": "Enter Your City Name",
      "address": "Address",
      "addressPlace": "Enter Your Address",
      "productName": "Product Name",
      "productNamePlace": "Enter Your Product Name",
      "gstNo": "GST Number",
      "gstNoPlace": "Enter Your GST Number",
      "uploadCatalogue": "Upload Catalogue",
      "submit": "Submit",
      "stories": "Seller Success Stories",
      "name": "Raju Lunawath",
      "storiesDes": "Dentalkarts support and innovarion fueled my exponential growth. i started with 1 category and moved to 5 category and in 5 year expansion!s",
      "faq": "FAQ"
    },
    "rewardZone": {
      "rewardZone": "Reward Zone",
      "howWork": "How it Works",
      "mail": "Still unsatisfied, mail us at"
    },
    "rewardCoin": {
      "rewardCoins": "Reward History",
      "monetaryValue": "Monetary Value",
      "expiryDate": "Expiry Date : ",
      "aboutExpire": "About to Expire",
      "txnNo": "Txn No. : ",
      "state": "State",
      "date": "Date",
      "status": "Status"
    },
    "magazine": {
      "searchMagazine": "Search magazine",
      "read": "Read",
      "library": "Library"
    },
    "returnPolicy": {
      "return": "Return",
      "requestSubmitted": "Request Submitted",
      "reqNote": "Your Request has been submitted, It will show in order section within 24hrs.",
      "submitRequest": "Submit Request",
      "or": "Or",
      "returnMoreProducts": "Return More Products",
      "returnsPolicy": "Returns Policy",
      "productUs": "Your product will be checked for the following conditions after received to us:",
      "category": "Category",
      "conditions": "Conditions",
      "notes": "For any products for which a refund is to be given, the refund will be processed once the returned product has been received by the customer to us.. \nIn certain cases where the manufacturer is unable to process a replacement for any reason whatsoever, A REFUNDS WILL BE GIVEN IN THE SAME MANNER THE PAYMENT WAS RECEIVED.\nIf the order was prepaid, refund will be done in the bank account and if the order was cash on delivery, refund will be in the DENTALKART ACCOUNT (COUPON FORM)."
    },
    "internet": {
      "title": "OOPS! NO INTERNET",
      "msg": "Please check your network connection."
    },
    "profileCompletion": {
      "complete": "complete",
      "yourProfile": "your profile for faster checkout",
      "and": "and",
      "&": "&",
      "personalizedOffers": "personalized offers.",
      "validationMessage": "Use 8+ Characters With Upper & Lowercase Letters, A Number, And A Symbol (@, #, $).",
      "login": "By logging or signing up, you agree to our ",
      "agreeMessage1": "By continuing to Sign up, you agree to Dentalkart's",
      "agreeMessage2": "Terms of services",
      "terms": "Terms",
      "agreeMessage3": "Privacy Policy",
      "policy": "Policy",
      "registrationId": "Registration ID",
      "registrationState": "Registration ID State",
      "needHelp": "Need help?",
      "connect": "Connect with us",
      "referral": "Have a Referral Code?",
      "referralMessage": "Sign up using a referral code to get 1 month membership free!",
      "registrationMessage": "Enter the number given by your medical/dental council at the time of registraton.",
      "stateMessage": "Enter the state where you got your registration approved."
    },
    "forceUpgrade": {
      "skip": "Skip For Now",
      "upgradeTitle": "New Upgrade Available!",
      "upgradeMsg": "Enjoy Faster, Smoother And Better Experience \nWith New Upgrade.",
      "btnText": "Upgrade App Now"
    },
    "iconUpdate": {
      "ok": "OK",
      "iconChange": "App Icon Update",
      "changeWarning": "As you're using Android, the app will exit and update the icon. Tap on the new icon to open the app again.",
      "premiumBegins": "Your Premium journey begins",
      "updgrageSubtitle": "Upgrade your app icon to reflect your premium membership."
    },
    "rate": {
      "rateTitle": "Rate your last order",
      "rateDes": "Your feedback matters to us!",
      "ratingTitle": "Rating & Reviews",
      "tellExp": "Tell us about your Experience",
      "addPhotos": "Add photos",
      "orderExp": "How was your order experience?",
      "withDentalkart": "with Dentalkart",
      "itemOrder": "Please tell us about these items in your order",
      "trust": "Trusted by Dentists all over india",
      "rateOrder": "Rate order",
      "rateYourOrder": "Rate your order",
      "ratingThank": "Thank you for your rating",
      "rated": "Rated",
      "outOf": "out of",
      "ratingSubmitted": "Rating Submitted",
      "thxFeedback": "Thank you for your feedback",
      "reviewChange": "Kindly add the latest review changes."
    }
  }
}

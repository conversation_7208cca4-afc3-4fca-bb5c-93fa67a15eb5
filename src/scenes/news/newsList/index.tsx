// import React, {useCallback, useEffect, useState} from 'react';
// import {SafeAreaView} from 'react-native-safe-area-context';
// // eslint-disable-next-line @typescript-eslint/no-unused-vars
// import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
// // eslint-disable-next-line @typescript-eslint/no-unused-vars
// import {RootStackParamsList} from 'routes';
// import {Header, NewsList} from 'components/molecules';
// import tokenClass from 'utils/token';
// import {bookmarkNews, savedNews} from 'api';
// import {showInfoMessage, showSuccessMessage} from 'utils/show_messages';
// import {LikeShare} from 'components/atoms';
// import {useTheme} from '@react-navigation/native';
// import {t} from 'i18next';

// type Props = {
//   navigation: NativeStackNavigationProp<RootStackParamsList>;
//   item: News;
//   onPress: () => void;
// };

// const NewsScene = ({navigation, route}: Props) => {
//   const [savedNewsData, setSavedNewsData] = useState([]);
//   const {colors} = useTheme();
//   const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

//   const saveNews = useCallback(async () => {
//     const {data} = await savedNews();
//     setSavedNewsData(data?.savedNews);
//   }, []);

//   useEffect(() => {
//     saveNews();
//   }, [route]);

//   const likeShare = useCallback(
//     (item: News) => {
//       const onSavePress = async () => {
//         if (await tokenClass.loginStatus()) {
//           const {data} = await bookmarkNews({id: item.id});
//           if (data.bookmarkNews) {
//             saveNews();
//             showSuccessMessage(t('toastMassages.saveSuccess'));
//           }
//         } else {
//           showInfoMessage(t('toastMassages.loginInfo'));
//           navigation.navigate('Login', {nextScreenName: 'NewsTab'});
//         }
//       };

//       return (
//         <LikeShare
//           style={styles.likesComponent}
//           isSaved={savedNewsData.some((e: News) => e.id === item.id)}
//           onSavePress={onSavePress}
//           likeText={'Like'}
//           shareText={'Share'}
//           saveText={'Save'}
//           news={item}
//         />
//       );
//     },
//     [savedNewsData, saveNews],
//   );

//   return (
//     <SafeAreaView style={styles.container}>
//       {/* <Header
//         text={t('news.recent')}
//         navigation={navigation}
//         hadarLogo={false}
//       /> */}
//       <Header
//         navigation={navigation}
//         backButton={true}
//         heartIcon
//         tintColorGOBack="text"
//         bagIcon
//         searchIcon
//         text={t('News')}
//         // text={t('news.News')}
//       />
//       <NewsList
//         type="news"
//         route={route}
//         likeShare={likeShare}
//         navigation={navigation}
//       />
//     </SafeAreaView>
//   );
// };

// export default NewsScene;

// * News List *//

import React, {useCallback, useContext, useEffect, useState} from 'react';
import {
  TouchableOpacity,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  ButtonProps,
  View,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import {
  CarouselCards,
  FastImagesItem,
  FooterButton,
  ImageIcon,
  Label,
  Link,
  ListView,
  NewsCardItem,
  Separator,
  Spacer,
} from 'components/atoms';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
import {news, savedNews} from 'api';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from 'routes';
import {showInfoMessage} from 'utils/show_messages';
import {useIsFocused, useTheme} from '@react-navigation/native';
import tokenClass from 'utils/token';
import context from 'context/context';
import {setLoading} from 'app-redux-store/slice/appSlice';
import NewsWebModal from '../newswebmodal';
import {useDispatch} from 'react-redux';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import {FlatList} from 'react-native-gesture-handler';
import Button from 'components/molecules/button';
import {Icon} from 'react-native-vector-icons/Icon';
import {Sizes} from 'common';
import {Header} from 'components/molecules';
import {getNewsList} from 'services/news';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: any;
  text?: string;
  icon?: keyof typeof Icons | null;
  onPress?: () => void;
  style?: ButtonProps['style'];
  type: 'news' | 'save';
  likeShare: (item: News) => React.ReactElement;
};

const NewsList = ({type = 'news', navigation, likeShare, route}: Props) => {
  const dispatch = useDispatch();
  const [recentTopic, setRecentTopic] = useState<News[]>([]);
  const isFocused = useIsFocused();
  const [showNewsLink, setShowNewsLink] = useState<null | string>(null);
  const [showNews, setShowNews] = useState(false);
  const [newsList, setNewsList] = useState({});
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isPressed, setIsPressed] = useState(false);
  // *xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  // const [currentCardTab, setCurrentCardTab] = useState<
  //   'creditCard' | 'debitcard'
  // >('creditCard');
  const memoizedSeparator = useCallback(() => {
    return <Separator Vertical color="grey2" />;
  }, []);
  const [isModelType, setIsModelType] = useState<'filter' | 'sort'>('');
  const [modelVisible, setModelVisible] = useState(false);

  const openFilterModel = () => {
    //  navigation.navigate()
  };
  const openSortModel = () => {
    setIsModelType('sort');
    setModelVisible(true);
  };

  const [error, setError] = useState({});
  const {store} = useContext(context);

  const newsLists = useCallback(async () => {
    dispatch(setLoading(true));
    const {data} = await getNewsList();
    dispatch(setLoading(false));
    setNewsList(data?.data);
  }, []);
  useEffect(() => {
    newsLists();
    // newsData();
  }, [newsLists]);
  // ----------------News apii--------------------

  // const newsData = useCallback(async () => {
  //   dispatch(setLoading(true));
  //   const resNews = await news();
  //   const resSavedNews = await savedNews();
  //   if (type === 'news') {
  //     setRecentTopic(resNews?.data?.news || []);
  //     dispatch(setLoading(false));
  //   } else {
  //     if (!(await tokenClass.loginStatus())) {
  //       showInfoMessage(t('toastMassages.loginInfo'));
  //       return navigation.navigate('Login', {
  //         nextScreenName: 'NewsTab',
  //         nextScreenParams: {
  //           screen: 'Saved',
  //         },
  //       });
  //     }
  //     setRecentTopic(resSavedNews?.data?.savedNews || []);
  //     dispatch(setLoading(false));
  //   }
  // }, [dispatch, navigation, type]);

  const navigationToNewsDetails = useCallback(
    currentNews => {
      navigation.navigate('NewsDetails', {
        item: recentTopic,
        currentItem: currentNews,
      });
    },
    [navigation, recentTopic],
  );

  const colorChange = () => {
    setIsPressed(true);
  };

  const data1 = [
    {
      id: '1',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner, // Assuming Icons is imported and contains memberShipBanner
    },
    {
      id: '2',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner, // Assuming Icons is imported and contains memberShipBanner
    },
    {
      id: '3',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner, // Assuming Icons is imported and contains memberShipBanner
    },
    {
      id: '4',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner, // Assuming Icons is imported and contains memberShipBanner
    },
    // Add more data objects here
  ];

  const renderItem1 = ({item}) => (
    <>
      <Spacer size="m" />
      <View style={styles.card4}>
        <View style={styles.sec4View1}>
          <View style={{flexDirection: 'column'}}>
            <View style={styles.sec4LabelView}>
              <Label color="categoryTitle" size="m" text={item.name} />
              <Label
                style={styles.labelBorder}
                color="categoryTitle"
                size="m"
                text={'|'}
              />
              <Label color="categoryTitle" size="m" text={item.type} />
            </View>
            <Label
              text={'----------------------------'}
              color="grey2"
              lineHeight="mx"
              weight="500"
              size="mx"
            />
            <View>
              <Label text={item.title} color="categoryTitle" size="mx" />
            </View>
          </View>
          <Spacer size="xm" />
          <View style={styles.sec4LabelView}>
            <Spacer type="Horizontal" size="xm" />
            <Label weight="600" size="m" text={'•'} color="grey" />
            <Spacer type="Horizontal" size="sx" />
            <Label color="grey" size="m" text={item.date} />
            <Spacer type="Horizontal" size="xm" />
            <Label weight="600" size="m" text={'•'} color="grey" />
            <Spacer type="Horizontal" size="sx" />
            <Label color="grey" size="m" text={item.readTime} />
          </View>
          <Spacer size="xm" />
          <View style={styles.iconContainer}>
            <TouchableOpacity>
              <ImageIcon tintColor="text2" icon="likeIcon" size="xxl" />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xl" />
            <TouchableOpacity>
              <ImageIcon tintColor="text2" icon="newBookmarkIcon" size="xxl" />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xl" />
            <TouchableOpacity>
              <ImageIcon tintColor="text2" icon="forwardIcon" size="xxl" />
            </TouchableOpacity>
          </View>
        </View>
        <Spacer type="Horizontal" size="m" />
        <View style={styles.sec4View2}>
          <FastImage
            source={item.image}
            resizeMode="stretch"
            style={styles.cardImage1}
          />
        </View>
      </View>
    </>
  );

  useEffect(() => {
    if (route?.params?.currentNews && recentTopic.length > 0) {
      navigationToNewsDetails(route?.params?.currentNews);
    }
  }, [navigationToNewsDetails, recentTopic, route]);

  const data = [
    {
      id: '1',
      image: Icons.MembershipBannerIcon,
      text: 'The Future Dentist’s Toolkit: Guide for Dental Students to find right materials online',
      icon: Icons.newBookmarkIcon,
    },
    {
      id: '1',
      image: Icons.MembershipBannerIcon,
      text: 'The Future Dentist’s Toolkit: Guide for Dental Students to find right materials online',
      icon: Icons.newBookmarkIcon,
    },
    {
      id: '1',
      image: Icons.MembershipBannerIcon,
      text: 'The Future Dentist’s Toolkit: Guide for Dental Students to find right materials online',
      icon: Icons.newBookmarkIcon,
    },
    {
      id: '1',
      image: Icons.MembershipBannerIcon,
      text: 'The Future Dentist’s Toolkit: Guide for Dental Students to find right materials online',
      icon: Icons.newBookmarkIcon,
    },
    // Add more items if needed
  ];

  const renderItem = ({item}) => (
    <>
      <View style={styles.singleCard}>
        <View style={styles.imageView}>
          <FastImage
            source={item.image}
            resizeMode="stretch"
            style={styles.cardImage}
          />
          <View style={styles.cardIconView}>
            <TouchableOpacity>
              <ImageIcon tintColor="text2" icon="newBookmarkIcon" size="l" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.cardText}>
          <Label style={styles.textLabel} color="text2" text={item.text} />
        </View>
      </View>
    </>
  );

  return (
    <>
      <Header
        navigation={navigation}
        backButton={true}
        heartIcon
        tintColorGOBack="text"
        bagIcon
        searchIcon
        text={t('News')}
        // text={t('news.News')}
      />
      <SafeAreaView style={styles.maincontainer}>
        <FlatList
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          data={[' ']}
          renderItem={() => (
            <>
              <View style={styles.container}>
                <Label size="l" style={styles.text} text={'Latest News'} />
                <View style={styles.line} />
              </View>
              <View style={styles.card}>
                <View>
                  <FastImage
                    source={Icons.memberShipBanner}
                    style={styles.image}
                    resizeMode="cover"
                  />
                </View>
                <Spacer size="m" />
                <View>
                  <Label
                    color="text2"
                    text={
                      '"Advancements In Oral Radiology And Oral Medicine: Revolutionizing Diagnostics And Treatments In Dentistry"'
                    }
                  />
                </View>
                <Spacer size="m" />
                <View style={styles.actions}>
                  <View style={styles.iconContainer}>
                    <TouchableOpacity>
                      <ImageIcon icon="newShareIcon" size="xxl" />
                    </TouchableOpacity>
                    <Spacer type="Horizontal" size="xl" />
                    <TouchableOpacity>
                      <ImageIcon icon="heart" size="xxl" />
                    </TouchableOpacity>
                  </View>
                  <View>
                    <TouchableOpacity>
                      <ImageIcon icon="newBookmarkIcon" size="xxl" />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              <View style={styles.section2}>
                <View style={styles.container}>
                  <View style={styles.line} />
                  <Label size="l" style={styles.text} text={'For You'} />
                  <View style={styles.line} />
                </View>
                <View>
                  <Label
                    align="center"
                    color="text"
                    text={
                      '"Advancements In Oral Radiology And Oral Medicine: Revolutionizing Diagnostics And Treatments In Dentistry"'
                    }
                    weight="600"
                  />
                </View>
                <Spacer size="l" />
                <View style={styles.cardView}>
                  <FastImage
                    source={Icons.membershipIcon}
                    style={styles.image2}
                    resizeMode="cover"
                  />
                </View>
                <Spacer size="l" />
                <View style={styles.labelContainer}>
                  <Label style={styles.textContainer}>
                    <>
                      <Label
                        style={{top: -9, borderWidth: 1}}
                        color="text2"
                        text={'“C'}
                        size="xxl"
                      />
                      <Label
                        style={{flex: 1}}
                        text={
                          'ontent here Advancements in Oral Radiology and Oral Medicine: Revolutionizing Diagnostics and Treatments in oral Medicine: Revolutionizing Diagnostics and  Dentistry”Advancements in Oral Radiology and Oral Medicine: Revolutionizing Diagnostics and Treatments in Dentistry”'
                        }
                        color="text2"
                        size="mx"
                      />
                    </>
                  </Label>
                </View>
                <Spacer size="l" />
                <View>
                  <Button
                    style={
                      isPressed
                        ? styles.pressedButtonStyle
                        : styles.normalButtonStyle
                    }
                    text={'Read More'}
                    onPress={() => colorChange}
                    labelSize="l"
                    radius="xm"
                    labelColor="categoryTitle"
                    weight="500"
                  />
                </View>
                <Spacer size="m" />
              </View>
              <View style={styles.container}>
                <Label size="l" style={styles.text} text={'Your Preferences'} />
                <View style={styles.line} />
              </View>
              <View style={styles.sec3Card}>
                <FlatList
                  ItemSeparatorComponent={() => (
                    <Spacer type="Horizontal" size="l" />
                  )}
                  horizontal={true}
                  data={data}
                  renderItem={renderItem}
                />
              </View>

              <View style={[styles.container, styles.container]}>
                <Label size="l" style={styles.text} text={'All News'} />
                <View style={styles.line} />
              </View>
              <View>
                <FlatList
                  data={newsList}
                  renderItem={renderItem1}
                  keyExtractor={item => item.id}
                />
              </View>
            </>
          )}
        />
      </SafeAreaView>
      <FooterButton
        shadowProps={true}
        useInsets={true}
        separator={memoizedSeparator}
        buttons={[
          {
            text: 'Saved',
            onPress: openFilterModel,
            ghost: true,
            iconLeft: 'newBookmarkIcon',
            labelSize: 'l',
            weight: '400',
            labelColor: 'text',
            tintColor: 'text',
            size: 'large',
            selfAlign: 'stretch',
          },
          {
            text: 'Sort By',
            height: 42,
            iconLeft: 'sortIcons',
            onPress: openSortModel,
            ghost: true,
            labelSize: 'l',
            weight: '400',
            labelColor: 'text',
            selfAlign: 'stretch',
          },
        ]}
      />
    </>
  );
};
export default NewsList;

import React from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from '@react-navigation/native';
import Config from 'react-native-config';
import NetworkLoggerStyles from './style';

interface ButtonInterceptorProps {
  show: boolean;
}

const ButtonInterceptor: React.FC<ButtonInterceptorProps> = ({show}) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const buttonText = Config.APP_CONFIG;
  const containerStyle = {backgroundColor: 'red'};
  const styles = NetworkLoggerStyles();
  if (!show) {
    return null;
  }
  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() => {
        // toggleInterceptor();
        navigation.navigate('ScreenNetworkLogger');
      }}
      style={styles.buttonStyle}>
      <View style={{...containerStyle, ...styles.containerStyle}}>
        <Text style={styles.versionTextStyle}>
          1.0.0
          {/* {`${DeviceInfo.getVersion()}` || '-'} */}
        </Text>
        <Text style={styles.textInputStyle}>{buttonText}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default ButtonInterceptor;

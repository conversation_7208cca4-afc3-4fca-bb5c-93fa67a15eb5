import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    body: {
      flex: Sizes.x,
    },
    title: {
      width: '100%',
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    rowDirection: {
      flexDirection: 'row',
    },
    imageBackGround: {
      flex: Sizes.x,
      paddingVertical: Sizes.x6l,
      paddingHorizontal: Sizes.xxxl,
    },
    containerForm: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
    },
    dentLogo: {
      width: Sizes.ex1 + Sizes.xm,
      height: Sizes.ex1 + Sizes.xm,
    },
    flex: {flex: Sizes.x},
    itemCenter: {alignSelf: 'center'},
    inputView: {
      color: colors.text,
      fontSize: Sizes.mx,
    },
    checkBox: {
      height: Sizes.xl,
      width: Sizes.xl,
    },
    buttonLabel: {paddingVertical: Sizes.xm},
    inputStyleView: {
      borderWidth: Sizes.x,
      color: colors.grey2,
      borderColor: colors.grey2,
    },
    termsView: {
      flex: Sizes.x,
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'center',
    },
    eyeStyle: {
      height: Sizes.xx,
      width: Sizes.xx,
    },
  });

export default styles;

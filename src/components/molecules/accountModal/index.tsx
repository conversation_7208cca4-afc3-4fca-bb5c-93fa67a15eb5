import React, {useCallback} from 'react';
import {TouchableOpacity, View, ScrollView, Text} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {
  ImageIcon,
  Label,
  Separator,
  Spacer,
  GradientText,
} from 'components/atoms';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import {accountMenu, accountMenu2, accountMenuBoxList} from 'staticData';
import Modal from 'react-native-modal';
import {Version} from 'config/environment';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  navigateToScreen: (data: screenProps) => void;
  logout: () => void;
  activePlan: boolean;
};

const AccountModal = ({
  visible,
  onClose,
  navigateToScreen,
  logout,
  activePlan,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {isLoggedIn, userInfo} = useSelector((state: RootState) => state.app);
  const user = userInfo || {};

  // Function to chunk array for grid layout
  const chunkArray = (arr: any[], size: number) => {
    return arr.reduce((acc, _, i) => {
      if (i % size === 0) acc.push(arr.slice(i, i + size));
      return acc;
    }, []);
  };

  // Memoized navigation functions to avoid unnecessary re-renders
  const handleNavigation = useCallback(
    (item: any) => {
      onClose();
      navigateToScreen(item);
    },
    [onClose, navigateToScreen],
  );

  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      // animationIn="slideInUp"
      // animationOut="slideOutDown"
      // animationInTiming={500}
      // animationOutTiming={500}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={styles.modalHeaderView}>
        <View style={styles.accountMainContainer}>
          <View style={styles.accountSubContainer}>
            <TouchableOpacity onPress={onClose} style={styles.topBarArrowView}>
              <ImageIcon size="xxl" tintColor="white60" icon="downArrow1" />
            </TouchableOpacity>

            <View style={styles.topBarArrowSubContainer}>
              <Spacer size="xms" />
              <Label
                color="whiteColor"
                fontFamily="SemiBold"
                size="l"
                text={`${user?.firstname || ''} ${user?.lastname || ''}`}
                style={styles.textCap}
              />
              <Label
                color="whiteColor"
                text={user?.mobile || user?.email}
                size="m"
                fontFamily="Medium"
              />
              <View>
                <TouchableOpacity
                  style={styles.linearContainer}
                  onPress={() => handleNavigation({screenName: 'Membership'})}>
                  <LinearGradient
                    colors={[colors.persimmon1, colors.persimmon1]}
                    start={{x: 0, y: 0}}
                    end={{x: 1, y: 1}}
                    style={styles.linearGradient}>
                    <Label
                      color="whiteColor"
                      text={t(
                        activePlan
                          ? 'profile.membership'
                          : 'buttons.getMembership',
                      )}
                      size="m"
                      fontFamily="SemiBold"
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </View>
              <Spacer size="s" />
              <View style={styles.orderSectionView}>
                {[
                  {
                    icon: 'accountOrder',
                    screen: 'OrderList',
                    label: t('profile.orders'),
                  },
                  {
                    icon: 'accountWishlist',
                    screen: 'WishList',
                    label: t('profile.wishlist'),
                  },
                  {
                    icon: 'accountRewardCoins',
                    screen: 'MyRewords',
                    label: t('rewardCoin.rewardCoins'),
                  },
                ].map((item, index) => (
                  <React.Fragment key={index}>
                    {index !== 0 && (
                      <Separator color="grey2" Vertical height="x9l" />
                    )}
                    <TouchableOpacity
                      style={styles.itemView}
                      onPress={() =>
                        handleNavigation({screenName: item.screen})
                      }>
                      <View style={styles.alignCenter}>
                        <ImageIcon size="x44" icon={item.icon} />
                        <Spacer size="s" />
                        <Label
                          size="m"
                          color="text2"
                          fontFamily="Medium"
                          text={item.label}
                        />
                      </View>
                    </TouchableOpacity>
                  </React.Fragment>
                ))}
              </View>
            </View>
          </View>
          <View style={styles.flexOne}>
            <ScrollView
              showsVerticalScrollIndicator={false}
              bounces={false}
              style={styles.accountContainerList}>
              <View style={styles.accountContainer}>
                <Label
                  color="text2"
                  size="mx"
                  fontFamily="SemiBold"
                  text={t('profile.account')}
                />
                {accountMenu.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.menuListView}
                    onPress={() => handleNavigation(item)}>
                    <View style={styles.menuSubListView}>
                      <ImageIcon size="x4l" icon={item?.image} />
                      <Label
                        style={styles.marginL10}
                        size="mx"
                        fontFamily="Medium"
                        text={item.title}
                        color="text2"
                      />
                      {item?.screenName === 'ProfileDetails' && (
                        <View style={styles.percentageInfoContainer}>
                          <Text style={styles.percentageText}>
                            {userInfo?.profile_completion}% Complete
                          </Text>
                        </View>
                      )}
                    </View>
                    <ImageIcon icon="arrowRight" size="xl" />
                  </TouchableOpacity>
                ))}
              </View>

              <View style={styles.boxViewList}>
                <View style={styles.spaceList}>
                  {chunkArray(accountMenuBoxList, 2).map((row, rowIndex) => (
                    <View key={rowIndex} style={styles.rowContainer}>
                      {row.map((item, itemIndex) => (
                        <TouchableOpacity
                          key={itemIndex}
                          style={styles.boxViewListView}
                          onPress={() => handleNavigation(item)}>
                          <ImageIcon size="x4l" icon={item.image} />
                          <Spacer size="s" />
                          <Label
                            size="mx"
                            fontFamily="Medium"
                            color="text2"
                            text={item.title}
                          />
                        </TouchableOpacity>
                      ))}
                    </View>
                  ))}
                </View>
              </View>

              <View style={styles.accountContainer}>
                {accountMenu2.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.menuListView}
                    onPress={() => {
                      onClose();
                      item?.title === 'Log Out'
                        ? logout()
                        : handleNavigation(item);
                    }}>
                    <View style={styles.menuSubListView}>
                      <ImageIcon size="x4l" icon={item?.image} />
                      <Label
                        style={styles.marginL10}
                        size="mx"
                        fontFamily="Medium"
                        text={item.title}
                        color="text2"
                      />
                    </View>
                    <ImageIcon icon="arrowRight" size="xl" />
                  </TouchableOpacity>
                ))}
              </View>

              <Spacer size="xm" />
              <Label
                size="m"
                align="center"
                color="text2"
                fontFamily="Regular"
                text={`${t('profile.version')} ${Version}`}
              />
              <Spacer size="xm" />
            </ScrollView>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default React.memo(AccountModal);

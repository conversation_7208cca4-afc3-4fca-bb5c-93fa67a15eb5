package com.vasadental.dentalkart; // Make sure this matches your package name

import ai.easyinsights.ei_sdk.EIUtils;
import ai.easyinsights.ei_sdk.EIEventPayload;
import android.content.Context;
import androidx.annotation.NonNull;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import org.json.JSONObject;  // ✅ Fix: Import JSON handling
import java.util.Iterator; 

public class EISDKModule extends ReactContextBaseJavaModule {
    private EIUtils eiUtils;

    public EISDKModule(ReactApplicationContext reactContext) {
        super(reactContext);
        eiUtils = new EIUtils();
    }

    @NonNull
    @Override
    public String getName() {
        return "EISDKModule";
    }

    @ReactMethod
    public void initializeSDK(String key, String userId, Promise promise) {
        try {
            eiUtils.init(getReactApplicationContext(), key, userId);
            promise.resolve("SDK Initialized Successfully");
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }

     @ReactMethod
    public void logEvent(String eventID, String jsonData, Promise promise) {
        try {
            EIEventPayload eventPayload = new EIEventPayload();
            eventPayload.updateID(eventID);

            // ✅ Fix: Parse JSON correctly
            JSONObject jsonObject = new JSONObject(jsonData);

            // Extract standard attributes
            String currency = jsonObject.optString("currency", "INR");
            String email = jsonObject.optString("email", "");
            String phone = jsonObject.optString("phone", "");
            String emailHash = jsonObject.optString("email_hash", "");
            String phoneHash = jsonObject.optString("phone_hash", "");
            String userID = jsonObject.optString("userid", "");
            String revenue = jsonObject.optString("revenue", "0");

            // ✅ Apply standard attributes
            eventPayload.updateCurrency(currency);
            eventPayload.updateEmail(email);
            eventPayload.updatePhone(phone);
            eventPayload.updateEmailHash(emailHash);
            eventPayload.updatePhoneHash(phoneHash);
            eventPayload.updateUserID(userID);
            
            // ✅ Store custom attributes inside updateValue() as a JSON string
            JSONObject customAttributes = new JSONObject();
            Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                if (!key.equals("currency") && !key.equals("email") && !key.equals("phone") &&
                    !key.equals("email_hash") && !key.equals("phone_hash") && !key.equals("userid") &&
                    !key.equals("revenue")) {
                    customAttributes.put(key, jsonObject.optString(key, ""));
                }
            }
            
            eventPayload.updateValue(customAttributes.toString()); // ✅ Custom attributes stored as JSON

            boolean isDebug = BuildConfig.DEBUG;
            eiUtils.logEvent(eventID, eventPayload, isDebug);

            promise.resolve("Event Logged Successfully");
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }
}

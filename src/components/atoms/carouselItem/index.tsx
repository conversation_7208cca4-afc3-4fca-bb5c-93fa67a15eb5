import React from 'react';
import {
  type StyleProp,
  type ViewStyle,
  type ViewProps,
  type ImageSourcePropType,
  Image,
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {LongPressGestureHandler} from 'react-native-gesture-handler';
import type {AnimateProps} from 'react-native-reanimated';
import Animated from 'react-native-reanimated';
import {Sizes} from 'common';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../../routes';

interface Props extends AnimateProps<ViewProps> {
  style?: StyleProp<ViewStyle>;
  index?: number;
  pretty?: boolean;
  showIndex?: boolean;
  img?: ImageSourcePropType;
  resizeMode?: string;
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  item?: Brand;
}

const CarouselItem: React.FC<Props> = props => {
  const {
    style,
    showIndex = true,
    index,
    pretty,
    img,
    testID,
    resizeMode,
    navigation,
    item,
    ...animatedViewProps
  } = props;
  const [isPretty, setIsPretty] = React.useState(pretty || false);
  return (
    <LongPressGestureHandler
      onActivated={() => {
        setIsPretty(!isPretty);
      }}>
      <Animated.View testID={testID} style={styles.flex} {...animatedViewProps}>
        <TouchableOpacity 
          testID='tOCarouselItem'
          style={[styles.container, style]}
          onPress={() =>
            navigation.navigate('CategoryDetail', {
              categoryId: item?.category_id,
            })
          }>
          <ActivityIndicator size="small" />
          <Image
            key={index}
            style={styles.image}
            source={img}
            resizeMode={resizeMode}
          />
        </TouchableOpacity>
      </Animated.View>
    </LongPressGestureHandler>
  );
};

export default CarouselItem;
const styles = StyleSheet.create({
  flex: {
    flex: Sizes.x,
  },
  container: {
    flex: Sizes.x,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderRadius: Sizes.xm,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
});

import React from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Separator, ImageIcon, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {DeviceWidth} from 'config/environment';
import Icons from 'common/icons';
import {useMemo} from 'react';


const CartPositionStatus = props => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {cartPositionStatus} = props;

  return (
    <View style={styles.addressView}>
      {cartPositionStatus.map((item: any, index: number) => {
        const width = (DeviceWidth - 24) / cartPositionStatus?.length;
        return (
          <View
            key={index}
            style={[
              styles.addressSubView,
              {width: item.title?.length > 10 ? width + 10 : width - 10},
            ]}>
            <Separator
              style={styles.flexView}
              color={item.status ? 'categoryTitle' : 'grey2'}
            />
            <ImageIcon
              icon={item?.current ? 'checkBoxBlue' : 'blueRadio'}
              size="m"
              tintColor={item.status ? 'categoryTitle' : 'grey2'}
            />
            <Label
              text={item.title}
              size="m"
              fontFamily="Medium"
              textTransform="capitalize"
              color={item.status ? 'categoryTitle' : 'grey2'}
              style={styles.labelStyle}
            />
          </View>
        );
      })}
    </View>
  );
};

export default React.memo(CartPositionStatus);

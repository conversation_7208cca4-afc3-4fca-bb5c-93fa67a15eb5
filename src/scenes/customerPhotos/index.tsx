import React from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../routes';
import stylesWithOutColor from './style';
import {Header} from 'components/molecules';
import {FlatList, Image, Text, TouchableOpacity, View} from 'react-native';
import {useMemo} from 'react';

import {useTheme} from '@react-navigation/native';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};
const catageoryData = [
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
  {
    iconUrl:
      'https://images.dentalkart.com/dt-media/category-icons/Orthodontics.png',
  },
];

const CustomerPhotosScene = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <SafeAreaView style={styles.container}>
      <Header
        navigation={navigation}
        backButton={true}
        isRedious
        text={'Customer Photos'}
      />

      <View style={styles.subContainer}>
        <View style={styles.mainContainer}>
          <View style={styles.categoryMainView}>
            <FlatList
              style={styles.SearchView}
              numColumns={5}
              data={catageoryData}
              renderItem={({item, index}) => (
                <TouchableOpacity style={styles.categorySubView}>
                  <Image
                    source={{
                      uri: item?.iconUrl,
                    }}
                    style={styles.categoryTitleView}
                  />
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CustomerPhotosScene;

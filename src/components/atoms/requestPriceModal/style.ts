import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
    },
    mainView: {paddingHorizontal: Sizes.m},
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    scrollViewStyle: {
      marginTop: 'auto',
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    viewSubProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.sx,
    },
    bottomLine: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    overallView: {
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    memberShipCardImageView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    input: {
      borderRadius: Sizes.xms,
      color: colors.text2,
    },
    searchIconStyle: {
      width: Sizes.l,
      height: Sizes.l,
    },
    inputBorderStyle: {
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      height: 45,
      backgroundColor: colors.background,
      borderColor: colors.placeholderColor,
    },
    cancelView: {
      justifyContent: 'flex-start',
      flexDirection: 'row',
    },
    referAndEarnView: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    childProductView: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.sx,
      marginBottom: Sizes.xm,
      backgroundColor: colors.background,
    },
    priceView: {
      flexDirection: 'row',
      alignItems: 'baseline',
    },
    expiryDateView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headingView: {borderBottomWidth: Sizes.x, borderColor: colors.grey2},
    showTierMainView: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      padding: Sizes.m,
      borderColor: colors.categoryTitle,
      marginHorizontal: Sizes.m,
    },
    centeredRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    doubleFlex: {
      flex: Sizes.xs,
    },
    earnView: {
      justifyContent: 'flex-end',
      flex: Sizes.x,
      flexDirection: 'row',
    },
  });

export default styles;

import React from 'react';
import {View, Animated} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type props = {
  scrollBarWidth: number;
  scrollBarPosition: number;
  activeColor: string;
};

const HorizontalScrollBar = ({
  scrollBarWidth,
  scrollBarPosition,
  activeColor,
}: props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={styles.scrollBarContainer}>
      <Animated.View
        style={[
          styles.scrollBar,
          {
            width: scrollBarWidth,
            transform: [{translateX: scrollBarPosition}],
            backgroundColor: activeColor,
          },
        ]}
      />
    </View>
  );
};

export default React.memo(HorizontalScrollBar);

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    processingContinuer: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    box: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.l,
      padding: Sizes.m,
    },

    width: {width: '100%'},
    borderBottom: {borderBottomColor: colors.primary},
    tabColor: {color: colors.primary, fontWeight: '700'},
    tabBar: {
      borderBottomWidth: Sizes.xs,
      width: '50%',
      alignItems: 'center',
      borderBottomColor: colors.background,
    },
    attachmentsButton: {
      width: '40%',
      alignSelf: 'flex-end',
      borderRadius: Sizes.xs,
      backgroundColor: colors.transparentColor,
    },
    imageView: {
      backgroundColor: colors.background,
      padding: Sizes.xl,
      borderRadius: Sizes.xm,
    },
    rowContinuer: {flexDirection: 'row', justifyContent: 'space-between'},
    iconView: {
      backgroundColor: colors.whiteGray,
      width: Sizes.x7l,
      height: Sizes.x7l,
      borderRadius: Sizes.xxxl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    label: {width: '70%'},
    itemsCenter: {alignItems: 'center'},
    placeHolderText: {color: colors.lightGray},
    dropDown: {height: Sizes.x7l, borderRadius: Sizes.xm},
    separator: {
      height: Sizes.x,
      width: '100%',
      backgroundColor: colors.whiteGray,
    },
    tabView: {backgroundColor: colors.whiteGray, flex: Sizes.x},
    textCenter: {textAlign: 'center'},
    labelView: {width: '80%', textAlign: 'left'},
    heading: {
      alignItems: 'flex-start',
    },
    returnErrorText: {
      flex: 1,
      marginHorizontal: Sizes.l,
      paddingTop: Sizes.s,
      fontSize: Sizes.m,
      color: colors.textError,
    },
    radio: {borderRadius: Sizes.s},
  });

export default styles;

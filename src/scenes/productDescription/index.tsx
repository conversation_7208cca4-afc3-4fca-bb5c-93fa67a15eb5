import React, {useCallback, useEffect, useRef, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../routes';
import {<PERSON><PERSON>, RenderCustomHTML} from 'components/molecules';
import stylesWithOutColor from './style';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import {
  Dimensions,
  FlatList,
  TouchableOpacity,
  View,
  Animated,
} from 'react-native';
import {RouteProp, useTheme} from '@react-navigation/native';
import {AttributeItem} from '@types/local';
import tokenClass from 'utils/token';
import {t} from 'i18next';
import {getAllAttributesData} from 'services/productDetail';
import {ScrollView} from 'react-native-gesture-handler';
import {Sizes} from 'common';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ProductDescription'>;
};
const {width} = Dimensions.get('window');
const ProductDescriptionScene = ({navigation, route}: Props) => {
  const TAG = 'ProductDescriptionScreen';
  const {productId} = route.params;
  const {product} = route.params;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [description, setDescription] = useState<AttributeItem | null>(
    [] || null,
  );
  const [descriptionKey, setDescriptionKey] = useState<AttributeItem | null>(
    [] || null,
  );
  const [loginStatus, setLoginStatus] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const [selectedItem, setSelectedItem] = useState(null);

  const loginStatusCheck = useCallback(async () => {
    let status = await tokenClass.loginStatus();
    setLoginStatus(status);
  }, []);
  const handleTabPress = index => {
    if (index !== activeTab) {
      const direction = index > activeTab ? width : -width;
      Animated.timing(slideAnim, {
        toValue: -direction,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        slideAnim.setValue(0);
        setActiveTab(index);
      });
    }
  };

  const getProductDescription = useCallback(async () => {
    const {data} = await getAllAttributesData(productId);
    if (data?.attributes) {
      let tempArr = data?.attributes?.filter(
        e => e?.attribute_label !== 'FAQs',
      );
      setDescription(
        [...tempArr, {attribute_label: 'FAQs', attribute_value: null}]?.map(
          item => {
            return {title: item.attribute_label, data: [item.attribute_value]};
          },
        ),
      );
    }
  }, [productId]);

  useEffect(() => {
    getProductDescription();
  }, [getProductDescription, loginStatusCheck]);
  useEffect(() => {
    if (description.length > 0) {
      setDescriptionKey(description.map(item => item));
    }
  }, [description]);

  useEffect(() => {
    if (descriptionKey.length > 0) {
      setSelectedItem(descriptionKey[0]);
    }
  }, [descriptionKey]);

  const renderItem = ({item, index}: {item: string; index: number}) => {
    return (
      <View key={index}>
        {item?.title !== 'Packaging' && item?.title !== 'Warranty' && (
          <TouchableOpacity
            key={index}
            onPress={() => {
              handleTabPress(index);
              setTimeout(() => {
                setSelectedItem(item);
              }, 200);
            }}
            style={[styles.tab, activeTab === index && styles.activeTab]}>
            <Label
              size="mx"
              weight={selectedItem === item ? '600' : '500'}
              color={selectedItem === item ? 'text' : 'text2'}
              text={item?.title}
            />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <>
      <SafeAreaView style={styles.mainView}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <Header
            backButton
            navigation={navigation}
            text={t('PDP.productdetails')}
            style={{shadowColor: 'black'}}
            tintColorGOBack="text"
          />
        </ErrorHandler>
        <Spacer size="m" />
        <View style={styles.section1}>
          <View style={styles.cardContainer}>
            <View style={styles.itemView}>
              <ErrorHandler
                componentName={`${TAG} FastImage`}
                onErrorComponent={<View />}>
                <FastImage
                  source={{uri: getImageUrl(product.media[0].file)}}
                  style={styles.productImage}
                />
              </ErrorHandler>
            </View>
            <Spacer type="Horizontal" size="mx" />
            <View style={styles.productDetails}>
              <View>
                <Label
                  color="text"
                  size="l"
                  weight="500"
                  style={styles.productTitle}
                  text={product?.name}
                />
              </View>
              {product.attributes?.reward_points &&
              !product?.pricing?.is_price_request
                ? Number(product.attributes?.reward_points) > 0 && (
                    <View style={styles.coinContainer}>
                      <View style={styles.coinView1}>
                        <ImageIcon icon="coin" size="l" />
                      </View>
                      <Spacer size="sx" type="Horizontal" />
                      <View style={styles.coinView2}>
                        <Label
                          align="center"
                          size="mx"
                          color="orange"
                          weight="500"
                          text={product.attributes.reward_points}
                        />
                      </View>
                    </View>
                  )
                : null}
              {!product?.pricing?.is_price_request && (
                <View style={styles.priceContainer}>
                  <Label
                    size="mx"
                    weight="500"
                    color="grey3"
                    textDecorationLine="line-through"
                    text={`${product?.pricing?.currency_symbol}${product?.pricing?.price}`}
                  />
                  <Spacer type="Horizontal" size="sx" />
                  <Label
                    size="mx"
                    weight="600"
                    color="text"
                    text={`${product?.pricing?.currency_symbol}${product?.pricing?.selling_price}`}
                  />
                  {product?.pricing?.discount?.label &&
                    !product?.pricing?.discount?.label?.startsWith('0') && (
                      <>
                        <Spacer type="Horizontal" size="xm" />
                        <View
                          style={{
                            backgroundColor: colors.green4,
                            paddingHorizontal: Sizes.sx,
                          }}>
                          <Label
                            size="mx"
                            weight="500"
                            color="green2"
                            text={product?.pricing?.discount?.label}
                          />
                        </View>
                      </>
                    )}
                </View>
              )}
            </View>
          </View>
          <Spacer size="l" />
          <View style={styles.descriptionKey}>
            <FlatList
              showsHorizontalScrollIndicator={false}
              horizontal
              data={description}
              ItemSeparatorComponent={() => (
                <Spacer type="Horizontal" size="x6l" />
              )}
              renderItem={renderItem}
              keyExtractor={(item, index) => index.toString()}
            />
          </View>
          <View style={styles.section2}>
            {selectedItem && (
              <ScrollView showsVerticalScrollIndicator={false}>
                <Animated.View style={[{transform: [{translateX: slideAnim}]}]}>
                  <ErrorHandler
                    componentName={`${TAG} RenderCustomHTML`}
                    onErrorComponent={<View />}>
                    <RenderCustomHTML
                      html={selectedItem?.data?.[0]?.replace(
                        /style="[^"]*"/g,
                        '',
                      )}
                      tagsStyles={styles.tags}
                    />
                  </ErrorHandler>
                </Animated.View>
              </ScrollView>
            )}
          </View>
        </View>
      </SafeAreaView>
    </>
  );
};

export default ProductDescriptionScene;

import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  LayoutAnimation,
  UIManager,
  Platform,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer} from 'components/atoms';
import {benefits} from 'staticData';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {Sizes} from 'common';
import stylesWithOutColor from './style';
import {useMemo} from 'react';

// Enable LayoutAnimation for Android
if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

type Props = {
  visible: boolean;
  onClose: () => void;
  contentType: string;
  modalText: ModalText;
  setIsShowFooterModal: boolean;
};

const KnowMoreFooterModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose, contentType, modalText, setIsShowFooterModal} =
    props;
  const [accordionActiveIndex, setAccordionActiveIndex] = useState<number>(-1);

  const handleAccordionToggle = (index: number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setAccordionActiveIndex(prevIndex => (prevIndex === index ? -1 : index));
  };

  const renderMultiDimensionalArray = arr => {
    if (Array.isArray(arr) && arr.length) {
      return arr.map((item, index) => {
        if (Array.isArray(item) && item.length) {
          return (
            <View key={index} style={styles.renderList}>
              {renderMultiDimensionalArray(item)}
            </View>
          );
        } else {
          return (
            <View style={styles.numList} key={index}>
              <Label text={`•  `} color="text2" size="m" fontFamily="Regular" />
              <Label text={item} color="text2" size="m" fontFamily="Regular" />
            </View>
          );
        }
      });
    }
  };

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={() => onClose()}
      content={
        contentType === 'knowMore' ? (
          <View style={styles.modalAccordion}>
            {benefits.map((value, index) => {
              return (
                <TouchableOpacity
                  key={index.toString()}
                  style={{
                    borderTopWidth: index === 0 ? 0 : 1,
                    borderTopColor: colors.placeholderColor,
                    paddingVertical: 16,
                    paddingHorizontal: 8,
                  }}
                  onPress={() => handleAccordionToggle(index)}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <Label text={value.label} size="l" weight="600" textTransform={'capitalize'}/>
                    {index === accordionActiveIndex ? (
                      <ImageIcon icon="arrowBottom" size="xx" />
                    ) : (
                      <ImageIcon icon="arrowRight" size="xx" />
                    )}
                  </View>
                  {index === accordionActiveIndex ? (
                    <View style={{paddingVertical: 16}}>
                      {value.content.map((content, i) => (
                        <View
                          style={{flexDirection: 'row', paddingVertical: 2}}
                          key={i.toString()}>
                          <Label
                            text={`•  `}
                            color="text2"
                            size="m"
                            weight="400"
                          />
                          <Label
                            text={content}
                            color="text2"
                            size="m"
                            weight="400"
                          />
                        </View>
                      ))}
                    </View>
                  ) : null}
                </TouchableOpacity>
              );
            })}
          </View>
        ) : contentType === 'benefits' ? (
          <>
            <View style={styles.modalHeader}>
              <Label
                text={modalText?.label}
                size="l"
                weight="600"
                color="text"
                textTransform={"capitalize"}
              />
            </View>
            <Spacer size="m" />
            <View style={styles.modalTextContainer}>
              {modalText?.content.map((content, index) => (
                <View
                  key={index.toString()}
                  style={{flexDirection: 'row', paddingVertical: Sizes.xs}}>
                  <Label text={`•  `} color="text2" size="m" weight="400" />
                  <Label text={content} color="text2" size="m" weight="400" />
                </View>
              ))}
            </View>
          </>
        ) : contentType === 'paymentOptions' ? (
          <>
            <View style={styles.modalHeader}>
              <Label
                text={modalText?.label}
                size="l"
                weight="600"
                color="text"
              />
            </View>
            <Spacer size="m" />
            <View style={styles.modalTextContainer}>
              {renderMultiDimensionalArray(modalText.content)}
            </View>
          </>
        ) : null
      }
    />
  );
};

export default React.memo(KnowMoreFooterModal);

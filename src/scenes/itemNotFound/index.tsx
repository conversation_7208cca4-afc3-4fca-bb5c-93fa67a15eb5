import React, { useEffect } from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {RootStackParamsList} from '../../routes';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import {Label, Spacer, GradientText} from 'components/atoms';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import FastImage from 'react-native-fast-image';
import {useMemo} from 'react';
import { useSelector } from 'react-redux';
import { gethomepageBrandsItemProps, RootState } from '@types/local';
import { AnalyticsEvents } from 'components/organisms';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  gethomepagebrands: gethomepageBrandsItemProps;
};

const ItemNotFound = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  useEffect(()=>{
    AnalyticsEvents('ERROR', 'Error screen', {}, userInfo, isLoggedIn);
  },[])

  return (
    <SafeAreaView style={styles.flex}>
      <Header
        backButton
        navigation={navigation}
        text={''}
        searchIcon={true}
        bagIcon={true}
        heartIcon={true}
      />
      <FastImage source={Icons.error404} style={styles.mainView} />
      <View style={styles.bottomView}>
        <Label
          fontFamily="Medium"
          align="center"
          size="l"
          color="skyBlue17"
          text={t('page404.title')}
        />
        <Spacer size="xms" />
        <GradientText
          linearColor={[colors.cornflowerBlue1, colors.lavenderRose]}
          style={styles.linearTextStyle}>
          {t('page404.des')}
        </GradientText>
        <Spacer size="xms" />
        <Button
          type="secondary"
          onPress={() => navigation.navigate('Tab', {screen: 'Shop'})}
          labelColor="whiteColor"
          style={styles.buttonStyle}
          text={t('page404.goToHome')}
        />
        <Spacer size="xm" />
        <Button
          onPress={() => navigation.navigate('CategoryDetail', {categoryId: 2566})}
          style={[styles.buttonStyle, styles.button2]}
          text={t('page404.goToOffer')}
        />
      </View>
    </SafeAreaView>
  );
};

export default ItemNotFound;

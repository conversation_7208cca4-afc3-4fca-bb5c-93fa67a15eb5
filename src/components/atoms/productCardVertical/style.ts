import {Sizes, Fonts} from 'common';
import {Platform, StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    image: {
      width: checkDevice() ? Sizes.windowWidth * 0.2 : Sizes.windowWidth * 0.39,
      height: checkDevice()
        ? Sizes.windowWidth * 0.22
        : Sizes.windowWidth * 0.41,
      alignSelf: 'center',
    },
    cardContainer: {
      width: checkDevice()
        ? Sizes.windowWidth * 0.24
        : Sizes.windowWidth * 0.45,
      height: checkDevice() ? Sizes.ex416 : Sizes.ex410,
      backgroundColor: colors.background, // Background color for card
      borderRadius: Sizes.m, // Rounded corners
      elevation: Sizes.xs + Sizes.x, // Shadow on Android
      shadowColor: colors.shadowColor2, // Shadow on iOS
      shadowOffset: {width: 0, height: Sizes.xs},
      shadowOpacity: 0.1,
      shadowRadius: Sizes.xs + Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    imageCard: {
      width: '100%', // Adjust image width inside the card
      height: '100%', // Adjust image height inside the card
      borderRadius: Sizes.m, // Rounded corners for the image
    },
    imageSmall: {
      width: checkDevice() ? Sizes.windowWidth * 0.2 : Sizes.windowWidth * 0.35,
      height: checkDevice()
        ? Sizes.windowWidth * 0.3
        : Sizes.windowWidth * 0.35,
      alignSelf: 'center',
    },
    tagView: {
      padding: Sizes.z,
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.sx,
      marginTop: Sizes.xs,
    },
    tagEmptyView: {
      marginTop: Sizes.xxl,
    },
    coinNumber: {height: Sizes.xl},
    ratingNumber: {height: Sizes.xx},
    rateTagView: {
      width: Sizes.xx4l,
      height: Sizes.xx,
      borderRadius: Sizes.s,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 0,
    },
    rateTagLabel: {
      fontFamily: Fonts.Medium,
    },
    tagLabel: {
      color: colors.text2,
      fontFamily: Fonts.Medium,
    },
    tagLabelfree: {
      color: colors.background,
      fontFamily: Fonts.Medium,
    },
    deliveryOfferRow: {
      flexDirection: 'row',
      bottom: 0,
      right: 0,
      position: 'absolute',
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    priceOfferContainer: {
      flex: Sizes.x,
      justifyContent: 'center',
      paddingHorizontal: Sizes.sx,
      paddingVertical: Sizes.s,
    },
    pricingRow: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    freeTag: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
    },
    rowCentered: {
      paddingHorizontal: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
    },
    productDetailRatingRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    detailSection: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xm,
    },
    bestSellerTag: {
      height: Sizes.xxl,
      width: Sizes.ex,
    },
    productImageContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingRight: Sizes.xm,
    },
    container: {
      paddingHorizontal: Sizes.s,
      marginBottom: Sizes.s,
      // flex: Sizes.x,
    },
    innerContainer: {
      backgroundColor: colors.whiteColor,
      flex: Sizes.x,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      paddingTop: Sizes.xm,
    },
    PH8: {
      paddingHorizontal: Sizes.xm,
    },
    cartButton: {
      flex: Sizes.x,
      borderColor: colors.categoryTitle,
      borderWidth: 1.5,
    },
    productImgView: {
      paddingVertical: Sizes.s,
    },
    btnStyle: {
      height: Sizes.x4l,
    },
    fOne: {
      flex: Sizes.x,
    },
    fRow: {
      flexDirection: 'row',
    },
    addCartBtn: {
      height: 31,
    },
    qtyView: {
      width: 90,
      height: 31,
    },
    qtyInput: {
      padding: 0,
      flex: Sizes.x,
      height: Sizes.x3l,
    },
    qtyPlus: {
      width: 29,
      height: Sizes.x3l,
      padding: 0,
    },
    rowAlignCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    spaceView: {
      height: Sizes.xxl,
    },
    space1View: {
      height: Sizes.x26 + Sizes.x,
    },
    reqView: {
      height: Sizes.l,
    },
    priceSpace: {
      height: Sizes.xx,
    },
    titleStyle: {
      height: Platform.OS === 'ios' ? Sizes.x6l : Sizes.xx4l + Sizes.x,
    },
    desStyle: {
      height: Sizes.x34,
    },
    btnLabel: {
      fontFamily: Fonts.Medium,
      textTransform: 'uppercase',
      paddingTop: Sizes.xs,
    },
    btnLabelnotify: {
      fontFamily: Fonts.Medium,
      borderRadius: Sizes.sx,
      backgroundColor: colors.lightgrey,
      width: Sizes.ex1 + Sizes.xms,
    },

    imageContainer: {
      position: 'relative',
      alignItems: 'center',
    },

    overlayContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      alignItems: 'center',
      justifyContent: 'center',
    },
    simpleView: {
      padding: Sizes.xm,
    },
    maskImageView: {backgroundColor: colors.white1, opacity: 0.3},
    soldOutView: {
      backgroundColor: colors.soldoutbackground,
      padding: Sizes.sx,
      borderRadius: Sizes.sx,
      paddingHorizontal: Sizes.x26,
      paddingVertical: Sizes.sx,
      alignSelf: 'center',
    },
    alignSelf: {
      alignSelf: 'center',
    },
    cartBtnStyle: {
      width: Sizes.ex1,
    },
    btnSize: {
      width: Sizes.x8l,
    },
    addTocartView: {
      marginTop: Sizes.sx,
    },
    separatorStyle: {
      borderRadius: Sizes.xs,
    },
  });

export default styles;

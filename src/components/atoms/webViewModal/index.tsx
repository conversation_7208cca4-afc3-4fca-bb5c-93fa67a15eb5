import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, ModalComponent} from 'components/atoms';
import stylesWithOutColor from './style';
import WebView from 'react-native-webview';
import {useMemo} from 'react';

type Props = {
  url: string;
  visible: boolean;
  onClose: () => void;
};

const WebViewModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose, url} = props;

  return (
    <ModalComponent
      modelStyle={styles.modalCont}
      style={styles.modelBG}
      visible={visible}>
      <View style={styles.subView}>
        <TouchableOpacity
          style={styles.fullSizesModel}
          onPress={() => onClose()}>
          <ImageIcon tintColor="textLight" size="x3l" icon="closeIcons" />
        </TouchableOpacity>
        <View style={styles.container}>
          <WebView source={{uri: url}} startInLoadingState={true} />
        </View>
      </View>
    </ModalComponent>
  );
};

export default WebViewModal;

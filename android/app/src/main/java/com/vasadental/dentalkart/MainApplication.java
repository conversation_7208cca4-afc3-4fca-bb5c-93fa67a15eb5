package com.vasadental.dentalkart;

import android.app.Application;
import android.content.Context;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.soloader.SoLoader;
import java.util.List;
import com.microsoft.codepush.react.CodePush;
import com.moengage.core.DataCenter;
import com.moengage.core.LogLevel;
import com.moengage.core.MoEngage;
import com.moengage.core.config.LogConfig;
import com.moengage.core.config.MoEDefaultConfig;
import com.moengage.core.config.NotificationConfig;
import com.moengage.pushbase.MoEPushHelper;
import com.moengage.react.MoEInitializer;
import com.xiaomi.channel.commonutils.android.Region;
import com.xiaomi.mipush.sdk.MiPushClient;
import com.vasadental.dentalkart.EISDKPackage;
import com.vasadental.dentalkart.HintPickerModule;

public class MainApplication extends Application implements ReactApplication {

  public static final String MI_APP_ID = "2882303761522231619";
  public static final String MI_APP_KEY = "5782223129619";

  private final ReactNativeHost mReactNativeHost =
      new DefaultReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          packages.add(new EISDKPackage());
          packages.add(new HintPickerPackage());
          
          // Packages that cannot be autolinked yet can be added manually here, for example:
          // packages.add(new MyReactNativePackage());
          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }

        @Override
        protected String getJSBundleFile() {
            return CodePush.getJSBundleFile();
        }

        @Override
        protected boolean isNewArchEnabled() {
          return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
        }
        @Override
        protected Boolean isHermesEnabled() {
          return BuildConfig.IS_HERMES_ENABLED;
        }
      };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  @Override
  public void onCreate() {
    super.onCreate();
    SoLoader.init(this, /* native exopackage */ false);
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      DefaultNewArchitectureEntryPoint.load();
    }
    MiPushClient.setRegion(Region.India);
    MiPushClient.registerPush(this, MI_APP_ID, MI_APP_KEY);

    ReactNativeFlipper.initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
      MoEngage.Builder moEngage =
              new MoEngage.Builder(this, "WK6FCL243291LP34PE424WUH", DataCenter.DATA_CENTER_3)
                      .configureLogs(new LogConfig(LogLevel.VERBOSE, true))
                      .configureNotificationMetaData(new NotificationConfig(R.mipmap.ic_launcher, R.mipmap.ic_launcher));

      MoEInitializer.INSTANCE.initializeDefaultInstance(getApplicationContext(), moEngage, true);
      MoEPushHelper.getInstance().setUpNotificationChannels(this.getApplicationContext());

  }
}

import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  memo,
  useRef,
} from 'react';
import {
  TouchableOpacity,
  View,
  ViewProps,
  SafeAreaView,
  TouchableWithoutFeedback,
  InteractionManager,
  Platform,
} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '@react-navigation/native';
import DropDownPicker from 'react-native-dropdown-picker';
import {useSelector} from 'react-redux';
import {t} from 'i18next';
import ImageIcon from '../imageIcon';
import Label from '../label';
import stylesWithOutColor from './style';
import Spacer from '../spacer';
import Separator from '../separator';
import ListView from '../listView';
import {AddressCard} from 'components/molecules';
import {Link} from '..';
import {navigate} from 'utils/navigationRef';
import {RootStackParamsList} from 'routes';
import localStorage from 'utils/localStorage';
import {countryPhoneCode} from 'constants/countryPhoneCode/phoneCode';
import DeliveryInfoModal from '../deliveryInfoModal';
import LinearGradient from 'react-native-linear-gradient';
import Modal from 'react-native-modal';
import {AnalyticsEvents} from 'components/organisms';
import {showInfoMessage} from 'utils/show_messages';
import useToastConfig from '../toastConfig/toastConfig';
import Toast from 'react-native-toast-message';
import {debugLog} from 'utils/debugLog';
import {getTag, distance, fullNameText} from 'utils/utils';
import Icons from 'common/icons';
import FastImage from 'react-native-fast-image';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

type Props = {
  style?: ViewProps['style'];
  addressTextStyle?: ViewProps['style'];
  index: number;
  Address: string;
  isAddress: string;
  addressList?: CustomerAddressV2[];
  setSelectedAddress: (selected: CustomerAddressV2) => void;
  selectedAddress: CustomerAddressV2;
  addressType?: 'cart' | 'ProductDetails';
  onPressModel?: () => void;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  productType?: string;
  deliveryStatusData?: PaymentMethodResponseV2;
  onAddressOrPostcodeChange: (value: CustomerAddressV2) => void;
  isAddressModalOpen: boolean;
  setIsAddressModalOpen: (value: boolean) => void;
  onPressAddNewAddress: () => void;
  cardType: string;
  useInsets: boolean;
  buyNow: boolean;
  openMapsModal: (check: boolean, value: CustomerAddressV2) => void;
  coords: any;
};

const Address = ({
  style,
  addressData,
  setSelectedAddress,
  selectedAddress,
  addressType,
  productType,
  deliveryStatusData,
  onAddressOrPostcodeChange,
  isAddressModalOpen,
  setIsAddressModalOpen,
  cardType,
  onPressAddNewAddress,
  useInsets,
  buyNow,
  openMapsModal,
  coords,
}: Props) => {
  const {colors} = useTheme();
  const {countryListings, isLoggedIn, userInfo, baseCountryData} = useSelector(
    (state: RootState) => state.app,
  );
  const insets = useSafeAreaInsets();
  const refAddress = useRef(null);
  const currentY = useRef(0);
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [pinCodeValue, setPinCodeValue] = useState('');
  const [pinCodeError, setPinCodeError] = useState('');
  const [codTextField, setCodTextField] = useState('');
  const [shippingAddress, setShippingAddress] = useState('');
  // const [open, setOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [returnInfoModel, setReturnInfoModel] = useState(false);
  const [selectedAddressState, setSelectedAddressState] = useState({});
  const [infoIcon, setInfoIcon] = useState('');
  const [addressList, setAddressList] = useState([]);
  const [billingAddress, setBillingAddress] = useState();
  const [addBillingId, setAddBillingId] = useState();
  const [shippingBillingAddressId, setShippingBillingAddressId] = useState();
  // const handlePinCodeFieldChange = (value: React.SetStateAction<string>) => {
  //   setPinCodeValue(value);
  //   setCodTextField(value);
  //   setPinCodeError('');
  // };
  const [selectAddress, setSelectAddress] = useState<CustomerAddressV2>();

  // const handleCodSubmit = async pinCodeValue => {
  //   const pincodeRegex = /^(\d{4}|\d{6})$/;
  //   if (pinCodeValue.trim().length === 6 && pincodeRegex.test(pinCodeValue)) {
  //     setIsAddressModalOpen(false);
  //     setSelectedAddress({country_id: 'IN', postcode: pinCodeValue});
  //     await localStorage.set('delivery_address', {
  //       country_id: 'IN',
  //       postcode: pinCodeValue,
  //     });
  //   } else if (pinCodeValue.trim().length === 0) {
  //     setPinCodeError('Enter a 6 digit pincode!');
  //   } else if (
  //     pinCodeValue.trim().length > 0 &&
  //     pinCodeValue.trim().length < 6
  //   ) {
  //     setPinCodeError('Entered pincode is incomplete!');
  //   } else {
  //     setPinCodeError('Entered pincode is incomplete!');
  //   }
  // };

  useEffect(() => {
    if (addressData?.length > 0) {
      const address = addressData?.filter(
        item =>
          (!item?.default_billing && item?.default_shipping) ||
          (item?.default_billing && item?.default_shipping) ||
          (!item?.default_billing && !item?.default_shipping),
      );
      const billingList = addressData?.filter(
        item => item?.default_billing && !item?.default_shipping,
      );
      setAddressList(address);
      const checkBillingAdd = billingList?.length > 0 ? true : false;
      setBillingAddress(checkBillingAdd ? billingList[0] : undefined);
      if (checkBillingAdd) {
        getShippingId(checkBillingAdd);
      } else {
        setShippingBillingAddressId(undefined);
        setAddBillingId(undefined);
        localStorage.remove('shippingAddressId');
      }
    }
  }, [addressData]);

  const getShippingId = async billingAddress => {
    const shippingAddressLinkId = await localStorage.get('shippingAddressId');
    setShippingBillingAddressId(shippingAddressLinkId);
    if (billingAddress) {
      let defaultAddress;
      if (shippingAddressLinkId) {
        defaultAddress = addressData.find(
          address => address?.id == Number(shippingAddressLinkId),
        );
      }
      if (!defaultAddress) {
        defaultAddress = addressData.find(address => address.default_shipping);
      }
      const id = defaultAddress ? defaultAddress?.id : '';
      setShippingBillingAddressId(id);
      await localStorage.set('shippingAddressId', id);
    }
  };

  useEffect(() => {
    if (!isLoggedIn) {
      setSelectAddress(selectedAddress);
      return;
    }
    if (selectedAddress || (addressList && addressList.length > 0)) {
      const defaultAddress = selectedAddress ?? addressList?.[0];
      setSelectAddress(defaultAddress);
    }
  }, [selectedAddress, isLoggedIn, addressList]);

  useEffect(() => {
    const getLocalAddressData = async () => {
      let addr = await localStorage.get('delivery_address');
      setSelectedAddressState(addr);
    };
    getLocalAddressData();
  }, []);

  // const selectCountry = useCallback(
  //   async (item: string) => {
  //     if (item === 'IN') {
  //       setSelectedAddress({country_id: item, postcode: ''});
  //       setSelectAddress({country_id: item, postcode: ''});
  //       await localStorage.set('delivery_address', {
  //         country_id: item,
  //         postcode: selectedAddress?.postcode ? selectedAddress?.postcode : '',
  //       });
  //     } else {
  //       setSelectedAddress({country_id: item, postcode: ''});
  //       setSelectAddress({country_id: item, postcode: ''});
  //       await localStorage.set('delivery_address', {
  //         country_id: item,
  //         postcode: '',
  //       });
  //     }
  //     setIsAddressModalOpen(false);
  //   },
  //   [selectedAddress?.postcode],
  // );

  // const countries = useMemo(() => {
  //   return countryListings?.map((item: Country) => ({
  //     label: item?.full_name_english,
  //     value: item?.id,
  //   }));
  // }, [countryListings]);

  const handleShippingAddressClick = useCallback(
    async (data: CustomerAddressV2, check = false) => {
      setTimeout(() => {
        setIsAddressModalOpen(check);
      }, 100);
      if (!check) {
        setSelectedAddress(data);
        setSelectAddress(data);
        await localStorage.set('delivery_address', data);
      }
    },
    [setIsAddressModalOpen, setSelectedAddress, setSelectAddress],
  );

  const onDeliverAddress = useCallback(
    address => {
      if (!address) return;
      debugLog(address, 'selectAddress');
      if (address) {
        handleShippingAddressClick(address);
        const data = {
          ...address,
        };
        AnalyticsEvents(
          'SHIPPING_DETAILS_SELECTED',
          'Shipping Details Selected',
          data,
          userInfo,
          isLoggedIn,
        );
      }
    },
    [handleShippingAddressClick, userInfo, isLoggedIn],
  );

  const onCloseAddressModal = useCallback(() => {
    setIsAddressModalOpen(false);
    setSelectAddress(selectedAddress);
    setAddBillingId(undefined);
  }, [selectedAddress]);

  const onLogin = useCallback(() => {
    setIsAddressModalOpen(false);
    setTimeout(() => {
      showInfoMessage(t('toastMassages.addressLogin'));
      navigate('Login', {
        nextScreenName: 'Cart',
        nextScreenParams: {},
      });
    }, 200);
  }, [navigate, showInfoMessage, t]);

  const addressRenderItems = useCallback(
    ({item, index}: {item: CustomerAddressV2; index: number}) => {
      return (
        <View key={index}>
          <AddressCard
            style={[
              {
                borderColor:
                  selectAddress?.id === item?.id
                    ? colors.primary
                    : item?.default_shipping === true && selectAddress === null
                    ? colors.primary
                    : colors.border,
              },
            ]}
            selectedCountry={selectedAddress?.country_id}
            selectedPostcode={selectedAddress?.postcode}
            // onSelectCountry={selectCountry}
            // onSelectPostcode={handleCodSubmit}
            cartAddress
            item={item}
            index={index}
            itemLength={addressList?.length}
            buyNow={buyNow}
            onChangeSelectAddress={(
              data: CustomerAddressV2,
              hideModal = true,
            ) => {
              setSelectAddress(data);
              handleShippingAddressClick(data);
            }}
            selected={
              selectAddress?.id === item?.id
                ? true
                : item?.default_shipping === true && selectAddress === null
                ? true
                : false
            }
            username={item?.firstname}
            city={item?.street}
            pincode={item?.postcode}
            phone={item?.telephone}
            isDefault={item?.default_shipping}
            onSelect={
              item === ''
                ? () => {
                    debugLog(item, 'item selected');
                    setIsAddressModalOpen(false);
                    onPressAddNewAddress();
                  }
                : () => {
                    debugLog(item, 'item selected');
                    if (selectAddress?.id !== item?.id) {
                      setSelectAddress(item);
                      onDeliverAddress(item);
                    }
                  }
            }
            index={index}
            cardType={cardType}
            selectedAddress={selectedAddress}
            setIsAddressModalOpen={setIsAddressModalOpen}
            onChangeLocation={(data: CustomerAddressV2, hideModal = true) => {
              setIsAddressModalOpen(false);
              openMapsModal(true, {
                ...data,
                lng: data?.longitude,
                lat: data?.latitude,
                formatted_address: data?.map_address,
              });
            }}
            coords={coords}
            addBillingId={addBillingId}
            billingAddress={billingAddress}
            shippingBillingAddressId={shippingBillingAddressId}
            onItemChangeAddress={id => {
              setAddBillingId(id && id !== addBillingId ? id : undefined);
              if (id && id !== addBillingId) {
                scrollToMove();
              }
            }}
          />
        </View>
      );
    },
    [
      selectedAddress,
      selectAddress,
      coords,
      handleShippingAddressClick,
      openMapsModal,
      setIsAddressModalOpen,
      setAddBillingId,
      addBillingId,
      billingAddress,
      shippingBillingAddressId,
      scrollToMove,
    ],
  );
  // useEffect(() => {
  //   setShippingAddress(null);
  //   if (selectedAddress && selectedAddress.id) {
  //     setShippingAddress(selectedAddress);
  //   } else {
  //     if (selectedAddress?.postcode || selectedAddress?.country_code) {
  //       if (selectedAddress?.postcode) {
  //         handlePinCodeFieldChange(selectedAddress?.postcode);
  //       }
  //       if (selectedAddress?.country_code) {
  //         setSelectedCountry(selectedAddress?.country_code);
  //       }
  //     } else {
  //       setSelectedCountry(baseCountryData?.country_id || 'IN');
  //     }
  //   }
  // }, [baseCountryData?.country_id, selectedAddress]);

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const address = useMemo(() => {
    if (selectedAddress) {
      const {street, city, region, map_address, customer_street_2} =
        selectedAddress;
      let street2 = '';
      if (customer_street_2) {
        street2 = customer_street_2;
      } else if (map_address) {
        const parts = map_address?.split(',');
        const beforePin = parts
          .slice(0, parts.length - 2)
          .join(',')
          .trim();
        street2 = beforePin;
      }
      return `${street?.length > 0 ? street?.filter(Boolean)?.join(', ') : ''}${
        street2 ? ', ' + street2 : ''
      }${city ? ', ' + city : ''}${
        region?.region ? ', ' + region?.region : ''
      }`;
    }
    return '';
  }, [selectedAddress]);

  let defaultValue =
    selectedAddress?.country_id === 'IN'
      ? selectedAddress?.postcode
      : selectedAddress?.country_id;
  defaultValue = defaultValue || selectedAddressState?.postcode;

  const {toastConfig} = useToastConfig();

  const onChangeAddress = useCallback(() => {
    if (!isLoggedIn) {
      showInfoMessage(t('toastMassages.addressLogin'));
      navigate('Login', {
        nextScreenName: 'Cart',
        nextScreenParams: {},
      });
      return;
    }
    const hasAddresses = addressList?.length > 0;
    if (hasAddresses) {
      setIsAddressModalOpen(true);
      return;
    }
    setIsAddressModalOpen(false);
    openMapsModal(true, {
      ...selectAddress,
      lng: selectAddress?.longitude,
      lat: selectAddress?.latitude,
      formatted_address: selectAddress?.map_address,
    });
  }, [
    isLoggedIn,
    navigate,
    addressList,
    selectAddress,
    setIsAddressModalOpen,
    openMapsModal,
    showInfoMessage,
    t,
  ]);

  const handleInfoIconPress = useCallback(type => {
    if (!type) return;
    setReturnInfoModel(true);
    setInfoIcon(type);
  }, []);

  const closeAddressModal = useCallback(() => {
    InteractionManager.runAfterInteractions(() => {
      setIsAddressModalOpen(prevState => !prevState);
    });
  }, []);

  const closeReturnModal = useCallback(() => {
    InteractionManager.runAfterInteractions(() => {
      setReturnInfoModel(false);
    });
  }, []);

  const kmText = useMemo(() => {
    if (
      !coords?.latitude ||
      !coords?.longitude ||
      !selectedAddress?.latitude ||
      !selectedAddress?.longitude
    )
      return '';
    const distanceValue = distance(
      [coords?.latitude, coords?.longitude],
      [selectedAddress?.latitude, selectedAddress?.longitude],
      'km',
    );
    const km = parseFloat(distanceValue).toFixed(2);
    const isHere = Number(km) === 0 || Number(km) < 0.01;
    return `${isHere ? '' : `${km} ${t('address.cLocation')}`}`;
  }, [coords, selectedAddress, t]);

  const scrollToMove = useCallback(() => {
    if (refAddress.current) {
      refAddress.current.scrollTo({y: currentY.current + 200, animated: true});
    }
  }, []);

  const handleScroll = event => {
    currentY.current = event.nativeEvent.contentOffset.y;
  };

  const deliveryStatus = () => {
    return (
      <View>
        {!!deliveryStatusData?.delivery_info?.[0]?.max_delivery_days_text ||
        !!deliveryStatusData?.service_availability?.[0]?.message ? (
          <>
            <View style={styles.deliveryTruckView}>
              <View style={styles.fRow}>
                {!!deliveryStatusData?.service_availability?.[0]?.message ? (
                  <View style={styles.deliveryItem}>
                    <ImageIcon
                      size="xxxl"
                      icon={
                        deliveryStatusData?.service_availability?.[0]?.services
                          ?.COD
                          ? 'codIcon'
                          : 'nonReturnable'
                      }
                    />
                    <Spacer size="s" type="Horizontal" />
                    <Label
                      fontFamily="Medium"
                      color={
                        deliveryStatusData?.service_availability?.[0]?.services
                          ?.COD
                          ? 'categoryTitle'
                          : 'red3'
                      }
                      size="mx"
                      text={`${deliveryStatusData?.service_availability?.[0]?.message?.replace(
                        /\.$/,
                        '',
                      )}`}
                      textTransform={'capitalize'}
                    />
                    <TouchableOpacity
                      onPress={() => handleInfoIconPress('codCheck')}>
                      <ImageIcon
                        size="xxl"
                        icon="infoCircle"
                        tintColor={
                          deliveryStatusData?.service_availability?.[0]
                            ?.services?.COD
                            ? 'categoryTitle'
                            : 'red3'
                        }
                      />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View />
                )}
                <Separator color="grey2" Vertical style={styles.seperator} />

                <View style={styles.deliveryItem}>
                  <ImageIcon size="xxxl" icon="compliancesIcon" />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    fontFamily="Medium"
                    color="categoryTitle"
                    size="mx"
                    text={t('cart.compliances')}
                  />
                  <TouchableOpacity
                    onPress={() => handleInfoIconPress('compliances')}>
                    <ImageIcon size="xxl" icon="infoCircle" />
                  </TouchableOpacity>
                </View>
              </View>
              {!!deliveryStatusData?.delivery_info?.[0]
                ?.max_delivery_days_text ? (
                <View style={styles.deliveryItem}>
                  <ImageIcon size="xxxl" icon="deliveryInfoIcon" />
                  <Spacer size="xm" type="Horizontal" />
                  <Label
                    fontFamily="Medium"
                    color="categoryTitle"
                    size="mx"
                    text={
                      deliveryStatusData?.delivery_info?.[0]
                        ?.max_delivery_days_text
                    }
                  />
                  <TouchableOpacity
                    onPress={() => handleInfoIconPress('deliveryInfo')}>
                    <ImageIcon size="xxl" icon="infoCircle" />
                  </TouchableOpacity>
                </View>
              ) : (
                <View />
              )}
            </View>
          </>
        ) : (
          <View />
        )}
      </View>
    );
  };

  return (
    <View>
      {cardType === 'cart' ? (
        <>
          <View style={[styles.deliveryCont, style]}>
            <View style={styles.addressView}>
              <View style={styles.tagView}>
                <ImageIcon size="xsl" icon={getTag(selectedAddress?.tag)} />
              </View>
              <Spacer type="Horizontal" size="s" />
              <View style={styles.fOne}>
                <Label
                  text={`${t('address.deliver')}: `}
                  size="mx"
                  fontFamily="Medium"
                  color="text">
                  <Label
                    text={
                      selectedAddress
                        ? selectedAddress?.country_id === 'IN'
                          ? selectedAddress?.postcode || 'IN'
                          : selectedAddress?.country_id
                        : 'IN'
                    }
                    size="mx"
                    fontFamily="Medium"
                    color="text"
                  />
                </Label>
                {address ? (
                  <Label
                    numberOfLines={2}
                    text={address}
                    size="m"
                    fontFamily="Regular"
                    color="grey"
                    textTransform="capitalize"
                  />
                ) : (
                  <View />
                )}
                {kmText ? (
                  <>
                    <Spacer size="s" />
                    <Label
                      numberOfLines={1}
                      ellipsizeMode="tail"
                      text={kmText}
                      size="m"
                      fontFamily="Medium"
                      color="vividOrange"
                    />
                  </>
                ) : (
                  <View />
                )}
              </View>
            </View>
            <Spacer type="Horizontal" size="xm" />
            <TouchableOpacity
              onPress={onChangeAddress}
              activeOpacity={0.7}
              style={styles.changeView}>
              <Label
                text={
                  isLoggedIn && addressList?.length > 0
                    ? t('address.change')
                    : t('address.addYourAddress')
                }
                size="m"
                fontFamily="Medium"
                color="categoryTitle"
              />
            </TouchableOpacity>
          </View>
          <Spacer size="xm" />
          {deliveryStatus()}
        </>
      ) : (
        <View>
          {deliveryStatus()}
          <Spacer size="xm" />
          <Label
            numberOfLines={2}
            text={t('addressCard.addressDetails')}
            size="l"
            fontFamily="Medium"
            weight="500"
            color="text"
            textTransform="capitalize"
            style={styles.addDesTxt}
          />
          <Spacer size="xm" />
          <View style={styles.checkoutCard}>
            <View style={styles.deliveryItem}>
              <View style={styles.tagView}>
                <ImageIcon size="l" icon={getTag(selectedAddress?.tag)} />
              </View>
              <Label
                text={selectedAddress?.tag || 'Home'}
                fontFamily="Medium"
                size="l"
                color="text"
                textTransform="capitalize"
              />
              <View style={styles.vLine} />
              <Label
                text={t('addressCard.shippingAddress')}
                fontFamily="Medium"
                size="mx"
                color="text"
                textTransform="capitalize"
              />
              <View style={styles.fOne} />
              {selectedAddress?.default_shipping ? (
                <FastImage
                  style={styles.defaultGif}
                  source={Icons.defaultGif}
                  resizeMode="contain"
                />
              ) : (
                <View />
              )}
            </View>
            <View style={styles.hLine} />
            <View style={styles.fOne}>
              {selectedAddress?.firstname && (
                <>
                  <Label
                    text={fullNameText(selectedAddress)}
                    fontFamily="SemiBold"
                    size="mx"
                    color="text2"
                    textTransform="capitalize"
                  />
                  <Spacer size="s" />
                </>
              )}

              {address ? (
                <Label
                  numberOfLines={3}
                  text={address + `, PIN Code ${selectedAddress?.postcode}`}
                  size="mx"
                  fontFamily="Medium"
                  color="text2"
                  textTransform="capitalize"
                />
              ) : (
                <Label
                  text={`${t('address.deliver')}: `}
                  size="mx"
                  fontFamily="Medium"
                  color="text">
                  <Label
                    text={
                      selectedAddress
                        ? selectedAddress?.country_id === 'IN'
                          ? selectedAddress?.postcode || 'IN'
                          : selectedAddress?.country_id
                        : 'IN'
                    }
                    size="mx"
                    fontFamily="SemiBold"
                    color="text"
                  />
                </Label>
              )}
              {kmText ? (
                <>
                  <Spacer size="s" />
                  <Label
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    text={kmText}
                    size="m"
                    fontFamily="Medium"
                    color="vividOrange"
                  />
                </>
              ) : (
                <View />
              )}
              {selectedAddress?.telephone && (
                <>
                  <Spacer size="s" />
                  <View style={styles.deliveryItem}>
                    <ImageIcon size="mx" icon="phone1" />
                    <Spacer size="s" type="Horizontal" />
                    <Label
                      text={`${selectedAddress?.telephone}${
                        selectedAddress?.custom_attributes?.length > 0 &&
                        selectedAddress?.custom_attributes[0].value
                          ? ', ' + selectedAddress?.custom_attributes[0].value
                          : ''
                      }`}
                      fontFamily="Medium"
                      size="m"
                      color="text2"
                      textTransform="capitalize"
                    />
                  </View>
                </>
              )}

              {selectedAddress?.vat_id ? (
                <>
                  <Spacer size="s" />
                  <View style={styles.deliveryItem}>
                    <ImageIcon size="xx" icon="gst" tintColor="text2" />
                    <Spacer size="s" type="Horizontal" />
                    <Label
                      text={selectedAddress?.vat_id}
                      fontFamily="Medium"
                      size="m"
                      color="text2"
                    />
                  </View>
                </>
              ) : (
                <View />
              )}
            </View>
            {shippingBillingAddressId &&
              Number(shippingBillingAddressId) == selectedAddress?.id && (
                <>
                  <View style={styles.hLine} />
                  <View style={styles.deliveryItem}>
                    <View style={styles.tagView}>
                      <ImageIcon size="l" icon="checkList" />
                    </View>
                    <Label
                      text={t('addressCard.billingAddress')}
                      fontFamily="Medium"
                      size="mx"
                      color="text"
                      textTransform="capitalize"
                    />
                  </View>
                  <Spacer size="s" />
                  <View style={styles.fOne}>
                    <Label
                      text={fullNameText(billingAddress)}
                      fontFamily="SemiBold"
                      size="mx"
                      color="text2"
                      textTransform="capitalize"
                    />
                    <Spacer size="s" />

                    <Label
                      numberOfLines={3}
                      text={`${billingAddress?.street
                        ?.filter(Boolean)
                        .join(' ')}${
                        billingAddress?.city ? ', ' + billingAddress?.city : ''
                      }${
                        billingAddress?.region?.region
                          ? ', ' + billingAddress?.region?.region
                          : ''
                      }${
                        billingAddress?.postcode
                          ? ' - ' + billingAddress?.postcode
                          : ''
                      }`}
                      size="mx"
                      fontFamily="Medium"
                      color="text2"
                      textTransform="capitalize"
                    />

                    <Spacer size="s" />
                    <View style={styles.deliveryItem}>
                      <ImageIcon size="mx" icon="phone1" />
                      <Spacer size="s" type="Horizontal" />
                      <Label
                        text={`${billingAddress?.telephone}${
                          billingAddress?.custom_attributes?.length > 0 &&
                          billingAddress?.custom_attributes[0].value
                            ? ', ' + billingAddress?.custom_attributes[0].value
                            : ''
                        }`}
                        fontFamily="Medium"
                        size="m"
                        color="text2"
                        textTransform="capitalize"
                      />
                    </View>

                    {billingAddress?.vat_id ? (
                      <>
                        <Spacer size="s" />
                        <View style={styles.deliveryItem}>
                          <ImageIcon size="xx" icon="gst" tintColor="text2" />
                          <Spacer size="s" type="Horizontal" />
                          <Label
                            text={billingAddress?.vat_id}
                            fontFamily="Medium"
                            size="m"
                            color="text2"
                          />
                        </View>
                      </>
                    ) : (
                      <View />
                    )}
                  </View>
                </>
              )}

            <Spacer type="Horizontal" size="xm" />
            <View style={styles.fRow}>
              <View style={styles.fOne} />
              <TouchableOpacity
                onPress={onChangeAddress}
                activeOpacity={0.7}
                style={styles.changeView1}>
                <ImageIcon
                  size="xx"
                  icon="arrowLeft1"
                  tintColor="vividOrange"
                />
                <Spacer size="xm" type="Horizontal" />
                <Label
                  text={
                    isLoggedIn && addressList?.length > 0
                      ? t('otherText.changeAddress')
                      : t('address.addYourAddress')
                  }
                  size="m"
                  fontFamily="SemiBold"
                  weight="600"
                  color="vividOrange"
                  textTransform="uppercase"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* ========modal======================== */}
      {returnInfoModel && (
        <DeliveryInfoModal
          visible={returnInfoModel}
          onClose={closeReturnModal}
          infoIcon={infoIcon}
          deliveryStatusData={deliveryStatusData}
        />
      )}
      {isAddressModalOpen && (
        <Modal
          onBackButtonPress={onCloseAddressModal}
          isVisible={isAddressModalOpen}
          animationIn="fadeIn"
          animationOut="fadeOut"
          backdropOpacity={0.01}
          style={styles.modalStyle}>
          <SafeAreaView style={styles.fOne}>
            <View style={styles.modelBackGround}>
              <View>
                <View style={styles.mainCont}>
                  <Label
                    style={styles.orText}
                    text={t('otherText.selectDeliveryAddress')}
                    color="text"
                    fontFamily="Medium"
                  />
                  <TouchableOpacity onPress={onCloseAddressModal}>
                    <ImageIcon icon={'cross'} size="xxl" tintColor="text" />
                  </TouchableOpacity>
                </View>
              </View>
              <Spacer size="sx" />
              {/* <Label
                size="mx"
                fontFamily="Medium"
                color="text2"
                text={t('otherText.selectDeliveryAddress')}
              /> */}
              <View style={styles.fOne}>
                <KeyboardAwareScrollView
                  innerRef={ref => {
                    refAddress.current = ref;
                  }}
                  onScroll={handleScroll}
                  contentContainerStyle={styles.scrollStyle}
                  showsVerticalScrollIndicator={false}
                  extraScrollHeight={Platform.OS === 'ios' ? 100 : 200}
                  enableOnAndroid={true}
                  keyboardShouldPersistTaps="always">
                  <ListView
                    keyExtractor={listViewKeyExtractor}
                    data={
                      isLoggedIn
                        ? [
                            '',
                            ...[...addressList].sort((a, b) => {
                              if (a.default_shipping && !b.default_shipping)
                                return -1;
                              if (!a.default_shipping && b.default_shipping)
                                return 1;
                              if (a.id === selectAddress?.id) return -1;
                              if (b.id === selectAddress?.id) return 1;
                              return 0;
                            }),
                          ]
                        : ['']
                    }
                    renderItem={addressRenderItems}
                    keyboardShouldPersistTaps="always"
                  />
                  {!isLoggedIn ? (
                    <View style={styles.addressBtnView}>
                      <Link
                        onPress={onLogin}
                        color="primary"
                        size="m"
                        fontFamily="Bold"
                        text={t('address.seeAddress') + '  '}
                      />
                    </View>
                  ) : null}
                </KeyboardAwareScrollView>
                {/* {isLoggedIn && addressList?.length > 0 && (
                  <TouchableOpacity
                    onPress={onDeliverAddress}
                    style={styles.deliveryBtnView}>
                    <LinearGradient
                      style={styles.deliveryBtn}
                      start={{x: 0, y: 0}}
                      end={{x: 1, y: 1}}
                      colors={[colors.coral, colors.persimmon]}>
                      <Label
                        text={t('buttons.deliverHere')}
                        size="mx"
                        color="whiteColor"
                        fontFamily="Medium"
                      />
                    </LinearGradient>
                  </TouchableOpacity>
                )} */}
                {/* <View style={{  display: open ? 'flex' : 'none'}}>
                  <DropDownPicker
                    arrowIconStyle={styles.arrowStyle}
                    placeholderStyle={styles.placeholderStyles}
                    searchable
                    open={open}
                    value={selectedCountry || null}
                    items={countries || []}
                    setOpen={setOpen}
                    onSelectItem={item => selectCountry(item?.value)}
                    style={styles.countryPicker}
                    placeholder={t('address.countrySelect')}
                    listMode="MODAL"
                  />
                </View> */}
              </View>
              <View
                style={{
                  marginBottom:
                    Platform.OS === 'ios'
                      ? insets?.bottom + 40
                      : insets?.bottom,
                }}
              />
            </View>
          </SafeAreaView>
          <Toast config={toastConfig} swipeable={false} />
        </Modal>
      )}
    </View>
  );
};

export default memo(Address);

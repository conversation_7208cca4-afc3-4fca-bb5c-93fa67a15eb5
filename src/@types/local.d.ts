import {Theme} from '@react-navigation/native';
declare module 'react-native-pie';
type initialStoreProps = {
  loading: boolean;
  cart: any;
  cartCount: number;
  cartId: null | string;
};
type CardDataType = {
  id: number;
  en: string;
  local: string;
};

type ProductCardType = {
  id: number;
  title: string;
  price: number;
};

type AddressType = {
  id?: number;
  name: string;
  address: string;
  pincode: number;
  state: string;
  city: string;
  isDefault?: boolean;
};

type InputBoxType = {
  placeholder?: string;
  onChangeText: (text: string) => void;
  value?: string;
  error?: string;
};

type LabelType = {
  text: string | number | undefined | null;
  align?: 'left' | 'right' | 'center' | 'justify';
};

type DropDownOptionsType = {
  key: string;
  value: string;
};

type AddOnsType = {
  id: number;
  name: string;
  price: number;
  description: string;
};

interface CustomTheme extends Theme {
  colors: Theme['colors'] & {
    text: string;
    notification: string;
    primary: string;
    secondary: string;
    background: string;
    background2: string;
    background3: string;
    background4: string;
    background5: string;
    card: string;
    textSecondary: string;
    textDarkSecondary: string;
    textLight: string;
    textError: string;
    text5: string;
    text6: string;
    text7: string;
    darkGray: string;
    verifyPopupBackGround: string;
    lightPrimaryColor: string;
    velvetLightPink2: string;
    velvetLightPink: string;
    lightPink: string;
    smoothPink: string;
    lightGray: string;
    sunnyOrange: string;
    grassGreen: string;
    tabInActive: string;
    yellowToast: string;
    redToast: string;
    borderGrey: string;
    tabActive: string;
    border: string;
    borderColor: string;
    borderColor1: string;
    redToastNew: string;
    borderColor2: string;
    placHoldersTextColor: string;
    disabledButtonColor: string;
    disabledLabelColor: string;
    lightGrayBackground: string;
    green5: string;
    blackIcons: string;
    goBackIconColor: string;
    cartCountColor: string;
    borderSaprater: string;
    offerItemCartBg: string;
    offerTitleBlue: string;
    blueInfo: string;
    offerSubTitleBlue: string;
    offerTitleGreen: string;
    offerSubTitleGreen: string;
    offerTitleGolden: string;
    offerSubTitleGolden: string;
    topItemsBg: string;
    topBrandsIconBg: string;
    subCategriesBg: string;
    linkText: string;
    itemsBorderColor: string;
    lightRedTextColor: string;
    soldOutColor: string;
    filterBg: string;
    filterLable: string;
    modelBG: string;
    decrementColor: string;
    incremetColor: string;
    modelBg: string;
    modelCategoryBg: string;
    actionSheetBG: string;
    buttonLightBlue: string;
    smoothBlue: string;
    borderRound: string;
    grayTextColor: string;
    gray10: string;
    memberShipBg: string;
    planeCartBg: string;
    currentPlane: string;
    memberShipOrders: string;
    textDecorationsColor: string;
    removePrize: string;
    helpCenterBg: string;
    shadowBg: string;
    socialButton: string;
    whiteColor: string;
    white1: string;
    modelBackgroundTransparent: string;
    heartBG: string;
    placeHoldersTextColor: string;
    placeHoldersTextColor1: string;
    filterLabel: string;
    shadowColor: string;
    incrementColor: string;
    placeholderColor: string;
    veryDarkGray: string;
    railsBg: string;
    selectedRailsBg: string;
    thumbColor: string;
    lightRed: string;
    skyBlue: string;
    skyBlue2: string;
    skyBlue3: string;
    skyBlue4: string;
    skyBlue5: string;
    skyBlue6: string;
    skyBlue7: string;
    skyBlue8: string;
    skyBlue9: string;
    skyBlue10: string;
    skyBlue11: string;
    skyBlue12: string;
    skyBlue13: string;
    skyBlue14: string;
    skyBlue15: string;
    skyBlue16: string;
    skyBlue17: string;
    skyBlue18: string;
    skyBlue19: string;
    skyBlue20: string;
    skyBlue21: string;
    warnToast1: string;
    warnToast2: string;
    skyBlue22: string;
    linen: string;
    mistyRose: string;
    aliceBlue: string;
    lightCyan: string;
    shadow: string;
    underlayColor: string;
    warnToast: string;
    planeCardShadow: string;
    darkGreen: string;
    topBorder: string;
    blackColor: string;
    veryPale: string;
    smoothOrange: string;
    smoothRed: string;
    red1: string;
    outStock: string;
    outStockBG: string;
    lightPurple: string;
    blue: string;
    blue1: string;
    blue2: string;
    blue3: string;
    blue4: string;
    cornflowerBlue: string;
    transparentColor: string;
    lightGrey1: string;
    bloodyRed: string;
    green: string;
    lightGreen: string;
    paleGreen: string;
    disabledText: string;
    darkGrayColor: string;
    goldenColor: string;
    blankGray: string;
    whiteGray: string;
    offWhite: string;
    offWhite1: string;
    offWhite2: string;
    veryLight: string;
    progress: string;
    chemSpider: string;
    transparentHighlight: string;
    switch: string;
    extraGrey: string;
    labelBrand: string;
    pigeonColor: string;
    smoothGreen: string;
    sectionHeaderTitle: string;
    videoCountBackgroundColor: string;
    commentUserNameColor: string;
    commentDate: string;
    notice: string;
    progressBackgroundColor: string;
    lightGrayishBlue: string;
    categoryTitle: string;
    categoryTitle2: string;
    categoryTitle4: string;
    categoryTitle5: string;
    categoryTitle6: string;
    categoryTitle7: string;
    categoryTitle8: string;
    grey: string;
    grey2: string;
    grey3: string;
    grey4: string;
    grey5: string;
    grey6: string;
    grey7: string;
    grey8: string;
    grey9: string;
    grey10: string;
    grey11: string;
    newPrimary: string;
    sunnyOrange1: string;
    sunnyOrange2: string;
    sunnyOrange3: string;
    sunnyOrange4: string;
    sunnyOrange5: string;
    sunnyOrange6: string;
    orange: string;
    fadeOrange: string;
    orange1: string;
    green2: string;
    black: string;
    black1: string;
    text2: string;
    modalShadow: string;
    smoothGrey: string;
    text3: string;
    text4: string;
    textTransparent: string;
    imageBorderColor: string;
    silkBlue: string;
    silkBlue2: string;
    lightblue: string;
    lightblue1: string;
    lightgrey: string;
    blueLagoon: string;
    greenRgb0: string;
    greenRgb3: string;
    violet1: string;
    bgPaginate: string;
    shadow2: string;
    green4: string;
    lightGray2: string;
    blueRomance: string;
    greenLight: string;
    internationalOrange: string;
    parsleyGreen: string;
    darkBlue: string;
    oysterBay: string;
    newSunnyOrange: string;
    orangeOffer: string;
    yellow: string;
    pinkBlush: string;
    pink: string;
    orangeHalf: string;
    aliceBlue1: string;
    blue2Offer: string;
    darkCerulean: string;
    navyBlue: string;
    paleCornflowerBlue: string;
    coral: string;
    persimmon: string;
    lavender: string;
    whiteSmoke: string;
    whisper: string;
    cobalt: string;
    gray10: string;
    lightDisabled: string;
    bubbles: string;
    orient16: string;
    jacksonsPurple: string;
    blackberry: string;
    blackRussian: string;
    pattensBlue24: string;
    pattensBlue: string;
    pumpkinOrange: string;
    darkPink: string;
    nightRider: string;
    mandyPink: string;
    mintCream: string;
    baliHaiLightGray: string;
    caramel: string;
    green3: string;
    gray2: string;
    white46: string;
    whiteSmoke35: string;
    persimmonOrange44: string;
    mandarianOrange: string;
    coralOrange: string;
    prussianBlue: string;
    cobaltBlue: string;
    myWishlistBG: string;
    hawkesBlue: string;
    white13: string;
    dimGray25: string;
    black0: string;
    blackTransparent: string;
    orangeLight44: string;
    wedgewoodBlue: string;
    cornflowerBlue1: string;
    lavenderRose: string;
    soldoutbackground: string;
    purpleMountain: string;
    buccaneer: string;
    softPeach: string;
    persianRed: string;
    cosmos: string;
    darkGoldenrod: string;
    oasis: string;
    solitude: string;
    lightSlateBlue: string;
    cerise0: string;
    cerise24: string;
    soldoutbackground1: string;
    spray: string;
    bostonBlue: string;
    electricBlue: string;
    buttercup: string;
    lightSkyBlue: string;
    cerulean: string;
    cerulean1: string;
    black25: string;
    white28: string;
    jordyBlue61: string;
    jordyBlue34: string;
    aliceBlue2: string;
    lightPink1: string;
    red20: string;
    whiteIce: string;
    lightSlateBlue1: string;
    orient12: string;
    danube100: string;
    danube0: string;
    haiti: string;
    tyrianPurple: string;
    mardiGras: string;
    blackPearl: string;
    cornflowerBlue2: string;
    paleMagenta: string;
    hawkesBlue1: string;
    marqueeColor: string;
    venetianRed: string;
    white60: string;
    quartz: string;
    pattensBlue70: string;
    torchRed: string;
    aliceBlue3: string;
    crusoeGreen: string;
    whiteIce1: string;
    britishRacingGreen: string;
    britishRacingGree: string;
    robRoy: string;
    cornField: string;
    resolutionBlue: string;
    royalBlue: string;
    persimmon1: string;
    dimGray: string;
    islandGreen: string;
    vividOrange: string;
    whiteBlue: string;
    limeGreen: string;
    darkBlue1: string;
    strongRed: string;
    darkCyanGreen: string;
    darkCyanGreen1: string;
    lightOrange: string;
    lightRed1: string;
    brown: string;
    limeGreen1: string;
    whiteBlue1: string;
    softOrange: string;
    lightCyan1: string;
    lightLimeGreen: string;
    darkLimeGreen: string;
    lightBlue: string;
    strongBlue: string;
    lightRed2: string;
    strongRed1: string;
    darkLimeGreen1: string;
    strongBlue1: string;
    strongRed2: string;
    cyanGreen: string;
    grey70: string;
    softRed: string;
    brightOrange: string;
    lightGrey: string;
  };
}

type Theme = {
  dark: boolean;
  colors: {
    text: string;
    notification: string;
    primary: string;
    secondary: string;
    background: string;
    background2: string;
    background3: string;
    background4: string;
    background5: string;
    card: string;
    textSecondary: string;
    textDarkSecondary: string;
    textLight: string;
    textError: string;
    text5: string;
    text6: string;
    text7: string;
    darkGray: string;
    verifyPopupBackGround: string;
    lightPrimaryColor: string;
    velvetLightPink2: string;
    velvetLightPink: string;
    lightPink: string;
    smoothPink: string;
    lightGray: string;
    sunnyOrange: string;
    grassGreen: string;
    tabInActive: string;
    yellowToast: string;
    redToast: string;
    borderGrey: string;
    tabActive: string;
    border: string;
    borderColor: string;
    borderColor1: string;
    redToastNew: string;
    borderColor2: string;
    placHoldersTextColor: string;
    disabledButtonColor: string;
    disabledLabelColor: string;
    lightGrayBackground: string;
    green5: string;
    blackIcons: string;
    goBackIconColor: string;
    cartCountColor: string;
    borderSaprater: string;
    offerItemCartBg: string;
    offerTitleBlue: string;
    blueInfo: string;
    offerSubTitleBlue: string;
    offerTitleGreen: string;
    offerSubTitleGreen: string;
    offerTitleGolden: string;
    offerSubTitleGolden: string;
    topItemsBg: string;
    topBrandsIconBg: string;
    subCategriesBg: string;
    linkText: string;
    itemsBorderColor: string;
    lightRedTextColor: string;
    soldOutColor: string;
    filterBg: string;
    filterLable: string;
    modelBG: string;
    decrementColor: string;
    incremetColor: string;
    modelBg: string;
    modelCategoryBg: string;
    actionSheetBG: string;
    buttonLightBlue: string;
    smoothBlue: string;
    borderRound: string;
    grayTextColor: string;
    gray10: string;
    memberShipBg: string;
    planeCartBg: string;
    currentPlane: string;
    memberShipOrders: string;
    textDecorationsColor: string;
    removePrize: string;
    helpCenterBg: string;
    shadowBg: string;
    socialButton: string;
    whiteColor: string;
    white1: string;
    modelBackgroundTransparent: string;
    heartBG: string;
    placeHoldersTextColor: string;
    placeHoldersTextColor1: string;
    filterLabel: string;
    shadowColor: string;
    incrementColor: string;
    placeholderColor: string;
    veryDarkGray: string;
    railsBg: string;
    selectedRailsBg: string;
    thumbColor: string;
    lightRed: string;
    skyBlue: string;
    skyBlue2: string;
    skyBlue3: string;
    skyBlue4: string;
    skyBlue5: string;
    skyBlue6: string;
    skyBlue7: string;
    skyBlue8: string;
    skyBlue9: string;
    skyBlue10: string;
    skyBlue11: string;
    skyBlue12: string;
    skyBlue13: string;
    skyBlue14: string;
    skyBlue15: string;
    skyBlue16: string;
    skyBlue17: string;
    skyBlue18: string;
    skyBlue19: string;
    skyBlue20: string;
    skyBlue21: string;
    warnToast1: string;
    warnToast2: string;
    skyBlue22: string;
    linen: string;
    mistyRose: string;
    aliceBlue: string;
    lightCyan: string;
    shadow: string;
    underlayColor: string;
    warnToast: string;
    planeCardShadow: string;
    darkGreen: string;
    topBorder: string;
    blackColor: string;
    veryPale: string;
    smoothOrange: string;
    smoothRed: string;
    red1: string;
    outStock: string;
    outStockBG: string;
    lightPurple: string;
    blue: string;
    blue1: string;
    blue2: string;
    blue3: string;
    blue4: string;
    cornflowerBlue: string;
    transparentColor: string;
    lightGrey1: string;
    bloodyRed: string;
    green: string;
    lightGreen: string;
    paleGreen: string;
    disabledText: string;
    darkGrayColor: string;
    goldenColor: string;
    blankGray: string;
    whiteGray: string;
    offWhite: string;
    offWhite1: string;
    offWhite2: string;
    veryLight: string;
    progress: string;
    chemSpider: string;
    transparentHighlight: string;
    switch: string;
    extraGrey: string;
    labelBrand: string;
    pigeonColor: string;
    smoothGreen: string;
    sectionHeaderTitle: string;
    videoCountBackgroundColor: string;
    commentUserNameColor: string;
    commentDate: string;
    notice: string;
    progressBackgroundColor: string;
    lightGrayishBlue: string;
    categoryTitle: string;
    categoryTitle2: string;
    categoryTitle4: string;
    categoryTitle5: string;
    categoryTitle6: string;
    categoryTitle7: string;
    categoryTitle8: string;
    grey: string;
    grey2: string;
    grey3: string;
    grey4: string;
    grey5: string;
    grey6: string;
    grey7: string;
    grey8: string;
    grey9: string;
    grey10: string;
    grey11: string;
    newPrimary: string;
    sunnyOrange1: string;
    sunnyOrange2: string;
    sunnyOrange3: string;
    sunnyOrange4: string;
    sunnyOrange5: string;
    sunnyOrange6: string;
    orange: string;
    fadeOrange: string;
    orange1: string;
    green2: string;
    black: string;
    black1: string;
    text2: string;
    modalShadow: string;
    smoothGrey: string;
    text3: string;
    text4: string;
    textTransparent: string;
    imageBorderColor: string;
    silkBlue: string;
    silkBlue2: string;
    lightblue: string;
    lightblue1: string;
    lightgrey: string;
    blueLagoon: string;
    greenRgb0: string;
    greenRgb3: string;
    violet1: string;
    bgPaginate: string;
    shadow2: string;
    green4: string;
    lightGray2: string;
    blueRomance: string;
    greenLight: string;
    internationalOrange: string;
    parsleyGreen: string;
    darkBlue: string;
    oysterBay: string;
    newSunnyOrange: string;
    orangeOffer: string;
    yellow: string;
    pinkBlush: string;
    pink: string;
    orangeHalf: string;
    aliceBlue1: string;
    blue2Offer: string;
    darkCerulean: string;
    navyBlue: string;
    paleCornflowerBlue: string;
    coral: string;
    persimmon: string;
    lavender: string;
    whiteSmoke: string;
    whisper: string;
    cobalt: string;
    gray10: string;
    lightDisabled: string;
    bubbles: string;
    orient16: string;
    jacksonsPurple: string;
    blackberry: string;
    blackRussian: string;
    pattensBlue24: string;
    pattensBlue: string;
    pumpkinOrange: string;
    darkPink: string;
    nightRider: string;
    mandyPink: string;
    mintCream: string;
    baliHaiLightGray: string;
    caramel: string;
    green3: string;
    gray2: string;
    white46: string;
    whiteSmoke35: string;
    persimmonOrange44: string;
    mandarianOrange: string;
    coralOrange: string;
    prussianBlue: string;
    cobaltBlue: string;
    myWishlistBG: string;
    hawkesBlue: string;
    white13: string;
    dimGray25: string;
    black0: string;
    blackTransparent: string;
    orangeLight44: string;
    wedgewoodBlue: string;
    cornflowerBlue1: string;
    lavenderRose: string;
    soldoutbackground: string;
    purpleMountain: string;
    buccaneer: string;
    softPeach: string;
    persianRed: string;
    cosmos: string;
    darkGoldenrod: string;
    oasis: string;
    solitude: string;
    lightSlateBlue: string;
    cerise0: string;
    cerise24: string;
    soldoutbackground1: string;
    spray: string;
    bostonBlue: string;
    electricBlue: string;
    buttercup: string;
    lightSkyBlue: string;
    cerulean: string;
    cerulean1: string;
    black25: string;
    white28: string;
    jordyBlue61: string;
    jordyBlue34: string;
    aliceBlue2: string;
    lightPink1: string;
    red20: string;
    whiteIce: string;
    lightSlateBlue1: string;
    orient12: string;
    danube100: string;
    danube0: string;
    haiti: string;
    tyrianPurple: string;
    mardiGras: string;
    blackPearl: string;
    cornflowerBlue2: string;
    paleMagenta: string;
    hawkesBlue1: string;
    marqueeColor: string;
    venetianRed: string;
    white60: string;
    quartz: string;
    pattensBlue70: string;
    torchRed: string;
    aliceBlue3: string;
    crusoeGreen: string;
    whiteIce1: string;
    britishRacingGreen: string;
    britishRacingGree: string;
    robRoy: string;
    cornField: string;
    resolutionBlue: string;
    royalBlue: string;
    persimmon1: string;
    orient20: string;
    dimGray: string;
    islandGreen: string;
    vividOrange: string;
    whiteBlue: string;
    limeGreen: string;
    darkBlue1: string;
    strongRed: string;
    darkCyanGreen: string;
    darkCyanGreen1: string;
    lightOrange: string;
    lightRed1: string;
    brown: string;
    limeGreen1: string;
    whiteBlue1: string;
    softOrange: string;
    lightCyan1: string;
    lightLimeGreen: string;
    darkLimeGreen: string;
    lightBlue: string;
    strongBlue: string;
    lightRed2: string;
    strongRed1: string;
    darkLimeGreen1: string;
    strongBlue1: string;
    strongRed2: string;
    cyanGreen: string;
    grey70: string;
    softRed: string;
    brightOrange: string;
    lightGrey: string;
  };
};
type loginOtpInputParmsType = {
  mobileNumber: string;
  websiteId: number;
};
type verifyLoginOtpInputParmsType = {
  mobileNumber: string;
  otp: string;
  websiteId: number;
};
type loginUserInputParmsType = {
  username: string;
  password: string;
};
type socialLoginInputParmsType = {
  token: string;
  type: string;
  quoteId: string;
};
type checkNewEmailParmsType = {
  email: string;
};
type createEmailUserParmsType = {
  email: string;
  firstname: string;
  lastname: string;
  password: string;
};
type createMobileUserParmsType = {
  customerEmail: string;
  firstname: string;
  lastname: string;
  password: string;
  mobileNumber: string;
  otp: string;
  websiteId: number;
};
type createAccountOtpParmsType = {
  mobileNumber: string;
  websiteId: number;
};
type forgotPassworOTPParmsType = {
  mobileNumber: string;
  websiteId: number;
};
type forgotPasswordEmailParmsType = {
  email: string;
};
type checkOtpParmsType = {
  email: string;
  otp: string;
};
type resetPasswordParmsType = {
  email: string;
  otp: string;
  newPassword: string;
};
type rresetPasswordMobileParmsType = {
  mobileNumber: string;
  otp: string;
  password: string;
  websiteId: number;
};
type forgotPassworOTPVerifyParmsType = {
  mobileNumber: string;
  otp: string;
  websiteId: number;
};
type resendOtpEmailParmsType = {
  email: string;
};
type createAccountOTPVerifyParmsType = {
  mobileNumber: string;
  otp: string;
  websiteId: number;
};
type cartItemInput = {
  quantity: number;
  sku: string;
};
type simpleProductCartItemInput = {
  data: cartItemInput;
};
type addSimpleProductsToCartInput = {
  cart_id: string;
  cart_items: [simpleProductCartItemInput];
};
type homePageBannerItemProps = {
  alt: string;
  link: string;
  mobile_image: string;
  relative: boolean;
  small_image: string;
  title: string;
  web_image: string;
  id: string;
};
type BasicBannerDetails = {
  alt: string;
  id: string;
  link: string;
  mobile_image: string;
  relative: boolean;
  small_image: string;
  title: string;
  web_image: string;
};
type gethomepagecarouselItemProps = {
  products: any;
  heading: string;
  heading_url: string;
  view: string;
  sku: Array<string>[];
};
type Carousel = {
  heading: string;
  heading_url: string;
  ids: [string];
  sku: [string];
  view: string;
};

type gethomepagesalesbannerItemProps = {
  before_sale_page_url: string;
  desktop_timer_position: string;
  fallback_mobile_image: string;
  fallback_web_image: string;
  mobile_image: string;
  mobile_timer_position: string;
  page_url: string;
  section_enabled: boolean;
  start_time: string;
  timer_color: string;
  web_image: string;
};
type gethomepageslidersItemProps = {
  id: string;
  link: string;
  relative: boolean;
  mobile_image: string;
  title: string;
};
type gethomepageBrandsItemProps = {
  reduce(arg0: (r: any, e: any) => any, arg1: {}): unknown;
  sort(arg0: (a: any, b: any) => any): unknown;
  id: number;
  name: string;
  brand_id: number;
  category_id: number;
  logo: string;
  featured: string;
  url_path: string;
  is_active: boolean;
};
type ProductCategoryData = {
  level: number;
  name: string;
  position: number;
  url_path: string;
  id: number;
  thumbnail: string;
  include_in_menu: number;
  is_anchor: number;
};
type SubCategory = {
  children: ProductCategoryData[];
  level: number;
  name: string;
  position: number;
  url_path: string;
  id: number;
  thumbnail: string;
  include_in_menu: number;
  is_anchor: number;
};
type homePageTopCategoryItemProps = {
  categoryId: number;
  categoryName: string;
  iconUrl: string;
  url_path: string;
};
type ProductMediaGalleryEntriesVideoContent = {
  media_type: string;
  video_description: string;
  video_metadata: string;
  video_provider: string;
  video_title: string;
  video_url: string;
};
type MediaGalleryEntry = {
  id: number;
  disabled: boolean;
  file: string;
  label: string;
  media_type: string;
  position: number;
  types: string;
  video_content: [ProductMediaGalleryEntriesVideoContent];
};
type getHomepageSuperListItemProps = {
  average_rating: string;
  categories: [ProductCategoryData];
  demo_available: string;
  dentalkart_custom_fee: number;
  dispatch_days: number;
  id: number;
  image_url: string;
  is_cod: string;
  is_in_stock: boolean;
  manufacturer: string;
  max_sale_qty: number;
  media_gallery_entries: Array<MediaGalleryEntry>;
  meta_description: string;
  meta_keyword: string;
  meta_title: string;
  msrp: number;
  name: string;
  pd_expiry_date: string;
  price: '';
  rating_count: string;
  reward_point_product: number;
  short_description: string;
  sku: string;
  special_price: number;
  tax_class_id: string;
  thumbnail_url: string;
  tier_prices: number | string;
  type_id: string;
  url_key: string;
  weight: number;
};
type filterParmsType = {
  category_id: number;
};

type CategoryFiltersOptions = {
  label: string;
  value: string;
};
type SelectCategoryFilters = {
  applyFilters: object;
  brand_ids?: string[];
  page_no: number;
  price_range: PriceRange;
  sort_by?: string;
};
type PriceRange = {
  max: number;
  min: number;
};
type ProductDataParms = {
  id: Array<number>;
  sku: Array<string>;
};

type Product = {
  action_btn: {
    action: string;
    text: string;
  };
  average_rating: number;
  currency_symbol: string;
  discount: any;
  is_demo: boolean;
  is_in_stock: boolean;
  is_price_request: boolean;
  is_salable: boolean;
  max_sale_qty: number;
  media: {
    alt: string;
    mobile_image: string;
    small_image: string;
    tab_image: string;
    web_image: string;
  };
  min_sale_qty: number;
  name: string;
  price: number;
  product_id: number;
  rating_count: number;
  reward_points: any;
  selling_price: number;
  short_description: string;
  sku: string;
  tags: Array<{
    code: any;
    type: any;
    url: any;
  }>;
  type: 'simple' | 'grouped';
  url_key: string;
};

//////////////////////////////ProductData start/////////////////////////////////
type ProductData = {
  product_id: number;
  name: string;
  categories: ProductCategory[] | null;
  sku: string;
  type: 'simple' | 'grouped';
  weight: number;
  inventory: Inventory;
  attributes: Attributes;
  brand: {
    id: string;
    name: string;
  };
  rating: number;
  reviews_count: number;
  pricing: Pricing;
  seo: Seo;
  media?: MediaEntity[] | null;
  action_btn: {
    action: string;
    text: string;
  };
  discount: {
    label: string;
    value: string;
  };
  average_rating: number;
  currency_symbol: string;
  is_demo: boolean;
  is_in_stock: boolean;
  is_price_request: boolean;
  is_salable: boolean;
  max_sale_qty: number;
  min_sale_qty: number;
  price: number;
  rating_count: number;
  reward_points: number;
  selling_price: number;
  short_description: string;
  tags: Tag[];
  url_key: string;
};
type Tag = {
  code: string | null;
  type: string | null;
  url: string | null;
};
type Inventory = {
  is_in_stock: boolean;
  min_sale_qty: number;
  max_sale_qty: number;
};
type Attributes = {
  is_cod: string;
  is_demo: boolean;
  expiry_date: {
    label: string;
  };
  handling_charges: number;
  dispatch_days: string;
  reward_points: number;
  short_description: string;
};
type Pricing = {
  is_price_request: boolean;
  price: number;
  selling_price: number;
  discount: {
    value: number;
    label: string;
  };
  currency_symbol: string;
  tier_price?: TierPrice[] | null;
};
type TierPrice = {
  customer_group_id: number;
  qty: number;
  value: number;
};
type Seo = {
  url_key: string;
  meta_title: string;
  meta_keyword: string;
  meta_description: string;
};
type MediaEntity = {
  video_content?: null;
  id: number;
  media_type: string;
  label?: null;
  position: number;
  disabled: boolean;
  types?: (string | null)[] | null;
  file: string;
};
type ProductCategory = {
  id: number;
  level: string | number;
  name?: null | string;
  position: number;
  url_path: string;
};

///////////////////////////////ProductData end////////////////////////////////

////////////////////////////ChildProductResponse start//////////////////////////////////
type ChildProductResponse = {
  parent_id: number;
  child_products: ChildProduct[];
};

type ChildProduct = {
  product_id: number;
  name: string;
  sku: string;
  weight: number;
  is_in_stock: boolean;
  attributes: Attributes;
  pricing: Pricing;
  inventory: Inventory;
};

type Attributes = {
  is_cod: string;
  is_demo: boolean;
  expiry_date: {label: string};
  handling_charges: number;
  dispatch_days: string;
  reward_points: number;
  short_description: any;
};

/////////////////////////////ChildProductResponse end/////////////////////////////////

///////////////////////////////Product review start///////////////////////////////
type ProductReviewResponse = {
  product_id: number;
  review_meta: ReviewMeta;
  reviews: Review[];
  pagination: Pagination;
};

type ReviewMeta = {
  average_rating: number;
  total_reviews: number;
  rating_breakdown: RatingBreakdown;
  verified_buyers: any;
};

type RatingBreakdown = {
  '1': number;
  '2': number;
  '3': number;
  '4': number;
  '5': number;
};

type Review = {
  id: number;
  product_id: number;
  rating: number;
  title: string;
  content: string;
  author: string;
  date: string;
  upvotes: any;
  downvotes: any;
  media: any;
};

type Pagination = {
  current_page: number;
  next_page: any;
  total_pages: number;
  page_size: number;
  count: number;
};

/////////////////////////////Product review end//////////////////////////////////

type ProductPrices = {
  maximalPrice: Price;
  minimalPrice: Price;
  regularPrice: Price;
};
type ProductTierPrices = {
  customer_group_id: string;
  percentage_value: number;
  qty: number;
  value: number;
  website_id: number;
};
type Price = {
  adjustments: [string];
  amount: Money;
};
type Money = {
  currency: [CurrencyEnum];
  currency_symbol: string;
  value: number;
};
type GetAttributesBySkuOutput = {
  attachments: [ProductAttachment];
  attributes: [AttributeItem];
  crosssell: [ProductData];
  related: [ProductData];
  tags: [ProductTag];
  upsell: [ProductData];
};
type ProductAttachment = {thumbnail: string; title: string; url: string};
type AttributeItem = {
  attribute_code: string;
  attribute_label: string;
  attribute_value: string;
};
type ProductTag = {
  identifier: string;
  image: string;
  position: number;
  status: number;
  tag_description: string;
  tag_id: number;
  tag_title: string;
};
type ChildProduct = {
  items: [ProductData];
  parent_price: number;
  parent_stock_status: boolean;
};
type childProductV2 = {
  id: number;
};
type BulkOrderInput = {
  address: string;
  email: string;
  expected_price: number;
  name: string;
  phone: string;
  postcode: string;
  product_id: number;
  quantity: number;
  source: number;
};
type ProductReviews = {
  avg_ratings: string;
  count: [number];
  reviews: [ReviewData];
  total_reviews: string;
};
type ReviewData = {
  created_at: string;
  customer_id: number;
  details: string;
  id: number;
  nickname: string;
  product_id: number;
  rating: number;
  status: number;
  status_label: string;
  title: string;
};
type Country = {
  reduce(arg0: (r: any, e: any) => any, arg1: {}): unknown;
  sort(arg0: (a: any, b: any) => any): unknown;
  available_regions: [Region];
  full_name_english: string;
  full_name_locale: string;
  id: string;
  three_letter_abbreviation: string;
  two_letter_abbreviation: string;
};

type Region = {
  code: string;
  id: number;
  name: string;
};
// ------------------------------------------------------------------------------cart

type Cart = {
  delivery_and_saving_text: string;
  global_errors: any[];
  cart_id: string;
  is_virtual: boolean;
  total_quantity: number;
  total_weight: string;
  coupon_code: CouponCode;
  cart_currency: CartCurrency;
  pricing_details: PricingDetails;
  rewards: {
    total_coins: number;
    monetary_value: string;
  };
  addresses: Addresses;
  items: Item[];
};

type CouponCode = {
  code: string;
};

type CartCurrency = {
  code: string;
  currency_symbol: string;
};

type PricingDetails = {
  applied_taxes: {
    amount: Amount;
  }[];
  discounts: {
    code: string;
    amount: Amount;
  }[];
  grand_total: {
    amount: Amount;
  };
  overweight_delivery_charges: {
    amount: Amount;
  };
  shipping_charges: {
    amount: Amount;
  };
  subtotal_including_tax: {
    amount: Amount;
  };
  subtotal_excluding_tax: {
    amount: Amount;
  };
  subtotal_with_discount_excluding_tax: {
    amount: Amount;
  };
  total_savings: {
    amount: Amount;
  };
};
type Amount = {
  label: string;
  value: string;
};

type Rewards = {
  total_coins: number;
  monetary_value: string;
  earned_points: number;
  reward_icon: string;
  reward_term: string;
  spent_points: number;
};

type Addresses = {
  country: Country;
  firstname: string;
  city: string;
  lastname: string;
  postcode: string;
  region: Region;
  street: string[];
  customer_address_id: number;
};

type Country = {
  name: string;
  isoCode2: string;
  isoCode3: string;
};

type Region = {
  code: string;
  label: string;
};

type Item = {
  error_messages: any[];
  item_id: number;
  item_pricing_details: ItemPricingDetails;
  product: Product;
  qty_increments: number;
  quantity: string;
  is_free_product: boolean;
  reward_point_product: number;
  stock_status: number;
  is_member_ship_product: boolean;
};

type ItemPricingDetails = {
  discounts: Discount2[];
  price: {amount: Amount};
  row_total: RowTotal;
  row_total_including_tax: RowTotalIncludingTax;
};
type Discount2 = {
  code: string;
  amount: Amount;
};
type RowTotal = {
  amount: Amount;
};
type RowTotalIncludingTax = {
  amount: Amount;
};

type Image = {
  url: string;
  label: string;
};
type MinimalPrice = {
  adjustments: any[];
  amount: AmountWithCurrency;
};
type AmountWithCurrency = {
  currency: string;
  currency_symbol: string;
  value: number;
};
type RegularPrice = {
  adjustments: any[];
  amount: AmountWithCurrency;
};
type Thumbnail = {
  url: string;
  label: string;
};
type TierPrice = {
  customer_group_id: string;
  percentage_value: any;
  qty: number;
  value: number;
  website_id: any;
};
type RewardDiscount = {
  amount: cartMoney;
  label: string;
};
type CartTaxItem = {
  amount: cartMoney;
  label: string;
};
type CartDiscount = {
  amount: cartMoney;
  label: [string];
};
type Discount = {
  amount: cartMoney;
  label: string;
};
type cartMoney = {
  currency: string;
  currency_symbol: string;
  value: number;
};
type AvailableShippingMethod = {
  amount: cartMoney;
  available: boolean;
  base_amount: cartMoney;
  carrier_code: string;
  carrier_title: string;
  error_message: string;
  method_code: string;
  method_title: string;
  price_excl_tax: cartMoney;
  price_incl_tax: cartMoney;
};
type CartAddressCountry = {
  code: string;
  label: string;
};
type ItemErrors = {
  code: string;
  message: string;
};
type CartAddressRegion = {
  code: string;
  label: string;
};
type SelectedShippingMethod = {
  amount: cartMoney;
  base_amount: cartMoney;
  carrier_code: string;
  carrier_title: string;
  method_code: string;
  method_title: string;
};
type CartItemPrices = {
  discounts: [Discount];
  price: cartMoney;
  row_total: cartMoney;
  row_total_including_tax: cartMoney;
  total_item_discount: cartMoney;
};
type ProductStockStatus = {
  ProductStockStatus: 'IN_STOCK' | 'OUT_OF_STOCK';
};
type cartProductPrices = {
  maximalPrice: cartPrice;
  minimalPrice: cartPrice;
  regularPrice: cartPrice;
};
type cartPrice = {
  amount: cartMoney;
};
type CategoryInterfaceV2 = {
  app_link: string;
  id: number;
  level: number;
  name: string;
  position: number;
  url_path: string;
};
type ComplexTextValue = {
  html: string;
};
type ProductImage = {
  label: string;
  url: string;
};
type MediaGalleryInterface = {
  label: string;
  url: string;
};
type Check_new_emailParmsType = {
  email: string;
};
type create_Account_OtpParmsType = {
  mobileNumber: string;
  websiteId: Number;
};

type GetWishlistOutput = {
  visibility: string;
  is_default: boolean;
  hash_key: string;
  products: [WishlistProducts];
  id: string;
  title: string;
  items: any[];
};
type WishlistProducts = {
  image_url: string;
  added_date: string;
  product_id: number;
};
type wishlistInput = {
  wishlist_ids: [];
  sharing_hash?: null;
};
type CreateWishlistOutput = {
  access_type: string;
  customer_id: string;
  wishlist_id: string;
  wishlist_name: string;
};
type CreateWishlistInput = {
  access_type: string;
  wishlist_name: string;
};
type UpdateWishlistOutput = {
  access_type: string;
  wishlist_id: string;
  wishlist_name: string;
};
type UpdateWishlistInput = {
  visibility?: String;
  title?: String;
  is_default?: Boolean;
};
type RemoveWishlistOutput = {
  products: [WishlistProducts];
  wishlist_id: string;
};
type RemoveWishlistInput = {
  product_ids?: [number];
  wishlist_id: string;
};
type ShareWishlistOutput = {
  url: string;
};
type ShareWishlistInput = {
  email: string;
  mobile_no: string;
  wishlist_id: string;
};
type PaymentMethodResponseV2 = {
  checkcod: [CodPrepaidResponseV2];
  delivery_days: [ProductDeliveryDayResponse];
  max_delivery_days: string;
  payment_methods: [PaymentMethod];
  return_info: ReturnResponse;
};
type DeliveryInfo = {
  delivery_info: Array<{
    max_delivery_days: number;
    max_delivery_days_text: string;
    max_dispatch_days: number;
    delivery_days: Array<{
      product_id: number;
      days: number;
      dispatch_days: number;
      message: string;
    }>;
  }>;
  service_availability: Array<{
    services: {
      COD: boolean;
      Prepaid: boolean;
    };
    serviceAvailable: boolean;
    message: string;
    errors: Array<string>;
  }>;
};
type CodPrepaidResponseV2 = {
  cod_available: boolean;
  message: string;
  message_arr: [string];
  product_id: number;
  service_available: boolean;
  type: string;
};
type ReturnResponse = {
  message: string;
  message_arr: [string];
  return_period: number;
  returnable: boolean;
};
type ProductDeliveryDayResponse = {
  delivery_days: number;
  product_id: number;
  success_msg: string;
};
type PaymentMethod = {
  code: string;
  title: string;
};
type Confirmation = {
  email_confirm: boolean;
  mobile_confirm: boolean;
};
type CustomAttributesOutput = {
  attribute_code: string;
  value: string;
};
type CustomerAddressRegionV2 = {
  region: string;
  region_code: string;
  region_id: number;
};
type MoveProductWishlistOutput = {
  products: [WishlistProducts];
  target_wishlist_id: string;
  wishlist_id: string;
};
// eslint-disable-next-line @typescript-eslint/no-unused-vars
enum CountryCodeEnumV2 {
  AD,
  AE,
  AF,
  AG,
  AI,
  AL,
  AM,
  AN,
  AO,
  AQ,
  AR,
  AS,
  AT,
  AU,
  AW,
  AX,
  AZ,
  BA,
  BB,
  BD,
  BE,
  BF,
  BG,
  BH,
  BI,
  BJ,
  BL,
  BM,
  BN,
  BO,
  BR,
  BS,
  BT,
  BV,
  BW,
  BY,
  BZ,
  CA,
  CC,
  CD,
  CF,
  CG,
  CH,
  CI,
  CK,
  CL,
  CM,
  CN,
  CO,
  CR,
  CU,
  CV,
  CX,
  CY,
  CZ,
  DE,
  DJ,
  DK,
  DM,
  DO,
  DZ,
  EC,
  EE,
  EG,
  EH,
  ER,
  ES,
  ET,
  FI,
  FJ,
  FK,
  FM,
  FO,
  FR,
  GA,
  GB,
  GD,
  GE,
  GF,
  GG,
  GH,
  GI,
  GL,
  GM,
  GN,
  GP,
  GQ,
  GR,
  GS,
  GT,
  GU,
  GW,
  GY,
  HK,
  HM,
  HN,
  HR,
  HT,
  HU,
  ID,
  IE,
  IL,
  IM,
  IN,
  IO,
  IQ,
  IR,
  IS,
  IT,
  JE,
  JM,
  JO,
  JP,
  KE,
  KG,
  KH,
  KI,
  KM,
  KN,
  KP,
  KR,
  KW,
  KY,
  KZ,
  LA,
  LB,
  LC,
  LI,
  LK,
  LR,
  LS,
  LT,
  LU,
  LV,
  LY,
  MA,
  MC,
  MD,
  ME,
  MF,
  MG,
  MH,
  MK,
  ML,
  MM,
  MN,
  MO,
  MP,
  MQ,
  MR,
  MS,
  MT,
  MU,
  MV,
  MW,
  MX,
  MY,
  MZ,
  NA,
  NC,
  NE,
  NF,
  NG,
  NI,
  NL,
  NO,
  NP,
  NR,
  NU,
  NZ,
  OM,
  PA,
  PE,
  PF,
  PG,
  PH,
  PK,
  PL,
  PM,
  PN,
  PS,
  PT,
  PW,
  PY,
  QA,
  RE,
  RO,
  RS,
  RU,
  RW,
  SA,
  SB,
  SC,
  SD,
  SE,
  SG,
  SH,
  SI,
  SJ,
  SK,
  SL,
  SM,
  SN,
  SO,
  SR,
  ST,
  SV,
  SY,
  SZ,
  VA,
  TC,
  VE,
  TD,
  VI,
  TF,
  VU,
  TG,
  WS,
  TH,
  TJ,
  ZA,
  TK,
  ZW,
  TL,
  TM,
  TN,
  TO,
  TR,
  TT,
  TV,
  TW,
  TZ,
  UA,
  UG,
  UM,
  US,
  UY,
  UZ,
  VC,
  VG,
  VN,
  WF,
  YE,
  YT,
  ZM,
}
type CustomerAddressInput = {
  city: string;
  company: string;
  country_code: CountryCodeEnumV2;
  country_id: CountryCodeEnumV2;
  custom_attributes: [CustomerAddressAttributeInput];
  default_billing: boolean;
  default_shipping: boolean;
  firstname: string;
  lastname: string;
  middlename: string;
  postcode: string;
  region: CustomerAddressRegionInput;
  street: [string];
  telephone: string;
  vat_id: string;
};
type CustomerAddressAttributeInput = {
  attribute_code: string;
  value: string;
};
type CustomerAddressAttribute = {
  attribute_code: string;
  value: string;
};
type CustomerAddressRegionInput = {
  region: string;
  region_code: string;
  region_id: number;
};
type GetValidationRules = {
  alternate_telephone_required: boolean;
  emailsignup: boolean;
  mobilesignup: boolean;
  postcode_format: string;
  postcode_required: boolean;
  service_availability_enabled: boolean;
  state_dropdown_required: boolean;
  state_required: boolean;
  tax_format: string;
  tax_label: string;
  tax_required: boolean;
  telephone_code: string;
  telephone_format: string;
  whatsappsignup: boolean;
};
type deleteCustomerAddress = {id: number};

type getValidationRulesOutPut = {
  country_id: string;
};
type News = {
  content: string;
  date: string;
  id: number;
  image: string;
  is_archived: boolean;
  source_link: string;
  title: string;
  type: string;
};
type bookmarkNewsInput = {
  ids: [number];
};
type CountNewsOutput = {
  count: number;
};

type Multimedia = {
  course: [MultimediaCourses];
  video: [MultimediaVideos];
  webinar: [MultimediaWebinars];
};
type MultimediaCourses = {
  category: string;
  created_at: string;
  description: string;
  duration: string;
  id: number;
  price: string;
  source: string;
  start_date: string;
  thumbnail: string;
  topic: string;
};
type MultimediaVideos = {
  author: string;
  category: string;
  created_at: string;
  id: number;
  source: string;
  status: string;
  thumbnail: string;
  title: string;
};
type MultimediaWebinars = {
  category: string;
  created_at: string;
  description: string;
  id: number;
  price: string;
  source: string;
  start_date: string;
  thumbnail: string;
  topic: string;
};
type ThankyouPage = {
  img: string;
  link: string;
};
type OrderDetailsV1 = {
  value: string | null | undefined;
  billing_address: Address_details;
  can_cancel: boolean;
  can_return: boolean;
  currency: string;
  order_date: string;
  order_id: string;
  order_status: string;
  order_summary: [OrderSummary];
  packages: [packages];
  payment_method: string;
  payment_method_code: string;
  rewards: RewardsSummary;
  shipping_address: Address_details;
};
type FetchOrderOutputV2 = {
  amount: number;
  can_refetch: boolean;
  can_retry_payment: boolean;
  currency: string;
  currency_symbol: string;
  error_msg: string;
  failure_wait_time: string;
  merchant_id: string;
  order_created_at: string;
  order_detail_available: boolean;
  order_id: string;
  reference_number: string;
  status: string;
};
type Address_details = {
  alternate_mobile: string;
  city: string;
  company: string;
  country_id: string;
  email: string;
  name: string;
  postcode: string;
  region: string;
  region_id: string;
  street: string;
  telephone: string;
  vat_id: string;
};
type OrderSummary = {
  color: any;
  area: string;
  code: string;
  label: string;
  value: number;
};
type packages = {
  can_cancel: boolean;
  collectable_amount: string;
  date: string;
  delivereddate: string;
  deliveryNumber: string;
  items: [OrderItems];
  pack_date: string;
  pick_date: string;
  qty: number;
  shipment_value: string;
  status: string;
  status_history: [StatusHistory];
  tracking_number: string;
  tracking_url: string;
  transporter: string;
};
type OrderItems = {
  can_cancel: boolean;
  image: string;
  name: string;
  price: number;
  qty_canceled: number;
  qty_cancellable: number;
  qty_ordered: number;
  qty_refunded: number;
  qty_shipped: number;
  sku: string;
  status: string;
  ordered_qty: string;
  thumbnail: string;
  delivered_qty: number;
  is_free_product: boolean;
  is_tat_expired: boolean;
  max_qty_returnable: number;
  name: string;
  non_returnable: boolean;
  url_key: string;
};
type StatusHistory = {
  current: boolean;
  date: string;
  status: string;
};
type RewardsSummary = {
  reward_icon: string;
  reward_term: string;
  rewardpoints_earned: number;
  rewardpoints_used: number;
  rewardpoints_used_monetary: string;
};
type CustomerOrder = {
  can_cancel: boolean;
  can_reorder: boolean;
  can_return: boolean;
  cta_title: string;
  dates: {
    delivered: string | null;
    ordered: string | null;
  };
  id: number;
  currency: string;
  created_at: string;
  grand_total: number;
  increment_id: string;
  is_processing: boolean;
  item_text: string;
  items: OrderProductItem[];
  order_id: string;
  order_summary: OrderSummaryData;
  rewards: Rewards;
  status: OrderStatus;
  payment_method: string;
};
type OrderSummaryData = {
  order_amount: number;
  total_product_qty: number;
  total_savings: number;
  unique_product_count: number;
};
type OrderStatus = {
  code: string;
  id: string | null;
  label: string;
};
type OrderResponse = {
  data: CustomerOrder;
  tracking: Tracking;
};
type Tracking = {
  deeplink: string;
  track_title: string;
  tracking_available: boolean;
};
type OrderProductItem = {
  can_return: boolean;
  name: string;
  price: number;
  product_id: number;
  qty: number;
  returnable_qty: number;
  rewardpoints: string;
  sku: string;
  thumbnail: string;
  url_key: string;
  ordered_qty: number;
  reward_earned_coins: number;
  row_total: number;
  status: OrderStatus;
};

type InvoiceLink = {
  awb_number: string;
  order_id: string;
  pdf_link: string;
  is_error: boolean;
  is_retry: boolean;
  error_message: string;
};
type CancelableOrderResponseType = {
  order: CancelableFieldsType;
};
type CancelableFieldsType = {
  isFullCancel: boolean;
};
type CancelOrderInput = {
  id(id: any): void;
  fullOrderCancel: null;
  orderId: string;
  packages: PackagesInput;
  reason: number;
};
type PackagesInput = {
  items: [ItemsInput];
  shipmentId: [String];
};
type ItemsInput = {
  productId: number;
  qty: number;
  sku: string;
};
type TrackV2Input = {
  courier: string;
  order_id: string;
  track_number: string;
};
type TrackV2Output = {
  error: Error;
  response: Response;
  status: String;
};
type ShippingAddressInputV2 = {
  address: CartAddressInputV2;
  customer_address_id: number;
  customer_notes: string;
};
type CartAddressInputV2 = {
  alternate_mobile: string;
  city: string;
  company: string;
  country_code: string;
  firstname: string;
  lastname: string;
  postcode: string;
  region: string;
  save_in_address_book: boolean;
  street: string;
  telephone: string;
};
type SetShippingAddressesOnCartOutput = {
  cart: Cart;
};

type UpdateCartItemsInput = {
  cart_id: string;
  cart_items: [CartItemUpdateInput];
};
type CartItemUpdateInput = {
  cart_item_id: number;
  quantity: number;
};
type GetAvailableMethodsV2Output = {
  country_code: string;
  is_cod: boolean;
  pincode: string;
  total_amount: number;
  total_weight: number;
};
type GetCategoryFiltersOutput = {
  category_id: number;
  filters: [CategoryFilters];
};
type CategoryFilters = {
  code: string;
  display: number;
  label: string;
  options: [CategoryFiltersOptions];
  type: string;
  value: string;
};
type getCategoryProductsInputs = {
  manufacturer?: [string];
  price: CategoryPriceFilterInput;
  sortBy?: string;
  category_id: number;
  page_no: number;
};
type CategoryPriceFilterInput = {
  max_price: number;
  min_price: number;
};
type GetCategoryProductsOutput = {
  ads_banner: [CategoryAdsBanner];
  category_id: number;
  description: string;
  image: string;
  items: [ProductData];
  meta_description: string;
  meta_keywords: string;
  meta_title: string;
  name: string;
  page_no: number;
  product_count: number;
  url_key: string;
};
type CategoryAdsBanner = {
  banners: [CategoryAdsBanners];
  type: string;
};
type CategoryAdsBanners = {
  alt: string;
  app_url: string;
  mobile_img: string;
  web_img: string;
  web_url: string;
};
type ReviewResponseType = {
  created_at: string;
  customer_id: number;
  description: string;
  details: string;
  id: number;
  nickname: string;
  product_id: number;
  rating: number;
  source: string;
  status: string;
  status_label: string;
  title: string;
};

type MembershipInfoResponseType = {
  currentPlan: CurrentPlanResponseType;
  daysLeft: string;
  memberships: [MembershipModel];
  monetoryValue: string;
};
type CurrentPlanResponseType = {
  duration: string;
  order_id: string;
  price: string;
};
type MembershipModel = {
  created_at: string;
  expiry_date: string;
  is_active: boolean;
  is_expired: boolean;
  monetory_value: string;
  order_id: string;
  product_sku: string;
};
type FaqCategory = {
  category_createdBy: string;
  category_id: number;
  category_name: string;
  category_sequence: number;
  category_thumbnail: string;
  department_id: number;
  isparent: number;
};

type FaqCategoryItem = {
  answer: string;
  dislikes: number;
  id: number;
  likes: number;
  parent: FaqCategory;
  question: string;
  sort_order: number;
  status: number;
  ticket_type: number;
  views: number;
};
type RewardsTransactionInfo = {
  page_info: SearchResultPageInfo;
  total_count: number;
  transactions: [RewardsTransaction];
};
type RewardsTransactionInput = {
  currentPage: number;
  pageSize: number;
};
type SearchResultPageInfo = {
  current_page: number;
  page_size: number;
  total_pages: number;
};
type RewardsTransaction = {
  amount: string;
  amount_text: string;
  amount_with_sign: string;
  expired_time: string;
  history_id: string;
  history_order_increment_id: string;
  reward_icon: string;
  reward_term: string;
  status: string;
  transaction_detail: string;
  transaction_time: string;
  type_of_transaction: string;
};
type RewardsAccountInfo = {
  balance: string;
  balance_monetary: string;
  currency: string;
  currency_icon: string;
  earn_points: string;
  earned_points_monetary: string;
  exchange_rate_info: string;
  pending_points: string;
  pending_points_monetary: string;
  reward_icon: string;
  reward_term: string;
  spent_monetary: string;
  spent_points: string;
};
type RewardsExpiryInfo = {
  info: string;
  page_info: SearchResultPageInfo;
  reward_icon: string;
  reward_term: string;
  total_count: number;
  transactions: [RewardsExpiryTransaction];
};
type RewardsExpiryTransaction = {
  amount: string;
  expired_time: string;
  history_id: string;
  transaction_time: string;
};
type trackByTrackNumberV2 = {
  courier: string;
  order_id: string;
  track_number: string;
};
type PostProductReview = {
  created_at?: string;
  customer_id: number;
  details: string;
  id?: number;
  nickname: string;
  product_id: number;
  rating: number;
  source: string;
  status: number;
  status_label: string;
  title: string;
};
type ReviewDataInput = {
  details: string;
  id?: number;
  nickname: string;
  product_id: number;
  rating: number;
  source?: string;
  title: string;
};
type checkCustomerBoughtProductInput = {
  product_id: number;
  type: string;
};
type AddProductToWishlistOutput = {
  products: [WishlistProducts];
  wishlist_id: string;
  wishlist_name: string;
};
type CartInput = {
  cart_amount: number;
  cart_weight: number;
  is_cod_on_cart: boolean;
};
type ProductsInput = {
  children: [number];
  parent_id: number;
};
type Brand = {
  brand_id: number;
  brand_name: string;
  category_id: number;
  created_at: string;
  id: number;
  is_active: boolean;
  is_dk_suggest: boolean;
  is_featured: boolean;
  is_international: boolean;
  is_newly_added: boolean;
  is_top: boolean;
  logo: string;
  sort_order: number;
  updated_at: string;
  url: string;
  name: string;
  featured: string;
  url_path: string;
};
type Notice = {
  background: string;
  colour: string;
  content: string;
  content_type: string;
  link: string;
  notification_type: string;
  section: string;
  sort_order: number;
  source: string;
};
type SearchInput = {
  enable: string;
  product_id: number;
  search: string;
};
type CustomerInput = {
  date_of_birth: string;
  dob: string;
  email: string;
  firstname: string;
  gender: number;
  is_subscribed: boolean;
  lastname: string;
  middlename: string;
  password: string;
  prefix: string;
  suffix: string;
  taxvat: string;
};

type CustomerAddressV2 = {
  city: string;
  company: string;
  country_code: CountryCodeEnumV2;
  country_id: string;
  custom_attributes: [CustomerAddressAttribute];
  customer_id: number;
  default_billing: boolean;
  default_shipping: boolean;
  fax: string;
  firstname: string;
  id: number;
  lastname: string;
  postcode: string;
  prefix: string;
  region: CustomerAddressRegionV2;
  region_id: number;
  street: [string];
  suffix: string;
  telephone: string;
  vat_id: string;
};

type CustomerType = {
  addresses: [CustomerAddressV2];
  fullname: string;
  firstname: string;
  lastname: string;
  businessname: string;
  id: number;
  email: string;
  dob: string;
  anniversary: string;
  mobile: string;
  taxvat: string;
  speciality: string;
  type: string;
  gender: string;
};

type BaseCountry = {
  country: string;
  country_id: string;
  currency_code: string;
  postal_code: string;
  state: string;
};

type RootState = {
  app: {
    loading: boolean;
    userInfo: CustomerType | null;
    cart: any;
    cartCount: number;
    cartId: string | null;
    isLoggedIn: boolean;
    whatsAppLink: string | null;
    validationRules: any;
    countryListings: any;
    notifyProductId: number | any;
    baseCountryData: BaseCountry | null;
    brandsName: Brand[];
    wishlistItems: Array<number>;
    getOfferItems: null;
    wishlists: any;
    membership: any;
    membership_type: string
  };
};

type WeightWithPrice = {
  currency: string;
  price: number;
  weight: number;
  weight_range: string;
};

type dkPlaceOrderInputV2 = {
  cart_id: string;
  extension_attributes: [ExtensionAttributeInput];
  payment_method: string;
};
type ExtensionAttributeInput = {
  attribute_code: string;
  value: string;
};
type Packages = {
  can_cancel: boolean;
  collectable_amount: string;
  date: string;
  delivereddate: string;
  deliveryNumber: string;
  items: [OrderItems];
  pack_date: string;
  pick_date: string;
  qty: number;
  shipment_value: string;
  status: string;
  status_history: [StatusHistory];
  tracking_number: string;
  tracking_url: string;
  transporter: string;
};
type FreeProducts = {
  buy_qty: string;
  free_product_quantity: string;
  free_product_sku: string;
  parent_sku: string;
  product_sku: string;
};
type AnswerData = {
  updated_at: string;
  updated_by: string;
  value: string;
};
type EditQuestionsInputs = {
  _id: string;
  answer?: AnswerDataInput;
  dislike?: number;
  enable?: Boolean;
  like?: number;
  product_id: number;
  product_image?: string;
  product_name?: string;
  question?: string;
  status?: string;
};
type AnswerDataInput = {
  updated_at: string;
  updated_by: string;
  value: string;
};
type QuestionsOutput = {
  count: number;
  result: [QuestionsOutputData!];
};
type QuestionsOutputData = {
  _id: string;
  answer: AnswerData;
  created_at: string;
  customer_token: string;
  customer_name: string;
  dislike: number;
  enable: boolean;
  like: number;
  product_id: number;
  product_image: string;
  product_name: string;
  question: string;
  status: string;
  user: string;
};
type QuestionsPaginationInput = {
  pageNumber: number;
  rowsPerPage: number;
};

type Section = {
  active_in_device: string;
  title: string;
  description: string;
  sort_order: number;
  layout_type: LayoutType;
  elements: SectionElement[];
};

type LayoutType = {
  code: string;
  label: string;
  total_elements: number;
};

type SectionElement = {
  title: string;
  landing_page: string;
  relative: boolean;
  sort_order: number;
  media?: Media;
  landing_page_entity?: LandingPageEntity;
  name?: string;
  description?: string;
  reward_point_product?: number;
  price?: number;
  selling_price?: number;
  is_in_stock?: boolean;
};

type Media = {
  web_image: string;
  small_image: string;
  tab_image: string;
  mobile_image: string;
  alt?: string;
};

type LandingPageEntity = {
  url: string;
  product_id?: number;
  sku?: string;
  category_id?: number;
};

// =================new==========
type Root = {
  order_id: string;
  order_date: string;
  packages: Package[];
};

type Package = {
  delivered_date: string;
  qty: number;
  status: string;
  tracking_number: string;
  transporter: string;
  collectable_amount: string;
  delivery_number: string;
  shipment_value: string;
  items: Item[];
  status_history: StatusHistory[];
  tracking_url: any;
  can_cancel: any;
  latetst_status_history: any;
};

type Item = {
  sku: string;
  name: string;
  price: string;
  status: string;
  image: string;
  qty_ordered: string;
  qty_shipped: string;
  qty_canceled: string;
  qty_returned: string;
  qty_refunded: any;
  can_cancel: boolean;
  qty_cancellable: boolean;
};

type StatusHistory = {
  status: string;
  date: string;
  current: boolean;
};

type ApiHeader = {
  Accept: string;
  'Content-Type': string;
  'x-api-key': string;
  platform: string;
  version: string;
  Authorization?: string;
};

type Referrals = {
  refer_type: string;
  product_sku?: string;
  url_key?: string;
};

type Benefits = {
  icon: keyof typeof Icons;
  label: string;
  content: string[];
};

type BulkForm = {
  userName: string;
  expectedPrice: string;
  userEmail: string;
  userPincode: string;
  address: string;
  bulkQuantity: string;
  userPhone: string;
};

type Faq = {
  _id: string;
  like?: number;
  dislike?: number;
};

type SelectedGroupProducts = {
  sku: string;
  quantity: number;
};

type ModalText = {
  label: string;
  content: string[];
};

type PdfFiles = {
  title: string;
  url: string;
};

type SliderPdp = {
  file: string;
};

type RefersRecord = {
  onelink_url: string;
  referral_code: string;
};

type Feedback = {
  quality: string;
  price: string;
  feedback: string;
};

type ProductReviewMedia = {
  type: string;
  url: string;
};

type ProductReviewForm = {
  rating: number;
  title: string;
  content: string;
  media: ProductReviewMedia[];
};

type BenefitsInfo = {
  icon: string;
  backgroundColor: string;
  label: string;
  content: string[]; // Array of strings for the content
};

type BuyAgainCart = {
  data: {
    product: ProductData;
    quantity: number;
  };
};
type Magazine = {
  _id: string;
  author: string;
  coverPage_url: string;
  created_At: string;
  date_of_publish: string;
  enable: boolean;
  magazine_name: string;
  magazine_url: string;
  publisher: string;
  updated_At: string;
};
type CatalogueData = {
  uri?: string;
  fileName?: string;
  fileSize?: string;
};
type ShortsCategory = {
  VideoTags: {
    id: number;
  };
  category_key: string;
  id: number;
};
type Shorts = {
  author_image: string;
  author_name: string;
  caption: string;
  categories: ShortsCategory[];
  comment_count: number;
  created_at: string;
  deleted_at: string | null;
  duration: number;
  featured_product: any | null;
  height: number;
  id: number;
  like_count: number;
  status: string;
  streaming_url: string;
  thumbnail_gif_url: string;
  thumbnail_url: string;
  title: string;
  totalWatchCount: number;
  totalWatchTime: number;
  updated_at: string;
  video_url: string;
  width: number;
};
type PlaybackProgress = {
  currentPlaybackTime: number;
  seekableDuration: number;
  playableDuration: number;
  currentTime: number;
};
type OrderStatusIcons = {
  [status: string]: [string, string, string];
};
type OrderFilterOption = {
  data: string[];
  title: string;
};
type CanCancel = {
  is_cancelable: boolean;
};
type OrderReturn = {
  amount: number;
  cancelled_items: any[];
  created_at: string;
  image: string;
  name: string;
  order_id: string;
  qty: number;
  qty_ordered: number;
  return_id: number;
  sku: string;
  status: string;
  status_label: string;
  url: string | null;
};
type SocialIcon = {
  icon: string;
  type: string;
};
type couponList = {
  title: string;
  data: couponItem[];
};
type couponItem = {
  coupon_code: string;
  description: string;
};
type screenProps = {
  screenName?: string;
  screenParams?: object;
  pageType?: string;
};
type estimateStatus = {
  status: string;
  expectedDate: string;
};
type trackingData = {
  label: string;
  status: string;
  lineActive: boolean;
  showLine: boolean;
  icon: string;
};
type ReturnAction = {
  action: string;
  created_at: string;
  customer_enable: boolean;
  enable: boolean;
  id: number;
  updated_at: string;
  user: string;
};
interface SubReason {
  attachment: boolean;
  code: string;
  created_at: string;
  enable: boolean;
  id: number;
  reason: string;
  updated_at: string;
}
interface ReturnReasons {
  attachment: boolean;
  code: string;
  created_at: string;
  enable: boolean;
  id: number;
  reason: string;
  return_actions: ReturnAction[];
  sub_reasons: SubReason[];
  updated_at: string;
  user: string;
}
interface ReasonError {
  reason_id: string;
  attachments: string;
}
interface Registration {
  customer_email: string;
  firstname: string;
  id: number;
  is_default: boolean;
  is_guest: boolean;
  is_verified: boolean;
  lastname: string;
  registration_no: string;
}
interface PlaceSuggestion {
  alternateName: string;
  eLoc: string;
  keywords: string[];
  orderIndex: number;
  placeAddress: string;
  placeName: string;
  suggester: string;
  type: string;
}

import {ImageIcon, Label, Quantity, Spacer} from 'components/atoms';
import React, {memo, useMemo} from 'react';
import {View, TouchableOpacity, TextInput} from 'react-native';
import <PERSON>rror<PERSON>andler from 'utils/ErrorHandler';
import Button from '../button';
import stylesWithOutColor from '../../../scenes/productDetail/style';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {ChildProduct, ProductData} from 'local';
import Icons from 'common/icons';
import FastImage from 'react-native-fast-image';

type Props = {
  searchProductQuery: string;
  handleProductSearch: (text: any) => void;
  setSearchProductQuery: React.Dispatch<React.SetStateAction<string>>;
  filteredProducts: ChildProduct[];
  product: ProductData | null;
  updateSelectedGroupProducts: (
    qty: number,
    productPrice: number,
    sku: string,
  ) => void;
  handleGroupQuantityUpdateDebounce: (
    count: number,
    price: number,
    sku: string,
    change: boolean,
  ) => void;
  selectedGroupProductsForUI: {
    data: {
      sku: string;
      quantity: number;
    };
  }[];
  notifyIcons: any;
  handleNotifyClick: (sku: any, id: any) => Promise<void>;
  quantityInputRef: React.RefObject<TextInput>;
  setReturnInfoModel: React.Dispatch<React.SetStateAction<boolean>>;
  setInfoIcon: React.Dispatch<React.SetStateAction<string>>;
  deliveryStatusData: any;
  postCode: string;
  availablePaymentMethod: any;
  freeBie: any;
  getFreeBieMessage: () => void;
  pinCodeWrong: boolean;
};

const SearchProductComponent = ({
  // Search related props
  searchProductQuery = '',
  handleProductSearch,
  setSearchProductQuery,

  // Product related props
  filteredProducts,
  product,
  // Actions and handlers
  updateSelectedGroupProducts,
  handleGroupQuantityUpdateDebounce,
  selectedGroupProductsForUI,

  // Notification related props
  notifyIcons = {},
  handleNotifyClick,

  // References
  quantityInputRef,

  // Modals and informational controls
  setReturnInfoModel,
  setInfoIcon,

  // Delivery and payment related props
  deliveryStatusData,
  postCode = '',
  availablePaymentMethod,

  // Freebie related props
  freeBie = [],
  getFreeBieMessage,
  pinCodeWrong,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={styles.searchIconView}>
      <View style={styles.searchsubView}>
        <ImageIcon icon="search" size="xxl" tintColor="text2" />
        <Spacer size="xm" type="Horizontal" />
        <View style={styles.searchLine} />
        <Spacer size="xm" type="Horizontal" />
        <ErrorHandler componentName="TextInput" onErrorComponent={<View />}>
          <TextInput
            testID="txtBuyAgainSearch"
            placeholderTextColor={colors?.text2}
            value={searchProductQuery}
            placeholder={t('buyAgain.searchByName')}
            onChangeText={handleProductSearch}
            style={styles.textInputView}
            allowFontScaling={false}
            keyboardType="default"
          />
        </ErrorHandler>
        <Spacer size="xm" type="Horizontal" />
        <View>
          {searchProductQuery?.length > 0 && (
            <TouchableOpacity
              style={styles.cross}
              onPress={() => setSearchProductQuery?.('')}>
              <Spacer size="xm" type="Horizontal" />
              <ImageIcon icon="cross" size="xsl" tintColor="text2" />
            </TouchableOpacity>
          )}
        </View>
      </View>
      <Spacer size="sx" />
      {filteredProducts?.length > 0 && product?.type === 'grouped' ? (
        filteredProducts?.map((childProduct, index) => {
          return (
            <View key={index.toString()} style={styles.childProductView}>
              <View style={styles.subChildProductView}>
                <View style={[styles.flexRow, styles.flexSpacer]}>
                  <View style={styles.childName}>
                    <Label
                      weight="600"
                      color="text"
                      size="mx"
                      text={childProduct?.name}
                    />
                  </View>
                  <Spacer size="m" type="Horizontal" />
                  {/* Commented out refer and earn section
                  <TouchableOpacity
                    onPress={() => {
                      setReferCoin?.(
                        childProduct?.attributes?.reward_points,
                      );
                      referAndEarn?.();
                    }}
                    style={styles.subEarnView}>
                    <Label
                      weight="500"
                      color="silkBlue"
                      numberOfLines={1}
                      size="mx"
                      text={t('PDP.referEarn')}
                    />
                    <Spacer type="Horizontal" size="xm" />
                    <ImageIcon
                      size="xxl"
                      icon="announcement"
                      tintColor="silkBlue"
                    />
                  </TouchableOpacity> */}
                </View>
              </View>
              <Spacer size="sx" />
              <View style={styles.maximalPriceView}>
                <View style={styles.priceView}>
                  <Label
                    weight="600"
                    color="text"
                    size="xl"
                    text={
                      childProduct?.pricing?.currency_symbol +
                      ' ' +
                      childProduct?.pricing?.selling_price
                    }
                  />
                  <Spacer size="s" type="Horizontal" />
                  {childProduct?.pricing?.price !==
                    childProduct?.pricing?.selling_price && (
                    <Label
                      weight="500"
                      textDecorationLine="line-through"
                      color="grey3"
                      size="mx"
                      text={childProduct?.pricing?.price}
                    />
                  )}
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    weight="500"
                    color="green2"
                    size="mx"
                    text={childProduct?.pricing?.discount?.label}
                  />
                </View>
              </View>
              <Spacer size="s" />

              <View>
                {!!childProduct?.attributes?.reward_points && (
                  <View style={styles.expiryDateView}>
                    <ImageIcon icon="coin" size="xx" />
                    <Spacer size="xm" type="Horizontal" />
                    <Label
                      weight="500"
                      color="orange"
                      size="mx"
                      text={`${childProduct?.attributes?.reward_points}`}
                    />
                  </View>
                )}
                <View style={styles.maximalPriceView}>
                  <View style={styles.flex}>
                    {!!childProduct?.attributes?.expiry_date?.label && (
                      <View style={styles.expiryDateView}>
                        <Label
                          weight="500"
                          color="text"
                          size="mx"
                          text={t('rewardCoin.expiryDate')}
                        />
                        <Label
                          weight="500"
                          color="categoryTitle"
                          size="mx"
                          text={childProduct?.attributes?.expiry_date?.label}
                        />
                        <TouchableOpacity
                          onPress={() => {
                            setReturnInfoModel?.(true);
                            setInfoIcon?.('expiryDate');
                          }}>
                          <ImageIcon
                            tintColor="categoryTitle"
                            size="xl"
                            icon="infoCircle"
                          />
                        </TouchableOpacity>
                      </View>
                    )}
                    {postCode.length === 6 &&
                      childProduct?.is_in_stock &&
                      !pinCodeWrong && (
                        <View style={styles.deliveryInfoContainer}>
                          {childProduct?.is_in_stock && (
                            <View>
                              {!!deliveryStatusData?.delivery_info?.[0]
                                ?.max_dispatch_days && (
                                <View style={styles.expiryDateView}>
                                  <ImageIcon
                                    size="xxl"
                                    tintColor="categoryTitle"
                                    icon="deliveryTruckIcon"
                                  />
                                  <Spacer size="xm" type="Horizontal" />
                                  <Label
                                    color="categoryTitle"
                                    weight="500"
                                    size="mx"
                                    text={
                                      deliveryStatusData?.delivery_info?.[0]
                                        ?.delivery_days?.[0]?.message
                                    }
                                  />
                                  <TouchableOpacity
                                    onPress={() => {
                                      setReturnInfoModel?.(true);
                                      setInfoIcon?.('deliveryInfo');
                                    }}
                                    style={styles.imgLeft}>
                                    <ImageIcon
                                      size="xl"
                                      tintColor="categoryTitle"
                                      style={styles.imgDeliveryIcon}
                                      icon="infoCircle"
                                    />
                                  </TouchableOpacity>
                                </View>
                              )}
                              {deliveryStatusData?.service_availability?.find?.(
                                service =>
                                  service?.product_id ===
                                  childProduct?.product_id,
                              )?.message &&
                                postCode?.length == 6 && (
                                  <View style={styles.expiryDateView}>
                                    <ImageIcon
                                      size="xxl"
                                      tintColor={
                                        deliveryStatusData?.service_availability?.find?.(
                                          service =>
                                            service?.product_id ===
                                            childProduct?.product_id,
                                        )?.services?.COD
                                          ? 'categoryTitle'
                                          : ''
                                      }
                                      icon={
                                        deliveryStatusData?.service_availability?.find?.(
                                          service =>
                                            service?.product_id ===
                                            childProduct?.product_id,
                                        )?.services?.COD
                                          ? 'creditIcon'
                                          : 'nonReturnable'
                                      }
                                    />
                                    <Spacer size="xm" type="Horizontal" />
                                    <Label
                                      color={
                                        deliveryStatusData?.service_availability?.find?.(
                                          service =>
                                            service?.product_id ===
                                            childProduct?.product_id,
                                        )?.services?.COD
                                          ? 'categoryTitle'
                                          : 'red3'
                                      }
                                      weight="500"
                                      size="mx"
                                      text={deliveryStatusData?.service_availability
                                        ?.find?.(
                                          service =>
                                            service?.product_id ===
                                            childProduct?.product_id,
                                        )
                                        ?.message?.replace?.(/\.$/, '')}
                                      textTransform="capitalize"
                                    />
                                    {deliveryStatusData
                                      ?.service_availability?.[0]
                                      ?.serviceAvailable && (
                                      <TouchableOpacity
                                        onPress={() => {
                                          setReturnInfoModel?.(true);
                                          setInfoIcon?.('codCheck');
                                        }}>
                                        <ImageIcon
                                          tintColor={
                                            deliveryStatusData?.service_availability?.find?.(
                                              service =>
                                                service?.product_id ===
                                                childProduct?.product_id,
                                            )?.services?.COD
                                              ? 'categoryTitle'
                                              : 'red3'
                                          }
                                          size="xl"
                                          icon="infoCircle"
                                        />
                                      </TouchableOpacity>
                                    )}
                                  </View>
                                )}
                              <Spacer type="Horizontal" size="sx" />
                              {deliveryStatusData?.return_info?.[0] !==
                                undefined && (
                                <View style={styles.expiryDateView}>
                                  <ImageIcon
                                    size="xxl"
                                    icon={
                                      deliveryStatusData?.return_info?.[0]
                                        ?.is_returnable
                                        ? 'returnable'
                                        : 'nonReturnable'
                                    }
                                  />
                                  <Spacer size="xm" type="Horizontal" />
                                  <Label
                                    textTransform="capitalize"
                                    color={
                                      deliveryStatusData?.return_info?.[0]
                                        ?.is_returnable
                                        ? 'categoryTitle'
                                        : 'red3'
                                    }
                                    weight="500"
                                    size="mx"
                                    text={
                                      deliveryStatusData?.return_info?.[0]
                                        ?.is_returnable
                                        ? t('PDP.returnable')
                                        : t('PDP.nonReturnable')
                                    }
                                  />
                                </View>
                              )}
                              <Spacer type="Horizontal" size="sx" />
                            </View>
                          )}
                        </View>
                      )}
                  </View>
                  <View style={styles.freeBieView}>
                    {childProduct?.is_in_stock ? (
                      <>
                        {Array.isArray(freeBie) &&
                          freeBie?.some?.(
                            e => e?.sku === childProduct?.sku,
                          ) && (
                            <ErrorHandler
                              componentName="Tag"
                              onErrorComponent={<View />}>
                              <TouchableOpacity
                                style={styles.freeBieButton}
                                activeOpacity={1}
                                onPress={getFreeBieMessage}>
                                <FastImage
                                  style={styles.emptyImage}
                                  source={Icons.freebieGif}
                                />
                              </TouchableOpacity>
                            </ErrorHandler>
                          )}
                      </>
                    ) : (
                      <ErrorHandler
                        componentName="Button"
                        onErrorComponent={<View />}>
                        <Button
                          disabled
                          type="disabled"
                          onPress={() => null}
                          text={t('PDP.outStock')}
                          radius="sx"
                          size="extra-small"
                          selfAlign="flex-end"
                          borderColor="grey2"
                          labelColor="text"
                          labelSize="mx"
                          style={styles.outStock}
                          labelStyle={styles.labelBottom}
                          paddingHorizontal="m"
                        />
                        <Spacer size="s" />
                      </ErrorHandler>
                    )}
                    <View>
                      {childProduct?.is_in_stock ? (
                        <View>
                          {!selectedGroupProductsForUI?.find?.(
                            child => child?.data?.sku === childProduct?.sku,
                          ) ? (
                            <ErrorHandler
                              componentName="Button"
                              onErrorComponent={<View />}>
                              <Button
                                type="bordered"
                                onPress={() =>
                                  updateSelectedGroupProducts?.(
                                    1,
                                    childProduct?.pricing?.selling_price,
                                    childProduct?.sku,
                                  )
                                }
                                text={t('buttons.add')}
                                style={styles.addBtnStyle}
                                labelStyle={styles.labelStyle}
                                radius="sx"
                                selfAlign="center"
                                borderColor="newSunnyOrange"
                                labelColor="newSunnyOrange"
                                paddingHorizontal="x6l"
                              />
                            </ErrorHandler>
                          ) : (
                            <ErrorHandler
                              componentName="Quantity"
                              onErrorComponent={<View />}>
                              <Quantity
                                textInputRef={quantityInputRef}
                                min={0}
                                max={childProduct?.inventory?.max_sale_qty}
                                value={
                                  selectedGroupProductsForUI?.find?.(
                                    data =>
                                      data?.data?.sku === childProduct?.sku,
                                  )?.data?.quantity
                                }
                                onUpdate={(count, change) => {
                                  handleGroupQuantityUpdateDebounce?.(
                                    count,
                                    childProduct?.pricing?.selling_price,
                                    childProduct?.sku,
                                    change,
                                  );
                                }}
                                backgroundColor="newSunnyOrange"
                                inputStyle={styles.qtytextStyle}
                              />
                            </ErrorHandler>
                          )}
                        </View>
                      ) : (
                        <ErrorHandler
                          componentName="Button"
                          onErrorComponent={<View />}>
                          <Button
                            type="bordered"
                            disabled={notifyIcons?.[childProduct?.sku]}
                            onPress={() => {
                              handleNotifyClick?.(
                                childProduct?.sku,
                                childProduct?.product_id,
                              );
                            }}
                            text={
                              !notifyIcons?.[childProduct?.sku] &&
                              t('buttons.notifyme')
                            }
                            radius="sx"
                            iconLeft={
                              notifyIcons?.[childProduct?.sku] &&
                              'notifyBellIcon'
                            }
                            selfAlign="center"
                            styleLeftIcon={styles.notifyIcon}
                            style={styles.notifyBtnStyle}
                            labelColor="categoryTitle"
                            labelSize="mx"
                            borderColor="categoryTitle"
                            paddingHorizontal={
                              notifyIcons?.[childProduct?.sku] ? 'x5l' : 'xxl'
                            }
                          />
                        </ErrorHandler>
                      )}
                    </View>
                  </View>
                </View>

                {availablePaymentMethod ? (
                  <View>
                    {availablePaymentMethod?.delivery_days?.find?.(
                      deliveryDaysItem =>
                        deliveryDaysItem?.product_id === product?.id,
                    )?.delivery_days ? (
                      <View style={styles.expiryDateView}>
                        <ImageIcon
                          size="xxl"
                          tintColor="categoryTitle"
                          icon="deliveryTruckIcon"
                        />
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          color="categoryTitle"
                          size="m"
                          weight="400"
                          text={
                            availablePaymentMethod?.delivery_days?.find?.(
                              deliveryDaysItem =>
                                deliveryDaysItem?.product_id === product?.id,
                            )?.success_msg
                          }
                        />
                      </View>
                    ) : null}
                    <Spacer size="s" />
                    {availablePaymentMethod?.checkcod?.find?.(
                      deliveryDaysItem =>
                        deliveryDaysItem?.product_id === product?.id,
                    )?.message ? (
                      <View style={styles.expiryDateView}>
                        <ImageIcon
                          size="xxl"
                          tintColor="newSunnyOrange"
                          icon="creditIcon"
                        />
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          color="newSunnyOrange"
                          weight="400"
                          size="m"
                          text={
                            availablePaymentMethod?.checkcod?.find?.(
                              deliveryDaysItem =>
                                deliveryDaysItem?.product_id === product?.id,
                            )?.message
                          }
                        />
                        {availablePaymentMethod?.checkcod?.find?.(
                          deliveryDaysItem =>
                            deliveryDaysItem?.product_id === product?.id,
                        )?.message_arr?.length > 0 ? (
                          <TouchableOpacity
                            onPress={() => {
                              setReturnInfoModel?.(true);
                            }}
                            style={styles.left}>
                            <ImageIcon
                              tintColor="lightGray"
                              size="l"
                              style={styles.imgDeliveryIcon}
                              icon="informationIcon"
                            />
                          </TouchableOpacity>
                        ) : null}
                      </View>
                    ) : null}
                  </View>
                ) : null}
              </View>
            </View>
          );
        })
      ) : (
        <Label
          text={t('PDP.noDataFound')}
          size="mx"
          fontFamily="Medium"
          color="red2"
          align="center"
          textTransform="capitalize"
        />
      )}
    </View>
  );
};

export default memo(SearchProductComponent);

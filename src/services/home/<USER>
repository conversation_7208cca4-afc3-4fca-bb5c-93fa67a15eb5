import Api from 'services';
import {apiRoute, apiEndpoints, subEndpoint} from 'config/apiEndpoint';

const API = new Api();

export const homePageSections = async (params?: any, pageNumber?: number) => {
  const url = `${apiRoute.interface}${apiEndpoints.homePageSections}?page=${
    pageNumber ? pageNumber : 1
  }`;
  const resp = await API.get(url, undefined, params);
  return resp;
};
export const recentlyViewedSection = async (sectionCode?: string) => {
  const url = `${apiRoute.interface}${apiEndpoints.recentView}`;
  const resp = await API.get(url);
  return resp;
};

export const recentlyViewedProduct = async (data?: any) => {
  const url = `${apiRoute.interface}${apiEndpoints.recentlyViewed}`;
  const resp = await API.post(url, data);
  return resp;
};

export const urlResolver = async (urlKey: string) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.urlResolver}?url=${urlKey}`;
  const resp = await API.get(url);
  return resp;
};

export const addProductSuggestion = async (data: any) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.products}/${subEndpoint.products.suggestion}`;
  const resp = await API.post(url, data);
  return resp;
};
export const sellOnDentalKart = async (data: any) => {
  const url = `${apiRoute.orderMg}${apiEndpoints.sellOnDentalKart}/${subEndpoint.sellOnDentalKart.request}`;
  const resp = await API.post(url, data);
  return resp;
};

export const getAllItemPromotionProducts = async () => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${apiEndpoints.itemPromotions}`;
  const resp = await API.get(url);
  return resp;
};

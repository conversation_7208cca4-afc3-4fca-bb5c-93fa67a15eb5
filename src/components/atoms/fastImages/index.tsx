import Icons from 'common/icons';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {View, ViewProps, Image, ImageProps} from 'react-native';
import stylesWithOutColor from './style';
// import FastImage, {FastImageProps} from 'react-native-fast-image';
import {useTheme} from '@react-navigation/native';
import {productDummyImage} from 'utils/imageUrlHelper';
import {useMemo} from 'react';
import FastImage, {FastImageProps} from 'react-native-fast-image';

type Props = {
  icon?: keyof typeof Icons;
  card?: boolean;
  index?: number;
  style?: ViewProps['style'];
  FastImageStyle?: FastImageProps['style'];
  source?: FastImageProps['source'];
  resizeMode?: FastImageProps['resizeMode'];
  tintColor?: string | null;
};

const FastImagesItem = ({
  card = false,
  index = 0,
  style,
  source,
  FastImageStyle,
  resizeMode = 'contain',
  ...props
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [imageSource, setImageSource] = useState(source);

  useEffect(() => {
    setImageSource(source);
  }, [source]);

  const handleError = useCallback(() => {
    if (source && typeof source === 'object' && 'uri' in source) {
      setImageSource({uri: productDummyImage});
    }
  }, [source]);

  return (
    <View key={index} style={[card && styles.imageCard, style]}>
      <FastImage
        style={FastImageStyle}
        source={imageSource}
        resizeMode={resizeMode}
        onError={handleError}
        {...props}
      />
    </View>
  );
};

export default React.memo(FastImagesItem);

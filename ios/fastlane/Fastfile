default_platform(:ios)

platform :ios do
  desc "Deploy a new version to TestFlight"
  api_key = app_store_connect_api_key(
    key_id: ENV["APP_STORE_CONNECT_KEY_ID"],
    issuer_id: ENV["APP_STORE_CONNECT_ISSUER_ID"],
    key_filepath: ENV["APP_STORE_CONNECT_KEY_FILEPATH"],
    in_house: false
  )

  lane :deploy do |options|
    environment = options[:environment] || "production"
    deploy_helper(environment: environment)
  end
  
  
  private_lane :deploy_helper do |options|
    environment = options[:environment]
    is_retry = options[:is_retry] || false

    # Determine the app identifier, scheme, profile names, and targets based on environment
    case environment
    when "production"
      app_identifier = "com.dentalkart.vasadental"
      scheme = "Dentalkart"
      main_profile_name = ENV["PRODUCTION_PROFILE_NAME"]
      notification_service_profile_name = ENV["PRODUCTION_NOTIFICATION_SERVICE_PROFILE_NAME"]
      notification_view_controller_profile_name = ENV["PRODUCTION_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME"]
      target = "Dentalkart"
      notification_service_target = "NSEDev"
      notification_view_controller_target = "NCEDev"
      notification_service_identifier = "com.dentalkart.vasadental.dev.NSEDev"
      notification_view_controller_identifier = "com.dentalkart.vasadental.dev.NCEDev"
    when "staging"
      app_identifier = "com.dentalkart.vasadental.staging"
      scheme = "Staging"
      main_profile_name = ENV["STAGING_PROFILE_NAME"]
      notification_service_profile_name = ENV["STAGING_NOTIFICATION_SERVICE_PROFILE_NAME"]
      notification_view_controller_profile_name = ENV["STAGING_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME"]
      target = "Staging"
      notification_service_target = "NSEDev"
      notification_view_controller_target = "NCEDev"
      notification_service_identifier = "com.dentalkart.vasadental.dev.NSEDev"
      notification_view_controller_identifier = "com.dentalkart.vasadental.dev.NCEDev"
    when "dev"
      app_identifier = "com.dentalkart.vasadental.dev"
      scheme = "Dev"
      main_profile_name = ENV["DEV_PROFILE_NAME"]
      notification_service_profile_name = ENV["DEV_NOTIFICATION_SERVICE_PROFILE_NAME"]
      notification_view_controller_profile_name = ENV["DEV_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME"]
      target = "Dev"
      notification_service_target = "NSEDev"
      notification_view_controller_target = "NCEDev"
      notification_service_identifier = "com.dentalkart.vasadental.dev.NSEDev"
      notification_view_controller_identifier = "com.dentalkart.vasadental.dev.NCEDev"
    else
      UI.user_error!("Invalid environment: #{environment}")
    end

    setup_ci

    # Update code signing settings for the main target
    update_code_signing_settings(
      use_automatic_signing: false,
      path: "Dentalkart.xcodeproj",
      team_id: ENV["TEAM_ID"],
      targets: [target],
      code_sign_identity: ENV["CODE_SIGN_IDENTITY"],
      profile_name: main_profile_name
    )

    # Update code signing settings for NotificationService target
    update_code_signing_settings(
      use_automatic_signing: false,
      path: "Dentalkart.xcodeproj",
      team_id: ENV["TEAM_ID"],
      targets: [notification_service_target],
      code_sign_identity: ENV["CODE_SIGN_IDENTITY"],
      profile_name: notification_service_profile_name
    )

    # Update code signing settings for NotificationViewController target
    update_code_signing_settings(
      use_automatic_signing: false,
      path: "Dentalkart.xcodeproj",
      team_id: ENV["TEAM_ID"],
      targets: [notification_view_controller_target],
      code_sign_identity: ENV["CODE_SIGN_IDENTITY"],
      profile_name: notification_view_controller_profile_name
    )

    match(
      type: "appstore",
      api_key: api_key,
      git_basic_authorization: ENV["MATCH_GIT_BASIC_AUTHORIZATION"],
      app_identifier: [app_identifier, notification_service_identifier, notification_view_controller_identifier]
    )

    # Pass the target dynamically to custom_version_management lane if not retry
    # custom_version_management(target: target) unless is_retry

    #increment_build_number(xcodeproj: "Dentalkart.xcodeproj")

    build_app(
      workspace: "Dentalkart.xcworkspace",
      scheme: scheme,
      clean: false,
      export_options: {
        provisioningProfiles: {
          app_identifier => main_profile_name,
          notification_service_identifier => notification_service_profile_name,
          notification_view_controller_identifier => notification_view_controller_profile_name
        }
      }
    )

    update_code_signing_settings(
      use_automatic_signing: true,
      path: "Dentalkart.xcodeproj"
    )

    upload_to_testflight(
      api_key: api_key,
      skip_waiting_for_build_processing: true,
      app_identifier: app_identifier
    )
  end

  lane :custom_version_management do |options|
    target = options[:target]

    current_version = get_version_number(
      xcodeproj: "Dentalkart.xcodeproj",
      target: target
    )

    # Split the current version into major, minor, and patch parts
    version_parts = current_version.split(".").map(&:to_i)
    major = version_parts[0]
    minor = version_parts[1]
    patch = version_parts[2]

    # Determine how to increment the version
    if patch < 9
      patch += 1
    elsif minor < 9
      patch = 0
      minor += 1
    else
      patch = 0
      minor = 0
      major += 1
    end

    # Construct the new version number
    new_version = "#{major}.#{minor}.#{patch}"

    # Set the new version number
    increment_version_number(
      xcodeproj: "Dentalkart.xcodeproj",
      version_number: new_version
    )
  end
  lane :sync_all_certificates do
    # match(
    #   type: "development",
    #   readonly: false,
    # )
  
    match(
      type: "appstore",
      readonly: false,
    )
  end
end

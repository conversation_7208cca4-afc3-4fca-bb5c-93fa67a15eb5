import React, {useMemo, useRef, useEffect} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {
  ImageIcon,
  Label,
  FastImagesItem,
  Quantity,
  Spacer,
} from 'components/atoms';
import getImageUrl from 'utils/imageUrlHelper';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import LinearGradient from 'react-native-linear-gradient';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../../routes';
import {useDebouncedCallback} from 'use-debounce';
import {BlurView} from '@react-native-community/blur';

type Props = {
  item?: Item;
  index?: number;
  deliveryInfoMsg?: string;
  removeCart?: (item: Item) => void;
  updateCart?: (count: number, id: string) => void;
  selectedAddress?: CustomerAddressV2;
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  buyNow?: boolean;
  infoClick: () => void;
};

const CartItem = (props: Props) => {
  const {
    item,
    index,
    deliveryInfoMsg,
    selectedAddress,
    removeCart,
    updateCart,
    navigation,
    buyNow = false,
    infoClick,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  let itemQuantityRef = useRef(item?.quantity);
  itemQuantityRef.current = item?.quantity;
  const debouncedQuantityUpdate = useDebouncedCallback((count: number) => {
    itemQuantityRef.current = count;
    updateCart(Number(count), item?.item_id);
  }, 500);

  useEffect(() => {
    itemQuantityRef.current = item?.quantity;
  }, [item?.quantity]);

  const off = item?.item_pricing_details?.discounts?.find(
    (discount: any) => discount?.amount?.label === 'Saving on regular price',
  )?.amount?.value;

  return (
    <LinearGradient
      key={index}
      colors={
        item?.is_member_ship_product
          ? [colors.haiti, colors.tyrianPurple, colors.mardiGras]
          : item?.is_free_product
          ? [colors.violet1, colors.violet1]
          : [colors.whiteColor, colors.whiteColor]
      }
      start={{x: 0, y: 0}}
      end={{x: 0, y: 1}}
      style={
        item?.is_free_product || item?.is_member_ship_product
          ? styles.cartItem
          : styles.mainView
      }>
      <View style={styles.cartContinuer}>
        <View style={styles.cartSubItem}>
          <View style={styles.cartSubContinuer}>
            <TouchableOpacity
              testID="tOCartItem"
              onPress={() => {
                if (item?.is_member_ship_product) {
                  navigation.navigate('MembershipPage');
                } else {
                  navigation.navigate('ProductDetail', {
                    productId: item?.parent_id
                      ? item?.parent_id
                      : item?.product?.id,
                    ProductItems: {
                      media: {
                        mobile_image: item?.product?.image?.url,
                      },
                    },
                  });
                }
              }}
              activeOpacity={item?.is_member_ship_product ? 1 : 0.7}
              style={styles.subContainer2}>
              <FastImagesItem
                FastImageStyle={[
                  styles.imgPlus,
                  item?.is_free_product && {height: 84},
                  item.stock_status == 0 && styles.outStockBg,
                ]}
                source={
                  item?.is_member_ship_product
                    ? Icons.mamberShipCardImage
                    : {uri: getImageUrl(item?.product?.thumbnail?.url)}
                }
              />
              {item.stock_status == 0 && (
                <View style={styles.outStock}>
                  <BlurView
                    style={styles.blur}
                    blurType="light"
                    blurAmount={0.53}
                    reducedTransparencyFallbackColor={colors.orient20}
                  />
                  <Label
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    text={t('PDP.outStock')}
                    size="m"
                    weight="500"
                    color="text"
                    style={styles.labelBottom}
                  />
                </View>
              )}
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xm" />
            <View style={styles.cardView}>
              <TouchableOpacity
                testID="tOCardView"
                onPress={() => {
                  if (item?.is_member_ship_product) {
                    navigation.navigate('MembershipPage');
                  } else {
                    navigation.navigate('ProductDetail', {
                      productId: item?.parent_id
                        ? item?.parent_id
                        : item?.product?.id,
                      ProductItems: {
                        media: {
                          mobile_image: item?.product?.image?.url,
                        },
                      },
                    });
                  }
                }}
                activeOpacity={item?.is_member_ship_product ? 1 : 0.7}
                style={styles.cartBrands}>
                <View style={styles.rowView}>
                  <Label
                    numberOfLines={2}
                    ellipsizeMode="tail"
                    text={item?.product?.name}
                    size="mx"
                    fontFamily="Medium"
                    color="text"
                    style={styles.fOne}
                  />
                  {!item?.is_free_product && buyNow && (
                    <TouchableOpacity
                      testID="tORemoveCart"
                      style={styles.closeView}
                      onPress={() => removeCart(item)}>
                      <ImageIcon icon="crossGrey" size="m" tintColor="grey" />
                    </TouchableOpacity>
                  )}
                </View>

                {!!item?.reward_point_product && !item?.is_free_product ? (
                  <>
                    <Spacer size="xs" />
                    <View style={styles.rewardPointView}>
                      <ImageIcon size="mx" icon="coin" />
                      <Spacer size="s" type="Horizontal" />
                      <Label
                        text={item?.reward_point_product}
                        size="m"
                        fontFamily="Medium"
                        color="orange"
                      />
                    </View>
                  </>
                ) : null}
                {!!deliveryInfoMsg &&
                !item?.is_free_product &&
                !item?.is_member_ship_product ? (
                  <>
                    <Spacer size="xs" />
                    <View style={styles.deliveryTruck}>
                      <ImageIcon size="xsl" icon="deliveryTruckIcon" />
                      <Spacer size="xm" type="Horizontal" />
                      <Label
                        fontFamily="Regular"
                        color="categoryTitle"
                        size="m"
                        text={deliveryInfoMsg}
                      />
                      <TouchableOpacity
                        testID="tOInfoClick"
                        onPress={() => infoClick()}
                        style={styles.imgLeft}>
                        <ImageIcon size="xsl" icon="infoCircle" />
                      </TouchableOpacity>
                    </View>
                  </>
                ) : null}
                <Spacer size="xs" />

                {!item?.is_free_product &&
                  Array.isArray(item?.error_messages) &&
                  item?.error_messages?.map((e: any, inx: number) => {
                    return !selectedAddress?.country_id ||
                      (selectedAddress?.country_id === 'IN' &&
                        e?.code === 'internationally_inactive') ? null : (
                      <View
                        key={inx?.toString()}
                        style={styles.itemFullSizeView}>
                        <ImageIcon
                          size="mx"
                          tintColor="textError"
                          style={styles.itemRight}
                          icon="closeIcons"
                        />
                        <View style={styles.fOne}>
                          <Label
                            text={e?.message}
                            size="m"
                            weight="400"
                            color="textError"
                            style={styles.errorMsg}
                          />
                        </View>
                      </View>
                    );
                  })}
                <Spacer size="xs" />
                <View style={styles.rowView}>
                  <View style={styles.underPriceView}>
                    <Label
                      size="mx"
                      weight="500"
                      color="text"
                      text={
                        !item?.is_free_product
                          ? item?.product?.price?.minimalPrice?.amount
                              ?.currency_symbol +
                            Number(
                              item?.item_pricing_details?.discounts.find(
                                i => i?.code === 'total_discount',
                              )?.amount?.value,
                            )
                          : item?.product?.price?.minimalPrice?.amount
                              ?.currency_symbol + 0
                      }
                    />
                    {off > 0 && (
                      <>
                        <Spacer type="Horizontal" size="s" />
                        <Label
                          size="m"
                          weight="500"
                          color="darkGray"
                          textDecorationLine={'line-through'}
                          text={
                            item?.product?.price?.regularPrice?.amount
                              ?.currency_symbol +
                            item?.item_pricing_details?.row_total_regular_price
                              ?.amount?.value
                          }
                        />
                      </>
                    )}
                    {/* <Spacer type="Horizontal" size="s" />
                    {item?.product?.stock_status !== 'OUT_OF_STOCK' &&
                    !item?.is_free_product ? (
                      <View style={styles.discountStyle}>
                        <Label
                          text={`${
                            item?.item_pricing_details?.discounts?.find(
                              (discount: any) =>
                                discount.amount.label ===
                                'Saving on regular price',
                            )?.amount?.value
                          }${t('productItemsHorizontal.off')}`}
                          color="darkGreen"
                          size="mx"
                          fontFamily="Medium"
                        />
                      </View>
                    ) : null} */}
                  </View>
                </View>
                {item?.product?.dentalkart_custom_fee && (
                  <Label
                    size="mx"
                    fontFamily="Medium"
                    color="text"
                    text={
                      '+ ' +
                      item?.product?.price?.minimalPrice?.amount
                        ?.currency_symbol +
                      (!item?.is_free_product
                        ? Number(item?.product?.dentalkart_custom_fee) *
                          item?.quantity
                        : 0) +
                      ' ' +
                      t('cart.deliveryFee')
                    }
                  />
                )}
                {item?.is_free_product ? (
                  <>
                    <Spacer size="xs" />
                    {/* <Label
                      fontFamily="Medium"
                      color="categoryTitle"
                      size="mx"
                      text={
                        t('cart.unitProduct') +
                        item?.product?.weight +
                        ' ' +
                        t('cart.kg')
                      }
                    /> */}
                    <Label
                      fontFamily="Medium"
                      color="categoryTitle"
                      size="m"
                      text={t('cart.Quantity') + ' : ' + item?.quantity}
                    />
                  </>
                ) : null}
                {item?.is_member_ship_product && (
                  <>
                    <Label
                      fontFamily="Medium"
                      color="text"
                      size="m"
                      text={t('PDP.sameDayDelivery')}
                    />
                    <Label
                      fontFamily="SemiBold"
                      color="text"
                      size="m"
                      text={t('cart.Quantity') + ' : ' + item?.quantity}
                    />
                  </>
                )}
              </TouchableOpacity>
              {!item?.is_free_product && !item?.is_member_ship_product && (
                <View style={styles.cartQtyView}>
                  {/* <Label
                    color="categoryTitle"
                    size="mx"
                    fontFamily="Medium"
                    text={
                      t('cart.unitProduct') +
                      item?.product?.weight +
                      ' ' +
                      t('cart.kg')
                    }
                  /> */}
                  {/* {item?.product?.dentalkart_custom_fee && (
                    <View style={styles.deliveryTruck}>
                      <Spacer size="xm" type="Horizontal" />
                      <Label
                        fontFamily="Regular"
                        color="categoryTitle"
                        size="mx"
                        text={`${t('cart.deliveryFee')}-${
                          item?.product?.price?.regularPrice?.amount
                            ?.currency_symbol
                        }${item?.product?.dentalkart_custom_fee}`}
                      />
                    </View>
                  )} */}
                  {item?.product?.stock_status !== 'OUT_OF_STOCK' &&
                  !item?.is_free_product &&
                  off > 0 ? (
                    <View style={styles.discountStyle}>
                      <Label
                        text={`${off}${t('productItemsHorizontal.off')}`}
                        color="darkGreen"
                        size="mx"
                        fontFamily="Medium"
                      />
                    </View>
                  ) : (
                    <View style={styles.fOne} />
                  )}
                  {buyNow ? (
                    <Quantity
                      min={1}
                      max={item?.product?.max_sale_qty}
                      value={Number(itemQuantityRef.current).toFixed(0)}
                      onUpdate={count => {
                        // AnalyticsEvents(
                        //   'CART_ITEM_QUANTITY_UPDATED',
                        //   'cart_prod_qty_updated',
                        //   {...item, updated_quantity: count},
                        // );
                        debouncedQuantityUpdate(Number(count));
                      }}
                      cartItemScreen
                      minusStyle={styles.width}
                      plusStyle={styles.width}
                      delayUpdate
                    />
                  ) : (
                    <View style={styles.alignEnd}>
                      <Label
                        text={`${t('cart.Quantity')} : ${item?.quantity}`}
                        color="categoryTitle"
                        size="mx"
                        fontFamily="Medium"
                      />
                    </View>
                  )}
                </View>
              )}
            </View>
          </View>
        </View>

        {item?.is_free_product ? (
          <View style={styles.freeContinuer}>
            <View>
              <View style={styles.freeSubContinuer} />
              <Spacer size="xms" />
              <View style={styles.freeSubContinuer} />
              <Spacer size="xms" />
              <View style={styles.freeSubContinuer} />
              <Spacer size="xms" />
              <View style={styles.freeSubContinuer} />
            </View>
            <Label
              style={styles.freeText}
              color="darkGreen"
              text={t('cart.free')}
              size="mx"
              weight="500"
            />
          </View>
        ) : (
          <View />
        )}
      </View>
    </LinearGradient>
  );
};

export default React.memo(CartItem);

import {Platform} from 'react-native';
import Api from 'services';
import objectToQueryString from 'utils/objectToQueryString';
import {
  apiRoute,
  apiEndpoints,
  subEndpoint,
  REFERRAL_X_API_KEY,
} from 'config/apiEndpoint';
import DeviceInfo from 'react-native-device-info';
import tokenClass from 'utils/token';
const API = new Api();

export const getMe = async () => {
  const loginStatus = await tokenClass.loginStatus();
  if(loginStatus){
    const url = `${apiRoute.customers}${apiEndpoints.customerMe}`;
    const resp = await API.get(url);
    return resp;
  }
};

export const getAppVersion = async loggedIn => {
  if (!loggedIn) {
    return {status: false, data: null};
  }
  const url = `${apiRoute.customers}${apiEndpoints.customerMe}/${subEndpoint.app.version}`;
  const obj = {
    app_version: DeviceInfo.getVersion(),
    device_id: await DeviceInfo.getUniqueId(),
    platform: Platform.OS,
  };
  const resp = await API.post(url, obj);
  return resp;
};

export const getForceUpdateVersion = async (platform: string) => {
  const url = `${apiRoute.orderMg}${apiEndpoints.appConfig}?${subEndpoint.app.platform}${platform}`;
  const resp = await API.get(url);
  return resp;
};

export const updateMe = async data => {
  const url = `${apiRoute.customers}${apiEndpoints.customerMe}`;
  const resp = await API.patch(url, data);
  return resp;
};

export const deleteMe = async () => {
  const url = `${apiRoute.customers}${apiEndpoints.customerMe}`;
  const resp = await API.delete(url);
  return resp;
};
// =================reward zone====================
export const accountSummery = async () => {
  const loginStatus = await tokenClass.loginStatus();
  if(loginStatus){
    const url = `${apiRoute.reward}${apiEndpoints.rewards}/${subEndpoint.rewards.summary}`;
    const resp = await API.get(url);
    return resp;
  }
};
export const rewordTransactions = async data => {
  const query = objectToQueryString(data);
  const url = `${apiRoute.reward}${apiEndpoints.rewards}/${subEndpoint.rewards.transactions}?${query}`;
  const resp = await API.get(url);
  return resp;
};

export const getMyReferral = async () => {
  const url = `${apiRoute.referral}${apiEndpoints.referrals}/${subEndpoint.referrals.summary}`;
  const resp = await API.get(url, true, null, REFERRAL_X_API_KEY);
  return resp;
};

export const getReferrals = async (data: Referrals) => {
  const loginStatus = await tokenClass.loginStatus();
  if(loginStatus){
    const url = `${apiRoute.referral}${apiEndpoints.referrals}`;
    const resp = await API.post(url, data, REFERRAL_X_API_KEY);
    return resp;
  }
};

export const checkReferralCode = async (code: string) => {
  const url = `${apiRoute.referral}${apiEndpoints.referrals}/${code}/${subEndpoint.referrals.validate}`;
  const resp = await API.get(url, false, null, REFERRAL_X_API_KEY);
  return resp;
};

export const getAllFAQ = async () => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.faq}/${subEndpoint.helpCenter.category}`;
  const resp = await API.get(url);
  return resp;
};

export const useWhatsApp = async () => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.chat}/${subEndpoint.helpCenter.whatsAppConfig}`;
  const resp = await API.get(url);
  return resp;
};

export const notifyMe = async id => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.notifyMe}`;
  const resp = await API.post(url, {id});
  return resp;
};

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      width: '100%',
      height: Sizes.x6l + Sizes.xms,
      borderColor: colors.border,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.m,
    },
    errorBorder: {
      borderColor: colors.textError,
    },
    errorContainer: {
      marginTop: Sizes.s,
      alignSelf: 'flex-start',
      paddingLeft: Sizes.xs,
    },
    input: {
      fontSize: Sizes.l,

      color: colors.textLight,
    },
    dropDownContainer: {
      borderColor: colors.border,
      borderRadius: Sizes.xm,
      backgroundColor: colors.background,
    },
  });

export default styles;

import Api from 'services';
import {apiRoute, apiEndpoints, subEndpoint} from 'config/apiEndpoint';

const API = new Api();

export const createOrder = async (data: any) => {
  const url = `${apiRoute.order}${apiEndpoints.orders}`;
  const resp = await API.post(url, data);
  return resp;
};
export const fetchPayment = async (data: any) => {
  const url = `${apiRoute.order}${apiEndpoints.orders}/${subEndpoint.order.payments}`;
  const resp = await API.post(url, data);
  return resp;
};

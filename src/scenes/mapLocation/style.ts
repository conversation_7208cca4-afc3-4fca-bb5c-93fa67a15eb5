import {Fonts, Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      flex: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    container: {
      backgroundColor: colors.background,
      flex: Sizes.x,
    },
    fOne: {
      flex: Sizes.x,
    },
    headerView: {
      alignItems: 'center',
      flexDirection: 'row',
      height: Sizes.x8l,
    },
    rowCenter: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    labelBottom: {
      marginBottom: -Sizes.xs,
    },
    addressContainer: {
      backgroundColor: colors.whiteColor,
      borderTopLeftRadius: Sizes.xm,
      borderTopRightRadius: Sizes.xm,
      paddingHorizontal: Sizes.l,
      shadowColor: colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4.65,
      elevation: Sizes.xs,
      zIndex: Sizes.m,
      height: Sizes.ex166,
    },
    btnView: {
      width: '100%',
      borderRadius: Sizes.m,
      height: Sizes.x7l,
    },
    btnTxt: {
      fontFamily: Fonts.Medium,
    },
    searchMainView: {
      position: 'absolute',
      alignItems: 'center',
      margin: Sizes.xms,
      flexDirection: 'row',
    },
    placeText: {
      fontFamily: Fonts.Medium,
      fontSize: Sizes.m,
    },
    closeIcon: {
      paddingRight: Sizes.l,
      paddingLeft: Sizes.s,
    },
    currentLatView: {
      borderColor: colors.vividOrange,
      borderWidth: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'center',
      paddingHorizontal: Sizes.m,
      height: Sizes.x26,
      borderRadius: Sizes.sx,
      position: 'absolute',
      bottom: Sizes.l,
      backgroundColor: colors.whiteColor,
      zIndex: Sizes.m,
    },
    callView: {
      backgroundColor: colors.text,
      borderRadius: Sizes.xms,
      padding: Sizes.xms,
      alignItems: 'center',
      elevation: Sizes.s,
    },
    arrowDown: {
      width: Sizes.x6l,
      height: Sizes.x6l,
      borderLeftWidth: Sizes.xl,
      borderRightWidth: Sizes.xl,
      borderTopWidth: Sizes.xl,
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderTopColor: colors.text,
      alignSelf: 'center',
      marginTop: Platform.OS === 'android' ? 0 : Sizes.xm,
    },
    arrowUp: {
      width: Sizes.x6l,
      height: Sizes.x6l,
      borderLeftWidth: Sizes.xl,
      borderRightWidth: Sizes.xl,
      borderBottomWidth: Sizes.xl,
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderBottomColor: colors.text,
      alignSelf: 'center',
    },
    markerPos: {
      marginBottom: Platform.OS === 'android' ? 0 : -Sizes.l,
    },
    address: {
      marginBottom: -Sizes.xs,
      height: Sizes.x34,
    },
    callout: {
      position: 'absolute',
      zIndex: Sizes.xms,
      width: Platform.OS === 'android' ? Sizes.ex3l : Sizes.ex290,
      height: Sizes.x70,
      backgroundColor: colors.text,
      borderRadius: Sizes.xms,
      padding: Sizes.xms,
      alignItems: 'center',
      elevation: Sizes.s,
    },
    callView1: {
      padding: Sizes.s,
    },
    markerView: {
      height: Sizes.x7l,
      width: Sizes.x7l,
      position: 'absolute',
      zIndex: Sizes.xms,
    },
  });
export default styles;

import Icons from 'common/icons';
export const news = [
  {
    id: 3,
    content: 'CONTENST TEST-----2_TEST',
    image: Icons.imageMag,
    date: '1 February 2024',
    readTime: '3 mins read',
    name: 'Dr. <PERSON><PERSON>',
    source_link: 'SOUCRVSS LINK TESTING------2_YEST',
    type: 'Dentistry Tips',
    title:
      'Navigating the CAD/CAM workflow in modern dentistry. Navigating the CAD/CAM workflow.',
    is_archived: true,
    created_at: '2024-07-14T13:19:10.878Z',
    updated_at: '2024-07-15T06:46:31.000Z',
    news_extra_info: [
      {
        id: 10,
        customer_id: 20579,
        action: 'add-to-history',
        created_at: '2024-07-15T06:42:38.957Z',
      },
      {
        id: 7,
        customer_id: 20579,
        action: 'bookmarked',
        created_at: '2024-07-15T06:39:03.214Z',
      },
    ],
  },
  {
    id: 6,
    content: 'CONTENST TEST-----2_TEST',
    image: Icons.imageMag,
    date: '1 February 2024',
    readTime: '3 mins read',
    name: 'Dr. <PERSON><PERSON>d',
    source_link: 'SOUCRVSS LINK TESTING------2_YEST',
    type: 'Dentistry Tips',
    title:
      'Navigating the CAD/CAM workflow in modern dentistry. Navigating the CAD/CAM workflow.',
    is_archived: true,
    created_at: '2024-07-18T06:57:41.191Z',
    updated_at: '2024-07-24T12:12:21.000Z',
    news_extra_info: [],
  },
  {
    id: 7,
    content: 'CONTENST TEST',
    image: Icons.imageMag,
    date: '1 February 2024',
    readTime: '3 mins read',
    name: 'Dr. Megha Sood',
    source_link: 'SOUCRVSS LINK TESTING',
    type: 'TYPE NEWS',
    title: 'TITLE CHECK',
    is_archived: true,
    created_at: '2024-07-24T12:11:48.018Z',
    updated_at: '2024-07-24T12:11:48.018Z',
    news_extra_info: [],
  },
];

export const shorts = [
  {
    id: 155,
    video_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/output_1723469506397_Vasa_SensiShield-SF_Mouthwash_(Pack_of_10).mp4',
    caption:
      'Discover the dual-action power of Vasa SensiShield-SF Mouthwash! Specially formulated with potassium nitrate and sodium fluoride, this mouthwash tackles tooth sensitivity while strengthening enamel to prevent cavities. The potassium nitrate soothes nerve ',
    title: 'Vasa SensiShield-SF Mouthwash',
    height: 1920,
    width: 1080,
    thumbnail_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723469502991_Dentalkart_-_Vasa_SensiShield-SF_Mouthwash_%28Pack_of_10%29_-_Advanced_Sensitivity_%26_Cavity_Protection_%23dentist_%5BOw2-k1pjVhQ_-_290x516_-_0m01s%5D_%281%29.png',
    thumbnail_gif_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723469506259_Vasa_SensiShield-SF_Mouthwash_%28Pack_of_10%29.mp4.gif',
    status: 'SUCCESS',
    like_count: 0,
    comment_count: 0,
    featured_product: null,
    created_at: '2024-08-12T13:31:43.000Z',
    updated_at: '2024-08-12T13:32:16.000Z',
    deleted_at: null,
    duration: 8,
    totalWatchCount: 0,
    totalWatchTime: 0,
    streaming_url:
      'https://feeds-prod.dentalkart.com/feeds/stream/output_1723469506397_Vasa_SensiShield-SF_Mouthwash_(Pack_of_10).mp4',
    author_name: 'DENTALKART',
    author_image: 'https://www.dentalkart.com/dentalkarticon.png',
    categories: [
      {
        id: 3,
        category_key: 'General',
        VideoTags: {
          id: 265,
        },
      },
      {
        id: 4,
        category_key: 'Dental',
        VideoTags: {
          id: 266,
        },
      },
    ],
  },
  {
    id: 154,
    video_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/output_1723201553805_Periosteal_Elevator_Molt_No-9.mp4',
    caption:
      'Waldent Periosteal Elevator Molt No.9 is Double - Ended Instrument with one rounded, blunted end & other end is sharp-pointed. The blunt end is Spoon - Shaped. The rounded edge of the Molt 9 periosteal elevator minimises the tearing of tissue when lifting',
    title: 'Periosteal Elevator Molt No-9.',
    height: 1920,
    width: 1080,
    thumbnail_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723201544832_Dentalkart_-_Waldent_Periosteal_Elevator_Molt_No.9_%289108%29_%23doctor_%23dentalkart_%5BEAAfOXDzaWk_-_290x516_-_0m00s%5D.png',
    thumbnail_gif_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723201553657_Periosteal_Elevator_Molt_No-9.mp4.gif',
    status: 'SUCCESS',
    like_count: 1,
    comment_count: 0,
    featured_product: null,
    created_at: '2024-08-09T11:05:44.000Z',
    updated_at: '2024-08-10T04:05:44.000Z',
    deleted_at: null,
    duration: 14,
    totalWatchCount: 4,
    totalWatchTime: 18,
    streaming_url:
      'https://feeds-prod.dentalkart.com/feeds/stream/output_1723201553805_Periosteal_Elevator_Molt_No-9.mp4',
    author_name: 'DENTALKART',
    author_image: 'https://www.dentalkart.com/dentalkarticon.png',
    categories: [
      {
        id: 3,
        category_key: 'General',
        VideoTags: {
          id: 263,
        },
      },
      {
        id: 4,
        category_key: 'Dental',
        VideoTags: {
          id: 264,
        },
      },
    ],
  },
  {
    id: 153,
    video_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/output_1723122971305_iDENTical_Kids_Ultra_Soft_Toothbrush_(Elephant).mp4',
    caption:
      'Introducing the iDENTical Kids Ultra Soft Toothbrush (Elephant), the perfect dental companion for children aged 3-12 years. This adorable toothbrush features ultra-soft nylon bristles that are gentle on gums, ensuring a comfortable and effective brushing ',
    title: 'iDENTical Kids Ultra Soft Toothbrush (Elephant)',
    height: 1920,
    width: 1080,
    thumbnail_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723122964271_Dentalkart_-_iDENTical_Kids_Ultra_Soft_Toothbrush_%28Elephant%29_-_Fun_and_Gentle_Oral_Care_for_Children_%23toothbrush_%5BX2dISQ383pk_-_290x516_-_0m00s%5D.png',
    thumbnail_gif_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723122971130_iDENTical_Kids_Ultra_Soft_Toothbrush_%28Elephant%29.mp4.gif',
    status: 'SUCCESS',
    like_count: 0,
    comment_count: 0,
    featured_product: null,
    created_at: '2024-08-08T13:16:04.000Z',
    updated_at: '2024-08-08T13:16:41.000Z',
    deleted_at: null,
    duration: 13,
    totalWatchCount: 2,
    totalWatchTime: 1,
    streaming_url:
      'https://feeds-prod.dentalkart.com/feeds/stream/output_1723122971305_iDENTical_Kids_Ultra_Soft_Toothbrush_(Elephant).mp4',
    author_name: 'DENTALKART',
    author_image: 'https://www.dentalkart.com/dentalkarticon.png',
    categories: [
      {
        id: 3,
        category_key: 'General',
        VideoTags: {
          id: 261,
        },
      },
      {
        id: 4,
        category_key: 'Dental',
        VideoTags: {
          id: 262,
        },
      },
    ],
  },
  {
    id: 152,
    video_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/output_1723122664046_iDENTical_Dental_Educational_Suture_Model.mp4',
    caption:
      'Elevate your oral surgical skills with the iDENTical Dental Educational Suture Model, a premier training tool designed for mastering suturing techniques in dental procedures. Available in three variations—Maxillary Training Model, Toothless Training Model',
    title: 'DENTical Dental Educational Suture Model',
    height: 1920,
    width: 1080,
    thumbnail_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723122655588_Dentalkart_-_Master_Suturing_Techniques_with_iDENTical_Dental_Educational_Suture_Model_%23dentist_%23dentalkart_%5BZCPwjylnzo8_-_290x516_-_0m00s%5D.png',
    thumbnail_gif_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1723122663548_iDENTical_Dental_Educational_Suture_Model.mp4.gif',
    status: 'SUCCESS',
    like_count: 0,
    comment_count: 0,
    featured_product: null,
    created_at: '2024-08-08T13:10:55.000Z',
    updated_at: '2024-08-10T04:10:46.000Z',
    deleted_at: null,
    duration: 12,
    totalWatchCount: 13,
    totalWatchTime: 29,
    streaming_url:
      'https://feeds-prod.dentalkart.com/feeds/stream/output_1723122664046_iDENTical_Dental_Educational_Suture_Model.mp4',
    author_name: 'DENTALKART',
    author_image: 'https://www.dentalkart.com/dentalkarticon.png',
    categories: [
      {
        id: 3,
        category_key: 'General',
        VideoTags: {
          id: 259,
        },
      },
      {
        id: 4,
        category_key: 'Dental',
        VideoTags: {
          id: 260,
        },
      },
    ],
  },
  {
    id: 151,
    video_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/output_1722922939319_iDENTical_Mandible_Bone_Model_M4014.mp4',
    caption:
      'Explore the iDENTical Mandible Bone Model M4014, a detailed anatomical model perfect for teaching and demonstrating the anatomy of the mandible. Ideal for dental colleges and hospitals, this model is also invaluable in dental clinics for patient education',
    title: 'iDENTical Mandible Bone Model ',
    height: 1920,
    width: 1080,
    thumbnail_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1722922931932_Dentalkart_-_iDENTical_Mandible_Bone_Model_M4014_%5BrKG1EUmOLbM_-_290x516_-_0m02s%5D.png',
    thumbnail_gif_url:
      'https://dental-feeds.s3.ap-south-1.amazonaws.com/1722922939101_iDENTical_Mandible_Bone_Model_M4014.mp4.gif',
    status: 'SUCCESS',
    like_count: 0,
    comment_count: 1,
    featured_product: null,
    created_at: '2024-08-06T05:42:12.000Z',
    updated_at: '2024-08-10T04:20:11.000Z',
    deleted_at: null,
    duration: 14,
    totalWatchCount: 24,
    totalWatchTime: 135,
    streaming_url:
      'https://feeds-prod.dentalkart.com/feeds/stream/output_1722922939319_iDENTical_Mandible_Bone_Model_M4014.mp4',
    author_name: 'DENTALKART',
    author_image: 'https://www.dentalkart.com/dentalkarticon.png',
    categories: [
      {
        id: 3,
        category_key: 'General',
        VideoTags: {
          id: 257,
        },
      },
      {
        id: 4,
        category_key: 'Dental',
        VideoTags: {
          id: 258,
        },
      },
    ],
  },
];

export const magazine = [
  {
    _id: 'f7db5e65-ce4a-4e91-bbbd-a27e16b0acd4',
    magazine_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/Dental-Technology%20April%20May.pdf',
    magazine_name: 'Dental Technology',
    author: 'Dental Technology',
    publisher: 'Dental Technology',
    date_of_publish: '2024-05-31T00:00:00.000Z',
    coverPage_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/Dental%20tech.png',
    created_At: '2024-07-29T11:13:17.073Z',
    updated_At: '2024-07-29T11:13:17.073Z',
    enable: true,
  },
  {
    _id: '4240d5d7-d44c-4c59-a8f4-1d6eff2a09aa',
    magazine_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/inews_aeedc_2024%20%281%29.pdf',
    magazine_name: 'inews',
    author: 'inews',
    publisher: 'Infodent',
    date_of_publish: '2024-02-01T00:00:00.000Z',
    coverPage_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/Screenshot%202024-06-10%20174600.png',
    created_At: '2024-06-10T12:19:12.806Z',
    updated_At: '2024-06-22T12:34:28.125Z',
    enable: true,
  },
  {
    _id: '03b5adca-2281-4d2e-b93f-c8a1928234ce',
    magazine_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/infodent_02_2024.pdf',
    magazine_name: 'Infodent',
    author: 'Infodent',
    publisher: 'Infodent',
    date_of_publish: '2024-05-01T00:00:00.000Z',
    coverPage_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/Screenshot%202024-06-10%20174034.png',
    created_At: '2024-06-10T12:11:58.371Z',
    updated_At: '2024-06-10T12:11:58.371Z',
    enable: true,
  },
  {
    _id: '116141aa-c2ed-4d0b-9a59-2dff037ac3cf',
    magazine_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/infodent_01_2024.pdf',
    magazine_name: 'Infodent',
    author: 'Infodent',
    publisher: 'Infodent',
    date_of_publish: '2024-02-01T00:00:00.000Z',
    coverPage_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/Screenshot%202024-06-10%20173541.png',
    created_At: '2024-06-10T12:08:22.660Z',
    updated_At: '2024-06-10T12:08:22.660Z',
    enable: true,
  },
  {
    _id: '31b50933-7ae4-4880-9e94-abc36ed14589',
    magazine_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/Famdent%20April%20-%20June%202024.pdf',
    magazine_name: 'Famdent',
    author: 'Famdent',
    publisher: 'Famdent',
    date_of_publish: '2024-04-01T00:00:00.000Z',
    coverPage_url:
      'https://dentalkart-application-media.s3.amazonaws.com/magazine/Famdent%20Apr-Jun%202024.PNG',
    created_At: '2024-06-03T12:10:56.233Z',
    updated_At: '2024-06-03T12:10:56.233Z',
    enable: true,
  },
];

export const blogs = [
  {
    icon: Icons.helpBanner,
    name: 'Dr. Neha Tiwari',
    title: 'Dentistry Tips',
    text: 'Best RVG Sensors to buy in 2024 and Importance of RVG in Clinic Practice',
  },
  {
    icon: Icons.newsBannerIcon,
    name: 'Dr. Neha Tiwari',
    title: 'Dentistry Tips',
    text: 'Best RVG Sensors to buy in 2024 and Importance of RVG in Clinic Practice',
  },
  {
    icon: Icons.orderBanner,
    name: 'Dr. Neha Tiwari',
    title: 'Dentistry Tips',
    text: 'Best RVG Sensors to buy in 2024 and Importance of RVG in Clinic Practice',
  },
  {
    icon: Icons.rewardBanner,
    name: 'Dr. Neha Tiwari',
    title: 'Dentistry Tips',
    text: 'Best RVG Sensors to buy in 2024 and Importance of RVG in Clinic Practice',
  },
];

export const buttons = [
  {
    data: 'News',
    id: 1,
  },
  {
    data: 'Shorts',
    id: 2,
  },
  {
    data: 'Magazine',
    id: 3,
  },
  {
    data: 'Blogs',
    id: 4,
  },
  {
    data: 'Events',
    id: 4,
  },
];
export const months = [
  {label: 'January', value: '1'},
  {label: 'February', value: '2'},
  {label: 'March', value: '3'},
  {label: 'April', value: '4'},
  {label: 'May', value: '5'},
  {label: 'June', value: '6'},
  {label: 'July', value: '7'},
  {label: 'August', value: '8'},
  {label: 'September', value: '9'},
  {label: 'October', value: '10'},
  {label: 'November', value: '11'},
  {label: 'December', value: '12'},
];

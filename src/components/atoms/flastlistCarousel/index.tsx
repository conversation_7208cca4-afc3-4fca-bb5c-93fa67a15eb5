import React, {useCallback, useState, memo, useMemo, useRef, useEffect} from 'react';
import {
  Dimensions,
  Linking,
  View,
  ViewProps,
  Animated,
  FlatList,
} from 'react-native';
import {useSharedValue, withSpring} from 'react-native-reanimated';
import {
  FlatListCarouselCardItem,
  ReanimatedCarouselPagination,
  Spacer,
} from 'components/atoms';
import styles from './style';
import {navigate} from 'utils/navigationRef';
import {AnalyticsEvents} from 'components/organisms';
import {Sizes} from 'common';
import {useSelector} from 'react-redux';
import getImageUrl from 'utils/imageUrlHelper';
import FastImage from 'react-native-fast-image';

const {width: PAGE_WIDTH} = Dimensions.get('window');

// Define types for calculateHeight function
type HeightType =
  | 'small'
  | 'medium'
  | 'large'
  | 'extraLarge'
  | 'doubleExtraLarge';

// Define types for carousel items
type FlatListCarouselItem = {
  mobile_image?: string;
  mobile_img?: string;
  file?: string;
  thumbnail_url?: string;
  media?: {
    mobile_image?: string;
  };
  landing_page_entity?: {
    category_id?: string;
    product_id?: string;
    link?: string;
    url?: string;
    [key: string]: any;
  };
  relative?: boolean;
  link?: string;
  app_url?: string;
};

interface FlatListCarouselProps {
  autoplayInterval?: number;
  data: FlatListCarouselItem[];
  autoPlay?: boolean;
  height?: HeightType;
  pagingEnabled?: boolean;
  snapEnabled?: boolean;
  loop?: boolean;
  pagination?: boolean;
  fullScreen?: boolean;
  urlResolverKey?: string;
  showDotsOnImage?: boolean;
  onChangeIndex?: (index: number) => void;
  paginationSpacer?: boolean;
  onCardPress?: (item: FlatListCarouselItem) => void;
  style?: ViewProps['style'];
  customColor?: string;
  carouselCardItemStyle?: ViewProps['style'];
  carouselStyle?: ViewProps['style'];
  renderItem?: ({
    item,
    index,
  }: {
    item: FlatListCarouselItem;
    index: number;
  }) => React.ReactElement;
  mode?: string;
  carouselHeight?: number;
  ref?: React.Ref<FlatList>;
  paginationType?: string;
  bannerImgStyle?: ViewProps['style'];
  screenName?: string;
  sectionType?: string;
  paginationSize?: boolean;
}

const FlatListCarousel: React.FC<FlatListCarouselProps> = ({
  autoplayInterval = 3000,
  data = [],
  autoPlay = false,
  height = 'medium',
  pagingEnabled = true,
  snapEnabled = false,
  loop = false,
  pagination = false,
  fullScreen = false,
  urlResolverKey,
  showDotsOnImage,
  onChangeIndex,
  paginationSpacer = false,
  onCardPress,
  style,
  customColor,
  carouselCardItemStyle,
  carouselStyle,
  renderItem,
  mode,
  carouselHeight,
  ref,
  paginationType,
  bannerImgStyle,
  screenName = 'Unknown',
  sectionType = 'Main Banner',
  paginationSize = false,
  ...props
}) => {
  const progressValue = useSharedValue(0);
  const scrollX = useMemo(() => new Animated.Value(0), []);
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  const isLoggedIn = useSelector((state: any) => state.app.isLoggedIn);
  const userInfo = useSelector((state: any) => state.app.userInfo);

  const itemWidth = useMemo(() => {
    if (mode === 'parallax') {
      return PAGE_WIDTH * 0.98;
    }
    return PAGE_WIDTH;
  }, []);

  const containerStyle = useMemo(() => [styles.container, style], [style]);

  const computedCarouselStyle = useMemo(
    () => [
      {
        width: PAGE_WIDTH,
      },
      carouselStyle,
    ],
    [carouselStyle],
  );

  const carouselContainerStyle = useMemo(
    () =>
      carouselHeight
        ? [styles.carouselContainer, {top: carouselHeight - Sizes.mx}]
        : null,
    [carouselHeight],
  );

  const navigateRoute = useCallback(
    item => {
      const updatedItem = {...item, PageName: screenName, Section: sectionType};
      switch (item?.title) {
        case 'News':
          AnalyticsEvents(
            'NEWS',
            'News tab viewed',
            {
              source: 'Saved Content Screen',
            },
            userInfo,
            isLoggedIn,
          );
          break;
        case 'Blogs':
          AnalyticsEvents(
            'BLOGS',
            'Blogs tab viewed',
            {
              source: 'Saved Content Screen',
            },
            userInfo,
            isLoggedIn,
          );
          break;
        case 'Shorts':
          AnalyticsEvents(
            'SHORTS',
            'Shorts tab viewed',
            {
              source: 'Saved Content Screen',
            },
            userInfo,
            isLoggedIn,
          );
          break;
        case 'MAGAZINE':
          AnalyticsEvents(
            'Saved Content',
            'Magazine tab viewed',
            {source: 'Saved Content Screen'},
            userInfo,
            isLoggedIn,
          );
          break;
        default:
          break;
      }

      AnalyticsEvents(
        'BANNER_CLICKED',
        'Banner Clicked',
        updatedItem,
        userInfo,
        isLoggedIn,
      );

      if (onCardPress) {
        return onCardPress(item);
      } else if (item?.landing_page_entity?.category_id) {
        return navigate('CategoryDetail', {
          categoryId: item?.landing_page_entity.category_id,
        });
      } else if (item?.landing_page_entity?.product_id) {
        return navigate('ProductDetail', {
          productId: item?.landing_page_entity.product_id,
        });
      } else if (urlResolverKey) {
        return navigate('UrlResolver', {
          urlKey: item?.landing_page_entity?.[urlResolverKey],
        });
      } else if (item.relative) {
        if (item?.link && item.link.includes('sale')) {
          return navigate('AllBrands', {
            saleUrl: item.link,
          });
        } else if (item?.app_url && item.app_url.includes('sale')) {
          return navigate('AllBrands', {
            saleUrl: item.app_url,
          });
        } else if (item.link && item.link.includes('membership')) {
          return navigate('MyMembership');
        } else if (item?.link && item?.link !== '#') {
          return navigate('UrlResolver', {urlKey: item.link});
        } else {
          navigate('UrlResolver', {urlKey: item.link});
          return false;
        }
      } else if (
        item?.landing_page_entity?.link ||
        item?.landing_page_entity?.url
      ) {
        let link =
          item?.landing_page_entity?.link ?? item?.landing_page_entity?.url;
        return Linking.openURL(link ?? '#');
      }
    },
    [onCardPress, urlResolverKey, userInfo, isLoggedIn],
  );

  const onScrollEnd = useCallback(
    (event: any) => {
      const offsetX = event.nativeEvent.contentOffset.x;
      const index = Math.round(offsetX / itemWidth);
  
      if (!loopedData || loopedData.length === 0) return;
  
      // Clamp index between 0 and loopedData.length - 1
      const clampedIndex = Math.max(0, Math.min(index, loopedData.length - 1));
  
      if (loop) {
        if (clampedIndex === 0) {
          if (loopedData.length > 2) {
            setTimeout(() => {
              flatListRef?.current?.scrollToIndex({ index: loopedData.length - 2, animated: false });
              setCurrentIndex(loopedData.length - 2);
            }, 50);
          }
          return;
        }
    
        if (clampedIndex === loopedData.length - 1) {
          if (loopedData.length > 2) {
            setTimeout(() => {
              flatListRef?.current?.scrollToIndex({ index: 1, animated: false });
              setCurrentIndex(1);
            }, 50);
          }
          return;
        }
      }
  
      setCurrentIndex(clampedIndex);
      progressValue.value = withSpring(clampedIndex, { damping: 10, stiffness: 90 });
      if (onChangeIndex) onChangeIndex(clampedIndex);
    },
    [loop, itemWidth, loopedData, onChangeIndex, progressValue],
  );
  
  // Use a ref for data.length to avoid recreating functions when only the length changes
  const dataLengthRef = useRef(data.length);
  dataLengthRef.current = data.length;

  // Pre-memoize the card style to avoid creating a new array on each render
  const cardItemStyle = useMemo(
    () => [
      styles.itemContainer,
      carouselCardItemStyle,
      {
        width: itemWidth,
      },
    ],
    [carouselCardItemStyle, itemWidth],
  );

  // Memoize the card prop to avoid inline boolean calculation
  const cardProp = useMemo(() => !fullScreen, [fullScreen]);

  // Create a single handler factory that returns memoized item handlers
  const getItemPressHandler = useCallback(
    (item: CarouselItem) => {
      // This function would ideally be memoized per item using a cache or itemId key
      // For this implementation, we'll return a function that calls navigateRoute
      return () => navigateRoute(item);
    },
    [navigateRoute],
  );

  // Memoize item render function with minimal dependencies
  const memoizedRenderItem = useCallback(
    ({item, index}: {item: CarouselItem; index: number}) => {
      return (
        <FlatListCarouselCardItem
          style={cardItemStyle}
          bannerImgStyle={bannerImgStyle}
          index={index}
          card={cardProp}
          item={item}
          onPress={getItemPressHandler(item)}
          mode={mode}
        />
      );
    },
    [cardItemStyle, bannerImgStyle, cardProp, mode, getItemPressHandler],
  );

  useEffect(() => {
    if (!autoPlay || !loop || loopedData.length <= 1) return;
  
    const interval = setInterval(() => {
      let nextIndex = currentIndex + 1;
  
      if (nextIndex >= loopedData.length) {
        nextIndex = 1;
      }
  
      if (flatListRef.current) {
        flatListRef.current.scrollToIndex({
          index: nextIndex,
          animated: true,
        });
      }
      setCurrentIndex(nextIndex);
  
      if (nextIndex === loopedData.length - 1 && loopedData.length > 2) {
        setTimeout(() => {
          if (flatListRef.current) {
            flatListRef.current.scrollToIndex({
              index: 1,
              animated: false,
            });
          }
          setCurrentIndex(1);
        }, 300);
      }
    }, autoplayInterval);
  
    return () => clearInterval(interval);
  }, [autoPlay, autoplayInterval, loop, currentIndex, loopedData]);
  

const loopedData = useMemo(() => {
  if (data.length > 1) {
    return [
      data[data.length - 1], // clone last → first
      ...data,
      data[0],               // clone first → last
    ];
  }
  return data;
}, [data]);

  useEffect(() => {
    if (loop && flatListRef?.current && loopedData.length > 1) {
      setTimeout(() => {
        flatListRef?.current?.scrollToIndex({ index: 1, animated: false });
      }, 10); // slight delay to ensure FlatList is ready
    }
  }, [loop, loopedData]);

  useMemo(() => {
    data.forEach(item => {
      const imageUrl = getImageUrl(
        item?.mobile_image ||
          item?.mobile_img ||
          item?.file ||
          item?.thumbnail_url ||
          item?.media?.mobile_image ||
          'https://via.placeholder.com/500',
      );
      FastImage.preload([{uri: imageUrl, priority: FastImage.priority.high}]);
    });
  }, [data]);

  const keyExtractor = useCallback(
    (_: FlatListCarouselItem, index: number) => `carousel-item-${index}`,
    [],
  );

  // Handle scroll events to update animation values
  const handleScroll = Animated.event(
    [{nativeEvent: {contentOffset: {x: scrollX}}}],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        const offsetX = event.nativeEvent.contentOffset.x;
        const newIndex = Math.round(offsetX / itemWidth);
        if (newIndex !== currentIndex) {
          setCurrentIndex(newIndex);
          progressValue.value = newIndex;
        }
      },
    },
  );

  const flatListContentStyle = useMemo(() => {
    if (mode === 'parallax') {
      return [
        styles.flatListContent,
        {gap: Sizes.xm, paddingHorizontal: Sizes.xm},
      ];
    }
    return styles.flatListContent;
  }, [mode]);

  return (
    <View style={containerStyle}>
      <FlatList
        ref={flatListRef}
        style={computedCarouselStyle}
        contentContainerStyle={flatListContentStyle}
        data={loopedData}
        renderItem={renderItem || memoizedRenderItem}
        horizontal
        pagingEnabled={pagingEnabled}
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={onScrollEnd}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyExtractor={keyExtractor}
        decelerationRate={snapEnabled ? 'fast' : 'normal'}
        removeClippedSubviews={false}
        maxToRenderPerBatch={5}
        windowSize={5}
        initialNumToRender={3}
        bounces={false}
        getItemLayout={(_, index) => ({
          length: itemWidth,
          offset: itemWidth * index,
          index: Math.min(index, loopedData.length - 1),
        })}
        {...props}
      />
      {pagination ? (
        <View style={carouselContainerStyle}>
          {paginationSpacer && <Spacer size="xm" />}
          <View style={showDotsOnImage ? styles.paginationContainer : {}}>
            <ReanimatedCarouselPagination
              activeIndex={
                loop
                  ? currentIndex === 0
                    ? data.length - 1
                    : currentIndex === loopedData.length - 1
                    ? 0
                    : currentIndex - 1
                  : currentIndex
              }
              dotColor={customColor as any}
              dotsLength={data.length}
              paginationType={
                (paginationType || 'normal') as 'normal' | 'capsule'
              }
              progress={progressValue.value}
              paginationSize={paginationSize}
            />
          </View>
        </View>
      ) : null}
    </View>
  );
};

export default memo(FlatListCarousel);
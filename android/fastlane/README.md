fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### deploy_internal

```sh
[bundle exec] fastlane deploy_internal
```



### deploy_production

```sh
[bundle exec] fastlane deploy_production
```



### deploy_production_staging

```sh
[bundle exec] fastlane deploy_production_staging
```



### deploy_staging

```sh
[bundle exec] fastlane deploy_staging
```



### deploy_dev

```sh
[bundle exec] fastlane deploy_dev
```



### DevAPK

```sh
[bundle exec] fastlane DevAPK
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).

import {Sizes} from 'common';
import {Dimensions, StyleSheet} from 'react-native';
const {width: SCREEN_WIDTH} = Dimensions.get('window');
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    scrollBarContainer: {
      position: 'absolute',
      marginTop: Sizes.xm,
      bottom: Sizes.l, // Adjust the bottom positioning of the scroll bar
      left: SCREEN_WIDTH / 2.4, // Start the scroll bar 1/4th from the left for centering
      right: SCREEN_WIDTH / 2.4, // End the scroll bar 1/4th from the right for centering
      height: Sizes.s, // Slimmer scroll bar
      backgroundColor: colors.grey2, // Gray background for inactive section
      borderRadius: Sizes.s, // Rounded corners for the whole scroll bar
    },
    scrollBar: {
      height: '100%',
      // backgroundColor: colors.blue3, // Active scroll color
      borderRadius: Sizes.xms, // Rounded corners for the active scroll bar
    },
  });
export default styles;

import React, {useState} from 'react';
import {
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  PermissionsAndroid,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import Modal from 'react-native-modal';
import {ImageIcon, Label, Spacer, Separator} from 'components/atoms';
import {Button} from 'components/molecules';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {formatBytes} from 'utils/utils';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {useMemo} from 'react';
import { debugLog } from 'utils/debugLog';

type Props = {
  visible: boolean;
  onClose: () => void;
  catalogueData: CatalogueData;
  setCatalougePress: (values: CatalogueData) => void;
  clearSelectedCatalouge: () => void;
  saveCatalouge: () => void;
};

const CatalogueModal = ({
  visible,
  onClose,
  catalogueData,
  setCatalougePress,
  clearSelectedCatalouge,
  saveCatalouge,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [url, setUrl] = useState('');

  const requestCameraPermission = async (type: string) => {
    try {
      const grantedCamera = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CAMERA,
        {
          title: 'App Camera Permission',
          message: 'App needs access to your camera ',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      const grantedStorage = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'App Camera Permission',
          message: 'App needs access to your camera ',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      if (
        grantedCamera === PermissionsAndroid.RESULTS.GRANTED &&
        grantedStorage === PermissionsAndroid.RESULTS.GRANTED
      ) {
        debugLog('You can use the camera');
        handleDocumentSelection(type);
      } else {
        debugLog('Camera permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  };

  const handleDocumentSelection = async (type: string) => {
    let response;
    let options = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      saveToPhotos: true,
    };

    if (type === 'camera') {
      await launchCamera(options, data => {
        if (data.didCancel) {
          debugLog('User cancelled image picker');
        } else if (data.errorCode) {
          debugLog('ImagePicker Error: ', data.errorMessage);
        } else {
          response = data?.assets?.[0];
          setCatalougePress(response);
        }
      }).catch(err => {
        debugLog(err);
      });
    } else {
      const result = await launchImageLibrary({mediaType: 'photo'});
      response = result?.assets?.[0];
      setCatalougePress(response);
    }
  };

  return (
    <>
      <Modal
        onBackButtonPress={onClose}
        isVisible={visible}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={75}
        animationOutTiming={75}
        backdropOpacity={0.01}
        style={styles.modalStyle}>
        <View style={styles.modalBackground}>
          <View style={styles.modalContainer}>
            <View style={styles.modalView}>
              <Label
                size="l"
                color="categoryTitle"
                fontFamily="Medium"
                lineHeight="xxl"
                text={t('sellOnDentalkart.uploadPhotos')}
              />
              <TouchableOpacity
                testID='touchabelOpacityCatalogueModalOnClose'
                onPress={() => onClose()}>
                <ImageIcon icon="cross" size="xl" />
              </TouchableOpacity>
            </View>
            <Spacer size="l" />
            <TouchableOpacity
            testID='touchabelOpacityCatalogueModalAttachments'
              onPress={() =>
                Alert.alert(
                  t('orderReturn.attachmentTitle'),
                  t('orderReturn.attachmentDes'),
                  [
                    {
                      text: t('orderReturn.browseFiles'),
                      onPress: () => handleDocumentSelection('gallary'),
                    },
                    {
                      text: t('orderReturn.useCamera'),
                      onPress: () => requestCameraPermission('camera'),
                    },
                  ],
                  {cancelable: false},
                )
              }>
              <View style={styles.modalUploadView}>
                <FastImage
                  source={Icons.pickImage}
                  resizeMode="contain"
                  style={styles.pickImage}
                />
                <View style={styles.modalSpace}>
                  <Label
                    size="m"
                    fontFamily="Regular"
                    color="black1"
                    align="center"
                    text={t('sellOnDentalkart.selectImgMsg') + ' '}
                    style={styles.label}>
                    <Label
                      align="center"
                      text={t('sellOnDentalkart.browse')}
                      color="borderColor1"
                      size="m"
                      fontFamily="Medium"
                    />
                  </Label>
                  <Label
                    size="m"
                    color="text2"
                    align="center"
                    fontFamily="Medium"
                    text={t('sellOnDentalkart.supportImg')}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <Spacer size="l" />
            <View style={styles.lineView}>
              <Separator style={styles.flex} color="text6" />
              <Label
                style={styles.separatorText}
                text={t('returnPolicy.or')}
                size="m"
                fontFamily="Medium"
                color="text7"
              />
              <Separator style={styles.flex} color="text6" />
            </View>
            {catalogueData ? (
              <View style={styles.selectedImage}>
                <View style={styles.rowDirection}>
                  <FastImage
                    resizeMode="stretch"
                    style={styles.catalogueModalImage}
                    source={{uri: catalogueData?.uri}}
                  />
                  <Spacer type="Horizontal" size="sx" />
                  <View style={styles.selectedImageData}>
                    <View style={styles.imageSubView}>
                      <Label
                        size="xms"
                        fontFamily="Medium"
                        lineHeight="l"
                        color="categoryTitle8"
                        text={catalogueData?.fileName}
                      />
                      <Spacer type="Horizontal" size="s" />
                      <ImageIcon icon="rightCheckIcon" size="xl" />
                    </View>
                    <Spacer size="sx" />
                    <Label
                      color="text5"
                      size="xm"
                      fontFamily="Regular"
                      text={formatBytes(catalogueData?.fileSize)}
                    />
                  </View>
                </View>
              </View>
            ) : null}

            <Spacer size="l" />
            <View>
              <Label
                text={t('sellOnDentalkart.importUrl')}
                color="categoryTitle"
                size="mx"
                fontFamily="Medium"
              />
              <Spacer size="xm" />
              <View style={styles.urlView}>
                <TextInput testID="txtCatalougueAddFileURL"
                  style={styles.urlInput}
                  onChangeText={text => setUrl(text)}
                  placeholder={t('sellOnDentalkart.urlPlace')}
                  allowFontScaling={false}
                />
                <Button testID="btnCatalougueUpload"
                  style={styles.uploadView}
                  onPress={() => setCatalougePress({uri: url})}
                  labelSize="xms"
                  labelColor="grey"
                  text={t('sellOnDentalkart.upload')}
                  labelStyle={styles.btnStyle}
                />
              </View>
            </View>
            {catalogueData ? (
              <>
                <Spacer size="l" />
                <View style={styles.modalView}>
                  <View style={styles.selectedImageButton}>
                    <ImageIcon
                      icon="helpCenterIcon"
                      size="mx"
                      tintColor="grey"
                    />
                    <Spacer type="Horizontal" size="s" />
                    <Label
                      size="m"
                      color="grey"
                      text={t('sellOnDentalkart.helpCenter')}
                      fontFamily="Medium"
                    />
                  </View>
                  <View style={styles.rowDirection}>
                    <Button
                      onPress={clearSelectedCatalouge}
                      style={styles.cancelButton}
                      labelColor="categoryTitle"
                      text={t('buttons.cancel')}
                      labelStyle={styles.btnStyle}
                    />
                    <Spacer type="Horizontal" size="xm" />
                    <Button
                      onPress={saveCatalouge}
                      labelColor="whiteColor"
                      style={styles.doneButton}
                      text={t('buttons.done')}
                      labelStyle={styles.btnStyle}
                    />
                  </View>
                </View>
              </>
            ) : null}
          </View>
        </View>
      </Modal>
    </>
  );
};

export default CatalogueModal;

import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background2,
    },
    excludeSubView: {
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      marginRight: Sizes.xms,
    },
    fullScreen: {
      width: '100%',
      height: '100%',
      paddingVertical: Sizes.x6l,
    },
    weightHeading: {
      backgroundColor: colors.cartCountColor,
      padding: Sizes.xm,
      width: '50%',
      alignItems: 'center',
    },
    saprater: {
      height: '100%',
      width: Sizes.x,
      backgroundColor: colors.lightGray,
    },
    sapraterWhite: {
      height: '100%',
      width: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    addressText: {
      color: colors.textLight,
    },
    buttons: {
      flexDirection: 'row',
      alignSelf: 'flex-end',
    },
    promotionCart: {
      backgroundColor: colors.bubbles,
      padding: Sizes.xms,
    },
    baseCountryVIew: {
      backgroundColor: colors.green4,
      padding: Sizes.xms,
      borderRadius: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: Sizes.xm,
    },
    bgLightGray: {
      backgroundColor: colors.lightGray,
    },
    background: {
      backgroundColor: colors.background,
    },
    forgetView: {
      paddingLeft: Sizes.m,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: Sizes.x3l,
    },
    onCheckoutView: {
      padding: Sizes.mx,
      paddingHorizontal: Sizes.x8l,
      borderRadius: Sizes.xms,
    },
    disView: {
      position: 'absolute',
      zIndex: Sizes.xs,
      alignSelf: 'center',
    },
    addressView: {
      padding: Sizes.m,
      flexDirection: 'row',
    },
    addressSubView: {
      alignItems: 'center',
      flexDirection: 'row',
      flex: Sizes.x,
    },
    flexView: {
      flex: Sizes.x,
    },
    fontWeight: {
      fontWeight: '500',
    },
    flexRow: {
      flexDirection: 'row',
      gap: Sizes.xs,
      alignItems: 'center',
    },
    flexRowWidth: {
      flexDirection: 'row',
      gap: Sizes.xs,
      flexWrap: 'wrap',
      flexShrink: 1,
      minWidth: 0,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    alignCenter: {
      alignItems: 'center',
    },
    discountView: {
      paddingHorizontal: Sizes.xl,
    },
    offerLinear: {
      borderRadius: Sizes.xms,
      alignItems: 'center',
    },
    wishListLinear: {
      paddingVertical: Sizes.xm,
    },
    fRow: {
      flexDirection: 'row',
    },
    freeProductImage: {
      height: Sizes.xx,
      width: Sizes.xsl,
    },
    productListView: {
      paddingTop: Sizes.m,
    },
    buttonView: {
      flexDirection: 'row',
      paddingLeft: Sizes.m,
    },
    bottomBtnView: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.whiteColor,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: Sizes.s,
      paddingVertical: Sizes.xm,
      paddingHorizontal: Sizes.l,
    },
    cartSection: {
      paddingHorizontal: Sizes.xm,
    },
    headerSection: {
      backgroundColor: colors.background,
      paddingBottom: Sizes.s,
    },
    btnStyle: {
      height: Sizes.x4l + Sizes.x,
      paddingVertical: 0,
    },
    btnTxt: {
      marginBottom: -Sizes.xs,
    },
    titleStyle: {
      fontSize: Sizes.xx,
      fontFamily: Fonts.SemiBold,
    },
    desStyle: {
      fontSize: Sizes.l,
      fontFamily: Fonts.SemiBold,
    },
    infoStyle: {
      marginTop: -Sizes.x,
    },
  });

export default styles;

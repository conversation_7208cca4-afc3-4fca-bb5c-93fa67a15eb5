import React, {useCallback, useEffect, useMemo, useState, useRef} from 'react';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../routes';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import stylesWithOutColor from './style';
import RazorpayCheckout from 'react-native-razorpay';
import {RouteProp, useTheme} from '@react-navigation/native';
import {
  Address,
  CartItem,
  CartRemoveModal,
  CheckBox,
  ImageIcon,
  Label,
  ListView,
  Radio,
  Spacer,
  WithBackground,
  WithGradient,
  CartPositionStatus,
  CartPriceSection,
  PaymentSupportCart,
  CouponListModal,
  SuccessModal,
  RegistrationModal,
  CouponCart,
  DeliveryInfoModal,
  CartEmptyView,
} from 'components/atoms';
import {
  View,
  TouchableOpacity,
  ScrollView,
  TextInput,
  BackHandler,
  Platform,
} from 'react-native';
import {t} from 'i18next';
import {AnalyticsEvents, TermsAndConditionModal} from 'components/organisms';
import {useDispatch, useSelector} from 'react-redux';
import {
  resetCart,
  setCartCount,
  setLoading,
  addToWishListThunk,
} from 'app-redux-store/slice/appSlice';
import {
  showErrorMessage,
  showInfoMessage,
  showSuccessMessage,
} from 'utils/show_messages';
import localStorage from 'utils/localStorage';
import {getCartTotalWithKeys} from 'utils/calculatePrizes';
import DashedLine from 'react-native-dashed-line';
import getImageUrl from 'utils/imageUrlHelper';
import {
  applyDiscountElement,
  checkServiceAvailability,
  removeCouponFromCart,
  setCartAddress,
  getCouponList,
  getApplicableRewardsPoints,
  getCustomerRegistration,
  addNewRegId,
  deleteRegId,
  updateRegId,
  cartPaymentMethods,
  updateCartItem,
  deleteCartItem,
} from 'services/cart';
import {createOrder, fetchPayment} from 'services/checkout';
import {ImagePath} from 'config/apiEndpoint';
import {CartLoader} from 'skeletonLoader';
import {
  handleErrMsg,
  debounce,
  getCurrentLocation,
  billingAdd,
  onCheckGst,
} from 'utils/utils';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {checkoutPositionStatus} from 'staticData';
import {navigate} from 'utils/navigationRef';
import ErrorHandler from 'utils/ErrorHandler';
import {debugLog} from 'utils/debugLog';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import ReactMoE from 'react-native-moengage';
import {setCustomUserId} from '@microsoft/react-native-clarity';
import base64 from 'react-native-base64';
import {trackEvent} from 'components/organisms/appEventsLogger/FacebookEventTracker';
import {Settings} from 'react-native-fbsdk-next';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';
import {defaultPinCode} from 'config/environment';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'PaymentPage'>;
};

const PaymentPageScene = ({navigation, route}: Props) => {
  const TAG = 'PaymentPageScreen';
  const {selectedAddress, cart, paymentMethods, buyNow, title} = route.params;
  const scrollViewRef = useRef(null);
  const targetViewPosition = useRef(null);
  const {colors} = useTheme();
  const dispatch = useDispatch();
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const cartId = useSelector((state: RootState) => state.app.cartId);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const baseCountryData = useSelector(
    (state: RootState) => state.app.baseCountryData,
  );

  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [currentCartId, setCurrentCartId] = useState(null);
  const [couponCode, setCouponCode] = useState('');
  const [regIdValue, setRegIdValue] = useState('');
  const [showAddNewInput, setShowAddNewInput] = useState(false);
  const [regIdEditValue, setRegIdEditValue] = useState('');
  const [showEditInput, setShowEditInput] = useState(false);
  const [regIdEdit, setRegIdEdit] = useState('');
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [regModalVisible, setRegModalVisible] = useState(false);
  const [tncVisible, setTncVisible] = useState(false);
  const [orderSuccessModel, setOrderSuccessModel] = useState(false);
  const [tncSelected, setTncSelected] = useState(true);
  const [cartData, setCartData] = useState<Cart | null>(null);
  const [maxApplicablePoints, setMaxApplicablePoints] = useState(0);
  const [appliedRewardPoints, setAppliedRewardPoints] = useState(0);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState([]);
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [selectedRegId, setSelectedRegId] = useState('');
  const [regIdConfig, setRegIdConfig] = useState(null);
  const [regIds, setRegIds] = useState<Registration[]>([]);
  const [selectedRegIdForEdit, setSelectedRegIdForEdit] = useState<
    number | null
  >(null);
  const [appliedCoupon, setAppliedCoupon] = useState(false);
  const [couponList, setCouponList] = useState([]);
  const [deliveryStatusData, setDeliveryStatusData] =
    useState<DeliveryInfo | null>(null);
  const [priceDetails, setPriceDetails] = useState({});
  const [promotionByCart, setPromotionByCart] = useState('');
  const [selectedAddressData, setSelectedAddressData] =
    useState<CustomerAddressV2 | null>(null);
  const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
  const [returnInfoModel, setReturnInfoModel] = useState(false);
  const [infoIcon, setInfoIcon] = useState('');
  const [isCouponOpen, setCouponOpen] = useState(true);
  const [productModal, setProductModal] = useState(false);
  const [removeCartModal, setRemoveCartModal] = useState(false);
  const [removeCart, setRemoveCart] = useState<Item | null>(null);
  const [openKeyboard, setOpenKeyboard] = useState(false);
  const [successModel, setSuccessModel] = useState(false);
  const [coords, setCoords] = useState();
  const [billingAddress, setBillingAddress] = useState();
  const prevUserInfoRef = useRef(null);
  const rewardPointStatusRef = useRef<boolean>(false);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, [!!appliedRewardPoints]);

  const backAction = async () => {
    if (appliedRewardPoints) {
      await applyDiscount(
        'reward_points',
        appliedRewardPoints ? 0 : maxApplicablePoints,
      );
    }
    navigation?.goBack();
    return true;
  };
  const checkStatusVariables = useMemo(() => {
    let isCodStatus = true;
    let totalWeightCount = 0;
    cartData?.items.map((item: CartItemInterface) => {
      totalWeightCount += Number(item?.product?.weight) ?? 0;
      if (isCodStatus && !item?.product?.is_cod) {
        isCodStatus = false;
      }
    });
    return {
      cart_weight: parseInt(totalWeightCount * 1000),
      is_cod_eligible: isCodStatus,
      cart_amount: parseFloat(
        cartData?.pricing_details?.grand_total?.amount?.value || 0,
      ),
    };
  }, [cartData?.items, cartData?.pricing_details]);

  const getApplicableRewardsPointsData = useCallback(
    async (check = false) => {
      dispatch(setLoading(check ? false : true));
      const {data, status} = await getApplicableRewardsPoints(
        currentCartId,
        buyNow,
      );
      if (status) {
        setMaxApplicablePoints(data?.max_applied_points);
        if (rewardPointStatusRef?.current) {
          applyDiscount('reward_points', data?.max_applied_points);
        }
      }
      dispatch(setLoading(false));
    },
    [currentCartId, buyNow],
  );

  const toggleRewardPointStatus = useCallback(() => {
    rewardPointStatusRef.current = !rewardPointStatusRef.current;
  }, []);

  const getCouponListData = useCallback(async () => {
    dispatch(setLoading(true));
    const {data, status} = await getCouponList(currentCartId, buyNow);
    if (status) {
      const groupedData = data?.data?.reduce((acc, coupon) => {
        const expiryDate = coupon.expiry_date;
        if (!acc[expiryDate]) {
          acc[expiryDate] = [];
        }
        acc[expiryDate].push(coupon);
        return acc;
      }, {});

      if (groupedData) {
        const result = Object.keys(groupedData).map(date => {
          return {
            title: `Valid till ${new Date(date).toLocaleDateString('en-US', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            })}`,
            data: groupedData[date],
          };
        });
        setCouponList(result);
      }
    }
    dispatch(setLoading(false));
  }, [currentCartId, buyNow]);

  const getCustomerRegistrationData = useCallback(async () => {
    dispatch(setLoading(true));
    const {data} = await getCustomerRegistration();
    const registrationIds = data?.registrations;
    const defaultReg = registrationIds?.find(reg => reg?.is_default === true);
    if (!selectedRegId) {
      setSelectedRegId(defaultReg);
      setRegIdEditValue(defaultReg?.registration_no);
    }
    setRegIds(registrationIds);
    setRegIdConfig(data?.config);
    dispatch(setLoading(false));
  }, []);

  const onUpdateRegisterId = (text: string) => {
    setRegIdEditValue(text);
    debouncedUpdate(selectedRegId?.id, text);
  };

  const debouncedUpdate = useCallback(
    debounce((id: number, text: number) => {
      onUpdateRegId(id, text);
    }, 500),
    [],
  );

  const onAddNewRegId = useCallback(async () => {
    dispatch(setLoading(true));
    const {data, status} = await addNewRegId({registration_no: regIdValue});
    if (status) {
      regIds.push(data);
      showSuccessMessage(t('registrationId.addReg'));
      setRegIdValue('');
      setShowAddNewInput(false);
    } else {
      const errorMessages = Object.values(data).flat().join(', ');
      showErrorMessage(errorMessages);
    }
    dispatch(setLoading(false));
  }, [regIdValue]);

  const onDeleteRegId = useCallback(
    async regIdValue => {
      dispatch(setLoading(true));
      const {data, status} = await deleteRegId(regIdValue);
      if (status) {
        showSuccessMessage(t('registrationId.deleteReg'));
        getCustomerRegistrationData();
      } else {
        showErrorMessage(data);
      }
      dispatch(setLoading(false));
    },
    [regIdValue],
  );

  const onUpdateRegId = async (regId, regNo) => {
    dispatch(setLoading(true));
    const {data, status} = await updateRegId(regId, {
      registration_no: regNo,
    });
    if (status) {
      getCustomerRegistrationData();
      setSelectedRegIdForEdit(null);
      setRegIdEdit('');
      setShowEditInput(false);
      showSuccessMessage(t('registrationId.updateReg'));
    } else {
      showErrorMessage(handleErrMsg(data));
    }
    dispatch(setLoading(false));
  };

  const onUpdateDefaultRegId = useCallback(async (regId, regNo) => {
    dispatch(setLoading(true));
    const {data, status} = await updateRegId(regId, {
      is_default: true,
      registration_no: regNo,
    });
    if (status) {
      getCustomerRegistrationData();
    } else {
      showErrorMessage(data?.message);
    }
    dispatch(setLoading(false));
  }, []);

  const navigateToOrderSuccess = async (payload, cod = false) => {
    setOrderSuccessModel(cod ? false : true);
    dispatch(resetCart());
    setTimeout(
      () => {
        setOrderSuccessModel(false);
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'ThankYouPage',
              params: {order_id: payload?.order_id},
            },
          ],
        });
      },
      cod ? 0 : 5000,
    );
  };

  const confirmPayment = async payload => {
    const {data, status} = await fetchPayment(payload);
    dispatch(setLoading(false));
    if (data && data?.order_id) {
      dispatch(resetCart());
      if (data && data?.status === 'success') {
        setOrderSuccessModel(true);
        const paymentData = {
          ...data,
          ...cartData,
          ...selectedAddressData,
        };
        const addressParts = [
          paymentData?.addresses?.street?.join(', '),
          paymentData?.addresses?.city,
          paymentData?.addresses?.region?.label,
          paymentData?.addresses?.postcode,
          paymentData?.addresses?.country?.name,
        ].filter(Boolean);
        const paymentSuccessData = {
          'Order Id': paymentData?.order_id ?? 'Unknown Order ID',
          'Payment Mode': 'razorpay',
          Currency: 'INR',
          Coupon: paymentData?.coupon_code?.code ?? 'No Coupon',
          'Total Amount': paymentData?.amount / 100 ?? 0, // Convert from paisa to INR
          Brand: paymentData?.items?.map(
            item => item?.product?.manufacturer ?? 'Unknown Brand',
          ),
          Category: paymentData?.items?.map(item => 'Dental'), // No category provided, assuming Dental
          SKU: paymentData?.items?.map(
            item => item?.product?.sku ?? 'Unknown SKU',
          ),
          'Parent SKU': paymentData?.items?.map(
            item => item?.product?.sku ?? 'Unknown Parent SKU',
          ),
          'Product Name': paymentData?.items?.map(
            item => item?.product?.name ?? 'Unknown Product',
          ),
          'Total Price':
            paymentData?.pricing_details?.item_total_selling_price?.amount
              ?.value ?? 0,
          'Product Details': paymentData?.items ?? [], // Full item details
          'Total Quantity': paymentData?.total_quantity ?? 1,
          'Total MRP':
            paymentData?.pricing_details?.item_total_regular_price?.amount
              ?.value ?? 0,
          'Order Date': new Date().toISOString(), // Assuming the current date as Order Date
          'No. Of Products': paymentData?.items?.length ?? 1,
          'Reward Earned': paymentData?.rewards?.total_coins ?? 0,
          'Coupon Value':
            paymentData?.pricing_details?.discounts?.find(
              d => d?.code === 'coupon',
            )?.amount?.value ?? 0,
          // Address: paymentData.data?.region?.region ?? 'Unknown Address',
          Address:
            addressParts.length > 0
              ? addressParts.join(', ')
              : 'Unknown Address',
          'Reward Used': paymentData?.rewards?.total_coins_used ?? 0, // Assuming a key for reward usage, update if needed
          'Product Price': paymentData?.items?.map(
            item => item?.product?.price?.minimalPrice?.amount?.value ?? 0,
          ),
          'Total Amount Saved':
            paymentData?.pricing_details?.total_savings?.amount?.value ?? 0,
          'Product Id': paymentData?.items?.map(
            item => item?.product?.id ?? 'Unknown Product ID',
          ),
          'Discount Amount': paymentData?.pricing_details?.discounts?.reduce(
            (sum, d) => sum + (d?.amount?.value ?? 0),
            0,
          ),
          'Shipping Charge':
            paymentData?.pricing_details?.shipping_charges?.amount?.value ?? 0,
          'Tax Amount': paymentData?.pricing_details?.applied_taxes?.reduce(
            (sum, tax) => sum + Number(tax?.amount?.value ?? 0),
            0,
          ),
        };

        // appsFlyer Online Payment OrderData
        const appsFlyerOrderData = {
          estimatedRevenue: paymentSuccessData['Total Amount'],
          totalAmount: paymentSuccessData['Total Amount'],
          sku: paymentSuccessData['SKU']?.join(', '),
          productIds: paymentSuccessData['Product Id']?.join(', '),
          productNames: paymentSuccessData['Product Name']?.join(', '),
          currency: paymentSuccessData['Currency'],
          productQuantities: paymentSuccessData['No. Of Products'],
          orderId: paymentSuccessData['Order Id'],
          receiptId: paymentSuccessData['Order Id'],
        };
        appsFlyerEvent('CheckoutCompleted', appsFlyerOrderData);

        AnalyticsEvents(
          'CHECKOUT_COMPLETED',
          'Checkout Completed',
          paymentSuccessData,
          userInfo,
          isLoggedIn,
        );
        Settings.setAutoLogAppEventsEnabled(true);
        Settings.setAdvertiserIDCollectionEnabled(true);
        trackEvent('PURCHASE', {
          currency: paymentSuccessData?.Currency,
          amount: paymentSuccessData['Total Amount'],
          params: {
            fb_content_type: 'product',
            // fb_content_id: '',
            // num_items: 1,
          },
        });
        setTimeout(() => {
          setOrderSuccessModel(false);
          navigation.reset({
            index: 0,
            routes: [
              {
                name: 'ThankYouPage',
                params: {order_id: data?.order_id},
              },
            ],
          });
        }, 5000);
        // navigateToOrderSuccess(data);
      } else {
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'Tab',
              state: {
                routes: [
                  {
                    name: 'Home',
                  },
                ],
              },
            },
            {
              name: 'OrderDetail',
              params: {
                order_id: data?.order_id,
                retryPayment: false,
                can_cancel: true,
              },
            },
          ],
        });
      }
    }
  };

  const initiateRazorPay = (payload, rzpData) => {
    const options = {
      ...rzpData,
      description: '',
      image: ImagePath.dentalkart,
      name: 'Dentalkart',
      prefill: {
        email: userInfo.email,
        contact: selectedAddressData ? selectedAddressData.telephone : '',
        name: `${userInfo.firstname} ${userInfo.lastname}`,
      },
      theme: {color: ''},
    };
    try {
      RazorpayCheckout.open(options)
        .then(response => {
          // handle success
          dispatch(setLoading(true));
          const rzpres = {
            rzp_payment_id: response.razorpay_payment_id,
            rzp_order_id: response.razorpay_order_id,
          };
          const RzpPayload = {...payload, ...rzpres};
          setTimeout(() => {
            confirmPayment(RzpPayload);
          }, 5000);
        })
        .catch(error => {
          dispatch(setLoading(true));
          setTimeout(() => {
            confirmPayment(payload);
          }, 5000);
          let data = {
            Reason: error.description,
            'Payment mode': 'razorpay',
            'Total Amount': Number(cartData?.price_de?.['grand_total']?.value),
            name: String(
              cartData?.items.map(item => item?.product?.name).join(','),
            ),
            id: String(payload?.order_id),
            quantity: String(cartData?.total_quantity),
            currency: String(cartData?.prices?.grand_total?.currency),
          };
          const paymentFailData = {
            ...cartData,
            reason: data?.Reason,
            orderId: data?.id,
          };
          AnalyticsEvents(
            'PAYMENT_FAILURE',
            'Payment Failure',
            paymentFailData,
            userInfo,
            isLoggedIn,
          );
          showErrorMessage(
            `${
              error.error.description && error.error.description !== 'undefined'
                ? error.error.description
                : t('payment.payFail')
            }. ${t('payment.tryAgain')}`,
          );
        });
    } catch (err) {
      debugLog(err, 'err***');
    }
  };

  const onCheckoutPress = useCallback(async () => {
    if (!selectedAddressData?.id) {
      if (userInfo?.addresses?.length == 0) {
        openMapsModal(false, selectedAddressData);
      } else {
        showErrorMessage(t('validations.selectAddress'));
      }
      return;
    }
    // if (regIdConfig?.registration_no_required === true && !selectedRegId?.id) {
    //   showErrorMessage(t('validations.selectRegId'));
    //   return;
    // }
    if (currentCartId && selectedMethod?.code) {
      const variables = {
        cart_id: currentCartId,
        payment_method: selectedMethod?.code,
        custom_attributes: [
          {
            attribute_code: 'registration_no',
            value: selectedRegId?.registration_no ?? '',
          },
        ],
        is_buy_now: buyNow,
      };
      dispatch(setLoading(true));
      const checkBilling =
        billingAddress?.gst_id?.trim()?.length > 0 ||
        selectedAddressData?.vat_id?.trim()?.length > 0
          ? await onCheckGst(
              selectedAddressData?.vat_id
                ? selectedAddressData?.vat_id
                : billingAddress?.gst_id,
              selectedAddressData?.vat_id
                ? selectedAddressData?.region?.region
                : billingAddress?.region,
            )
          : {status: true};
      if (!checkBilling?.status) {
        dispatch(setLoading(false));
        openMapsModal(false, {...selectedAddressData, skipLocation: true});
        return;
      }
      const {data} = await createOrder(variables);
      dispatch(setLoading(false));
      if (data && data?.order_number) {
        const payload = {order_id: data?.order_number};
        if (selectedMethod?.code === 'razorpay' && data?.reference_number) {
          const rzpData = {
            amount: data?.amount,
            order_id: data?.reference_number,
            key: data?.merchant_id,
            currency: data?.currency,
          };
          setTimeout(
            () => {
              initiateRazorPay(payload, rzpData);
            },
            Platform.OS === 'ios' ? 100 : 0,
          );
        } else {
          // AnalyticsEvents('CHECKOUT_COMPLETED', 'Checkout Completed', {
          //   ...cartData,
          //   selectedMethod: selectedMethod?.code,
          // });
          const paymentData = {
            ...data,
            ...cartData,
            ...selectedAddressData,
          };
          const addressParts = [
            paymentData?.addresses?.street?.join(', '),
            paymentData?.addresses?.city,
            paymentData?.addresses?.region?.label,
            paymentData?.addresses?.postcode,
            paymentData?.addresses?.country?.name,
          ].filter(Boolean);
          const paymentSuccessData = {
            'Order Id': paymentData?.order_number ?? 'Unknown Order ID',
            'Payment Mode': 'cashondelivery',
            Currency: 'INR',
            Coupon: paymentData?.coupon_code?.code ?? 'No Coupon',
            'Total Amount': paymentData?.amount / 100 ?? 0, // Convert from paisa to INR
            Brand: paymentData?.items?.map(
              item => item?.product?.manufacturer ?? 'Unknown Brand',
            ),
            Category: paymentData?.items?.map(item => 'Dental'), // No category provided, assuming Dental
            SKU: paymentData?.items?.map(
              item => item?.product?.sku ?? 'Unknown SKU',
            ),
            'Parent SKU': paymentData?.items?.map(
              item => item?.product?.sku ?? 'Unknown Parent SKU',
            ),
            'Product Name': paymentData?.items?.map(
              item => item?.product?.name ?? 'Unknown Product',
            ),
            'Total Price':
              paymentData?.pricing_details?.item_total_selling_price?.amount
                ?.value ?? 0,
            'Product Details': paymentData?.items ?? [], // Full item details
            'Total Quantity': paymentData?.total_quantity ?? 1,
            'Total MRP':
              paymentData?.pricing_details?.item_total_regular_price?.amount
                ?.value ?? 0,
            'Order Date': new Date().toISOString(), // Assuming the current date as Order Date
            'No. Of Products': paymentData?.items?.length ?? 1,
            'Reward Earned': paymentData?.rewards?.total_coins ?? 0,
            'Coupon Value':
              paymentData?.pricing_details?.discounts?.find(
                d => d?.code === 'coupon',
              )?.amount?.value ?? 0,
            // Address: paymentData.data?.region?.region ?? 'Unknown Address',
            Address:
              addressParts.length > 0
                ? addressParts.join(', ')
                : 'Unknown Address',
            'Reward Used': paymentData?.rewards?.total_coins_used ?? 0, // Assuming a key for reward usage, update if needed
            'Product Price': paymentData?.items?.map(
              item => item?.product?.price?.minimalPrice?.amount?.value ?? 0,
            ),
            'Total Amount Saved':
              paymentData?.pricing_details?.total_savings?.amount?.value ?? 0,
            'Product Id': paymentData?.items?.map(
              item => item?.product?.id ?? 'Unknown Product ID',
            ),
            'Discount Amount': paymentData?.pricing_details?.discounts?.reduce(
              (sum, d) => sum + (d?.amount?.value ?? 0),
              0,
            ),
            'Shipping Charge':
              paymentData?.pricing_details?.shipping_charges?.amount?.value ??
              0,
            'Tax Amount': paymentData?.pricing_details?.applied_taxes?.reduce(
              (sum, tax) => sum + Number(tax?.amount?.value ?? 0),
              0,
            ),
          };

          // appsFlyer COD Payment OrderData
          const appsFlyerOrderData = {
            estimatedRevenue: paymentSuccessData['Total Amount'],
            totalAmount: paymentSuccessData['Total Amount'],
            sku: paymentSuccessData['SKU']?.join(', '),
            productIds: paymentSuccessData['Product Id']?.join(', '),
            productNames: paymentSuccessData['Product Name']?.join(', '),
            currency: paymentSuccessData['Currency'],
            productQuantities: paymentSuccessData['No. Of Products'],
            orderId: paymentSuccessData['Order Id'],
            receiptId: paymentSuccessData['Order Id'],
          };
          console.log('appsFlyerOrderData', appsFlyerOrderData);

          appsFlyerEvent('CheckoutCompleted', appsFlyerOrderData);

          AnalyticsEvents(
            'CHECKOUT_COMPLETED',
            'Checkout Completed',
            paymentSuccessData,
            userInfo,
            isLoggedIn,
          );
          Settings.setAutoLogAppEventsEnabled(true);
          Settings.setAdvertiserIDCollectionEnabled(true);
          trackEvent('PURCHASE', {
            currency: paymentSuccessData?.Currency,
            amount: paymentSuccessData['Total Amount'],
            params: {
              fb_content_type: 'product',
              // fb_content_id: '',
              // num_items: 1,
            },
          });
          navigateToOrderSuccess(
            payload,
            selectedMethod?.code === 'cashondelivery',
          );
        }
        await localStorage.remove('buyNowCartId');
      } else {
        showErrorMessage(data?.message || data?.response?.message);
        if (!buyNow) {
          navigation.navigate('Cart');
        }
      }
    } else if (!selectedMethod?.code) {
      showErrorMessage(t('validations.noPayment'));
    }
  }, [
    currentCartId,
    regIdConfig?.registration_no_required,
    selectedMethod,
    selectedRegId?.id,
    selectedRegId?.registration_no,
    billingAddress,
  ]);

  const statusVariables = (updatedCartData: Cart) => {
    let isCodStatus = true;
    let totalWeightCount = 0;
    updatedCartData?.items.map((item: Item) => {
      totalWeightCount += item?.product?.weight;
      if (isCodStatus && !item?.product?.is_cod) {
        isCodStatus = false;
      }
    });
    return {
      cart_weight: parseInt(totalWeightCount * 1000),
      is_cod_eligible: isCodStatus,
      cart_amount: parseFloat(
        updatedCartData?.pricing_details?.grand_total?.amount?.value || 0,
      ),
    };
  };

  const updateAddressDetails = async (selectAddress, updatedCartData) => {
    if (selectAddress?.country_id && selectAddress?.postcode) {
      const {data} = await checkServiceAvailability({
        postcode: Number(selectAddress?.postcode),
        country_code: selectAddress?.country_id,
        product_ids:
          updatedCartData?.items?.map(item => item?.product?.id) || [],
        cart_data: statusVariables(updatedCartData),
      });
      if (data?.response) {
        setDeliveryStatusData(data?.response);
      }
      setAddressOnCart(selectAddress);
    } else if (!isLoggedIn) {
      setAddressOnCart({});
    }
  };

  const deleteCartProduct = async (item: Item, hide?: boolean) => {
    dispatch(setLoading(hide ? false : true));
    const itemId = item?.item_id;
    const {data, status} = await deleteCartItem(currentCartId, itemId, buyNow);
    if (data?.cart && status) {
      dispatch(setCartCount(data?.cart?.items?.length));
      setCartData(data?.cart);
      setPriceDetails(getCartTotalWithKeys(data?.cart, isLoggedIn));
      showSuccessMessage(t('cart.removeCart'));
      updateAddressDetails(selectedAddressData, data?.cart);
      if (data?.cart?.items?.length > 0) {
        getApplicableRewardsPointsData(true);
      }
    } else {
      showErrorMessage(data?.message);
    }
    dispatch(setLoading(false));
  };

  const updateCount = useCallback(
    async (
      count: number,
      itemId: number,
      updatedCartId: number,
      item: Item,
    ) => {
      dispatch(setLoading(true));
      const {data} = await updateCartItem(updatedCartId, {
        cart_items: [{cart_item_id: itemId, quantity: count}],
        buy_now: buyNow,
      });
      dispatch(setLoading(false));
      if (data?.cart) {
        setCartData(data?.cart);
        setPriceDetails(getCartTotalWithKeys(data?.cart, isLoggedIn));
        // AnalyticsEvents('CART_UPDATED', 'Cart Updated', {data: data?.cart});
        dispatch(setLoading(false));
        showSuccessMessage(t('cart.updatedCart'));
        updateAddressDetails(selectedAddressData, data?.cart);
      } else {
        setCartData(null);
        dispatch(setLoading(false));
        const {data} = await updateCartItem(updatedCartId, {
          cart_items: [{cart_item_id: itemId, quantity: item?.quantity}],
          buy_now: buyNow,
        });
        setCartData(data?.cart);
        setPriceDetails(getCartTotalWithKeys(data?.cart, isLoggedIn));
        // AnalyticsEvents('CART_UPDATED', 'Cart Updated', {data: data?.cart});
        showSuccessMessage(t('cart.updatedCart'));
        dispatch(setLoading(false));
      }
      getApplicableRewardsPointsData(true);
    },
    [buyNow, selectedAddressData],
  );

  const estimateDate = useCallback(
    item => {
      if (!item?.product?.id || !deliveryStatusData?.delivery_info?.length) {
        return '';
      }
      const deliveryMessage =
        deliveryStatusData.delivery_info[0]?.delivery_days?.find(
          deliveryItem => deliveryItem?.product_id === item.product.id,
        )?.message;
      return deliveryMessage || '';
    },
    [deliveryStatusData],
  );

  const renderCartItem = useCallback(
    ({item, index}: {item: Item; index: number}) => {
      return (
        <CartItem
          item={item}
          index={index}
          buyNow={buyNow}
          navigation={navigation}
          selectedAddress={selectedAddress}
          deliveryInfoMsg={estimateDate(item)}
          removeCart={(data: Item) => {
            setRemoveCartModal(true);
            setRemoveCart(data);
          }}
          updateCart={(count: number, cartId: string) => {
            updateCount(Number(count), cartId, currentCartId, item);
          }}
          infoClick={() => {
            setReturnInfoModel(true);
            setInfoIcon('deliveryInfo');
          }}
        />
      );
    },
    [currentCartId, deliveryStatusData, buyNow],
  );

  const setAddressOnCart = useCallback(
    async address => {
      if (currentCartId) {
        const billingAddInfo = await billingAdd(
          address?.id,
          userInfo?.addresses || [],
        );
        setBillingAddress(billingAddInfo);
        const {data} = await setCartAddress(currentCartId, {
          shipping_addresses: [
            isLoggedIn
              ? {
                  address: {
                    country_code: address?.country_code ?? address?.country_id,
                    country_id: address?.country_id ?? 'IN',
                    region_code: address?.region?.region_code,
                    street: address.street,
                    postcode: address.postcode,
                    city: address.city,
                    firstname: address.firstname,
                    lastname: address.lastname,
                    telephone: address.telephone,
                    gst_id: address?.vat_id,
                    same_as_billing: billingAddInfo ? false : true,
                    alternate_mobile:
                      address?.custom_attributes?.find(
                        attribute =>
                          attribute?.attribute_code === 'alternate_telephone',
                      ).value ?? null,
                    region_id: address?.region?.region_id ?? 0,
                    region: address?.region?.region,
                    longitude: address?.longitude,
                    latitude: address?.latitude,
                    customer_street_2: address?.customer_street_2,
                    map_address: address?.map_address,
                  },
                  customer_address_id: address?.id,
                }
              : {
                  address: {country_code: 'IN'},
                },
          ],
          buy_now: buyNow,
          billing_address: isLoggedIn ? billingAddInfo : null,
        });
        setCartData(data?.cart);
      }
    },
    [currentCartId],
  );

  const updateCartAddress = useCallback(async () => {
    if (selectedAddressData?.country_id && selectedAddressData?.postcode) {
      dispatch(setLoading(true));
      const {data} = await checkServiceAvailability({
        postcode: Number(selectedAddressData?.postcode),
        country_code: selectedAddressData?.country_id,
        product_ids: cartData?.items?.map(item => item?.product?.id) || [],
        cart_data: {
          cart_amount: checkStatusVariables?.cart_amount ?? 0,
          cart_weight: checkStatusVariables?.cart_weight ?? 0,
          is_cod_eligible: checkStatusVariables?.is_cod_eligible,
        },
      });
      dispatch(setLoading(false));
      if (data?.response) {
        setDeliveryStatusData(data?.response);
      }
      setAddressOnCart(selectedAddressData);
    }
  }, [cartData, selectedAddressData]);

  const getPaymentMethods = useCallback(async () => {
    if (selectedAddressData?.country_id && selectedAddressData?.postcode) {
      // dispatch(setLoading(true));
      const {data, status} = await cartPaymentMethods(
        selectedAddressData?.country_id,
        selectedAddressData?.postcode,
        checkStatusVariables?.cart_amount,
        checkStatusVariables?.cart_weight,
        checkStatusVariables?.is_cod_eligible,
      );
      dispatch(setLoading(false));
      if (status) {
        setAvailablePaymentMethods(data?.response?.payment_methods);
        let paymentMethod = data?.response?.payment_methods?.[0];
        if (selectedMethod) {
          const selectedPaymentOption = data?.response?.payment_methods?.filter(
            item => item?.code === selectedMethod?.code,
          );
          if (selectedPaymentOption?.length > 0) {
            paymentMethod = selectedPaymentOption[0];
          }
        }
        setSelectedMethod(paymentMethod);
      } else {
        showErrorMessage(data?.message);
      }
    }
  }, [selectedAddressData, checkStatusVariables]);

  const applyDiscount = useCallback(
    async (key: 'reward_points' | 'coupon_code', value: number | string) => {
      dispatch(setLoading(true));
      const {data, status} = await applyDiscountElement(currentCartId, {
        buy_now: buyNow,
        [key]: value,
      });
      dispatch(setLoading(false));
      if (status) {
        setCartData(data?.cart);
        const rewardPoints = data?.cart?.pricing_details?.discounts?.find(
          discount => discount?.code === 'reward',
        )?.amount?.value;
        if (key === 'reward_points') {
          showSuccessMessage(
            t(
              key === 'reward_points'
                ? rewardPoints
                  ? 'cart.RewardApplied'
                  : 'cart.RewardRemoved'
                : 'cart.CouponApplied',
            ),
          );
        } else {
          setSuccessModel(true);
          setTimeout(() => {
            setSuccessModel(false);
          }, 4000);
        }
        updateAddressDetails(selectedAddressData, data?.cart);
      } else {
        if (data?.devMessage === 'Invalid coupon code') {
          setCouponCode('');
        }
        showErrorMessage(data?.devMessage);
      }
    },
    [currentCartId],
  );

  const removeCoupon = useCallback(async () => {
    dispatch(setLoading(true));
    const {data, status} = await removeCouponFromCart(currentCartId, {
      buy_now: buyNow,
    });
    dispatch(setLoading(false));
    if (status) {
      setCouponCode('');
      setCartData(data?.cart);
      showSuccessMessage(t('cart.CouponRemoved'));
      updateAddressDetails(selectedAddressData, data?.cart);
    } else {
      showErrorMessage(data?.message);
    }
  }, [currentCartId]);

  const selectExistingAddress = useCallback(async () => {
    const deliveryAddress = await localStorage.get('delivery_address');
    const defaultAddress = userInfo.addresses.find(address =>
      deliveryAddress?.id
        ? address?.id === deliveryAddress?.id
        : address.default_shipping,
    );
    if (defaultAddress) {
      setSelectedAddressData(defaultAddress);
    } else if (userInfo?.addresses?.length > 0) {
      setSelectedAddressData(userInfo.addresses[0]);
    } else {
      setSelectedAddressData({country_id: 'IN', postcode: ''});
    }
  }, []);

  useEffect(() => {
    if (cart) {
      setCartData(cart);
    }
    if (paymentMethods) {
      setAvailablePaymentMethods(paymentMethods);
      setSelectedMethod(paymentMethods?.[0]);
    }
    if (selectedAddress) {
      setSelectedAddressData(selectedAddress);
    }
    if (userInfo) {
      // Check if we need to update ReactMoE
      const shouldUpdate =
        !prevUserInfoRef.current ||
        prevUserInfoRef.current.id !== userInfo.id ||
        prevUserInfoRef.current.email !== userInfo.email ||
        prevUserInfoRef.current.mobile !== userInfo.mobile;

      if (shouldUpdate) {
        // Only update ReactMoE when user info actually changes
        let eventID;
        if (userInfo?.email) {
          eventID = base64.encode(userInfo.email.toString());
        } else {
          eventID = base64.encode(
            userInfo.mobile?.toString() + '@dentalkart.user',
          );
        }

        ReactMoE.setUserUniqueID(eventID);
        setCustomUserId(eventID)
          .then(() => {
            debugLog('Clarity User ID set', eventID);
          })
          .catch(e => {
            debugLog('Clarity User ID error', e);
          });
        prevUserInfoRef.current = {...userInfo};
      }
    }
  }, []);

  useEffect(() => {
    updateCartAddress();
    getPaymentMethods();
  }, [selectedAddressData]);

  useEffect(() => {
    selectExistingAddress();
    getCurrentLocation(position => {
      setCoords(position?.coords);
    }, false);
  }, []);

  useEffect(() => {
    setAppliedCoupon(!!cartData?.coupon_code?.code);
    setPriceDetails(getCartTotalWithKeys(cartData, isLoggedIn));
    const rewardPoints = cartData?.pricing_details?.discounts?.find(
      discount => discount?.code === 'reward',
    )?.amount?.value;
    setAppliedRewardPoints(Number(rewardPoints) || 0);
    getPaymentMethods();
    if (cartData && !buyNow) {
      const resData = {
        ...cartData,
        eta: deliveryStatusData?.delivery_info?.[0]?.max_delivery_days_text,
      };
      AnalyticsEvents('CHECKOUT_VIEW', 'Checkout Viewed', resData, isLoggedIn);
      if (userInfo) {
        // Check if we need to update ReactMoE
        const shouldUpdate =
          !prevUserInfoRef.current ||
          prevUserInfoRef.current.id !== userInfo.id ||
          prevUserInfoRef.current.email !== userInfo.email ||
          prevUserInfoRef.current.mobile !== userInfo.mobile;

        if (shouldUpdate) {
          // Only update ReactMoE when user info actually changes
          let eventID;
          if (userInfo?.email) {
            eventID = base64.encode(userInfo.email.toString());
          } else {
            eventID = base64.encode(
              userInfo.mobile?.toString() + '@dentalkart.user',
            );
          }
          ReactMoE.setUserUniqueID(eventID);
          setCustomUserId(eventID)
            .then(() => {
              debugLog('Clarity User ID set', eventID);
            })
            .catch(e => {
              debugLog('Clarity User ID error', e);
            });
          prevUserInfoRef.current = {...userInfo};
        }
      }
    }
  }, [cartData]);

  useEffect(() => {
    setCurrentCartId(buyNow ? cart?.cart_id : cartId);
    if (buyNow) {
      const resData = {
        ...cart,
        eta: deliveryStatusData?.delivery_info?.[0]?.max_delivery_days_text,
      };
      localStorage.set('buyNowCartId', cart?.cart_id);
      AnalyticsEvents('BUY_NOW', 'Buy Now', resData, userInfo, isLoggedIn);
    }
  }, []);

  const onPressWishlist = (item: Item) => {
    setRemoveCartModal(false);
    if (isLoggedIn) {
      const productId = item?.parent_id ? item?.parent_id : item?.product?.id;
      dispatch(
        addToWishListThunk({
          productId,
          callBack: () => deleteCartProduct(item, true),
        }),
      );
    } else {
      showInfoMessage(t('toastMassages.wishlistLogin'));
      const prevRoutes = navigation.getState().routes;
      navigation.navigate('Login', {
        nextRouterState: {
          index: 0,
          routes: prevRoutes,
        },
      });
    }
  };

  useEffect(() => {
    if (currentCartId) {
      getCouponListData();
      getApplicableRewardsPointsData();
      getCustomerRegistrationData();
    }
  }, [currentCartId]);

  const scrollToCouponView = useCallback(() => {
    if (scrollViewRef.current && targetViewPosition.current !== null) {
      scrollViewRef?.current?.scrollTo({
        y: targetViewPosition.current,
        animated: true,
      });
      setOpenKeyboard(true);
      setTimeout(() => {
        setOpenKeyboard(false);
      }, 1500);
    }
  }, [targetViewPosition]);

  const handleTermsClick = useCallback(() => {
    setTncVisible(!tncVisible);
  }, [tncVisible]);

  const handleCouponClick = useCallback(() => {
    scrollToCouponView();
  }, [scrollToCouponView]);

  const handleCheckBoxClick = useCallback(() => {
    setTncSelected(!tncSelected);
  }, [tncSelected]);

  const handleReturnInfoModalClick = useCallback(() => {
    setReturnInfoModel(!returnInfoModel);
  }, [returnInfoModel]);

  const handleDeliveryInfoModalClick = useCallback(() => {
    setReturnInfoModel(true);
    setInfoIcon('deliveryInfo');
  }, []);

  const checkCODProduct = useMemo(() => {
    if (cartData?.items?.length > 0) {
      const codList = cartData?.items?.filter(item => !item?.product?.is_cod);
      return codList?.length > 0 ? true : false;
    }
    return false;
  }, [cartData]);

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const openMapsModal = useCallback(
    (cartCheck = false, addressObj) => {
      const deliverAddress = {
        ...addressObj,
        formatted_address: addressObj?.map_address,
      };
      const obj = {
        cardType: 'checkoutPage',
        location: true,
        coords: coords,
        goBack: () =>
          setIsAddressModalOpen(userInfo?.addresses?.length > 0 ? true : false),
        onChangeSelectAddress: updateData => onDeliverAddress(updateData),
      };
      if (addressObj?.skipLocation === true) {
        navigation.navigate('ManageAddress', {
          ...obj,
          address: deliverAddress,
        });
      } else {
        navigation.navigate('MapLocation', {...obj, deliverAddress});
      }
    },
    [coords, userInfo, setIsAddressModalOpen, onDeliverAddress],
  );

  const onDeliverAddress = async data => {
    setIsAddressModalOpen(false);
    setSelectedAddressData(data);
    await localStorage.set('delivery_address', data);
  };

  const scrollToBottom = () => {
    scrollViewRef?.current?.scrollToEnd({
      animated: true,
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <Header
            backButton
            navigation={navigation}
            text={title ? title : t('checkOut.checkOut')}
            subTitle={
              cartData?.items?.length > 0
                ? `${cartData?.items?.length} ${t('orderReturn.items')}`
                : ''
            }
            useInsets
            onPressNavigation={async () => {
              if (appliedRewardPoints) {
                await applyDiscount(
                  'reward_points',
                  appliedRewardPoints ? 0 : maxApplicablePoints,
                );
              }
              if (buyNow) {
                navigation.goBack();
                navigation.navigate('Cart');
              } else {
                navigation?.goBack();
              }
            }}
          />
        </ErrorHandler>
        {cartData ? (
          !cartData?.items || cartData?.items?.length === 0 ? (
            <ErrorHandler
              componentName={`${TAG} CartEmptyView`}
              onErrorComponent={<View />}>
              <CartEmptyView navigation={navigation} />
            </ErrorHandler>
          ) : (
            <View style={styles.flex}>
              {/* {checkoutPositionStatus?.length > 0 && (
                <ErrorHandler
                  componentName={`${TAG} CartPositionStatus`}
                  onErrorComponent={<View />}>
                  <CartPositionStatus
                    cartPositionStatus={checkoutPositionStatus}
                  />
                </ErrorHandler>
              )} */}
              <ScrollView
                style={styles.subView}
                ref={scrollViewRef}
                keyboardShouldPersistTaps="always">
                <Spacer size="xm" />
                <View style={styles.checkoutSection}>
                  <ErrorHandler
                    componentName={`${TAG} Address`}
                    onErrorComponent={<View />}>
                    <Address
                      cardType="checkoutPage"
                      addressType="cart"
                      buyNow={buyNow}
                      addressTextStyle={{color: colors.textLight}}
                      addressData={userInfo?.addresses}
                      // onAddressOrPostcodeChange={checkDeliveryStatus}
                      deliveryStatusData={deliveryStatusData}
                      setSelectedAddress={setSelectedAddressData}
                      selectedAddress={selectedAddressData}
                      isAddressModalOpen={isAddressModalOpen}
                      useInsets={true}
                      setIsAddressModalOpen={setIsAddressModalOpen}
                      onPressAddNewAddress={() =>
                        navigation.navigate('ManageAddress', {
                          nextScreenName: 'PaymentPage',
                          nextScreenParams: {
                            selectedAddress: selectedAddress,
                            cart: cartData,
                            paymentMethods: availablePaymentMethods,
                          },
                        })
                      }
                      coords={coords}
                      openMapsModal={openMapsModal}
                    />
                  </ErrorHandler>
                  {!!promotionByCart && (
                    <View style={styles.promotionCartView}>
                      <Label
                        text={promotionByCart}
                        size="m"
                        fontFamily="Medium"
                        color="green"
                      />
                    </View>
                  )}
                  {selectedAddressData?.country_id ? (
                    <>
                      <Spacer size="xm" />
                      <View style={styles.promotionCartSubView}>
                        {selectedAddressData?.country_id === 'IN' ||
                        (!selectedAddressData?.country_id &&
                          baseCountryData?.country_id === 'IN') ? (
                          <>
                            <FastImage
                              source={Icons.freeProductGif}
                              style={styles.freeProductGif}
                            />
                            <Spacer size="xm" type="Horizontal" />
                            <Label
                              text={t('cart.freeDelivery')}
                              fontFamily="Medium"
                              style={styles.flex}
                              color="green2"
                              size="m"
                            />
                          </>
                        ) : (
                          <Label
                            text={t('cart.shippingCharges')}
                            fontFamily="Regular"
                            color="green2"
                            size="m"
                          />
                        )}
                      </View>
                    </>
                  ) : null}
                </View>
                <Spacer size="xm" />
                <View style={styles.deliveryNoteView}>
                  <View style={styles.deliveryView}>
                    <Label
                      text={t('checkOut.delivery')}
                      size="mx"
                      fontFamily="Medium"
                      weight="500"
                      color="text"
                    />

                    <TouchableOpacity
                      onPress={() => setProductModal(!productModal)}
                      style={styles.uparrowStyle}>
                      <ImageIcon
                        icon="arrowBottom"
                        size="xxl"
                        style={[
                          styles.downStyle,
                          productModal && styles.upDownStyle,
                        ]}
                      />
                    </TouchableOpacity>
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.underPrizeView}>
                    <View style={styles.underPrizeMainView}>
                      {cartData?.items?.length > 1 && (
                        <View style={styles.image1} />
                      )}
                      {cartData?.items?.length > 2 && (
                        <View style={styles.image2} />
                      )}
                      <TouchableOpacity
                        onPress={() => setProductModal(!productModal)}>
                        <WithBackground
                          source={{
                            uri: getImageUrl(
                              cartData?.items?.[0]?.product?.small_image,
                            ),
                          }}
                          imageStyle={styles.imageView}
                          style={[
                            styles.itemsView,
                            cartData?.items?.length > 1 && styles.image3,
                          ]}>
                          <View style={styles.itemsSubView}>
                            <Label
                              text={
                                cartData?.items?.length > 1
                                  ? '+' + (cartData?.items?.length - 1)
                                  : cartData?.items?.length
                              }
                              color="background"
                              size="mx"
                              fontFamily="Regular"
                            />
                          </View>
                        </WithBackground>
                      </TouchableOpacity>
                    </View>
                    <View style={styles.cartQtyView}>
                      <View style={styles.fRow}>
                        <View style={styles.cartQtySub}>
                          <Label
                            text={`${t('checkOut.noItems')} : ${
                              cartData?.items?.length
                            }`}
                            size="mx"
                            fontFamily="Medium"
                            color="categoryTitle"
                          />
                        </View>
                        <View style={styles.alignEnd}>
                          <Label
                            text={`${t('cart.totalQty')} : ${
                              cartData?.total_quantity
                            }`}
                            color="categoryTitle"
                            size="mx"
                            fontFamily="Medium"
                          />
                          <Label
                            text={`${t('checkOut.orderTotal')}: ${
                              cartData?.cart_currency?.currency_symbol
                            }${
                              cartData?.pricing_details?.grand_total?.amount
                                ?.value
                            }`}
                            color="newSunnyOrange"
                            size="mx"
                            fontFamily="SemiBold"
                          />
                        </View>
                      </View>

                      {deliveryStatusData?.delivery_info?.[0]
                        ?.max_delivery_days_text ? (
                        <View style={styles.deliveryStatusView}>
                          <ImageIcon size="xxl" icon="deliveryTruckIcon" />
                          <Spacer size="xm" type="Horizontal" />
                          <Label
                            fontFamily="Regular"
                            color="categoryTitle"
                            size="mx"
                            text={
                              deliveryStatusData?.delivery_info?.[0]
                                ?.max_delivery_days_text
                            }
                          />
                          {!deliveryStatusData?.checkcod?.[0]
                            ?.message_arr?.[0] ? (
                            <>
                              <Spacer size="s" type="Horizontal" />
                              <TouchableOpacity
                                onPress={handleDeliveryInfoModalClick}>
                                <ImageIcon size="xxl" icon="infoCircle" />
                              </TouchableOpacity>
                            </>
                          ) : null}
                        </View>
                      ) : null}
                    </View>
                  </View>
                </View>
                <Spacer size="xm" />
                {productModal === true ? (
                  <>
                    <View style={styles.productView}>
                      <ErrorHandler
                        componentName={`${TAG} ListView`}
                        onErrorComponent={<View />}>
                        <ListView
                          showsVerticalScrollIndicator={false}
                          keyExtractor={listViewKeyExtractor}
                          data={[...cartData?.items].sort((a, b) => {
                            if (a.is_free_product === b.is_free_product) {
                              return 0;
                            }
                            return a.is_free_product ? -1 : 1;
                          })}
                          renderItem={renderCartItem}
                          ItemSeparatorComponent={() => <Spacer size="xm" />}
                        />
                      </ErrorHandler>
                    </View>
                    <Spacer size="xm" />
                  </>
                ) : null}

                <View style={styles.registrationView}>
                  <View style={styles.registrationSubView}>
                    <Label
                      text={t('registrationId.registration')}
                      size="l"
                      fontFamily="Medium"
                      color="text"
                    />
                    <ErrorHandler
                      componentName={`${TAG} Button`}
                      onErrorComponent={<View />}>
                      <Button
                        onPress={() => setRegModalVisible(true)}
                        text={
                          selectedRegId
                            ? t('registrationId.change')
                            : t('registrationId.add')
                        }
                        size="zero-height"
                        type="bordered"
                        borderColor="sunnyOrange3"
                        radius="sx"
                        paddingHorizontal="xxl"
                        labelSize="mx"
                        labelColor="sunnyOrange3"
                        weight="400"
                        style={styles.registerBtn}
                      />
                    </ErrorHandler>
                  </View>
                  <Spacer size="xms" />
                  {selectedRegId ? (
                    <View style={styles.registerView}>
                      <Label
                        text={t('registrationId.ID') + ' -'}
                        size="l"
                        fontFamily="Medium"
                        color="text"
                      />
                      <ErrorHandler
                        componentName={`${TAG} TextInput`}
                        onErrorComponent={<View />}>
                        <TextInput
                          testID="txtRegisterationId"
                          value={regIdEditValue}
                          onChangeText={(text: string) =>
                            onUpdateRegisterId(text)
                          }
                          editable={false}
                          placeholder={t('registrationId.placeId')}
                          placeholderTextColor={colors.text}
                          style={styles.inputBox}
                          allowFontScaling={false}
                        />
                      </ErrorHandler>
                      {/* <Button
                      onPress={() => onUpdateRegId(selectedRegId?.id)}
                      text={
                        !!selectedRegId
                          ? t('registrationId.save')
                          : t('registrationId.add')
                      }
                      type="secondary"
                      size="zero-height"
                      borderColor="sunnyOrange3"
                      radius="sx"
                      paddingHorizontal="xxl"
                      labelSize="mx"
                      labelColor="white1"
                      weight="400"
                      style={styles.registerBtn}
                    /> */}
                      {/* <Label
                      size="mx"
                      fontFamily="Medium"
                      color="text"
                      text={`${t('registrationId.ID')}-${
                        selectedRegId?.registration_no
                      }`}
                    /> */}
                    </View>
                  ) : null}
                  <Spacer size="xm" />
                  <DashedLine
                    dashLength={4}
                    dashThickness={1.5}
                    dashColor={colors.grey2}
                  />
                  <Spacer size="xm" />
                  <Label
                    size="m"
                    fontFamily="Medium"
                    color="text2"
                    text={
                      regIdConfig?.message?.[
                        selectedRegId ? 'reg_info_msg' : 'no_reg_id_msg'
                      ]
                    }
                    style={styles.registerTxt}
                  />
                </View>
                <Spacer size="xm" />
                <View
                  onLayout={event => {
                    const {y} = event.nativeEvent.layout;
                    targetViewPosition.current = y;
                  }}>
                  <ErrorHandler
                    componentName={`${TAG} CouponCart`}
                    onErrorComponent={<View />}>
                    <CouponCart
                      showNo={true}
                      alwaysShow={true}
                      isCouponOpen={isCouponOpen}
                      couponCode={
                        appliedCoupon ? cartData?.coupon_code?.code : couponCode
                      }
                      couponList={couponList}
                      appliedCoupon={appliedCoupon}
                      savedAmount={`₹${
                        cartData?.pricing_details?.discounts?.find(
                          discount => discount?.code === 'coupon',
                        )?.amount?.value ?? 0
                      }`}
                      saveNote={`${t('checkOut.saved')} ₹ ${
                        cartData?.pricing_details?.discounts?.find(
                          discount => discount?.code === 'coupon',
                        )?.amount?.value ?? 0
                      }`}
                      openCoupon={() => setCouponOpen(!isCouponOpen)}
                      changeCouponCode={(code: string) => setCouponCode(code)}
                      onApplyDiscount={(code: string) => {
                        appliedCoupon
                          ? removeCoupon()
                          : applyDiscount('coupon_code', code);
                      }}
                      openCouponListModal={() =>
                        setIsCouponModalOpen(!isCouponModalOpen)
                      }
                      openKeyboard={openKeyboard}
                    />
                  </ErrorHandler>
                </View>
                {maxApplicablePoints > 0 && (
                  <>
                    <Spacer size="xm" />
                    <View style={styles.redeemView}>
                      <View style={styles.redeemSubView}>
                        <ImageIcon icon="coin" size="xl" />
                        <Spacer size="xms" type="Horizontal" />
                        <Label
                          text={t('checkOut.redeemCoin')}
                          size="mx"
                          fontFamily="Medium"
                          color="text"
                        />
                      </View>
                      <Spacer size="xm" />
                      <View style={styles.selectedView}>
                        <ErrorHandler
                          componentName={`${TAG} CheckBox`}
                          onErrorComponent={<View />}>
                          <CheckBox
                            selected={!!appliedRewardPoints}
                            fillColor="categoryTitle"
                            onValueChange={() => {
                              toggleRewardPointStatus();
                              applyDiscount(
                                'reward_points',
                                appliedRewardPoints ? 0 : maxApplicablePoints,
                              );
                            }}
                          />
                        </ErrorHandler>
                        <Spacer size="xm" type="Horizontal" />
                        <View style={styles.flex}>
                          <Label
                            text={t('checkOut.applyCoin') + ' '}
                            size="mx"
                            fontFamily="SemiBold"
                            color="categoryTitle">
                            <Label
                              text={`(${t('checkOut.applyNote')} ${
                                maxApplicablePoints ?? 0
                              } ${t('checkOut.applyNote1')})`}
                              size="mx"
                              fontFamily="Medium"
                              color="grey"
                            />
                          </Label>
                        </View>
                      </View>
                    </View>
                  </>
                )}
                <Spacer size="xm" />
                <View style={styles.paymentView}>
                  <Label
                    text={t('checkOut.paymentMethod')}
                    size="l"
                    fontFamily="Medium"
                    color="text"
                    weight="500"
                  />

                  {availablePaymentMethods.map((item, index) => {
                    if (item?.code === 'cashondelivery' && checkCODProduct) {
                      return <View />;
                    }
                    return (
                      <TouchableOpacity
                        key={index?.toString()}
                        style={styles.paymentSubView}
                        onPress={() => setSelectedMethod(item)}>
                        <Radio
                          selected={selectedMethod?.code === item?.code}
                          fillColor="text2"
                          onPress={() => setSelectedMethod(item)}
                        />
                        <Spacer size="m" type="Horizontal" />
                        <View style={styles.flex}>
                          <Label
                            text={item?.title}
                            size="m"
                            fontFamily="Medium"
                            color="text2"
                            weight="500"
                          />
                        </View>
                        {item?.code === 'razorpay' ? (
                          <>
                            <Spacer size="xm" type="Horizontal" />
                            <FastImage
                              style={styles.payCashView}
                              source={Icons.goCashlessGif}
                            />
                          </>
                        ) : (
                          <View />
                        )}
                      </TouchableOpacity>
                    );
                  })}
                  {availablePaymentMethods?.length === 0 ? (
                    <Label
                      text={t('checkOut.noService')}
                      size="m"
                      fontFamily="Regular"
                      color="text2"
                    />
                  ) : null}
                </View>
                <Spacer size="xm" />
                <ErrorHandler
                  componentName={`${TAG} CartPriceSection`}
                  onErrorComponent={<View />}>
                  <CartPriceSection
                    priceDetails={priceDetails}
                    cartData={cartData}
                    agreePolicy={true}
                    selectTerms={tncSelected}
                    termsClick={handleTermsClick}
                    onCheckBoxClick={handleCheckBoxClick}
                    onCouponClick={handleCouponClick}
                  />
                </ErrorHandler>
                <Spacer size="l" />
                <ErrorHandler
                  componentName={`${TAG} PaymentSupportCart`}
                  onErrorComponent={<View />}>
                  <PaymentSupportCart />
                </ErrorHandler>
              </ScrollView>
              <View
                style={[
                  styles.bottomBtnView,
                  {paddingBottom: insets?.bottom / 2 + 8},
                ]}>
                {!!cartData?.global_errors &&
                  !!selectedAddress?.country_code &&
                  selectedAddress?.country_code !== 'IN' && (
                    <>
                      <Spacer size="xm" />
                      <Label
                        text={cartData?.global_errors}
                        size="mx"
                        color="textError"
                        fontFamily="Regular"
                        align="center"
                      />
                      <Spacer size="xm" />
                    </>
                  )}
                <Spacer size="l" />
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() => scrollToBottom()}>
                  <Label
                    text={
                      priceDetails?.item_total_selling_price?.currency +
                      ' ' +
                      priceDetails?.item_total_selling_price?.regularValue
                    }
                    size="m"
                    weight="500"
                    color="darkGray"
                    textDecorationLine="line-through"
                  />
                  <View style={styles.fRow}>
                    <Label
                      text={`${t('otherText.total')} - ${
                        priceDetails?.grand_total?.currency
                      } ${priceDetails?.grand_total?.value}`}
                      size="mx"
                      weight="500"
                      color="text"
                    />
                    <Spacer size="sx" type="Horizontal" />
                    <ImageIcon
                      size="xl"
                      icon="infoCircle"
                      style={styles.infoStyle}
                    />
                  </View>
                </TouchableOpacity>
                <View style={styles.flex} />
                <TouchableOpacity
                  onPress={onCheckoutPress}
                  disabled={
                    (!!cartData?.global_errors &&
                      cartData?.global_errors?.length > 0) ||
                    !tncSelected ||
                    cartData?.items?.length === 0
                  }>
                  <WithGradient
                    gradientColors={
                      (!!cartData?.global_errors &&
                        cartData?.global_errors?.length > 0) ||
                      !tncSelected ||
                      cartData?.items?.length === 0
                        ? [colors.grey2, colors.grey2]
                        : [colors.coral, colors.persimmon]
                    }
                    gradientStyle={styles.onCheckoutView}>
                    <Label
                      text={t('checkOut.placeOrder')}
                      size="mx"
                      fontFamily="Medium"
                      color="background"
                      align="center"
                    />
                  </WithGradient>
                </TouchableOpacity>
              </View>
            </View>
          )
        ) : (
          <CartLoader />
        )}
      </View>

      {removeCartModal && (
        <ErrorHandler
          componentName={`${TAG} CartRemoveModal`}
          onErrorComponent={<View />}>
          <CartRemoveModal
            visible={removeCartModal}
            item={removeCart}
            onClose={() => setRemoveCartModal(!removeCartModal)}
            removeCart={() => {
              setRemoveCartModal(false);
              deleteCartProduct(removeCart);
            }}
            addWishlist={() => onPressWishlist(removeCart)}
          />
        </ErrorHandler>
      )}
      {isCouponModalOpen && (
        <ErrorHandler
          componentName={`${TAG} CouponListModal`}
          onErrorComponent={<View />}>
          <CouponListModal
            visible={isCouponModalOpen}
            couponList={couponList}
            onClose={() => setIsCouponModalOpen(!isCouponModalOpen)}
            couponItemClick={(item: couponItem) => {
              setCouponCode(item?.coupon_code);
              setIsCouponModalOpen(false);
              applyDiscount('coupon_code', item?.coupon_code);
            }}
          />
        </ErrorHandler>
      )}
      {orderSuccessModel && (
        <ErrorHandler
          componentName={`${TAG} SuccessModal`}
          onErrorComponent={<View />}>
          <SuccessModal
            visible={orderSuccessModel}
            type="orderSuccess"
            title={t('checkOut.paymentDone')}
            titleStyle={styles.successTitle}
            onClose={() => setOrderSuccessModel(!orderSuccessModel)}
          />
        </ErrorHandler>
      )}
      {regModalVisible && (
        <ErrorHandler
          componentName={`${TAG} RegistrationModal`}
          onErrorComponent={<View />}>
          <RegistrationModal
            visible={regModalVisible}
            selectedRegId={selectedRegId}
            regIds={regIds}
            selectedRegIdForEdit={selectedRegIdForEdit}
            regIdEditValue={regIdEditValue}
            showAddNewInput={showAddNewInput}
            regIdValue={regIdValue}
            setRegIdValue={setRegIdValue}
            showEditInput={showEditInput}
            regIdEdit={regIdEdit}
            setRegIdEdit={setRegIdEdit}
            onAddReg={() => {
              setShowAddNewInput(!showAddNewInput);
              setSelectedRegIdForEdit(null);
            }}
            setRegIdEditValue={setRegIdEditValue}
            onAddNewRegId={onAddNewRegId}
            updateRegId={(item: Registration, updateValue: string) => {
              onUpdateRegId(item?.id, updateValue);
            }}
            onSelectRadio={(item: Registration) => {
              onUpdateDefaultRegId(item?.id, item?.registration_no);
              setSelectedRegId(item);
              setRegIdEditValue(item?.registration_no);
            }}
            onEditRegId={(item: Registration) => {
              setShowEditInput(true);
              setShowAddNewInput(false);
              setSelectedRegIdForEdit(item?.id);
              setRegIdEdit(item?.registration_no);
            }}
            onDeleteRegId={(item: Registration) => onDeleteRegId(item?.id)}
            onClose={() => {
              setRegModalVisible(!regModalVisible);
              setShowEditInput(false);
              setShowAddNewInput(false);
              setSelectedRegIdForEdit(null);
            }}
          />
        </ErrorHandler>
      )}
      {tncVisible && (
        <ErrorHandler
          componentName={`${TAG} TermsAndConditionModal`}
          onErrorComponent={<View />}>
          <TermsAndConditionModal
            visible={tncVisible}
            onClose={() => setTncVisible(!tncVisible)}
          />
        </ErrorHandler>
      )}
      {returnInfoModel && (
        <ErrorHandler
          componentName={`${TAG} DeliveryInfoModal`}
          onErrorComponent={<View />}>
          <DeliveryInfoModal
            visible={returnInfoModel}
            onClose={handleReturnInfoModalClick}
            infoIcon={infoIcon}
            deliveryStatusData={deliveryStatusData}
          />
        </ErrorHandler>
      )}
      {successModel && (
        <ErrorHandler
          componentName={`${TAG} SuccessModal`}
          onErrorComponent={<View />}>
          <SuccessModal
            visible={successModel}
            title={`${t(
              'cart.yay',
            )} ${cartData?.coupon_code?.code?.toUpperCase()} ${t(
              'cart.applied',
            )}`}
            description={`${t('cart.youSaved')} ₹ ${
              cartData?.pricing_details?.discounts?.find(
                discount => discount?.code === 'coupon',
              )?.amount?.value ?? 0
            }\n${t('cart.thisOrder')}`}
            onClose={() => setSuccessModel(!successModel)}
            titleStyle={styles.titleStyle}
            descriptionStyle={styles.desStyle}
          />
        </ErrorHandler>
      )}
    </View>
  );
};

export default PaymentPageScene;

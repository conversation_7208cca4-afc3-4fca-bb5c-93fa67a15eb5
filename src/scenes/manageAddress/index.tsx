import React, {useCallback, useEffect, useState, useMemo, useRef} from 'react';
import {
  View,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import {<PERSON><PERSON>, Header, InputBox, TextInputBox} from 'components/molecules';
import {
  Spacer,
  Label,
  DropDown,
  Separator,
  ImageIcon,
  DeliveryInfoModal,
  MapsModal,
  AddressTagList,
} from 'components/atoms';
import {RootStackParamsList} from 'routes';
import {RouteProp, useTheme} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {useDispatch, useSelector} from 'react-redux';
import {getUserInfo, setLoading} from 'app-redux-store/slice/appSlice';
import ToggleSwitch from 'toggle-switch-react-native';
import {
  createCostumerAddress,
  getAddressValidationRule,
  updateCostumerAddress,
  checkPinCodeValid,
} from 'services/address';
import {
  checkDevice,
  fullNameRegex,
  stringReg,
  phoneReg,
  nameSplit,
  btnClickCallBack,
  fullNameReg,
  onCheckGst,
  billingAdd,
} from 'utils/utils';
import localStorage from 'utils/localStorage';
import {AnalyticsEvents} from 'components/organisms';
import ErrorHandler from 'utils/ErrorHandler';
import Icons from 'common/icons';
import {setCartAddress} from 'services/cart';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ManageAddress'>;
};

const ManageAddressScene = ({navigation, route}: Props) => {
  const TAG = 'ManageAddressScreen';
  const {colors} = useTheme();
  const dispatch = useDispatch();
  const {countryListings, cartId} = useSelector(
    (state: RootState) => state.app,
  );
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const address = route?.params?.address;
  const [infoModel, setInfoModel] = useState(false);
  const [apiValidation, setApiValidation] = useState({});
  const [validPinCode, setValidPinCode] = useState(false);
  const [alterNoShow, setAlterNoShow] = useState(false);
  const [gstShow, setGstShow] = useState(false);
  const [landmarkShow, setLandmarkShow] = useState(false);
  const [mapShow, setMapShow] = useState(false);
  const [pinCodeData, setPinCodeData] = useState();
  const [values, setValues] = useState({
    firstName: '',
    // lastName: '',
    country_code: '',
    region: {id: '', state_code: '', state_name: ''},
    tag: '',
    deliveryArea: '',
    deliverAddress: {city: '', formatted_address: '', lat: '', lng: ''},
    other: '',
  });
  const [errors, setErrors] = useState({});
  const [postCodeFormat, setPostCodeFormat] = useState<undefined | RegExp>(
    undefined,
  );
  const [taxFormat, setTaxFormat] = useState<undefined | RegExp>(undefined);
  const [telephoneFormat, setTelephoneFormat] = useState<undefined | RegExp>(
    undefined,
  );
  const [regionList, setRegionList] = useState([]);
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const scrollRef = useRef(null);
  const insets = useSafeAreaInsets();

  const GetRegex = useCallback((data: string) => {
    if (data) {
      let index = data.lastIndexOf('/');
      let s = data.substring(0, index) + '' + data.substring(index + 1);
      return new RegExp(s.replace('/', ''));
    }
  }, []);

  // const checkValid = useMemo(() => {
  //   const isValidFullName =
  //     values?.firstName && fullNameRegex.test(values.firstName);
  //   const value =
  //     isValidFullName &&
  //     // values?.lastName &&
  //     values?.telephone &&
  //     // values?.alternateNumber &&
  //     values?.street &&
  //     values?.postcode &&
  //     values?.city &&
  //     values?.region?.id &&
  //     values?.country_code?.country_id &&
  //     (values?.tax_required && values?.vat_id ? true : true)
  //       ? true
  //       : false;
  //   return value;
  // }, [values]);

  // ------------------- create Address -----------
  const createUserAddress = useCallback(async () => {
    try {
      const isValid = await validate(values);
      if (!isValid) {
        return;
      }
      const gst = values?.vat_id?.trim();
      const stateRegion = values?.region;
      dispatch(setLoading(true));
      if (gstShow && gst.length > 0) {
        const gstResponse = await checkGst(gst, stateRegion?.state_name, true);
        if (!gstResponse?.status) {
          dispatch(setLoading(false));
          return;
        }
      }
      const stateCode = stateRegion?.state_code;
      const stateCodeMismatch =
        stateCode !== pinCodeData?.district?.state?.state_code ||
        values?.postcode !== pinCodeData?.pincode;
      let isPinCodeValid = true;
      if (stateCodeMismatch) {
        const pinCodeRes = await checkPinCodeValid(values?.postcode);
        const verifyPostCode =
          stateCode !== pinCodeRes?.data?.district?.state?.state_code ||
          values?.postcode !== pinCodeRes?.data;
        if (verifyPostCode) {
          showErrorMessage(t('validations.postcodeState'));
          Keyboard.dismiss();
          dispatch(setLoading(false));
          return;
        }
        isPinCodeValid = pinCodeRes?.status;
      }
      if (!isPinCodeValid) {
        dispatch(setLoading(false));
        return;
      }
      setErrors({});
      const {fname, lname} = nameSplit(values?.firstName?.trim());
      let variables = {
        firstname: fname || '',
        lastname: lname ? lname : fname,
        street: [
          values?.street?.trim().replaceAll(',', '') || '',
          values?.landmark?.trim() || '',
        ],
        postcode: values?.postcode?.trim() || '',
        city: values?.city?.trim() || '',
        telephone: values?.telephone?.trim() || '',
        default_shipping:
          values?.default_shipping ||
          (userInfo?.addresses?.length === 0 ? true : false),
        default_billing: false,
        vat_id: values?.vat_id?.trim() || '',
        country_id: values?.country_code?.country_id,
        region: {
          region_id: values?.region?.id,
          region: values.region?.state_name,
          region_code: values.region?.state_code,
        },
        latitude: values?.deliverAddress?.lat || '',
        longitude: values?.deliverAddress?.lng || '',
        map_address: values?.deliverAddress?.formatted_address || '',
        tag:
          (values?.tag !== 'Home' && values?.tag !== 'Clinic'
            ? values?.other || 'Other'
            : values?.tag) || '',
        customer_street_2:
          values?.deliveryArea?.trim()?.replace(/\s+/g, ' ') || '',
        custom_attributes: [
          {
            attribute_code: 'alternate_telephone',
            value: values?.alternateNumber?.trim() || '',
          },
        ],
      };
      if (address?.id) {
        variables.id = address.id;
      }
      const submit = address?.id
        ? updateCostumerAddress
        : createCostumerAddress;
      const {data, status} = await submit(variables);
      dispatch(setLoading(false));
      if (!status) {
        showErrorMessage(data?.message);
        return;
      }
      showSuccessMessage(
        address
          ? t('manageAddress.addressUpdated')
          : t('manageAddress.addressAdded'),
      );

      if (address) {
        AnalyticsEvents(
          'SHIPPING_DETAILS_UPDATED',
          'Shipping Details Updated',
          variables,
          userInfo,
          isLoggedIn,
        );
      }
      dispatch(getUserInfo());
      const savedAddress = await localStorage.get('delivery_address');
      const isSameAddress = savedAddress?.id === data?.id;
      const isEmpty = !savedAddress || savedAddress?.postcode === '';
      const updateAddress = {...data, ...variables};
      if (isSameAddress || isEmpty) {
        await localStorage.set('delivery_address', updateAddress);
      }
      const backScreen = route?.params?.address?.skipLocation === true ? 1 : 2;
      if (route?.params?.cardType) {
        if (route?.params?.onChangeSelectAddress) {
          setAddressOnCart(updateAddress);
          route?.params?.onChangeSelectAddress?.(updateAddress);
        }
        setTimeout(() => navigation?.pop(backScreen), 500);
      } else {
        // navigation.navigate('AddressList');
        navigation?.pop(backScreen);
      }
    } catch (error) {
      dispatch(setLoading(false));
      showErrorMessage(t('validations.someThingWrong'));
    }
  }, [
    values,
    address,
    userInfo,
    pinCodeData,
    dispatch,
    navigation,
    route,
    isLoggedIn,
  ]);

  const setAddressOnCart = useCallback(
    async (address: CustomerAddressV2) => {
      if (cartId) {
        const billingAddInfo = await billingAdd(
          address?.id,
          userInfo?.addresses || [],
        );
        const {data, status} = await setCartAddress(cartId, {
          shipping_addresses: [
            isLoggedIn
              ? {
                  address: {
                    country_code: address?.country_code ?? address?.country_id,
                    country_id: address?.country_id ?? 'IN',
                    region_code: address?.region?.region_code,
                    street: address.street,
                    postcode: address.postcode,
                    city: address.city,
                    firstname: address.firstname,
                    lastname: address.lastname,
                    telephone: address.telephone,
                    gst_id: address?.vat_id,
                    same_as_billing: billingAddInfo ? false : true,
                    alternate_mobile:
                      address?.custom_attributes?.find(
                        attribute =>
                          attribute?.attribute_code === 'alternate_telephone',
                      ).value ?? null,
                    region_id: address?.region?.region_id ?? 0,
                    region: address?.region?.region,
                  },
                  customer_address_id: address?.id,
                }
              : {
                  address: {country_code: 'IN'},
                },
          ],
          buy_now: false,
          billing_address: isLoggedIn ? billingAddInfo : null,
        });
        if (status) {
          await localStorage.set('delivery_address', address);
        }
      }
    },
    [cartId, isLoggedIn],
  );

  const getAddressValidations = async (country_Id = 'IN') => {
    try {
      const {data} = await getAddressValidationRule(country_Id);
      if (data) {
        setApiValidation(data);
        let TX = GetRegex(data.tax_format);
        let TF = GetRegex(data.telephone_format);
        let PF = GetRegex(data.postcode_format);
        setTaxFormat(TX);
        setTelephoneFormat(TF);
        setPostCodeFormat(PF);
        setValues(prev => ({
          ...prev,
          alternate_telephone_required: data?.alternate_telephone_required,
          postcode_required: data?.postcode_required,
          tax_required: data?.tax_required,
          tax_format: TX,
          postcode_format: PF,
          telephone_format: TF,
        }));
      }
    } catch (error) {
      showErrorMessage(`${error?.message}. Please try again.`);
    }
  };

  const validate = () => {
    const error = {};
    let errorStatus = true;
    const validations = [
      {
        field: 'firstName',
        rule: stringReg,
        message: t('validations.fullNameReq'),
        matchMessage: t('validations.fullNameCorrect'),
      },
      // {
      //   field: 'lastName',
      //   rule: stringReg,
      //   message: t('validations.lastNameReq'),
      //   matchMessage: t('validations.lastNameCorrect'),
      // },
      {
        field: 'telephone',
        rule: phoneReg,
        message: t('validations.telephoneRequired'),
        matchMessage: t('validations.telephoneCorrect'),
      },
      {
        field: 'alternateNumber',
        rule: phoneReg,
        message: t('validations.alternateRequired'),
        matchMessage: t('validations.alternateCorrect'),
        required: values?.alternateNumber ? true : false,
      },
      // {field: 'deliverAddress', message: t('validations.deliverAddress')},
      {field: 'street', message: t('validations.street')},
      {field: 'deliveryArea', message: t('validations.deliveryArea')},
      {
        field: 'postcode',
        rule: postCodeFormat,
        message: t('validations.postcodeRequired'),
        matchMessage: t('validations.validPostCode'),
      },
      {
        field: 'city',
        rule: stringReg,
        message: t('validations.cityReq'),
        matchMessage: t('validations.alphabets'),
      },
      {field: 'region', message: t('validations.region')},
      {field: 'country_code', message: t('validations.country')},
      {
        field: 'vat_id',
        rule: taxFormat,
        message: t('validations.tax'),
        matchMessage: t('validations.validTex'),
        required: values?.tax_required,
      },
      {
        field: 'tag',
        message: t('validations.tagReq'),
      },
      // {
      //   field: 'other',
      //   message: t('validations.otherReq'),
      //   required:
      //     values?.tag !== 'Home' && values?.tag !== 'Clinic' ? true : false,
      // },
    ];
    for (const {
      field,
      rule,
      message,
      matchMessage,
      required = true,
    } of validations) {
      const value = field.split('.').reduce((obj, key) => obj?.[key], values);
      if (required && !value) {
        error[field] = message;
        errorStatus = false;
      } else if (required && rule && !value?.match(rule)) {
        error[field] = matchMessage;
        errorStatus = false;
      } else if (field === 'region' && !value?.id) {
        error[field] = message;
        errorStatus = false;
      } else if (field === 'country_code' && !value?.country_id) {
        error[field] = message;
        errorStatus = false;
      } else if (field === 'deliverAddress' && !value?.lat) {
        error[field] = message;
        errorStatus = false;
      } else if (
        field === 'vat_id' &&
        value?.trim()?.length > 0 &&
        rule &&
        !value?.trim()?.match(rule)
      ) {
        error[field] = matchMessage;
        errorStatus = false;
      }
    }
    setErrors(error);
    return errorStatus;
  };

  useEffect(() => {
    if (address?.postcode) {
      checkPinCode(address?.postcode, true);
    }
    if (address?.skipLocation === true && address?.vat_id) {
      checkGst(address?.vat_id, address?.region?.region);
    }
    const item = countryListings.find(
      item =>
        item.country_id ===
        (address?.country_code?.id ? address?.country_code?.id : 'IN'),
    );

    setRegionList(item?.states || []);
    const alternateTelephoneIndex = address?.custom_attributes?.findIndex(
      e => e.attribute_code === 'alternate_telephone',
    );
    let state = address?.region
      ? item?.states?.find(r => r.id === address?.region?.region_id)
      : {id: '', state_code: '', state_name: ''};
    if (address?.state) {
      const states = item?.states?.filter(
        item => item.state_name.toLowerCase() === address?.state?.toLowerCase(),
      );
      if (states?.length > 0) {
        state = states[0];
      }
    }
    const alternateNo =
      address?.custom_attributes?.[alternateTelephoneIndex]?.value;
    getAddressValidations(item?.id);
    setLandmarkShow(address?.street?.[1] ? true : false);
    setGstShow(address?.vat_id ? true : false);
    setAlterNoShow(alternateNo ? true : false);
    const otherTag = address?.tag !== 'Home' && address?.tag !== 'Clinic';
    const googleAddress = address?.formatted_address || address?.map_address;
    const deliverAddress = {
      lat: address?.lat || address?.latitude,
      lng: address?.lng || address?.longitude,
      formatted_address: googleAddress,
      city: address?.city,
    };
    const lastName =
      address?.firstname === address?.lastname ? '' : address?.lastname;
    setValues(prev => ({
      ...prev,
      firstName:
        [
          address?.firstname ||
            `${userInfo?.firstname || ''} ${userInfo?.lastname || ''}` ||
            '',
          lastName || '',
        ]
          .filter(Boolean)
          .join(' ') || '',
      // lastName: address?.lastname || '',
      postcode: address?.postcode || '',
      street: address?.street?.[0] || '',
      landmark: address?.street?.[1] || '',
      city: address?.city || '',
      telephone: address?.telephone || userInfo?.mobile || '',
      default_shipping: address?.default_shipping
        ? Boolean(address?.default_shipping)
        : userInfo?.addresses?.length === 0
        ? true
        : false,
      default_billing: false,
      alternateNumber: alternateNo ?? '',
      country_code: item,
      region: state,
      tag: address?.tag || 'Clinic',
      other: otherTag ? address?.tag : '',
      deliveryArea: address?.deliveryArea || address?.customer_street_2 || '',
      deliverAddress: deliverAddress,
      vat_id: address?.vat_id ? address?.vat_id : '',
    }));
  }, [address, countryListings]);

  const checkGst = async (gst, name, checkReturn = false) => {
    const response = await onCheckGst(gst?.trim(), name);
    const {status, error} = response;
    if (!status) {
      setErrors({...errors, region: error, gst: true});
      if (checkReturn) {
        return response;
      }
    } else {
      setErrors({...errors, region: '', gst: false});
      if (checkReturn) {
        return {status: true};
      }
    }
  };

  const onErrorView = (error: string) => {
    return (
      <>
        <Spacer size="sx" />
        <Label
          text={String(t(error) || '')}
          size="m"
          weight="400"
          color="red3"
        />
      </>
    );
  };

  const checkPinCode = useCallback(
    async (postcode: string, check = false) => {
      try {
        const {data, status} = await checkPinCodeValid(postcode);
        dispatch(setLoading(false));
        if (!check) {
          setValidPinCode(status ? false : true);
        }
        if (status && data) {
          setPinCodeData(data);
          if (!check) {
            const states = regionList?.filter(
              item =>
                item.state_code.toLowerCase() ===
                data?.district?.state?.state_code?.toLowerCase(),
            );
            const obj = {
              city: data?.district?.district_name,
            };
            if (states?.length > 0 && states[0]) {
              obj['region'] = states[0];
              setErrors({...errors, region: '', city: ''});
            } else {
              setErrors({...errors, city: ''});
            }
            setValues(prev => ({
              ...prev,
              ...obj,
            }));
          }
        }
      } catch (error) {}
    },
    [regionList],
  );

  const onChangeText = (text, key) => {
    if (
      key === 'region' &&
      text?.state_code !== pinCodeData?.district?.state?.state_code &&
      values?.postcode
    ) {
      showErrorMessage(t('validations.postcodeState'));
      Keyboard.dismiss();
    }
    const validationRules = {
      firstName: {
        required: t('validations.fullNameReq'),
        pattern: stringReg,
        invalid: t('validations.fullNameCorrect'),
      },
      // lastName: {
      //   required: t('validations.lastNameReq'),
      //   pattern: stringReg,
      //   invalid: t('validations.lastNameCorrect'),
      // },
      telephone: {
        required: t('validations.telephoneRequired'),
        pattern: phoneReg,
        invalid: t('validations.telephoneCorrect'),
      },
      alternateNumber: {
        required:
          key === 'alternateNumber' && text?.length > 0
            ? t('validations.alternateRequired')
            : '',
        pattern: phoneReg,
        invalid: t('validations.alternateCorrect'),
      },
      // deliverAddress: {
      //   required: t('validations.deliverAddress'),
      // },
      street: {
        required: t('validations.street'),
      },
      postcode: {
        required: t('validations.postcodeRequired'),
        pattern: postCodeFormat,
        invalid: t('validations.validPostCode'),
      },
      city: {
        required: t('validations.cityReq'),
        pattern: stringReg,
        invalid: t('validations.alphabets'),
      },
      region: {
        required: t('validations.region'),
      },
      country_code: {
        required: t('validations.country'),
      },
      vat_id: {
        required: values?.tax_required ? t('validations.tax') : '',
        pattern: taxFormat,
        invalid: t('validations.validTex'),
      },
      deliveryArea: {
        required: t('validations.deliveryArea'),
      },
      tag: {
        required: t('validations.tagReq'),
      },
      // other: {
      //   required: t(
      //     values?.tag !== 'Home' && values?.tag !== 'Clinic'
      //       ? 'validations.otherReq'
      //       : '',
      //   ),
      // },
    };

    const error = {...errors};
    const rule = validationRules[key];
    setValues(prev => ({...prev, [key]: text}));
    if (!rule) return;
    if (!text && rule.required) {
      error[key] = rule.required;
    } else if (rule.required && rule.pattern && !text?.match(rule.pattern)) {
      error[key] = rule.invalid || '';
    } else if (key === 'region' && !text?.id) {
      error[field] = rule.required;
    } else if (key === 'country_code' && !text?.country_id) {
      error[field] = rule.required;
    } else if (
      key === 'vat_id' &&
      text?.trim()?.length > 0 &&
      rule.pattern &&
      !text?.trim()?.match(rule.pattern)
    ) {
      error[key] = rule.invalid || '';
    } else {
      error[key] = '';
    }
    setErrors(error);
    if (key === 'country_code') {
      setValues(prev => ({...prev, region: text?.states?.[0]}));
      setRegionList(text?.states || []);
      getAddressValidations(text?.country_id);
    }

    if (key === 'postcode' && text?.length > 5) {
      checkPinCode(text);
    }
    if (key === 'region' && values?.vat_id && gstShow) {
      checkGst(values?.vat_id, text.state_name);
    }
  };

  const renderItem = (item, selected) => {
    return (
      <View style={[styles.dropDownItem, selected && styles.itemSelected]}>
        <Label
          color={selected ? 'whiteColor' : 'text2'}
          text={item.state_name}
          size="mx"
          weight="500"
        />
      </View>
    );
  };

  const inputAreaHeight = useMemo(() => {
    return values?.deliveryArea?.length < 50
      ? 44
      : values?.deliveryArea?.length < 80
      ? 74
      : 78;
  }, [values?.deliveryArea]);

  const deliveryArea = useCallback(() => {
    return (
      <TextInputBox
        id="txtManageAddressDeliveryArea"
        label={t('manageAddress.location')}
        onChangeText={text => onChangeText(text.trimStart(), 'deliveryArea')}
        value={values?.deliveryArea}
        error={!!errors?.deliveryArea}
        errorText={errors?.deliveryArea}
        returnKeyType="next"
        keyboardType="name-phone-pad"
        leftIcon={Icons.map}
        editable={values?.deliveryArea || address?.latitude ? true : false}
        pointerEvents={
          values?.deliveryArea || address?.latitude ? 'auto' : 'none'
        }
        containerStyle={
          values?.deliveryArea
            ? [
                styles.inputContent,
                {
                  paddingTop: values?.deliveryArea?.length < 40 ? 10 : 4,
                  maxHeight: inputAreaHeight,
                },
              ]
            : styles.inputBoxStyle
        }
        inputStyle={
          values?.deliveryArea ? styles.inputStyle : styles.inputBoxStyle
        }
        multiline={values?.deliveryArea ? true : false}
        textAlignVertical="top"
      />
    );
  }, [
    values?.deliveryArea,
    errors?.deliveryArea,
    inputAreaHeight,
    onChangeText,
  ]);

  const openMapsModal = useCallback(() => {
    setMapShow(prevState => !prevState);
  }, []);

  return (
    <SafeAreaView style={[styles.container, {paddingTop: insets.top}]}>
      <KeyboardAvoidingView
        style={styles.fOne}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <Header
            backButton
            navigation={navigation}
            searchIcon={true}
            bagIcon={true}
            text={
              address?.customer_id
                ? t('manageAddress.editAddress')
                : t('manageAddress.addAddress')
            }
            style={styles.headerStyle}
            onPressNavigation={() => {
              navigation?.goBack();
            }}
          />
        </ErrorHandler>

        <View style={styles.fOne}>
          <ErrorHandler
            componentName={`${TAG} AddressForm`}
            onErrorComponent={<View />}>
            <ScrollView
              ref={scrollRef}
              keyboardShouldPersistTaps="always"
              bounces={false}
              showsVerticalScrollIndicator={false}>
              <View style={styles.mainView}>
                {/* <View style={styles.list}>
                  <Label
                    size="mx"
                    text={
                      address?.customer_id
                        ? t('manageAddress.edit')
                        : t('manageAddress.newAddress')
                    }
                    fontFamily="Medium"
                    color="text2"
                    style={styles.addNewTxt}
                  />
                  <Spacer type="Horizontal" size="xms" />
                  <View style={styles.lineCut} />
                </View> */}
                <Spacer size="sx" />
                <View>
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressFullName"
                      label={t('manageAddress.fullName')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'firstName')
                      }
                      value={values?.firstName}
                      error={!!errors?.firstName}
                      errorText={errors?.firstName}
                      returnKeyType="next"
                      leftIcon={Icons.user}
                    />
                  </View>
                  {/* <Spacer
                    size="m"
                    type={checkDevice() ? 'Horizontal' : 'Vertical'}
                  />
                  <View style={styles.fOne}>
                    <InputBox
                      keyboardType="name-phone-pad"
                      textStyle={styles.textMain}
                      style={styles.input}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'lastName')
                      }
                      placeholder={t('profile.lastname') + ' *'}
                      value={values?.lastName}
                      error={String(errors?.lastName || '')}
                      placeholderTextColor={colors.text2}
                    />
                  </View> */}
                </View>
                <Spacer size="sx" />
                <View>
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressTelePhoneNumber"
                      label={t('manageAddress.telephoneNumber')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'telephone')
                      }
                      value={values?.telephone}
                      error={!!errors?.telephone}
                      errorText={errors?.telephone}
                      returnKeyType="done"
                      maxLength={10}
                      keyboardType="phone-pad"
                      leftIcon={Icons.phone1}
                    />
                  </View>
                  <Spacer size="sx" type="Vertical" />
                  <View style={styles.fOne}>
                    {alterNoShow ? (
                      <>
                        <TextInputBox
                          testID="txtManageAddressAlternateTelephone"
                          label={t('manageAddress.alternateTelephone')}
                          onChangeText={text =>
                            onChangeText(text.trimStart(), 'alternateNumber')
                          }
                          value={values?.alternateNumber}
                          error={!!errors?.alternateNumber}
                          errorText={errors?.alternateNumber}
                          returnKeyType="done"
                          maxLength={10}
                          keyboardType="phone-pad"
                          leftIcon={Icons.mobile}
                        />
                        {(!values?.alternateNumber ||
                          values?.alternateNumber?.length === 0) && (
                          <TouchableOpacity
                            style={styles.closeIcons}
                            onPress={() => [
                              setAlterNoShow(false),
                              onChangeText('', 'alternateNumber'),
                            ]}>
                            <ImageIcon
                              size="l"
                              tintColor="text"
                              icon="closeIcons"
                              resizeMode="contain"
                            />
                          </TouchableOpacity>
                        )}
                      </>
                    ) : (
                      <TouchableOpacity
                        style={[styles.rowCenter, styles.labelTxt]}
                        onPress={() => setAlterNoShow(!alterNoShow)}>
                        <ImageIcon
                          size="l"
                          icon="plus"
                          tintColor="newSunnyOrange"
                        />
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          size="m"
                          text={t('manageAddress.addAlternate')}
                          fontFamily="Medium"
                          color="newSunnyOrange"
                        />
                        <Spacer size="xs" type="Horizontal" />
                        <Pressable onPress={() => setInfoModel(!infoModel)}>
                          <ImageIcon
                            size="xxl"
                            icon="infoCircle"
                            tintColor="text2"
                          />
                        </Pressable>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
                <Spacer size={values?.alternateNumber ? 'xm' : 'sx'} />
                <View style={styles.addressCardView}>
                  <View style={styles.fOne}>
                    <View style={styles.rowCenter}>
                      <ImageIcon
                        size="xx"
                        tintColor="text"
                        icon="addressPin"
                        resizeMode="contain"
                      />
                      {values?.deliverAddress?.formatted_address && (
                        <>
                          <Spacer size="xm" type="Horizontal" />
                          <Label
                            numberOfLines={1}
                            ellipsizeMode="tail"
                            text={t('address.deliverHere')}
                            size="m"
                            weight="600"
                            color="text2"
                          />
                        </>
                      )}
                      <Spacer size="xm" type="Horizontal" />
                      <TouchableOpacity onPress={openMapsModal}>
                        <Label
                          text={
                            values?.deliverAddress?.formatted_address
                              ? t('buttons.change')
                              : t('manageAddress.updateDeliveryLocation')
                          }
                          size="m"
                          weight="500"
                          color="newSunnyOrange"
                        />
                      </TouchableOpacity>
                    </View>
                    {values?.deliverAddress?.formatted_address ? (
                      <>
                        <Spacer size="s" />
                        <Label
                          text={values?.deliverAddress?.formatted_address || ''}
                          size="m"
                          weight="400"
                          color="text2"
                          style={styles.addNewTxt}
                        />
                      </>
                    ) : (
                      <View />
                    )}
                  </View>
                  <Spacer size="xm" type="Horizontal" />
                  {values?.deliverAddress?.formatted_address && (
                    <ImageIcon size="x8l" icon="map1" resizeMode="contain" />
                  )}
                </View>
                {errors?.deliverAddress &&
                  onErrorView(errors?.deliverAddress || '')}
                <Spacer size="sx" />
                <TextInputBox
                  testID="txtManageAddressFullAddress"
                  label={t('manageAddress.fullAddress')}
                  fieldMandatory={true}
                  onChangeText={text =>
                    onChangeText(text.trimStart(), 'street')
                  }
                  value={values?.street}
                  error={!!errors?.street}
                  errorText={errors?.street}
                  returnKeyType="next"
                  leftIcon={Icons.home2}
                  // numberOfLines={4}
                  // multiline={true}
                  // style={styles.addressMainView}
                />
                <Spacer size="sx" />
                <TextInputBox
                  id="txtManageAddressDeliveryArea"
                  label={t('manageAddress.location')}
                  onChangeText={text =>
                    onChangeText(text.trimStart(), 'deliveryArea')
                  }
                  value={values?.deliveryArea}
                  error={!!errors?.deliveryArea}
                  errorText={errors?.deliveryArea}
                  returnKeyType="next"
                  keyboardType="default"
                  leftIcon={Icons.map}
                  containerStyle={
                    values?.deliveryArea
                      ? [
                          styles.inputContent,
                          {
                            paddingTop:
                              values?.deliveryArea?.length < 40 ? 10 : 4,
                            maxHeight: inputAreaHeight,
                          },
                        ]
                      : styles.inputBoxStyle
                  }
                  multiline={true}
                  textAlignVertical="top"
                  blurOnSubmit={false}
                />
                {/* {values?.deliveryArea ? (
                  deliveryArea()
                ) : (
                  <TouchableOpacity onPress={openMapsModal}>
                    {deliveryArea()}
                  </TouchableOpacity>
                )} */}

                <Spacer size="sx" />
                <View style={[styles.rowView, styles.allCenter]}>
                  <View style={styles.fOne}>
                    {landmarkShow ? (
                      <>
                        <TextInputBox
                          testID="txtManageAddressLandmark"
                          label={t('manageAddress.landmark')}
                          onChangeText={text => {
                            setValues(prev => ({
                              ...prev,
                              landmark: text.trimStart(),
                            }));
                          }}
                          value={values?.landmark}
                          error={!!errors?.landmark}
                          errorText={errors?.landmark}
                          returnKeyType="next"
                          maxLength={30}
                        />
                        {(!values?.landmark ||
                          values?.landmark?.length === 0) && (
                          <TouchableOpacity
                            style={styles.closeIcons}
                            onPress={() => setLandmarkShow(false)}>
                            <ImageIcon
                              size="l"
                              tintColor="text"
                              icon="closeIcons"
                              resizeMode="contain"
                            />
                          </TouchableOpacity>
                        )}
                      </>
                    ) : (
                      <TouchableOpacity
                        style={[styles.rowCenter, styles.labelTxt]}
                        onPress={() => setLandmarkShow(!landmarkShow)}>
                        <ImageIcon
                          size="l"
                          icon="plus"
                          tintColor="newSunnyOrange"
                        />
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          size="m"
                          text={t('manageAddress.addLandmark')}
                          fontFamily="Medium"
                          color="newSunnyOrange"
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                  <Spacer size="m" type="Horizontal" />
                  <View style={styles.fOne}>
                    {gstShow ? (
                      <>
                        <TextInputBox
                          testID="txtManageAddressGstIn"
                          label={
                            values?.tax_required
                              ? t('orderTrack.gstIn')
                              : t('manageAddress.gstIn')
                          }
                          fieldMandatory={values?.tax_required ? true : false}
                          onChangeText={text =>
                            onChangeText(text.trimStart(), 'vat_id')
                          }
                          value={values?.vat_id}
                          error={!!errors?.vat_id || errors?.gst}
                          errorText={errors?.vat_id}
                          returnKeyType="next"
                          onBlur={() => {
                            if (
                              values?.region?.state_name &&
                              values?.vat_id?.length > 0
                            ) {
                              checkGst(
                                values?.vat_id,
                                values?.region?.state_name,
                              );
                            }
                          }}
                        />
                        {(!values?.vat_id || values?.vat_id?.length === 0) && (
                          <TouchableOpacity
                            style={styles.closeIcons}
                            onPress={() => setGstShow(false)}>
                            <ImageIcon
                              size="l"
                              tintColor="text"
                              icon="closeIcons"
                              resizeMode="contain"
                            />
                          </TouchableOpacity>
                        )}
                      </>
                    ) : (
                      <TouchableOpacity
                        style={[styles.rowCenter, styles.labelTxt]}
                        onPress={() => setGstShow(!gstShow)}>
                        <ImageIcon
                          size="l"
                          icon="plus"
                          tintColor="newSunnyOrange"
                        />
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          size="m"
                          text={t('manageAddress.addGSTIN')}
                          fontFamily="Medium"
                          color="newSunnyOrange"
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
                <Spacer size="xm" />
                <View style={styles.rowView}>
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressPostcode"
                      label={t('manageAddress.pincode')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'postcode')
                      }
                      value={values?.postcode}
                      error={errors?.postcode || validPinCode ? true : false}
                      errorText={String(
                        errors?.postcode || validPinCode
                          ? values.postcode?.trim() === ''
                            ? t('validations.postcodeRequired')
                            : t('validations.validPostCode')
                          : '',
                      )}
                      returnKeyType="done"
                      maxLength={6}
                      keyboardType={
                        Platform.OS === 'ios' ? 'number-pad' : 'numeric'
                      }
                      leftIcon={Icons.location1}
                    />
                  </View>
                  <Spacer size="m" type="Horizontal" />
                  <View style={styles.fOne}>
                    <TextInputBox
                      testID="txtManageAddressCity"
                      label={t('manageAddress.city')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'city')
                      }
                      value={values?.city}
                      error={!!errors?.city}
                      errorText={errors?.city}
                      returnKeyType="next"
                      leftIcon={Icons.locationPin}
                    />
                  </View>
                </View>
                <Spacer size="s" type="Vertical" />
                <View style={checkDevice() && styles.rowView}>
                  <View style={styles.fOne}>
                    <DropDown
                      testID="manageAddressSelectRegion"
                      heading={t('manageAddress.selectState')}
                      placeholder={t('manageAddress.selectRegion')}
                      fieldMandatory={true}
                      value={values?.region?.id ? values?.region : null}
                      listContainerStyle={styles.dropDownItemView}
                      selectedTextStyle={styles.dropDownTextStyle}
                      styleDropDown={[
                        styles.styleDropDown,
                        errors?.region && styles.errorBorder,
                      ]}
                      itemTextStyle={styles.dropDownTextStyle}
                      placeholderStyle={styles.dropDownTextStyle}
                      renderRightIcon={(focus: boolean) => {
                        if (focus && Platform.OS === 'ios') {
                          setTimeout(() => {
                            scrollRef?.current?.scrollTo({
                              y: 100,
                              animated: true,
                            });
                          }, 500);
                        }
                        return (
                          <ImageIcon
                            size="xl"
                            tintColor="text"
                            icon="dUpArrow"
                            resizeMode="contain"
                            style={!focus && styles.downImg}
                          />
                        );
                      }}
                      renderItem={renderItem}
                      onChange={item => onChangeText(item, 'region')}
                      returnValueOnly={false}
                      data={regionList}
                      valueField="state_code"
                      labelField="state_name"
                      dropdownPosition="top"
                    />
                    {errors?.region && onErrorView(errors?.region || '')}
                  </View>
                  <Spacer
                    size={checkDevice() ? 'm' : 'sx'}
                    type={checkDevice() ? 'Horizontal' : 'Vertical'}
                  />
                  <View style={styles.fOne}>
                    {values?.country_code?.country_id ? (
                      <>
                        {checkDevice() && <Spacer size="sx" type="Vertical" />}
                        <TextInputBox
                          testID="txtManageAddressCountry"
                          label={t('manageAddress.country')}
                          value={values?.country_code?.country_name}
                          editable={false}
                        />
                      </>
                    ) : (
                      <>
                        <DropDown
                          value={values?.country_code}
                          selectedTextStyle={styles.dropDownTextStyle}
                          styleDropDown={styles.styleDropDown}
                          itemTextStyle={styles.dropDownTextStyle}
                          placeholderStyle={styles.dropDownTextStyle}
                          onChange={item => onChangeText(item, 'country_code')}
                          renderRightIcon={(focus: boolean) => (
                            <ImageIcon
                              size="xl"
                              tintColor="text"
                              icon="downArrow1"
                              resizeMode="contain"
                              style={focus && styles.downImg}
                            />
                          )}
                          returnValueOnly={false}
                          data={
                            countryListings?.length > 0
                              ? countryListings?.filter(
                                  (item: CustomerAddressV2) =>
                                    item.country_id === 'IN',
                                )
                              : []
                          }
                          labelField="country_name"
                          valueField="country_id"
                        />
                        {errors?.country_code &&
                          onErrorView(errors?.country_code || '')}
                      </>
                    )}
                  </View>
                </View>
                <Spacer size="xms" />
                <AddressTagList
                  selected={values?.tag}
                  onPress={(value: string) =>
                    onChangeText(value.trimStart(), 'tag')
                  }
                />
                {errors?.tag && onErrorView(errors?.tag || '')}
                <Spacer size="m" />
                {values?.tag &&
                  values?.tag !== 'Home' &&
                  values?.tag !== 'Clinic' && (
                    <>
                      <TextInputBox
                        label={t('manageAddress.other')}
                        fieldMandatory={false}
                        onChangeText={text =>
                          onChangeText(text.trimStart(), 'other')
                        }
                        value={values?.other}
                        // error={!!errors?.other}
                        // errorText={errors?.other}
                        returnKeyType="next"
                        keyboardType="name-phone-pad"
                        leftIcon={Icons.home2}
                      />
                      <Spacer size="m" />
                    </>
                  )}
                <View
                  style={[
                    styles.buttonContinuer,
                    checkDevice() && styles.defaultView,
                  ]}>
                  <Label
                    text={t('manageAddress.default')}
                    size="mx"
                    fontFamily="Medium"
                    color="text2"
                    style={styles.textCap}
                  />
                  <ToggleSwitch
                    isOn={values?.default_shipping}
                    onColor={colors.text2}
                    offColor={colors.placeholderColor}
                    size="medium"
                    onToggle={value => {
                      const allowDefault = !userInfo?.addresses?.some(
                        item =>
                          item?.id === address?.id && item?.default_shipping,
                      );
                      if (allowDefault) {
                        setValues(prev => ({...prev, default_shipping: value}));
                      }
                    }}
                    thumbOffStyle={styles.toggleOff}
                    thumbOnStyle={[
                      {
                        backgroundColor: values?.default_shipping
                          ? colors.whiteColor
                          : colors.placeholderColor,
                      },
                      styles.toggleOn,
                      styles.toggleView,
                    ]}
                  />
                </View>
                <Spacer type="Vertical" size="mx" />
                {checkDevice() && (
                  <Button
                    style={[styles.btnView, styles.btnTabView, styles.btnColor]}
                    labelStyle={styles.btnTxt}
                    text={t('buttons.saveAddress')}
                    onPress={() => btnClickCallBack(() => createUserAddress())}
                    labelSize="mx"
                    size="large"
                    type="primary"
                    radius="sx"
                    labelColor="whiteColor"
                    weight="500"
                  />
                )}
              </View>
              <Spacer type="Vertical" size="sx" />
            </ScrollView>
          </ErrorHandler>
        </View>
      </KeyboardAvoidingView>
      {!checkDevice() && (
        <View style={styles.btnContainer}>
          <Button
            style={[styles.btnView, styles.btnColor]}
            labelStyle={styles.btnTxt}
            text={t('buttons.saveAddress')}
            onPress={() => btnClickCallBack(() => createUserAddress())}
            labelSize="mx"
            size="large"
            type="primary"
            radius="sx"
            labelColor="whiteColor"
            weight="500"
          />
        </View>
      )}
      {infoModel && (
        <DeliveryInfoModal
          visible={infoModel}
          onClose={() => setInfoModel(false)}
          infoIcon="phoneNumber"
        />
      )}
      {mapShow && (
        <MapsModal
          visible={mapShow}
          coords={route?.params?.coords}
          onClose={openMapsModal}
          deliverAddress={values?.deliverAddress}
          onSelectAddress={(deliveryAdd: PlaceSuggestion) => {
            openMapsModal();
            if (deliveryAdd?.formatted_address) {
              let obj = {
                deliverAddress: deliveryAdd,
                deliveryArea:
                  deliveryAdd?.deliveryArea || deliveryAdd?.formatted_address,
                city: deliveryAdd.city || '',
              };
              if (deliveryAdd?.postcode) {
                onChangeText(deliveryAdd.postcode, 'postcode');
              }
              if (deliveryAdd?.state) {
                const states = regionList?.filter(
                  item =>
                    item.state_name.toLowerCase() ===
                    deliveryAdd?.state?.toLowerCase(),
                );
                if (states?.length > 0) {
                  obj['region'] = states[0];
                }
              }
              setValues(prev => ({
                ...prev,
                ...obj,
              }));
            }
          }}
          goBack={openMapsModal}
        />
      )}
    </SafeAreaView>
  );
};

export default ManageAddressScene;

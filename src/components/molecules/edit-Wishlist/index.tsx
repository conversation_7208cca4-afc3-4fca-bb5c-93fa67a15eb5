import React, {useCallback, useState} from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {
  TouchableOpacity,
  ButtonProps,
  View,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import Modal from 'react-native-modal';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {Formik} from 'formik';
import * as yup from 'yup';
import Button from '../button';
import {useTheme} from '@react-navigation/native';
import {showSuccessMessage} from 'utils/show_messages';
import {useDispatch} from 'react-redux';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {createWishlist} from 'services/wishlist';
import {Sizes} from 'common';
import {useMemo} from 'react';

type Props = {
  text?: string;
  icon?: keyof typeof Icons | null;
  onUpdate?: () => void;
  onPress?: () => void;
  tab?: string;
  item?: GetWishlistOutput;
  style?: ButtonProps['style'];
  modalType?: 'create' | 'edit';
  tintColor?: keyof Theme['colors'];
};

const EditWishList = ({
  item,
  tab,
  onUpdate,
  modalType,
  setMenuModel,
}: Props) => {
  const dispatch = useDispatch();
  const validationSchema = yup.object().shape({
    access_type: yup.string().required('Select Item'),
    wishlist_name: yup.string().required(t('validations.wishListNameRequired')),
  });
  const [apiError, setApiError] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const data1 = [
    {label: 'Private', value: 'private'},
    {label: 'Public', value: 'public'},
  ];

  const addNewWishList = useCallback(async (values, {resetForm}) => {
    if (values.wishlist_name?.trim().length !== 0) {
      dispatch(setLoading(true));
      const res = await createWishlist({
        title: values.wishlist_name,
        visibility: values.access_type,
        // visibility: 'public',
      });
      dispatch(setLoading(false));
      if (res?.status && res?.data) {
        setModalVisible(false);
        showSuccessMessage(t('toastMassages.createSuccess'));
        setApiError(null);
        onUpdate();
        resetForm();
      }
    }
  }, []);

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          setModalVisible(true);
        }}>
        {tab === 'My Lists' && (
          <View>
            <ImageIcon size="x7l" icon="addWishlist" resizeMode="contain" />
          </View>
        )}
      </TouchableOpacity>
      <Modal
        onBackButtonPress={() => setModalVisible(false)}
        isVisible={modalVisible}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={75}
        animationOutTiming={75}
        backdropOpacity={0.01}
        avoidKeyboard={true}
        style={styles.modalStyle}>
        <View style={styles.modalContainer}>
          <TouchableWithoutFeedback
            onPress={() => {
              setModalVisible(!modalVisible);
            }}>
            <TouchableOpacity
              onPress={() => {
                setModalVisible(!modalVisible);
              }}>
              <ImageIcon icon="close" size="x6l" />
            </TouchableOpacity>
          </TouchableWithoutFeedback>
          <Spacer size="m" />
          <View style={styles.modalView}>
            {/* <Spacer size="xl" /> */}
            <View>
              <Formik
                initialValues={{
                  wishlist_name: item?.wishlist_name,
                  access_type:
                    modalType === 'create'
                      ? // ? data1.find(e => e.value === item?.access_type)
                        'public'
                      : item?.access_type,
                }}
                validationSchema={validationSchema}
                onSubmit={modalType === 'create' ? addNewWishList : () => null}>
                {({
                  values,
                  handleChange,
                  handleSubmit,
                  setFieldValue,
                  errors,
                }) => (
                  <>
                    <View style={styles.headingBorder}>
                      <Label
                        text={
                          modalType === 'create'
                            ? t('editWishList.newWishlist')
                            : '' || modalType === 'edit'
                            ? t('otp.editNames') + 'item?.wishlist_name'
                            : ''
                        }
                        weight="600"
                        size="mx"
                        color="text2"
                      />
                    </View>
                    <Spacer type="Vertical" size="x6l" />
                    <View style={styles.modelSpace}>
                      {(modalType === 'create' || modalType === 'edit') && (
                        <>
                          <View>
                            <Label
                              text={
                                modalType === 'create'
                                  ? t('editWishList.listName')
                                  : '' || modalType === 'edit'
                                  ? t('wishList.yourLastName')
                                  : ''
                              }
                              weight="600"
                              size="m"
                              color="categoryTitle"
                            />
                            <Spacer type="Vertical" size="xms" />
                            <View style={styles.a1}>
                              <TextInput
                                testID="txtEditWishList1"
                                maxLength={Sizes.xl}
                                style={styles.input}
                                value={values?.wishlist_name?.trimStart()}
                                onChangeText={text =>
                                  setFieldValue(
                                    'wishlist_name',
                                    text.trimStart(),
                                  )
                                }
                                placeholder={t('otherText.wishListOne')}
                                placeholderTextColor={colors.text2}
                                allowFontScaling={false}
                              />
                              <Label
                                style={styles.rightText}
                                text={
                                  values?.wishlist_name?.length === undefined ||
                                  0
                                    ? '0/20'
                                    : `${
                                        values?.wishlist_name?.trimStart()
                                          .length
                                      }/20`
                                }
                                color="grey2"
                              />
                            </View>
                            {errors?.wishlist_name ? (
                              <Label
                                text={errors?.wishlist_name}
                                color="textError"
                                size="mx"
                                weight="400"
                              />
                            ) : null}
                          </View>
                        </>
                      )}
                    </View>
                    <Spacer type="Vertical" size="m" />
                    <Button
                      onPress={handleSubmit}
                      style={styles.buttonCreate}
                      type="Primary"
                      text={t('editWishList.create')}
                      labelSize="mx"
                      weight="400"
                      labelColor="whiteColor"
                    />
                  </>
                )}
              </Formik>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};
export default EditWishList;

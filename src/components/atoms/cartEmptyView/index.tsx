import React, {useEffect, useState} from 'react';
import {View, FlatList} from 'react-native';
import {useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {t} from 'i18next';
import {Spacer, Label, ProductCardVertical, ImageIcon} from 'components/atoms';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import {homePageSections} from 'services/home';
import {useSelector} from 'react-redux';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../../routes';
import {useMemo} from 'react';
import {AnalyticsEvents} from 'components/organisms';
import {RootState} from '@types/local';
import {debugLog} from 'utils/debugLog';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const CartEmptyView = (props: Props) => {
  const {navigation} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [showSuggest, setShowSuggest] = useState(false);
  const [hotSellingData, setHotSellingData] = useState<{[key: string]: string}>(
    {},
  );
  const {isLoggedIn, userInfo} = useSelector((state: RootState) => state.app);

  const getHotSellers = () => {
    let params = {'page-type': 'pdp'};
    homePageSections(params)
      .then((res: any) => {
        if (res.status && res?.data?.sections) {
          setHotSellingData(res?.data?.sections[0]);
          if (res?.data?.sections.length > 0) {
            setShowSuggest(true);
          } else {
            setShowSuggest(false);
          }
        }
      })
      .catch(err => {
        debugLog(err, 'responseData');
      });
  };
  useEffect(() => {
    getHotSellers();
    AnalyticsEvents(
      'EMPTY_CART',
      'Empty cart screen viewed',
      {
        source: 'Cart Screen',
      },
      userInfo,
      isLoggedIn,
    );
  }, []);

  return (
    <FlatList
      style={styles.mainView}
      data={['']}
      showsVerticalScrollIndicator={false}
      renderItem={() => (
        <>
          <View style={styles.wrapper}>
            <View style={styles.imageView}>
              <FastImage
                resizeMode="contain"
                style={styles.circle}
                source={Icons.circleGradient}
              />
              <FastImage
                resizeMode="contain"
                style={styles.emptyImage}
                source={Icons.emptyCart}
              />
            </View>
            <Spacer size="xxl" />
            <Label
              color="text2"
              size="l"
              fontFamily="Medium"
              text={t('otherText.seemsLike')}
              style={styles.textCap}
            />
            <Spacer size="xl" />
            {!isLoggedIn ? (
              <View>
                <Button
                  onPress={() => navigation.navigate('Tab', {screen: 'Home'})}
                  style={styles.btnStyle}
                  radius="xm"
                  type="secondary"
                  labelSize="mx"
                  labelColor="whiteColor"
                  labelStyle={styles.btnText}
                  text={t('otherText.homePage')}
                />
                <Spacer size="xm" />
                <Button
                  onPress={() =>
                    navigation.navigate('CategoryDetail', {categoryId: 2566})
                  }
                  style={styles.btnStyle}
                  radius="xm"
                  type="bordered"
                  labelSize="mx"
                  labelStyle={styles.btnText}
                  labelColor="categoryTitle"
                  text={t('otherText.offerPage')}
                />
              </View>
            ) : (
              <Button
                onPress={() => navigation.navigate('WishList')}
                style={styles.btnStyle}
                radius="xm"
                type="secondary"
                labelSize="mx"
                labelColor="whiteColor"
                labelStyle={styles.btnText}
                text={t('wishList.addItemWishlist')}
              />
            )}
          </View>
          <Spacer size="xl" />

          {showSuggest && (
            <View style={styles.forgetSection}>
              <Spacer size="xl" />
              <View style={styles.forgetView}>
                <Label
                  text={t('otherText.suggForYou')}
                  size="l"
                  fontFamily="SemiBold"
                  color="categoryTitle"
                />
                <Spacer size="s" />
                <ImageIcon icon="rightArrowCircle" size="xxl" />
              </View>
              <Spacer size="l" />
              <OptimizedFlatList
                horizontal
                ItemSeparatorComponent={() => <Spacer size="xm" />}
                onEndReachedThreshold={0.8}
                showsVerticalScrollIndicator={false}
                data={hotSellingData?.elements}
                keyExtractor={(_, i) => i.toString()}
                renderItem={({item, index}) => {
                  return (
                    <ProductCardVertical
                      index={index}
                      actionBtn={item?.action_btn}
                      skuId={item?.sku}
                      size="small"
                      hideAddToCart={true}
                      imageWithBorder={true}
                      maxWidth={0.47}
                      item={item}
                      productType={item?.type_id}
                      inStock={item.is_in_stock}
                      maxSaleQty={item?.max_sale_qty}
                      demoAvailable={item?.demo_available}
                      msrp={item?.msrp}
                      isBestSeller={true}
                      image={item?.media?.mobile_image}
                      name={item?.name}
                      rewardPoint={item?.reward_point_product}
                      description={item?.short_description}
                      rating={(item?.rating === 'null' ||
                      item.average_rating === null
                        ? 0
                        : Number(item?.rating) || Number(item?.average_rating)
                      ).toFixed(1)}
                      ratingCount={
                        !!item?.rating_count ? `(${item?.rating_count})` : '(0)'
                      }
                      price={item?.price}
                      sellingPrice={item?.selling_price}
                      currencySymbol={item?.currency_symbol}
                      discount={item?.discount?.label}
                      onPress={() => {
                        navigation.navigate('ProductDetail', {
                          productId: item?.product_id,
                          ProductItems: item,
                        });
                      }}
                      navigation={navigation}
                      onPressShare={() => null}
                      freeProducts={[]}
                    />
                  );
                }}
              />
            </View>
          )}
        </>
      )}
    />
  );
};

export default React.memo(CartEmptyView);

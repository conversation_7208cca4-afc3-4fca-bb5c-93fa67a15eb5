{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "android_dev": "react-native run-android --mode devdebug --appIdSuffix=dev", "android_release": "react-native run-android --mode devrelease", "android_staging": "react-native run-android --mode stagingdebug", "android_production": "react-native run-android --mode productiondebug", "ios": "react-native run-ios", "ios_dev": "react-native run-ios --simulator='iPhone 16 Pro' --scheme=Dev", "ios_staging": "react-native run-ios --simulator='iPhone 15 Pro' --scheme=Staging", "ios_production": "react-native run-ios --simulator='iPhone 15 Pro' --scheme=Dentalkart", "ios:pod:reset": "cd ios && pod deintegrate && pod setup && pod install", "ios:clean": "cd ios && rm -rf ~/Library/Caches/CocoaPods && rm -rf Pods && rm -rf ~/Library/Developer/Xcode/DerivedData/* && yarn ios:pod:reset", "start": "react-native start", "test": "jest", "lint": "eslint .", "build:android": "cd android && ./gradlew clean && ./gradlew assembleRelease && cd ..", "build:androidapp": "cd android && ./gradlew clean && ./gradlew app:assembleProductionRelease && cd ..", "smsHashGenerator": "./sms_retriever_hash.sh --package 'com.vasadental.dentalkart' --keystore ./android/dentalkart-app.jks", "postinstall": "npx patch-package && react-native setup-ios-permissions", "bundle-android:prod": "cp .env.production .env && ENVFILE=.env.production && cd android && ./gradlew clean &&  ./gradlew app:bundleProdRelease"}, "dependencies": {"@animatereactnative/marquee": "^0.2.0", "@apollo/client": "^3.8.2", "@apollo/react-hooks": "3.1.1", "@miblanchard/react-native-slider": "^2.3.1", "@microsoft/react-native-clarity": "4.1.8", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/art": "^1.2.0", "@react-native-community/blur": "^4.4.0", "@react-native-community/checkbox": "^0.5.14", "@react-native-community/cli-platform-android": "^10.2.0", "@react-native-community/cli-platform-ios": "10.2.5", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "7.6.3", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "^18.7.2", "@react-native-firebase/app": "^18.7.2", "@react-native-firebase/crashlytics": "^18.7.2", "@react-native-firebase/messaging": "^18.7.2", "@react-native-firebase/perf": "^21.0.0", "@react-native-google-signin/google-signin": "^9.0.2", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-material/core": "^1.3.7", "@react-navigation/bottom-tabs": "^6.4.3", "@react-navigation/native": "^6.0.16", "@react-navigation/native-stack": "^6.9.4", "@reduxjs/toolkit": "^1.9.7", "@twotalltotems/react-native-otp-input": "^1.3.11", "@types/react-native-base64": "^0.2.2", "@types/react-native-snap-carousel": "^3.8.5", "apollo-cache-inmemory": "1.6.3", "apollo-cache-persist": "0.1.1", "apollo-client": "2.6.4", "apollo-link": "1.2.12", "apollo-link-context": "1.0.18", "apollo-link-error": "1.1.11", "apollo-link-http": "1.5.15", "axios": "^1.2.0", "crypto-js": "^4.1.1", "date-fns": "^3.6.0", "deprecated-react-native-prop-types": "^5.0.0", "formik": "^2.2.9", "graphql": "^16.8.0", "graphql-tag": "2.10.1", "i18next": "^22.0.6", "jail-monkey": "^2.8.0", "moment": "^2.29.4", "patch-package": "^6.5.0", "pod-install": "^0.1.38", "postinstall-postinstall": "^2.1.0", "prop-types": "^15.8.1", "react": "18.2.0", "react-apollo": "3.0.1", "react-content-loader": "^6.2.1", "react-i18next": "^12.0.0", "react-native": "0.71.14", "react-native-actions-sheet": "^0.8.10", "react-native-animated-otp-input": "^0.1.7", "react-native-appsflyer": "6.12.2", "react-native-base64": "^0.2.1", "react-native-change-icon": "^5.0.0", "react-native-code-push": "^8.2.1", "react-native-config": "^1.5.3", "react-native-countdown-component": "^2.7.1", "react-native-country-picker-modal": "^2.0.0", "react-native-dashed-line": "^1.1.0", "react-native-date-picker": "^4.2.6", "react-native-device-info": "^14.0.0", "react-native-document-picker": "^9.0.1", "react-native-dropdown-picker": "^5.1.21", "react-native-dropdown-select-list": "^2.0.2", "react-native-element-dropdown": "^2.8.0", "react-native-error-boundary": "^1.2.5", "react-native-fast-image": "^8.6.3", "react-native-fbsdk-next": "^13.4.1", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "2.9.0", "react-native-google-places-autocomplete": "2.5.6", "react-native-image-crop-picker": "^0.42.0", "react-native-image-pan-zoom": "^2.1.12", "react-native-image-picker": "^7.2.2", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-linear-gradient": "^2.6.2", "react-native-maps": "1.15.0", "react-native-material-menu": "^2.0.0", "react-native-modal": "^13.0.1", "react-native-moengage": "^11.1.0", "react-native-network-logger": "^1.17.0", "react-native-orientation-locker": "^1.7.0", "react-native-otp-entry": "1.8.4", "react-native-otp-verify": "^1.1.8", "react-native-paper": "^5.13.1", "react-native-permissions": "^3.8.1", "react-native-pie": "^1.1.2", "react-native-popable": "^0.4.3", "react-native-ratings": "^8.1.0", "react-native-raw-bottom-sheet": "^2.2.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "3.6.1", "react-native-reanimated-carousel": "^3.5.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.4.1", "react-native-screens": "3.27.0", "react-native-share": "^11.1.0", "react-native-sms-retriever": "^1.1.1", "react-native-snap-carousel": "^3.9.1", "react-native-splash-screen": "^3.3.0", "react-native-star-rating": "^1.1.0", "react-native-svg": "15.0.0", "react-native-swipeable-item": "^2.0.9", "react-native-toast-message": "^2.1.5", "react-native-vector-icons": "^9.2.0", "react-native-video": "^5.2.1", "react-native-webview": "12.1.0", "react-native-youtube-iframe": "^2.2.2", "react-query": "^3.39.2", "react-redux": "^8.1.3", "rn-fetch-blob": "^0.12.0", "rn-range-slider": "2.0.4", "toggle-switch-react-native": "^3.3.0", "use-debounce": "^10.0.4", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/react": "^18.0.24", "@types/react-native": "^0.70.7", "@types/react-native-razorpay": "^2.2.4", "@types/react-native-star-rating": "^1.1.6", "@types/react-native-vector-icons": "^6.4.12", "@types/react-redux": "^7.1.27", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "^0.73.10", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "^4.9.3"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "reactNativePermissionsIOS": ["Camera", "LocationAccuracy", "LocationAlways", "LocationWhenInUse", "MediaLibrary", "Notifications", "PhotoLibrary", "PhotoLibraryAddOnly"], "resolutions": {"react-native-image-picker@^7.2.2": "patch:react-native-image-picker@npm%3A7.2.2#./.yarn/patches/react-native-image-picker-npm-7.2.2-acefa2f244.patch", "react-native@0.71.14": "patch:react-native@npm%3A0.71.14#./.yarn/patches/react-native-npm-0.71.14-625572a2a0.patch", "react-native@*": "patch:react-native@npm%3A0.71.14#./.yarn/patches/react-native-npm-0.71.14-625572a2a0.patch"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}
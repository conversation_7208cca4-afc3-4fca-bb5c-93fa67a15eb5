import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const RewardLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1900}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="10" rx="5" ry="5" width={100} height={100} />
        <Rect x={width - 110} y="10" rx="5" ry="5" width={100} height={100} />
        <Rect
          x="10"
          y="120"
          rx="5"
          ry="5"
          width={(width - 30) / 2}
          height="50"
        />
        <Rect
          x={width / 2}
          y="120"
          rx="5"
          ry="5"
          width={(width - 30) / 2}
          height="50"
        />
        <Rect x="10" y="180" rx="5" ry="5" width={width - 20} height={100} />
        <Rect x="10" y="290" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="450" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="610" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="770" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="930" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1090" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1250" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1410" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1570" rx="5" ry="5" width={width - 20} height={150} />
        <Rect x="10" y="1730" rx="5" ry="5" width={width - 20} height={150} />
      </ContentLoader>
    </ScrollView>
  );
};

export default RewardLoader;

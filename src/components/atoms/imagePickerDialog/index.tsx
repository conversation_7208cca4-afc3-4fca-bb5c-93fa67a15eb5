import React, {useEffect, useState, useMemo} from 'react';
import {
  View,
  TouchableOpacity,
  Platform,
  Alert,
  TouchableWithoutFeedback,
} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import Modal from 'react-native-modal';
import {
  check,
  request,
  PERMISSIONS,
  openSettings,
} from 'react-native-permissions';
import DeviceInfo from 'react-native-device-info';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {ImageIcon, Label, Spacer} from 'components/atoms';

const ImagePickerDialog = props => {
  const {visible, onConfirm, onClose} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [apiLevel, setApiLevel] = useState(0);

  useEffect(() => {
    DeviceInfo.getApiLevel().then(level => {
      setApiLevel(level);
    });
  }, []);

  const openCamera = () => {
    ImagePicker.openCamera({
      width: 300,
      height: 300,
      cropping: false,
    })
      .then(image => {
        onConfirm(image);
      })
      .catch(() => onClose());
  };

  const openGallery = () => {
    ImagePicker.openPicker({
      width: 300,
      height: 300,
      multiple: true,
      cropping: false,
    })
      .then(image => {
        onConfirm(image);
      })
      .catch(() => onClose());
  };

  const requestPermission = async type => {
    const gallery = type === 'gallery' ? true : false;
    const photoPermissions =
      Platform.OS === 'android'
        ? apiLevel < 33
          ? PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE
          : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
        : PERMISSIONS.IOS.PHOTO_LIBRARY;
    const cameraPermissions =
      Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.CAMERA
        : PERMISSIONS.IOS.CAMERA;
    const permission = gallery ? photoPermissions : cameraPermissions;
    request(permission).then(statuses => {
      check(permission).then(status => {
        if (status === 'granted') {
          gallery ? openGallery() : openCamera();
        } else {
          request(permission).then(result => {
            if (result === 'granted') {
              gallery ? openGallery() : openCamera();
            } else {
              setTimeout(() => {
                Alert.alert(
                  t('validations.permissionAllow'),
                  gallery
                    ? t('validations.permissionPhotoMsg')
                    : t('validations.permissionCameraMsg'),
                  [
                    {text: t('buttons.no')},
                    {
                      text: t('buttons.yes'),
                      style: 'cancel',
                      onPress: () => openSettings().catch(() => {}),
                    },
                  ],
                );
              }, 200);
            }
          });
        }
      });
    });
  };

  return (
    <Modal
      isVisible={visible}
      style={styles.modalStyle}
      onBackButtonPress={() => onClose()}>
      <View style={styles.modalOverlay}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.modalCloseBtnContainer}>
            <TouchableOpacity onPress={onClose} style={styles.modalCloseButton}>
              <ImageIcon icon="close" size="x7l" />
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
        <View style={styles.modalContent}>
          <TouchableOpacity
            style={styles.option}
            onPress={() => requestPermission('gallery')}>
            <ImageIcon icon="galleryGrey" size="xxxl" />
            <Spacer size="xms" type="Horizontal" />
            <Label
              text={t('otherText.gallery')}
              size="l"
              fontFamily="SemiBold"
              weight="600"
              color="text"
            />
          </TouchableOpacity>
          <View style={styles.lineStyle} />
          <TouchableOpacity
            style={styles.option}
            onPress={() => requestPermission('camera')}>
            <ImageIcon icon="cameraGrey" size="xxxl" />
            <Spacer size="xms" type="Horizontal" />
            <Label
              text={t('otherText.takePhoto')}
              size="l"
              fontFamily="SemiBold"
              weight="600"
              color="text"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default ImagePickerDialog;

import { Sizes } from 'common';
import { useMemo } from 'react';
import { Dimensions, Platform, useWindowDimensions } from 'react-native';
import { checkDevice } from 'utils/utils';

/**
 * Custom hook that provides device-dependent configuration values for React Native
 * @param {Function} checkDeviceFunc - Optional custom function to determine device type
 * @returns {Object} Configuration values based on device type
 */
const useDeviceConfig = () => {
  // Use React Native's useWindowDimensions hook to get responsive dimensions
  
  // Default check function uses width breakpoint to determine if device is tablet/large

  // Use provided check function or default
  
  const numColumns = useMemo(() => checkDevice() ? 4 : 2, [checkDevice]);
  const initialNumToRender = useMemo(() => checkDevice() ? 6 : 3, [checkDevice]);
  const maxToRenderPerBatch = useMemo(() => checkDevice() ? 4 : 2, [checkDevice]);
  const windowSize = useMemo(() => checkDevice() ? 7 : 5, [checkDevice]);
  const itemHeight = useMemo(() => checkDevice() ? Sizes.ex416 : Sizes.ex405, [checkDevice])
  return {
    numColumns,
    initialNumToRender,
    maxToRenderPerBatch,
    windowSize,
    itemHeight,
    isLargeDevice: checkDevice() // Including this can be helpful
  };
};

export default useDeviceConfig;
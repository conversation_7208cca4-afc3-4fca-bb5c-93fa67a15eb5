import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ReferEarn = () => {
  const {colors} = useTheme();
  const boxWidth = (width - 80) / 6;
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1930}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="0" rx="5" ry="5" width={width - 20} height={250} />
        <Rect
          x={width / 2 - 125}
          y="260"
          rx="5"
          ry="5"
          width={250}
          height={50}
        />
        <Rect
          x={width / 2 - 80}
          y="320"
          rx="5"
          ry="5"
          width={160}
          height={20}
        />
        <Rect x={boxWidth - 50} y="350" rx="5" ry="5" width="50" height="50" />
        <Rect x={boxWidth + 50} y="350" rx="5" ry="5" width="50" height="50" />
        <Rect x={boxWidth * 2 + 50} y="350" rx="5" ry="5" width="50" height="50" />
        <Rect x={boxWidth * 3 + 50} y="350" rx="5" ry="5" width="50" height="50" />
        <Rect x={boxWidth * 4 + 50} y="350" rx="5" ry="5" width="50" height="50" />
        <Rect x={boxWidth * 5 + 50} y="350" rx="5" ry="5" width="50" height="50" />
        <Rect
          x={width / 2 - 80}
          y="410"
          rx="5"
          ry="5"
          width={160}
          height={20}
        />
        <Rect x="10" y="440" rx="5" ry="5" width="150" height={110} />
        <Rect x="170" y="440" rx="5" ry="5" width={width - 170} height={50} />
        <Rect x="170" y="500" rx="5" ry="5" width={width - 170} height={50} />
        <Rect x="10" y="560" rx="5" ry="5" width={width - 20} height={330} />
        <Rect x="10" y="900" rx="5" ry="5" width={width - 20} height={330} />
        <Rect x="10" y="1240" rx="5" ry="5" width={width - 20} height={330} />
        <Rect x="10" y="1580" rx="5" ry="5" width={width - 20} height={330} />
      </ContentLoader>
    </ScrollView>
  );
};

export default ReferEarn;

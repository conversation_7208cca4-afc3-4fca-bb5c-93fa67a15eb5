import {View} from 'react-native';
import React, { useEffect } from 'react';
import {Label, Spacer} from 'components/atoms';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {SafeAreaView} from 'react-native-safe-area-context';
import stylesWithOutColor from './style';
import Button from '../button';
import {useTheme} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {useMemo} from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@types/local';
import { AnalyticsEvents } from 'components/organisms';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const errorPage = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  useEffect(()=>{
    AnalyticsEvents('ERROR', 'Error screen', {}, userInfo, isLoggedIn);
  },[])

  return (
    <SafeAreaView>
      <View>
        <FastImage source={Icons.error404} style={styles.mainView} />
      </View>
      <View style={styles.labelView}>
        <View>
          <Label
            weight="500"
            align="center"
            size="l"
            color="skyBlue17"
            text={'Seems Like There Is Some Error..'}
          />
          <Spacer size="xms" />
          <Label
            text={'Don’t Worry We Have Got You Covered'}
            align="center"
            weight="500"
            size="l"
            color="skyBlue17"
          />
        </View>
        <Spacer size="xms" />
        <View>
          <Button
            type="secondary"
            onPress={() => navigation.navigate('Tab', {screen: 'Shop'})}
            labelColor="whiteColor"
            style={styles.button1}
            text="Go to home page"
          />
          <Spacer size="xm" />
          <Button
            onPress={() => navigation.navigate('CategoryDetail', {categoryId: 2566})}
            style={styles.button2}
            text="Go to offer page"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default React.memo(errorPage);

import React, {useCallback} from 'react';
import {TouchableOpacity, View, SectionList} from 'react-native';
import {useNavigation, useTheme} from '@react-navigation/native';
import DashedLine from 'react-native-dashed-line';
import {
  ImageIcon,
  Label,
  Spacer,
  Separator,
  WithBackground,
} from 'components/atoms';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {useMemo} from 'react';
import Button from 'components/molecules/button';
import Modal from 'react-native-modal';

type Props = {
  visible: boolean;
  onClose: () => void;
  couponList: couponList[];
  couponItemClick: (data: couponItem) => void;
};

const CouponListModal = (props: Props) => {
  const {visible, onClose, couponList, couponItemClick} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const navigation = useNavigation();
  const onPressExplore = () => {
    navigation.navigate('CategoryDetail', {categoryId: 2566});
    onClose();
  };

  const couponRenderHeader = useCallback((title: string) => {
    return (
      <View style={styles.couponRenderView}>
        <View style={styles.flexView}>
          <Separator thickness="xs" color="smoothGrey" />
        </View>
        <Spacer size="l" type="Horizontal" />
        <Label size="m" color="grey" text={title} fontFamily="Regular" />
        <Spacer size="l" type="Horizontal" />
        <View style={styles.flexView}>
          <Separator thickness="xs" color="smoothGrey" />
        </View>
      </View>
    );
  }, []);

  const couponRenderItem = useCallback(({item}: {item: couponItem}) => {
    return (
      <WithBackground
        image="couponValid"
        imageStyle={styles.couponValidView}
        style={styles.couponValidSubView}>
        <TouchableOpacity
          testID="tOCouponRenderItem"
          style={styles.couponCardContentContainer}
          onPress={() => couponItemClick(item)}>
          <View style={styles.cardSection}>
            <Label
              text={item?.coupon_code}
              align="left"
              color="background"
              size="mx"
              fontFamily="Bold"
              style={styles.discountLabelView}
            />
            <Label
              text={item?.description}
              align="left"
              color="background"
              size="m"
              fontFamily="Regular"
              style={styles.discountLabelView}
            />
          </View>
          <View style={styles.cardDateSection}>
            <Label
              text={t('otherText.expireDate')}
              color="background"
              size="xm"
              align="center"
              fontFamily="Medium"
            />
            <Label
              text={item.expiry_date}
              color="background"
              size="xm"
              align="center"
              fontFamily="Medium"
            />
          </View>
          <Spacer size="xm" type="Horizontal" />
        </TouchableOpacity>
      </WithBackground>
    );
  }, []);

  return (
    <Modal
      style={styles.container}
      onBackdropPress={() => onClose()}
      isVisible={visible}>
      <View style={styles.subView}>
        <View style={styles.excitingView}>
          {couponList.length > 0 ? (
            <Label
              size="mx"
              text={t('cart.nextOrder')}
              color="text"
              fontFamily="Medium"
              textTransform="capitalize"
            />
          ) : (
            <View />
          )}
          <TouchableOpacity
            testID="tOCouponRenderItemOnClose"
            onPress={() => onClose()}>
            <ImageIcon icon={'cross'} size="xxl" tintColor="black" />
          </TouchableOpacity>
        </View>
        {/* <Spacer size="xl" />
        <DashedLine
          dashLength={4}
          dashThickness={1.5}
          dashColor={colors.skyBlue5}
        /> */}
        <View style={styles.flexView}>
          {couponList.length > 0 ? (
            <SectionList
              keyExtractor={(_, index) => index.toString()}
              sections={couponList}
              // renderSectionHeader={couponRenderHeader}
              renderSectionHeader={({section: {title}}) =>
                couponRenderHeader(title)
              }
              ItemSeparatorComponent={() => <Spacer size="xxl" />}
              renderItem={couponRenderItem}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View>
              {couponRenderHeader(t('otherText.noCouponAvailable'))}

              <View style={styles.noCouponAvailView}>
                <FastImage
                  resizeMode="contain"
                  style={styles.noCouponAvailBackground}
                  source={Icons.noCouponAvailable}
                />
                <FastImage
                  style={styles.image}
                  source={Icons.noCouponAvailableGif}
                />
              </View>
              <Label
                text={t('profile.couponEmptyTxt')}
                color="text"
                fontFamily="Medium"
                align="center"
                textTransform="capitalize"
                style={styles.titleStyle}
              />
              <Spacer size="l" />

              <Button
                onPress={onPressExplore}
                style={styles.btnStyle}
                radius="m"
                type="secondary"
                labelSize="mx"
                labelColor="whiteColor"
                text={t('buttons.exploreNow')}
              />
            </View>
          )}
        </View>
        <Spacer size="m" />
      </View>
    </Modal>
  );
};

export default React.memo(CouponListModal);

import {StyleSheet} from 'react-native';
import {Sizes} from 'common';

const styles = (
  colors: any,
  thickness: keyof typeof Sizes | undefined,
  color: keyof Theme['colors'] | undefined,
) =>
  StyleSheet.create({
    container: {
      width: '100%',
      height: thickness ? Sizes[thickness] : 1,
      backgroundColor: color ? colors[color] : colors.separatorColor,
    },
    deviedLine: {
      borderWidth: thickness ? Sizes[thickness] : 0.8,
      height: Sizes.screenWidth * 0.06,
      width: thickness ? Sizes[thickness] : 1,
      borderColor: color ? colors[color] : colors.separatorColor,
    },
  });

export default styles;

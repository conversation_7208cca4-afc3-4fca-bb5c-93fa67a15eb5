import React, {memo, useCallback, useEffect, useState} from 'react';
import {useMemo} from 'react';

import {
  TouchableOpacity,
  View,
  Image,
  ViewProps,
  Pressable,
  BackHandler,
} from 'react-native';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import {
  Label,
  Link,
  Tag,
  ImageIcon,
  Spacer,
  SearchInput,
} from 'components/atoms';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {navigate, reset, resetNavigation} from 'utils/navigationRef';
import {useTheme} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {showInfoMessage} from 'utils/show_messages';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import ErrorHandler from 'utils/ErrorHandler';
import {Sizes} from 'common';
import { RootState } from '@types/local';
import { MEMBERSHIP_TYPE } from 'components/atoms/changeIconModal/constants';
type Props = {
  text?: string;
  subTitle?: string;
  value?: string | number;
  link?: string;
  backButton?: boolean;
  heartIcon?: boolean;
  shareIcon?: boolean;
  searchIcon?: boolean;
  tag?: boolean;
  bagIcon?: boolean;
  userIcon?: boolean;
  customIcon?: boolean;
  userIconPress?: () => void;
  linkText?: boolean;
  searchBar?: boolean;
  hadarLogo?: boolean;
  onPress?: () => void;
  onShare?: () => void;
  likes?: boolean;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  searchNavigation?: boolean;
  onChangeSearched?: (text: string) => void;
  searchAutoFocus?: boolean;
  style?: ViewProps['style'];
  tagStyle?: ViewProps['style'];
  linkStyle?: ViewProps['style'];
  labelStyle?: ViewProps['style'];
  shareNews?: () => void;
  likeNews?: () => void;
  bookmarkNews?: () => void;
  onPressNavigation?: () => void;
  onPressSearchIcon?: () => void;
  setSearchBar?: (val: boolean) => void;
  newsId?: number;
  useInsets?: boolean;
  extraItems?: () => React.ReactElement;
  tintColorGOBack?: keyof Theme['colors'];
  homePage?: boolean;
  searchPage?: boolean;
  optionalContent?: any;
  optionalComponentHeight?: number;
  isRedious?: boolean;
  onPressLogo?: () => void;
  searchColor?: keyof Theme['colors'];
  bottomShadow?: boolean;
  placeHolder?: string;
  // placeHolderText? : boolean;
};
const Header = ({
  text,
  subTitle,
  link,
  backButton,
  heartIcon,
  shareIcon,
  searchIcon,
  tag,
  bagIcon,
  userIcon,
  userIconPress,
  searchBar,
  customIcon = false,
  onPress,
  // onBackPress,
  onShare,
  hadarLogo = false,
  navigation,
  onPressNavigation,
  onPressSearchIcon,
  style,
  searchNavigation = true,
  searchAutoFocus = false,
  linkText = false,
  tagStyle,
  labelStyle,
  extraItems,
  value,
  tintColorGOBack,
  useInsets = false,
  onChangeSearched = () => {},
  homePage,
  searchPage,
  isRedious,
  setSearchBar,
  optionalContent,
  optionalComponentHeight,
  onPressLogo,
  searchColor,
  bottomShadow,
  placeHolder,
  // placeHolderText
}: Props) => {
  const TAG = 'HEADER';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const insets = useSafeAreaInsets();
  const {cartCount, userInfo, isLoggedIn} = useSelector(
    (state: RootState) => state.app,
  );
  const firstInitial = userInfo?.firstname?.trim().charAt(0);
  const lastInitial = userInfo?.lastname?.trim().charAt(0);
  const {membership_type: membershipType} = useSelector((state: RootState) => state.app);

  const cartIcon = () => {
    if (bagIcon === true) {
      return (
        <View style={styles.icons}>
          <ImageIcon size="xxl" icon="cart" tintColor="text" />
          {cartCount ? (
            <View style={styles.cartBag}>
              <Label
                size="mx"
                color="whiteColor"
                fontFamily="Regular"
                text={cartCount}
                weight="500"
              />
            </View>
          ) : null}
        </View>
      );
    }
    return <View />;
  };

  const profileIcon = () => {
    if (userIcon === true) {
      return (
        <>
          <View style={styles.icons1}>
          {!isLoggedIn ? (
          <View style={{flexDirection:'row'}}>
          <ImageIcon tintColor="text" size="xxl" icon="guestUser" />
          <Label
                size="l"
                color='text'
                text={t('profile.login')}
                fontFamily="Medium"
                align="center"
                style={styles.profileTxt1}
              />
          </View>
        ) : (
          <View style={{flexDirection: 'row'}}>
            {!firstInitial && !lastInitial ? (
              <ImageIcon tintColor="categoryTitle" size="xxl" icon="userIc" />
            ) : (
              <View style={[
                styles.userView,
                membershipType !== MEMBERSHIP_TYPE.ANDROID_PREMIUM && { backgroundColor: colors.categoryTitle }
              ]}>
                {membershipType ===MEMBERSHIP_TYPE.ANDROID_PREMIUM && <ImageIcon  size="xxl" icon="premiumBG" style={styles.premiumgBG} />}
                <Label
                  size="m"
                  color={membershipType ===MEMBERSHIP_TYPE.ANDROID_PREMIUM?"secondary":"whiteColor"}
                  textTransform="uppercase"
                  text={`${firstInitial ? firstInitial : ''}${
                    lastInitial ? lastInitial : ''
                  }`}
                  fontFamily="Medium"
                  align="center"
                  style={styles.profileTxt}
                />
              </View>
            )}
            <Label
              size="l"
              color='text'
              text={t('profile.you')}
              fontFamily="Medium"
              align="center"
              style={styles.profileTxt1}
            />
          </View>
        )}
          </View>
        </>
      );
    }
    return <View />;
  };

  const handlePress = useCallback(() => {
    if (onPressNavigation) {
      onPressNavigation();
    } else {
      // Check if we can get an immediate response
      // InteractionManager.runAfterInteractions(() => {
        navigation?.goBack();
      // });
    }
    onPressLogo?.();
  }, [onPressNavigation, onPressLogo, navigation]);

  const handleSearchPress = useCallback(() => {
    if (onPressSearchIcon) {
      onPressSearchIcon();
    } else {
      navigate('SearchProduct');
    }
  }, [onPressSearchIcon]);

  const handleWishListPress = useCallback(() => {
    const prevRoutes = navigation?.getState().routes;
    if (isLoggedIn) {
      navigate('WishList');
    } else {
      showInfoMessage(t('toastMassages.wishlistLogin'));
      navigate('Login', {
        nextRouterState: {
          index: 0,
          routes: prevRoutes,
        },
      });
    }
  }, [isLoggedIn]);
  useEffect(() => {
    const backAction = () => {
      handlePress(); // Reuse the handlePress function to navigate back
      return true; // Prevent default behavior (exit the app)
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove(); // Clean up the event listener
  }, [handlePress]);
  return (
    <View style={[bottomShadow && styles.shadowParent]}>
      <View
        style={[
          styles.container,
          bottomShadow && styles.shadow,
          useInsets && {marginTop: insets.top},
          isRedious && styles.borderRadiusStyle,
          style,
        ]}>
        {homePage ? (
          <TouchableOpacity
            onPress={handlePress}
            style={styles.goBackButton}
            activeOpacity={0.7}>
            <Spacer size="s" />
            <ImageIcon style={styles.logoView} icon="newDentalKartLogo" />
            {backButton && (
              <ImageIcon
                tintColor={tintColorGOBack ? colors[tintColorGOBack] : 'text'}
                style={styles.goBackImage}
                resizeMode="contain"
                icon="arrowLeft"
              />
            )}
            {hadarLogo && (
              <Image source={Icons.headerLogo} resizeMode="contain" />
            )}
          </TouchableOpacity>
        ) : null}
        <View style={styles.flex}>
          {!searchBar ? (
            <View style={styles.goBackView}>
              <TouchableOpacity
                onPress={handlePress}
                style={styles.goBackButton}>
                {backButton === true ? (
                  <ImageIcon
                    tintColor={
                      tintColorGOBack ? colors[tintColorGOBack] : 'text'
                    }
                    style={styles.goBackImage}
                    resizeMode="contain"
                    icon="arrowLeft"
                  />
                ) : null}
                {hadarLogo ? (
                  <Image source={Icons.headerLogo} resizeMode="contain" />
                ) : null}
              </TouchableOpacity>

              <View style={[styles.flex, styles.headerTextContainer]}>
                {customIcon && (
                  <>
                    <TouchableOpacity
                      onPress={() => resetNavigation('Tab')}   
                      >
                      <FastImage
                        style={styles.dentalIcon}
                        resizeMode="contain"
                        source={Icons.dentalKartNavigation}
                      />
                    </TouchableOpacity>
                    <Spacer size="s" type="Horizontal" />
                  </>
                )}
                <View style={styles.flex}>
                  {text ? (
                    <Label
                      fontFamily="Medium"
                      size="l"
                      color="text"
                      text={text}
                      // textTransform="capitalize"
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    />
                  ) : null}
                  {subTitle ? (
                    <Label
                      fontFamily="Regular"
                      size="m"
                      color="grey"
                      textTransform="capitalize"
                      text={subTitle}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    />
                  ) : null}
                </View>
              </View>
            </View>
          ) : null}
          {searchBar ? (
            <ErrorHandler
              componentName={`${TAG} SearchInput`}
              onErrorComponent={<View />}>
              <SearchInput
                searchPage={searchPage}
                onChangeText={onChangeSearched}
                onBackPress={() => navigation?.goBack()}
                withDebounce
                autoFocus={searchAutoFocus}
                value={value}
                placeHolder={placeHolder}
                // placeHolderText={true}
              />
            </ErrorHandler>
          ) : null}
        </View>
        {!searchBar ? (
          <View style={styles.iconsHeader}>
            {searchIcon === true ? (
              <TouchableOpacity
                onPress={handleSearchPress}
                style={styles.icons}>
                <ImageIcon
                  size="xxl"
                  icon="search"
                  tintColor={searchColor ? searchColor : 'text'}
                />
              </TouchableOpacity>
            ) : null}
            {heartIcon === true ? (
              <TouchableOpacity
                onPress={handleWishListPress}
                style={styles.icons}>
                <ImageIcon size="xxl" icon="heart" tintColor="text" />
              </TouchableOpacity>
            ) : null}
            {shareIcon === true ? (
              <TouchableOpacity onPress={onShare} style={styles.icons}>
                <ImageIcon size="xxl" icon="shareIcon" tintColor="text" />
              </TouchableOpacity>
            ) : null}

            {isLoggedIn ? (
              <>
                <Pressable
                  onPress={() => userIconPress?.()}
                  style={({pressed}) => [
                    styles.pressable,
                    pressed && styles.pressedStyle,
                  ]}>
                  {profileIcon()}
                </Pressable>
                <TouchableOpacity
                  onPress={() => navigate('Cart')}
                  style={{
                    marginRight: Sizes.m,
                  }}>
                  {cartIcon()}
                </TouchableOpacity>
              </>
            ) : (
              <>
                <Pressable
                  onPress={() => userIconPress?.()}
                  style={({pressed}) => [
                    styles.pressable,
                    pressed && styles.pressedStyle,
                  ]}>
                  {profileIcon()}
                </Pressable>
                <TouchableOpacity
                  onPress={() => navigate('Cart')}
                  style={{
                    marginRight: Sizes.m,
                  }}>
                  {cartIcon()}
                </TouchableOpacity>
              </>
            )}

            {linkText === true ? <Link text={link} onPress={onPress} /> : null}
          </View>
        ) : null}
        {extraItems && <View style={styles.extraItems}>{extraItems()}</View>}
        {tag && <Tag labelStyle={labelStyle} style={tagStyle} label={tag} />}
      </View>
      {optionalContent && (
        <View style={[styles.optional, {height: optionalComponentHeight}]}>
          {optionalContent}
        </View>
      )}
    </ View>
  );
};
export default memo(Header);

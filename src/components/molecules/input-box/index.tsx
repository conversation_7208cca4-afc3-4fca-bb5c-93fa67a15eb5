import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {TextInput, TextInputProps, View} from 'react-native';
import {Label} from 'components/atoms';
import {useMemo} from 'react';

import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';

type Props = {
  style?: TextInputProps['style'];
  textStyle?: TextInputProps['textStyle'];
  placeholderTextColor?: TextInputProps['placeholderTextColor'];
  textInputColor?: TextInputProps['placeholderTextColor'];
  secureTextEntry?: boolean;
  maxLength?: TextInputProps['maxLength'];
  keyboardType?: TextInputProps['keyboardType'];
  defaultValue?: TextInputProps['defaultValue'];
  numberOfLines?: TextInputProps['numberOfLines'];
  multiline?: boolean;
  editable?: boolean;
} & InputBoxType;

const InputBox = ({
  id,
  placeholder,
  onChangeText,
  value,
  defaultValue,
  error,
  style,
  secureTextEntry,
  numberOfLines,
  maxLength = undefined,
  keyboardType = 'phone-pad',
  placeholderTextColor = '#353B50',
  multiline,
  textInputColor,
  editable,
  textStyle,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <>
      <View style={[styles.container, error && styles.errorBorder, style]}>
        <TextInput
          id={id}
          autoComplete="off"
          style={[
            styles.input,
            textInputColor,
            textStyle,
            numberOfLines ? {height: Sizes.xxl * numberOfLines} : {},
          ]}
          keyboardType={keyboardType}
          maxLength={maxLength}
          placeholder={placeholder}
          onChangeText={onChangeText}
          value={value}
          defaultValue={defaultValue}
          placeholderTextColor={placeholderTextColor}
          secureTextEntry={secureTextEntry}
          numberOfLines={numberOfLines}
          multiline={multiline}
          editable={editable}
          allowFontScaling={false}
        />
      </View>
      {error && (
        <View style={styles.errorContainer}>
          <Label color="textError" size="m" text={error} />
        </View>
      )}
    </>
  );
};

export default InputBox;

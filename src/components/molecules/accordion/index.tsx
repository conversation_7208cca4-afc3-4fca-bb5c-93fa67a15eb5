import React, {useState} from 'react';
import {Platform, TouchableOpacity, UIManager, View} from 'react-native';
import stylesWithOutColor from './style';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';
import {useMemo} from 'react';

type Props = {
  selected?: Array<AddOnsType['id']>;
  onPress: () => void;
  data?: any;
  title?: string;
  renderItem?: any;
  isOpen?: boolean;
} & AddOnsType;

const Accordion = ({data, title, renderItem, isOpen = false}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [expanded, setExpanded] = useState(isOpen);
  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  return (
    <View>
      <TouchableOpacity
        style={[styles.row, !expanded && styles.label]}
        onPress={() => setExpanded(!expanded)}>
        <Label
          size="l"
          lineHeight={Sizes.l}
          color="textLight"
          weight="600"
          text={title}
        />
        <ImageIcon size="xx" icon={expanded ? 'minusIcon' : 'addIcon'} />
      </TouchableOpacity>
      <Spacer type="Vertical" size="xm" />
      <View style={styles.parentHr} />
      {expanded &&
        data.map((item: string, index: number | string) => {
          return <View key={index?.toString()}>{renderItem(item, title)}</View>;
        })}
    </View>
  );
};

export default React.memo(Accordion);

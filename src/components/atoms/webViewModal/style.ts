import {Sizes} from 'common';
import {StyleSheet, Platform} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalCont: {
      backgroundColor: colors.background,
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: '100%',
    },
    modelBG: {
      backgroundColor: colors.background,
    },
    subView: {
      width: '100%',
      backgroundColor: colors.background,
      height: Platform.OS === 'ios' ? '80%' : '100%',
    },
    fullSizesModel: {
      width: Sizes.x5l,
      height: Sizes.x5l,
      borderRadius: Sizes.x4l,
      padding: Sizes.s,
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'flex-end',
    },
    container: {
      flex: Sizes.x,
    },
  });

export default styles;

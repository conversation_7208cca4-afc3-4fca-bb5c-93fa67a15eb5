const Icons = {
  flagIndia: require('images/flagIndia.webp'),
  signIn: require('images/signIn.webp'), 
  userIc: require('images/userIc.webp'),
  checkboxChecked: require('images/check-box-checked.webp'),
  check: require('images/check.webp'),
  checkboxUnchecked: require('images/check-box-unchecked.webp'),
  sampleProductImage: require('images/sample-product-image.webp'),
  profileIcon: require('images/profile.webp'),
  calendar: require('images/calendar.webp'),
  birthdayCalendar: require('images/calender2.webp'),
  loginBackground: require('images/onboardingBackground.webp'),
  nextArrow: require('images/nextArrow.webp'),
  rightArrowCircle: require('images/rightArrowCircle.webp'),
  otpBackgroundImage: require('images/otpBackground.webp'),
  verifyImage: require('images/verify.webp'),
  emailIcon: require('images/emailicon.webp'),
  lockIcon: require('images/lockIcon.webp'),
  userIcon: require('images/userIcon.webp'),
  dentalkartLogo: require('images/dentalkartLogo.webp'),
  goBack: require('images/goBack.webp'),
  dentalKartNavigation: require('images/dentalKartNavigation.webp'),
  headerLogo: require('images/headerLogo.webp'),
  searchIcon: require('images/searchIconHeader.webp'),
  bagIcon: require('images/bagIcon.webp'),
  heartIcon: require('images/heartIcon.webp'),
  heartLight: require('images/heartLight.webp'),
  shareIcon: require('images/shareIcon.webp'),
  newShareIcon: require('images/bookMark.webp'),
  shareFillIcon: require('images/shareFill.webp'),
  shareOutline: require('images/shareOutline.webp'),
  forwardIcon: require('images/forward.webp'),
  medicalGowns: require('images/medicalGowns.webp'),
  roundIcon: require('images/roundIcon.png'),
  crossIcon: require('images/crossIcon.png'),
  underPrizeIcon: require('images/youth.png'),
  pulsImg: require('images/puls.png'),
  profileBottam: require('images/profileBottam.webp'),
  news: require('images/news.webp'),
  categories: require('images/categories.webp'),
  shops: require('images/shop.webp'),
  addIcon: require('images/addIcon.webp'),
  addImagePlaceholder: require('images/addImagePlaceholder.webp'),
  minusIcon: require('images/minus.webp'),
  ratingIcons: require('images/ratingIcons.webp'),
  ratingGif: require('images/ratingGif.gif'),
  copyGif: require('images/copygif.gif'),
  dislikeFilled: require('images/dislikeFilled.webp'),
  likeFilled: require('images/likeFilled.webp'),
  wentWrongGif: require('images/wentWrong.gif'),
  pdpRatingIcon: require('images/pdpRatingIcon.webp'),
  filterIcons: require('images/filter.webp'),
  sortIcons: require('images/sort.webp'),
  closeIcons: require('images/closeIcon.webp'),
  close: require('images/close.webp'),
  cross: require('images/cross.webp'),
  xRedIcon: require('images/xRedIcon.webp'),
  decrementIcon: require('images/decrement.webp'),
  incrementIcon: require('images/increment.webp'),
  locationIcon: require('images/location.webp'),
  nextVectorIcon: require('images/nextVactorIcon.webp'),
  maskIcon: require('images/mask.webp'),
  bigStarIcon: require('images/bigStar.webp'),
  binIcon: require('images/bin.webp'),
  editIcon: require('images/editIcon.webp'),
  edit: require('images/edit.webp'),
  moreIcon: require('images/more.webp'),
  cartBucketIcon: require('images/cartBucket.webp'),
  upArrowIcon: require('images/upArrow.webp'),
  starIcon: require('images/starSingle.webp'),
  starRow: require('images/starRow.webp'),
  cartBigIcon: require('images/cartBig.webp'),
  locationGroupIcon: require('images/locationGroup.webp'),
  orderIcon: require('images/order.webp'),
  ticketIcon: require('images/ticket.webp'),
  returnIcon: require('images/return.webp'),
  rewardsIcon: require('images/rewards.webp'),
  logOutIcon: require('images/logout.webp'),
  newsBannerIcon: require('images/newsBanner.webp'),
  newsItemIcon: require('images/newsItem.webp'),
  newsbigBannerIcon: require('images/newsbigBanner.webp'),
  like: require('images/like.webp'),
  likeIcon: require('images/ThumbsUp.webp'),
  dislike: require('images/disLike.webp'),
  saveIcon: require('images/save.webp'),
  orderSuccessIcon: require('images/succesCheck.webp'),
  deviceIcon: require('images/device.webp'),
  likeBlank: require('images/likeblank.webp'),
  bookmarkIcon: require('images/bookmark.webp'),
  newBookmarkIcon: require('images/bookmarkNew.webp'),
  multimediaIcon: require('images/playButton.webp'),
  packageIcon: require('images/package.webp'),
  trackFillIcon: require('images/trackFill.webp'),
  tracknullIcon: require('images/tracknull.webp'),
  starGroupNullIcon: require('images/starGroupNull.webp'),
  downloadButtonIcon: require('images/download.webp'),
  phone: require('images/phone.webp'),
  mobileIcon: require('images/mobileIcon.webp'),
  pdfIcon: require('images/pdf.webp'),
  findIcon: require('images/findIcon.webp'),
  ProductIcon: require('images/Product_icon.webp'),
  brandIcon: require('images/Brand_icon.webp'),
  barsFilterIcon: require('images/barsFilter.webp'),
  nextBlueIcon: require('images/nextBlue.webp'),
  membershipIcon: require('images/Membership.webp'),
  membershipRupeeIcon: require('images/MembershipRupee.webp'),
  membershipWorkIcon: require('images/MembershipWork.webp'),
  notfoundIcon: require('images/notfound.webp'),
  noDatafoundIcon: require('images/noData_Found.png'),
  defaultIcon: require('images/defaultIcon.webp'),
  premiumIcon: require('images/premiumIcon.webp'),
  savingsIcon: require('images/savings.webp'),
  benefitsIcon: require('images/benefits.webp'),
  socialResposiblatyIcon: require('images/socialResposiblaty.webp'),
  MembershipBannerIcon: require('images/MembershipBanner.webp'),
  refreshIcon: require('images/refresh.webp'),
  bitcoinIcon: require('images/bitcoin.webp'),
  helpIcon: require('images/help.webp'),
  helpCenterIcon: require('images/helpCenterIcon.webp'),
  helpCenterHelpIcon: require('images/helpCenterHelpIcon.webp'),
  questionIcon: require('images/question.webp'),
  creditIcon: require('images/notCod.webp'),
  whatsAppIcon: require('images/whatsapp.webp'),
  whatsappSmallIcon: require('images/whatsappSmall.webp'),
  eyeShowIcon: require('images/eyeShow.webp'),
  eyeHideIcon: require('images/eyeHide.webp'),
  backGroundWithoutLogoIcon: require('images/backGroundWithoutLogo.webp'),
  googleIcon: require('images/google.webp'),
  codImgIcon: require('images/codImg.webp'),
  deliveryTruckIcon: require('images/deliveryTruck.webp'),
  informationIcon: require('images/information.webp'),
  info: require('images/info.webp'),
  checkboxRoundIcon: require('images/checkboxRound.webp'),
  shoppingBagIcon: require('images/shoppingBag.webp'),
  notFoundIcon: require('images/not-found.webp'),
  heartNotLikedIcon: require('images/heart.webp'),
  heartLikedIcon: require('images/heart-liked.webp'),
  downArrowIcon: require('images/down-arrow.webp'),
  arrowRight: require('images/arrowRight.webp'),
  commentIcon: require('images/comment.webp'),
  sendIcon: require('images/send.webp'),
  warningBold: require('images/warningBold.webp'),
  verifiedIcon: require('images/verified-user.webp'),
  checkVerifiedNew: require('images/checkVerifiedNew.webp'),
  roundCheckedIcon: require('images/circle.webp'),
  circleGradient: require('images/gradientCircle.webp'),
  radioUnCheckedIcon: require('images/radio.webp'),
  partnerShip: require('images/partnerShipBG.webp'),
  becomeSellerBG: require('images/becomeSellerBG.webp'),
  dentalKartBG: require('images/dentalKartBG.webp'),
  registerBG: require('images/registerBG.webp'),
  faqBG: require('images/faqBG.webp'),
  copyIcon: require('images/copy.webp'),
  homeAddressIcon: require('images/home-address.webp'),
  pinCodeIcon: require('images/pincode.webp'),
  mapPin: require('images/mapPin.webp'),
  solidUserGroupIcon: require('images/solidUserGroup.webp'),
  bi_PinterIcon: require('images/bi_pinterest.webp'),
  mdi_linkedinIcon: require('images/linkedin.webp'),
  vectorIcon: require('images/Vector.webp'),
  imageGoldCoinIcon: require('images/imageGoldCoin.webp'),
  newCopyIcon: require('images/ic_outline-file-copy.webp'),
  ri_instagramFillIcon: require('images/ri_instagram-fill.webp'),
  ri_whatsappIcon: require('images/ri_whatsapp-fill.webp'),
  uil_facebookIcon: require('images/uil_facebook.webp'),
  shareNewIcon: require('images/shareNew.webp'),
  newIconShare: require('images/newShareIcon.webp'),
  newShare: require('images/shares.webp'),
  oopsIcon: require('images/oops.webp'),
  arrowLeft: require('images/arrow-left.webp'),
  cart: require('images/cart.webp'),
  emptyCart: require('images/emptyCart.gif'),
  newSimilarGif: require('images/newSimilarGif.gif'),
  crackers: require('images/crackers.gif'),
  emptyWishlist: require('images/emptyWishlist.gif'),
  emptyAddressGif: require('images/emptyAddressGif.gif'),
  emptyReturnBG: require('images/emptyReturnBG.webp'),
  emptyOrderBG: require('images/emptyOrderBG.webp'),
  emptyAddressBG: require('images/emptyAddressBG.webp'),
  emptyReturn: require('images/emptyReturn.gif'),
  emptyOrder: require('images/emptyOrder.gif'),
  heart: require('images/heart.webp'),
  heartFilled: require('images/heartFilled.webp'),
  search: require('images/search.webp'),
  coin: require('images/coin.webp'),
  star: require('images/star.webp'),
  emptyStar: require('images/ratingStarEmpty.webp'),
  filledStar: require('images/fillStartIcon.webp'),
  phoneIcon: require('images/phone.webp'),
  memberShipBanner: require('images/memberShipBanner.webp'),
  memberShipBanner1: require('images/memberShipBanner1.webp'),
  googleLogo: require('images/google.webp'),
  lock: require('images/lock.webp'),
  whatsapp: require('images/whatsapp.webp'),
  arrowBottom: require('images/arrowBottom.webp'),
  viewHide: require('images/viewHide.webp'),
  newphone: require('images/newPhone.webp'),
  newmail: require('images/newmail.webp'),
  newUser: require('images/newUser.webp'),
  guestUser: require('images/guestUser.png'),
  premiumBG: require('images/premiumBG.webp'),
  dentalSuccess: require('images/dentalSuccess.png'),
  successGif: require('images/successGif.gif'),
  share: require('images/share.webp'),
  bestSeller: require('images/bestSeller.webp'),
  giftbox: require('images/giftbox.webp'),
  rewardBanner: require('images/rewardBanner.webp'),
  rewardBanner1: require('images/rewardBanner1.webp'),
  pennyBanner: require('images/pennyBanner.webp'),
  pennyBanner1: require('images/pennyBanner1.webp'),
  shopMoreBanner: require('images/shopMoreBanner.webp'),
  shopMoreBanner1: require('images/shopMoreBanner1.webp'),
  gift: require('images/gift.webp'),
  wallet: require('images/Wallet.webp'),
  arrowUp: require('images/arrowUp.webp'),
  couponExpired: require('images/couponExpired.webp'),
  couponUsed: require('images/couponUsed.webp'),
  couponValid: require('images/couponValid.webp'),
  coupon: require('images/coupon.webp'),
  couponCode: require('images/couponCode.webp'),
  couponCodeApplied: require('images/couponCodeApplied.webp'),
  couponPercent: require('images/couponPercent.webp'),
  helpBanner: require('images/helpBanner.webp'),
  checkRightIcon: require('images/checkRightIcon.webp'),
  deliveryTruck: require('images/deliveryTruck.webp'),
  moneyBag: require('images/moneyBag.webp'),
  clickTapIcon: require('images/clickTapIcon.webp'),
  fluentForm: require('images/fluentForm.webp'),
  rightCheckDarkBlueIcon: require('images/rightCheckDarkBlueIcon.webp'),
  rightCheckIcon: require('images/rightGreenCheckIcon.webp'),
  solarBox: require('images/solarBox.webp'),
  starFill: require('images/starFill.webp'),
  starOutline: require('images/starOutline.webp'),
  Basket: require('images/Basket.webp'),
  codTag: require('images/codTag.webp'),
  atWarehouseActive: require('images/atWarehouseActive.webp'),
  atWarehouseInActive: require('images/atWarehouseInActive.webp'),
  deliveredActive: require('images/deliveredActive.webp'),
  deliveredInActive: require('images/deliveredInActive.webp'),
  inTransInActive: require('images/inTransInActive.webp'),
  inTransitActive: require('images/inTransitActive.webp'),
  orderConfirmedActive: require('images/orderConfirmedActive.webp'),
  outForDeliveryActive: require('images/outForDeliveryActive.webp'),
  outForDeliveryInActive: require('images/outForDeliveryInActive.webp'),
  shippedActive: require('images/shippedActive.webp'),
  shippedInActive: require('images/shippedInActive.webp'),
  dottedBorder: require('images/dotted.webp'),
  orderReturnTerms: require('images/thumbsUpImage.webp'),
  thumbsUp: require('images/thumbsUpp.webp'),
  microPhone: require('images/microphone.webp'),
  refund: require('images/inActiveMoney.webp'),
  refundInactive: require('images/repairedActive.webp'),
  requestInActive: require('images/inActiveRequestApproved.webp'),
  requestActive: require('images/requestApproved.webp'),
  defaultImage: require('images/defaultImage.webp'),
  picUpComplete: require('images/pickUpComplete.webp'),
  picUpfaild: require('images/pickUpComplete.webp'),
  pickImage: require('images/pickImage.webp'),
  requestRaiseD: require('images/Request-raised--deactivated.webp'),
  requestunRaise: require('images/requestRaised.webp'),
  moveWishListIcon: require('images/moveWishListIcon.webp'),
  addWishlist: require('images/addWishlist.webp'),
  cancellation: require('images/cancellation.webp'),
  rupeeCircle: require('images/rupeeCircle.webp'),
  replacement: require('images/replacement.webp'),
  genuineProduct: require('images/genuineProduct.webp'),
  rupeeCircleWithOutBg: require('images/rupeeCircleWithOutBg.webp'),
  bank: require('images/bank.webp'),
  infoCircle: require('images/infoCircle.webp'),
  creditCardOutline: require('images/creditCardOutline.webp'),
  timeLine: require('images/timeLine.webp'),
  plus: require('images/plus.webp'),
  plusIcon: require('images/plusIcons.webp'),
  addToCart: require('images/addToCart.webp'),
  bannerMag: require('images/bannerMag.webp'),
  imageMag: require('images/imageMag.webp'),
  groupUsers: require('images/groupUsers.webp'),
  groupIcon: require('images/groupIcon.webp'),
  brandTag: require('images/brandTag.webp'),
  shortsBanner: require('images/shortsBanner.webp'),
  shortsCategoryIcon: require('images/shortsCategoryIcon.webp'),
  shortsCategoryIcon1: require('images/shortsCategoryIcon1.webp'),
  shortsCategoryIcon2: require('images/shortsCategoryIcon2.webp'),
  saveOutline: require('images/saveOutline.webp'),
  doubleArrowRight: require('images/doubleArrowRight.webp'),
  doubleArrowRightBlue: require('images/doubleArrowRightBlue.webp'),
  truck: require('images/truck.webp'),
  tag: require('images/tag.webp'),
  CheckVerifide: require('images/CheckVerifide.webp'),
  shippingBox: require('images/shippingBox_.webp'),
  birthday: require('images/birthday.webp'),
  giftGif: require('images/gift.gif'),
  thankYou: require('images/thankYou.gif'),
  wishListEditIcon: require('images/wishListEditicon.webp'),
  accountBell: require('images/account/Bell.webp'),
  accountMembership: require('images/account/accountMembership.webp'),
  accountOrder: require('images/account/accountOrder.webp'),
  accountWishlist: require('images/account/accountWishlist.webp'),
  accountRewardCoins: require('images/account/accountRewardCoins.webp'),
  accountProfile: require('images/account/accountProfile.webp'),
  accountAddressBook: require('images/account/accountAddressBook.webp'),
  accountReturn: require('images/account/accountReturn.webp'),
  accountHelp: require('images/account/accountHelp.webp'),
  accountSavedContent: require('images/account/accountSavedContent.webp'),
  accountRewardZone: require('images/account/accountRewardZone.webp'),
  accountMyCoupons: require('images/account/accountMyCoupons.webp'),
  accountRefer: require('images/account/accountRefer.webp'),
  accountBuyAgain: require('images/account/accountBuyAgain.webp'),
  accountLogOut: require('images/account/accountLogOut.webp'),
  accountMembershipText: require('images/account/accountMembershipText.webp'),
  doctorIcon: require('images/doctorIcon.webp'),
  teenyIcon: require('images/teenyicons.webp'),
  maleIcon: require('images/maleicon.webp'),
  securePayments: require('images/securePayments.webp'),
  easyReturn: require('images/easyReturn.webp'),
  paymentOptions: require('images/paymentOptions.webp'),
  dot: require('images/dot.webp'),
  radio: require('images/radio.webp'),
  viewAll: require('images/viewAll.webp'),
  locationArrow: require('images/locationArrow.webp'),
  brandsStar: require('images/brandsStar.webp'),
  Exclude: require('images/Exclude.webp'),
  Include: require('images/leftScrollIcon.webp'),
  homePageBannerImages: require('images/homePageBannerImages.webp'),
  hotSellerRightArrow: require('images/hotSellerRightArrow.webp'),
  dis: require('images/dis.gif'),
  error404: require('images/404image.gif'),
  orderBanner: require('images/orderbanner.webp'),
  orderReturnIcon: require('images/returnOrder.webp'),
  drPhoto: require('images/drPhoto.webp'),
  endokingRightArrowIcon: require('images/endokingRightArrow.webp'),
  brands: require('images/brands.webp'),
  brandsInActive: require('images/brandsInActive.webp'),
  brandsActive: require('images/brandsActive.webp'),
  community: require('images/community.webp'),
  homeInActive: require('images/homeInActive.webp'),
  communityActive: require('images/communityActive.webp'),
  offersInActive: require('images/offersInActive.webp'),
  offersActive: require('images/offersActive.webp'),
  freebiesInActive: require('images/freebiesInActive.png'),
  freebiesActive: require('images/freebiesActive.png'),
  homeActive: require('images/homeActive.webp'),
  more: require('images/more.webp'),
  blogs: require('images/blogs.webp'),
  closeBar: require('images/closeBar.webp'),
  cloudBg: require('images/cloudBg.webp'),
  event: require('images/event.webp'),
  magazine: require('images/magazine.webp'),
  toastCross: require('images/toastCross.webp'),
  shorts: require('images/shorts.webp'),
  termsGif: require('images/termsAndConditions.gif'),
  moreVerticalIcon: require('images/moreVerticalIcon.webp'),
  hotSellingBanner: require('images/hotSellingBanner.webp'),
  endokingBanner: require('images/endokingBanner.webp'),
  offerZoneBanner: require('images/offerZoneBanner.webp'),
  waldentBanner: require('images/waldentBanner.webp'),
  sortsBackImage: require('images/sortsBackImage.webp'),
  freeProductGif: require('images/freeProduct.gif'),
  mostSerchedproduct: require('images/mostSerched.webp'),
  mamberShipBanner: require('images/mamberShipBanner.webp'),
  box: require('images/box.webp'),
  urlIcon: require('images/url.webp'),
  suggestProductGif: require('images/suggest.gif'),
  sorts: require('images/sorts.webp'),
  shortsViews: require('images/shortsViews.webp'),
  categoryLineImage: require('images/categeoriLine.webp'),
  mamberShipCardImage: require('images/mamberShipCardImage.webp'),
  freecarouselImage: require('images/freecarousel.webp'),
  sellerStoryImage: require('images/sellerStory.webp'),
  starGif: require('images/star.gif'),
  viewSimilarProduct: require('images/viewSimilarProduct.webp'),
  similarProductGif: require('images/viewSimilar-Product.gif'),
  announcement: require('images/announcement.webp'),
  upiCardImage: require('images/upiImage.webp'),
  writeUsGif: require('images/write-us.gif'),
  flaxibalTimeIcon: require('images/flaxibalTimeIcon.webp'),
  copydocIcon: require('images/copydoc.webp'),
  bankImageIcon: require('images/bankImage.webp'),
  noDocumentIcon: require('images/nodocumentImage.webp'),
  flowbiteCash: require('images/flowbiteCash.webp'),
  lazyPayIcon: require('images/lazyPay.webp'),
  simplePayIcon: require('images/simplPay.webp'),
  unCheckVrifiedIcon: require('images/Check-verified.webp'),
  checkVrifiedIcon: require('images/Check-verifiedBlack.webp'),
  buyNow: require('images/buy_now.webp'),
  orderSuccess: require('images/orderSuccess.gif'),
  rightArrow: require('images/arrowLeftwhite.webp'),
  birthdayBold: require('images/birthdayBold.webp'),
  transgenderIcon: require('images/transgendericon.webp'),
  femaleIcon: require('images/femaleIcon.webp'),
  chatIcon: require('images/chat.webp'),
  callIcon: require('images/call.webp'),
  activeMamberShipIcon: require('images/ActivemamberShip.webp'),
  editPencil: require('images/editPencil.webp'),
  delete: require('images/delete.webp'),
  requestRaiseOrange: require('images/requestRaiseOrange.webp'),
  picUpCompleteOrange: require('images/picUpCompleteOrange.webp'),
  requestActiveOrange: require('images/requestActiveOrange.webp'),
  refundOrange: require('images/refundOrange.webp'),
  refundCompleteOrange: require('images/refundCompleteOrange.webp'),
  inTransitOrange: require('images/inTransitOrange.webp'),
  atWarehouseOrange: require('images/atWarehouseOrange.webp'),
  shippedOrange: require('images/shippedOrange.webp'),
  addressPin: require('images/addressPin.webp'),
  giftIcon: require('images/giftIcon.webp'),
  Pinterest: require('images/Pinterest.png'),
  brandB: require('images/b.webp'),
  userProfile: require('images/userProfile.webp'),
  camera: require('images/camera.webp'),
  sellerStoryVideo: require('images/sellerStoryVideo.mp4'),
  referralBg: require('images/referralBg.webp'),
  downArrowBlack: require('images/downArrow.webp'),
  upArrowBlack: require('images/upArrow1.webp'),
  newDentalKartLogo: require('images/newdentalkartlogo.webp'),
  arrowTop: require('images/arrow-top.webp'),
  editIconGif: require('images/editGif.gif'),
  freebieGif: require('images/freebie.gif'),
  play: require('images/play.webp'),
  playInActive: require('images/playInActive.webp'),
  playActive: require('images/playActive.webp'),
  sortBy: require('images/sortBy.webp'),
  music: require('images/music.webp'),
  musicStop: require('images/musicStop.webp'),
  playPause: require('images/playPause.webp'),
  saved: require('images/saved.webp'),
  closeWhiteRound: require('images/closeWhiteRound.webp'),
  notifyMe: require('images/notifyme.webp'),
  notifyBellIcon: require('images/notifyBellIcon.webp'),
  brandsGif: require('images/brands1.gif'),
  suggestGif: require('images/suggestProductGif.gif'),
  roundGreen: require('images/roundGreen.webp'),
  checkGreen: require('images/checkGreen.webp'),
  circleGreen: require('images/circleGreen.webp'),
  doubleRightArrow: require('images/doubleRightArrow.webp'),
  doubleLeftArrow: require('images/doubleLeftArrow.webp'),
  twitter: require('images/twitter.webp'),
  simpleProductGif: require('images/simplegift.gif'),
  tireSuccessGif: require('images/tireSuccess.gif'),
  delivery: require('images/delivery.webp'),
  membershipPrice: require('images/membershipPrice.webp'),
  orderCard: require('images/orderCard.webp'),
  paymentCard: require('images/paymentCard.webp'),
  productIssue: require('images/productIssue.webp'),
  refundCard: require('images/refund.webp'),
  returnCard: require('images/returnCard.webp'),
  rewardPrice: require('images/rewardPrice.webp'),
  tracking: require('images/tracking.webp'),
  userLogin: require('images/userLogin.webp'),
  celebrateGif: require('images/celebrate.gif'),
  checkList: require('images/checkList.webp'),
  memberShipGif: require('images/memberShip.gif'),
  shipping: require('images/shipping.webp'),
  rewardValue: require('images/rewardValue.webp'),
  valueCoin: require('images/valueCoin.webp'),
  accountMyMembership: require('images/account/accountMyMembership.webp'),
  prepaid: require('images/prepaid.webp'),
  checkBox: require('images/checkBox.webp'),
  uncheckBox: require('images/uncheckBox.webp'),
  goCashlessGif: require('images/goCashless.gif'),
  whatsappGray: require('images/whatsappGray.webp'),
  downArrow1: require('images/downArrow1.webp'),
  checkBoxBlue: require('images/checkBoxBlue.webp'),
  copy1: require('images/copy1.webp'),
  closeBlue: require('images/closeBlue.webp'),
  eyeView: require('images/eyeView.webp'),
  eyeLine: require('images/eyeLine.webp'),
  orderBannerGif: require('images/orderBannerGif.gif'),
  splashAnimated: require('images/splashAnimated.gif'),
  cancel: require('images/cancel.webp'),
  tansparentIcon: require('images/Transparent.webp'),
  noCouponAvailableGif: require('images/noCouponAvailableGif.gif'),
  backToTop: require('images/backToTop.png'),
  blueRadio: require('images/blueRadio.webp'),
  backToArrowTop: require('images/backToArrowTop.png'),
  coinBg: require('images/account/coinBg.webp'),
  couponPer: require('images/account/couponPer.webp'),
  cardCoin: require('images/account/cardCoin.webp'),
  couponBg: require('images/account/couponBg.webp'),
  indiaFlag: require('images/indiaFlag.webp'),
  rewardValueBanner: require('images/rewardValueBanner.webp'),
  noCouponAvailable: require('images/noCouponAvailable.webp'),
  lock1: require('images/lock1.webp'),
  bag: require('images/bag.webp'),
  email: require('images/email.webp'),
  noteList: require('images/noteList.webp'),
  down: require('images/down.webp'),
  circle1: require('images/circle1.webp'),
  circleFill: require('images/circleFill.webp'),
  search1: require('images/search1.webp'),
  search2: require('images/search2.webp'),
  ellipse: require('images/ellipse.webp'),
  eyeWhite: require('images/eyeWhite.webp'),
  starRate: require('images/starRate.webp'),
  codIcon: require('images/codIcon.webp'),
  compliancesIcon: require('images/compliancesIcon.webp'),
  deliveryInfoIcon: require('images/deliveryInfoIcon.webp'),
  lucideBoxIcon: require('images/lucideBoxIcon.webp'),
  categoryGif: require('images/categoryGif.gif'),
  whatsapp1: require('images/whatsapp1.webp'),
  gst: require('images/gst.webp'),
  phoneIn: require('images/phoneIn.webp'),
  location1: require('images/location1.webp'),
  locationPin: require('images/locationPin.webp'),
  user: require('images/user.webp'),
  mobile: require('images/mobile.webp'),
  home: require('images/home.webp'),
  phone1: require('images/phone1.webp'),
  map: require('images/map.webp'),
  home2: require('images/home2.webp'),
  dUpArrow: require('images/dUpArrow.webp'),
  dotCircle: require('images/dotCircle.webp'),
  plus1: require('images/plus1.webp'),
  map1: require('images/map1.webp'),
  marker: require('images/marker.webp'),
  latestDentalKartLogo: require('images/newDentalKartLogo.png'),
  dentalKartFull: require('images/dentalKartFull.webp'),
  cancelClose: require('images/cancelcircle.png'),
  bgImgSign: require('images/bgImgSign.png'),
  defaultGif: require('images/defaultGif.gif'),
  noInternet: require('images/noInternet.gif'),
  auth: require('images/auth.gif'),
  iMark: require('images/iMark.webp'),
  upgradeBG: require('images/upgradeBG.png'),
  dentalkartUpdatedIcon: require('images/dentalkartUpdatedIcon.webp'),
  mobileNumberIcon: require('images/mobileNumberIcon.webp'),
  mobileModalCrossIcon: require('images/mobileModalCrossIcon.webp'),
  greenCheck: require('images/greenCheck.webp'),
  loginCoin: require('images/loginCoin.png'),
  returnable: require('images/returnable.webp'),
  nonReturnable: require('images/nonReturnable.webp'),
  blackCheck: require('images/blackCheck.webp'),
  authGif: require('images/authGif1.gif'),
  plus2: require('images/plus2.webp'),
  whiteCross: require('images/whiteCross.webp'),
  crossGrey: require('images/crossGrey.webp'),
  arrowLeft1: require('images/arrowLeft1.webp'),
  recommendedBanner: require('images/recommendedBanner.png'),
  plusGold: require('images/plusGold.webp'),
  exchange: require('images/exchange.webp'),
  retry: require('images/retry.webp'),
  crossBlack: require('images/crossBlack.webp'),
  backward: require('images/backward.webp'),
  note: require('images/note.webp'),
  statusCav: require('images/statusCav.webp'),
  checkRound: require('images/checkRound.webp'),
  compensationApproval: require('images/compensationApproval.webp'),
  compensationProgress: require('images/compensationProgress.webp'),
  compensated: require('images/compensated.webp'),
  replaceShipped: require('images/replaceShipped.webp'),
  replaceR: require('images/replaceR.webp'),
  requestRaise: require('images/requestRaise.webp'),
  repaired: require('images/repaired.webp'),
  resolved: require('images/resolved.webp'),
  cancelledR: require('images/cancelledR.webp'),
  replaceApproval: require('images/replaceApproval.webp'),
  refundProcess: require('images/refundProcess.webp'),
  freeR: require('images/freeR.webp'),
  partShipped: require('images/partShipped.webp'),
  pickupR: require('images/pickupR.webp'),
  partDelivery: require('images/partDelivery.webp'),
  searchR: require('images/searchR.webp'),
  refundCompleted: require('images/refundCompleted.webp'),
  delivered: require('images/delivered.webp'),
  pending: require('images/pending.webp'),
  requestApproved: require('images/requestApproved.webp'),
  rateGif: require('images/rateGif.gif'),
  deliveryBoy: require('images/deliveryBoy.gif'),
  gallery: require('images/gallery.webp'),
  starFillGreen: require('images/starFillGreen.webp'),
  starOutlineGreen: require('images/starOutlineGreen.webp'),
  cameraGrey: require('images/cameraGrey.webp'),
  galleryGrey: require('images/galleryGrey.webp'),
  copyGrey: require('images/copyGrey.webp'),
  arrivalRound: require('images/arrivalRound.webp'),
  cancelRound: require('images/cancelRound.webp'),
  rightArrowGrey: require('images/rightArrowGrey.webp'),
  returnRound: require('images/returnRound.webp'),
};

export default Icons;

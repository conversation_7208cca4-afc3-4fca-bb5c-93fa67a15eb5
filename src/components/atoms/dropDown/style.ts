import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.whiteColor,
    },
    dropdown: {
      height: Sizes.xx4l,
      paddingHorizontal: Sizes.m,
      borderWidth: Sizes.x,
      borderRadius: Sizes.s,
      borderColor: colors.border,
    },
    placeholderStyle: {
      fontSize: Sizes.mx,
      color: colors.text2,
      fontFamily: Fonts.Medium,
    },
    selectedTextStyle: {
      fontSize: Sizes.l,
      color: colors.blackColor,
    },
    flex: {flex: Sizes.x},
    inputSearchStyle: {
      height: Sizes.x46,
      color: colors.text2,
      fontFamily: Fonts.Medium,
      borderColor: colors.borderColor,
      borderRadius: Sizes.mx,
      fontSize: Sizes.mx,
    },
    textDropDown: {color: colors.textLight},
    dropdownBg: {
      // backgroundColor: colors.background
    },
    listDropdown: {
      backgroundColor: colors.background,
      borderRadius: Sizes.mx,
      paddingTop: Sizes.xm,
      // borderTopLeftRadius: Sizes.s,
      // borderTopRightRadius: Sizes.s,
      // borderBottomLeftRadius: Sizes.xm,
      // borderBottomRightRadius: Sizes.xm,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      shadowOffset: {
        width: Sizes.xs,
        height: Sizes.xs,
      },
      shadowRadius: Sizes.xm,
      elevation: Sizes.s,
    },
    icons: {
      left: Sizes.xm,
      paddingHorizontal: Sizes.s,
    },
    errorStyle: {
      marginTop: Sizes.s,
    },
    label: {
      position: 'absolute',
      backgroundColor: 'white',
      left: Sizes.xms,
      top: 0,
      zIndex: 999,
      paddingHorizontal: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
    },
  });
export default styles;

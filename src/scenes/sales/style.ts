const stylesWithOutColor = (colors) => ({
  // Existing styles
  screenContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  // New styles
  contentContainer: {
    flex: 1,
    padding: 16,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderText: {
    marginTop: 10,
    color: colors.text,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: colors.error,
    fontSize: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    color: colors.text,
    fontSize: 16,
    textAlign: 'center',
  },
  detailsContainer: {
    padding: 16,
    backgroundColor: colors.card,
    borderRadius: 8,
  },
  detailTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 16,
  },
  detailItem: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 8,
  },
});

export default stylesWithOutColor;
import { FooterButton } from 'components/atoms';
import React, { memo, useMemo } from 'react';
import {View} from 'react-native';
import ErrorHandler from 'utils/ErrorHandler';
import stylesWithOutColor from '../../../scenes/productDetail/style';
import { useTheme } from '@react-navigation/native';
import { t } from 'i18next';
import { ProductData } from 'local';
import { ToastPosition } from 'react-native-toast-message';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamsList } from 'routes';

const TAG = 'PoductDetailScreen';

type Props = {
  memoizedSeparator: () => React.JSX.Element
  notifyIcon: boolean
  isAddToCartInProgress: boolean
  isInStock: boolean | undefined
  product: ProductData | null
  isLoggedIn: any
  notifyLoading: boolean
  addToCartPress: () => Promise<void>
  setReqPriceModal: React.Dispatch<React.SetStateAction<boolean>>
  setBulkModal: React.Dispatch<React.SetStateAction<boolean>>
  showInfoMessage:  (message: string, pos?: ToastPosition, theme?: string) => void
  navigation: NativeStackNavigationProp<RootStackParamsList>
  notifyClick: () => Promise<void>
  showSimilar: boolean
  setSimilarProductModal: React.Dispatch<React.SetStateAction<boolean>>
  isBuyNowInProgress: boolean
  onPressBuyNow: () => Promise<void>
}

const ProductFooter = ({
  memoizedSeparator,
  notifyIcon,
  isAddToCartInProgress,
  isInStock,
  product,
  isLoggedIn,
  notifyLoading,
  addToCartPress,
  setReqPriceModal,
  setBulkModal,
  showInfoMessage,
  navigation,
  notifyClick,
  showSimilar,
  setSimilarProductModal,
  isBuyNowInProgress,
  onPressBuyNow,
}: Props) => {
    const {colors} = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <ErrorHandler
      componentName={`${TAG} FooterButton`}
      onErrorComponent={<View />}>
      <FooterButton
        shadowProps
        useInsets
        separator={memoizedSeparator}
        footerStyle={styles?.footer}
        buttons={[
          {
            key: 'addToCartButton',
            disabled: notifyIcon || isAddToCartInProgress,
            labelSize: 'mx',
            iconLeft:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? 'cart'
                : notifyIcon
                ? 'notifyMe'
                : null,
            tintColor: !notifyIcon ? 'whiteColor' : null,
            radius: 'm',
            type:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? 'secondary'
                : 'bordered',
            iconSize: 'xxl',
            labelStyle: {textTransform: 'uppercase'},
            weight: '500',
            labelColor:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? 'whiteColor'
                : 'categoryTitle',
            selfAlign: 'stretch',
            text: isInStock
              ? product?.action_btn?.text === 'Request Price'
                ? t?.('buttons.reqPrice')
                : t?.('buttons.addToCart')
              : !notifyIcon
              ? t?.('buttons.notifyme')
              : null,
            onPress: async () => {
              if (isInStock) {
                if (product?.action_btn?.text !== 'Request Price') {
                  if (!isAddToCartInProgress) {
                    addToCartPress?.();
                  }
                } else {
                  setReqPriceModal?.(true);
                  setBulkModal?.(true);
                }
              } else {
                if (!isLoggedIn) {
                  showInfoMessage?.('Please login first.');
                  navigation?.navigate?.('Login', {back: true});
                } else {
                  if (!notifyIcon && !notifyLoading) {
                    notifyClick?.();
                  }
                }
              }
            },
          },
          {
            hidden:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? false
                : showSimilar
                ? false
                : true,
            key: 'buyNowOrSimilarButton',
            text: t?.(
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? 'PDP.buyNow'
                : 'PDP.similar'
            ),
            onPress: () => {
              if (
                isInStock &&
                product?.action_btn?.text !== 'Request Price'
              ) {
                if (!isBuyNowInProgress) {
                  onPressBuyNow?.();
                }
              } else {
                setSimilarProductModal?.(true);
              }
            },
            disabled: isBuyNowInProgress,
            borderColor:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? null
                : 'pumpkinOrange',
            type:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? 'primary'
                : 'bordered',
            radius: 'm',
            fastImageStyle:
              !isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? styles?.similarImage
                : styles?.defaultImage,
            labelSize: 'mx',
            fastImageLeft:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? 'buyNow'
                : 'newSimilarGif',
            weight: '500',
            selfAlign: 'stretch',
            labelColor:
              isInStock &&
              product?.action_btn?.text !== 'Request Price'
                ? 'background'
                : 'pumpkinOrange',
          },
        ]}
      />
    </ErrorHandler>
  );
};

export default memo(ProductFooter);
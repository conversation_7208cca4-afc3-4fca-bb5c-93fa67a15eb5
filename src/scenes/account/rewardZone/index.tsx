import React, {useCallback, useEffect, useState} from 'react';
import {View, Linking, FlatList, SafeAreaView, BackHandler} from 'react-native';
import {DropDownList, Header} from 'components/molecules';
import {t} from 'i18next';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useTheme} from '@react-navigation/native';
import {RootStackParamsList} from 'routes';
import {FastImagesItem, Label, Link, ListView, Spacer} from 'components/atoms';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
import {useDispatch, useSelector} from 'react-redux';
import {memberShipsFaqApi} from 'services/mambership';
import {RewardZone} from 'skeletonLoader';
import {checkDevice} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import { AnalyticsEvents } from 'components/organisms';
import { RootState } from '@types/local';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'RewardZoneScene'>;
};

const constantData = [
  {
    title: 'Sign Into Your Account And Make A Purchase.',
    icon: Icons.packageIcon,
  },
  {
    title: 'Earn Reward Coins On Successful Delivery Of Order.',
    icon: Icons.wallet,
  },
  {
    title: 'Enjoy Redeeming Your Rewards On Subsequent Order.',
    icon: Icons.gift,
  },
];

const RewardZoneScene = ({navigation, route}: Props) => {
  const TAG = 'RewardZoneScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const [faqsData, setFaqsData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState();
  const insets = useSafeAreaInsets();

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const renderItem = ({item, index}) => {
    if (checkDevice()) {
      return (
        <View key={index} style={styles.howContainer}>
          <FastImagesItem
            source={item.icon}
            FastImageStyle={styles.renderImg1}
          />
          <Spacer size="xm" type="Horizontal" />
          <Label
            style={styles.howItText}
            size="mx"
            text={item.title}
            fontFamily="Regular"
            color="text2"
            textTransform="capitalize"
          />
        </View>
      );
    } else {
      return (
        <View style={styles.workContainer} key={index}>
          <Label
            lineHeight="xl"
            style={styles.signText}
            size="mx"
            text={item.title}
            fontFamily="Regular"
            color="text2"
          />
          <FastImagesItem
            source={item.icon}
            FastImageStyle={styles.renderImg}
          />
        </View>
      );
    }
  };
  const faqApi = useCallback(async () => {
    setLoading(true);
    const {data, status} = await memberShipsFaqApi({parents: ['Rewards']});
    setLoading(false);

    if (data?.result && status) {
      const membershipData = data?.result?.filter(item =>
        item.parents.includes('Rewards'),
      );
      setFaqsData(membershipData);
    }
  }, [faqsData]);
const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  useEffect(() => {
    AnalyticsEvents('REWARD_ZONE', 'Reward zone viewed', {}, userInfo, isLoggedIn);
    faqApi();
  }, []);

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  return (
    <SafeAreaView style={[styles.mainContainer, { paddingTop: insets.top }]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton
          navigation={navigation}
          searchIcon={true}
          bagIcon={true}
          text={t('rewardZone.rewardZone')}
          onPressNavigation={() => {
            navigation.goBack();
            if (route.params?.goBack) {
              route.params?.goBack();
            }
          }}
        />
      </ErrorHandler>
      {loading ? (
        <RewardZone />
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={['']}
          renderItem={() => (
            <>
              <ErrorHandler
                componentName={`${TAG} RewardZoneList`}
                onErrorComponent={<View />}>
                <View style={styles.container}>
                  <FastImagesItem
                    resizeMode={'stretch'}
                    source={
                      checkDevice() ? Icons.rewardBanner1 : Icons.rewardBanner
                    }
                    FastImageStyle={[
                      styles.rewardImg,
                      checkDevice() && styles.rewardImg1,
                    ]}
                  />

                  <Spacer size={checkDevice() ? 'l' : 'xm'} />
                  <FastImagesItem
                    resizeMode="stretch"
                    source={
                      checkDevice() ? Icons.pennyBanner1 : Icons.pennyBanner
                    }
                    FastImageStyle={[
                      styles.workItImg,
                      checkDevice() && styles.workItImg1,
                    ]}
                  />
                  <Spacer size={checkDevice() ? 'l' : 'xm'} />
                  <FastImagesItem
                    resizeMode={'stretch'}
                    source={
                      checkDevice()
                        ? Icons.shopMoreBanner1
                        : Icons.shopMoreBanner
                    }
                    FastImageStyle={[
                      styles.moreImg,
                      checkDevice() && styles.moreImg1,
                    ]}
                  />
                  <Spacer size={checkDevice() ? 'l' : 'xm'} />
                  <FastImagesItem
                    resizeMode={'stretch'}
                    source={Icons.rewardValueBanner}
                    FastImageStyle={styles.rewardValue}
                  />
                  <View style={checkDevice() && styles.howView}>
                    <View style={styles.howIt}>
                      <Label
                        size={checkDevice() ? 'xxl' : 'mx'}
                        fontFamily="Medium"
                        text={t('rewardZone.howWork')}
                      />
                    </View>

                    <ListView
                      ItemSeparatorComponent={
                        <Spacer type="Vertical" size="xm" />
                      }
                      data={constantData}
                      horizontal={checkDevice()}
                      scrollEnabled={false}
                      renderItem={renderItem}
                      keyExtractor={listViewKeyExtractor}
                    />
                  </View>
                  <Spacer size="m" />
                  <View>
                    <ListView
                      data={faqsData}
                      renderItem={({item, index}) => (
                        <DropDownList
                          answerLabelStyle={styles.answerLabel}
                          // index={index}
                          // currentIndex={currentIndex}
                          item={item}
                          // onPress={() =>
                          //   setCurrentIndex(
                          //     currentIndex === index ? undefined : index,
                          //   )
                          // }
                        />
                      )}
                      keyExtractor={listViewKeyExtractor}
                      ItemSeparatorComponent={
                        <Spacer type="Vertical" size="xx" />
                      }
                    />
                  </View>
                  <Spacer size="xx" />
                  <View style={styles.fRow}>
                    <Label
                      color="text2"
                      textTransform="capitalize"
                      size="m"
                      fontFamily="Regular"
                      text={`${t('rewardZone.mail')} `}
                    />
                    <Link
                      onPress={() =>
                        Linking.openURL('mailto:<EMAIL>')
                      }
                      color="newSunnyOrange"
                      size="m"
                      fontFamily="Regular"
                      text={t('login.supportEmail')}
                      isUnderlined
                    />
                  </View>
                  <Spacer size="xx" />
                </View>
              </ErrorHandler>
            </>
          )}
        />
      )}
    </SafeAreaView>
  );
};

export default RewardZoneScene;

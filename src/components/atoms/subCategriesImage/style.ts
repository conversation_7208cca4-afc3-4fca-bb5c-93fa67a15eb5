import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    imageCategries: {
      width: 51,
      height: 51,
    },
    imageContioner: {
      width: 64,
      height: 63,
      borderWidth: 1,
      padding: Sizes.xs,
      borderRadius: Sizes.s,
      marginLeft: Sizes.m,
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: colors.borderColor,
      backgroundColor: '#FFFFFF',
    },
    active: {
      borderColor: '#6B90BC',
    },
  });

export default styles;

import {createSlice, PayloadAction, createAsyncThunk} from '@reduxjs/toolkit';
import {AnalyticsEvents} from 'components/organisms';
import {RootStackParamsList} from 'routes';
import {
  accountSummery,
  getMe,
  getReferrals,
  notifyMe,
  useWhatsApp,
  getAppVersion,
} from 'services/account';
import {
  addToCartProduct,
  generateCart,
  getCart,
  getFreeProduct,
  mergeCart,
} from 'services/cart';
import {
  addProductToWishlist,
  deleteFriendWishlist,
  deleteProductFromWishlist,
  deleteUserWishlist,
  getWishlist,
} from 'services/wishlist';
import localStorage from 'utils/localStorage';
import {
  showCartMessage,
  showErrorMessage,
  showSuccessMessage,
  showWishListMessage,
} from 'utils/show_messages';
import {t} from 'i18next';
import tokenClass from 'utils/token';
import {getCountries} from 'services/address';
import {getAllItemPromotionProducts} from 'services/home';
import {getShorts} from 'services/shorts';
import {notifyProduct} from 'services/productDetail';
import {EmptyWishlist} from 'components/atoms';
// import {notifyMe} from 'api';
import {debugError, debugLog} from 'utils/debugLog';
import navigationService from '../../utils/navigationRef';

const getMergeCart = async (guestCartId: any, customerCartId: any) => {
  try {
    if (guestCartId && customerCartId) {
      const {data} = await mergeCart(
        {destination_cart_id: customerCartId, buy_now: false},
        guestCartId,
      );
      if (data?.cart) {
        await localStorage.remove('guestCartId');
        await localStorage.set('cartCount', data?.cart?.items.length);
        return data?.cart?.items.length;
      }
    }
  } catch (err) {
    debugLog('mergeCartsV2', err);
  }
};

export const setLogout = createAsyncThunk(
  'app/setLogout',
  async (screen: keyof RootStackParamsList = '', {dispatch}) => {
    try {
      await tokenClass.removeToken();
      await localStorage.remove('guest_cart_id');
      await localStorage.remove('buyNowCartId');
      await localStorage.remove('customer_cart_id');
      await localStorage.remove('delivery_address');
      await localStorage.remove('pincode');
      await localStorage.remove('cartCount');

      dispatch(generateNewCart());
      setIsLoggedIn(false);
      setWishlistItems([]);
      // if (screen) {
      //   return navigate(screen);
      // }
      return;
    } catch (error) {
      debugLog('catch error setLogout : ', error);
      return;
    }
  },
);
export const setLogoutAPI = createAsyncThunk(
  'app/setLogout',
  async ({dispatch}) => {
    
    navigationService.resetNavigation('Tab', {screen: 'Shop'});
    navigationService.safeNavigate('Login');
    try {
      await tokenClass.removeToken();
      await localStorage.remove('guest_cart_id');
      await localStorage.remove('buyNowCartId');
      await localStorage.remove('customer_cart_id');
      await localStorage.remove('delivery_address');
      await localStorage.remove('pincode');
      await localStorage.remove('cartCount');

      dispatch(generateNewCart());
      setIsLoggedIn(false);
      setWishlistItems([]);
      return;
    } catch (error) {
      debugLog('catch error setLogout : ', error);
      return;
    }
  },
);
export const resetCart = createAsyncThunk(
  'app/resetCart',
  async (_, {dispatch}) => {
    try {
      await localStorage.remove('guest_cart_id');
      await localStorage.remove('customer_cart_id');
      await localStorage.remove('cartCount');
      dispatch(generateNewCart());
      return;
    } catch (error) {
      debugLog('catch error resetCart : ', error);
      return;
    }
  },
);

export const addToCart = createAsyncThunk(
  'app/addToCart',
  async (productData: any, {dispatch, getState}) => {
    try {
      let state = getState();
      let cartId = state?.app?.cartId;
      let cartCount = state?.app?.cartCount;
      const {data, status} = await addToCartProduct(cartId, productData);
      if (status) {
        localStorage.set('cartCount', data?.cart?.items?.length);
        const qty = productData?.cart_items?.reduce(
          (sum, item) => sum + item?.data?.quantity,
          0,
        );
        setTimeout(() => {
          showCartMessage(
            `${qty} ${qty > 1 ? 'items' : 'item'}`,
            'bottom',
            productData?.image,
            qty > 1 ? true : false,
            qty,
          );
        }, 200);
        return data?.cart?.items?.length;
      } else {
        if (data?.message !== 'Cart not found') {
          setTimeout(() => {
            showErrorMessage(
              data?.message ? data?.message : t('validations.someThingWrong'),
            );
          }, 200);
        }
        if (data?.message === 'Cart not found') {
          await localStorage.set('guest_cart_id', '');
          await localStorage.set('customer_cart_id', '');
          dispatch(generateNewCart()).then(() => {
            dispatch(addToCart(productData));
          });
        }
        return cartCount;
      }
    } catch (error) {
      showErrorMessage(t('validations.someThingWrong'));
    }
  },
);

export const getCartData = async () => {
  const loginStatus = await tokenClass.loginStatus();
  const guest_cart_id = await localStorage.get('guest_cart_id');
  const customer_cart_id = await localStorage.get('customer_cart_id');
  try {
    if (!loginStatus && !guest_cart_id) {
      const {data} = await generateCart();
      if (data?.cart_id) {
        await localStorage.set('guest_cart_id', data?.cart_id);
      }
      return {cartCount: 0, cartId: data?.cart_id};
    } else if (loginStatus && !customer_cart_id) {
      const {data} = await generateCart();

      if (data?.cart_id) {
        let cartCount = 0;
        await localStorage.set('customer_cart_id', data?.cart_id);
        if (guest_cart_id) {
          debugLog(guest_cart_id, 'updatedcart', data?.cart_id);
          cartCount = await getMergeCart(guest_cart_id, data?.cart_id);
          return {cartCount: cartCount, cartId: data?.cart_id};
        } else {
          return {cartCount: cartCount, cartId: data?.cart_id};
        }
      }
    } else if (loginStatus && customer_cart_id) {
      const {data} = await getCart(customer_cart_id);
      return {
        cartCount: data?.cart?.items?.length || 0,
        cartId: customer_cart_id,
      };
    } else {
      if (guest_cart_id) {
        const {data} = await getCart(guest_cart_id);
        return {
          cartCount: data?.cart?.items?.length || 0,
          cartId: guest_cart_id,
        };
      } else {
        return {
          cartCount: 0,
          cartId: null,
        };
      }
    }
  } catch (error) {
    debugLog('catch generateNewCart : ', error);
  }
};
const transformWishlists = (
  wishlists: any[] = [],
): {[key: string]: {id: string; name: string; productIds: number[]}} => {
  const result: {
    [key: string]: {id: string; name: string; productIds: number[]};
  } = {};

  wishlists.forEach(wishlist => {
    const {is_default, title, items = [], id} = wishlist;
    const key = is_default
      ? 'default'
      : title?.toLowerCase()?.replace(/\s+/g, '') || 'unnamed';

    result[key] = {
      id: id, // Use the original ID here
      name: title || 'Unnamed Wishlist',
      productIds: items.map((item: any) => item?.product_id).filter(Boolean),
    };
  });

  return result;
};
export const getWishlistData = createAsyncThunk(
  'app/getWishlistData',
  async () => {
    const loginStatus = await tokenClass.loginStatus();
    try {
      if (loginStatus) {
        const {data} = await getWishlist();
        const wishlists = data?.wishlists || [];
        return transformWishlists(wishlists);
      }
      return {};
    } catch (error) {
      debugLog('catch wishlist : ', error);
      return {};
    }
  },
);

export const generateNewCart = createAsyncThunk(
  'app/generateNewCart',
  getCartData,
);
export const addToWishListThunk = createAsyncThunk(
  'app/addToWishlist',
  async (
    {
      productId,
      image,
      wishlistId = 'default',
      callBack,
    }: {
      productId: number;
      image: string;
      wishlistId?: string;
      callBack?: any;
    },
    {dispatch, getState},
  ) => {
    try {
      // Get the current wishlists state
      const currentWishlists = getState().app.wishlists;

      // Create optimistic update
      const optimisticWishlists = {...currentWishlists};

      // Determine which wishlist to update (default is typically the one to use)
      const listKey = wishlistId || 'default';

      if (optimisticWishlists[listKey]) {
        // Only add if not already in the list
        if (
          !optimisticWishlists[listKey].productIds.includes(Number(productId))
        ) {
          optimisticWishlists[listKey] = {
            ...optimisticWishlists[listKey],
            productIds: [
              ...optimisticWishlists[listKey].productIds,
              Number(productId),
            ],
          };
          // Show the message immediately for better UX
          showWishListMessage(
            t('toastMassages.addedToWishlist'),
            'bottom',
            image,
          );
        }
      }

      // Immediately update state with optimistic data
      dispatch(updateWishlists(optimisticWishlists));

      // If there's a callback, execute it immediately
      if (callBack) {
        callBack();
      }

      // Now make the actual API call
      const {data, status} = await addProductToWishlist({
        product_id: Number(productId),
      });

      if (status) {
        // API call succeeded, get the actual updated wishlists to ensure consistency
        const actualWishlists = await dispatch(getWishlistData()).unwrap();
        return {productId, wishlists: actualWishlists};
      } else {
        // API call failed with an error status
        if (data?.message === 'Product already exist in wishlist!') {
          showErrorMessage(data?.message);
          return {productId, wishlists: optimisticWishlists}; // Keep optimistic update
        }

        // Revert to previous state
        dispatch(updateWishlists(currentWishlists));
        showErrorMessage(data?.message || t('validations.someThingWrong'));
        return null;
      }
    } catch (error) {
      // API call failed with an exception
      // Revert to the previous state
      const currentWishlists = getState().app.wishlists;
      dispatch(updateWishlists(currentWishlists));

      showErrorMessage(t('validations.someThingWrong'));
      debugError('Error adding to wishlist:', error);
      return null;
    }
  },
);

export const removeFromWishlist = createAsyncThunk(
  'app/removeFromWishlist',
  async (
    {
      productId,
      callBack,
    }: {
      productId: number;
      callBack?: any;
    },
    {dispatch, getState},
  ) => {
    try {
      // Get current wishlists from the state
      const currentWishlists = {...getState().app.wishlists};

      // Find which wishlist contains this product
      let wishlistKey = null;
      let wishlistId = null;

      for (const [key, wishlist] of Object.entries(currentWishlists)) {
        if (wishlist.productIds.includes(Number(productId))) {
          wishlistKey = key;
          wishlistId = wishlist.id;
          break;
        }
      }

      // If product not found in any wishlist, return early
      if (!wishlistKey || !wishlistId) {
        // showErrorMessage(t('validations.productNotInWishlist'));
        return false;
      }

      // Create optimistic update by removing the product
      const optimisticWishlists = {...currentWishlists};
      optimisticWishlists[wishlistKey] = {
        ...optimisticWishlists[wishlistKey],
        productIds: optimisticWishlists[wishlistKey].productIds.filter(
          id => id !== Number(productId),
        ),
      };

      // Immediately update state with optimistic data
      dispatch(updateWishlists(optimisticWishlists));

      // Show success message immediately for better UX
      showSuccessMessage(t('toastMassages.updateSuccess'));

      // If there's a callback, execute it immediately
      if (callBack) {
        callBack();
      }

      // Now make the actual API call
      const {data, status} = await deleteProductFromWishlist(
        wishlistId,
        Number(productId),
      );

      if (status) {
        // API call succeeded, get the actual updated wishlists to ensure consistency
        const actualWishlists = await dispatch(getWishlistData()).unwrap();
        return {productId, wishlists: actualWishlists};
      } else {
        // API call failed with an error status
        // Revert to previous state
        dispatch(updateWishlists(currentWishlists));
        showErrorMessage(data?.message || t('validations.someThingWrong'));
        return false;
      }
    } catch (error) {
      // API call failed with an exception
      // Revert to the previous state
      const currentWishlists = getState().app.wishlists;
      dispatch(updateWishlists(currentWishlists));

      showErrorMessage(t('validations.someThingWrong'));
      debugError('Error removing from wishlist:', error);
      return false;
    }
  },
);
export const deleteWishlistThunk = createAsyncThunk(
  'app/deleteWishlist',
  async (
    {
      wishlistId,
      isFriendWishlist = false,
    }: {
      wishlistId: string;
      isFriendWishlist?: boolean;
    },
    {dispatch},
  ) => {
    try {
      let response;

      if (isFriendWishlist) {
        response = await deleteFriendWishlist(wishlistId);
      } else {
        response = await deleteUserWishlist(wishlistId);
      }

      const {data, status} = response;
      if (status && data) {
        // Show success message
        showSuccessMessage(data?.message || t('toastMassages.updateSuccess'));

        // For friend wishlist, we need to refresh friend wishlist data
        // For user wishlist, update Redux state
        if (isFriendWishlist) {
          // You may need to implement a separate action for friend wishlists if needed
          return response;
        } else {
          // Get fresh wishlist data to update state
          const updatedWishlists = await dispatch(getWishlistData()).unwrap();
          return response;
        }
      } else {
        showErrorMessage(data?.message || t('validations.someThingWrong'));
        return {success: false};
      }
    } catch (error) {
      debugError('Error deleting wishlist:', error);
      showErrorMessage(t('validations.someThingWrong'));
      return {success: false};
    }
  },
);
export const getCountriesList = createAsyncThunk(
  'app/getCountriesList',
  async () => {
    const {data} = await getCountries();
    return data?.countries;
  },
);

export const getWhatsAppLink = createAsyncThunk(
  'app/getWhatsAppLink',
  async () => {
    const {data} = await useWhatsApp();
    return data;
  },
);

// export const notify = createAsyncThunk('app/notify', async id => {
//   const {data} = await notifyMe(id);
//   if (data?.subscribeForStockAlert?.message === 'success') {
//     setTimeout(() => {
//       showSuccessMessage(t('PDP.notify'));
//     }, 200);
//   }
// });

export const notify = createAsyncThunk('app/notify', async id => {
  const {data} = await notifyMe(id);
  if (data?.statusCode === 200 || 201) {
    setTimeout(() => {
      showSuccessMessage(t('PDP.notify'));
    }, 200);
  } else {
    showErrorMessage(t(data?.message));
  }
});

export const getReferralLink = createAsyncThunk(
  'app/getReferralLink',
  async (productKey: Referrals) => {
    const {data} = await getReferrals(productKey);
    return data;
  },
);

export const getUserInfo = createAsyncThunk('app/getUserInfo', async () => {
  const {data} = await getMe();
  AnalyticsEvents('USER_INFO', 'User Info', data);
  return data;
});

export const getRewardCoins = createAsyncThunk(
  'app/getRewardCoins',
  async () => {
    const {data} = await accountSummery();
    return data;
  },
);
export const getFreeGiftProduct = createAsyncThunk(
  'app/getFreeGiftProduct',
  async () => {
    const {data} = await getAllItemPromotionProducts();
    return data.data;
  },
);
export const getOfferProduct = createAsyncThunk(
  'app/getOfferProduct',
  async () => {
    const {data} = await getFreeProduct();
    return data?.data;
  },
);

export const initializeAppData = createAsyncThunk(
  'app/initializeAppData',
  async (_, {dispatch, getState}) => {
    try {
      const appState = getState().app;
      // Run all API calls in parallel
      const [
        appVersionResult,
        cartResult,
        promotionResult,
        countriesResult,
        whatsappResult,
        referralResult,
        referralLinkResult,
        freeProductResult,
        accountSummaryResult,
      ] = await Promise.allSettled([
        getAppVersion(appState?.isLoggedIn),
        getCartData(),
        getAllItemPromotionProducts(),
        getCountries(),
        useWhatsApp(),
        getReferrals({refer_type: 'USER'}),
        getReferralLink({refer_type: 'USER'}),
        getFreeProduct(),
        accountSummery(),
      ]);
      let userLogout = false;
      if (
        appVersionResult.status === 'fulfilled' &&
        appVersionResult.value?.data?.force_logout
      ) {
        dispatch(setLogout('Login'));
        userLogout = true;
      }

      let wishListResponse = null;
      try {
        wishListResponse = await dispatch(getWishlistData()).unwrap();
      } catch (err) {
        console.error('Wishlist API failed:', err);
      }
      return {
        // cart: cartResponse,
        // countries: countriesResponse.data?.countries,
        // whatsApp: whatsAppResponse.data,
        // referral: referralResponse.data,
        // referralLink: referralLinkResponse.data,
        // freeGift: freeGiftResponse.data?.data,
        // offer: offerResponse.data?.data,
        // rewards: rewardResponse.data,
        // wishlists: wishListResponse,
        cart: userLogout
          ? null
          : cartResult.status === 'fulfilled'
          ? cartResult.value
          : null,
        countries:
          countriesResult.status === 'fulfilled'
            ? countriesResult.value?.data?.countries
            : [],
        whatsApp:
          whatsappResult.status === 'fulfilled'
            ? whatsappResult.value?.data
            : null,
        referral:
          referralResult.status === 'fulfilled'
            ? referralResult.value?.data
            : null,
        referralLink:
          referralLinkResult.status === 'fulfilled'
            ? referralLinkResult.value?.data
            : null,
        freeGift:
          promotionResult.status === 'fulfilled'
            ? promotionResult.value?.data?.data
            : null,
        offer:
          freeProductResult.status === 'fulfilled'
            ? freeProductResult.value?.data?.data
            : null,
        rewards:
          accountSummaryResult.status === 'fulfilled'
            ? accountSummaryResult.value?.data
            : null,
        wishlists: wishListResponse || null,
      };
    } catch (error) {
      debugError('Error initializing app data:', error);
    }
  },
);

export const getShortsList = createAsyncThunk('app/getShortsList', async () => {
  const {data} = await getShorts();
  return data?.data;
});

const initialState: RootState['app'] = {
  loading: false,
  isLoggedIn: false,
  userInfo: null,
  cart: null,
  cartCount: 0,
  cartId: null,
  whatsAppLink: null,
  validationRules: null,
  wishlistItems: [],
  countryListings: null,
  baseCountryData: null,
  referLink: null,
  notifyProductId: null,
  brandsName: [],
  getFreeGiftItem: null,
  getOfferItems: null,
  wishlists: {},
  shortsAllVideo: [],
  shortsLoader: false,
};

const appSlice = createSlice({
  name: 'app',
  initialState: initialState,
  reducers: {
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setIsLoggedIn(state, action: PayloadAction<boolean>) {
      state.isLoggedIn = action.payload;
    },
    setCartCount(state, action: PayloadAction<number>) {
      state.cartCount = action.payload;
    },
    setWishlistItems(state, action: PayloadAction<Array<number>>) {
      state.wishlistItems = action.payload;
    },
    updateWishlists(state, action: PayloadAction<any>) {
      state.wishlists = action.payload;
    },
    moveWishlistItem: (
      state,
      action: PayloadAction<{
        sourceWishlistId: string;
        targetWishlistId: string;
        productId: number;
      }>,
    ) => {
      const {sourceWishlistId, targetWishlistId, productId} = action.payload;

      // Find the source and target wishlists
      const sourceKey = Object.keys(state.wishlists).find(
        key => state.wishlists[key].id === sourceWishlistId,
      );
      const targetKey = Object.keys(state.wishlists).find(
        key => state.wishlists[key].id === targetWishlistId,
      );

      if (sourceKey && targetKey) {
        // Remove product from source wishlist
        state.wishlists[sourceKey] = {
          ...state.wishlists[sourceKey],
          productIds: state.wishlists[sourceKey].productIds.filter(
            id => id !== productId,
          ),
        };

        // Add product to target wishlist (if not already present)
        if (!state.wishlists[targetKey].productIds.includes(productId)) {
          state.wishlists[targetKey] = {
            ...state.wishlists[targetKey],
            productIds: [...state.wishlists[targetKey].productIds, productId],
          };
        }
      }
    },
    removeWishlist: (state, action: PayloadAction<string>) => {
      const wishlistIdToRemove = action.payload;

      // Find and remove the wishlist with the given ID
      const updatedWishlists = {...state.wishlists};

      for (const [key, wishlist] of Object.entries(updatedWishlists)) {
        if (wishlist.id === wishlistIdToRemove) {
          delete updatedWishlists[key];
          break;
        }
      }

      state.wishlists = updatedWishlists;
    },
  },
  extraReducers: builder => {
    builder.addCase(generateNewCart.fulfilled, (state, action) => {
      state.cartCount = action.payload?.cartCount;
      state.cartId = action.payload?.cartId;
    });
    builder.addCase(setLogout.fulfilled, state => {
      state.isLoggedIn = false;
      state.userInfo = null;
      state.wishlistItems = [];
      state.cartCount = 0;
      state.cart = null;
      state.wishlists = {};
    });

    // addToCart start
    builder.addCase(addToCart.pending, state => {
      state.loading = false;
    });
    builder.addCase(addToCart.fulfilled, (state, action) => {
      state.cartCount = action.payload;
      state.loading = false;
    });
    builder.addCase(addToCart.rejected, state => {
      state.loading = false;
    });

    // addToCart end

    builder.addCase(getUserInfo.fulfilled, (state, action) => {
      state.userInfo = action.payload;
    });
    builder.addCase(getCountriesList.fulfilled, (state, action) => {
      state.countryListings = action.payload;
    });
    builder.addCase(getFreeGiftProduct.fulfilled, (state, action) => {
      state.getFreeGiftItem = action.payload;
    });
    builder.addCase(getOfferProduct.fulfilled, (state, action) => {
      state.getOfferItems = action.payload;
    });
    builder.addCase(getWhatsAppLink.fulfilled, (state, action) => {
      state.whatsAppLink = action.payload;
    });
    builder.addCase(getRewardCoins.fulfilled, (state, action) => {
      state.rewardCoinsData = action.payload;
    });
    builder.addCase(getReferralLink.fulfilled, (state, action) => {
      state.referLink = action.payload;
    });
    builder.addCase(notify.fulfilled, (state, action) => {
      state.notifyProductId = action.payload;
    });

    builder.addCase(getWishlistData.fulfilled, (state, action) => {
      state.wishlists = action.payload;
    });
    builder.addCase(addToWishListThunk.fulfilled, (state, action) => {
      if (action.payload) {
        // The old approach just pushed to wishlistItems array
        // state.wishlistItems.push(action.payload);

        // Instead, we should use the updated wishlists from the API
        if (action.payload.wishlists) {
          state.wishlists = action.payload.wishlists;
        }
      }
    });
    builder.addCase(deleteWishlistThunk.fulfilled, (state, action) => {
      if (action.payload?.success && !action.payload?.isFriendWishlist) {
        // If it's a user wishlist, update the wishlists state
        if (action.payload.wishlists) {
          state.wishlists = action.payload.wishlists;
        }
      }
    });

    builder.addCase(initializeAppData.fulfilled, (state, action) => {
      state.cartCount = action.payload?.cart?.cartCount;
      state.cartId = action.payload?.cart?.cartId;
      state.countryListings = action.payload?.countries;
      state.whatsAppLink = action.payload?.whatsApp;
      state.referLink = action.payload?.referral;
      state.getFreeGiftItem = action.payload?.freeGift;
      state.getOfferItems = action.payload?.offer;
      state.rewardCoinsData = action.payload?.rewards;
      state.wishlists = action.payload?.wishlists;
    });
    builder.addCase(getShortsList.pending, (state, action) => {
      state.shortsLoader = true;
    });
    builder.addCase(getShortsList.fulfilled, (state, action) => {
      state.shortsLoader = false;
      state.shortsAllVideo = action.payload?.rows || [];
    });
    builder.addCase(getShortsList.rejected, (state, action) => {
      state.shortsLoader = false;
    });
  },
});
export const {
  setLoading,
  setIsLoggedIn,
  setCartCount,
  setWishlistItems,
  updateWishlists,
  moveWishlistItem,
  removeWishlist,
} = appSlice.actions;
export default appSlice.reducer;

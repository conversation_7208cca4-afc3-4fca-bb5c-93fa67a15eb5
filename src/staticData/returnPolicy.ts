const returnPolicy = [
  'Returns is a scheme provided by us directly under this policy in terms of which the option of exchange, replacement and/ or refund is offered by the respective sellers to you. All products listedunder a particular category may not have the same return policy.',
  `For all products, the policy on the product page shall prevail over the general return policy. Do refer the respective item's applicable return policy on the product page for any exceptions to the table below.`,
  'The return policy is divided into two parts; Do read all sections carefully to understand the conditions and cases under which returns will be accepted.',
];

const productPolicy = [
  {
    title: 'Part 1 - Category, Return Window and Actions possible',
    data: [
      {
        title: 'All Products',
        data: [
          '10 days',
          'Replacement only',
          'In order to help you resolve issues with your product, we may troubleshoot your product either through online tools, over the phone, and/or through a call from technical team.',
          'If a defect is determined within the Returns Window, a replacement of the same model will be provided at no additional cost.',
          'Note- If the rectification comes frequently then rectification cost will be applicable as per T&C.',
        ],
      },
    ],
  },
  {
    title: 'Products are not eligible for returns',
    data: [
      {
        title: 'No Returns Categories',
        data: [
          'Products not in warranty or exceeds the time for replacement (10 days) are not returned and all we ask for is the product returned back to us must have intact original Packaging, seal, and the accessories.',
          'The following table contains a list of products that are not eligible for returns as per the company Returns Policy.',
        ],
      },
      {
        title: 'Equipment',
        data: [
          'Dental Chairs, Dental Compressors, UV chambers and Autoclaves, X-ray Units, RVG sensor machine, Model Trimmers, OPG and CBCT machines, Apex locators and Endomotors, Ultrasonic Cleaners and Scalers, Micromotors, Implant Motors/Physiodispenser, Bleaching Light and light cure units, Amalgamator.',
        ],
      },
      {
        title: 'Products directly used on Patients',
        data: [
          'Tooth creme or mousse, MRC/Orthodontic trainers, Water Flossers, Chin Caps, headgear, face mask or other myofunctional appliances.',
        ],
      },
    ],
  },
  {
    title: 'Part 2 - Returns Pick-Up and Processing',
    description:
      'In case of returns where you would like item(s) to be picked up from a different address, the address can only be changed if pick-up service is available at the new address.',
    data: [],
  },
];

const productCondition = [
  {
    title: 'Complete Product',
    des: 'All in-the-box accessories & freebies and combos (if any) should be present.',
  },
  {
    title: 'Unused Product',
    des: 'The product should be unused, unwashed, unsoiled, without any stains and with non-tampered quality check seals/ warranty seals (wherever applicable). Before returning any product.',
  },
  {
    title: 'Undamaged Product',
    des: 'The product should be undamaged and without any scratches, dents, tears or holes.',
  },
  {
    title: 'Undamaged Packaging',
    des: `Product's original packaging/ box should be undamaged. For any products for which a refund is to be given, the refund will be processed once the returned product has been received by the customer to us.`,
  },
];

export {returnPolicy, productPolicy, productCondition};

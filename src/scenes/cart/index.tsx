import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ScrollView,
  View,
  TouchableOpacity,
  FlatList,
  Animated,
  InteractionManager,
  RefreshControl,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useIsFocused, useTheme} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import {RootStackParamsList} from '../../routes';
import {
  Address,
  ImageIcon,
  Label,
  ListView,
  ProductCardVertical,
  Spacer,
  WithGradient,
  CartItem,
  CouponListModal,
  CartRemoveModal,
  CouponCart,
  CartEmptyView,
  CartPriceSection,
  CartPositionStatus,
  PaymentSupportCart,
  WishlistButton,
  DeliveryInfoModal,
  HorizontalScrollBar,
  ErrorFallback,
  SuccessModal,
  AddressConfirmModal,
} from 'components/atoms';
import {<PERSON><PERSON>, CartOfferCarousel, Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {getCartTotalWithKeys} from 'utils/calculatePrizes';
import {
  showErrorMessage,
  showInfoMessage,
  showSuccessMessage,
} from 'utils/show_messages';
import localStorage from 'utils/localStorage';
import {
  resetCart,
  setLoading,
  setCartCount,
  addToWishListThunk,
  addToCart,
} from 'app-redux-store/slice/appSlice';
import {AnalyticsEvents} from 'components/organisms';
import {
  applyDiscountElement,
  cartPaymentMethods,
  checkServiceAvailability,
  deleteCartItem,
  getCart,
  getCouponList,
  getFreeProduct,
  mergeCart,
  removeCouponFromCart,
  setCartAddress,
  shippingRates,
  updateCartItem,
} from 'services/cart';
import {cartPositionStatus} from 'staticData';
import {getDefaultWishlist} from 'services/wishlist';
import {CartLoader} from 'skeletonLoader';
import {getCategoryDetails} from 'services/category';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {Dimensions} from 'react-native';
import {
  checkDevice,
  getCurrentLocation,
  cardTabType,
  billingAdd,
} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import getImageUrl from 'utils/imageUrlHelper';
import {setCurrentScreenName} from '@microsoft/react-native-clarity';
import {debugError, debugLog} from 'utils/debugLog';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {trackEvent} from 'components/organisms/appEventsLogger/FacebookEventTracker';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';
import {defaultPinCode} from 'config/environment';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'Cart'>;
  item: homePageTopCategoryItemProps;
  undoProduct?: boolean;
};

const CartScene = ({navigation, undoProduct = false}: Props) => {
  const TAG = 'CartScreen';
  const {colors} = useTheme();
  const scrollViewRef = useRef(null);
  const targetViewPosition = useRef(null);
  const insets = useSafeAreaInsets();
  const cartId = useSelector((state: RootState) => state.app.cartId);
  const {isLoggedIn, userInfo, cartCount} = useSelector(
    (state: RootState) => state.app,
  );
  const baseCountryData = useSelector(
    (state: RootState) => state.app.baseCountryData,
  );

  const dispatch = useDispatch();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [weight, setWeight] = useState(false);
  const [isCouponOpen, setCouponOpen] = useState(true);
  const [promotionByCart, setPromotionByCart] = useState('');
  const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [removeCartModal, setRemoveCartModal] = useState(false);
  const [removeCart, setRemoveCart] = useState<Item | null>(null);
  const [cartData, setCartData] = useState<Cart | null>(null);
  const [cartItems, setCartItems] = useState<Cart | []>([]);
  const [couponList, setCouponList] = useState([]);
  const [appliedCoupon, setAppliedCoupon] = useState(false);
  const [appliedRewardPoints, setAppliedRewardPoints] = useState(0);
  const [priceDetails, setPriceDetails] = useState({});
  const [couponCode, setCouponCode] = useState('');
  const [currentScrollPosition, setCurrentScrollPosition] = useState(0);
  const {width: screenWidth} = useMemo(() => Dimensions.get('window'), []);
  const [returnInfoModel, setReturnInfoModel] = useState(false);
  const [selectedAddress, setSelectedAddress] =
    useState<CustomerAddressV2 | null>(null);
  const [weightData, setWeightData] = useState<WeightWithPrice[] | []>([]);
  const isFocus = useIsFocused();
  const [deliveryStatusData, setDeliveryStatusData] =
    useState<DeliveryInfo | null>(null);
  const [openKeyboard, setOpenKeyboard] = useState(false);
  const flatListRef = useRef(null);
  const [selectedTab, setSelectedTab] = useState(cardTabType.Wishlist);
  const [freeProduct, setFreeProduct] = useState({});
  const [defaultWishList, setDefaultWishList] = useState<Product[]>([]);
  const [bestDeal, setBestDeal] = useState<Product[]>([]);
  const [scrollBarWidth, setScrollBarWidth] = useState(0);
  const [listWidth, setListWidth] = useState(0);
  const bestDealId = 2567;
  const scrollX1 = useRef(new Animated.Value(0)).current;
  const ITEM_WIDTH = useMemo(() => screenWidth * 0.47, [screenWidth]);
  const ITEMS_TO_SCROLL = 2;
  const [scrollX, setScrollX] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);
  const [positionStatus, setPositionStatus] = useState(cartPositionStatus);
  const [successModel, setSuccessModel] = useState(false);
  const [addressConfirm, setAddressConfirm] = useState(false);
  const currentAddress = useRef<CustomerAddressV2 | null>(null);
  const [coords, setCoords] = useState();
  const [cartCheckModal, setCartCheckModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const handleScroll = useMemo(
    () =>
      Animated.event([{nativeEvent: {contentOffset: {x: scrollX1}}}], {
        useNativeDriver: false,
        listener: event => {
          const offsetX = event.nativeEvent.contentOffset.x;
          setScrollX(offsetX);
        },
      }),
    [scrollX1],
  );

  const dataMap = useMemo(
    () => ({
      Wishlist: defaultWishList,
      Bestdeal: bestDeal,
    }),
    [defaultWishList, bestDeal],
  );

  useEffect(() => {
    if (isFocus) {
      getCartData(cartId, false, userInfo);
      if (isLoggedIn) {
        getDefaultWishListData();
        getCouponListData();
      }
      getBestDealData();
      getFreeBies();
    }
  }, [isFocus, cartId, isLoggedIn]);

  useEffect(() => {
    setCurrentScreenName('Cart page');
    getCurrentLocation(position => {
      setCoords(position?.coords);
    }, false);
    return () => {
      setCurrentScreenName('Clarity event');
    };
  }, []);

  useEffect(() => {
    updateCartAddress();
    if (selectedAddress?.id) {
      const updatedSteps = positionStatus.map(step =>
        step.title === 'Address' ? {...step, status: true} : step,
      );
      setPositionStatus(updatedSteps);
    }
  }, [selectedAddress]);

  useEffect(() => {
    if (!isLoggedIn) {
      setSelectedTab(cardTabType.Bestdeal);
    }
  }, []);

  useEffect(() => {
    if (cartData) {
      AnalyticsEvents(
        'CART_VIEWED',
        'Cart View',
        {...cartData, deliveryStatusData},
        userInfo,
        isLoggedIn,
      );
    }

    setAppliedCoupon(!!cartData?.coupon_code?.code);
    setPriceDetails(getCartTotalWithKeys(cartData, isLoggedIn));
  }, [cartData, isLoggedIn]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getCartData(cartId, true, userInfo);
  }, [cartId, userInfo]);

  const handleButtonClick = useCallback(
    (buttonName: string) => {
      setSelectedTab(buttonName);
    },
    [setSelectedTab],
  );

  const scrollToEnd = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollX + ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
      setCurrentScrollPosition(scrollX + ITEM_WIDTH * ITEMS_TO_SCROLL);
    }
  }, [ITEM_WIDTH, scrollX]);

  const scrollToStart = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollX - ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
      setCurrentScrollPosition(scrollX - ITEM_WIDTH * ITEMS_TO_SCROLL);
    }
  }, [ITEM_WIDTH, scrollX]);

  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({offset: 0, animated: false});
    }
  }, [selectedTab]);

  const getDefaultWishListData = useCallback(async () => {
    const {data, status} = await getDefaultWishlist();
    if (data && status) {
      setDefaultWishList(data?.items);
      setSelectedTab(
        data?.items?.length === 0 ? cardTabType.Bestdeal : cardTabType.Wishlist,
      );
    }
  }, []);

  const getBestDealData = useCallback(async () => {
    const {data, status} = await getCategoryDetails(bestDealId);
    if (data && status) {
      setBestDeal(data?.categoryProducts?.products);
    }
  }, []);

  const checkoutOfStock = (cartItemsData: Item[]) => {
    let outOfStock = false;
    const stock = cartItemsData?.some(
      (item: Item) => item?.product?.stock_status === 'OUT_OF_STOCK',
    );
    if (stock) {
      outOfStock = true;
    }
    return outOfStock;
  };

  const checkStatusVariables = useMemo(() => {
    let isCodStatus = true;
    let totalWeightCount = 0;
    cartData?.items.map((item: Item) => {
      totalWeightCount += item?.product?.weight;
      if (isCodStatus && !item?.product?.is_cod) {
        isCodStatus = false;
      }
    });
    return {
      cart_weight: parseInt(totalWeightCount * 1000),
      is_cod_eligible: isCodStatus,
      cart_amount: parseFloat(
        cartData?.pricing_details?.grand_total?.amount?.value || 0,
      ),
    };
  }, [
    cartData?.items,
    cartItems,
    cartData?.pricing_details?.grand_total?.amount?.value,
  ]);

  const estimateDate = useCallback(
    item => {
      if (!item?.product?.id || !deliveryStatusData?.delivery_info?.length) {
        return '';
      }
      const deliveryMessage =
        deliveryStatusData.delivery_info[0]?.delivery_days?.find(
          deliveryItem => deliveryItem?.product_id === item.product.id,
        )?.message;
      return deliveryMessage || '';
    },
    [deliveryStatusData],
  );

  const getCartData = useCallback(
    async (cartID: string, hide = false, users) => {
      if (cartID) {
        const buyNowCartId = await localStorage.get('buyNowCartId');
        let data;
        setIsLoading(hide ? false : true);
        if (buyNowCartId) {
          data = await getMergeCart(cartID, buyNowCartId);
        } else {
          data = await getCart(cartID);
        }
        // setIsLoading(false);
        if (
          data?.status === false &&
          data?.data?.message === 'Cart not found'
        ) {
          dispatch(resetCart());
          setCartData(null);
          setCartItems([]);
          setIsLoading(false);
          setRefreshing(false);
          return;
        }
        let cartResponse = data?.data?.cart;
        const checkReward =
          data?.data?.cart?.pricing_details?.discounts?.filter(
            item => item.code === 'reward',
          );
        if (cartID && checkReward?.length > 0) {
          if (
            checkReward[0].amount?.value &&
            checkReward[0].amount?.value > 0
          ) {
            const response = await applyDiscountElement(cartId, {
              buy_now: false,
              reward_points: 0,
            });
            cartResponse = response?.data?.cart;
          }
        }
        dispatch(setCartCount(cartResponse?.items.length));
        setCartData(cartResponse);
        setCartItems(sortCartList(cartResponse?.items));
        updateAddressDetails(selectedAddress, cartResponse);
        setRefreshing(false);
        setIsLoading(false);
        if (cartResponse?.items?.length > 0) {
          setPriceDetails(getCartTotalWithKeys(cartResponse, isLoggedIn));
          const address = cartResponse?.addresses;
          let deliveryAddress;
          if (address?.customer_address_id) {
            deliveryAddress = {
              ...address,
              id: address?.customer_address_id,
              country_id: 'IN',
            };
            localStorage.set('delivery_address', deliveryAddress);
          } else {
            deliveryAddress = await localStorage.get('delivery_address');
          }
          if (isLoggedIn) {
            const addressExists = users?.addresses.find(
              addr => addr.id === deliveryAddress?.id,
            );
            if (addressExists) {
              localStorage.set('delivery_address', addressExists);
              setSelectedAddress(addressExists);
            } else if (
              cartResponse?.addresses &&
              deliveryAddress &&
              cartResponse?.addresses?.customer_address_id !==
                deliveryAddress?.id
            ) {
              setSelectedAddress(deliveryAddress);
            } else {
              const defaultAddress = users?.addresses.find(
                address => address.default_shipping,
              );
              if (defaultAddress) {
                setSelectedAddress(defaultAddress);
                localStorage.set('delivery_address', defaultAddress);
              } else if (!addressExists && users?.addresses?.length > 0) {
                const deliveredAddress = users?.addresses[0];
                if (deliveredAddress) {
                  setSelectedAddress(deliveredAddress);
                  localStorage.set('delivery_address', deliveredAddress);
                }
              } else {
                setSelectedAddress({country_id: 'IN', postcode: ''});
              }
            }
          } else {
            if (!cartResponse?.addresses) {
              setSelectedAddress({country_id: 'IN', postcode: ''});
            } else {
              setSelectedAddress({
                country_id: cartResponse?.addresses?.country?.name,
                postcode: cartResponse?.addresses?.postcode,
              });
            }
          }
        }
        return cartResponse;
      }
    },
    [deliveryStatusData, isLoggedIn],
  );

  const updateCount = useCallback(
    async (count: number, itemId: string) => {
      dispatch(setLoading(true));
      try {
        const {data} = await updateCartItem(cartId, {
          cart_items: [{cart_item_id: itemId, quantity: count}],
        });

        if (data?.cart) {
          setCartData(data?.cart);
          setCartItems(sortCartList(data?.cart?.items));
          setPriceDetails(getCartTotalWithKeys(data?.cart, isLoggedIn));
          dispatch(setLoading(false));
          showSuccessMessage(t('cart.updatedCart'));
          updateAddressDetails(selectedAddress, data?.cart);
          if (data?.deliveryData?.response) {
            setDeliveryStatusData(data?.deliveryData?.response);
          }
        } else {
          dispatch(setLoading(false));
          getCartData(cartId, true, userInfo);
        }
      } catch (error) {
        dispatch(setLoading(false));
        debugError('Error updating count:', error);
      }
    },
    [
      cartId,
      dispatch,
      getCartData,
      isLoggedIn,
      selectedAddress,
      updateAddressDetails,
      userInfo,
    ],
  );

  const renderCartItem = useCallback(
    ({item, index}: {item: Item; index: number}) => {
      return (
        <ErrorHandler
          componentName={`${TAG} CartItem`}
          onErrorComponent={<View />}>
          <CartItem
            item={item}
            index={index}
            buyNow={true}
            navigation={navigation}
            selectedAddress={selectedAddress}
            deliveryInfoMsg={estimateDate(item)}
            removeCart={(data: Item) => {
              setRemoveCartModal(true);
              setRemoveCart(data);
            }}
            updateCart={updateCount}
            infoClick={() => setReturnInfoModel(true)}
          />
        </ErrorHandler>
      );
    },
    [estimateDate, navigation, selectedAddress, updateCount],
  );

  const applyDiscount = useCallback(
    async (key: 'reward_points' | 'coupon_code', value: number | string) => {
      dispatch(setLoading(true));
      const {data, status} = await applyDiscountElement(cartId, {
        buy_now: false,
        [key]: value,
      });
      dispatch(setLoading(false));
      // showSuccessMessage(t('cart.CouponApplied'));
      if (status) {
        setSuccessModel(true);
        setTimeout(() => {
          setSuccessModel(false);
        }, 4000);
        setCartData(data?.cart);
        setCartItems(sortCartList(data?.cart?.items));
        updateAddressDetails(selectedAddress, data?.cart);
      } else {
        setCouponCode('');
        showErrorMessage(data?.devMessage);
      }
    },
    [cartId, selectedAddress],
  );

  const getCouponListData = useCallback(async () => {
    dispatch(setLoading(true));
    const {data, status} = await getCouponList(cartId);
    if (status) {
      const groupedData = data?.data?.reduce((acc, coupon) => {
        const expiryDate = coupon.expiry_date;
        if (!acc[expiryDate]) {
          acc[expiryDate] = [];
        }
        acc[expiryDate].push(coupon);
        return acc;
      }, {});
      const result = Object.keys(groupedData || {}).map(date => {
        return {
          title: `Valid till ${new Date(date).toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })}`,
          data: groupedData[date],
        };
      });
      setCouponList(result);
    }
    dispatch(setLoading(false));
  }, [cartId]);

  const removeCoupon = useCallback(async () => {
    dispatch(setLoading(true));
    const {data, status} = await removeCouponFromCart(cartId, {
      buy_now: false,
    });
    dispatch(setLoading(false));

    if (status) {
      setCouponCode('');
      setCartData(data?.cart);
      setCartItems(sortCartList(data?.cart?.items));
      updateAddressDetails(selectedAddress, data?.cart);
      showSuccessMessage(t('cart.CouponRemoved'));
    } else {
      showErrorMessage(data?.devMessage);
    }
  }, [cartId, selectedAddress]);

  const updateCartAddress = useCallback(async () => {
    if (selectedAddress?.country_id && selectedAddress?.postcode) {
      const {data} = await checkServiceAvailability({
        postcode: Number(selectedAddress?.postcode),
        country_code: selectedAddress?.country_id,
        product_ids: cartData?.items?.map(item => item?.product?.id) || [],
        cart_data: checkStatusVariables,
      });
      if (data?.response) {
        setDeliveryStatusData(data?.response);
      }
      setAddressOnCart(selectedAddress);
    } else if (!isLoggedIn) {
      setAddressOnCart({});
    }
  }, [cartData, cartItems, checkStatusVariables, selectedAddress]);

  const statusVariables = (updatedCartData: Cart) => {
    let isCodStatus = true;
    let totalWeightCount = 0;
    updatedCartData?.items.map((item: Item) => {
      totalWeightCount += item?.product?.weight;
      if (isCodStatus && !item?.product?.is_cod) {
        isCodStatus = false;
      }
    });
    return {
      cart_weight: parseInt(totalWeightCount * 1000),
      is_cod_eligible: isCodStatus,
      cart_amount: parseFloat(
        updatedCartData?.pricing_details?.grand_total?.amount?.value || 0,
      ),
    };
  };
  const updateAddressDetails = async (address: any, updatedCartData: Cart) => {
    const sAddress = address || selectedAddress;
    if (sAddress?.country_id && sAddress?.postcode) {
      const {data} = await checkServiceAvailability({
        postcode: Number(sAddress?.postcode),
        country_code: sAddress?.country_id,
        product_ids:
          updatedCartData?.items?.map(item => item?.product?.id) || [],
        cart_data: statusVariables(updatedCartData),
      });
      if (data?.response) {
        setDeliveryStatusData(data?.response);
      }
      setAddressOnCart(sAddress);
    } else if (!isLoggedIn) {
      setAddressOnCart({});
    }
  };

  const onCheckout = useCallback(
    async (skip = false) => {
      if (cartData?.global_errors?.length > 0) {
        const msg = cartData?.global_errors?.map(e => e.message)?.join(',');
        showErrorMessage(msg);
      } else {
        if (!isLoggedIn) {
          showInfoMessage(t('toastMassages.processOrder'));
          return navigation.navigate('Login', {
            nextScreenName: 'Cart',
            nextScreenParams: {},
          });
        }
        if (selectedAddress?.latitude || skip) {
          if (selectedAddress?.id) {
            dispatch(setLoading(true));
            const cartResp = await getCart(cartId);
            const resData = {
              ...cartResp,
              eta: deliveryStatusData?.delivery_info?.[0]
                ?.max_delivery_days_text,
            };
            AnalyticsEvents(
              'CHECKOUT_STARTED',
              'Proceed To Checkout',
              resData,
              userInfo,
              isLoggedIn,
            );
            trackEvent('INITIATE_CHECKOUT', {
              // contentId: cartData?.sku,
              // contentType: 'product',
              // currency: 'INR',
              // value: cartData?.selling_price,
              params: {resData: resData},
            });

            // appsFlyer Checkout Started (INITIATE_CHECKOUT) Event
            const productIds = cartData?.items
              ?.map(item => item.product.id)
              .join(',');
            const categories = cartData?.items
              ?.map(item => item.product.name)
              .join(', ');
            appsFlyerEvent('CheckoutStarted', {
              totalPrice: cartData?.pricing_details?.grand_total?.amount?.value,
              productIds: productIds,
              category: categories,
              currency: 'INR',
              totalQuantity: cartData?.total_quantity,
            });
            const {data} = await cartPaymentMethods(
              selectedAddress?.country_id,
              selectedAddress?.postcode,
              checkStatusVariables?.cart_amount,
              checkStatusVariables?.cart_weight,
              checkStatusVariables?.is_cod_eligible,
            );
            dispatch(setLoading(false));
            const isStock = checkoutOfStock(cartResp?.data?.cart?.items);
            const paymentMethods = data?.response?.payment_methods;
            if (isStock) {
              return showErrorMessage(t('cart.productsOutOfStock'));
            }

            if (paymentMethods && paymentMethods?.length === 0) {
              return showErrorMessage(t('cart.serviceNotproviding'));
            }
            if (
              !!cartResp?.data?.cart?.global_errors &&
              cartResp?.data?.cart?.global_errors?.length > 0
            ) {
              showErrorMessage(cartResp?.data?.cart?.global_errors);
            } else {
              navigation.navigate('PaymentPage', {
                selectedAddress: selectedAddress,
                cart: cartResp?.data?.cart,
                paymentMethods,
                buyNow: false,
              });
            }
          } else {
            if (userInfo?.addresses?.length === 0) {
              navigation.navigate('ManageAddress', {
                address: selectedAddress,
                cardType: 'cart',
                onChangeSelectAddress: async updateData => {
                  setSelectedAddress(updateData);
                },
              });
            } else {
              showErrorMessage(t('validations.selectAddress'));
            }
          }
        } else {
          if (userInfo?.addresses?.length > 0) {
            openAddressModal();
          } else {
            openMapsModal(false, selectedAddress);
          }
        }
      }
    },
    [
      isLoggedIn,
      selectedAddress,
      navigation,
      dispatch,
      cartId,
      checkStatusVariables,
      openAddressModal,
      openMapsModal,
      userInfo,
    ],
  );

  const getFreeBies = useCallback(async () => {
    const {data} = await getFreeProduct();
    if (data) {
      setFreeProduct(data?.data);
    }
  }, []);

  const onPressWishlist = async (item: Item) => {
    setRemoveCartModal(false);
    if (isLoggedIn) {
      const productId = item?.parent_id ? item?.parent_id : item?.product?.id;
      const itemId = item?.item_id;
      dispatch(
        addToWishListThunk({
          productId,
          callBack: () => deleteCartProduct(item, true),
          image: getImageUrl(item?.product?.thumbnail?.url),
        }),
      );
      trackEvent('ADD_TO_WISHLIST', {
        contentId: productId,
        contentType: 'product',
        // currency: 'INR',
        // value: 499,
        // params: { item_name: '' }
      });
      // appsFlyer Add to Wishlist Event
      appsFlyerEvent('AddToWishlist', {
        price: item?.product?.special_price,
        productId: productId,
        category: item?.product?.name,
        currency: 'INR',
      });
      // appsFlyer Remove From Cart Event
      appsFlyerEvent('RemoveFromCart', {
        productId: productId,
        category: item?.product?.name,
      });
    } else {
      showInfoMessage(t('toastMassages.wishlistLogin'));
      const prevRoutes = navigation.getState().routes;
      navigation.navigate('Login', {
        nextRouterState: {
          index: 0,
          routes: prevRoutes,
        },
      });
    }
  };

  const deleteCartProduct = async (item: Item, hide?: boolean) => {
    dispatch(setLoading(hide ? false : true));
    const itemId = item?.item_id;
    const {data, status} = await deleteCartItem(cartId, itemId);
    const deliveryInfo = estimateDate(item);
    if (data?.cart && status) {
      AnalyticsEvents(
        'REMOVE_FROM_CART',
        'Remove From Cart',
        {...item, deliveryInfo},
        userInfo,
        isLoggedIn,
      );
      // appsFlyer Remove From Cart Event
      appsFlyerEvent('RemoveFromCart', {
        productId: item?.product?.id,
        category: item?.product?.name,
      });
      dispatch(setCartCount(data?.cart?.items.length));
      setCartData(data?.cart);
      setCartItems(sortCartList(data?.cart?.items));
      setPriceDetails(getCartTotalWithKeys(data?.cart, isLoggedIn));
      showSuccessMessage(t('cart.removeCart'));
      updateAddressDetails(selectedAddress, data?.cart);
    } else {
      showErrorMessage(data?.devMessage);
    }
    setRemoveCartModal(false);
    dispatch(setLoading(false));
  };

  // const getWeightSlab = useCallback(async () => {
  //   dispatch(setLoading(true));
  //   const {data} = await shippingRates(selectedAddress?.country_id || 'IN');
  //   dispatch(setLoading(false));

  //   if (data) {
  //     setWeightData(data?.data);
  //   }
  // }, [dispatch, selectedAddress]);

  const setAddressOnCart = useCallback(
    async (address: CustomerAddressV2) => {
      const isAddressChanged =
        JSON.stringify(address) !== JSON.stringify(currentAddress.current);
      if (isAddressChanged) {
        currentAddress.current = address;
      } else {
        return;
      }
      const billingAddInfo = await billingAdd(
        address?.id,
        userInfo?.addresses || [],
      );
      if (cartId) {
        const {data, status} = await setCartAddress(cartId, {
          shipping_addresses: [
            isLoggedIn
              ? {
                  address: {
                    country_code: address?.country_code ?? address?.country_id,
                    country_id: address?.country_id ?? 'IN',
                    region_code: address?.region?.region_code,
                    street: address.street,
                    postcode: address.postcode,
                    city: address.city,
                    firstname: address.firstname,
                    lastname: address.lastname,
                    telephone: address.telephone,
                    gst_id: address?.vat_id,
                    same_as_billing: billingAddInfo ? false : true,
                    alternate_mobile:
                      address?.custom_attributes?.find(
                        attribute =>
                          attribute?.attribute_code === 'alternate_telephone',
                      ).value ?? null,
                    region_id: address?.region?.region_id ?? 0,
                    region: address?.region?.region,
                    longitude: address?.longitude,
                    latitude: address?.latitude,
                    customer_street_2: address?.customer_street_2,
                    map_address: address?.map_address,
                  },
                  customer_address_id: address?.id,
                }
              : {
                  address: {country_code: 'IN'},
                },
          ],
          buy_now: false,
          billing_address: isLoggedIn ? billingAddInfo : null,
        });

        if (status) {
          setCartData(data?.cart);
          setCartItems(sortCartList(data?.cart?.items));
        }
      }
    },
    [cartId],
  );

  const getMergeCart = async (buyNowCartId: string, customerCartId: string) => {
    try {
      if (buyNowCartId && customerCartId) {
        const data = await mergeCart(
          {destination_cart_id: customerCartId, buy_now: true},
          buyNowCartId,
        );
        debugLog('merge cart result==================', data);

        if (data?.data?.cart && data?.status) {
          await localStorage.remove('buyNowCartId');
          await localStorage.set('cartCount', data?.data?.cart?.items.length);
          dispatch(setCartCount(data?.data?.cart?.items.length));
          return data;
        }
      }
    } catch (err) {
      debugLog('mergeCartsV2', err);
    }
  };

  const sortCartList = useCallback(data => {
    return data?.length > 0
      ? [...data].sort((a, b) => b?.is_free_product - a?.is_free_product)
      : [];
  }, []);

  const renderFirstFlatListItem = useCallback(
    () => (
      <View style={styles.buttonView}>
        {isLoggedIn && (
          <>
            {defaultWishList.length > 0 ? (
              <>
                <ErrorHandler
                  componentName={`${TAG} Button`}
                  onErrorComponent={<View />}>
                  <Button
                    text={t('wishList.wishList')}
                    type={
                      selectedTab === cardTabType.Wishlist
                        ? 'secondary'
                        : 'bordered'
                    }
                    radius="sx"
                    labelColor={
                      selectedTab === cardTabType.Wishlist
                        ? 'background'
                        : 'categoryTitle'
                    }
                    labelSize="mx"
                    size="extra-small"
                    paddingHorizontal="xx"
                    weight="500"
                    onPress={() => handleButtonClick(cardTabType.Wishlist)}
                    style={styles.btnStyle}
                    labelStyle={styles.btnTxt}
                  />
                </ErrorHandler>
                <Spacer type="Horizontal" size="xm" />
              </>
            ) : null}
          </>
        )}
        <ErrorHandler
          componentName={`${TAG} Button`}
          onErrorComponent={<View />}>
          <Button
            text={t('otherText.bestDeal')}
            type={
              selectedTab === cardTabType.Bestdeal ? 'secondary' : 'bordered'
            }
            radius="sx"
            labelColor={
              selectedTab === cardTabType.Bestdeal
                ? 'background'
                : 'categoryTitle'
            }
            labelSize="mx"
            size="extra-small"
            paddingHorizontal="xx"
            weight="500"
            onPress={() => handleButtonClick(cardTabType.Bestdeal)}
            style={styles.btnStyle}
            labelStyle={styles.btnTxt}
          />
        </ErrorHandler>
      </View>
    ),
    [
      isLoggedIn,
      defaultWishList.length,
      selectedTab,
      handleButtonClick,
      styles,
      t,
    ],
  );

  const handleFlatListContentSizeChange = useCallback(contentWidth => {
    setContentWidth(contentWidth);
    handleContentSizeChange(contentWidth);
  }, []);

  const addToCartButton = async (product: ProductData, qty: number) => {
    setIsLoading(false);
    const cartData = {
      ...product,
      qty,
    };
    AnalyticsEvents(
      'ADDED_TO_CART',
      'Added to Cart',
      cartData,
      userInfo,
      isLoggedIn,
    );
    await dispatch(
      addToCart({
        cart_items: [{data: {quantity: qty, sku: product?.sku}}],
        image: product?.media?.mobile_image,
      }),
    );

    getCartData(cartId, true, userInfo);
    setIsLoading(false);
  };

  const renderSecondFlatListItem = useCallback(
    ({item, index}) => (
      <ScrollView horizontal key={index}>
        <ErrorHandler
          componentName={`${TAG} ProductCardVertical`}
          onErrorComponent={<View />}>
          <ProductCardVertical
            index={index}
            skuId={item?.sku}
            actionBtn={item?.action_btn}
            size="large"
            maxWidth={checkDevice() ? 0.25 : 0.47}
            item={item}
            productType={item?.type}
            inStock={item.is_in_stock}
            maxSaleQty={item?.max_sale_qty}
            demoAvailable={item?.demo_available}
            msrp={item?.msrp}
            image={item?.media?.mobile_image}
            name={item?.name}
            rewardPoint={item?.reward_point_product}
            description={item?.short_description}
            rating={(item?.rating === 'null' || item.average_rating === null
              ? 0
              : Number(item?.rating) || Number(item?.average_rating)
            ).toFixed(1)}
            ratingCount={item?.rating_count ? `(${item?.rating_count})` : '(0)'}
            price={item?.price}
            sellingPrice={item?.selling_price}
            currencySymbol={item?.currency_symbol}
            discount={item?.discount?.label}
            onPress={() => {
              navigation.navigate('ProductDetail', {
                productId: item?.product_id,
                ProductItems: {
                  media: {
                    mobile_image: item?.media?.mobile_image,
                  },
                },
              });
            }}
            navigation={navigation}
            showWishlist={true}
            onCartPress={qty => addToCartButton(item, qty)}
            freeProducts={[]}
            isCartPage={true}
          />
        </ErrorHandler>
      </ScrollView>
    ),
    [navigation, addToCartButton, checkDevice],
  );

  const scrollToCouponView = () => {
    if (scrollViewRef.current && targetViewPosition.current !== null) {
      scrollViewRef?.current?.scrollTo({
        y: targetViewPosition.current,
        animated: true,
      });
      setOpenKeyboard(true);
      setTimeout(() => {
        setOpenKeyboard(false);
      }, 1500);
    }
  };

  const deliveryFree = useMemo(() => {
    return cartData?.pricing_details?.shipping_charges?.amount?.value === 0
      ? true
      : false;
  }, [cartData]);

  const handleContentSizeChange = contentWidth => {
    setListWidth(contentWidth);
    if (screenWidth > 0 && contentWidth > screenWidth) {
      const calculatedScrollBarWidth =
        (screenWidth / contentWidth) * (screenWidth / 4);
      setScrollBarWidth(Math.max(calculatedScrollBarWidth, 20));
    }
  };

  const scrollBarPosition = scrollX1.interpolate({
    inputRange: [0, Math.max(0, listWidth - screenWidth)],
    outputRange: [0, Math.max(0, screenWidth / 6 - scrollBarWidth)],
    extrapolate: 'clamp',
  });

  const keyExtractor = useCallback(item => item?.product_id?.toString(), []);

  const cartItemSeparator = useMemo(() => {
    return <Spacer size="xm" />;
  }, []);

  const openAddressModal = useCallback(() => {
    InteractionManager.runAfterInteractions(() => {
      setAddressConfirm(prevState => !prevState);
    });
  }, []);

  const openMapsModal = useCallback(
    (cartCheck = false, addressObj) => {
      InteractionManager.runAfterInteractions(() => {
        setCartCheckModal(cartCheck);
        setAddressConfirm(false);
        const deliverAddress = {
          ...addressObj,
          formatted_address: addressObj?.map_address,
        };
        const obj = {
          cardType: 'cart',
          location: true,
          coords: coords,
          goBack: () =>
            setIsAddressModalOpen(
              userInfo?.addresses?.length > 0 && cartCheckModal ? true : false,
            ),
          onChangeSelectAddress: updateData => onDeliverAddress(updateData),
        };
        if (addressObj?.skipLocation === true) {
          navigation.navigate('ManageAddress', {
            ...obj,
            address: deliverAddress,
          });
        } else {
          navigation.navigate('MapLocation', {...obj, deliverAddress});
        }
      });
    },
    [userInfo, coords, cartCheckModal, setIsAddressModalOpen, onDeliverAddress],
  );

  const onDeliverAddress = async data => {
    setIsAddressModalOpen(false);
    setSelectedAddress(data);
    await localStorage.set('delivery_address', data);
  };

  const scrollToBottom = () => {
    scrollViewRef?.current?.scrollToEnd({
      animated: true,
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerSection}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <Header
            searchIcon
            customIcon={true}
            navigation={navigation}
            backButton={true}
            text={t('cart.cart')}
            subTitle={
              cartData?.items.length > 0
                ? `${cartData?.items.length} ${t('orderReturn.items')}`
                : ''
            }
            useInsets
          />
        </ErrorHandler>
      </View>
      {isLoading ? (
        <CartLoader />
      ) : (
        <View style={styles.flexView}>
          {/* <View style={styles.background}>
            {cartData?.items?.length > 0 && (
              <ErrorHandler
                componentName={`${TAG} CartPositionStatus`}
                onErrorComponent={<View />}>
                <CartPositionStatus cartPositionStatus={positionStatus} />
              </ErrorHandler>
            )}
          </View> */}
          {!cartData?.items || cartData?.items?.length === 0 ? (
            <ErrorHandler
              componentName={`${TAG} CartEmptyView`}
              onErrorComponent={<View />}>
              <CartEmptyView navigation={navigation} />
            </ErrorHandler>
          ) : (
            <View style={styles.flexView}>
              <View style={styles.flexView}>
                <ScrollView
                  style={styles.flexView}
                  ref={scrollViewRef}
                  keyboardShouldPersistTaps="always"
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={onRefresh}
                    />
                  }>
                  <Spacer size="xm" />
                  <View style={styles.cartSection}>
                    <ErrorHandler
                      componentName={`${TAG} Address`}
                      onErrorComponent={<View />}>
                      <Address
                        cardType="cart"
                        addressType="cart"
                        buyNow={false}
                        addressTextStyle={styles.addressText}
                        addressData={userInfo?.addresses}
                        // onAddressOrPostcodeChange={checkDeliveryStatus}
                        deliveryStatusData={deliveryStatusData}
                        setSelectedAddress={setSelectedAddress}
                        selectedAddress={selectedAddress}
                        isAddressModalOpen={isAddressModalOpen}
                        setIsAddressModalOpen={setIsAddressModalOpen}
                        useInsets={true}
                        onPressAddNewAddress={() =>
                          navigation.navigate('ManageAddress', {
                            nextScreenName: 'PaymentPage',
                            nextScreenParams: {
                              selectedAddress: selectedAddress,
                              cart: cartData,
                              paymentMethods: [],
                            },
                          })
                        }
                        coords={coords}
                        openMapsModal={openMapsModal}
                      />
                    </ErrorHandler>
                    {!!promotionByCart && (
                      <View style={styles.promotionCart}>
                        <Label
                          text={promotionByCart}
                          size="m"
                          fontFamily="Medium"
                          color="green"
                        />
                      </View>
                    )}
                    {selectedAddress?.country_id ||
                    baseCountryData?.currency_code ? (
                      <View
                        style={[
                          styles.baseCountryVIew,
                          {
                            backgroundColor: deliveryFree
                              ? colors.greenLight
                              : colors.grey7,
                          },
                        ]}>
                        {selectedAddress?.country_id === 'IN' ||
                        (!selectedAddress?.country_id &&
                          baseCountryData?.country_id === 'IN') ? (
                          <View style={styles.flexRow}>
                            <ErrorHandler
                              componentName={`${TAG} FastImage`}
                              onErrorComponent={<View />}>
                              <FastImage
                                source={Icons.freeProductGif}
                                style={styles.freeProductImage}
                              />
                            </ErrorHandler>
                            <Spacer size="xm" type="Horizontal" />
                            <View style={styles.flexRowWidth}>
                              {cartData?.delivery_and_saving_text
                                .split(' ')
                                .map((word, index) => (
                                  <Label
                                    key={index}
                                    style={
                                      word?.startsWith('₹') ||
                                      word === 'Free' ||
                                      word === 'Delivery'
                                        ? styles.fontWeight
                                        : {}
                                    }
                                    text={word}
                                    fontFamily={
                                      word?.startsWith('₹') ||
                                      word === 'Free' ||
                                      word === 'Delivery'
                                        ? 'Medium'
                                        : 'Regular'
                                    }
                                    color={
                                      deliveryFree
                                        ? 'green2'
                                        : 'lightSlateBlue1'
                                    }
                                    textTransform="capitalize"
                                    size="m"
                                  />
                                ))}
                            </View>
                          </View>
                        ) : (
                          <Label
                            text={t('cart.shippingCharges')}
                            fontFamily="SemiBold"
                            textTransform="capitalize"
                            color={deliveryFree ? 'green2' : 'lightSlateBlue1'}
                            size="m"
                          />
                        )}
                      </View>
                    ) : null}
                    {freeProduct?.length > 0 && (
                      <>
                        <Spacer size="xm" />
                        <ErrorHandler
                          componentName={`${TAG} CartOfferCarousel`}
                          onErrorComponent={<View />}>
                          <CartOfferCarousel data={freeProduct} />
                        </ErrorHandler>
                      </>
                    )}
                    <Spacer size="xm" />
                    <ErrorHandler
                      componentName={`${TAG} ListView`}
                      onErrorComponent={<View />}>
                      <ListView
                        showsVerticalScrollIndicator={false}
                        keyExtractor={keyExtractor}
                        extraData={cartItems}
                        data={cartItems}
                        renderItem={renderCartItem}
                        ItemSeparatorComponent={cartItemSeparator}
                      />
                    </ErrorHandler>
                  </View>
                  <Spacer size="xm" />
                  <View
                    onLayout={event =>
                      setContainerWidth(event.nativeEvent.layout.width)
                    }>
                    <WithGradient
                      gradientColors={[colors.cerise0, colors.cerise24]}
                      gradientStyle={styles.wishListLinear}
                      gradientAngle={0}>
                      <View>
                        <View style={styles.forgetView}>
                          <Label
                            text={t('orderListing.didForget')}
                            size="xl"
                            fontFamily="Medium"
                            color="categoryTitle"
                          />
                          <Spacer size="s" />
                          <View style={styles.excludeSubView}>
                            {scrollX > 0 && (
                              <TouchableOpacity onPress={scrollToStart}>
                                <ImageIcon icon="Include" size="x3l" />
                              </TouchableOpacity>
                            )}
                            <Spacer size="xms" />
                            {Math.round(scrollX + containerWidth) <
                              Math.round(contentWidth) && (
                              <TouchableOpacity onPress={scrollToEnd}>
                                <ImageIcon icon="Exclude" size="x3l" />
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                        <Spacer size="xms" />
                        <FlatList
                          data={['']}
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          renderItem={renderFirstFlatListItem}
                          removeClippedSubviews={true}
                          windowSize={5}
                          maxToRenderPerBatch={5}
                          updateCellsBatchingPeriod={50}
                        />
                        <OptimizedFlatList
                          horizontal
                          ref={flatListRef}
                          onScroll={handleScroll}
                          data={
                            (selectedTab === cardTabType.Wishlist
                              ? defaultWishList
                              : bestDeal) || []
                          }
                          showsHorizontalScrollIndicator={false}
                          style={styles.productListView}
                          keyExtractor={keyExtractor}
                          scrollEventThrottle={16}
                          onContentSizeChange={handleFlatListContentSizeChange}
                          renderItem={renderSecondFlatListItem}
                          // removeClippedSubviews={true}
                          // windowSize={5}
                          // maxToRenderPerBatch={5}
                          // updateCellsBatchingPeriod={50}
                        />
                      </View>
                      <Spacer size="l" />
                      {scrollBarWidth > 0 &&
                        (selectedTab === cardTabType.Wishlist
                          ? defaultWishList
                          : bestDeal
                        ).length > 2 && (
                          <ErrorHandler
                            componentName={`${TAG} HorizontalScrollBar`}
                            onErrorComponent={<View />}>
                            <HorizontalScrollBar
                              activeColor={colors.smoothPink}
                              scrollBarWidth={scrollBarWidth}
                              scrollBarPosition={scrollBarPosition}
                            />
                          </ErrorHandler>
                        )}
                    </WithGradient>
                  </View>
                  {isLoggedIn && (
                    <>
                      <View
                        onLayout={event => {
                          const {y} = event.nativeEvent.layout;
                          targetViewPosition.current = y;
                        }}>
                        <ErrorHandler
                          componentName={`${TAG} CouponCart`}
                          onErrorComponent={<View />}>
                          <CouponCart
                            alwaysShow={true}
                            isCouponOpen={isCouponOpen}
                            couponCode={
                              appliedCoupon
                                ? cartData?.coupon_code?.code
                                : couponCode
                            }
                            couponList={couponList}
                            appliedCoupon={appliedCoupon}
                            savedAmount={`${
                              cartData?.pricing_details?.discounts?.find(
                                discount => discount?.code === 'coupon',
                              )?.amount?.value ?? 0
                            }`}
                            saveNote={`${t('checkOut.saved')} ₹ ${
                              cartData?.pricing_details?.discounts?.find(
                                discount => discount?.code === 'coupon',
                              )?.amount?.value ?? 0
                            }`}
                            openCoupon={() => setCouponOpen(!isCouponOpen)}
                            changeCouponCode={(code: string) =>
                              setCouponCode(code)
                            }
                            onApplyDiscount={(code: string) => {
                              appliedCoupon
                                ? removeCoupon()
                                : applyDiscount('coupon_code', code);
                            }}
                            openCouponListModal={() =>
                              setIsCouponModalOpen(!isCouponModalOpen)
                            }
                            openKeyboard={openKeyboard}
                          />
                        </ErrorHandler>
                      </View>
                      <Spacer size="xm" />
                    </>
                  )}
                  <ErrorHandler
                    componentName={`${TAG} CartPriceSection`}
                    onErrorComponent={<View />}>
                    <CartPriceSection
                      priceDetails={priceDetails}
                      cartData={cartData}
                      onSetWeight={() => setWeight(true)}
                      onCouponClick={() => scrollToCouponView()}
                    />
                  </ErrorHandler>
                  <Spacer size="l" />
                  <ErrorHandler
                    componentName={`${TAG} PaymentSupportCart`}
                    onErrorComponent={<View />}>
                    <PaymentSupportCart />
                  </ErrorHandler>
                </ScrollView>
                <View
                  style={[
                    styles.bottomBtnView,
                    {paddingBottom: insets?.bottom / 2 + 8},
                  ]}>
                  <TouchableOpacity
                    activeOpacity={1}
                    onPress={() => scrollToBottom()}>
                    <Label
                      text={
                        priceDetails?.item_total_selling_price?.currency +
                        ' ' +
                        priceDetails?.item_total_selling_price?.regularValue
                      }
                      size="m"
                      weight="500"
                      color="darkGray"
                      textDecorationLine="line-through"
                    />
                    <View style={styles.rowCenter}>
                      <Label
                        text={`${t('otherText.total')} - ${
                          priceDetails?.grand_total?.currency
                        } ${priceDetails?.grand_total?.value}`}
                        size="mx"
                        weight="500"
                        color="text"
                      />
                      <Spacer size="sx" type="Horizontal" />
                      <ImageIcon
                        size="xl"
                        icon="infoCircle"
                        style={styles.infoStyle}
                      />
                    </View>
                  </TouchableOpacity>
                  <View style={styles.flexView} />
                  <TouchableOpacity onPress={() => onCheckout(false)}>
                    <WithGradient
                      gradientColors={[colors.coral, colors.persimmon]}
                      gradientStyle={styles.onCheckoutView}>
                      <Label
                        text={t('otherText.proceed')}
                        size="mx"
                        fontFamily="Medium"
                        color="background"
                        align="center"
                      />
                    </WithGradient>
                  </TouchableOpacity>
                </View>
              </View>
              <View>
                {/* ***************** Coupon List modal component********************** */}
                {isCouponModalOpen && (
                  <ErrorHandler
                    componentName={`${TAG} CouponListModal`}
                    onErrorComponent={<View />}>
                    <CouponListModal
                      visible={isCouponModalOpen}
                      couponList={couponList}
                      onClose={() => setIsCouponModalOpen(!isCouponModalOpen)}
                      couponItemClick={(item: couponItem) => {
                        setCouponCode(item?.coupon_code);
                        setIsCouponModalOpen(false);
                        applyDiscount('coupon_code', item?.coupon_code);
                      }}
                    />
                  </ErrorHandler>
                )}

                {addressConfirm && (
                  <ErrorHandler
                    componentName={`${TAG} AddressConfirmModal`}
                    onErrorComponent={<View />}>
                    <AddressConfirmModal
                      visible={addressConfirm}
                      title={t('address.locationTitle')}
                      desc={t('address.locationMsg')}
                      btn1Text={t('buttons.no')}
                      btn2Text={t('buttons.tryNow')}
                      onClose={openAddressModal}
                      onMap={() => openMapsModal(false, selectedAddress)}
                      onSkip={() => onCheckout(true)}
                    />
                  </ErrorHandler>
                )}

                {/* ***************** delete modal component********************** */}
                {removeCartModal && (
                  <ErrorHandler
                    componentName={`${TAG} CartRemoveModal`}
                    onErrorComponent={<View />}>
                    <CartRemoveModal
                      visible={removeCartModal}
                      item={removeCart}
                      onClose={() => setRemoveCartModal(false)}
                      removeCart={() => deleteCartProduct(removeCart)}
                      addWishlist={() => onPressWishlist(removeCart)}
                    />
                  </ErrorHandler>
                )}
                {returnInfoModel && (
                  <ErrorHandler
                    componentName={`${TAG} DeliveryInfoModal`}
                    onErrorComponent={<View />}>
                    <DeliveryInfoModal
                      visible={returnInfoModel}
                      onClose={() => setReturnInfoModel(false)}
                      infoIcon="deliveryInfo"
                      deliveryStatusData={deliveryStatusData}
                    />
                  </ErrorHandler>
                )}
                {successModel && (
                  <ErrorHandler
                    componentName={`${TAG} SuccessModal`}
                    onErrorComponent={<View />}>
                    <SuccessModal
                      visible={successModel}
                      title={`${t(
                        'cart.yay',
                      )} ${cartData?.coupon_code?.code?.toUpperCase()} ${t(
                        'cart.applied',
                      )}`}
                      description={`${t('cart.youSaved')} ₹ ${
                        cartData?.pricing_details?.discounts?.find(
                          discount => discount?.code === 'coupon',
                        )?.amount?.value ?? 0
                      }\n${t('cart.thisOrder')}`}
                      onClose={() => setSuccessModel(!successModel)}
                      titleStyle={styles.titleStyle}
                      descriptionStyle={styles.desStyle}
                    />
                  </ErrorHandler>
                )}
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

export default CartScene;

//
//  NotificationViewController.swift
//  NCEDev
//
//  Created by Dev on 21/12/24.
//

import UIKit
import UserNotifications
import UserNotificationsUI
import MoEngageRichNotification
  
class NotificationViewController: UI<PERSON>iew<PERSON>ontroller, UNNotificationContentExtension {
    override func viewDidLoad() {
        super.viewDidLoad()
        // Set App Group ID
        MoEngageSDKRichNotification.setAppGroupID("group.com.dentalkart.vasadental.dev.MoE")
    }
  
    
    func didReceive(_ notification: UNNotification) {
        // Method to add template to UI
        MoEngageSDKRichNotification.addPushTemplate(toController: self, withNotification: notification)
    }
}

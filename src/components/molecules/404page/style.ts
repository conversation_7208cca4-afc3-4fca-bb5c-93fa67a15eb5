import {StyleSheet} from 'react-native';
import {Sizes} from 'common';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    mainView: {
      height: '100%',
      width: '100%',
    },
    button1: {
      borderRadius: Sizes.xm,
      paddingVertical: Sizes.mx,
      paddingHorizontal: Sizes.x7l,
    },
    button2: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      paddingVertical: Sizes.mx,
      paddingHorizontal: Sizes.x6l,
    },
    labelView: {
      marginTop: Sizes.ex4l + Sizes.x8l,
      flexDirection: 'column',
      position: 'absolute',
      width: '100%',
      paddingHorizontal: Sizes.xx4l,
    },
  });

export default styles;

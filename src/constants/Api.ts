import objectToQueryString from 'utils/objectToQueryString';

const BASE_URL = 'https://www.dentalkart.com/';
const API_BASE_URL = 'https://apis.dentalkart.com';
const API_BASE_URL_STAGING = 'https://www.staging-apis.dentalkart.com';
const INTERFACE_BASE_URL =
  'https://interface-dev.dentalkart.com/interface/api/v1';
export const FEEDS_API_BASE_URL = 'https://feeds-prod.dentalkart.com';
export const PRODUCT_IMAGE_URL =
  'https://images.dentalkart.com/media/catalog/product';
const SERVERLESS_BASE_URL = 'https://serverless-prod.dentalkart.com';
const CUSTOMER_BASE_URL = 'https://customer-staging.dentalkart.com';
export const BASE_URL_INTERFACE =
  'https://interface-dev.dentalkart.com/interface/api/v1';
export const BASE_URL_SERVERLESS = 'https://serverless-prod.dentalkart.com';
export const BASE_URL_ADMIN = 'https://dental-admin.dentalkart.com';
export const BASE_URL_CART = 'https://gq-cart.dentalkart.com/cart/api/v1';
export const BASE_URL_UTILSPY = 'https://utilspy-staging.dentalkart.com';
export const BASE_URL_ORDER_SERVICES = 'https://staging-order.dentalkart.com';
export const BASE_URL_REVIEW_STAGING = 'https://review-staging.dentalkart.com';
export const BASE_URL_ORDER_RETURN_SERVICES =
  'https://return-staging.dentalkart.com';
export const BASE_URL_REWARD_COINS = 'http://rewards-staging.dentalkart.com';
export const BASE_URL_CAN_CANCEL = 'http://ordermg-staging.dentalkart.com';
export const BASE_URL_WISHLIST = 'http://wishlist-staging.dentalkart.com';
export const BASE_URL_MAMBERSHIP_SERVICES =
  'https://rewards-staging.dentalkart.com';
export const BASE_URL_ORDERMG_SERVICES =
  'https://ordermg-staging.dentalkart.com';
export const FAQS_BASE_URL = 'https://dental-admin.dentalkart.com';
export const X_API_KEY = 'ZFobrRyccnTyXyXHPUVO4eyyKEKoSjWB';
// XUQVEomDnXBI5IaZabnujPkbS1rpPlSseG

const API = {
  checkout: {
    estimate_shipping_methods: `${BASE_URL}rest/default/V1/carts/mine/estimate-shipping-methods`,
    set_estimate_shipping_methods: cart_id =>
      `${BASE_URL}rest/V1/guest-carts/${cart_id}/estimate-shipping-methods`,
    set_shipping_information: cart_id =>
      `${BASE_URL}rest//V1/guest-carts/${cart_id}/shipping-information`,
    set_shipping_and_billing_information: `${BASE_URL}rest/default/V1/carts/mine/dk-shipping-information`,
    send_payment_information: `${BASE_URL}rest/default/V1/carts/mine/dt-payment-information`,
    order_success: `${BASE_URL}rest/V1/checkout/order/success`,
    razorpay: `${BASE_URL}rest/V1/razorpay/payment/order`,
  },
  validateFields: `${BASE_URL}rest/V1/mobileapp/directory/checkOptionalFields`,
  FEATURE_PRODUCT_LIST: `${API_BASE_URL}/api/v1/products/list`,
  CATEGORY_DETAIL_LIST: categoryId =>
    `${API_BASE_URL}/api/v1/categories/${categoryId}/details`,

  CATEGORY_FILTER_DATA: category_id =>
    `${API_BASE_URL}/api/v1/category-filters?category_id=${category_id}`,
  BASE_URL: 'https://interface-dev.dentalkart.com/interface/api/v1',
  // HOMEPAGE_SECTIONS: '/homepage/api/v1/homepage-sections',
  SIGNIN: `${API_BASE_URL}/customer/api/v1/customers/auth/send-otp`,
  GET_ME: `${API_BASE_URL}/customer/api/v1/customers/me`,
  UPDATE_ME: `${API_BASE_URL}/customer/api/v1/customers/me`,
  // ? ??//
  UPDATE_PASSWORD: `${API_BASE_URL}/customer/api/v1/customers/me/password`,
  VERIFYOTP: `${API_BASE_URL}/customer/api/v1/customers/auth/verify`,
  CREATECUSTOMER: `${API_BASE_URL}/customer/api/v1/customers`,
  SOCIAL_LOGIN: `${API_BASE_URL}/customer/api/v1/customers/auth/social-login`,
  HOMEPAGE_SECTIONS: `${API_BASE_URL}/interface/api/v1/homepage-sections`,
  ALL_BRANDS: (queryParams: string) => `${API_BASE_URL}/brands${queryParams}`,
  ALL_CATEGORIES: `${API_BASE_URL}/api/v1/categories/list`,
  URL_RESOLVER: (url: string) =>
    `${API_BASE_URL}/api/v1/url-resolver?url=${url}`,
  PRODUCT_SUGGESTION: `${API_BASE_URL}/product/suggestion`,

  /********** Magazine Page Api ***********/
  GET_MEGAZINE_LIST: `${API_BASE_URL}/magazines`,
  GET_PUBLISHER_LIST: `${API_BASE_URL}/magazines/publishers`,
  MAGAZINE_BANNER: `${API_BASE_URL}/magazines/banners`,
  GET_MAGAZINE_BY_ID: (magazineId: string) =>
    `${API_BASE_URL}/magazines/detail/?id=${magazineId}`,
  /********** News Page Api ***********/
  GET_NEWS_LIST: `${API_BASE_URL}/interface/api/v1/news?page=1&limit=10`,
  /********** cart api ***********/
  GENERATE_CART: `${API_BASE_URL}/cart/api/v1/carts`,
  MERGE_CART: (guestCartId: string) =>
    `${API_BASE_URL}/cart/api/v1/carts/${guestCartId}/merge`,
  ADD_TO_CART: (cartId: string) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/items`,
  SET_CART_ADDRESS: (cartId: string) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/addresses`,
  UPDATE_CART_ITEMS: (cartId: string) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/items`,
  DELETE_CART_ITEMS: (cartId: string, itemId: number, buyNow: boolean) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/items/${itemId}?buy_now=${buyNow}`,
  SHIPPING_RATES: (countryCode: string) =>
    `${API_BASE_URL}/cart/api/v1/carts/${countryCode}/shipping-rates`,
  APPLY_DISCOUNT_ELEMENT: (cartId: string) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/apply-discount-element`,
  REMOVE_DISCOUNT_ELEMENT: (cartId: string) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/remove-coupon`,
  APPLICABLE_REWARDS_POINTS: (cartId: string, buyNow: boolean) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/reward-points?buy_now=${buyNow}`,
  CUSTOMER_REGISTRATIONS: `${API_BASE_URL}/order-mg/api/v1/customer-registrations/`,
  CUSTOMER_REGISTRATIONS_DELETE: (regId: number) =>
    `${API_BASE_URL}/order-mg/api/v1/customer-registrations/${regId}/`,
  CUSTOMER_REGISTRATIONS_UPDATE: (regId: number) =>
    `${API_BASE_URL}/order-mg/api/v1/customer-registrations/${regId}/`,
  COUPONS_LIST: (cartId: string, buyNow: boolean) =>
    `${API_BASE_URL}/cart/api/v1/carts/${cartId}/coupons?buy_now=${buyNow}`,
  CHECK_SERVICE_AVAILABILITY: `${API_BASE_URL}/utility-py/api/v1/delivery/product-delivery-options?required_sections=service_availability&required_sections=delivery_days`,
  CART_PAYMENT_METHODS: (
    countryCode: string,
    postcode: string,
    cartAmount: number,
    cartWeight: number, //in grams
    isCodEligible: boolean,
  ) =>
    `${API_BASE_URL}/utility-py/api/v1/delivery/cart-payment-methods?country_code=${countryCode}&postcode=${postcode}&cart_amount=${cartAmount}&cart_weight=${cartWeight}&is_cod_eligible=${isCodEligible}`,
  /********** payment page apis ***********/
  CREATE_ORDER: `${API_BASE_URL}/order/api/v1/orders`,
  FETCH_PAYMENT: `${API_BASE_URL}/order/api/v1/orders/payments`,
  PRODUCT_DETAIL: productId => `${API_BASE_URL}/api/v1/products/${productId}`,
  PRODUCT_ATTRIBUTES: productId =>
    `${API_BASE_URL}/api/v1/products/${productId}/attachments`,
  PRODUCT_ALL_ATTRIBUTES: productId =>
    `${API_BASE_URL}/api/v1/products/${productId}/attribute-meta-data`,
  CHILD_PRODUCT: productId =>
    `${API_BASE_URL}/api/v1/products/${productId}/children`,
  REVIEWS: (
    productId: number,
    sortBy: string = 'most_recent',
    pageNo: number,
    limit: number = 35,
    child_ids: Array<number> = [],
  ) =>
    `${API_BASE_URL}/review/api/v1/products/${productId}/reviews?sort_by=${sortBy}&page=${pageNo}&limit=${limit}${
      child_ids.length > 0
        ? '&' + child_ids?.map(e => 'child_ids=' + e).join('&')
        : ''
    }`,
  BUY_NOW: `${API_BASE_URL}/cart/api/v1/carts/buy-now`,
  FAQS: (productId: number, serarchText: string) =>
    `${API_BASE_URL}/question/get?product_id=${productId}&search=${serarchText}`,
  POST_FAQS: `${API_BASE_URL}/question/add`,
  CREATECUSTOMER_ADDRESS: `${API_BASE_URL}/customer/api/v1/customer-addresses`,
  DELETECUSTOMER_ADDRESS: id =>
    `${API_BASE_URL}/customer/api/v1/customer-addresses/${id}`,
  MY_MAMBERSHIP: `${API_BASE_URL}/reward/api/v1/customer-memberships/me`,
  MAMBERSHIP_PLAN: `${API_BASE_URL}/reward/api/v1/memberships`,
  ALL_FAQS: `${API_BASE_URL}/faq/items`,
  REWARD_COINS: `${API_BASE_URL}/reward/api/v1/rewards/summary`,
  REWARD_TRANCTIONS: data =>
    `${API_BASE_URL}/reward/api/v1/rewards/transactions?${data}`,
  ORDER_LIST: data =>
    `${API_BASE_URL}/serverless/api/v1/customer-orders?${data}`,
  CAN_CANCEL: orderId =>
    `${API_BASE_URL}/order-mg/api/v1/cancel-orders/${orderId}/cancelable`,
  CANCEL_REASONS_LIST: `${API_BASE_URL}/order-mg/api/v1/cancel-reasons`,
  ORDER_DETAIL: orderId =>
    `${API_BASE_URL}/serverless/api/v1/customer-orders/${orderId}/summary`,
  TEACK_SHIPMENT: orderId =>
    `${API_BASE_URL}/serverless/api/v1/customer-orders/${orderId}/shipments`,
  INVOICE_LINK: `${API_BASE_URL}/serverless/api/v1/customer-orders/shipment-invoice`,
  RETURN_LIST: data => `${API_BASE_URL}/return/api/v1/returns?${data}`,
  TRACK_RETURN_ORDER: (productSku: string, returnId: number) =>
    `${API_BASE_URL}/return/api/v1/returns/${returnId}/items/${productSku}/track-detail`,
  PICK_RETURN_DATE_UPDATE: `${API_BASE_URL}/return/api/v1/returns/failed-pickups`,
  RETURNABLE_ITEMS_LIST: (orderId: number) =>
    `${API_BASE_URL}/return/api/v1/returns/orders/${orderId}/returnable-items`,
  RETURN_REASON_LIST: `${API_BASE_URL}/return/api/v1/returns/reasons-and-actions`,
  ATTECHMENT_URL: `${API_BASE_URL}/return/api/v1/returns/file/upload`,
  RETURN_REQUEST_URL: `${API_BASE_URL}/return/api/v1/returns`,
  BUY_AGAIN_URL: `${API_BASE_URL}/api/v1/products/details`,
  // WISHLIST APIS

  WISHLIST_ENDPOINT: `${API_BASE_URL}/wishlist/api/v1/wishlists`,
  WISHLIST_TO_WISHLIST: `${API_BASE_URL}/wishlist/api/v1/wishlists/default/products`,
  DEFAULT_WISHLIST_ENDPOINT: `${API_BASE_URL}/wishlist/api/v1/wishlists/default`,

  // WISHLIST APIS
  MY_REFERRAL: 'https://referral-prod.dentalkart.com/rewards/referred-count',

  // pdp _page Apis
  RECOMMENDED_PRODUCTS: (id: any) =>
    `${API_BASE_URL}/api/v1/products/${id}/recommended-products`,
  SUBMIT_FEEDBACK: `${API_BASE_URL}/product/feedback`,
  createBolkProduct: `${BASE_URL_ORDERMG_SERVICES}/order-mg/api/v1/bulk-order/requests`,
  // sell_on_dentalKart
  SELL_ON_DENTALKART: `${API_BASE_URL}/order-mg/api/v1/sell-on-dentalkart/requests`,
  createRateAndReviews: (id: any) =>
    `${BASE_URL_REVIEW_STAGING}/review/api/v1/products/${id}/reviews`,

  checkCanRateAndReviews: (id: any) =>
    `${BASE_URL_REVIEW_STAGING}/review/api/v1/reviews/validate_submission/${id}`,
};

export default API;

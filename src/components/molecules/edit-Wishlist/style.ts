import {Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    buttonCreate: {
      backgroundColor: colors.categoryTitle,
      paddingVertical: Sizes.mx,
      width: '100%',
      borderRadius: Sizes.xm,
    },
    PhoneInput: {
      borderRadius: Sizes.xm,
      paddingRight: Sizes.l,
      paddingLeft: Sizes.xms,
    },
    headingBorder: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    a1: {
      position: 'relative',
      width: '100%',
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      justifyContent: 'center',
    },
    input: {
      alignItems: 'center',
      fontWeight: '500',
      fontSize: Sizes.mx,
      color: colors.text2,
      paddingLeft: Sizes.xms,
      height: Platform.OS === 'ios' ? Sizes.x6l : Sizes.x46,
    },
    rightText: {
      position: 'absolute',
      right: Sizes.l,
      fontSize: Sizes.l,
      color: 'gray',
    },
    modalContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalView: {
      paddingVertical: Sizes.xl,
      paddingBottom: Sizes.x4l,
      width: '100%',
      backgroundColor: 'white',
      paddingHorizontal: Sizes.xl,
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
    },
  });

export default styles;

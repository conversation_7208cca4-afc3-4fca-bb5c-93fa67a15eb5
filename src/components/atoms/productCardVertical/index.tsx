import React, {useCallback, useState, useEffect, useMemo, useRef} from 'react';
import {
  ActivityIndicator,
  Keyboard,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {t} from 'i18next';
import {
  ImageIcon,
  Label,
  Quantity,
  Separator,
  Spacer,
  Tag,
  WishlistButton,
} from 'components/atoms';
import getImageUrl, {productDummyImage} from 'utils/imageUrlHelper';
import {Button} from 'components/molecules';
import {Sizes} from 'common';
import {useDispatch, useSelector} from 'react-redux';
import Icons from 'common/icons';
import {notify} from 'app-redux-store/slice/appSlice';
import {checkDevice, debounce} from 'utils/utils';
import {navigate} from 'utils/navigationRef';
import {showInfoMessage} from 'utils/show_messages';
import ErrorHandler from 'utils/ErrorHandler';
import {AnalyticsEvents} from 'components/organisms';
import {addToCart} from 'app-redux-store/slice/appSlice';
import stylesWithOutColor from './style';
import {useDebouncedCallback} from 'use-debounce';
import {debugError, debugLog} from 'utils/debugLog';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';
import { appsFlyerEvent } from 'components/organisms/analytics-Events/appsFlyerEvent';
import { resolveUrl } from 'utils/resolveUrl';

const addToCartButtonVisibility = (isDemoAvailable: string, msrp: any) => {
  const demoPrice =
    isDemoAvailable && (isDemoAvailable === '1' || isDemoAvailable === 'Yes')
      ? true
      : false;
  const reqPrice = msrp ? true : false;
  const hideButton = reqPrice || demoPrice;
  return hideButton;
};

type Props = {
  index?: number;
  item: Partial<ProductData>;
  onPress?: () => void; // TODO: mbl remove this
  onCartPress?: (count: number) => void; // only relevant when hideCart is true or isCartPage is true
  onPressWishlist?: () => void;
  freeProducts?: FreeProducts[];
  maxWidth?: number;
  size?: 'small' | 'medium' | 'large';
  showWishlist?: boolean;
  topRightComponent?: React.ReactElement;
  hideAddToCart?: boolean;
  imageWithBorder?: boolean;
  image: string;
  name: string;
  description?: string;
  actionBtn?: object;
  rewardPoint?: number;
  rating?: string;
  skuId?: string;
  ratingCount?: string;
  price: number;
  sellingPrice: number;
  currencySymbol: string;
  discount: string;
  isBestSeller?: string;
  imageUrl?: string;
  productType?: 'simple' | 'grouped';
  maxSaleQty?: number;
  minSaleQty?: number;
  height?: number;
  defaultQty?: number;
  demoAvailable?: boolean;
  msrp?: any;
  inStock?: boolean;
  hideCart?: boolean;
  hideCartBtn?: boolean;
  navigation: any;
  waldent?: boolean;
  isCartPage?: boolean;
  isSearchPage?: boolean;
  onClose?: () => void;
  type?: string;
  onFocus?: () => void;
};

const ProductCardVertical = ({
  index,
  onCartPress, // only relevant when hideCart is true or isCartPage is true
  onPress,
  maxWidth,
  actionBtn,
  topRightComponent,
  imageUrl,
  skuId,
  size = 'medium',
  image,
  name,
  description,
  rewardPoint,
  rating,
  ratingCount,
  price = 0,
  sellingPrice = 0,
  currencySymbol,
  discount,
  isBestSeller,
  productType,
  maxSaleQty = 100,
  minSaleQty = 0,
  defaultQty = 1,
  demoAvailable,
  msrp,
  inStock,
  item,
  hideCart = false,
  hideCartBtn = false,
  hideAddToCart = false,
  navigation,
  showWishlist = false,
  waldent = false,
  isCartPage = false,
  isSearchPage = false,
  onClose,
  type,
  onFocus,
}: Props) => {
  const TAG = 'ProductCartVertical';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isAddToCartPressed, setIsAddToCartPressed] = useState(false);
  const [isNotifyPressed, setIsNotifyPressed] = useState(false);
  // state updates are async and sometimes might give stale value hence we are keeping a refernece for immeidate value
  // don't use setQty use updateQty instead to update both state and ref
  const [qtyForUI, setQtyForUI] = useState(defaultQty);
  const qtyRef = useRef(defaultQty);
  const [lastQty, setLastQty] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const getFreeGiftItem = useSelector(
    (state: RootState) => state.app.getFreeGiftItem,
  );

  const updateQty = useCallback((count: number) => {
    setQtyForUI(count);
    qtyRef.current = count;
  }, []);

  const matchSku = useMemo(() => {
    return getFreeGiftItem?.some(e => e?.product_sku === skuId);
  }, [getFreeGiftItem, skuId]);

  const isHide = useMemo(() => {
    if (String(discount).includes('0.0%')) {
      let num = String(discount).split('%')[0];
      return !Number(num);
    }
    return false;
  }, [discount]);

  const debouncedQuantityUpdate = useDebouncedCallback((count: number) => {
    handleQuantityUpdate(count);
  }, 500);

  const flushQuantityUpdate = useCallback(async () => {
    if (Keyboard.isVisible()) {
      quantityInputRef.current?.blur();
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    debouncedQuantityUpdate.flush();
  }, [debouncedQuantityUpdate]);

  const triggerAddToCartAPI = useCallback(async () => {
    await flushQuantityUpdate();
    if (qtyRef.current === 0) {
      return;
    }
    const cartData = {
      ...item,
      qty: qtyRef.current,
    };
    if (isSearchPage) {
      AnalyticsEvents(
        'ADDED_TO_CART_SEARCH',
        'Added to Cart',
        cartData,
        userInfo,
        isLoggedIn,
      );
      trackEvent('ADD_TO_CART', {
        contentId: cartData?.sku || cartData?.landing_page_entity?.product_id || cartData?.product_id,
        contentType: 'product',
        currency: 'INR',
        value: cartData?.selling_price,
        // params: { cartData: cartData }
      });
      // appsflyer Add To Cart Event 
      appsFlyerEvent('AddToCart', {
        price: cartData?.special_price,
        sku: cartData?.sku,
        productId: cartData?.objectID,
        category: cartData?.name,
        currency: 'INR',
        quantity: cartData?.qty,
      });
    } else {
      AnalyticsEvents(
        'ADDED_TO_CART',
        'Added to Cart',
        cartData,
        userInfo,
        isLoggedIn,
      );
      trackEvent('ADD_TO_CART', {
        contentId: cartData?.sku || cartData?.landing_page_entity?.product_id || cartData?.product_id,
        contentType: 'product',
        currency: 'INR',
        value: cartData?.selling_price,
        // params: { cartData: cartData}
      });
      // appsflyer Add To Cart Event
      appsFlyerEvent('AddToCart', {
        price: cartData?.selling_price,
        sku: cartData?.sku,
        productId: cartData?.product_id,
        category: cartData?.name,
        currency: 'INR',
        quantity: cartData?.qty,
      });
    }
    if (isSearchPage) {
      await dispatch(
        addToCart({
          cart_items: [
            {
              data: {
                quantity: qtyRef.current,
                sku: item?.sku || item?.landing_page_entity?.sku,
              },
            },
          ],
          image: item?.thumbnail_url,
        }),
      );
    } else {
      await dispatch(
        addToCart({
          cart_items: [
            {
              data: {
                quantity: qtyRef.current,
                sku: item?.sku || item?.landing_page_entity?.sku,
              },
            },
          ],
          image: item?.media?.mobile_image,
        }),
      );
    }
  }, [
    debouncedQuantityUpdate,
    dispatch,
    isLoggedIn,
    isSearchPage,
    item,
    userInfo,
  ]);

  const handleCardPress = useCallback(() => {
    if (onPress) {
      onPress();
      return;
    }
    if (item?.product_id && !isLoading) {
      navigation.push('ProductDetail', {
        productId: item?.product_id,
        ProductItems: item,
      });
    }
  }, [onPress, navigation, item, isLoading]);

  useEffect(() => {
    updateQty(defaultQty);
  }, [defaultQty, updateQty]);

  useEffect(() => {
    setIsNotifyPressed(false);
  }, [isLoggedIn]);

  const updateCart = useCallback(
    async (value: number) => {
      setLastQty(value);
      if (hideCart || isCartPage) {
        if (value === 0) {
          setIsAddToCartPressed(false);
          setIsLoading(false);
          if (hideCartBtn) {
            await onCartPress?.(value);
          }
          return;
        }
        await onCartPress?.(value);
      } else {
        await triggerAddToCartAPI();
        updateQty(1);
      }
      setIsAddToCartPressed(false);
      setIsLoading(false);
      if (type && type === 'modal') {
        onClose?.();
      }
    },
    [
      hideCart,
      isCartPage,
      type,
      onCartPress,
      triggerAddToCartAPI,
      updateQty,
      onClose,
    ],
  );

  const onSetQty = useCallback(
    (value: number) => {
      updateQty(value);
      updateCart(value);
    },
    [updateCart, updateQty],
  );

  const handleAddToCart = useCallback(async () => {
    await flushQuantityUpdate();
    setIsLoading(true);
    updateCart(qtyRef.current);
  }, [updateCart, flushQuantityUpdate]);

  const cartBtnAction = useCallback(() => {
    if (actionBtn?.text !== 'Request Price') {
      if (hideCart) {
        updateQty(0);
        setLastQty(0);
        onCartPress?.(0);
      } else {
        updateQty(1);
      }
      setIsAddToCartPressed(true);
    }
  }, [actionBtn?.text, hideCart, onCartPress, updateQty]);

  const containerStyle = useMemo(() => {
    return [
      styles.container,
      maxWidth ? {width: Sizes.windowWidth * maxWidth} : styles.fOne,
      !!image && checkDevice() && {marginRight: skuId ? 0 : Sizes.m},
    ];
  }, [styles.container, styles.fOne, maxWidth, image, skuId]);

  const innerContainerStyle = useMemo(() => {
    return [styles.innerContainer, {justifyContent: 'space-between'}];
  }, [styles.innerContainer]);

  const productImageContainerStyle = useMemo(() => {
    if (size === 'small') {
      return [styles.productImageContainer, {marginHorizontal: -Sizes.xms}];
    }
    return [styles.productImageContainer, {margin: Sizes.xs}];
  }, [styles.productImageContainer, size]);

  const fastImageStyle = useMemo(() => {
    return [styles.image, size === 'small' ? styles.imageSmall : {}];
  }, [styles.image, styles.imageSmall, size]);

  const imageSource = useMemo(() => {
    return image ? {uri: getImageUrl(image)} : Icons.defaultImage;
  }, [image]);

  const originalPriceText = useMemo(() => {
    return currencySymbol + ' ' + price;
  }, [currencySymbol, price]);

  const sellingPriceText = useMemo(() => {
    return currencySymbol + ' ' + sellingPrice;
  }, [currencySymbol, sellingPrice]);

  const fRowStyle = useMemo(() => {
    return [styles.fRow, {marginHorizontal: size !== 'small' ? Sizes.xm : 0}];
  }, [styles.fRow, size]);

  const discountGradientStyle = useMemo(() => {
    return {
      paddingLeft: size === 'small' ? Sizes.xm : Sizes.s,
      flex: 2,
    };
  }, [size]);

  const discountGradientColors = useMemo(() => {
    return [colors.greenRgb0, colors.greenRgb3];
  }, [colors]);

  const onActionBtnPress = useCallback(() => {
    if (actionBtn?.text === 'Request Price') {
      handleCardPress();
    } else {
      cartBtnAction();
    }
  }, [actionBtn?.text, handleCardPress, cartBtnAction]);

  const onNotifyPress = useCallback(async () => {
    if (!isLoggedIn) {
      if (onClose) {
        onClose();
        setTimeout(() => {
          showInfoMessage(t('toastMassages.loginInfo'));
          navigate('Login', {back: true});
        }, 200);
        return;
      }
      showInfoMessage(t('toastMassages.loginInfo'));
      navigate('Login', {back: true});
      return;
    }
    if (!isNotifyPressed) {
      try {
        const response = await dispatch(
          notify(Number(item?.object_id) || item?.product_id),
        );
        if (response) {
          isLoggedIn && setIsNotifyPressed(true);
        }
        if (onClose) {
          onClose();
        }
      } catch (error) {
        debugError(error);
      }
    }
  }, [
    dispatch,
    isLoggedIn,
    isNotifyPressed,
    item?.object_id,
    item?.product_id,
  ]);

  const notifyIcon = useMemo(() => {
    return isLoggedIn && isNotifyPressed ? 'notifyBellIcon' : undefined;
  }, [isLoggedIn, isNotifyPressed]);

  const onUpdateQty = useCallback(
    (count: number) => {
      hideCart ? onSetQty(count) : updateQty(count);
    },
    [hideCart, onSetQty, updateQty],
  );

  const qtyViewStyle = useMemo(() => {
    return [styles.qtyView, hideCart && styles.cartBtnStyle];
  }, [styles.qtyView, hideCart, styles.cartBtnStyle]);

  const qtyInputStyle = useMemo(() => {
    return [styles.qtyInput, hideCart && styles.btnSize];
  }, [styles.qtyInput, styles.btnSize, hideCart]);

  const qtyPlusMinusStyle = useMemo(() => {
    return [styles.qtyPlus, hideCart && styles.btnSize];
  }, [styles.qtyPlus, styles.btnSize, hideCart]);

  const fallbackImageOnPress = useCallback(() => {
    if (imageUrl) {
      navigate('UrlResolver', {urlKey: imageUrl});
    }
    else{
     const fallbackUrl = '/waldent-provac-dental-suction-unit-wl-x01.html'
      resolveUrl({urlKey:fallbackUrl,navigation})
    }
  }, [imageUrl]);

  const fallbackImageOnError = useCallback(() => {
    debugLog('Image failed to load');
  }, []);

  const wishlistButton = useMemo(() => {
    return (
      <WishlistButton
        item = {item}
        productId={item?.product_id || item?.object_id}
        image={item?.media?.mobile_image || item?.thumbnail_url}
        navigation={navigation}
        onClose={onClose}
      />
    );
  }, [item?.product_id, item?.media?.mobile_image, navigation]);

  const handleQuantityUpdate = useCallback(
    (count: number) => {
      if (count === 0) {
        setIsAddToCartPressed(false);
      }
      onUpdateQty(count);
    },
    [onUpdateQty, updateQty],
  );

  const quantityInputRef = useRef<TextInput>(null);

  const btnStyle = useMemo(() => {
    return [styles.btnStyle];
  }, [styles.btnStyle]);

  const btnLabel = useMemo(() => {
    return [styles.btnLabel];
  }, [styles.btnLabel]);

  const cartView = useMemo(() => {
    if (hideCart && qtyRef.current === 0) {
      return (
        <View style={styles.fOne}>
          <Button
            type="bordered"
            onPress={() => onSetQty(1)}
            text={t('buttons.add')}
            radius="sx"
            size="zero-height"
            selfAlign="stretch"
            style={btnStyle}
            labelStyle={btnLabel}
            stopPropagation={true}
          />
        </View>
      );
    }
    return (
      <Quantity
        textInputRef={quantityInputRef}
        min={hideCart ? 0 : minSaleQty}
        max={maxSaleQty}
        value={qtyForUI}
        onUpdate={(count, change) => {
          if (change && !hideCart) {
            debouncedQuantityUpdate(count);
          } else {
            handleQuantityUpdate(count);
          }
        }}
        // onUpdate={hideCart ? handleQuantityUpdate : debouncedQuantityUpdate}
        style={qtyViewStyle}
        inputStyle={qtyInputStyle}
        minusStyle={qtyPlusMinusStyle}
        plusStyle={qtyPlusMinusStyle}
        hideCart={hideCart}
        hideCartBtn={hideCartBtn}
        disabled={isLoading}
        onFocus={onFocus}
      />
    );
  }, [
    hideCart,
    qtyRef.current,
    onSetQty,
    t,
    btnStyle,
    btnLabel,
    quantityInputRef,
    minSaleQty,
    maxSaleQty,
    qtyForUI,
    handleQuantityUpdate,
    debouncedQuantityUpdate,
    qtyViewStyle,
    qtyInputStyle,
    qtyPlusMinusStyle,
    hideCartBtn,
    isLoading,
    onFocus,
  ]);

  return (
    <TouchableOpacity
      disabled={!skuId && !image}
      activeOpacity={1}
      onPress={handleCardPress}
      key={index}
      style={containerStyle}>
      {skuId ? (
        <View style={innerContainerStyle}>
          <View>
            <View style={size === 'small' ? {paddingHorizontal: Sizes.xm} : {}}>
              <View style={size === 'small' ? styles.productImgView : {}}>
                <View style={productImageContainerStyle}>
                  {isBestSeller ? (
                    <ImageIcon icon="bestSeller" style={styles.bestSellerTag} />
                  ) : (
                    <View />
                  )}
                  {showWishlist ? (
                    <View>{wishlistButton}</View>
                  ) : topRightComponent ? (
                    <View>{topRightComponent}</View>
                  ) : null}
                </View>
                <View style={styles.imageContainer}>
                  <View style={!inStock && styles.maskImageView}>
                    <FastImage
                      resizeMode="contain"
                      style={fastImageStyle}
                      source={imageSource}
                    />
                  </View>
                  {!inStock && (
                    <View style={styles.overlayContainer}>
                      <View style={styles.soldOutView}>
                        <Label
                          text={t('PDP.outStock')}
                          size="mx"
                          fontFamily="Medium"
                          color="black"
                        />
                      </View>
                    </View>
                  )}
                </View>
                {matchSku && inStock && (
                  <View style={styles.deliveryOfferRow}>
                    <Tag
                      style={styles.tagView}
                      labelStyle={styles.tagLabelfree}
                      label={t('productCardVertical.enjoyfreegift')}
                      color="offerTitleGreen1"
                    />
                    <Spacer
                      size={size === 'small' ? 's' : 'xm'}
                      type="Horizontal"
                    />
                  </View>
                )}
              </View>
            </View>
            <Spacer size="xm" />
            <View style={styles.rowCentered}>
              {rewardPoint &&
              Number(rewardPoint) > 0 &&
              item?.action_btn?.text !== 'Request Price' ? (
                <View style={styles.rowAlignCenter}>
                  <ImageIcon icon="coin" size="l" />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    text={rewardPoint || '0'}
                    size="mx"
                    style={styles.coinNumber}
                    fontFamily="Medium"
                    color="orange"
                  />
                  <Spacer size="s" type="Horizontal" />
                  {rating !== '0.0' && ratingCount !== '(0)' && (
                    <>
                      <Separator
                        Vertical
                        color="grey2"
                        height="l"
                        style={styles.separatorStyle}
                      />
                      <Spacer size="s" type="Horizontal" />
                    </>
                  )}
                </View>
              ) : (
                <View style={styles.priceSpace} />
              )}
              {rating && rating !== '0.0' && (
                <>
                  <Tag
                    style={styles.rateTagView}
                    labelStyle={styles.rateTagLabel}
                    label={rating}
                    icon="starIcon"
                    iconSize="xm"
                    color="green2"
                  />
                  <Spacer size="s" type="Horizontal" />
                </>
              )}
              {ratingCount && ratingCount !== '(0)' && (
                <Label
                  fontFamily="Regular"
                  size="m"
                  color="grey"
                  style={styles.ratingNumber}
                  text={ratingCount}
                />
              )}
            </View>
            <Spacer size="s" />
            <View style={{paddingHorizontal: Sizes.xm}}>
              <Label
                text={name}
                color="text"
                size={size === 'small' ? 'm' : 'mx'}
                fontFamily="Medium"
                numberOfLines={2}
                ellipsizeMode="tail"
                style={styles.titleStyle}
              />
              {size !== 'small' ? (
                <>
                  <Spacer size="s" />
                  <Label
                    text={description}
                    fontFamily="Regular"
                    size="m"
                    color="grey"
                    ellipsizeMode="tail"
                    numberOfLines={2}
                    style={styles.desStyle}
                  />
                </>
              ) : null}
              <View style={styles.pricingRow}>
                {productType === 'grouped' ? (
                  <Label
                    fontFamily="Medium"
                    size="m"
                    color="text"
                    text={t('productItemsHorizontal.starting')}
                  />
                ) : (
                  !item?.msrp &&
                  item?.action_btn?.text !== 'Request Price' &&
                  price !== sellingPrice && (
                    <Label
                      fontFamily="Medium"
                      size="m"
                      color="grey3"
                      textDecorationLine="line-through"
                      text={originalPriceText}
                    />
                  )
                )}
                <Spacer size="s" type="Horizontal" />
                {!item?.msrp && item?.action_btn?.text !== 'Request Price' ? (
                  <>
                    <Label
                      color="text"
                      fontFamily="SemiBold"
                      size="mx"
                      text={sellingPriceText}
                    />
                  </>
                ) : (
                  <View style={styles.reqView} />
                )}
              </View>
              {!(productType === 'grouped' || !item?.msrp) && (
                <Spacer size="xm" />
              )}
            </View>
            <View style={fRowStyle}>
              {!!discount &&
              !isHide &&
              !msrp &&
              item?.action_btn?.text !== 'Request Price' ? (
                <LinearGradient
                  style={discountGradientStyle}
                  useAngle
                  angle={270}
                  colors={discountGradientColors}>
                  <Label
                    color="green2"
                    fontFamily="Medium"
                    size="mx"
                    align="left"
                    text={discount}
                  />
                </LinearGradient>
              ) : (
                <View style={waldent ? styles.spaceView : styles.spaceView} />
              )}
              <View style={styles.fOne} />
            </View>
          </View>
          {/* {size === 'small' ? <Spacer size="xxl" /> : null} */}
          {size !== 'small' || !hideAddToCart ? (
            <View
              style={styles.simpleView}
              onStartShouldSetResponder={() => {
                return true;
              }}>
              {productType === 'simple' &&
              !addToCartButtonVisibility(demoAvailable, msrp) ? (
                <>
                  {!isAddToCartPressed && (inStock ? !hideCartBtn : true) ? (
                    <>
                      {inStock ? (
                        <Button
                          type="bordered"
                          onPress={onActionBtnPress}
                          text={actionBtn?.text}
                          radius="sx"
                          size="zero-height"
                          selfAlign="stretch"
                          style={styles.btnStyle}
                          labelStyle={styles.btnLabel}
                          stopPropagation={true}
                        />
                      ) : (
                        <Button
                          type={!isNotifyPressed ? 'bordered' : 'pressed'}
                          onPress={onNotifyPress}
                          text={!isNotifyPressed ? t('PDP.notifyMe') : null}
                          iconCenter={notifyIcon}
                          radius="sx"
                          size="zero-height"
                          selfAlign="stretch"
                          style={styles.btnStyle}
                          labelStyle={styles.btnLabel}
                          iconSize="xxl"
                          stopPropagation={true}
                        />
                      )}
                    </>
                  ) : (
                    <View style={[styles.fRow, hideCart && styles.alignSelf]}>
                      <ErrorHandler
                        componentName={`${TAG} Quantity`}
                        onErrorComponent={<View />}>
                        {cartView}
                      </ErrorHandler>
                      {!hideCart && (
                        <>
                          <Spacer size="xs" type="Horizontal" />
                          <View style={styles.fOne}>
                            {isLoading ? (
                              <ActivityIndicator
                                size="small"
                                color={colors.categoryTitle}
                                style={styles.addTocartView}
                              />
                            ) : (
                              <Button
                                iconCenter="addToCart"
                                onPress={handleAddToCart}
                                radius="sx"
                                iconSize="xxl"
                                size="small"
                                type="bordered"
                                selfAlign="stretch"
                                style={styles.addCartBtn}
                                labelStyle={styles.btnLabel}
                                disabled={isLoading}
                                stopPropagation={true}
                              />
                            )}
                          </View>
                        </>
                      )}
                    </View>
                  )}
                </>
              ) : (
                <Button
                  type="bordered"
                  onPress={handleCardPress}
                  text={actionBtn?.text}
                  radius="sx"
                  size="zero-height"
                  selfAlign="stretch"
                  style={styles.btnStyle}
                  labelStyle={styles.btnLabel}
                  stopPropagation={true}
                />
              )}
            </View>
          ) : null}
        </View>
      ) : (
        <>
          {!!image && (
            <>
              <TouchableOpacity
                onPress={fallbackImageOnPress}
                style={styles.cardContainer}>
                <FastImage
                  resizeMode="stretch"
                  style={styles.imageCard}
                  onError={fallbackImageOnError}
                  source={imageSource}
                />
              </TouchableOpacity>
            </>
          )}
        </>
      )}
    </TouchableOpacity>
  );
};

export default React.memo(ProductCardVertical);

import React, {useMemo} from 'react';
import {
  View,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {ImageIcon, Label, Separator, Spacer} from 'components/atoms';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import <PERSON><PERSON>r<PERSON>and<PERSON> from 'utils/ErrorHandler';
import Button from '../button';
import stylesWithOutColor from '../../../scenes/productDetail/style';
import {ProductData} from 'local';

type DDProps = {
  product: ProductData;
  postCode: string;
  setPostCode: React.Dispatch<React.SetStateAction<string>>;
  deliveryStatusData: any;
  setDeliveryStatusData: React.Dispatch<any>;
  checkPincodeLoading: boolean;
  setSelectedAddress: any;
  setReturnInfoModel: React.Dispatch<React.SetStateAction<boolean>>;
  setInfoIcon: React.Dispatch<React.SetStateAction<string>>;
  pinCodeWrong: boolean;
  setPinCodeWrong: (val: boolean) => void;
};

const DeliveryDetails = ({
  product,
  postCode,
  setPostCode,
  deliveryStatusData,
  setDeliveryStatusData,
  checkPincodeLoading,
  setSelectedAddress,
  setReturnInfoModel,
  setInfoIcon,
  pinCodeWrong,
  setPinCodeWrong,
}: DDProps) => {
  // Only render if product is in stock
  if (!product?.inventory?.is_in_stock) {
    return null;
  }
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View style={styles.subProductNameView}>
      <View style={styles.deliveryDetails}>
        <Label
          text={t('PDP.deliveryDetails')}
          fontFamily="Medium"
          color="categoryTitle"
          size="l"
        />
        <Spacer size="xm" />
        <View style={styles.countryButton}>
          <View style={styles.pincodeView}>
            <ImageIcon icon="flagIndia" size="xxl" />
            <Spacer size="sx" type="Horizontal" />
            <Separator height="xxl" Vertical={true} color="placeholderColor" />
            <Spacer size="m" type="Horizontal" />
            <TextInput
              id="txtPdpPincode"
              style={styles.pincodeInput}
              value={postCode}
              onChangeText={t => {
                setPostCode(t);
                setDeliveryStatusData(null);
                setPinCodeWrong(false);
              }}
              maxLength={6}
              keyboardType={Platform.OS === 'ios' ? 'number-pad' : 'numeric'}
              allowFontScaling={false}
              placeholder={t('PDP.enterYourPincode')}
              placeholderTextColor={colors.text2}
            />
          </View>
          {checkPincodeLoading ? (
            <ActivityIndicator size="small" />
          ) : (
            <ErrorHandler
              componentName="DeliveryDetails Button"
              onErrorComponent={<View />}>
              <Button
                ghost
                labelColor={postCode?.trim() === '' ? 'grey' : 'text'}
                weight="500"
                labelSize="l"
                text={t('buttons.check').toUpperCase()}
                onPress={() =>
                  setSelectedAddress({
                    country_id: 'IN',
                    postcode: postCode,
                  })
                }
              />
            </ErrorHandler>
          )}
        </View>
        <Spacer size="xm" />
        {deliveryStatusData?.service_availability?.[0]?.message !==
          undefined && (
          <>
            <View>
              {deliveryStatusData?.service_availability?.some(
                i => i.services.COD === false && i.services.Prepaid === false,
              ) ? (
                <View style={styles.flexRow}>
                  <Label
                    textTransform="capitalize"
                    color="categoryTitle"
                    weight="500"
                    size="m"
                    text={`${deliveryStatusData?.service_availability?.[0]?.errors[0]}`}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      setReturnInfoModel(true);
                      setInfoIcon('wrongPinCode');
                    }}
                    style={styles.imgLeft}>
                    <ImageIcon
                      size="xl"
                      tintColor="categoryTitle"
                      style={styles.imgDeliveryIcon}
                      icon="infoCircle"
                    />
                  </TouchableOpacity>
                </View>
              ) : null}
            </View>
          </>
        )}
        {pinCodeWrong ? (
          <Label
            color="red3"
            weight="500"
            textTransform="capitalize"
            size="m"
            text={t('validations.validPostCode')}
          />
        ) : (
          <View
            style={[
              styles.deliveryStatusView,
              product?.type !== 'grouped' &&
              deliveryStatusData?.delivery_info?.[0]?.delivery_days?.[0]
                ?.message
                ? styles.serviceView
                : styles.startView,
            ]}>
            {postCode.length === 6 &&
              product?.type !== 'grouped' &&
              deliveryStatusData?.delivery_info?.[0]?.delivery_days?.[0]
                ?.message && (
                <>
                  <View style={styles.deliveryInfoContainer}>
                    <ImageIcon size="xxl" icon="deliveryInfoIcon" />
                    <Spacer size="s" type="Horizontal" />
                    <Label
                      color="categoryTitle"
                      weight="500"
                      textTransform="capitalize"
                      size="m"
                      text={`${deliveryStatusData?.delivery_info?.[0]?.delivery_days?.[0]?.message}`}
                    />

                    {deliveryStatusData.service_availability[0].services
                      .Prepaid && (
                      <TouchableOpacity
                        onPress={() => {
                          setReturnInfoModel(true);
                          setInfoIcon('deliveryInfo');
                        }}
                        style={styles.imgLeft}>
                        <ImageIcon
                          size="xl"
                          tintColor="categoryTitle"
                          style={styles.imgDeliveryIcon}
                          icon="infoCircle"
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </>
              )}
            {postCode.length === 6 &&
              product?.type !== 'grouped' &&
              deliveryStatusData?.service_availability?.[0]?.message !==
                undefined && (
                <View style={styles.deliveryInfoContainer}>
                  <ImageIcon
                    size="xxl"
                    icon={
                      deliveryStatusData?.service_availability?.[0]?.services
                        ?.COD
                        ? 'codIcon'
                        : 'nonReturnable'
                    }
                  />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    textTransform="capitalize"
                    color={
                      deliveryStatusData?.service_availability?.[0]?.services
                        ?.COD
                        ? 'categoryTitle'
                        : 'red3'
                    }
                    weight="500"
                    size="m"
                    text={`${deliveryStatusData?.service_availability?.[0]?.message?.replace(
                      /\.$/,
                      '',
                    )}`}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      setReturnInfoModel(true);
                      setInfoIcon('codCheck');
                    }}
                    style={styles.imgLeft}>
                    <ImageIcon
                      size="xl"
                      tintColor={
                        deliveryStatusData?.service_availability?.[0]?.services
                          ?.COD
                          ? 'categoryTitle'
                          : 'red3'
                      }
                      style={styles.imgDeliveryIcon}
                      icon="infoCircle"
                    />
                  </TouchableOpacity>
                </View>
              )}
            {postCode.length === 6 &&
              product?.type !== 'grouped' &&
              deliveryStatusData?.return_info?.[0] !== undefined && (
                <View style={styles.deliveryInfoContainer}>
                  <ImageIcon
                    size="xxl"
                    icon={
                      deliveryStatusData?.return_info?.[0]?.is_returnable
                        ? 'returnable'
                        : 'nonReturnable'
                    }
                  />
                  <Spacer size="s" type="Horizontal" />
                  <Label
                    textTransform="capitalize"
                    color={
                      deliveryStatusData?.return_info?.[0]?.is_returnable
                        ? 'categoryTitle'
                        : 'red3'
                    }
                    weight="500"
                    size="m"
                    text={
                      deliveryStatusData?.return_info?.[0]?.is_returnable
                        ? t('PDP.returnable')
                        : t('PDP.nonReturnable')
                    }
                  />
                </View>
              )}
          </View>
        )}
      </View>
    </View>
  );
};

export default React.memo(DeliveryDetails);

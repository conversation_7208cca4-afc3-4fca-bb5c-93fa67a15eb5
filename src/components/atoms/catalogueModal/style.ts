import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    modalBackground: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.blackTransparent,
    },
    modalContainer: {
      padding: Sizes.xx,
      width: '90%',
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
    },
    imageSubView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    modalView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
    },
    modalUploadView: {
      borderWidth: Sizes.x,
      borderStyle: 'dashed',
      borderColor: colors.borderColor1,
      borderRadius: Sizes.xms,
      paddingTop: Sizes.xl,
      paddingBottom: Sizes.x3l,
      paddingHorizontal: Sizes.x5l,
    },
    pickImage: {
      width: Sizes.exl + Sizes.s,
      height: Sizes.ex90,
      alignSelf: 'center',
    },
    selectedImage: {
      borderWidth: Sizes.x,
      borderColor: colors.borderColor2,
      borderRadius: Sizes.sx,
      padding: Sizes.m,
      marginTop: Sizes.l,
    },
    selectedCatalogue: {
      borderRadius: Sizes.sx,
      marginRight: Sizes.xm,
      width: Sizes.x70,
      height: Sizes.x60,
    },
    catalogueModalImage: {
      width: Sizes.x5l,
      height: Sizes.x5l,
    },
    modalSpace: {
      paddingHorizontal: Sizes.xl,
    },
    columnDirection: {
      flexDirection: 'column',
    },
    rowDirection: {
      flexDirection: 'row',
    },
    lineView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectedImageData: {
      flexDirection: 'column',
      flex: Sizes.x,
    },
    separatorText: {
      paddingHorizontal: Sizes.xm,
    },
    flex: {
      flex: Sizes.x,
    },
    urlView: {
      backgroundColor: colors.background5,
      flexDirection: 'row',
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.sx,
      height: Sizes.x6l,
      alignItems: 'center',
    },
    uploadView: {
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.s,
    },
    btnStyle: {
      fontFamily: Fonts.Regular,
    },
    cancelButton: {
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.sx,
    },
    selectedImageButton: {
      flexDirection: 'row',
      paddingVertical: Sizes.xms,
      alignItems: 'center',
    },
    doneButton: {
      borderRadius: Sizes.sx,
      backgroundColor: colors.categoryTitle,
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.sx,
    },
    urlInput: {
      flex: Sizes.x,
      fontSize: Sizes.m,
      color: colors.text2,
      fontFamily: Fonts.Regular,
    },
    label: {
      textTransform: 'capitalize',
    },
  });

export default styles;

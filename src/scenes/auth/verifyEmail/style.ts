import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xxxl,
    },
    saveButton: {fontSize: Sizes.l, fontWeight: '700'},
    button: {backgroundColor: colors.textError},
    titleView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: Sizes.xl,
    },
    modelContainer: {
      width: '90%',
      alignSelf: 'center',
    },
    modelInputBox: {
      height: Sizes.x6l,
      color: colors.grey,
    },
    modelBtn: {
      height: Sizes.x7l,
      width: '100%',
      marginTop: Sizes.xms,
      alignSelf: 'center',
      borderRadius: Sizes.xms,
      backgroundColor: colors.categoryTitle,
    },
    modelBtnView: {
      paddingVertical: Sizes.xl,
    },
    otpInput: {
      borderColor: colors.border,
      borderRadius: Sizes.s,
      color: colors.text,
      fontSize: Sizes.xl,
      fontWeight: '700',
      width: Sizes.x7l + Sizes.xs,
      height: Sizes.x7l + Sizes.xs,
      backgroundColor: colors.background,
    },
    horizontalSpace: {paddingHorizontal: Sizes.l},
    buttonText: {color: colors.whiteColor},
    otpText: {color: colors.textLight},
    otpInputContainer: {
      width: '100%',
      height: Sizes.x7l + Sizes.xs,
    },
    otpView: {height: Sizes.exl},
    updateEmailLabel: {
      backgroundColor: 'rgba(85, 101, 117, 0.11)',
      padding: Sizes.xms,
      color: colors.categoryTitle,
    },
    subContainer: {
      backgroundColor: colors.background,
    },
    inputView: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      height: Sizes.x6l + Sizes.s,
    },
    inputTextView: {color: colors.text, flex: 1},
    subCautionerBox: {
      width: '100%',
      borderRadius: Sizes.sx,
      marginBottom: Sizes.xms,
      height: Sizes.x6l + Sizes.s,
    },
    mainView: {justifyContent: 'center', flexDirection: 'row'},
  });

export default styles;

{"project_info": {"project_number": "285108782909", "firebase_url": "https://dentalkart-app-test.firebaseio.com", "project_id": "dentalkart-app-test", "storage_bucket": "dentalkart-app-test.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:285108782909:android:7f1bc9cf93ee9152", "android_client_info": {"package_name": "com.vasadental.dentalkart"}}, "oauth_client": [{"client_id": "285108782909-evek13776l2inm2durdbvak187kllpq8.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.vasadental.dentalkart", "certificate_hash": "d99b8daba4a94969a67863800d364038a3761417"}}, {"client_id": "285108782909-ouh6alnd146f1het75vn5lvim26tuvpm.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDG8-9F9FyLVdzmYK06A9V-4kTVOiuUSvY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "285108782909-fo47irfp91r7g3nhg9rj8mmbk84rtvas.apps.googleusercontent.com", "client_type": 3}, {"client_id": "285108782909-6eh1456hkr7neqt621ceout58lftint0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.vasadental.dentalkart"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:285108782909:android:b040a4977ac62199d72754", "android_client_info": {"package_name": "com.vasadental.dentalkart.dev"}}, "oauth_client": [{"client_id": "285108782909-ouh6alnd146f1het75vn5lvim26tuvpm.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDG8-9F9FyLVdzmYK06A9V-4kTVOiuUSvY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "285108782909-fo47irfp91r7g3nhg9rj8mmbk84rtvas.apps.googleusercontent.com", "client_type": 3}, {"client_id": "285108782909-6eh1456hkr7neqt621ceout58lftint0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.vasadental.dentalkart"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:285108782909:android:e338e5d3274f3abad72754", "android_client_info": {"package_name": "com.vasadental.dentalkart.staging"}}, "oauth_client": [{"client_id": "285108782909-ouh6alnd146f1het75vn5lvim26tuvpm.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDG8-9F9FyLVdzmYK06A9V-4kTVOiuUSvY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "285108782909-fo47irfp91r7g3nhg9rj8mmbk84rtvas.apps.googleusercontent.com", "client_type": 3}, {"client_id": "285108782909-6eh1456hkr7neqt621ceout58lftint0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.vasadental.dentalkart"}}]}}}], "configuration_version": "1"}
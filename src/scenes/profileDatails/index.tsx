import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {
  ScrollView,
  TouchableOpacity,
  View,
  BackHandler,
  Keyboard,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {<PERSON><PERSON>, <PERSON>er, TextInputBox} from 'components/molecules';
import {
  DropDown,
  ImageIcon,
  Label,
  Separator,
  Spacer,
  CircularProgress,
} from 'components/atoms';
import {RootStackParamsList} from 'routes';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {showSuccessMessage} from 'utils/show_messages';
import {RouteProp, useIsFocused, useTheme} from '@react-navigation/native';
import DatePicker from 'react-native-date-picker';
import {useDispatch, useSelector} from 'react-redux';
import {getUserInfo, setLoading} from 'app-redux-store/slice/appSlice';
import {getMe, updateMe} from 'services/account';
import {
  checkDevice,
  stringReg,
  emailRegExp,
  phoneReg,
  nameSplit,
  fullNameReg,
} from 'utils/utils';
import {AnalyticsEvents} from 'components/organisms';
import ErrorHandler from 'utils/ErrorHandler';
import Icons from 'common/icons';
import {speciality} from 'staticData';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ProfileDetails'>;
};

const ProfileDetailsScene = ({navigation, route}: Props) => {
  const TAG = 'ProfileDetailsScreen';
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const isFocused = useIsFocused();
  const [customerData, setCustomerData] = useState<Customer | {}>({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const dispatch = useDispatch();
  const [selectedField, setSelectedField] = useState<
    'dob' | 'anniversary' | null
  >(null);
  const [errors, setErrors] = useState({});
  const [values, setValues] = useState({
    firstname: '',
    // lastname: '',
    type: '',
    speciality: '',
    businessname: '',
    gender: '',
    anniversary: '',
    dob: '',
    email: '',
    mobile: '',
    is_subscribed: false,
  });

  useEffect(() => {
    const defaultValue = (current, source) =>
      current ? current : source || '';

    setValues({
      firstname: defaultValue(
        values?.firstname,
        [userInfo?.firstname || '', userInfo?.lastname || '']
          .filter(Boolean)
          .join(' '),
      ),
      type: defaultValue(values?.type, userInfo?.type),
      speciality: defaultValue(values?.speciality, userInfo?.speciality),
      businessname: defaultValue(values?.businessname, userInfo?.business_name),
      gender: defaultValue(values?.gender, userInfo?.gender),
      anniversary:
        values?.anniversary ||
        (userInfo?.anniversary ? new Date(userInfo.anniversary) : ''),
      dob: values?.dob || (userInfo?.dob ? new Date(userInfo.dob) : ''),
      email: defaultValue(userInfo?.email, values?.email),
      mobile: defaultValue(values?.mobile, userInfo?.mobile),
      is_subscribed: defaultValue(
        values?.is_subscribed,
        customerData?.is_subscribed,
        false,
      ),
    });
  }, [userInfo]);

  useEffect(() => {
    AnalyticsEvents('ACCOUNT', 'Account screen', {}, userInfo, isLoggedIn);

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const getCustomerData = useCallback(async () => {
    const data = await getMe();
    setCustomerData(JSON.stringify(data));
  }, []);

  const validate = () => {
    const error = {};
    let errorStatus = true;
    const validations = [
      {
        field: 'firstname',
        rule: stringReg,
        message: t('validations.fullNameReq'),
        matchMessage: t('validations.fullNameCorrect'),
      },
      // {
      //   field: 'lastname',
      //   rule: /^[a-zA-Z ]*$/,
      //   message: t('validations.lastNameReq'),
      //   matchMessage: t('validations.lastNameCorrect'),
      // },
      {field: 'type', message: t('validations.typeReq')},
      {field: 'businessname', message: '', required: false},
      {
        field: 'speciality',
        message: t('validations.secialityReq'),
        required: values?.type === 'Dentist',
      },
      {field: 'gender', message: t('validations.genderReq')},
      {field: 'anniversary', message: t('validations.anniversaryReq')},
      {field: 'dob', message: t('validations.dobReq')},
      {
        field: 'mobile',
        rule: phoneReg,
        message: t('validations.mobileRequired'),
        matchMessage: t('validations.validPhone'),
      },
      {
        field: 'email',
        rule: emailRegExp,
        message: t('validations.emailReq'),
        matchMessage: t('validations.emailValid'),
      },
    ];
    for (const {
      field,
      rule,
      message,
      matchMessage,
      required = true,
    } of validations) {
      const value =
        field.split('.').reduce((obj, key) => obj?.[key], values) || '';

      if (required && !value) {
        error[field] = message;
        errorStatus = false;
      } else if (required && rule && !rule.test(value)) {
        error[field] = matchMessage;
        errorStatus = false;
      }
    }
    setErrors(error);
    return errorStatus;
  };

  const isValid = useMemo(() => {
    const value =
      values?.firstname &&
      values?.lastname &&
      values?.type &&
      (values?.type !== 'Dentist'
        ? true
        : values?.type === 'Dentist' && values?.speciality
        ? true
        : false) &&
      values?.gender &&
      values?.dob &&
      values?.anniversary &&
      values?.email &&
      values?.mobile
        ? true
        : false;
    return value;
  }, [values]);

  const userProfileDetails = async () => {
    let checkValid = await validate();
    if (checkValid) {
      const {fname, lname} = nameSplit(values?.firstname?.trim());
      let obj = {
        firstname: fname || '',
        lastname: lname || '',
        type: values.type,
        speciality: values.speciality,
        business_name: values.businessname,
        gender: values.gender,
        dob: formatDate(values.dob),
        anniversary: formatDate(values.anniversary),
        email: values.email,
        mobile: values.mobile,
      };
      dispatch(setLoading(true));
      const {data, status} = await updateMe(obj);
      dispatch(setLoading(false));
      if (status) {
        dispatch(getUserInfo(userInfo));
        AnalyticsEvents(
          'USER_UPDATE',
          'User Update',
          obj,
          userInfo,
          isLoggedIn,
        );
        showSuccessMessage(t('profileDetails.updatedProfileSuccessfully'));
        getCustomerData();
        dispatch(setLoading(false));
        setTimeout(() => {
          onBack();
        }, 1000);
      }
    }
  };

  useEffect(() => {
    getMe();
    getCustomerData();
  }, [getCustomerData, isFocused]);

  const handleShowDatePicker = (field: 'dob' | 'anniversary') => {
    setSelectedField(field);
    setShowDatePicker(true);
    Keyboard.dismiss();
  };
  const handleHideDatePicker = () => {
    setShowDatePicker(false);
  };

  const formatDate = date => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = `0${d.getMonth() + 1}`.slice(-2);
    const day = `0${d.getDate()}`.slice(-2);
    return `${year}-${month}-${day}`;
  };

  const Types = [
    {label: 'Seller', value: 'Seller'},
    {label: 'Dentist', value: 'Dentist'},
    {label: 'Business', value: 'Business'},
    {label: 'Other', value: 'Other'},
  ];
  const Gender = [
    {label: 'Male', value: 'Male', icon: 'maleIcon'},
    {label: 'Female', value: 'Female', icon: 'femaleIcon'},
    {label: 'Other', value: 'Other', icon: 'transgenderIcon'},
  ];

  const renderItem = (item, selected) => {
    return (
      <View style={[styles.dropDownItem, selected && styles.itemSelected]}>
        {item?.icon && (
          <>
            <ImageIcon
              icon={item?.icon}
              size="xl"
              tintColor={selected ? 'whiteColor' : 'black'}
            />
            <Spacer size="xm" type="Horizontal" />
          </>
        )}
        <Label
          color={selected ? 'whiteColor' : 'text2'}
          text={item.label}
          size="mx"
          fontFamily="Medium"
          weight="500"
        />
      </View>
    );
  };

  const onChangeText = (text, key) => {
    const validationRules = {
      firstname: {
        pattern: stringReg,
        required: t('validations.fullNameReq'),
        invalid: t('validations.fullNameCorrect'),
      },
      // lastname: {
      //   pattern: /^[a-zA-Z ]*$/,
      //   required: t('validations.lastNameReq'),
      //   invalid: t('validations.lastNameCorrect'),
      // },
      type: {required: t('validations.typeReq')},
      speciality: {
        required:
          values?.type === 'Dentist' ? t('validations.secialityReq') : null,
      },
      businessname: {required: null},
      gender: {required: t('validations.genderReq')},
      anniversary: {required: t('validations.anniversaryReq')},
      dob: {required: t('validations.dobReq')},
      mobile: {
        pattern: phoneReg,
        required: t('validations.mobileRequired'),
        invalid: t('validations.validPhone'),
      },
      email: {
        pattern: emailRegExp,
        required: t('validations.emailReq'),
        invalid: t('validations.emailValid'),
      },
    };

    const error = {...errors};
    const rule = validationRules[key];
    setValues(prev => ({...prev, [key]: text}));
    if (!rule) {
      return;
    }
    if (!text && rule.required) {
      error[key] = rule.required;
    } else if (rule.invalid && rule.pattern && !text?.match(rule.pattern)) {
      error[key] = rule.invalid || '';
    } else {
      error[key] = '';
    }
    setErrors(error);
  };

  const onBack = () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
  };
  const onErrorView = (error: string) => {
    return (
      <>
        <Spacer size="sx" />
        <Label
          text={String(t(error) || '')}
          size="m"
          weight="400"
          color="red3"
        />
      </>
    );
  };

  const renderLabel = text => {
    return (
      <View style={styles.placeStyle}>
        <Label color="grey" text={text} size="m" weight="500" />
        <Label color="red3" text=" *" size="m" weight="500" />
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton
          navigation={navigation}
          text={t('profile.title')}
          searchIcon={true}
          bagIcon={true}
          style={styles.headerView}
          onPressNavigation={() => onBack()}
        />
      </ErrorHandler>
      <View style={styles.flexOne}>
        <KeyboardAwareScrollView
          contentContainerStyle={styles.scrollStyle}
          showsVerticalScrollIndicator={false}
          enableOnAndroid={true}
          bounces={false}
          keyboardShouldPersistTaps="always">
          <ScrollView
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="always">
            <View style={styles.horizontalSpace}>
              {/* <View style={styles.profileSection}>
                <View style={styles.profileView}>
                  <ImageIcon
                    style={styles.profile}
                    tintColor="blueLine"
                    icon="userProfile"
                  />
                  <View style={styles.cameraView}>
                    <ImageIcon
                      style={styles.cameraIcon}
                      tintColor="text"
                      icon="camera"
                    />
                  </View>
                </View>
                <Label
                  color="text2"
                  text={`${userInfo?.firstname ? userInfo?.firstname : ''} ${
                    userInfo?.lastname ? userInfo?.lastname : ''
                  }`}
                  size="l"
                  fontFamily="SemiBold"
                />
                <Label
                  color="text2"
                  text={t('profile.des')}
                  size="m"
                  fontFamily="SemiBold"
                />
                <CircularProgress percentage={60} />
              </View> */}
              <View style={checkDevice() && styles.labelView}>
                <View style={styles.flexOne}>
                  <ErrorHandler
                    componentName={`${TAG} PhoneInputText`}
                    onErrorComponent={<View />}>
                    <TextInputBox
                      testID="txtProfileDetailsFullName"
                      label={t('placeHolder.fullName')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'firstname')
                      }
                      value={values?.firstname}
                      error={!!errors?.firstname}
                      errorText={errors?.firstname}
                      returnKeyType="next"
                      leftIcon={Icons.user}
                      leftIconsStyle={styles.leftIcon}
                    />
                  </ErrorHandler>
                </View>
                {/* <Spacer
                  size="xm"
                  type={checkDevice() ? 'Horizontal' : 'Vertical'}
                />
                <View style={styles.flexOne}>
                  <ErrorHandler
                    componentName={`${TAG} PhoneInputText`}
                    onErrorComponent={<View />}>
                    <TextInputBox
                      testID="txtProfileDetailsLastName"
                      label={t('placeHolder.fullName')}
                      fieldMandatory={true}
                      onChangeText={text =>
                        onChangeText(text.trimStart(), 'lastname')
                      }
                      value={values?.lastname}
                      error={!!errors?.lastname}
                      errorText={errors?.lastname}
                      returnKeyType="next"
                      leftIcon={Icons.user}
                      leftIconsStyle={styles.leftIcon}
                    />
                  </ErrorHandler>
                </View> */}
              </View>
              <Spacer size="xm" />
              <View style={checkDevice() && styles.labelView}>
                <View style={styles.flexOne}>
                  <View>
                    <Spacer size="s" />
                    <ErrorHandler
                      componentName={`${TAG} DropDown`}
                      onErrorComponent={<View />}>
                      <DropDown
                        testID="profileType"
                        heading={t('profile.dentistPlace')}
                        placeholder={renderLabel(t('profile.dentistPlace'))}
                        fieldMandatory={true}
                        listContainerStyle={styles.dropDownItemView}
                        selectedTextStyle={styles.inputTextView}
                        styleDropDown={[
                          styles.styleDropDown,
                          errors?.type && styles.errorBorder,
                        ]}
                        itemTextStyle={styles.dropDownTextStyle}
                        placeholderStyle={styles.placeTextStyle}
                        labelStyle={styles.labelStyle}
                        hideLabel={true}
                        search={false}
                        value={values.type}
                        onChange={item => {
                          onChangeText(item.value, 'type');
                          setValues(prev => ({
                            ...prev,
                            speciality:
                              item.value === 'Dentist'
                                ? userInfo?.speciality || ''
                                : '',
                          }));
                        }}
                        returnValueOnly={false}
                        data={Types}
                        renderItem={renderItem}
                        labelField="label"
                        valueField="value"
                        error={errors.type ? t(errors.type) : null}
                        renderLeftIcon={() => (
                          <ImageIcon
                            style={styles.icon}
                            tintColor="text"
                            icon="doctorIcon"
                            size="xl"
                          />
                        )}
                        renderRightIcon={(focus: boolean) => (
                          <ImageIcon
                            size="mx"
                            tintColor="text"
                            icon="down"
                            resizeMode="contain"
                            style={focus && styles.downImg}
                          />
                        )}
                      />
                    </ErrorHandler>
                  </View>
                  {errors?.type && onErrorView(errors?.type || '')}
                </View>
                <Spacer
                  size="xm"
                  type={checkDevice() ? 'Horizontal' : 'Vertical'}
                />
                <View style={styles.flexOne}>
                  <View>
                    <Spacer size="s" />
                    <ErrorHandler
                      componentName={`${TAG} DropDown`}
                      onErrorComponent={<View />}>
                      <DropDown
                        testID="profileSpeciality"
                        heading={t('profile.specialityPlace')}
                        placeholder={
                          values?.type === 'Dentist'
                            ? renderLabel(t('profile.specialityPlace'))
                            : t('profile.specialityPlace')
                        }
                        fieldMandatory={
                          values?.type === 'Dentist' ? true : false
                        }
                        listContainerStyle={styles.dropDownItemView}
                        selectedTextStyle={[
                          styles.inputTextView,
                          values?.type !== 'Dentist' && styles.disableColor,
                        ]}
                        styleDropDown={[
                          styles.styleDropDown,
                          errors?.speciality && styles.errorBorder,
                        ]}
                        itemTextStyle={styles.dropDownTextStyle}
                        placeholderStyle={[
                          styles.placeTextStyle,
                          values?.type !== 'Dentist' && styles.disableColor,
                        ]}
                        labelStyle={styles.labelStyle}
                        itemTextStyle={styles.dropDownTextStyle}
                        hideLabel={true}
                        value={values.speciality}
                        onChange={item =>
                          onChangeText(item.value, 'speciality')
                        }
                        renderItem={renderItem}
                        returnValueOnly={false}
                        disable={values?.type === 'Dentist' ? false : true}
                        data={speciality}
                        search={false}
                        labelField="label"
                        valueField="value"
                        text={String(errors?.speciality || '')}
                        renderRightIcon={(focus: boolean) =>
                          values?.type === 'Dentist' ? (
                            <ImageIcon
                              size="mx"
                              tintColor="text"
                              icon="down"
                              resizeMode="contain"
                              style={focus && styles.downImg}
                            />
                          ) : (
                            <View />
                          )
                        }
                      />
                    </ErrorHandler>
                    {errors?.speciality &&
                      onErrorView(errors?.speciality || '')}
                  </View>
                </View>
              </View>
              <Spacer size="xm" type="Vertical" />
              {checkDevice() ? (
                <View style={styles.labelView}>
                  <View style={styles.flexOne}>
                    <Spacer size="s" />
                    <ErrorHandler
                      componentName={`${TAG} DropDown`}
                      onErrorComponent={<View />}>
                      <DropDown
                        testID="profileGender"
                        heading={t('profile.gender')}
                        placeholder={renderLabel(t('profile.gender'))}
                        fieldMandatory={true}
                        listContainerStyle={styles.dropDownItemView}
                        selectedTextStyle={styles.inputTextView}
                        styleDropDown={[
                          styles.styleDropDown,
                          errors?.gender && styles.errorBorder,
                        ]}
                        itemTextStyle={styles.dropDownTextStyle}
                        placeholderStyle={styles.placeTextStyle}
                        labelStyle={styles.labelStyle}
                        hideLabel={true}
                        search={false}
                        value={values.gender}
                        onChange={item => onChangeText(item.value, 'gender')}
                        returnValueOnly={false}
                        isSpacerSize={true}
                        data={Gender}
                        labelField="label"
                        valueField="value"
                        renderItem={renderItem}
                        renderRightIcon={(focus: boolean) => (
                          <ImageIcon
                            size="mx"
                            tintColor="text"
                            icon="down"
                            resizeMode="contain"
                            style={focus && styles.downImg}
                          />
                        )}
                        renderLeftIcon={() =>
                          Gender.find(i => i?.value === values?.gender)
                            ?.icon ? (
                            <ImageIcon
                              style={styles.icon}
                              tintColor="black"
                              icon={
                                Gender.find(i => i?.value === values?.gender)
                                  ?.icon
                              }
                              size="xl"
                            />
                          ) : (
                            <ImageIcon
                              style={styles.icon}
                              tintColor="text"
                              icon="doctorIcon"
                              size="xl"
                            />
                          )
                        }
                      />
                    </ErrorHandler>
                    {errors?.gender && onErrorView(errors?.gender || '')}
                  </View>
                  <Spacer
                    size="xm"
                    type={checkDevice() ? 'Horizontal' : 'Vertical'}
                  />
                  <View style={styles.flexOne}>
                    <ErrorHandler
                      componentName={`${TAG} PhoneInputText`}
                      onErrorComponent={<View />}>
                      <TextInputBox
                        testID="txtProfileDetailsBusinessName"
                        label={t('profile.bOptional')}
                        onChangeText={text =>
                          onChangeText(text.trimStart(), 'businessname')
                        }
                        value={values?.businessname}
                        returnKeyType="next"
                        leftIcon={Icons.bag}
                        leftIconsStyle={styles.leftIcon}
                      />
                    </ErrorHandler>
                  </View>
                </View>
              ) : (
                <ErrorHandler
                  componentName={`${TAG} PhoneInputText`}
                  onErrorComponent={<View />}>
                  <TextInputBox
                    testID="txtProfileDetailsBusinessName"
                    label={t('profile.bOptional')}
                    onChangeText={text =>
                      onChangeText(text.trimStart(), 'businessname')
                    }
                    value={values?.businessname}
                    returnKeyType="next"
                    leftIcon={Icons.bag}
                    leftIconsStyle={styles.leftIcon}
                  />
                </ErrorHandler>
              )}
              <Spacer
                size="xm"
                type={checkDevice() ? 'Horizontal' : 'Vertical'}
              />
              {!checkDevice() && (
                <View>
                  <Spacer size="s" />
                  <ErrorHandler
                    componentName={`${TAG} DropDown`}
                    onErrorComponent={<View />}>
                    <DropDown
                      testID="profileGender"
                      heading={t('profile.gender')}
                      placeholder={renderLabel(t('profile.gender'))}
                      fieldMandatory={true}
                      listContainerStyle={styles.dropDownItemView}
                      selectedTextStyle={styles.inputTextView}
                      styleDropDown={[
                        styles.styleDropDown,
                        errors?.gender && styles.errorBorder,
                      ]}
                      itemTextStyle={styles.dropDownTextStyle}
                      placeholderStyle={styles.placeTextStyle}
                      labelStyle={styles.labelStyle}
                      hideLabel={true}
                      search={false}
                      value={values.gender}
                      onChange={item => onChangeText(item.value, 'gender')}
                      returnValueOnly={false}
                      data={Gender}
                      labelField="label"
                      valueField="value"
                      renderItem={renderItem}
                      renderRightIcon={(focus: boolean) => (
                        <ImageIcon
                          size="mx"
                          tintColor="text"
                          icon="down"
                          resizeMode="contain"
                          style={focus && styles.downImg}
                        />
                      )}
                      renderLeftIcon={() =>
                        Gender.find(i => i?.value === values?.gender)?.icon ? (
                          <ImageIcon
                            style={styles.icon}
                            tintColor="black"
                            icon={
                              Gender.find(i => i?.value === values?.gender)
                                ?.icon
                            }
                            size="xl"
                          />
                        ) : (
                          <ImageIcon
                            style={styles.icon}
                            tintColor="text"
                            icon="doctorIcon"
                            size="xl"
                          />
                        )
                      }
                    />
                  </ErrorHandler>
                  {errors?.gender && onErrorView(errors?.gender || '')}
                </View>
              )}
              <Spacer size="xm" />
              <View style={checkDevice() && styles.labelView}>
                <View style={styles.flexOne}>
                  <View>
                    {/* <Spacer size="s" /> */}
                    <TouchableOpacity
                      onPress={() => handleShowDatePicker('anniversary')}>
                      <ErrorHandler
                        componentName={`${TAG} PhoneInputText`}
                        onErrorComponent={<View />}>
                        <TextInputBox
                          testID="txtProfileDetailsAnniversaryDate"
                          label={t('profile.anniversary')}
                          fieldMandatory={true}
                          onChangeText={text =>
                            onChangeText(text.trimStart(), 'firstname')
                          }
                          value={
                            values.anniversary
                              ? formatDate(values.anniversary)
                              : ''
                          }
                          pointerEvents="none"
                          error={!!errors?.anniversary}
                          errorText={errors?.anniversary}
                          returnKeyType="next"
                          leftIcon={Icons.birthdayCalendar}
                          leftIconsStyle={styles.leftIcon}
                          rightIcon={Icons.editPencil}
                          rightIconsStyle={styles.rightIcon}
                          leftPress={() => handleShowDatePicker('anniversary')}
                          rightPress={() => handleShowDatePicker('anniversary')}
                          editable={false}
                        />
                      </ErrorHandler>
                    </TouchableOpacity>
                    {showDatePicker && selectedField === 'anniversary' && (
                      <ErrorHandler
                        componentName={`${TAG} DatePicker`}
                        onErrorComponent={<View />}>
                        <DatePicker
                          modal
                          open={showDatePicker}
                          date={values.anniversary || new Date()}
                          onConfirm={(dateObj: Date) => {
                            onChangeText(dateObj, 'anniversary');
                            setShowDatePicker(false);
                          }}
                          onCancel={() => handleHideDatePicker()}
                          maximumDate={new Date()}
                          mode={'date'}
                        />
                      </ErrorHandler>
                    )}
                  </View>
                </View>
                <Spacer
                  size="xm"
                  type={checkDevice() ? 'Horizontal' : 'Vertical'}
                />
                <View style={styles.flexOne}>
                  <View>
                    {/* <Spacer size="s" /> */}
                    <TouchableOpacity
                      onPress={() => handleShowDatePicker('dob')}>
                      <ErrorHandler
                        componentName={`${TAG} PhoneInputText`}
                        onErrorComponent={<View />}>
                        <TextInputBox
                          testID="txtProfileDetailsBirthDate"
                          label={t('profile.dob')}
                          fieldMandatory={true}
                          onChangeText={text =>
                            onChangeText(text.trimStart(), 'firstname')
                          }
                          value={values.dob ? formatDate(values.dob) : ''}
                          pointerEvents="none"
                          error={!!errors?.dob}
                          errorText={errors?.dob}
                          returnKeyType="next"
                          leftIcon={Icons.birthdayBold}
                          leftIconsStyle={styles.leftIcon}
                          rightIcon={Icons.editPencil}
                          rightIconsStyle={styles.rightIcon}
                          Press={() => handleShowDatePicker('dob')}
                          rightPress={() => handleShowDatePicker('dob')}
                          editable={false}
                        />
                      </ErrorHandler>
                    </TouchableOpacity>
                    {showDatePicker && selectedField === 'dob' && (
                      <ErrorHandler
                        componentName={`${TAG} DatePicker`}
                        onErrorComponent={<View />}>
                        <DatePicker
                          modal
                          open={showDatePicker}
                          date={values.dob || new Date()}
                          onConfirm={(dateObj: Date) => {
                            onChangeText(dateObj, 'dob');
                            setShowDatePicker(false);
                          }}
                          onCancel={() => handleHideDatePicker()}
                          maximumDate={new Date()}
                          mode={'date'}
                        />
                      </ErrorHandler>
                    )}
                  </View>
                </View>
              </View>
              <Spacer size="xm" />
              <View style={checkDevice() && styles.labelView}>
                <View style={styles.flexOne}>
                  <View>
                    {/* <Spacer size="s" /> */}
                    <View>
                      <TouchableOpacity
                        onPress={() =>
                          navigation.navigate('ChangeEmailOrMobile', {
                            isEditType: 'email',
                          })
                        }>
                        <View pointerEvents="none">
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <TextInputBox
                              testID="txtProfileDetailsEmail"
                              label={t('placeHolder.emailId')}
                              fieldMandatory={true}
                              onChangeText={text =>
                                onChangeText(text.trimStart(), 'email')
                              }
                              value={values?.email}
                              error={!!errors?.email}
                              errorText={errors?.email}
                              returnKeyType="next"
                              leftIcon={Icons.email}
                              leftIconsStyle={styles.leftIcon}
                              editable={false}
                              pointerEvents="none"
                            />
                          </ErrorHandler>
                        </View>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() =>
                          navigation.navigate('ChangePassword', {
                            isEditType: 'email',
                          })
                        }>
                        <Label
                          color="newSunnyOrange"
                          style={styles.changePassword}
                          text={t(
                            userInfo?.is_cred_exist
                              ? 'profile.changePassword'
                              : 'profile.createPassword',
                          )}
                          size="m"
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
                <Spacer
                  size="xm"
                  type={checkDevice() ? 'Horizontal' : 'Vertical'}
                />
                <View style={styles.flexOne}>
                  <View>
                    <TouchableOpacity
                      activeOpacity={userInfo?.mobile ? 1 : 0.7}
                      onPress={() =>
                        userInfo?.mobile
                          ? {}
                          : navigation.navigate('ChangeEmailOrMobile', {
                              isEditType: 'mobile',
                            })
                      }>
                      <View pointerEvents="none">
                        <ErrorHandler
                          componentName={`${TAG} PhoneInputText`}
                          onErrorComponent={<View />}>
                          <TextInputBox
                            testID="txtProfileDetailsEmail"
                            label={t('sellOnDentalkart.mobileNumber')}
                            fieldMandatory={true}
                            onChangeText={text =>
                              onChangeText(text.trimStart(), 'email')
                            }
                            value={
                              userInfo?.mobile
                                ? userInfo?.mobile
                                : t('profile.enterPhone')
                            }
                            error={!!errors?.mobile}
                            errorText={errors?.mobile}
                            returnKeyType="next"
                            leftIcon={Icons.newphone}
                            leftIconsStyle={styles.leftIcon}
                            editable={false}
                            pointerEvents="none"
                          />
                        </ErrorHandler>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              <Spacer size="xm" />
              {checkDevice() && (
                <ErrorHandler
                  componentName={`${TAG} Button`}
                  onErrorComponent={<View />}>
                  <Button
                    style={[styles.button, styles.tabSaveBtn]}
                    text={t('profileDetails.save')}
                    onPress={() => userProfileDetails()}
                    labelStyle={styles.saveButton}
                    labelSize="l"
                    size="l"
                    type="secondary"
                  />
                </ErrorHandler>
              )}
            </View>
          </ScrollView>
          {!checkDevice() && (
            <View style={styles.buttonView}>
              <ErrorHandler
                componentName={`${TAG} Button`}
                onErrorComponent={<View />}>
                <Button
                  style={styles.button}
                  text={t('profileDetails.save')}
                  onPress={() => userProfileDetails()}
                  labelStyle={styles.saveButton}
                  labelSize="l"
                  size="l"
                  type="secondary"
                />
              </ErrorHandler>
            </View>
          )}
        </KeyboardAwareScrollView>
      </View>
    </SafeAreaView>
  );
};

export default ProfileDetailsScene;

import React, {use<PERSON><PERSON>back, useEffect, useState} from 'react';
import {
  SafeAreaView,
  TouchableOpacity,
  View,
  FlatList,
  Linking,
  BackHandler,
  Pressable,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {RouteProp, useTheme} from '@react-navigation/native';
import {
  ImageIcon,
  Label,
  ListView,
  Spacer,
  OfferLineAnimation,
} from 'components/atoms';
import {RootStackParamsList} from 'routes';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import LinearGradient from 'react-native-linear-gradient';
import DropDownList from 'components/molecules/drop-down-list';
import {useSelector} from 'react-redux';
import {helpFaq} from 'staticData';
import {openWhatsApp} from 'utils/utils';
import {phoneNumber} from 'config/environment';
import ErrorHandler from 'utils/ErrorHandler';
import {memberShipsFaqApi} from 'services/mambership';
import {CardListLoader} from 'skeletonLoader';
import {useMemo} from 'react';
import {FaqCategory, RootState} from '@types/local';
import { AnalyticsEvents } from 'components/organisms';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'HelpCenter'>;
};

const HelpCenterScene = ({navigation, route}: Props) => {
  const TAG = 'HelpCenterScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {whatsAppLink} = useSelector((state: RootState) => state.app);
  const [currentIndex, setCurrentIndex] = useState();
  const [loading, setLoading] = useState(false);
  const [faqQuestions, setFaqQuestions] = useState([]);
  const insets = useSafeAreaInsets();
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  useEffect(() => {
    if (route.params?.login) {
      navigation.navigate('HelpOrder', {item: 'Login & My Account'});
    }
    helpFaqData();
    AnalyticsEvents('HELP_CENTER', 'Help center', {}, userInfo, isLoggedIn);

  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const helpFaqData = async () => {
    try {
      setLoading(true);
      const response = await memberShipsFaqApi({
        item_ids: [1, 4, 9, 7, 3, 46, 5],
      });
      const data = response?.data?.result;

      if (data) {
        setFaqQuestions(data);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const talkToUs = () => {
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const renderItem = ({item, index}: {item: FaqCategory; index: number}) => {
    return (
      <>
        <TouchableOpacity
          key={index}
          style={styles.navigationOption}
          onPress={() => {
            navigation.navigate('HelpOrder', {item: item?.name});
          }}>
          <View style={styles.iconView}>
            <Spacer size="sx" />
            <ImageIcon icon={item.icon} style={styles.giftImage} />
          </View>
          <Spacer type="Horizontal" size="sx" />
          <View style={styles.navigationTextMainView}>
            <Label
              color="categoryTitle"
              text={item?.name}
              textTransform="capitalize"
              fontFamily="Medium"
              size="l"
            />
            <Label color="grey" text={item?.des} fontFamily="Medium" size="m" />
          </View>
          <Spacer type="Horizontal" size="sx" />
          <View>
            <ImageIcon icon="arrowRight" size="xxl" tintColor="categoryTitle" />
          </View>
        </TouchableOpacity>
      </>
    );
  };

  return (
    <SafeAreaView style={[styles.flex, { paddingTop: insets.top }]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          navigation={navigation}
          backButton
          bagIcon
          searchIcon
          text={t('profile.helpCenter')}
          onPressNavigation={() => {
            navigation.goBack();
            if (route.params?.goBack) {
              route.params?.goBack();
            }
          }}
        />
      </ErrorHandler>
      {/* <OfferLineAnimation
        text1={t('helpOrderOptions.offerText')}
        // text1={t('helpOrderOptions.offerText1')}
      /> */}
      {loading ? (
        <CardListLoader />
      ) : (
        <ErrorHandler
          componentName={`${TAG} HelpCenterList`}
          onErrorComponent={<View />}>
          <FlatList
            data={['']}
            style={styles.container}
            showsVerticalScrollIndicator={false}
            renderItem={() => (
              <>
                <Spacer size="xms" />
                <Pressable
                  onPress={() => navigation.navigate('MembershipPage')}>
                  <FastImage
                    style={styles.imageCoupon}
                    source={Icons.orderBanner}
                    resizeMode="cover"
                  />
                </Pressable>
                <Spacer size="xms" />
                <View style={styles.contactView}>
                  <Label
                    text={t('helpOrderOptions.callTitle')}
                    size="l"
                    fontFamily="Medium"
                    align="center"
                    color="text2"
                  />
                  <Spacer size="m" />
                  <View style={styles.contactOptions}>
                    <TouchableOpacity
                      onPress={() => openWhatsApp(whatsAppLink?.app_link)}
                      style={styles.chatWithUs}>
                      <View style={styles.chatWithUsImageView}>
                        <ImageIcon size="xxl" icon="chatIcon" />
                      </View>
                      <Spacer type="Horizontal" size="sx" />
                      <Label
                        fontFamily="Medium"
                        size="mx"
                        text={t('helpOrderOptions.chatUs')}
                        color="text"
                      />
                    </TouchableOpacity>
                    <Spacer type="Horizontal" size="xms" />
                    <TouchableOpacity
                      onPress={talkToUs}
                      style={styles.talkToUs}>
                      <View style={styles.talkToUsImageView}>
                        <ImageIcon size="xxl" icon="callIcon" />
                      </View>
                      <Spacer type="Horizontal" size="sx" />
                      <Label
                        fontFamily="Medium"
                        size="mx"
                        text={t('helpOrderOptions.talkUs')}
                        color="text"
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={styles.feqMainView}>
                  <View>
                    <Label
                      text={t('helpOrderOptions.fvq')}
                      size="l"
                      textTransform="capitalize"
                      color="text2"
                      fontFamily="Medium"
                    />
                  </View>
                  <Spacer size="m" />
                  <View>
                    <ListView
                      ItemSeparatorComponent={() => (
                        <Spacer type="Vertical" size="xm" />
                      )}
                      keyExtractor={listViewKeyExtractor}
                      renderItem={({item, index}) => {
                        const isHTML = /<\/?[a-z][\s\S]*>/i.test(item?.answer);
                        return (
                          <View key={index} style={styles.feqContainer}>
                            <DropDownList
                              isborder={true}
                              // index={index}
                              // currentIndex={currentIndex}
                              displayHtml={isHTML}
                              item={{
                                question: item?.question,
                                answer: item?.answer,
                              }}
                              // onPress={() =>
                              //   setCurrentIndex(
                              //     currentIndex === index ? undefined : index,
                              //   )
                              // }
                            />
                          </View>
                        );
                      }}
                      data={faqQuestions}
                    />
                  </View>
                </View>
                <View style={styles.linearGradientView}>
                  <LinearGradient
                    colors={[
                      colors.grey7,
                      colors.blue1,
                      colors.smoothPink,
                      colors.sunnyOrange6,
                      colors.grey7,
                    ]}
                    useAngle
                    angle={90}>
                    <View style={styles.gradientLabelView}>
                      <Spacer size="m" />
                      <Label
                        text={t('helpOrderOptions.thingCare')}
                        color="text2"
                        size="l"
                        textTransform="capitalize"
                        fontFamily="Medium"
                        align="center"
                      />
                      <Spacer size="m" />
                    </View>
                  </LinearGradient>
                </View>
                <Spacer size="sx" />

                <View>
                  <FlatList
                    showsVerticalScrollIndicator={false}
                    // data={faqData?.result}
                    data={helpFaq}
                    ItemSeparatorComponent={() => (
                      <Spacer type="Vertical" size="xm" />
                    )}
                    renderItem={renderItem}
                  />
                  <Spacer size="xl" />
                </View>
              </>
            )}
          />
        </ErrorHandler>
      )}
    </SafeAreaView>
  );
};

export default HelpCenterScene;

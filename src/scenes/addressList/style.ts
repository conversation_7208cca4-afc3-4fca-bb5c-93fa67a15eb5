import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    subView: {
      flex: Sizes.x,
      backgroundColor: colors.background2,
    },
    locationIcon: {
      marginHorizontal: Sizes.l,
      width: '82%',
    },
    locationImage: {
      width: Sizes.xl + Sizes.xs,
      height: Sizes.xl + Sizes.xs,
      alignItems: 'center',
      justifyContent: 'center',
    },
    renderContainer: {
      padding: Sizes.m,
      backgroundColor: colors.whiteColor,
      flexDirection: 'row',
    },
    defaultButton: {
      flexDirection: 'column',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    shippedDefaultButton: {
      flexDirection: 'column',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      width: '26%',
    },
    savedAdd: {
      color: colors.textLight,
      padding: Sizes.xs,
      backgroundColor: colors.smoothBlue,
      paddingLeft: Sizes.l,
    },
    tag: {borderRadius: Sizes.s, backgroundColor: colors.green},
    buttons: {
      padding: Sizes.s * Sizes.xs,
    },
    directions: {flexDirection: 'row'},
    continuerFlex: {flex: Sizes.x, paddingHorizontal: Sizes.m},
    link: {color: colors.linkText},
    separators: {
      backgroundColor: colors.blankGray,
      height: Sizes.x,
    },
    defaultSeparators: {
      backgroundColor: colors.blankGray,
      height: Sizes.x,
      flex: Sizes.x,
      marginBottom: Sizes.x,
    },
    sectionHeader: {
      backgroundColor: colors.background,
      alignItems: 'center',
      flexDirection: 'row',
    },
    subCautionerBox: {
      width: '100%',
      borderRadius: Sizes.m,
      marginBottom: Sizes.xms,
      height: Sizes.x7l,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    textCap: {
      textTransform: 'capitalize',
    },
    editBtn: {
      paddingTop: Sizes.s,
      paddingRight: Sizes.sx,
      flexDirection: 'row',
      alignItems: 'center',
    },
    deleteBtn: {
      paddingTop: Sizes.s,
      paddingHorizontal: Sizes.sx,
      flexDirection: 'row',
      alignItems: 'center',
    },
    btnContainer: {
      shadowColor: colors.black,
      shadowOffset: {
        width: Sizes.xs,
        height: Sizes.xs,
      },
      elevation: Sizes.s,
      backgroundColor: colors.whiteColor,
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.l,
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
    },
    tagView: {
      backgroundColor: colors.aliceBlue,
      height: Sizes.xxl,
      width: Sizes.xxl,
      borderRadius: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: Sizes.xm,
    },
    flex: {
      flex: Sizes.x,
    },
    defaultView: {
      backgroundColor: colors.blue,
      borderRadius: Sizes.s,
      width: Sizes.x70,
      height: Sizes.xl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    toggleOff: {
      backgroundColor: colors.text2,
      margin: 0,
      padding: 0,
      height: Sizes.xsl,
      width: Sizes.xsl,
      borderRadius: Sizes.x8l,
    },
    toggleOn: {
      height: Sizes.xsl,
      width: Sizes.xsl,
      borderRadius: Sizes.x8l,
    },
    toggleView: {
      shadowColor: colors.black25,
      shadowOpacity: 0.2,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowRadius: Sizes.s,
      elevation: Sizes.xs,
    },
    fRow: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    row: {
      flexDirection: 'row',
    },
    awayView: {
      backgroundColor: colors.limeGreen,
      paddingHorizontal: Sizes.xm,
      height: Sizes.xl,
      borderRadius: Sizes.s,
      justifyContent: 'center',
      alignItems: 'center',
    },
    defaultGif: {
      width: Sizes.exl,
      height: Sizes.xxxl,
      marginRight: -Sizes.xl,
    },
  });

export default styles;

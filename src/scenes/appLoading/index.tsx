import { View, ActivityIndicator, Platform } from 'react-native';
import React, { useCallback, useEffect } from 'react';
import { useTheme } from '@react-navigation/native';
import stylesWithOutColor from './style';
import { useDispatch } from 'react-redux';
import {
  generateNewCart,
  getCountriesList,
  getUserInfo,
  getWhatsAppLink,
  setIsLoggedIn,
  getRewardCoins,
  getFreeGiftProduct,
  getOfferProduct,
} from 'app-redux-store/slice/appSlice';
import localStorage from 'utils/localStorage';
import tokenClass from 'utils/token';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamsList } from 'routes';
import appsFlyer from 'react-native-appsflyer';
import SplashScreen from 'react-native-splash-screen';
import {extractQueryParams} from 'utils/url_resolver_keys';
import {getDeepLinkData} from 'utils/get_request';
import {useMemo} from 'react';
import { debugError, debugLog } from 'utils/debugLog';
import { resolveUrl } from 'utils/resolveUrl';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const AppLoading = ({ navigation }: Props) => {
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();

  const setInitialScreen = useCallback(async () => {
    const isFirstTime = await localStorage.get('firstTime');
    const isLoggedIn = await tokenClass.loginStatus();
    dispatch(setIsLoggedIn(isLoggedIn));
    if (isLoggedIn) {
      dispatch(getUserInfo());
    }
    if (isLoggedIn || isFirstTime) {
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Tab',
            state: {
              routes: [
                {
                  name: 'Shop',
                },
              ],
            },
          },
        ],
      });
    } else {
      localStorage.set('firstTime', 'installed great!');
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Tab',
            state: {
              routes: [
                {
                  name: 'Shop',
                },
              ],
            },
          },
        ],
      });
    }
  }, [dispatch, navigation]);
  const cleanUrlKey = (url) => {
    try {
      const parsed = new URL(url);
      return parsed.origin + parsed.pathname; // removes ?query and #hash
    } catch (e) {
      console.warn("Invalid URL passed to cleanUrlKey:", url);
      if (typeof url === 'string') {
        return url.split('?')[0].split('#')[0];
      }
      return url;
    }
  };  
  const handleInitialScreen = useCallback(
    async (res: any) => {
       debugLog('handleInitialScreen--------->', res);
       
      let urlKey: any;
      if (Platform.OS === 'ios' && res?.data?.urlKey) {
        const fullUrl = res.data.link;
        debugLog('Full URL:', fullUrl);
        // urlKey = fullUrl
        //   .replace('https://www.dentalkart.com', '')
        //   .split('?')[0];
        // if(res?.data?.urlKey){
          urlKey = res?.data?.urlKey;
          debugLog('Extracted Pathname:', urlKey);
          if(urlKey){
            return setTimeout(async () => {
              // return navigation.navigate('UrlResolver', {
              //   urlKey: '/' + urlKey,
              // });
              await resolveUrl({ urlKey, navigation })
              return true;
            }, 1500);
  
          }
      } else if (res?.data?.host === 'www.dentalkart.com'||   res?.data?.link?.startsWith('https://www.dentalkart.com')
      ) {
        if (res?.data?.path) {
          urlKey = res?.data?.path;
          return setTimeout(async () => {
            // return navigation.navigate('UrlResolver', {
            //   urlKey: urlKey,
            //   referralCode: res?.data?.referralCode || null,
            //   referType: res?.data?.referType || null,
            //   productSku: res?.data?.productSku,
            // });
              const referralCode =  res?.data?.referralCode;
              const referType =  res?.data?.referType;
              const productSku= res?.data?.productSku;
              const link=res?.data?.link;
              console.log(link,'^^^^^^^^^^^^')
              await resolveUrl({ urlKey, referralCode, referType, productSku, navigation,link })
              return true;
          }, 1000);
        }
        else{
          const extractPath = (url: string): string => {
            const match = url.match(/^https?:\/\/[^/]+(\/[^?]+)/);
            return match ? match[1] : '';
          };
          
          const path = extractPath(res?.data?.link);
          console.log('path*****', path);
          if(!path){
            return navigation?.navigate('Tab', {screen: 'Shop'});

          }
          return setTimeout(async () => {
            // return navigation.navigate('UrlResolver', {
            //   urlKey: urlKey,
            //   referralCode: res?.data?.referralCode || null,
            //   referType: res?.data?.referType || null,
            //   productSku: res?.data?.productSku,
            // });
              const referralCode =  res?.data?.referralCode;
              const referType =  res?.data?.referType;
              const productSku= res?.data?.productSku;
              const link=res?.data?.link;
              await resolveUrl({ urlKey:path, referralCode, referType, productSku, navigation, link })
              return true;
          }, 1000);
        }
      } else {
        urlKey = res?.data?.urlKey;
        if (!!res?.data?.deep_link_value) {
          if (res?.data?.deep_link_value === 'shorts') {
            const videoId = res?.data?.videoId;
            return navigation.navigate('ShortVideos', {
              videoId: videoId,
            });
          } else if (res?.data?.deep_link_value === 'cart') {
            return navigation.navigate('Cart');
          } else if (res?.data?.deep_link_value === 'products') {
            return setTimeout(async () => {
              // return navigation.navigate('UrlResolver', {
              //   urlKey: '/' + urlKey,
              //   referralCode: res?.data?.referralCode,
              //   referType: res?.data?.referType,
              // });
              urlKey = '/' + urlKey;
              const referralCode =  res?.data?.referralCode;
              const referType =  res?.data?.referType;
              await resolveUrl({ urlKey, referralCode, referType, navigation })
              return true;
            }, 1500);
          } else if (res?.data?.deep_link_value === 'profile') {
            return navigation.navigate('Profile');
          } else if (res?.data?.deep_link_value === 'order-list') {
            return setTimeout(() => {
              setTimeout(() => {
                navigation.navigate('OrderList');
              }, 0);
              navigation.navigate('Profile');
            }, 1000);
          } else if (res?.data?.deep_link_value === 'sale') {
            return navigation.navigate('AllBrands', {
              saleUrl: 'https://www.dentalkart.com' + res?.data?.urlKey,
            });
          } else if (res?.data?.deep_link_value === 'home') {
            return navigation.navigate('HomePage');
          }
        }
      }

      if (!!urlKey) {
        // return navigation.navigate('UrlResolver', {
        //   urlKey: '/' + urlKey,
        // });
        await resolveUrl({ urlKey, navigation })
        return true;
      }
      return setInitialScreen();
    },
    [navigation, setInitialScreen],
  );

  const extractDeeplinkData = useCallback(
    async (url: string) => {
      try {
        const extraParams = extractQueryParams(url);
        const urlBeforeQueryParams = url.split('?');
        const extractedUrlBeforeQueryParams = urlBeforeQueryParams[0]
          .split('/')
          .reverse();
        const deeplinkId = extractedUrlBeforeQueryParams[0];
        const onelinkId = extractedUrlBeforeQueryParams[1];
        const resultPromise = await getDeepLinkData(onelinkId, deeplinkId);
        debugLog(resultPromise, 'reultPromise')
        const result = await resultPromise.json();
        const res: any = {
          data: {},
        };
        if (Object.keys(result).length > 0) {
          Object.keys(result).forEach((k, i) => {
            res.data[k] = result[k];
          });
        }
        if (Object.keys(extraParams).length > 0) {
          Object.keys(extraParams).forEach((k, i) => {
            res.data[k] = extraParams[k];
          });
        }
        handleInitialScreen(res);
      } catch (error) {
        debugError(error);
      }
    },
    [handleInitialScreen],
  );

  useEffect(() => {
    const getInitialAPICall = async () => {
      // const isLoggedIn = await tokenClass.loginStatus();
      // dispatch(generateNewCart());
      // dispatch(getCountriesList());
      // dispatch(getWhatsAppLink());
      dispatch(getFreeGiftProduct());
      // dispatch(getOfferProduct());
    };
    getInitialAPICall();
  }, []);

  useEffect(() => {
    let isDeeplink = false;
    let isScreenSet = false;
    let deepLinkListener: { (): void; remove?: any; };

    const initAppsFlyer = async () => {
      try {
        // Initialize SDK
        appsFlyer.initSdk(
          {
            devKey: '56HVuQCdooe9EBMnNkzPnC',
            isDebug: true,
            appId: Platform.OS === 'android' ? 'com.vasadental.dentalkart' : '1382207992',
            onDeepLinkListener: true,
          },
          result => debugLog('appsFlyer init result:', result),
          error => debugError('appsFlyer init error:', error)
        );

        appsFlyer.startSdk();

        // Listen to deep links
        deepLinkListener = appsFlyer.onDeepLink(async res => {
          // const normalized = normalizeAppsFlyerData(res);

          debugLog('appsFlyer.onDeepLink:', res);

          const isValidDeepLink = res?.deepLinkStatus !== 'NOT_FOUND' && res?.status !== 'failure';

          if (isValidDeepLink) {
            isDeeplink = true;
            await handleInitialScreen(res);
          } else if (!isScreenSet) {
            isScreenSet = true;
            setInitialScreen();
          }
        });

        if (!isDeeplink && !isScreenSet) {
          isScreenSet = true;
          setInitialScreen();
        }

      } catch (error) {
        debugError('AppsFlyer init error:', error);
        if (!isScreenSet) {
          isScreenSet = true;
          setInitialScreen();
        }
      }
    };

    initAppsFlyer();

    return () => {
      deepLinkListener?.remove();
    };
  }, []);

  return (
    <View style={styles.indicatorBackground}>
      <ActivityIndicator color={colors.primary} size="large" />
    </View>
  );
};

export default AppLoading;

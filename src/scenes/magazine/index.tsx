import React, {useCallback, useEffect, useState} from 'react';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../routes';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {
  FlatList,
  Linking,
  TextInput,
  TouchableOpacity,
  View,
  SafeAreaView,
} from 'react-native';
import {
  FooterButton,
  ImageIcon,
  Label,
  Separator,
  Spacer,
} from 'components/atoms';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import {
  getMagazineBanner,
  getMagazineList,
  getPublisher,
} from 'services/magazine';
import {useDispatch, useSelector} from 'react-redux';
import {setLoading} from 'app-redux-store/slice/appSlice';
import FilterModal from 'components/organisms/filterModal';
import Share from 'react-native-share';
import {months} from 'staticData';
import {debounce} from 'utils/utils';
import {useMemo} from 'react';
import { RootState } from '@types/local';
import { AnalyticsEvents } from 'components/organisms';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const MagazineScene = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const [modelVisible, setModelVisible] = useState(false);
  const [banner, setBanner] = useState<any>();
  const [allMagazine, setAllMagazine] = useState<any[]>([]);
  const [publishers, setPublishers] = useState<any[]>([]);
  const [filters, setFilters] = useState({
    publisher: [],
    year: Array.from({length: 30}, (_, index) => ({
      label: (new Date().getFullYear() - index).toString(),
      value: (new Date().getFullYear() - index).toString(),
    })),

    month: months,
  });
  const [appliedFilters, setAppliedFilters] = useState({
    magazine_name: '',
    publisher: [],
    year: [],
    month: [],
  });
  const [filtersArray, setFiltersArray] = useState<string[]>([]);
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  const getAllMagazine = useCallback(
    async (filters = {}) => {
      dispatch(setLoading(true));
      const {data} = await getMagazineList(filters);
      setAllMagazine(data?.result || []);
      dispatch(setLoading(false));
    },
    [dispatch],
  );

  const getPublisherList = useCallback(async () => {
    dispatch(setLoading(true));
    const {data} = await getPublisher();
    setPublishers(data);
    setFilters(prev => ({...prev, publisher: data}));
    dispatch(setLoading(false));
  }, [dispatch]);

  const magazineBanner = useCallback(async () => {
    dispatch(setLoading(true));
    const {data} = await getMagazineBanner();
    setBanner(data);
    dispatch(setLoading(false));
  }, [dispatch]);

  const share = async (url: any, socialName: string) => {
    try {
      const shareResponse = await Share.shareSingle({
        url: url,
        social: Share.Social[socialName],
      });
    } catch (error) {}
  };

  const updateFiltersArray = () => {
    const newFiltersArray = Object.values(appliedFilters)
      .flat()
      .filter(value => value !== '' && value?.length > 0)
      .map(filterItem => {
        const month = filters?.month?.find(m => m.value === filterItem);
        return month ? month?.label : filterItem;
      });
    setFiltersArray(newFiltersArray);
  };

  const handleApplyFilters = (filters: any) => {
    setAppliedFilters(filters);
    getAllMagazine(filters);
    setModelVisible(false);
  };

  const clearFilter = (filterItem: string) => {
    const newAppliedFilters = {...appliedFilters};
    const monthValue = filters?.month?.find(m => m.label === filterItem)?.value;

    if (appliedFilters?.publisher?.includes(filterItem)) {
      newAppliedFilters.publisher = newAppliedFilters?.publisher?.filter(
        item => item !== filterItem,
      );
    } else if (appliedFilters?.year?.includes(filterItem)) {
      newAppliedFilters.year = newAppliedFilters?.year.filter(
        item => item !== filterItem,
      );
    } else if (appliedFilters?.month?.includes(monthValue)) {
      newAppliedFilters.month = newAppliedFilters?.month.filter(
        item => item !== monthValue,
      );
    } else if (appliedFilters?.magazine_name === filterItem) {
      newAppliedFilters.magazine_name = '';
    }
    setAppliedFilters(newAppliedFilters);
    getAllMagazine(newAppliedFilters);
    updateFiltersArray();
  };
  const bannerUrl = banner?.[0]?.banner_url || null;
  const bannerCoverage = banner?.[0]?.banner_coverpage || null;

  const hasAppliedFilters = () =>
    Object.values(appliedFilters).some(filterArray =>
      Array.isArray(filterArray) ? filterArray?.length > 0 : filterArray,
    );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Label size="mx" text={'No data found'} />
    </View>
  );

  const renderItems = useCallback(
    (item: Magazine, index: number) => {
      const date = new Date(item.date_of_publish);
      const monthIndex = date.getMonth();
      const year = date.getFullYear();

      const month = months[monthIndex]?.label || 'Unknown';

      return (
        <View key={index} style={styles.magCardView}>
          <View style={styles.title}>
            <Label
              color="text"
              size="mx"
              fontFamily="Medium"
              text={`${item?.magazine_name} ${year}`}
              style={styles.flex}
              numberOfLines={1}
            />
            <Spacer size="s" />
            <TouchableOpacity
              onPress={() => share(item?.magazine_url, 'WHATSAPP')}>
              <ImageIcon size="xxl" icon="share" tintColor="text" />
            </TouchableOpacity>
          </View>
          <Label
            color="text2"
            style={styles.monthText}
            size="mx"
            align="left"
            fontFamily="Regular"
            text={month}
          />
          <Spacer size="xm" />
          <FastImage
            style={styles.magImage}
            source={{uri: item?.coverPage_url}}
            resizeMode="cover"
          />
          <Spacer size="xm" />
          <View style={styles.footerCard}>
            <TouchableOpacity
              onPress={() => Linking.openURL(item?.magazine_url)}
              style={styles.readBtn}>
              <Label
                size="m"
                fontFamily="Regular"
                text={t('magazine.read')}
                color="categoryTitle"
              />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="s" />
            <View style={styles.flex} />
            <View style={styles.subFooterCard}>
              <TouchableOpacity
                onPress={() => Linking.openURL(item?.magazine_url)}>
                <ImageIcon
                  icon="downloadButtonIcon"
                  size="xxl"
                  tintColor="text"
                />
              </TouchableOpacity>
              <Spacer type="Horizontal" size="xm" />
              <Separator color="grey2" Vertical />
              <Spacer type="Horizontal" size="xm" />
              <TouchableOpacity>
                <ImageIcon size="xxl" icon="newBookmarkIcon" tintColor="text" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      );
    },
    [styles, months],
  );

  const handleClearFilters = () => {
    setAppliedFilters({
      magazine_name: '',
      publisher: [],
      year: [],
      month: [],
    });
    getAllMagazine();
  };
  useEffect(() => {
    AnalyticsEvents('MAGAZINE', 'Magazine viewed', {}, userInfo, isLoggedIn);
  }, []);

  useEffect(() => {
    updateFiltersArray();
  }, [appliedFilters]);

  const debouncedMagazine = useCallback(
    debounce(searchTerm => {
      getAllMagazine({...appliedFilters, magazine_name: searchTerm});
    }, 1000),
    [appliedFilters, getAllMagazine],
  );

  const handleSearchInputChange = text => {
    debouncedMagazine(text);
  };

  useEffect(() => {
    getAllMagazine();
    magazineBanner();
    getPublisherList();
  }, [getAllMagazine, magazineBanner, getPublisherList]);

  const renderFilterItem = (item: string, index: number) => {
    return (
      <React.Fragment key={index}>
        <View style={styles.filterValue}>
          <Label
            color="categoryTitle"
            fontFamily="Regular"
            size="mx"
            text={item}
          />
          <Spacer type="Horizontal" size="sx" />
          <TouchableOpacity onPress={() => clearFilter(item)}>
            <ImageIcon size="xl" icon="cross" tintColor="categoryTitle" />
          </TouchableOpacity>
        </View>
        <Spacer type="Horizontal" size="xm" />
      </React.Fragment>
    );
  };

  return (
    <SafeAreaView style={styles.flex}>
      <View style={styles.container}>
        <View style={styles.searchBarView}>
          <TouchableOpacity
            style={styles.arrowButton}
            onPress={() => navigation.navigate('HomePage')}>
            <ImageIcon icon="arrowLeft" size="xxl" />
          </TouchableOpacity>
          <View style={styles.searchInput}>
            <TextInput 
              testID="txtMagazineSearch"
              placeholder={t('magazine.searchMagazine')}
              onChangeText={handleSearchInputChange}
              allowFontScaling={false}
            />
          </View>
          <View style={styles.searchIconView}>
            <TouchableOpacity>
              <ImageIcon icon="searchIcon" size="xsl" />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xms" />
            <Separator Vertical thickness="z" />
            <Spacer type="Horizontal" size="xms" />
            <TouchableOpacity>
              <ImageIcon tintColor="black" size="xsl" icon={'microPhone'} />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xm" />
          </View>
        </View>
        <FlatList
          showsVerticalScrollIndicator={false}
          data={['']}
          renderItem={() => (
            <View>
              {!hasAppliedFilters() ? (
                <>
                  {bannerCoverage && (
                    <>
                      <Spacer size="m" />
                      <TouchableOpacity
                        onPress={() => Linking.openURL(bannerUrl)}>
                        <FastImage
                          style={styles.magBannerView}
                          source={{uri: bannerCoverage}}
                          resizeMode="stretch"
                        />
                      </TouchableOpacity>
                    </>
                  )}
                  {/* <Spacer size="m" />
                  <View style={styles.suggestMagBtnView}>
                    <TouchableOpacity>
                      <LinearGradient
                        colors={['#FF8A50', '#EB5003']}
                        start={{x: 0, y: 0}}
                        end={{x: 1, y: 1}}
                        style={styles.suggestMagBtn}>
                        <Label
                          size="xms"
                          color="whiteColor"
                          weight="500"
                          text={'SUGGEST MAGZINE'}
                        />
                      </LinearGradient>
                    </TouchableOpacity>
                    <Label text={'2023'} />
                  </View> */}
                </>
              ) : (
                <View style={styles.filterValueMainView}>
                  <TouchableOpacity
                    style={styles.button1Opacity}
                    onPress={handleClearFilters}>
                    <Label
                      size="mx"
                      color="sunnyOrange4"
                      fontFamily="Medium"
                      text={t('filters.clear')}
                    />
                  </TouchableOpacity>
                  <Spacer type="Horizontal" size="m" />
                  <View style={styles.filterValueView}>
                    <FlatList
                      showsHorizontalScrollIndicator={false}
                      horizontal
                      data={filtersArray}
                      renderItem={({item, index}) =>
                        renderFilterItem(item, index)
                      }
                    />
                  </View>
                </View>
              )}
              <Spacer size="m" />
              <View>
                <FlatList
                  columnWrapperStyle={styles.cardFlatList}
                  ItemSeparatorComponent={() => <Spacer size="l" />}
                  numColumns={2}
                  data={allMagazine}
                  renderItem={({item, index}) => renderItems(item, index)}
                  keyExtractor={(item, index) => `${item?.id}_${index}`}
                  ListEmptyComponent={renderEmptyComponent}
                />
              </View>
            </View>
          )}
        />
      </View>
      <FooterButton
        shadowProps={true}
        separator={useCallback(
          () => (
            <Separator Vertical color="grey2" />
          ),
          [],
        )}
        buttons={[
          {
            text: t('filters.filter'),
            labelSize: 'l',
            weight: '400',
            labelColor: 'text',
            selfAlign: 'stretch',
            onPress: () => setModelVisible(true),
            iconLeft: 'filterIcons',
          },
          {
            text: t('magazine.library'),
            labelSize: 'l',
            weight: '400',
            labelColor: 'text',
            selfAlign: 'stretch',
            iconLeft: 'sortIcons',
            onPress: () => {},
          },
        ]}
      />
      {modelVisible && (
        <FilterModal
          visible={modelVisible}
          onClose={() => setModelVisible(false)}
          filters={filters}
          publishers={publishers}
          onApplyFilters={handleApplyFilters}
          appliedFilters={appliedFilters}
        />
      )}
    </SafeAreaView>
  );
};

export default MagazineScene;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    durationsBox: {
      flex: Sizes.x,
      flexWrap: 'wrap',
      flexDirection: 'row',
      alignItems: 'center',
    },
    box: {
      backgroundColor: colors.whiteColor,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.xms,
      paddingVertical: Sizes.xm,
    },
    processingContinuer: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    imageBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    image: {
      position: 'relative',
      borderWidth: Sizes.x,
      padding: Sizes.xs,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      alignItems: 'center',
      justifyContent: 'center',
      width: Sizes.x52,
      height: Sizes.x52,
    },
    mainImage: {
      flexDirection: 'row',
    },
    imageSize: {
      width: Sizes.x8l,
      height: Sizes.x8l,
      borderRadius: Sizes.xm,
    },
    buttonStyle: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    iconStyle: {
      width: Sizes.xxl,
      height: Sizes.xxl,
      marginRight: Sizes.xms,
    },
    returnOrderView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    underPrizeIconView: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.darkBlue,
      zIndex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    approvedView: {
      backgroundColor: colors.green4,
      alignItems: 'center',
      height: Sizes.xsl,
      justifyContent: 'center',
      paddingHorizontal: Sizes.xms,
      borderRadius: Sizes.s,
      marginLeft: Sizes.s,
    },
    earnedView: {
      flexDirection: 'row',
    },
    buyAgainDropView: {
      backgroundColor: colors.lightCyan,
      width: Sizes.xx4l,
      alignItems: 'center',
      height: Sizes.xx4l,
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    buyAgainSubView: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    canCancelView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: Sizes.l,
      width: '100%',
      alignSelf: 'center',
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xms,
      marginTop: Sizes.s,
    },
    rewardIconSmall: {
      width: Sizes.xsl,
      height: Sizes.xsl,
    },
    infoStyle: {
    },
  });

export default styles;

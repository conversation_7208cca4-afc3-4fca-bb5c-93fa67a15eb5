import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';
import {checkDevice} from 'utils/utils';

const {width} = Dimensions.get('window');

const WishlistDetailLoader = () => {
  const {colors} = useTheme();
  const tab = checkDevice();
  const boxWidth = (width - (tab ? 50 : 30)) / (tab ? 4 : 2);
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={2230}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="20" rx="5" ry="5" width={width - 20} height="30" />

        <Rect x={10} y="60" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="60"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="60"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="60"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="420" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="420"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="420"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="420"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="780" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="780"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="780"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="780"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="1140" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="1140"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="1140"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="1140"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="1500" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="1500"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="1500"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="1500"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="1860" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="1860"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="1860"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="1860"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}
      </ContentLoader>
    </ScrollView>
  );
};

export default WishlistDetailLoader;

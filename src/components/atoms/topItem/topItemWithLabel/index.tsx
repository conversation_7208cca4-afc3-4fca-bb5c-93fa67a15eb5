import Label from 'components/atoms/label';
import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {View, TouchableOpacity, ViewProps, TextProps} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import stylesWithOutColor from './style';
import FastImagesItem from 'components/atoms/fastImages';
import ImageIcon from 'components/atoms/imageIcon';
import {useTheme} from '@react-navigation/native';
import Separator from 'components/atoms/separator';
import {useMemo} from 'react';

type Props = {
  item?: gethomepageBrandsItemProps | undefined;
  labels?: boolean;
  productImage?: boolean;
  nextErr?: boolean;
  index: number;
  style?: ViewProps['style'];
  imageBoxStyle?: ViewProps['style'];
  labelStyle?: ViewProps['style'];
  numberOfLines?: TextProps['numberOfLines'];
  onPress: () => void;
};

const TopItemWithLabel = ({
  labels = false,
  productImage = true,
  item,
  index,
  style,
  nextErr = false,
  onPress,
  numberOfLines = 1,
  labelStyle,
  imageBoxStyle,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <>
      <TouchableOpacity
        key={index}
        style={[styles.topBrandsAlign, style]}
        onPress={onPress}>
        {productImage && (
          <LinearGradient
            style={[styles.roundImage, imageBoxStyle]}
            colors={[colors.labelBrand, colors.whiteColor]}>
            <View style={styles.roundImage}>
              {nextErr ? (
                <ImageIcon icon={'nextBlueIcon'} />
              ) : (
                <FastImagesItem
                  FastImageStyle={styles.imageCecile}
                  resizeMode="contain"
                  source={{uri: item?.iconUrl ? item?.iconUrl : item?.logo}}
                />
              )}
            </View>
          </LinearGradient>
        )}

        {labels && (
          <Label
            color="textLight"
            size="m"
            weight="400"
            style={[styles.label, labelStyle]}
            ellipsizeMode="tail"
            text={item ? item?.categoryName || item?.name : 'View All'}
            numberOfLines={numberOfLines}
          />
        )}
      </TouchableOpacity>
    </>
  );
};

export default TopItemWithLabel;

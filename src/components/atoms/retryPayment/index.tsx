import React, {useState} from 'react';
import {TouchableOpacity, View, TouchableWithoutFeedback} from 'react-native';
import {useTheme} from '@react-navigation/native';
import Modal from 'react-native-modal';
import {
  ImageIcon,
  Label,
  Spacer,
  Radio,
  RetryPaymentButton,
} from 'components/atoms';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  data: OrderFilterOption[];
  onSelectMethod: (data: string, paymentMethod: string) => void;
  checkPayment: () => void;
};

const RetryPaymentModal = (props: Props) => {
  const {visible, onClose, data, onSelectMethod, checkPayment} = props;
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [selectedMethod, setSelectedMethod] = useState(null);

  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={styles.imageModelMain}>
        <View style={styles.subImageView}>
          <TouchableWithoutFeedback onPress={onClose}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={onClose}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x5l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          {data?.can_refetch ? (
            <View style={styles.rePayView}>
              <RetryPaymentButton
                showBtn={true}
                btnText={t('buttons.place')}
                checkPayment={() => checkPayment()}
              />
            </View>
          ) : (
            <View style={styles.paymentMView}>
              <View style={styles.filterListView}>
                <Label
                  text={t('payment.selectPaymentMethod')}
                  size="mx"
                  color="text"
                  weight="500"
                  fontFamily="Medium"
                />

                <View style={styles.paymentView}>
                  {data?.payment_methods?.map((item, index) => {
                    return (
                      <TouchableOpacity
                        key={index?.toString()}
                        style={styles.paymentSubView}
                        onPress={() => {
                          setSelectedMethod(item);
                        }}>
                        <Radio
                          selected={selectedMethod?.code === item?.code}
                          fillColor="text2"
                          style={{borderColor: colors.text}}
                          onPress={() => {
                            setSelectedMethod(item);
                          }}
                        />
                        <Spacer size="m" type="Horizontal" />
                        <View style={styles.flex}>
                          <Label
                            text={item?.title}
                            size="m"
                            fontFamily="Medium"
                            color="text"
                            weight="500"
                          />
                        </View>
                        {item?.code === 'razorpay' ? (
                          <>
                            <Spacer size="xm" type="Horizontal" />
                            <FastImage
                              style={styles.payCashView}
                              source={Icons.goCashlessGif}
                            />
                          </>
                        ) : (
                          <View />
                        )}
                      </TouchableOpacity>
                    );
                  })}
                  <Spacer size="l" />
                  {data?.payment_methods?.length === 0 ? (
                    <Label
                      text={t('checkOut.noService')}
                      size="m"
                      fontFamily="Regular"
                      color="text2"
                    />
                  ) : null}
                </View>
              </View>
              <View style={styles.mainBtnStyle}>
                <View style={{paddingBottom: insets.bottom}}>
                  <Button
                    onPress={() => {
                      selectedMethod?.code
                        ? onSelectMethod(data, selectedMethod?.code)
                        : null;
                    }}
                    text={t('buttons.place')}
                    type={!selectedMethod?.code ? 'disabled' : 'primary'}
                    paddingHorizontal="l"
                    labelColor="whiteColor"
                    weight="500"
                    labelSize="mx"
                    radius="xms"
                    withGradient
                    size="medium"
                  />
                </View>
              </View>
            </View>
          )}

          <Spacer size="l" />
        </View>
      </View>
    </Modal>
  );
};

export default RetryPaymentModal;

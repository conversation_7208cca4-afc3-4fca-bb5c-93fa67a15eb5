import React, {useCallback, useEffect, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import {RootStackParamsList} from 'routes';
import stylesWithOutColor from './style';
import {RouteProp, useTheme} from '@react-navigation/native';
import {ImageIcon, Label, ListView, Spacer, EmptyView} from 'components/atoms';
import {ActivityIndicator, View, BackHandler} from 'react-native';
import {RewardsTransaction, RootState} from '@types/local';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import {accountSummery, rewordTransactions} from 'services/account';
import {productDummyImage} from 'utils/imageUrlHelper';
import {RewardLoader} from 'skeletonLoader';
import {myMemberShipsData} from 'services/mambership';
import Icons from 'common/icons';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import { AnalyticsEvents } from 'components/organisms';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'MyRewords'>;
};

const MyRewordsScene = ({navigation, route}: Props) => {
  const TAG = 'MyRewordsZoneScreen';
  const dispatch = useDispatch();
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [moreLoading, setMoreLoading] = useState<boolean>(false);
  const [activePlan, setActivePlan] = useState<boolean>(false);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [pageLimit, setPageLimit] = useState<number>(10);
  const [pageNo, setPageNo] = useState<number>(1);
  const [loader, setLoader] = useState<boolean>(true);
  const [rewordTrans, setRewordTrans] = useState<[RewardsTransaction] | []>([]);
  const [accountsRewords, setAccountsRewords] = useState<
    [RewardsAccountInfo] | []
  >([]);

  const transactionHistory = useCallback(async () => {
    if (pageNo == 1) {
      setLoader(true);
    } else {
      setMoreLoading(true);
    }
    const response = await rewordTransactions({
      page_size: pageLimit,
      page: pageNo,
    });
    setLoader(false);
    setMoreLoading(false);
    const {status, data} = response;
    if (status) {
      if (pageNo == 1) {
        setRewordTrans(data);
        const maxPages = Math.ceil(data?.count / pageLimit);
        setTotalPages(maxPages);
      } else {
        setRewordTrans({
          ...data,
          transactions: [...rewordTrans.transactions, ...data.transactions],
        });
      }
    }
  }, [dispatch, pageNo]);

  const accountRewordsApi = useCallback(async () => {
    setLoader(true);
    const {data} = await accountSummery();
    setLoader(false);
    if (data) {
      setAccountsRewords(data);
    }
  }, []);

  useEffect(() => {
    transactionHistory();
  }, [transactionHistory]);

  useEffect(() => {
    accountRewordsApi();
    myMembershipApis();
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const myMembershipApis = useCallback(async () => {
    setLoader(true);
    const {data, status} = await myMemberShipsData();
    setLoader(false);
    if (status) {
      if (data?.memberships?.length > 0) {
        const activePlanList = data?.memberships.filter(
          (item: MembershipModel) => item.is_active,
        );
        setActivePlan(activePlanList?.length > 0 ? true : false);
      }
    }
  }, []);

  const fetchMoreData = useCallback(() => {
    if (!loader && !moreLoading && pageNo < totalPages) {
      setPageNo(pageNo + 1);
    }
  }, [loader, moreLoading, pageNo, totalPages]);

  const emptyData = ({}) => {
    return (
      <View style={styles.noTransactionContainer}>
        <View>
          <ImageIcon tintColor="lightGray" size="ex" icon="questionIcon" />
        </View>
        <Spacer type="Vertical" size="l" />
        <Label
          color="lightGray"
          fontFamily="Regular"
          text={t('myRewords.transactions')}
        />
        <Spacer type="Vertical" size="l" />
        <Button
          onPress={() => {
            navigation.navigate('HomePage');
          }}
          labelStyle={styles.buttonLabel}
          iconStyle={styles.icons}
          style={styles.shopButton}
          radius="m"
          text={t('myRewords.shopNow')}
          iconRight={'nextArrow'}
        />
      </View>
    );
  };

  const renderHistoryList = useCallback(
    ({item, index}: {item: RewardsTransaction; index: any}) => {
      return (
        <View key={index} style={styles.historyListContainer}>
          <View style={styles.rowSpaceBetween}>
            <Label
              color={item?.state === 'Deducted' ? 'venetianRed' : 'green'}
              size="m"
              fontFamily="Medium"
              text={item?.state + ' ' + item?.coins}
            />
            <Label
              size="m"
              fontFamily="Medium"
              text={item?.date.split(',')[0]}
            />
            <Label
              color="blue"
              size="m"
              fontFamily="Medium"
              text={item?.status}
            />
          </View>
          {item?.order_id && (
            <>
              <Spacer size="xms" />
              <Label
                size="m"
                fontFamily="Medium"
                text={t('myRewords.orderNo') + item?.order_id}
              />
            </>
          )}
          <Spacer size="xm" />
          <View style={styles.sapraterView} />
          <Spacer size="m" />
          <View style={styles.expiredView}>
            {item?.expiry_date && (
              <Label
                color="grey"
                text={`${t('myRewords.expire2')} ${
                  item?.expiry_date ? item?.expiry_date : ''
                }`}
                size="xms"
                fontFamily="Medium"
              />
            )}
            <View style={styles.flex} />
            <Label
              color="grey"
              text={t('myRewords.txtNo') + item?.txn_id}
              size="xms"
              fontFamily="Medium"
            />
          </View>
        </View>
      );
    },
    [],
  );

  const renderFooter = () => {
    return moreLoading ? (
      <View style={styles.loaderStyle}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    ) : null;
  };

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);
  useEffect(() => {
    AnalyticsEvents('MY_REWARDS', 'My rewards viewed', {}, userInfo, isLoggedIn);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton
          navigation={navigation}
          searchIcon={true}
          bagIcon={true}
          text={t('rewardCoin.rewardCoins')}
          onPressNavigation={() => {
            navigation.goBack();
            if (route.params?.goBack) {
              route.params?.goBack();
            }
          }}
        />
      </ErrorHandler>
      {loader ? (
        <RewardLoader />
      ) : (
        <>
          <ErrorHandler
            componentName={`${TAG} RewardCoinList`}
            onErrorComponent={<View />}>
            {rewordTrans && rewordTrans?.transactions?.length > 0 ? (
              <LinearGradient
                colors={[colors.text, colors.categoryTitle]}
                start={{x: 0.5, y: 0}}
                end={{x: 1, y: 1}}
                locations={[0.0685, 0.5662]}
                style={styles.flex}>
                <View style={styles.balanceMainView}>
                  <View style={styles.balanceView}>
                    <View>
                      <View style={styles.balanceSubView}>
                        <Label
                          color="whiteColor"
                          text={accountsRewords?.available_coins || 0}
                          size="x4l"
                          fontFamily="Medium"
                        />
                        <Spacer size="xms" type="Horizontal" />
                        <FastImage
                          onError={e => {
                            e.target.uri = productDummyImage;
                          }}
                          resizeMode="contain"
                          style={styles.rewardIconSmall}
                          source={
                            accountsRewords?.reward_icon
                              ? {uri: accountsRewords?.reward_icon}
                              : Icons.defaultImage
                          }
                        />
                      </View>
                      <Label
                        size="mx"
                        fontFamily="Medium"
                        color="whiteColor"
                        text={
                          t('otherText.total') +
                          ' ' +
                          accountsRewords?.reward_term
                        }
                      />
                    </View>
                    <View>
                      <View style={styles.balanceSubView}>
                        <Label
                          color="whiteColor"
                          text={accountsRewords?.currency}
                          size="x4l"
                          fontFamily="Medium"
                        />
                        <Label
                          color="whiteColor"
                          text={
                            accountsRewords?.available_coins *
                              (accountsRewords?.exchange_rate?.value /
                                accountsRewords?.exchange_rate?.coins) || 0
                          }
                          size="x4l"
                          fontFamily="Medium"
                        />
                        <Spacer size="xms" type="Horizontal" />
                        <FastImage
                          onError={e => {
                            e.target.uri = productDummyImage;
                          }}
                          resizeMode="contain"
                          style={styles.rewardIconSmall}
                          source={{uri: accountsRewords?.currency_icon}}
                        />
                      </View>
                      <Label
                        size="mx"
                        color="whiteColor"
                        text={t('rewardCoin.monetaryValue')}
                        fontFamily="Medium"
                      />
                    </View>
                  </View>
                  <Spacer size="mx" />
                  <View style={styles.fRow}>
                    <View style={styles.accountsRewordView}>
                      <Label
                        size="xms"
                        text={t('myRewords.coinSpent')}
                        color="whiteColor"
                        fontFamily="Medium"
                      />
                      <Label
                        size="xms"
                        text={`${accountsRewords?.spent_coins || 0} ( ${
                          accountsRewords?.currency
                        } ${
                          (accountsRewords?.spent_coins *
                            accountsRewords?.exchange_rate?.value) /
                            (activePlan ? 1 : 2) || 0
                        } )`}
                        color="whiteColor"
                        fontFamily="Medium"
                      />
                    </View>
                    <Spacer size="xm" type="Horizontal" />
                    <View style={styles.accountsRewordView}>
                      <Label
                        text={t('orderListing.earnedCoins')}
                        color="whiteColor"
                        size="xms"
                        fontFamily="Medium"
                      />
                      <Label
                        text={`${accountsRewords?.earned_coins || 0} ( ${
                          accountsRewords?.currency
                        } ${
                          (accountsRewords?.earned_coins *
                            accountsRewords?.exchange_rate?.value) /
                            (activePlan ? 1 : 2) || 0
                        } )`}
                        color="whiteColor"
                        size="xms"
                        fontFamily="Medium"
                      />
                    </View>
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.expireCoins}>
                    <View style={styles.coinsLeftView}>
                      <Label
                        size="xms"
                        color="whiteColor"
                        text={t('myRewords.coinsPending')}
                        fontFamily="Medium"
                      />
                      <Label
                        size="xms"
                        color="whiteColor"
                        text={`${accountsRewords?.pending_coins || 0} ( ${
                          accountsRewords?.currency
                        } ${
                          (accountsRewords?.pending_coins *
                            accountsRewords?.exchange_rate?.value) /
                            (activePlan ? 1 : 2) || 0
                        } )`}
                        fontFamily="Medium"
                      />
                    </View>
                    <Spacer size="xm" type="Horizontal" />
                    <View style={styles.coinsLeftSubView}>
                      <View style={styles.aboutToExpireView}>
                        <Label
                          size="l"
                          color="whiteColor"
                          lineHeight="xl"
                          align="center"
                          text={rewordTrans?.about_to_expire?.coins || 0}
                          fontFamily="Medium"
                        />
                        <Label
                          size="xms"
                          color="whiteColor"
                          align="center"
                          text={t('rewardCoin.aboutExpire')}
                          fontFamily="Medium"
                        />
                      </View>
                      <Spacer size="sx" type="Horizontal" />

                      <View style={styles.expiredDateView}>
                        <Label
                          align="center"
                          size="xms"
                          color="whiteColor"
                          text={`${t('rewardCoin.expiryDate')} ${
                            rewordTrans?.about_to_expire?.expire_date || ''
                          }`}
                          fontFamily="Medium"
                        />
                        <Label
                          size="xms"
                          align="center"
                          color="whiteColor"
                          text={`${t('rewardCoin.txnNo')} ${
                            rewordTrans?.about_to_expire?.txn_id || ''
                          } ${
                            rewordTrans?.about_to_expire?.date
                              ? `(${rewordTrans?.about_to_expire?.date})`
                              : ''
                          }`}
                          fontFamily="Medium"
                        />
                      </View>
                    </View>
                  </View>
                  <Spacer size="xms" />

                  <View style={styles.flex}>
                    <View style={styles.statusHeading}>
                      <Label
                        size="mx"
                        color="whiteColor"
                        fontFamily="SemiBold"
                        text={t('rewardCoin.state')}
                      />
                      <Label
                        size="mx"
                        color="whiteColor"
                        fontFamily="SemiBold"
                        text={t('rewardCoin.date')}
                      />
                      <Label
                        size="mx"
                        color="whiteColor"
                        fontFamily="SemiBold"
                        text={t('rewardCoin.status')}
                      />
                    </View>

                    <View style={styles.earnedCoinList}>
                      <ListView
                        keyExtractor={listViewKeyExtractor}
                        data={rewordTrans?.transactions}
                        renderItem={renderHistoryList}
                        ListEmptyComponent={emptyData}
                        ItemSeparatorComponent={
                          <Spacer type="Vertical" size="xs" />
                        }
                        ListFooterComponent={renderFooter()}
                        onEndReachedThreshold={0.8}
                        onEndReached={fetchMoreData}
                      />
                    </View>
                  </View>
                </View>
              </LinearGradient>
            ) : (
              <EmptyView
                text={t('otherText.orderNow')}
                title={t('profile.rewardsEmptyTxt')}
                imageName={Icons.cardCoin}
                bgImg={Icons.coinBg}
                onPress={() => navigation.popToTop()}
              />
            )}
          </ErrorHandler>
        </>
      )}
    </SafeAreaView>
  );
};

export default MyRewordsScene;

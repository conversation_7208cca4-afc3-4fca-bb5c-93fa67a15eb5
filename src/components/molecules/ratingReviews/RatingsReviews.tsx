import React, { useMemo } from 'react';
import { View, TouchableOpacity } from 'react-native';
import stylesWithOutColor from '../../../scenes/productDetail/style';
import { useTheme } from '@react-navigation/native';
import { ImageIcon, Label, Separator, Spacer, Tag } from 'components/atoms';
import { t } from 'i18next';
import { dateTimeFormat } from 'utils/formatter';
import ErrorHandler from 'utils/ErrorHandler';
import { Sizes } from 'common';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamsList } from 'routes';

const TAG = 'PoductDetailScreen';

type RatingReviewsProps = {
    review: any
    productId: number
    navigation: NativeStackNavigationProp<RootStackParamsList>
    handleRateProduct: () => void
    ratingLayout: (event: any) => void
  }
  

const RatingsReviewsComponent = ({
  review,
  productId,
  navigation,
  handleRateProduct,
  ratingLayout,
}: RatingReviewsProps) => {
    const {colors} = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View onLayout={ratingLayout} style={styles.ratingsView}>
      <View style={styles.viewSpace}>
        <View style={styles.ratingMainView}>
          <View style={styles.ratingSubView}>
            <Label
              text={t('PDP.ratingsReviews')}
              align="left"
              size="l"
              weight="600"
              color="text"
            />
            {review?.review_meta?.average_rating > 0 && (
              <>
                <Spacer size="xm" />
                <View style={styles.avgRatingView}>
                  <Label
                    text={Number(
                      review?.review_meta?.average_rating ?? 0.0,
                    ).toFixed(1)}
                    size="mx"
                    weight="400"
                    color="text"
                  />
                  <Spacer type="Horizontal" size="xm" />
                  <ImageIcon
                    size="l"
                    icon="starIcon"
                    tintColor="green"
                    style={styles.filledStarIcon}
                  />
                  <Spacer type="Horizontal" size="xm" />
                  <Separator Vertical color="grey2" />
                  <Spacer type="Horizontal" size="xm" />
                  <Label
                    text={`${review?.review_meta?.total_reviews ?? 0} ${t(
                      'PDP.reviews',
                    )}`}
                    size="mx"
                    weight="400"
                    color="text"
                  />
                </View>
              </>
            )}
          </View>
          <View>
            <Separator height="x52" Vertical color="grey2" />
          </View>
          <View style={styles.deshedView}>
            <View>
              <TouchableOpacity
                style={styles.rateProductView}
                onPress={() => handleRateProduct()}>
                <Label
                  text={t('PDP.rateProduct')}
                  color="sunnyOrange3"
                  size="l"
                  weight="500"
                />
              </TouchableOpacity>
              <Spacer size="s" />
              {!!review?.review_meta?.verified_buyers > 0 && (
                <Label
                  text={`${review?.review_meta?.verified_buyers} ${t(
                    'PDP.verifiedBuyers',
                  )}`}
                  size="mx"
                  weight="400"
                  color="text"
                  align="center"
                />
              )}
            </View>
          </View>
        </View>
        {!!review?.review_meta?.total_reviews > 0 ? (
          <>
            <Spacer size="m" />
            <Separator color="grey2" />
            <Spacer size="m" />
            <Label
              text={`${t('PDP.customerReview')} (${
                review?.review_meta?.total_reviews || 0
              })`}
              size="mx"
              weight="600"
              color="text2"
            />
            <View
              style={[
                styles.ratingView,
                review?.review_meta?.total_reviews > 1 && {
                  borderBottomColor: colors.grey2,
                  borderBottomWidth: Sizes.x,
                },
              ]}>
              <View style={styles.expiryDateView}>
                <ErrorHandler
                  componentName={`${TAG} Tag`}
                  onErrorComponent={<View />}>
                  <Tag
                    style={styles.starIconView}
                    label={(review?.reviews?.[0]?.rating || 0).toFixed(1)}
                    icon="starIcon"
                    color="green2"
                    isRightIcon
                    iconSize="l"
                  />
                </ErrorHandler>
                <Spacer size="xm" type="Horizontal" />
                <Label
                  text={review?.reviews?.[0]?.title}
                  size="mx"
                  weight="600"
                  color="text2"
                  style={styles.flex}
                />
              </View>
              <Spacer size="xm" />
              <Label
                style={styles.flex}
                numberOfLines={2}
                text={review?.reviews?.[0]?.content}
                size="mx"
                weight="500"
                color="text2"
              />
              <Spacer size="m" />
              <View style={styles.transformedTierView}>
                <View style={styles.nicNameView}>
                  <Label
                    text={review?.reviews?.[0]?.author?.substring(0, 24)}
                    size="mx"
                    weight="500"
                    color="grey3"
                  />
                  <Spacer size="xm" type="Horizontal" />
                  <Separator
                    color="grey3"
                    Vertical
                    thickness="z"
                    height="l"
                  />
                  <Spacer size="xm" type="Horizontal" />
                  <Label
                    text={dateTimeFormat(
                      review?.reviews?.[0]?.date,
                      'D MMM YYYY',
                    )}
                    size="mx"
                    weight="500"
                    color="grey3"
                  />
                </View>
              </View>
            </View>
          </>
        ) : null}
        {review?.review_meta?.total_reviews > 1 ? (
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('ReviewListScene', {
                productId: productId,
              });
            }}
            style={styles.totalReview}>
            <Label
              text={`${t('PDP.viewAll1')} ${
                review?.review_meta?.total_reviews
              } ${t('reviewRating.reviews')}`}
              size="m"
              weight="500"
              color="newSunnyOrange"
            />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default  React.memo(RatingsReviewsComponent);
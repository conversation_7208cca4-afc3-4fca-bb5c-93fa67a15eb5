import React, { useCallback, useMemo } from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';

import { checkDevice } from 'utils/utils';
import { AnalyticsEvents } from 'components/organisms';
import Label from '../label';
import ImageIcon from '../imageIcon';
import Spacer from '../spacer';
import ErrorHandler from 'utils/ErrorHandler';
import ProductCardVertical from '../productCardVertical';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import { resolveUrl } from 'utils/resolveUrl';
import { getProductCardProps } from 'utils/productProps';

const TAG = 'HotSellingSection';
interface HotSellingSectionProps {
    section: any;
    userInfo: any;
    isLoggedIn: any;
    navigation: any;
}

const HotSellingSection: React.FC<HotSellingSectionProps> = ({ section, userInfo, isLoggedIn, navigation }) => {
//   const navigation = useNavigation();
    const {colors} = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
    
  const handleHotSellingItemPress = useCallback(
    sections => async  () => {
      const eventData = {
        'Page Link': sections?.landing_url
          ? sections?.landing_url
          : '/offer-zone/hot-selling.html',
        'Sections Name': sections?.title,
        Sections: 'Homepage Sectionss',
      };
      AnalyticsEvents(
        'VIEW_ALL_HOMEPAGE',
        'View All Homepage',
        eventData,
        userInfo,
        isLoggedIn,
      );

      const urlKey =  sections?.landing_url
           ? sections?.landing_url
          : '/offer-zone/hot-selling.html'
      await resolveUrl({urlKey,navigation})

    },
    [isLoggedIn, navigation, userInfo],
  );

  const EMPTY_FREE_PRODUCTS = useMemo(() => [], []);

  const renderHotSellingItem = useCallback(
    ({ item, index }: { item: ProductData; index: number }) => (
      <ErrorHandler
        componentName={`${TAG} ProductCardVertical`}
        onErrorComponent={<View />}>
        <ProductCardVertical
          {...getProductCardProps(item)}
          index={index}
          size="large"
          imageWithBorder
          maxWidth={checkDevice() ? 0.24 : undefined}
          showWishlist
          navigation={navigation}
          freeProducts={EMPTY_FREE_PRODUCTS}
        />
      </ErrorHandler>
    ),
    [getProductCardProps, navigation],
  );

  const keyExtractor = useCallback((_, index) => index.toString(), []);
  const itemSeparator = useMemo(() => <Spacer size="xm" />, []);


  return (
    <>
      <ImageBackground source={Icons.hotSellingBanner} style={styles.backgroundImage}>
        <TouchableOpacity
          onPress={handleHotSellingItemPress(section)}
          style={styles.sellerView}>
          <Label
            text={section?.title}
            size="l"
            weight="600"
            color="whiteColor"
          />
          <ImageIcon
            icon="hotSellerRightArrow"
            size="xxl"
            tintColor="white1"
          />
        </TouchableOpacity>

        <View style={styles.listDataView}>
          <FlatList
            data={section?.elements}
            keyExtractor={keyExtractor}
            ItemSeparatorComponent={itemSeparator}
            renderItem={renderHotSellingItem}
            horizontal={checkDevice()}
            numColumns={checkDevice() ? null : 2}
            onEndReachedThreshold={0.8}
            removeClippedSubviews
            windowSize={5}
            maxToRenderPerBatch={5}
            updateCellsBatchingPeriod={50}
          />
        </View>
      </ImageBackground>
      <Spacer size="m" />
    </>
  );
};

export default React.memo(HotSellingSection);

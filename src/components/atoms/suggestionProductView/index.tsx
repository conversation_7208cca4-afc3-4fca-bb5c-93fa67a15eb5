import React from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {Button} from 'components/molecules';
import {useMemo} from 'react';

type Props = {
  suggestionClick: () => void;
};

const SuggestionProductView = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {suggestionClick} = props;

  return (
    <>
      <View style={styles.suggestionProd}>
        <Label
          text={t('homePage.lookingFor')}
          size="xxl"
          fontFamily="SemiBold"
          color="grey2"
          align="center"
        />
        <Spacer size="xms" />
        <View style={styles.desView}>
          <Label
            text={`${t('homePage.knowUs')} `}
            size="m"
            fontFamily="Medium"
            color="grey2"
            align="center"
          />
          <ImageIcon icon="edit" size="l" tintColor="lightGray" />
          <Label
            text={t('homePage.detailBelow')}
            size="m"
            fontFamily="Medium"
            color="grey2"
            align="center"
          />
        </View>

        <Spacer size="s" />
        <Label
          text={t('homePage.suggestProduct')}
          size="m"
          fontFamily="Medium"
          color="grey2"
          align="center"
        />
      </View>
      <Spacer size="mx" />
      <Button
        onPress={() => suggestionClick()}
        radius="xms"
        size="large"
        selfAlign="center"
        text={t('buttons.suggestProduct')}
        type="bordered"
        style={styles.suggestProductBtn}
      />
    </>
  );
};

export default SuggestionProductView;

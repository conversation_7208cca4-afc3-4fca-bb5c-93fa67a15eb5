<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>This app needs access to your iTunes library to play music directly from your device.</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Dev Dentalkart</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>12.4.9</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>dentalkart</string>
				<string>com.googleusercontent.apps.285108782909-rc8spqnk18cjghp98dvrqmu7oabi8lgv</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>67</string>
	<key>CodePushDeploymentKey</key>
	<string>wrZSh6MYZeqUC51ESDd8ZV2TiCyC3vedWbYse</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To get the current location for the delivery, we need this permission</string>
	<key>UIAppFonts</key>
	<array>
		<string>Poppins-Black.ttf</string>
		<string>Poppins-BlackItalic.ttf</string>
		<string>Poppins-Bold.ttf</string>
		<string>Poppins-BoldItalic.ttf</string>
		<string>Poppins-ExtraBold.ttf</string>
		<string>Poppins-ExtraBoldItalic.ttf</string>
		<string>Poppins-ExtraLight.ttf</string>
		<string>Poppins-ExtraLightItalic.ttf</string>
		<string>Poppins-Italic.ttf</string>
		<string>Poppins-Light.ttf</string>
		<string>Poppins-LightItalic.ttf</string>
		<string>Poppins-Medium.ttf</string>
		<string>Poppins-MediumItalic.ttf</string>
		<string>Poppins-Regular.ttf</string>
		<string>Poppins-SemiBold.ttf</string>
		<string>Poppins-SemiBoldItalic.ttf</string>
		<string>Poppins-Thin.ttf</string>
		<string>Poppins-ThinItalic.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:dentalkart.onelink.me</string>
		<string>applinks:www.dentalkart.com</string>
	</array>


	<key>NSPhotoLibraryUsageDescription</key>
	<string>To upload the image, we need this permission</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>To get the current location for the delivery, we need this permission</string>
	
	<key>LSApplicationQueriesSchemes</key>
    <array>
     	<string>whatsapp</string>
     	<string>mailto</string>
    	<string>instagram</string>
    	<string>instagram-stories</string>
   		<string>fb</string>
    	<string>facebook-stories</string>
    	<string>twitter</string>
    	<string>linkedin</string>
   		<string>pinterest</string>
		  <string>googlechromes</string>
  	</array>
</dict>
</plist>

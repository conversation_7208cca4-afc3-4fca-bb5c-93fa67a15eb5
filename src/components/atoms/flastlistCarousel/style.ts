import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  carouselContainer: {
    position: 'absolute',
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dotStyle: {
    width: Sizes.xms,
    height: Sizes.xm,
    borderRadius: Sizes.sx,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
  },
  flatListContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemContainer: {
    overflow: 'hidden',
  },
});
export default styles;

import {Fonts, Sizes} from 'common';
import {Platform} from 'react-native';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    subContainer: {
      flex: Sizes.x,
    },
    offerSubView: {flexDirection: 'row', paddingHorizontal: Sizes.sx},
    mainModelView: {
      justifyContent: 'center',
      backgroundColor: colors.background,
    },
    referAndEarnView: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    ratingContainer: {
      height: Sizes.x4l * Sizes.xs + Sizes.x,
      borderWidth: Sizes.x,
      borderColor: colors.itemsBorderColor,
      marginHorizontal: Sizes.l,
      flexDirection: 'row',
      justifyContent: 'space-between',
      borderRadius: Sizes.s,
    },
    spreader: {
      backgroundColor: colors.borderSaprater,
      width: Sizes.x,
      height: Sizes.x4l * Sizes.xs + Sizes.x,
    },
    ratingSaprater: {
      backgroundColor: colors.textLight,
      width: Sizes.x,
      height: Sizes.m,
    },

    ratingLabContinuer: {
      width: '47%',
      paddingHorizontal: Sizes.l,
      alignItems: 'flex-start',
      justifyContent: 'center',
    },
    quantityContainer: {
      flexDirection: 'row',
      marginHorizontal: Sizes.m,
      justifyContent: 'space-between',
      width: '80%',
    },
    quantityLabel: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '56%',
      justifyContent: 'space-between',
      paddingLeft: Sizes.s,
    },
    containerOne: {
      width: '53%',
      paddingHorizontal: Sizes.l,
      alignItems: 'flex-end',
      justifyContent: 'center',
    },
    containerDeration: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      width: '100%',
      alignItems: 'center',
    },
    sapraterSec: {
      backgroundColor: colors.borderSaprater,
      height: Sizes.s,
    },
    sapraterSecModel: {
      backgroundColor: colors.smoothGrey,
      height: Sizes.xs,
    },

    qtyText: {color: colors.textLight, fontWeight: '600'},

    horizontalSpace: {marginHorizontal: Sizes.l},

    bulkButton: {
      width: Sizes.ex0 + Sizes.xm,
      height: Sizes.xx4l,
    },

    actionSheetContainer: {
      backgroundColor: colors.topItemsBg,
      borderRadius: Sizes.s,
      marginHorizontal: Sizes.xl,
      justifyContent: 'center',
      width: '90%',
      padding: Sizes.m,
      marginVertical: Sizes.s,
    },
    actionSheetSubContainer: {
      flexDirection: 'row',
      width: '60%',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    spaces: {justifyContent: 'space-between', width: '100%'},
    detailsButton: {
      position: 'absolute',
      justifyContent: 'flex-end',
      borderRadius: Sizes.s,
      right: Sizes.xxl,
      bottom: Sizes.xms,
      backgroundColor: colors.primary,
      paddingHorizontal: Sizes.m,
      zIndex: Sizes.xs,
    },
    label: {
      padding: Sizes.xs,
      fontSize: Sizes.m,
      fontWeight: '400',
      color: colors.text,
    },

    modalView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xl,
      padding: Sizes.l,
      maxHeight: Sizes.ex3l * Sizes.xs + Sizes.ex0,
    },
    pdf: {
      flexDirection: 'row',
      borderWidth: Sizes.x,
      borderColor: colors.lightRedTextColor,
      borderRadius: Sizes.xl,
      padding: Sizes.s,
      width: '65%',
      justifyContent: 'space-around',
      marginHorizontal: Sizes.l,
      alignItems: 'center',
    },
    tagStyle: {
      p: {
        color: colors.text2,
        fontSize: Sizes.mx,
        fontFamily: Fonts.Medium,
        margin: 0,
        padding: 0,
      },
      li: {
        color: colors.text2,
        fontSize: Sizes.mx,
        fontFamily: Fonts.Regular,
      },
      ul: {
        color: colors.text2,
        fontSize: Sizes.mx,
        fontFamily: Fonts.Regular,
        margin: 0,
        paddingLeft: '1em',
      },
      div: {color: colors.text2, fontFamily: Fonts.Medium},
      h1: {color: colors.text2, fontFamily: Fonts.Medium},
      span: {
        color: colors.text2,
        fontSize: Sizes.mx,
        fontFamily: Fonts.Regular,
        margin: 0,
        padding: 0,
      },
      strong: {
        color: colors.text2,
        fontSize: Sizes.mx,
        fontFamily: Fonts.SemiBold,
        margin: 0,
        padding: 0,
      },
      body: {
        whiteSpace: 'normal',
        color: colors.text2,
        fontSize: Sizes.mx,
        fontFamily: Fonts.Regular,
        lineHeight: Sizes.xl ,
      },
      a: {
        color: colors.linkText,
        fontFamily: Fonts.Regular,
      },
      table: { 
        borderWidth: 1, 
        borderColor: colors.border, 
        borderRadius: 4, 
        marginVertical: Sizes.s 
      },
      tr: { 
        flexDirection: 'row', 
        borderBottomWidth: 1, 
        borderColor: colors.border 
      },
      td: {
        padding: Sizes.xs,
        color: colors.text2,
        fontFamily: Fonts.Regular,
        borderRightWidth: 1,
        borderColor: colors.border,
      },
      
      th: {
        padding: Sizes.xs,
        fontFamily: Fonts.SemiBold,
        color: colors.text2,
        backgroundColor: colors.bgSecondary,
        borderRightWidth: 1,
        borderColor: colors.border,
      }
    },
    ratingTag: {alignSelf: 'center'},
    soldOutTag: {backgroundColor: colors.textError, padding: Sizes.xs},
    textWidth: {width: '90%'},
    soldOutLabel: {color: colors.whiteColor},
    imgDeliveryIcon: {
      marginRight: Sizes.s,
    },
    listContainer: {
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
    },
    deliveryCont: {
      flexDirection: 'row',
      backgroundColor: colors.categoryTitle,
      borderTopLeftRadius: Sizes.xm,
      borderTopRightRadius: Sizes.xm,
    },
    listRow: {},
    ContinuerRow: {flexDirection: 'row', alignItems: 'center'},
    soldOutLabels: {color: colors.decrementColor},
    fullSizesModel: {
      backgroundColor: colors.background,
      width: Sizes.x5l,
      height: Sizes.x5l,
      borderRadius: Sizes.x4l,
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'flex-end',
      marginRight: Sizes.s,
    },
    alignEnd: {
      // justifyContent: 'center',
      backgroundColor: colors.newSunnyOrange,
    },
    alignEndIos: {
      backgroundColor: colors.background,
    },
    addBtn: {
      backgroundColor: colors.background,
      borderRadius: Sizes.sx,
    },
    qtyStyle: {
      backgroundColor: colors.background,
      width: Sizes.ex112,
      height: Sizes.x34,
      paddingVertical: 0,
    },
    labelStyle: {
      fontSize: Sizes.m,
    },
    modalCont: {
      backgroundColor: colors.background,
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: '100%',
    },
    continuerActionSheet: {
      backgroundColor: colors.background,
    },
    paddingSpace: {
      paddingBottom: Sizes.s,
    },
    similarCont: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: Sizes.l,
      paddingVertical: Sizes.l,
    },

    addressText: {color: colors.textLight},

    buttonCheck: {width: '30%', height: Sizes.xx4l},
    quantitySelectButton: {height: Sizes.xx4l, width: '50%'},
    pdfImg: {
      width: Sizes.xx,
      height: Sizes.xx,
      paddingHorizontal: Sizes.s,
    },
    linkChild: {
      textDecorationLine: 'underline',
    },
    productImageSmall: {
      width: Sizes.xxl * Sizes.xs,
      height: Sizes.xxl * Sizes.xs,
    },
    loaderStyle: {
      justifyContent: 'center',
      alignSelf: 'center',
      flex: Sizes.x,
    },
    imageContainer: {
      width: Sizes.x4l * Sizes.xs,
      height: Sizes.x4l * Sizes.xs,
      borderWidth: Sizes.x,
      padding: Sizes.xs,
      borderRadius: Sizes.s,
      marginLeft: Sizes.m,
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: colors.itemsBorderColor,
      backgroundColor: colors.background,
    },
    alignmentCenter: {alignItems: 'center', justifyContent: 'center'},
    active: {
      borderColor: colors.border,
    },
    alignCenter: {alignItems: 'center'},
    iconContainer: {
      position: 'absolute',
      zIndex: Sizes.xms,
    },
    sellIcon: {width: Sizes.ex1, height: Sizes.xxl},
    dericationView: {flexDirection: 'row', justifyContent: 'space-around'},
    linkBox: {
      borderWidth: 0.8,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      padding: Sizes.xm,
      borderColor: colors.sunnyOrange,
      backgroundColor: colors.bloodyRed,
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    lineThought: {
      color: colors.grayTextColor,
      textDecorationLine: 'line-through',
    },
    justify: {justifyContent: 'space-between'},
    modelCSS: {width: '100%', backgroundColor: colors.background},
    checkedView: {padding: Sizes.m, paddingBottom: Sizes.l},
    itemCenter: {alignSelf: 'center'},
    closeButton: {height: Sizes.xl, alignSelf: 'center'},
    daysView: {
      justifyContent: 'center',
      width: '94%',
    },
    buttonLabel: {color: colors.primary},
    staricon: {flexDirection: 'row'},
    iconcolor: {color: colors.textError},
    amountSpace: {
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
    tierPrices: {
      marginBottom: Sizes.sx,
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle2,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: Sizes.s,
      paddingHorizontal: Sizes.mx,
    },
    button: {
      paddingVertical: Sizes.xms,
      paddingHorizontal: Sizes.xl,
      backgroundColor: colors.lightGrey1,
      borderRadius: Sizes.sx,
    },
    activeButton: {
      backgroundColor: colors.categoryTitle,
    },
    flexSpacer: {
      flex: Sizes.x,
      justifyContent: 'space-between',
    },
    thRight: {
      flex: Sizes.x,
      height: Sizes.x3l,
      justifyContent: 'center',
      alignItems: 'center',
    },
    thLeft: {
      flex: Sizes.x + Sizes.z,
      height: Sizes.x3l,
      justifyContent: 'center',
      alignItems: 'center',
      borderRightColor: colors.grey2,
      borderRightWidth: Sizes.x,
    },
    referBtnLabel: {
      color: colors.text,
      fontWeight: '500',
      fontSize: Sizes.m,
    },
    referButton: {
      height: Sizes.xl,
    },
    tableItemLeft: {
      flex: Sizes.x + Sizes.z,
      borderTopColor: colors.grey2,
      borderRightColor: colors.grey2,
      borderTopWidth: Sizes.x,
      borderRightWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      padding: Platform.OS === 'ios' ? Sizes.sx : undefined,
    },
    tableItemRight: {
      flex: Sizes.x,
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    itemsEnd: {alignItems: 'flex-end', width: '90%'},
    labelWidth: {width: '90%'},
    tagColor: {color: colors.whiteColor},
    auto: {height: 'auto', marginBottom: Sizes.ex},
    closeView: {width: '100%', backgroundColor: colors.background},
    modelBg: {backgroundColor: colors.background},
    flex: {flex: Sizes.x},
    outStock: {
      borderColor: colors.grey2,
      backgroundColor: colors.orient20,
      borderWidth: Sizes.x,
      height: Sizes.x4l,
      paddingVertical: 0,
    },
    labelBottom: {
      marginBottom: -Sizes.xs,
    },
    left: {marginLeft: Sizes.s},
    paddingSpace1: {padding: Sizes.l},
    decration: {textDecorationLine: 'line-through'},
    actionButton: {position: 'absolute', bottom: 0},
    seprater: {width: Sizes.x + Sizes.s},
    submitButton: {
      backgroundColor: colors.sunnyOrange,
      height: Sizes.x6l,
      width: '50%',
    },
    brandName: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xms,
    },
    cancel: {
      backgroundColor: 'lightgray',
      height: Sizes.x6l,
      width: '50%',
    },
    buttonView: {flexDirection: 'row', width: '100%'},
    row: {flexDirection: 'row'},
    sepraterRed: {backgroundColor: colors.textError},
    copyIcon: {width: Sizes.xl, height: Sizes.xl},
    modalAccordion: {
      paddingTop: Sizes.xm,
      paddingHorizontal: Sizes.xm,
    },
    modalHeader: {
      paddingTop: Sizes.xxl,
      paddingHorizontal: Sizes.xxl,
    },
    modalTextContainer: {
      paddingBottom: Sizes.xxl,
      paddingHorizontal: Sizes.xxl,
    },
    pincodeInput: {
      fontSize: Sizes.l,
      color: colors.text,
      fontWeight: '500',
      flex: Sizes.x,
    },
    checkButton: {
      height: 45,
      justifyContent: 'center',
      alignItems: 'center',
    },
    deliveryStatusView: {
      flexDirection: 'row',
      // flex: Sizes.s,
      alignItems: 'flex-start',
      // justifyContent: 'space-between',
      flexWrap: 'wrap',
    },
    pincodeView: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: Sizes.x,
    },
    offerIcon: {width: '30%'},
    productNameView: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.m,
    },
    subProductNameView: {paddingHorizontal: Sizes.m},
    stickyComponent: {
      paddingHorizontal: Sizes.m,
      position: 'absolute',
      zIndex: 1,
    },
    downloadButtonView: {
      color: colors.newSunnyOrange,
      fontSize: Sizes.m,
      fontWeight: '400',
    },
    transformedTierPriceView: {
      flex: Sizes.x,
      borderColor: colors.sunnyOrange2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.sx,
    },
    optionalView: {
      borderColor: colors.sunnyOrange2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.sx,
    },
    transformedTierView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    childProductView: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.sx,
      marginBottom: Sizes.xm,
      backgroundColor: colors.background,
    },
    subChildProductView: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    maximalPriceView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    expiryDateView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    coinView: {paddingTop: Sizes.m},
    coinTextView: {paddingTop: Sizes.xm, marginTop: Sizes.xs},
    priceCoinView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rightContainer: {
      maxWidth: '40%',
    },
    leftContainer: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      maxWidth: '55%',
    },
    tagLabelfree: {
      color: colors.background,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    childProductSkuView: {
      borderColor: colors.newSunnyOrange,
    },
    textUp: {
      marginTop: -Sizes.xs,
    },
    freeBieButton: {
      // backgroundColor: colors.offerTitleGreen1,
      height: Sizes.xxxl,
      borderRadius: Sizes.sx,
      paddingHorizontal: 0,
      marginBottom: Sizes.s,
      // paddingTop: Sizes.xs,
      justifyContent: 'center',
      alignItems: 'center',
    },
    highlightView: {
      flex: Sizes.x,
      backgroundColor: colors.whiteColor,
      borderColor: colors.darkPink,
      borderWidth: Sizes.x,
      borderRadius: Sizes.l,
    },
    highlightSubView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingTop: Sizes.m,
    },
    featureView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xm,
    },
    productDescriptionView: {
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    productDesView: {
      flexDirection: 'row',
    },
    detailsView: {paddingHorizontal: Sizes.m, paddingVertical: Sizes.xm},
    arrowBottomView: {
      borderBottomLeftRadius: Sizes.l,
      borderBottomRightRadius: Sizes.l,
      flex: Sizes.x,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xm,
      alignItems: 'flex-end',
    },
    dentalKartView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    contentTypeView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    contentTypeMainView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
    },
    contentTypeSubView: {
      alignItems: 'center',
      flex: Sizes.x,
    },
    paymentOptionsView: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.sx,
      paddingVertical: Sizes.xm,
    },
    paymentLabel: {marginHorizontal: Sizes.xs},
    paymentBox: {flexDirection: 'row', flexWrap: 'wrap', alignItems: 'center'},
    paymentOptionsSubView: {
      flexDirection: 'row',
      margin: Sizes.xs,
      borderWidth: Sizes.x,
      borderColor: colors.grey6,
      justifyContent: 'space-evenly',
      paddingVertical: Sizes.s,
      borderRadius: Sizes.s,
      flexGrow: Sizes.x,
      alignItems: 'center',
    },
    feedbackView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      borderBottomWidth: Sizes.x,
      paddingHorizontal: Sizes.m,
      borderTopWidth: Sizes.x,
      borderBottomColor: colors.grey2,
      borderTopColor: colors.grey2,
      paddingVertical: Sizes.xm,
    },
    feedbackSubView: {
      alignItems: 'center',
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      marginHorizontal: Sizes.m,
      padding: Sizes.xms,
    },
    feedbackMainView: {
      borderWidth: Sizes.x,
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.m,
      borderColor: colors.grey5,
      borderRadius: Sizes.m,
      backgroundColor: colors.background,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: Sizes.xs + Sizes.x,
    },
    hideAddToCartView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    buyBothView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
      alignItems: 'center',
    },
    fAQsView: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.mx,
      backgroundColor: colors.background,
      // shadowColor: colors.black,
      marginHorizontal: Sizes.m,
      // shadowOffset: {
      //   width: 0,
      //   height: Sizes.xs,
      // },
      // shadowOpacity: 0.23,
      // shadowRadius: 2.62,
      // elevation: Sizes.xs,
    },
    fAQsMainView: {
      flex: Sizes.x,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    fAQsSubView: {
      flex: Sizes.x,
    },
    deshedView: {
      alignItems: 'flex-end',
    },
    deshedMainView: {
      ...(Platform.OS === 'ios' && {
        padding: Sizes.xms,
      }),

      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.text2,
      borderRadius: Sizes.sx,
      paddingHorizontal: Sizes.m,
    },
    questionView: {
      borderBottomColor: colors.grey2,
      borderBottomWidth: Sizes.x,
      paddingVertical: Sizes.m,
    },
    questionMainView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    questionSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    faqQuestionsView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: Sizes.m,
    },
    noResult: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: Sizes.m,
      color: colors.text2,
    },
    ratingsView: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      backgroundColor: colors.background,
      // shadowColor: colors.black,
      marginHorizontal: Sizes.m,
      // shadowOffset: {
      //   width: 0,
      //   height: Sizes.x,
      // },
      // shadowOpacity: 0.2,
      // shadowRadius: 1.41,
      // elevation: Sizes.xs,
    },
    ratingMainView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-evenly',
    },
    ratingSubView: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    avgRatingView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.s,
      borderRadius: Sizes.s,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      alignItems: 'center',
    },
    rateProductView: {
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
      borderColor: colors.newSunnyOrange,
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.sx,
    },
    underPrizeIconView: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.darkBlue,
      zIndex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    viewSpace: {
      padding: Sizes.m,
    },
    bulkView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingHorizontal: Sizes.xs,
    },
    fqbtIcon: {
      right: Sizes.xx4l,
      alignSelf: 'flex-start',
    },
    fqbtBtn: {paddingHorizontal: Sizes.l, paddingBottom: Sizes.l},
    ratingView: {
      paddingVertical: Sizes.m,
    },
    underPrizeSubView: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.darkBlue,
      zIndex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    totalReview: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: Sizes.m,
    },
    shippingBoxView: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
    },
    brandTagView: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
    },
    brandTagSubView: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
    },
    renderMultiView: {flexDirection: 'row', paddingVertical: Sizes.xs},
    disView: {
      position: 'absolute',
      zIndex: Sizes.xs,
      alignSelf: 'center',
    },
    tagLabel: {
      color: colors.text2,
    },
    flexContainer: {flex: Sizes.x},
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',

      backgroundColor: colors.modalShadow,
    },
    modalCloseBtnContainer: {
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    highlistGradient: {transform: [{rotate: '270deg'}]},
    aboutProductWidth: {width: '70%'},
    feedView: {justifyContent: 'center', flexDirection: 'row'},
    offerZoneBannerView: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      flex: Sizes.x,
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
    },
    viewProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.sx,
    },
    viewSubProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.s + Sizes.x,
    },
    postQuestionView: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.s,
    },
    bankView: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.x,
    },

    searchsubView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      height: Sizes.x6l,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.xms,
      borderColor: colors.grey2,
      backgroundColor: colors.background,
    },
    cross: {alignSelf: 'flex-end'},
    textInputView: {
      color: colors.text2,
      flex: Sizes.x,
      fontWeight: '500',
      fontSize: Sizes.mx,
      // textTransform: 'capitalize',
    },
    removeModalSubView: {
      justifyContent: 'center',
      flexDirection: 'row',
      borderRadius: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
      padding: Sizes.xms,
    },
    mamberShipView: {
      borderRadius: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    removeImageView: {width: Sizes.ex, height: Sizes.exl},
    input: {width: '86%', borderRadius: Sizes.xms, color: colors.text2},
    subInput: {
      width: '80%',
      borderRadius: Sizes.m,
      color: colors.text,
      borderColor: colors.placeholderColor,
    },
    searchIconStyle: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    inputBorderStyle: {
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      backgroundColor: colors.background,
      borderColor: colors.placeholderColor,
    },
    inputPostQuestionStyle: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      height: Sizes.ex1 + Sizes.xl,
      borderColor: colors.text2,
    },
    backgroundImage: {
      resizeMode: 'cover',
      justifyContent: 'center',
    },
    sellerView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: Sizes.l,
    },
    listDataView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingBottom: Sizes.xxl,
    },
    modalHeaderView: {
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
    },
    width: {width: '100%'},
    categorySubView: {
      alignItems: 'center',
      padding: Sizes.sx,
    },
    categoryTitleView: {
      width: Sizes.ex82,
      height: Sizes.ex,
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x + Sizes.z,
      borderColor: colors.grey2,
    },
    offerView: {backgroundColor: colors.background, height: Sizes.ex0},
    offerMainView: {backgroundColor: colors.lightGreen, height: Sizes.exl},

    searchIconView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xx,
      // backgroundColor: colors.background2,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      borderColor: colors.background2,
    },
    flexRow: {
      flexDirection: 'row',
    },
    flexEnd: {alignSelf: 'flex-end', alignItems: 'baseline'},
    freeBieView: {
      flexShrink: Sizes.x,
      alignItems: 'flex-end',
      justifyContent: 'flex-end',
    },
    earnView: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    subEarnView: {
      alignSelf: 'flex-start',
      paddingHorizontal: Sizes.x,
      flexDirection: 'row',
      maxWidth: '35%',
      alignItems: 'center',
    },
    childName: {
      maxWidth: '96%',
    },
    priceView: {
      flexDirection: 'row',
      alignItems: 'baseline',
    },
    basePriceView: {
      flexDirection: 'row',
      alignItems: 'baseline',
      gap: 5,
    },
    alignCoin: {paddingTop: Sizes.s},
    priceCoinViews: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    priceLabel: {
      alignItems: 'baseline',
      flexDirection: 'row',
    },
    starIconView: {
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.s,
    },
    nicNameView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    renderHtml: {paddingTop: Sizes.xm},
    descriptionKey: {
      padding: Sizes.m,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    descriptionKeyView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
      marginHorizontal: -Sizes.m,
    },
    arrowStyle: {
      marginTop: Sizes.s,
    },
    description: {
      borderColor: colors.grey2,
      borderTopWidth: Sizes.x,
    },
    descText: {
      color: colors.text2,
      fontFamily: Fonts.Regular,
    },
    tags: {
      span: {color: colors.textLight, fontSize: Sizes.mx},
      p: {color: colors.text2, fontSize: Sizes.mx},
      h3: {
        color: colors.textLight,
        fontSize: Sizes.mx,
      },
      strong: {
        color: colors.text2,
        fontWeight: 500,
        fontSize: Sizes.mx,
      },
      ul: {
        color: colors.text2,
        fontSize: Sizes.m,
        fontFamily: Fonts.Regular,
        margin: 0,
      },
      li: {
        color: colors.text2,
        fontSize: Sizes.mx,
        fontWeight: 400,
        // paddingVertical: Sizes.xs,
        // textAlignVertical: 'center',
      },
      td: {color: colors.textLight},
    },
    LikeDisView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectView: {
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.xm,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
    },
    poppinsView: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontWeight: '500',
      fontFamily: 'Poppins-Regular',
    },
    poppinSubView: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontWeight: '500',
      fontFamily: 'Poppins-Regular',
    },
    styleDropView: {
      paddingVertical: Sizes.xl,
      paddingLeft: Sizes.m,
      paddingRight: Sizes.xm,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
    },
    styleDropSubView: {
      width: Sizes.xxxl,
      height: Sizes.xxxl,
      tintColor: colors.grey,
    },
    itemContainerView: {
      paddingHorizontal: Sizes.xm,
      borderRadius: Sizes.xm,
    },
    rateView: {
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
    },
    infoBottomIcon: {marginBottom: Sizes.xs},

    rateSubView: {flexDirection: 'row', alignItems: 'center'},
    searchByNameInput: {
      width: '80%',
      fontFamily: 'Poppins-Regular',
      color: colors.text2,
    },
    valuableView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    mamberShipCardImageView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    submitView: {
      justifyContent: 'flex-start',
      alignSelf: 'flex-start',
    },
    overallView: {
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    cancelView: {
      justifyContent: 'space-around',
      flexDirection: 'row',
    },
    answerView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    referView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xxl,
    },
    emiPlansView: {
      backgroundColor: colors.background,
      flex: Sizes.xs,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    emiPlansSubView: {
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      justifyContent: 'space-around',
      flexDirection: 'row',
      backgroundColor: colors.background3,
      padding: Sizes.m,
      alignItems: 'center',
    },
    creditCardView: {
      flexDirection: 'row',
      backgroundColor: colors.background3,
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.m,
    },
    creditCardSubView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: Sizes.l,
    },
    arrowRightView: {
      flexDirection: 'row',
      borderBottomWidth: Sizes.x,
      borderColor: colors.text2,
      padding: Sizes.xm,
      paddingVertical: Sizes.mx,
    },
    arrowRightSubView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xm,
    },
    enterView: {
      paddingLeft: Sizes.xl,
      paddingHorizontal: Sizes.xms,
    },
    debitView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: Sizes.xms,
    },
    cardlessView: {
      paddingHorizontal: Sizes.xms,
      paddingVertical: Sizes.m,
    },
    stateView: {
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.x3l,
    },
    payLaterDataView: {
      flexDirection: 'row',
      borderBottomWidth: Sizes.x,
      borderColor: colors.text2,
      padding: Sizes.xm,
      paddingVertical: Sizes.xsl,
    },
    payLaterSubDataView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xm,
      flexDirection: 'row',
    },
    referralLink: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'center',
    },
    newCopy: {width: Sizes.xxl, height: Sizes.xxl},
    underText: {textDecorationLine: 'underline'},
    scrollViewStyle: {
      marginTop: 'auto',
    },
    countryButton: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderRadius: Sizes.sx,
      paddingHorizontal: Sizes.m,
      height: Platform.OS === 'ios' ? Sizes.x7l : undefined,
    },
    paymentView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      flexShrink: Sizes.x,
    },
    centeredRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    flexShrink: {
      flexShrink: Sizes.x,
    },
    offerImage: {
      width: Sizes.xxl,
      height: Sizes.xxl,
    },
    fqbtCheckBox: {position: 'absolute', zIndex: Sizes.xs, right: 0},
    wishlistIcon: {
      position: 'absolute',
      zIndex: Sizes.xs,
      right: 0,
      marginBottom: Sizes.s,
    },
    similarImage: {
      borderRadius: Sizes.x8l,
      height: Sizes.xxl,
      width: Sizes.xxl,
      right: Sizes.xm,
      backgroundColor: colors.heartBG,
    },
    defaultImage: {
      height: Sizes.xxl,
      width: Sizes.xxl,
      right: Sizes.xm,
    },
    doubleFlex: {
      flex: Sizes.xs,
    },
    deliveryDetails: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      padding: Sizes.m,
      width: checkDevice() ? '70%' : '100%',
    },
    deliveryInfoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    imgLeft: {marginLeft: Sizes.s},
    emptyImage: {
      width: Sizes.ex112,
      height: Sizes.xxxl,
      borderRadius: Sizes.sx,
    },
    editIconGif: {
      height: Sizes.xx,
      width: Sizes.xsl,
      opacity: 0.45,
    },
    topIconView: {
      position: 'absolute',
      height: Sizes.xxxl,
      borderRadius: Sizes.x3l,
      backgroundColor: colors.black2,
      paddingHorizontal: Sizes.xms,
      elevation: Sizes.s,
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
      marginTop: Sizes.xms,
      flexDirection: 'row',
    },
    topIconSunView: {
      padding: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkBoxRadius: {borderRadius: Sizes.xxxl},
    simpleGifView: {
      width: Sizes.x3l,
      height: Sizes.x3l,
    },
    singleTireMainView: {
      height: Sizes.x7l,
      width: '100%',
      backgroundColor: colors.silkBlue,
      borderTopRightRadius: Sizes.xl,
      borderTopLeftRadius: Sizes.xl,
      padding: Sizes.s,
      paddingBottom: Sizes.s,
    },
    singleTireSubView: {flexDirection: 'row'},
    filledStarIcon: {marginBottom: Sizes.s},
    backgroundImageContainer: { 
      width: '100%', 
      paddingTop: Sizes.x26, 
      paddingBottom: Sizes.x26
    },
    recommendedContainer: { 
      left: 46, 
      top: -20 
    },
    recommenedButtonView: {
      flexDirection: 'row',
      paddingLeft: Sizes.s,
    },
    btnStyle: {
      height: Sizes.x4l + Sizes.x,
      paddingVertical: 0,
    },
    btnTxt: {
      marginBottom: -Sizes.xs,
    },
    suggestedView: {
      width: Sizes.screenWidth * 0.7,
      alignSelf: 'center',
      alignItems: 'center',
    },
    qtytextStyle: {
      height: Sizes.x34,
      borderWidth: 1,
      color: colors.newSunnyOrange,
      borderColor: colors.newSunnyOrange,
      backgroundColor: colors.background,
    },
    notifyIcon: {
      height: Sizes.xl,
      width: Sizes.xl,
      marginLeft: Sizes.xms,
    },
    separatorStyle: {
      borderRadius: Sizes.xs,
    },
    footer: {
      ...(Platform.OS === 'ios'
        ? {
            paddingTop: Sizes.xl,
            paddingHorizontal: Sizes.m,
            paddingBottom: Sizes.x6l,
          }
        : {
            paddingTop: Sizes.xl,
            paddingHorizontal: Sizes.m,
            paddingBottom: Sizes.xms,
          }),
    },
    addWidth: {width: Sizes.x4l},
    searchLine: {
      backgroundColor: colors.grey2,
      width: Sizes.x,
      height: Sizes.xl,
    },
    addBtnStyle: {
      backgroundColor: colors.background,
      width: Sizes.ex110,
      height: Sizes.x34,
      paddingVertical: 0,
    },
    notifyBtnStyle: {
      width: Sizes.ex134,
      height: Sizes.x34,
      paddingVertical: 0,
    },
    goTop: {
      marginBottom: -Sizes.xs,
    },
    listContainer2: {
      flex: Sizes.x,
      // paddingHorizontal: Sizes.xm,
    },
    serviceView: {
      justifyContent: 'space-between',
      gap: 2,
    },
    startView: {
      alignItems: 'flex-start',
    },
  });
export default styles;

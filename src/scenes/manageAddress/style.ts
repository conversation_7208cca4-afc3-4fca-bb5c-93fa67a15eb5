import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    headerStyle: {
      borderBottomColor: colors.grey2,
      borderBottomWidth: Sizes.x,
    },
    boxDirections: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    btnView: {
      width: '100%',
      borderRadius: Sizes.m,
      height: Sizes.x7l,
    },
    btnColor: {
      backgroundColor: colors.newPrimary,
    },
    btnContainer: {
      shadowColor: colors.black,
      shadowOffset: {
        width: 2,
        height: 2,
      },
      elevation: Sizes.s,
      backgroundColor: colors.whiteColor,
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.l,
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
    },
    addressInput: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      color: colors.text2,
      height: Sizes.exl,
      textAlignVertical: 'top',
      paddingBottom: Sizes.s,
      paddingTop: Sizes.xms,
    },
    addressMainView: {
      // borderRadius: Sizes.xm,
      // borderColor: colors.grey2,
      // borderWidth: Sizes.x,
      height: Sizes.exl,
    },
    septate: {
      backgroundColor: colors.grey7,
      height: Sizes.xm,
    },
    mainView: {
      paddingHorizontal: Sizes.l,
    },
    buttonContinuer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    textMain: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      color: colors.text2,
      marginTop: Sizes.xs,
      height: Sizes.x44,
    },
    input: {
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      height: Sizes.x7l,
    },
    styleDropDown: {
      height: Sizes.x7l,
      color: colors.grey2,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      fontFamily: Sizes.mx,
    },
    dropDownTextStyle: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    lineCut: {
      height: Sizes.x,
      backgroundColor: colors.grey2,
      flex: Sizes.x,
    },
    list: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: Sizes.m,
    },
    addNewTxt: {
      marginBottom: -Sizes.xs,
    },
    textCap: {
      textTransform: 'capitalize',
    },
    btnTxt: {
      fontFamily: Fonts.Medium,
    },
    fOne: {
      flex: Sizes.x,
    },
    rowView: {
      flexDirection: 'row',
    },
    defaultView: {
      width: Sizes.ex160,
      alignSelf: 'flex-end',
    },
    btnTabView: {
      width: Sizes.ex250,
      alignSelf: 'flex-end',
    },
    regionTab: {
      marginTop: -Sizes.sx,
    },
    downImg: {
      transform: [{rotate: '180deg'}],
    },
    dropDownItem: {
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.sx,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    allCenter: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    toggleOff: {
      backgroundColor: colors.text2,
      margin: 0,
      padding: 0,
      height: Sizes.xsl,
      width: Sizes.xsl,
      borderRadius: Sizes.x8l,
    },
    toggleOn: {
      height: Sizes.xsl,
      width: Sizes.xsl,
      borderRadius: Sizes.x8l,
    },
    toggleView: {
      shadowColor: colors.black25,
      shadowOpacity: 0.2,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowRadius: Sizes.s,
      elevation: Sizes.xs,
    },
    itemSelected: {
      backgroundColor: colors.text,
    },
    tagListView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    tagView: {
      borderColor: colors.categoryTitle,
      borderWidth: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.sx,
      paddingVertical: Sizes.sx,
      paddingHorizontal: Sizes.m,
    },
    addressCardView: {
      backgroundColor: colors.whiteBlue,
      padding: Sizes.s,
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.sx,
    },
    errorStyle: {
      marginTop: Sizes.s,
    },
    labelTxt: {
      height: Sizes.l,
      marginVertical: Sizes.xs,
    },
    inputContent: {
      maxHeight: Sizes.x78,
      marginTop: Sizes.s,
      paddingTop: 4,
    },
    inputBoxStyle: {
      height: Sizes.x7l,
    },
    inputStyle: {
      textAlignVertical: 'top',
      backgroundColor: colors.whiteColor,
      paddingBottom: 0,
    },
    closeIcons: {
      position: 'absolute',
      top: Sizes.x,
      right: -5,
      zIndex: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    dropDownItemView: {
      marginBottom: Sizes.xms,
    },
    errorBorder: {
      borderColor: colors.red3,
      borderWidth: Sizes.x,
    },
  });

export default styles;

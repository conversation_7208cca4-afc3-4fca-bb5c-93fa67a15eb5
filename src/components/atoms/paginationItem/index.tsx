// import React, {useRef} from 'react';
// import {View, Animated} from 'react-native';
// import {useTheme} from '@react-navigation/native';
// import {Sizes} from 'common';

// import stylesWithOutColor from './style';

// const PaginationItem: React.FC<{
//   index: number;
//   activeIndex: number;
//   paginationType: 'normal' | 'capsule';
//   opacity: number;
// }> = ({index, activeIndex, paginationType, opacity = 1}) => {
//   const {colors} = useTheme();
//   const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
//   const size = Sizes.sx;
//   const width = Sizes.xm;

//   const inactiveColor = colors.categoryTitle;
//   const activeColor = colors.categoryTitle;

//   const colorAnim = useRef(new Animated.Value(0)).current;

//   const dotColor = colorAnim.interpolate({
//     inputRange: [0, 1],
//     outputRange: [inactiveColor, activeColor],
//   });
//   const activeWidth = index === activeIndex ? Sizes.xl : size;
//   return (
//     <>
//       {paginationType === 'capsule' ? (
//         <View style={styles.subView}>
//           <Animated.View
//             style={{
//               height: size,
//               width: activeWidth,
//               borderRadius: activeWidth / Sizes.xs,
//               backgroundColor: activeColor,
//             }}
//           />
//           <Animated.View
//             style={{
//               position: 'absolute',
//               width: size,
//               height: size,
//               borderRadius: size / Sizes.xs,
//               backgroundColor: dotColor,
//             }}
//           />
//         </View>
//       ) : (
//         <View
//           style={[styles.container, {width, height: width, opacity}]}
//           key={index?.toString()}
//         />
//       )}
//     </>
//   );
// };

// export default PaginationItem;

// ============================================

import React, {useRef, useEffect} from 'react';
import {View, Animated} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';
import {useMemo} from 'react';

import stylesWithOutColor from './style';

const PaginationItem: React.FC<{
  index: number;
  activeIndex: number;
  paginationType: 'normal' | 'capsule';
  opacity: number;
  customDotColor?: object;
  progress: number;
  paginationSize: boolean;
  pdpImageSliderDots: boolean;
  dotsLength: number;
}> = ({
  index,
  activeIndex,
  paginationType,
  opacity = 1,
  progress,
  customDotColor,
  paginationSize,
  pdpImageSliderDots,
  dotsLength,
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const size = paginationSize ? Sizes.s : Sizes.sx;
  const width = paginationSize ? Sizes.s : Sizes.xm;
  const inactiveColor = !!customDotColor
    ? customDotColor?.inActive
    : colors.categoryTitle;
  const activeColor = !!customDotColor
    ? customDotColor?.active
    : colors.categoryTitle;

  const colorAnim = useRef(
    new Animated.Value(index === activeIndex ? 1 : 0),
  ).current;
  const widthAnim = useRef(
    new Animated.Value(index === activeIndex ? Sizes.xl : size),
  ).current;

  // Progressive animation effect
  useEffect(() => {
    // Run animations in parallel
    Animated.parallel([
      Animated.timing(colorAnim, {
        toValue: index === activeIndex ? 1 : 0,
        duration: 200, // Adjust duration for smooth progressive effect
        useNativeDriver: false,
      }),
      Animated.timing(widthAnim, {
        toValue: index === activeIndex ? Sizes.xl : size,
        duration: 200, // Same duration to keep color and width in sync
        useNativeDriver: false,
      }),
    ]).start();
  }, [activeIndex, index]);

  const dotColor = colorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [inactiveColor, activeColor],
  });
  return (
    <>
      {paginationType === 'capsule' ? (
        <View style={styles.subView}>
          <Animated.View
            style={{
              height: size,
              width: widthAnim, // Apply width animation
              borderRadius: widthAnim.interpolate({
                inputRange: [size, Sizes.xl],
                outputRange: [size / 2, Sizes.xl / 2],
              }),
              backgroundColor: dotColor, // Apply color animation
            }}
          />
        </View>
      ) : (
        dotsLength>1?
      (  <View
          style={[pdpImageSliderDots? styles.pdpContainer: styles.container, {width, height: width, opacity}]}
          key={index?.toString()}
        />): <View></View>
      )}
    </>
  );
};

export default PaginationItem;

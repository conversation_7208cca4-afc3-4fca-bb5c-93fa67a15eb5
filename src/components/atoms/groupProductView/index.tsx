import React, {useMemo} from 'react';
import {View, TouchableOpacity, TextInput, FlatList} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {
  Label,
  ImageIcon,
  Spacer,
  Separator,
  Tag,
  Quantity,
} from 'components/atoms';
import stylesWithOutColor from './style';
import {Button} from 'components/molecules';
import {t} from 'i18next';

type Props = {
  groupProduct: ChildProduct[];
  product: ProductData | null;
  onReferralRecord: (sku: string, url: string) => void;
  checkPinShipping: {
    [key: string]: string;
  };
  availablePaymentMethod: PaymentMethodResponseV2 | undefined;
  infoClick: () => void;
  updateCart: (count: number, productPrice: number, sku: string) => void;
  selectedGroupProducts: SelectedGroupProducts[] | any;
  qty: number | any;
};

const GroupProductView = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {
    groupProduct,
    product,
    onReferralRecord,
    checkPinShipping,
    availablePaymentMethod,
    infoClick,
    updateCart,
    selectedGroupProducts,
    qty,
  } = props;

  const delivery = useMemo(() => {
    const days = availablePaymentMethod?.delivery_days?.find(
      (item: any) => item?.product_id === product?.id,
    );
    const cod = availablePaymentMethod?.checkcod?.find(
      (item: any) => item?.product_id === product?.id,
    );
    return {days, cod};
  }, [availablePaymentMethod, product]);

  return (
    <>
      <Spacer size="l" />
      <View style={styles.searchIconView}>
        <View style={styles.searchSubView}>
          <ImageIcon icon={'searchIcon'} tintColor="text2" />
          <Spacer size="xm" type="Horizontal" />
          <Separator
            thickness="x"
            color="placeholderColor"
            Vertical={true}
            height="xl"
          />
          <Spacer size="xm" type="Horizontal" />
          <TextInput testID="txtGroupProductSearchByName"
            placeholderTextColor={colors.text2}
            keyboardType="name-phone-pad"
            placeholder={t('PDP.searchByName')}
            onChangeText={() => {}}
            style={styles.textInputView}
            allowFontScaling={false}
          />
        </View>
        <Spacer size="sx" />
        {groupProduct?.length > 0 && product?.type === 'grouped' ? (
          <FlatList
            data={groupProduct}
            extraData={groupProduct}
            renderItem={({item, index}) => {
              return (
                <View key={index.toString()} style={styles.childProductView}>
                  <View style={styles.childView}>
                    <Label
                      fontFamily="SemiBold"
                      numberOfLines={2}
                      ellipsizeMode={'tail'}
                      color="text"
                      size="mx"
                      text={item?.name}
                      style={styles.flex}
                    />
                    <TouchableOpacity
                    testID='tOGroupProductOnReferralRecord'
                      onPress={() => onReferralRecord(item?.sku, item?.url_key)}
                      style={styles.earnView}>
                      <Label
                        fontFamily="Medium"
                        color="silkBlue"
                        size="mx"
                        text={t('PDP.referEarn')}
                      />
                      <Spacer type="Horizontal" size="xm" />
                      <ImageIcon
                        size="xxl"
                        icon="announcement"
                        tintColor="silkBlue"
                      />
                    </TouchableOpacity>
                  </View>
                  <Spacer size="s" />
                  <View style={styles.maximalPriceView}>
                    <View style={styles.amountView}>
                      <Label
                        fontFamily="SemiBold"
                        color="text"
                        size="xl"
                        text={`₹${item?.pricing?.selling_price}`}
                      />
                      <Spacer size="xm" type="Horizontal" />
                      <Label
                        fontFamily="Medium"
                        textDecorationLine="line-through"
                        color="grey3"
                        size="mx"
                        text={item?.pricing?.price}
                      />
                      <Spacer size="xm" type="Horizontal" />
                      {item?.pricing?.discount?.label &&
                        !item?.pricing?.discount?.label?.startsWith('0') && (
                          <Label
                            fontFamily="Medium"
                            color="green2"
                            size="mx"
                            text={item?.pricing?.discount?.label}
                          />
                        )}
                    </View>
                    {/* {!item?.is_in_stock && (
                      <Tag
                        labelStyle={styles.soldOutLabel}
                        style={styles.soldOutTag}
                        label={t('PDP.soldOut')}
                      />
                    )} */}
                  </View>
                  {item?.attributes?.reward_points &&
                    Number(item?.attributes?.reward_points) > 0 && (
                      <>
                        <Spacer size="s" />
                        <View style={styles.centerItem}>
                          <ImageIcon icon="coin" size="xx" />
                          <Spacer size="xm" type="Horizontal" />
                          <Label
                            fontFamily="Medium"
                            color="orange"
                            size="mx"
                            text={`${item?.attributes?.reward_points}`}
                          />
                        </View>
                      </>
                    )}
                  <Spacer size="s" />
                  <View style={styles.expiryDateView}>
                    <Label
                      fontFamily="Medium"
                      color="categoryTitle"
                      size="m"
                      text={`${t('PDP.expiryDate')} : `}
                    />
                    <Label
                      fontFamily="Medium"
                      color="text2"
                      size="m"
                      text={item?.attributes?.expiry_date?.label}
                    />
                    <Spacer size="s" type="Horizontal" />
                    <TouchableOpacity testID='tOGroupProductViewInfo' style={styles.infoIcon}>
                      <ImageIcon
                        tintColor="lightGray"
                        size="l"
                        icon="informationIcon"
                      />
                    </TouchableOpacity>
                  </View>
                  <View style={styles.fRow}>
                    <View style={styles.expiryView}>
                      <View style={styles.expiryDateView}>
                        <ImageIcon
                          size="xxl"
                          tintColor="categoryTitle"
                          icon="deliveryTruckIcon"
                        />
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          color="categoryTitle"
                          fontFamily="Medium"
                          size="mx"
                          text={
                            checkPinShipping?.delivery_info?.[0]
                              ?.delivery_days?.[0]?.message
                          }
                        />
                      </View>

                      <View style={styles.expiryDateView}>
                        <ImageIcon
                          size="xxl"
                          tintColor="newSunnyOrange"
                          icon="creditIcon"
                        />
                        <Spacer size="xm" type="Horizontal" />

                        <Label
                          color="newSunnyOrange"
                          fontFamily="Medium"
                          size="mx"
                          text={`${
                            checkPinShipping?.service_availability?.find(
                              (service: any) =>
                                service.product_id === item?.product_id,
                            )?.message
                          }`}
                        />
                      </View>
                      <Spacer size="s" />
                    </View>
                    <View style={styles.expireEndView}>
                      {item?.is_in_stock ? (
                        <View>
                          {!selectedGroupProducts.find(
                            child => child?.data?.sku === item?.sku,
                          ) ? (
                            <Button
                              type="bordered"
                              onPress={() =>
                                updateCart(
                                  1,
                                  item?.pricing?.selling_price,
                                  item?.sku,
                                )
                              }
                              text={t('buttons.add').toUpperCase()}
                              radius="sx"
                              size="extra-small"
                              selfAlign="stretch"
                              borderColor={'newSunnyOrange'}
                              labelColor={'newSunnyOrange'}
                              paddingHorizontal="x6l"
                            />
                          ) : (
                            <Quantity
                              min={0}
                              max={item?.inventory?.max_sale_qty}
                              value={qty}
                              onUpdate={count => {
                                updateCart(
                                  count,
                                  item?.pricing?.selling_price,
                                  item?.sku,
                                );
                              }}
                              backgroundColor="newSunnyOrange"
                            />
                          )}
                        </View>
                      ) : null}
                    </View>
                  </View>
                  {availablePaymentMethod ? (
                    <View>
                      {!!delivery?.days?.delivery_days && (
                        <View style={styles.deliveryView}>
                          <ImageIcon
                            size="xxl"
                            tintColor="categoryTitle"
                            icon="deliveryTruckIcon"
                          />
                          <Spacer size="xm" type="Horizontal" />
                          <Label
                            color="categoryTitle"
                            size="m"
                            fontFamily="Regular"
                            text={delivery?.days?.success_msg}
                          />
                        </View>
                      )}
                      <Spacer size="s" />
                      {!!delivery?.cod?.message && (
                        <View style={styles.deliveryView}>
                          <ImageIcon
                            size="xxl"
                            tintColor="newSunnyOrange"
                            icon="creditIcon"
                          />
                          <Spacer size="xm" type="Horizontal" />
                          <Label
                            color="newSunnyOrange"
                            fontFamily="Regular"
                            size="m"
                            text={delivery?.cod?.message}
                          />
                          {delivery?.cod?.message_arr?.length > 0 && (
                            <TouchableOpacity
                            testID='tOGroupProductViewInfoClick'
                              onPress={() => infoClick()}
                              style={styles.left}>
                              <ImageIcon
                                tintColor="lightGray"
                                size="l"
                                style={styles.imgDeliveryIcon}
                                icon="informationIcon"
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )}
                    </View>
                  ) : null}
                </View>
              );
            }}
            keyExtractor={(item, index) => index.toString()}
          />
        ) : null}
      </View>
    </>
  );
};

export default GroupProductView;

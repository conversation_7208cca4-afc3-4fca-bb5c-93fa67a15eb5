import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    saveButton: {fontSize: Sizes.l, fontWeight: '700'},
    button: {backgroundColor: colors.textError},
    titleView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: Sizes.xl,
    },
    modelContainer: {
      width: '90%',
      alignSelf: 'center',
    },
    modelInputBox: {
      height: Sizes.x6l,
      color: colors.grey,
    },
    modelBtn: {
      height: Sizes.x7l,
      width: '100%',
      marginTop: Sizes.xms,
      alignSelf: 'center',
      borderRadius: Sizes.xms,
      backgroundColor: colors.categoryTitle,
    },
    modelBtnView: {
      paddingVertical: Sizes.xl,
    },
    otpInput: {
      borderColor: colors.border,
      borderRadius: Sizes.s,
      color: colors.text,
      fontSize: Sizes.xl,
      fontWeight: '700',
      width: Sizes.x7l + Sizes.xs,
      height: Sizes.x7l + Sizes.xs,
      backgroundColor: colors.background,
    },
    horizontalSpace: {paddingHorizontal: Sizes.l},
    buttonText: {color: colors.whiteColor},
    otpText: {color: colors.textLight},
    otpInputContainer: {
      width: '100%',
      height: Sizes.x7l + Sizes.xs,
    },
    otpView: {height: Sizes.exl},
    updateEmailLabel: {
      backgroundColor: colors.gray10,
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
    },
    subContainer: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.l,
    },
    inputView: {
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.grey2,
      height: Sizes.x46,
    },
    inputTextView: {color: colors.categoryTitle},
    subCautionerBox: {
      width: '100%',
      borderRadius: Sizes.sx,
      marginBottom: Sizes.xms,
      height: Sizes.x46,
    },
  });

export default styles;

import localStorage from 'utils/localStorage';

export const getCartId = async () => {
  const cartId = await localStorage.get('cart_id');
  return cartId;
};

export const setCartId = async (cartId: string) => {
  await localStorage.set('cart_id', cartId);
};

export const removeCartId = async () => {
  await localStorage.set('cart_id', '');
};

export const guestCartId = async () => {
  const cartId = await localStorage.get('guest_cart_id');
  return cartId;
};

export const customerCartId = async () => {
  const cartId = await localStorage.get('customer_cart_id');
  return cartId;
};

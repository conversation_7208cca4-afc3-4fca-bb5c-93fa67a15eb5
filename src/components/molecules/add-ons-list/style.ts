import {StyleSheet} from 'react-native';
import {Sizes} from 'common';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: Sizes.m,
      borderWidth: Sizes.x,
      borderColor: colors.border,
      borderRadius: Sizes.xm,
    },
    leftContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    checkBoxContainer: {
      width: Sizes.l,
      height: Sizes.l,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
    },
    checkBoxActive: {
      borderColor: colors.primary,
    },
  });

export default styles;

import Api from 'services';
import {apiRoute, apiEndpoints} from 'config/apiEndpoint';

const API = new Api();

export const getAllBrands = async (queryParams: string) => {
  const url = `${apiRoute.interface}${apiEndpoints.brands}${queryParams}`;
  const resp = await API.get(url);
  return resp;
};
export const BrandsCarousel = async () => {
  const url = `${apiRoute.interface}${
    apiEndpoints.brands
  }${'?section-layout=section_brands'}`;
  const resp = await API.get(url);
  return resp;
};

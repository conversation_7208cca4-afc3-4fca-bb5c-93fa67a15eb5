import {Fonts, Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      top: Sizes.xms,
      left: Sizes.xms,
      right: Sizes.xms,
      zIndex: Sizes.m + Sizes.x,
    },
    textMain: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      color: colors.text2,
      height: Sizes.x44,
      marginTop: Sizes.xs,
    },
    input: {
      height: Sizes.x7l,
      borderWidth: 0,
      paddingRight: Sizes.xs,
    },
    searchHeight: {
      height: Sizes.x8l,
    },
    searchInput: {
      flexDirection: 'row',
      borderRadius: Sizes.m,
      borderColor: colors.text,
      borderWidth: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    listStyle: {
      backgroundColor: colors.offWhite,
      maxHeight: Sizes.ex210,
      paddingHorizontal: Sizes.m,
      shadowColor: colors.text,
      shadowOffset: {
        width: 0,
        height: 3,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4.65,
      elevation: Sizes.xs,
    },
    fOne: {
      flex: Sizes.x,
    },
    closeIcon: {
      padding: Sizes.sx,
      marginRight: Sizes.sx,
      marginTop: Sizes.m,
    },
    mTop: {
      paddingTop: Sizes.m,
    },
    mBottom: {
      paddingBottom: Sizes.m,
    },
    searchIcon: {
      marginTop: Sizes.m,
    },
    placeText: {
      placeholderTextColor: colors.text2,
    },
    mapItemList: {
      backgroundColor: colors.offWhite,
      marginLeft: -35,
      borderRadius: Sizes.m,
      paddingRight: Sizes.xs,
      marginBottom: 3,
    },
    mapItemRow: {
      backgroundColor: colors.offWhite,
      padding: 0,
      marginHorizontal: 5,
      flexDirection: 'row',
    },
    mapItemLine: {
      height: 0,
      backgroundColor: colors.whiteColor,
    },
  });

export default styles;

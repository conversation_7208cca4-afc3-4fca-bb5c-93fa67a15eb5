import { AppEventsLogger } from 'react-native-fbsdk-next';

/**
 * Logs an AddToCart event to Facebook
 */
export const logAddToCart = (
  contentId: string,
  contentType: string,
  currency: string,
  value: number,
  params: Record<string, any> = {}
) => {
  AppEventsLogger.logEvent('AddToCart', {
    fb_content_id: contentId,
    fb_content_type: contentType,
    fb_currency: currency,
    fb_value: value,
    ...params,
  });
};

/**
 * Logs an AddToWishlist event to Facebook
 */
export const logAddToWishlist = (
  contentId: string,
  contentType: string,
  currency: string,
  value: number,
  params: Record<string, any> = {}
) => {
  AppEventsLogger.logEvent('AddToWishlist', {
    fb_content_id: contentId,
    fb_content_type: contentType,
    fb_currency: currency,
    fb_value: value,
    ...params,
  });
};

/**
 * Logs a CompleteRegistration event
 */
export const logCompleteRegistration = (
  registrationMethod: string,
  params: Record<string, any> = {}
) => {
  AppEventsLogger.logEvent('CompleteRegistration', {
    fb_registration_method: registrationMethod,
    ...params,
  });
};

/**
 * Logs an InitiateCheckout event
 */
export const logInitiateCheckout = (
  // contentIds: string[],
  // contentType: string,
  // currency: string,
  // value: number,
  params: Record<string, any> = {}
) => {
  AppEventsLogger.logEvent('InitiateCheckout', {
    // fb_content_ids: contentIds,
    // fb_content_type: contentType,
    // fb_currency: currency,
    // fb_value: value,
    ...params,
  });
};

/**
 * Logs a Purchase event
 */
export const logPurchase = (
  amount: number,
  currency: string,
  params: Record<string, any> = {}
) => {
  AppEventsLogger.logPurchase(amount, currency, params);
};

/**
 * Logs a Search event
 */
export const logSearch = (
  searchString: string,
  params: Record<string, any> = {}
) => {
  AppEventsLogger.logEvent('Search', {
    fb_search_string: searchString,
    ...params,
  });
};

/**
 * Logs a ViewContent event
 */
export const logViewContent = (
  contentId: string,
  contentType: string,
  currency: string,
  value: number,
  params: Record<string, any> = {}
) => {
  AppEventsLogger.logEvent('ViewContent', {
    fb_content_id: contentId,
    fb_content_type: contentType,
    fb_currency: currency,
    fb_value: value,
    ...params,
  });
};

import React, { useState, memo, useMemo, useCallback } from 'react';
import { TouchableOpacity, View, LayoutAnimation, Button, Linking, Alert } from 'react-native';
import stylesWithOutColor from './style';
import { useNavigation, useTheme } from '@react-navigation/native';
import { FastImagesItem, Label, Spacer } from 'components/atoms';
import Icons from 'common/icons';
import { RenderCustomHTML } from 'components/molecules';
import { t } from 'i18next';
import { useDispatch } from 'react-redux';
import { setLoading, setLogout } from 'app-redux-store/slice/appSlice';
import { showErrorMessage, showSuccessMessage } from 'utils/show_messages';
import { deleteMe } from 'services/account';

const DropDownListDelete = ({
    type,
    isborder,
    answerLabelStyle,
}: any) => {
    const { colors } = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
    const [isExpanded, setIsExpanded] = useState(false);
    const dispatch = useDispatch()
    const toggleExpand = () => {
        setIsExpanded(!isExpanded);
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    };
    const navigation = useNavigation()
    const logout = useCallback(async () => {
        dispatch(setLogout('Login'));
    }, [dispatch]);
    const deleteAccount = async () => {
        dispatch(setLoading(true));
        const {status} = await deleteMe()
        if(status){
            showErrorMessage(
                t('helpOrderOptions.deleteSuccess'), 
                undefined, 
                'dark', 
                { isAccountDelete: true }
            );
            await logout()
            dispatch(setLoading(false));
            navigation.reset({
                index: 0,
                routes: [{ name: 'Tab', params: { screen: 'Shop' } }],
              })
        }
        dispatch(setLoading(false));
    }
    const handleDelete = () => {
        setTimeout(() => {
            Alert.alert(t('helpOrderOptions.deleteAccount'), t('helpOrderOptions.deleteDesc'), [
              {text: t('helpOrderOptions.cancel'),
                style: 'default',
            },
              {
                text: t('helpOrderOptions.confirm'),
                style: 'default',
                onPress: () => deleteAccount(),
              },
            ]);
          }, 200);
    }
    return (
        <>
            <TouchableOpacity
                style={[
                    type === 'HTML'
                        ? [isborder ? styles.subContainer : styles.border]
                        : styles.mainContainer,
                ]}
                onPress={toggleExpand}>
                <View style={styles.titleView}>
                    <>
                        <Label
                            lineHeight="l"
                            style={styles.labelText}
                            size="m"
                            color="text2"
                            fontFamily="Medium"
                            text={
                                (t('helpOrderOptions.deleteQuestion')).replace(/<\/?[^>]+(>|$)/g, '')
                            }
                        />
                    </>


                    <FastImagesItem
                        source={Icons.arrowBottom}
                        FastImageStyle={
                            isExpanded
                                ? styles.upView
                                : styles.fastImageView
                        }
                        tintColor={colors.text2}
                    />
                </View>

                {isExpanded && (
                    <View style={styles.answerView}>

                        <Label
                            style={answerLabelStyle}
                            size="m"
                            color="text2"
                            fontFamily="Regular"
                            text={t('helpOrderOptions.deleteAnswer')}
                            textTransform='capitalize'
                        />

                        <Spacer size='m' />
                        <TouchableOpacity
                            onPress={handleDelete}
                            style={styles.pasteButton}>
                            <Label
                                text="Delete"
                                size="m"
                                color="newSunnyOrange"
                                weight="500"
                            />
                        </TouchableOpacity>
                        <Spacer size='m' />
                        <Label
                        style={answerLabelStyle}
                        size="m"
                        color="text2"
                        fontFamily="Regular"
                        text={t('otherText.unsatisfied')}
                        textTransform='capitalize'
                        />
                        <Label
                        onPress={() => Linking.openURL('mailto:<EMAIL>')}
                        text={t('login.supportEmail')}
                        color="silkBlue"
                        size="m"
                        fontFamily="Regular"
                        />

                    </View>
                )}
            </TouchableOpacity>
        </>
    );
};

export default memo(DropDownListDelete);
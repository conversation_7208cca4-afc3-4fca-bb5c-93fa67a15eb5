import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    subContainer: {
      flex: Sizes.x,
    },
    mainContainer: {
      backgroundColor: colors.background,

      flex: Sizes.x,
    },
    listView: {
      width: '30%',
    },
    listViewItems: {
      width: '70%',
    },
    categoriesLabel: {
      fontWeight: '400',
      fontSize: Sizes.m,
      width: '100%',
    },
    labelView: {
      height: Sizes.x7l,
      width: '100%',
      paddingHorizontal: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    activeBg: {
      backgroundColor: colors.primary,
    },
    listViewContainers: {flexDirection: 'row'},
    separates: {
      width: '100%',
      height: Sizes.xs,
      backgroundColor: colors.borderSaprater,
    },
    continuerBlue: {
      backgroundColor: colors.topItemsBg,
    },
    textAdd: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      paddingLeft: Sizes.xm,
    },
    subCategoriesList: {
      fontWeight: '400',
      fontSize: Sizes.m,
    },
    categoryMainView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.m,
    },
    SearchView: {flex: Sizes.x},
    categorySubView: {
      alignItems: 'center',
      paddingVertical: Sizes.xm,
      paddingHorizontal: Sizes.s,
    },
    categoryView: {
      alignItems: 'center',
      padding: Sizes.sx,
      borderWidth: 1.5,
      borderColor: colors.grey2,

      borderRadius: Sizes.m,
      marginLeft: Sizes.s,
      flex: Sizes.x,
    },
    categoryTitleView: {
      width: 76,
      height: 72,
      borderRadius: Sizes.xm,
      borderWidth: 1.5,
      borderColor: colors.grey2,
    },
    categoryChildView: {
      width: 110,
      height: 88,
      borderRadius: Sizes.xm,
      borderWidth: 1.5,
      borderColor: colors.grey2,
      padding: 3,
    },
    categoryTitleSubView: {
      width: 82,
      height: 81,
      borderRadius: 70,
      borderWidth: 1.5,
      borderColor: colors.grey2,
    },
    categoryNameView: {width: Sizes.x9l + Sizes.xs},
    goBackImage: {
      tintColor: colors.goBackIconColor,
      width: Sizes.xxl,
      height: Sizes.xxl,
    },
  });
export default styles;

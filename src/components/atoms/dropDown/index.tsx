import React, {useState, useMemo} from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {ImageProps, StyleProp, TextStyle, View, ViewProps} from 'react-native';
import {Dropdown} from 'react-native-element-dropdown';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import Label from '../label';
import Spacer from '../spacer';
import {t} from 'i18next';

type Props = {
  testID?: string;
  data?: any;
  onChange: (value: any) => void;
  setOpenDropDown: (value: boolean) => void;
  styleDropDown?: ViewProps['style'];
  listContainerStyle?: ViewProps['style'];
  itemContainerStyle?: ViewProps['style'];
  iconStyle?: ImageProps['style'];
  itemTextStyle?: ViewProps['style'];
  searchInputStyle?: ViewProps['style'];
  selectedTextStyle?: ViewProps['style'];
  placeholderStyle?: StyleProp<TextStyle>;
  heading?: boolean | string;
  isSpacerSize?: boolean;
  hideLabel?: boolean;
  search?: boolean;
  labelField?: string;
  valueField?: string;
  returnValueOnly?: boolean;
  fieldMandatory?: boolean;
  value?: any;
  selectedTextProps?: any;
  placeholder?: string;
  renderItem?: ({
    item,
    index,
  }: {
    item: any;
    index: number;
  }) => React.ReactElement | null;
  renderLeftIcon?: any;
  renderRightIcon?: (focus: boolean) => void;
  showsVerticalScrollIndicator?: boolean;
  disable?: boolean;
  dropdownPosition?: string;
  labelStyle?: ViewProps['style'];
};

const DropDown = ({
  testID,
  data,
  styleDropDown,
  iconStyle,
  itemTextStyle,
  selectedTextStyle,
  listContainerStyle,
  searchInputStyle,
  placeholderStyle,
  onChange,
  value,
  placeholder,
  renderItem,
  heading,
  search = true,
  selectedTextProps,
  isSpacerSize = false,
  hideLabel = false,
  returnValueOnly,
  fieldMandatory,
  labelField,
  valueField,
  itemContainerStyle,
  renderLeftIcon,
  renderRightIcon,
  setOpenDropDown,
  showsVerticalScrollIndicator = true,
  disable = false,
  dropdownPosition = 'auto',
  labelStyle,
}: Props) => {
  const [isFocus, setIsFocus] = useState(false);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [searchValue, setSearchValue] = useState('');

  const filteredData = data?.filter(item =>
    item?.value?.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const renderLabel = () => {
    if (value || isFocus) {
      return (
        <View style={[styles.label, labelStyle]}>
          <Label color="grey" text={heading} size="m" fontFamily="Medium" />
          {fieldMandatory && (
            <Label color="red3" text=" *" size="m" fontFamily="Regular" />
          )}
        </View>
      );
    }
    return null;
  };

  return (
    <View style={styles.container}>
      {heading ? renderLabel() : null}

      {!hideLabel && <Spacer size={isSpacerSize === true ? 'sx' : 'xm'} />}
      <Dropdown
        testID={testID}
        iconStyle={[{tintColor: colors.newPrimary}, iconStyle ? iconStyle : {}]}
        style={[
          styles.dropdown,
          isFocus && {borderColor: colors.primary},
          styleDropDown,
        ]}
        placeholderStyle={[styles.placeholderStyle, placeholderStyle]}
        selectedTextStyle={[styles.selectedTextStyle, selectedTextStyle]}
        inputSearchStyle={[styles.inputSearchStyle, searchInputStyle]}
        renderItem={renderItem}
        data={data}
        search={search}
        maxHeight={300}
        labelField={labelField ? labelField : 'label'}
        valueField={valueField ? valueField : 'value'}
        placeholder={!isFocus ? placeholder : ''}
        searchPlaceholder="Search..."
        searchPlaceholderTextColor={colors.text2}
        value={value}
        dropdownPosition={dropdownPosition}
        onFocus={() => {
          setIsFocus(true);
          setOpenDropDown && setOpenDropDown(true);
        }}
        onBlur={() => {
          setIsFocus(false);
          setOpenDropDown && setOpenDropDown(false);
        }}
        itemTextStyle={[styles.textDropDown, itemTextStyle]}
        containerStyle={[styles.listDropdown, listContainerStyle]}
        itemContainerStyle={[styles.dropdownBg, itemContainerStyle]}
        onChange={item => {
          onChange(returnValueOnly ? item?.[valueField || 'value'] : item); // Use valueField for returnValueOnly
          setIsFocus(false);
        }}
        selectedTextProps={selectedTextProps}
        renderLeftIcon={renderLeftIcon}
        renderRightIcon={
          renderRightIcon ? () => renderRightIcon(isFocus) : undefined
        }
        showsVerticalScrollIndicator={showsVerticalScrollIndicator}
        disable={disable}
        onChangeText={text => setSearchValue(text)}
        autoScroll={false}
      />
      {searchValue?.length > 0 && filteredData?.length === 0 && ( // Conditional check for filteredData
        <Label
          color="textError"
          text={t('PDP.noDataFound')}
          size="m"
          weight="400"
          style={styles.errorStyle}
        />
      )}
    </View>
  );
};

export default DropDown;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
    },
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    scrollViewStyle: {
      marginTop: 'auto',
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    viewSubProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.sx,
    },
    bottomLine: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    overallView: {
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    memberShipCardImageView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    input: {
      borderRadius: Sizes.xms,
      color: colors.text2,
    },
    searchIconStyle: {
      width: Sizes.l,
      height: Sizes.l,
    },
    inputBorderStyle: {
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      height: 45,
      backgroundColor: colors.background,
      borderColor: colors.placeholderColor,
    },
    cancelView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
    },
  });

export default styles;

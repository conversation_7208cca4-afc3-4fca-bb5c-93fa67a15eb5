import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    subCategoriesList: {
      width: '92%',
      fontWeight: '400',
      fontSize: Sizes.m,
    },
    labelView: {
      height: Sizes.x7l,
      width: '100%',
      paddingHorizontal: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    labelViewSub: {
      paddingHorizontal: Sizes.l,
    },
    borderBottom: {
      borderBottomWidth: 0.5,
      borderBottomColor: colors.lightGray,
    },
    labelsStyle: {
      maxWidth: '100%',
      // paddingHorizontal: Sizes.xm,

      alignItems: 'center',
      // maxWidth: Sizes.x6l + Sizes.l,
      textAlign: 'center',
      paddingVertical: Sizes.xs,
    },
    imageCategories: {
      width: Sizes.x5l,
      height: Sizes.x4l,
    },
    imageContainer: {
      width: Sizes.x6l,
      height: Sizes.x6l,
      borderWidth: Sizes.x,
      padding: Sizes.xs,
      borderRadius: Sizes.s,
      borderColor: colors.border,
    },
    textAdd: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      paddingLeft: Sizes.xm,
    },
    whiteBg: {
      backgroundColor: colors.background,
      borderWidth: Sizes.x,
      borderColor: colors.border,
    },
    continuerBlue: {
      backgroundColor: colors.topItemsBg,
    },
    optionsImage: {width: Sizes.l, height: Sizes.l},
    viewBoxes: {
      width: '100%',
      paddingVertical: Sizes.mx,
    },
  });

export default styles;

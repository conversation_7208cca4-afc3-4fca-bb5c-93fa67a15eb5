import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex:{
      flex: Sizes.x
    },
    container: {
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
    },
    errorText: {
      marginLeft: Sizes.l,
    },
    commentStyle: {
      borderRadius: Sizes.xm,
      height: Sizes.exl,
      borderWidth: Sizes.x,
    },
    inputBorderStyle: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      height: 45,
    },
    searchIconStyle: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    searchIconSize: {
      width: Sizes.xxl,
      height: Sizes.xxl,
    },
    sectionContainer: {
      backgroundColor: colors.whiteColor,
      padding: Sizes.m,
    },
    topLabel: {
      paddingVertical: Sizes.m,
      marginTop: Sizes.sx,
      marginHorizontal: Sizes.m,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.placeholderColor,
    },
    productImgMAinView: {
      backgroundColor: 'white',
      padding: Sizes.xs,
      width: Sizes.ex,
      height: Sizes.ex,
      borderRadius: Sizes.xl,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: Sizes.x6l,
      position: 'absolute',
      alignSelf: 'center',
      bottom: Sizes.x3l,
    },
    subView: {
      alignSelf: 'center',
      backgroundColor: colors.whiteColor,
      width: Sizes.ex - Sizes.sx,
      height: Sizes.ex - Sizes.sx,
      borderRadius: Sizes.xl,
      borderWidth: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: colors.border,
      position: 'absolute',
    },
    findImg: {
      height: Sizes.exl,
      width: Sizes.exl,
    },
    gradientImgBg: {
      width: Sizes.xl,
      height: Sizes.xl,
      justifyContent: 'center',
      borderTopLeftRadius: Sizes.x3l,
      borderBottomLeftRadius: Sizes.x3l,
      right: Sizes.s,
    },
    inputStyle: {
      borderRadius: Sizes.x3l,
      borderColor: colors.lightGray,
    },
    button: {
      borderRadius: Sizes.x3l,
      width: '50%',
      alignSelf: 'center',
      height: Sizes.xx4l,
    },
    modalHeaderView: {
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
    },
    closeIconView: {
      width: Sizes.x3l,
      height: Sizes.x3l,
      justifyContent: 'center',
      alignItems: 'flex-end',
      position: 'absolute',
      right: Sizes.xm,
      top: 0,
    },
    textStyle: {
      fontWeight: '400',
      textAlign: 'center',
      fontSize: Sizes.l,
      color: '#OOO',
      paddingVertical: 5,
      alignSelf: 'center',
      width: '72%',
    },
    input: {
      borderRadius: Sizes.xm,
    },
    searchIcon: {
      width: Sizes.x6l,
      height: Sizes.x6l + Sizes.xl,
    },
    paddingSpace: {
      paddingRight: Sizes.s,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: 46,
    },
    productSugView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    btnStyle: {
      alignSelf: 'flex-start',
    },
  });

export default styles;

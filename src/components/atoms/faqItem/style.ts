import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    questionView: {
      borderBottomColor: colors.grey2,
      borderBottomWidth: Sizes.x,
      paddingVertical: Sizes.m,
    },
    question2View: {
      paddingVertical: Sizes.m,
    },
    questionMainView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    questionSubView: {
      flex: Sizes.xs,
      flexWrap: 'wrap',
      flexDirection: 'row',
      alignSelf: 'center',
    },
    likeView: {
      flexWrap: 'wrap',
      alignItems: 'flex-end',
      justifyContent: 'flex-end',
      flex: Sizes.x,
      flexDirection: 'row',
    },
    faqQuestionsView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: Sizes.m,
    },
    flexView: {
      flex: Sizes.x,
    },
    tagView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    questionTag: {
      paddingHorizontal: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'flex-start',
      height: Sizes.x3l,
      backgroundColor: colors.green3,
      borderRadius: Sizes.s,
    },
  });

export default styles;

import React, {useEffect, useMemo} from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {Label} from 'components/atoms';
import {Button} from 'components/molecules';
import {sAllDevice} from 'utils/utils';
import {t} from 'i18next';

let refetchCounter = 0;
const RetryPaymentButton = props => {
  const {checkPayment, showBtn, btnText} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  useEffect(() => {
    let interval = setInterval(() => {
      if (refetchCounter == 5) {
        clearInterval(interval);
      }
      refetchCounter = refetchCounter + 1;
      checkPayment();
    }, 20000);

    return () => {
      clearInterval(interval);
    };
  }, [refetchCounter]);

  return (
    <View style={styles.detailMainView}>
      <View style={styles.detailBox}>
        <Label
          fontFamily="Medium"
          color="red3"
          text={t('payment.verifyingPayment')}
          size="mx"
          style={styles.fOne}
        />
        {showBtn ? (
          <View style={styles.btnStyle}>
            <Label
              fontFamily="Medium"
              weight="500"
              color="whiteColor"
              text={btnText}
              size={sAllDevice ? 'm' : 'mx'}
            />
          </View>
        ) : (
          <View />
        )}
      </View>
    </View>
  );
};

export default RetryPaymentButton;

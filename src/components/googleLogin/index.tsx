import {
  GoogleSigninButton,
  statusCodes,
  GoogleSignin,
} from '@react-native-google-signin/google-signin';
import {useTheme} from '@react-navigation/native';
import Config from 'react-native-config';
import stylesWithOutColor from './style';
import {useMemo} from 'react';
import { debugLog } from 'utils/debugLog';

type Props = {
  getUserData: (data: any) => Promise<void>;
};

const LoginGoogle = ({getUserData}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const googleSignIn = async () => {
    GoogleSignin.configure({
      scopes: ['email', 'profile'],
      webClientId: Config.WEB_CLIENT_ID,
      forceConsentPrompt: true,
    });
    try {
      const hasServices = await GoogleSignin.hasPlayServices();
      if (hasServices) {
        await GoogleSignin.signOut();
        const userInfo = await GoogleSignin.signIn();
        getUserData(userInfo);
      }
    } catch (error: any) {
      debugLog(error);
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      } else if (error.code === statusCodes.IN_PROGRESS) {
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      } else {
        debugLog('error===-------------------', error);
      }
    }
  };

  return <GoogleSigninButton style={styles.button} onPress={googleSignIn} />;
};

export default LoginGoogle;

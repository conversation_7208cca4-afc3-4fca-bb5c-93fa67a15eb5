import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const BrandLoader = () => {
  const {colors} = useTheme();
  const boxWidth = (width - 60) / 4;
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1500}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="10" rx="5" ry="5" width={width - 20} height="180" />

        <Rect x="10" y="200" rx="5" ry="5" width={width - 20} height="50" />
        <Rect x="10" y="260" rx="5" ry="5" width={width - 20} height="100" />

        <Rect x="10" y="370" rx="5" ry="5" width={width - 20} height="260" />
        <Rect x="20" y="380" rx="5" ry="5" width="30" height="20" />

        <Rect x="20" y="410" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="410"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="410"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="410"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect x="20" y="520" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="520"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="520"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="520"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />

        <Rect x="10" y="640" rx="5" ry="5" width={width - 20} height="260" />
        <Rect x="20" y="650" rx="5" ry="5" width="30" height="20" />

        <Rect x="20" y="680" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="680"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="680"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="680"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect x="20" y="790" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="790"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="790"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="790"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />

        <Rect x="10" y="910" rx="5" ry="5" width={width - 20} height="260" />
        <Rect x="20" y="920" rx="5" ry="5" width="30" height="20" />

        <Rect x="20" y="950" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="950"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="950"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="950"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect x="20" y="1060" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="1060"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="1060"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="1060"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />

        <Rect x="10" y="1180" rx="5" ry="5" width={width - 20} height="260" />
        <Rect x="20" y="1190" rx="5" ry="5" width="30" height="20" />

        <Rect x="20" y="1220" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="1220"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="1220"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="1220"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect x="20" y="1330" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect
          x={boxWidth + 10}
          y="1330"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 2 + 20}
          y="1330"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
        <Rect
          x={boxWidth * 3 + 30}
          y="1330"
          rx="5"
          ry="5"
          width={boxWidth}
          height="100"
        />
      </ContentLoader>
    </ScrollView>
  );
};

export default BrandLoader;

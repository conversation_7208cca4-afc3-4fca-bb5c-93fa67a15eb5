import React, { useCallback, useMemo } from 'react';
import {
  View,
  FlatList,
  ImageBackground,
  TouchableOpacity,
} from 'react-native';
import Label from '../label';
import ImageIcon from '../imageIcon';
import Spacer from '../spacer';
import ProductCardVertical from '../productCardVertical';
import ErrorHandler from 'utils/ErrorHandler';
import { checkDevice } from 'utils/utils';
import { resolveUrl } from 'utils/resolveUrl';
import { AnalyticsEvents } from 'components/organisms';
import { getProductCardProps } from 'utils/productProps';
import stylesWithoutColor from './style';
import { useTheme } from '@react-navigation/native';
import Icons from 'common/icons';


const TAG = 'StandardCarouselSection';

interface Props {
  section: any;
  userInfo: any;
  isLoggedIn: boolean;
  navigation: any;
  bannerImage: any;
  icon: keyof typeof Icons;
  defaultUrl: string;
}

const StandardCarouselSection: React.FC<Props> = ({
  section,
  userInfo,
  isLoggedIn,
  navigation,
  bannerImage,
  icon,
  defaultUrl,
}) => {
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithoutColor(colors), [colors]);

  const handlePress = useCallback(
    sectionData => async () => {
      const eventData = {
        'Page Link': sectionData?.landing_url || defaultUrl,
        'Section Name': sectionData?.title,
        Section: 'Homepage Sections',
      };

      AnalyticsEvents(
        'VIEW_ALL_HOMEPAGE',
        'View All Homepage',
        eventData,
        userInfo,
        isLoggedIn,
      );

      const urlKey = sectionData?.landing_url || defaultUrl;
      await resolveUrl({ urlKey, navigation });
    },
    [userInfo, isLoggedIn, navigation, defaultUrl],
  );

   const EMPTY_FREE_PRODUCTS = useMemo(() => [], []);

  const renderProductCard = useCallback(
    (item: any, index: number, width?: number) => (
      <ErrorHandler
        componentName={`${TAG} ProductCardVertical`}
        onErrorComponent={<View />}>
        <ProductCardVertical
          {...getProductCardProps(item)}
          index={index}
          size="large"
          imageWithBorder
          maxWidth={width}
          showWishlist
          navigation={navigation}
          freeProducts={EMPTY_FREE_PRODUCTS}
        />
      </ErrorHandler>
    ),
    [navigation],
  );

  const renderItem = useCallback(
    ({ item, index }: { item: any; index: number }) =>
      renderProductCard(item, index, checkDevice() && 0.24),
    [renderProductCard],
  );

  const keyExtractor = useCallback((_, index) => index.toString(), []);
  const itemSeparator = useMemo(() => <Spacer size="xm" />, []);

  return (
    <View>
      <TouchableOpacity onPress={handlePress(section)} style={styles.backImageView}>
        <ImageBackground source={Icons.endokingBanner} style={styles.backgroundImage}>
          <View style={styles.endKingView}>
            <Label
              text={section?.title}
              size="l"
              weight="600"
              color="categoryTitle"
            />
          </View>
        </ImageBackground>
        <ImageIcon icon="endokingRightArrowIcon" size="xxl" />
      </TouchableOpacity>

      <View style={styles.countView}>
        <FlatList
          data={section.elements}
          horizontal={checkDevice()}
          numColumns={checkDevice() ? null : 2}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          ItemSeparatorComponent={itemSeparator}
          onEndReachedThreshold={0.8}
          removeClippedSubviews
          windowSize={5}
          maxToRenderPerBatch={5}
          updateCellsBatchingPeriod={50}
        />
      </View>
      <Spacer size="m" />
    </View>
  );
};

export default React.memo(StandardCarouselSection);

import React, {useMemo, useRef, useState, useEffect, useCallback} from 'react';
import {
  TouchableOpacity,
  View,
  Platform,
  Alert,
  SafeAreaView,
  Image,
  Dimensions,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {
  ImageIcon,
  Label,
  Spacer,
  AddressAutoPlaces,
  AddressConfirmModal,
} from 'components/atoms';
import stylesWithOutColor from './style';
import Modal from 'react-native-modal';
import {
  btnClickCallBack,
  getCurrentLocation,
  debounce,
  distance,
} from 'utils/utils';
import {
  requestMultiple,
  PERMISSIONS,
  openSettings,
} from 'react-native-permissions';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import ErrorHandler from 'utils/ErrorHandler';
import {getMapLocationDetails} from 'services/address';
import MapView, {Marker, Callout, PROVIDER_GOOGLE} from 'react-native-maps';
import Icons from 'common/icons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const {width: DWidth, height: DHeight} = Dimensions.get('window');
const mapSize = Platform.OS === 'android' ? 186 : 186;

const INDIA_BOUNDS = {
  northEast: {latitude: 37.1, longitude: 97.4},
  southWest: {latitude: 6.5, longitude: 68.1},
};

const INDIA_REGION = {
  latitude: 22.9734,
  longitude: 78.6569,
  latitudeDelta: 20,
  longitudeDelta: 20,
};

type Props = {
  visible: boolean;
  deliverAddress: PlaceSuggestion;
  onClose: () => void;
  goBack: () => void;
  onSelectAddress: (address: PlaceSuggestion) => void;
  coords?: {latitude: number; longitude: number};
};

const MapsModal = (props: Props) => {
  const TAG = 'MapsModal';
  const {visible, onClose, goBack, onSelectAddress, deliverAddress, coords} =
    props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const refMap = useRef(null);
  const [latitude, setLatitude] = useState(0);
  const [longitude, setLongitude] = useState(0);
  const [selectedAddress, setSelectedAddress] = useState<PlaceSuggestion>();
  const [locationShow, setLocationShow] = useState(false);
  const [state, setState] = useState({});
  const [addressConfirm, setAddressConfirm] = useState(false);
  const locationRef = useRef(null);
  const [mapInit, setMapInit] = useState(true);
  const [showCallout, setShowCallout] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [permissionDeny, setPermissionDeny] = useState(false);
  const latestRegion = useRef(null);
  const insets = useSafeAreaInsets();
  const [goBackCheck, setGoBackCheck] = useState(true);
  const [currentCoords, setCurrentCoords] = useState();

  useEffect(() => {
    const lat = deliverAddress?.lat || deliverAddress?.latitude;
    setCurrentCoords(coords);
    if (lat) {
      const lng = deliverAddress?.lng || deliverAddress?.longitude;
      setSelectedAddress(deliverAddress);
      setLatitude(lat);
      setLongitude(lng);
      onMoveMap({
        latitude: lat,
        longitude: lng,
      });
    } else {
      const check = deliverAddress?.edit
        ? deliverAddress?.lat || deliverAddress?.latitude
        : true;
      // setGoBackCheck(check ? true : false);
      // if (coords) {
      //   getLocation(coords, check);
      // } else {
      //   getCurrentLatLng(check);
      // }
      getCurrentLatLng(check);
    }
  }, [deliverAddress, coords]);

  const getCurrentLatLng = check => {
    requestMultiple(
      Platform.OS === 'ios'
        ? [PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]
        : [PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION],
    ).then(result => {
      if (
        result[
          Platform.OS === 'ios'
            ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
            : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
        ] === 'granted'
      ) {
        setPermissionDeny(false);
        getCurrentLocation(async position => {
          setCurrentCoords(position.coords);
          getLocation(position.coords, check);
        });
      } else {
        setPermissionDeny(true);
        setTimeout(() => {
          Alert.alert(t('address.locPerTitle'), t('address.locPerMsg'), [
            {text: t('buttons.notNow')},
            {
              text: t('buttons.openSetting'),
              style: 'cancel',
              onPress: () => openSettings(),
            },
          ]);
        }, 200);
      }
    });
  };

  const getLocation = async (coords, check) => {
    try {
      setLatitude(coords.latitude);
      setLongitude(coords.longitude);
      // setGoBackCheck(check);
      onMoveMap(coords);
      // if (check) {
      const {data, status} = await getMapLocationDetails(
        coords.latitude,
        coords.longitude,
      );
      if (status) {
        onLocation(data?.results[0]);
      }
      // }
    } catch (e) {}
  };

  const onLocation = details => {
    if (!details) return;
    const components = details.address_components || [];
    const getComponent = type =>
      components.find(comp => comp.types.includes(type))?.long_name || '';
    const postcode = getComponent('postal_code') || '';
    const city = getComponent('locality') || '';
    const stateName = getComponent('administrative_area_level_1') || '';
    const state2 = getComponent('administrative_area_level_2') || '';
    const state3 = getComponent('administrative_area_level_3') || '';
    const county = getComponent('country') || '';
    const subpremise = getComponent('subpremise') || '';
    const premise = getComponent('premise') || '';
    const neighborhood = getComponent('neighborhood') || '';
    const street = getComponent('route') || '';
    const sublocality1 = getComponent('sublocality_level_1') || '';
    const sublocality2 = getComponent('sublocality_level_2') || '';
    const sublocality3 = getComponent('sublocality_level_3') || '';
    const customAddress = [
      subpremise,
      premise,
      street,
      neighborhood,
      sublocality3,
      sublocality2,
      sublocality1,
      state3,
    ]
      .filter(Boolean)
      .join(', ');
    const area = sublocality3 || sublocality2 || sublocality1 || state3 || city;

    const lat = details.geometry?.location?.lat;
    const lng = details.geometry?.location?.lng;

    const obj = {
      area,
      postcode,
      city,
      state: stateName,
      county,
      formatted_address: details.formatted_address,
      lat,
      lng,
      deliveryArea: customAddress,
    };
    setSelectedAddress(obj);
    setState({});
  };

  const onChange = () => {
    setLocationShow(true);
    setState({});
    setTimeout(() => {
      setLocationShow(false);
    }, 2000);
  };

  const handleDragEnd = async coords => {
    setLatitude(coords.latitude);
    setLongitude(coords.longitude);
    // setGoBackCheck(true);
    const {data, status} = await getMapLocationDetails(
      coords.latitude,
      coords.longitude,
    );
    // onMoveMap(coords);
    if (status) {
      onLocation(data?.results[0]);
      setShowCallout(true);
      setState({});
    }
  };

  const handleRegionEnd = useCallback(
    debounce(() => {
      if (
        latestRegion?.current &&
        longitude &&
        latestRegion?.current?.latitude &&
        latestRegion?.current?.latitude !== 0 &&
        Number(latestRegion?.current?.longitude).toFixed(6) !==
          Number(longitude).toFixed(6) &&
        !mapInit
      ) {
        const obj = {
          latitude: Number(latestRegion.current?.latitude).toFixed(8),
          longitude: Number(latestRegion.current?.longitude).toFixed(8),
        };
        handleDragEnd(obj);
      }
      setIsDragging(false);
    }, 500),
    [latestRegion, mapInit, longitude, handleDragEnd, setIsDragging],
  );

  const onMoveMap = coords => {
    const lat = Number(coords.latitude);
    const long = Number(coords.longitude);
    setLatitude(lat);
    setLongitude(long);
    let newRegion = {
      latitude: lat,
      longitude: long,
      latitudeDelta: 0.0001,
      longitudeDelta: 0.0001,
    };
    setTimeout(() => {
      refMap?.current?.animateToRegion(newRegion, 1000);
      setMapInit(false);
    }, 500);
  };

  const onSelectLocation = item => {
    setSelectedAddress(item);
    // setGoBackCheck(true);
    onMoveMap({
      latitude: item?.lat || item?.latitude,
      longitude: item?.lng || item?.longitude,
    });
  };

  const onAddConfirmClose = () => {
    closeAddModal();
    goBack();
  };

  const closeAddModal = useCallback(() => {
    setAddressConfirm(prevState => !prevState);
  }, []);

  const kmText = useMemo(() => {
    if (
      !currentCoords?.latitude ||
      !currentCoords?.longitude ||
      !latitude ||
      !longitude
    )
      return '';
    const distanceValue = distance(
      [currentCoords?.latitude, currentCoords?.longitude],
      [latitude, longitude],
      'km',
    );
    const km = parseFloat(distanceValue).toFixed(2);
    const isHere = Number(km) === 0 || Number(km) < 0.01;
    return `${
      isHere ? '' : `${t('address.pLocation')} ${km} ${t('address.cLocation')}`
    }`;
  }, [currentCoords, latitude, longitude, t]);

  const renderLocation = useCallback(() => {
    return (
      <View style={styles.addressContainer}>
        <Spacer size="m" />
        {selectedAddress && (
          <>
            <View style={styles.rowCenter}>
              <ImageIcon size="l" icon="mapPin" tintColor="text" />
              <Spacer size="xm" type="Horizontal" />
              <Label
                numberOfLines={1}
                ellipsizeMode="tail"
                text={
                  selectedAddress?.area ||
                  selectedAddress?.tag ||
                  selectedAddress?.city ||
                  ''
                }
                size="mx"
                fontFamily="Medium"
                color="text"
                style={styles.fOne}
              />
              <Spacer size="xm" type="Horizontal" />
              <TouchableOpacity onPress={() => onChange()}>
                <Label
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  text={t('buttons.change')}
                  size="mx"
                  fontFamily="Medium"
                  color="newSunnyOrange"
                />
              </TouchableOpacity>
            </View>
            <Spacer size="s" />
          </>
        )}
        <Label
          text={
            goBackCheck
              ? selectedAddress?.formatted_address ||
                selectedAddress?.deliveryArea ||
                (permissionDeny ? t('address.enableLocation') : '') ||
                ''
              : t('address.manuallyLocation')
          }
          size="m"
          fontFamily="Medium"
          color={
            goBackCheck
              ? selectedAddress?.formatted_address ||
                selectedAddress?.deliveryArea
                ? 'text'
                : 'vividOrange'
              : 'text'
          }
          style={styles.address}
          numberOfLines={2}
          ellipsizeMode="tail"
        />
        {kmText ? (
          <View style={{height: 16}}>
            <Spacer size="s" />
            <Label
              numberOfLines={1}
              ellipsizeMode="tail"
              text={kmText}
              size="m"
              fontFamily="Medium"
              color="vividOrange"
            />
          </View>
        ) : (
          <View />
        )}
        <Spacer size="m" />
        <Button
          style={styles.btnView}
          labelStyle={styles.btnTxt}
          text={t(
            deliverAddress?.lat || deliverAddress?.latitude
              ? 'manageAddress.updateMoreDetails'
              : 'manageAddress.moreDetails',
          )}
          onPress={() => onSelectAddress(selectedAddress)}
          labelSize="mx"
          size="l"
          type="secondary"
          radius="sx"
          labelColor="whiteColor"
          weight="500"
        />
        <Spacer size="m" />
      </View>
    );
  }, [selectedAddress, insets.bottom, permissionDeny, goBackCheck, kmText]);

  const isInsideIndia = region => {
    const {latitude, longitude} = region;
    return (
      latitude >= INDIA_BOUNDS.southWest.latitude &&
      latitude <= INDIA_BOUNDS.northEast.latitude &&
      longitude >= INDIA_BOUNDS.southWest.longitude &&
      longitude <= INDIA_BOUNDS.northEast.longitude
    );
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.25}
      style={styles.modalStyle}>
      <SafeAreaView style={styles.container}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <View style={styles.headerView}>
            <View style={styles.fOne}>
              <Header
                backButton
                text={t('manageAddress.confirmLocationTitle')}
                onPressNavigation={onClose}
              />
            </View>
            <TouchableOpacity
              style={styles.closeIcon}
              onPress={goBackCheck ? closeAddModal : onClose}>
              <ImageIcon size="xxl" icon="closeIcons" tintColor="text" />
            </TouchableOpacity>
          </View>
        </ErrorHandler>
        <View
          style={{
            height: DHeight - (mapSize + insets.top + insets.bottom),
          }}>
          <Image
            source={Icons.marker}
            resizeMode="contain"
            style={[
              styles.markerView,
              {
                top:
                  (DHeight - (mapSize + (insets.top + insets.bottom))) / 2 - 48,
                left: (DWidth - 48) / 2,
              },
            ]}
          />
          {showCallout ? (
            <View
              style={[
                styles.callout,
                {
                  top:
                    (DHeight - (mapSize + (insets.top + insets.bottom))) / 2 -
                    140,
                  left: (DWidth - (Platform.OS === 'android' ? 300 : 290)) / 2,
                },
              ]}>
              <View style={styles.callView1}>
                <Label
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  text={t('address.orderHere')}
                  size="l"
                  weight="600"
                  color="whiteColor"
                />
                <Label
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  text={t('address.placeMap')}
                  size="mx"
                  weight="500"
                  color="text2"
                />
              </View>
              <View style={styles.arrowDown} />
            </View>
          ) : (
            <View />
          )}
          <MapView
            ref={refMap}
            style={{
              height: DHeight - (mapSize + insets.top + insets.bottom),
              width: DWidth,
            }}
            showsUserLocation={true}
            showsMyLocationButton={false}
            provider={PROVIDER_GOOGLE}
            initialRegion={{
              latitude: Number(latitude),
              longitude: Number(longitude),
              latitudeDelta: 0.0001,
              longitudeDelta: 0.0001,
            }}
            // onLongPress={e => handleDragEnd(e.nativeEvent.coordinate)}
            onRegionChange={region => {
              if (isInsideIndia(region)) {
                if (!isDragging) setIsDragging(true);
              }
            }}
            onRegionChangeComplete={region => {
              if (isInsideIndia(region)) {
                setShowCallout(false);
                if (!isDragging) setIsDragging(true);
                latestRegion.current = region;
                handleRegionEnd();
              } else {
                refMap.current?.animateToRegion(INDIA_REGION, 1000);
              }
            }}>
            {/* <Marker
            coordinate={{
              latitude: Number(latitude),
              longitude: Number(longitude),
            }}
            image={Icons.marker}
            draggable
            onDragEnd={e => handleDragEnd(e.nativeEvent.coordinate)}>
            <Callout tooltip>
              <View style={styles.markerPos}>
                <View style={styles.callView}>
                  <Label
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    text={t('address.orderHere')}
                    size="l"
                    weight="600"
                    color="whiteColor"
                  />
                  <Label
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    text={t('address.placeMap')}
                    size="mx"
                    weight="500"
                    color="text2"
                  />
                </View>
                <View
                  style={styles.arrowDown}
                />
              </View>
            </Callout>
          </Marker> */}
          </MapView>
          <AddressAutoPlaces
            selected={selectedAddress}
            onPress={(item: any) => onSelectLocation(item)}
            locationShow={locationShow}
            locationRef={locationRef}
            insets={insets}
          />
          <TouchableOpacity
            style={styles.currentLatView}
            onPress={() => getCurrentLatLng(true)}>
            <ImageIcon size="l" icon="mapPin" tintColor="vividOrange" />
            <Spacer size="xm" type="Horizontal" />
            <Label
              text={t('address.currentLoc')}
              size="mx"
              fontFamily="Medium"
              color="vividOrange"
              textTransform="capitalize"
              style={styles.labelBottom}
            />
          </TouchableOpacity>
        </View>
        {renderLocation()}
      </SafeAreaView>

      {addressConfirm && (
        <ErrorHandler
          componentName={`${TAG} AddressConfirmModal`}
          onErrorComponent={<View />}>
          <AddressConfirmModal
            visible={addressConfirm}
            title={t('address.updateLocTitle')}
            subTitle={t('address.updateLocSubTitle')}
            desc={t('address.updateLocMsg')}
            btn1Text={t('address.yesI')}
            btn2Text={t('address.noI')}
            onClose={closeAddModal}
            onSkip={() => onAddConfirmClose()}
            onMap={() => {}}
          />
        </ErrorHandler>
      )}
    </Modal>
  );
};

export default React.memo(MapsModal);

import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {Label, Spacer} from 'components/atoms';
import {Button} from 'components/molecules';
import {RootStackParamsList} from 'routes';
import {snakeToTitleCase} from 'utils/formatter';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import {orderStatusColor} from 'utils/utils';
import {useMemo} from 'react';
import { resolveUrl } from 'utils/resolveUrl';

type Props = {
  item: OrderReturn;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  type: string;
  index: number;
};

const ReturnCard = ({item, type, index, navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const statusObj = orderStatusColor(item?.status, colors);
  return (
    <View key={index} style={styles.box}>
      <View style={styles.processingContinuer}>
        <View style={styles.durationsBox}>
          <Label
            size="m"
            color="text2"
            text={`${t('orderListing.orderID')} - ${item?.order_id}`}
            fontFamily="Medium"
          />
        </View>
        <View
          style={[styles.approvedView, {backgroundColor: statusObj?.bgColor}]}>
          <Label
            size="m"
            color={statusObj?.color}
            text={snakeToTitleCase(
              item?.status,
              'Approved',
              'Request Approved',
            )}
            fontFamily="Medium"
          />
        </View>
      </View>
      <Spacer size="s" />
      <Label
        size="m"
        color="text2"
        text={`${t('orderListing.returnId')} - ${item?.return_id}`}
        fontFamily="Medium"
      />
      <Spacer size="s" />
      <Label
        size="m"
        color="text2"
        text={`${t('orderListing.requestedOn')} - ${
          item?.created_at.split('T')[0]
        } `}
        fontFamily="Medium"
      />
      <Spacer size="m" />
      <Label
        size="mx"
        color="categoryTitle"
        text={item?.name?.substring(0, 25) + '...'}
        fontFamily="SemiBold"
      />
      <Spacer size="s" />
      <Label
        text={`${t('cart.quantity')} ${item?.qty}`}
        fontFamily="Medium"
        size="m"
        color="text"
      />
      <Spacer size="m" />
      <Label
        text={`${t('orderListing.orderAmount1')} - ${t(
          'PDP.rupee',
        )}${parseFloat(item?.amount)?.toFixed(2)}`}
        size="mx"
        color="text"
        fontFamily="SemiBold"
      />
      <Spacer size="m" />
      <View style={styles.imageBox}>
        <TouchableOpacity
          style={styles.image}
          onPress={async () => {
            if (item?.url) {
              // navigation.navigate('UrlResolver', {urlKey: item?.url + '.html'});
               const urlKey = item?.url + '.html';
               await resolveUrl({ urlKey, navigation });
            }
          }}>
          <FastImage
            style={styles.imageSize}
            source={{uri: getImageUrl(item.image, 'product')}}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <Button
          onPress={() => navigation.navigate('OrderDetail', {item, type})}
          text={t('buttons.track')}
          type={'secondary'}
          labelColor="whiteColor"
          weight="500"
          labelSize="mx"
          radius="xms"
          size="extra-small"
          style={styles.btnStyle}
        />
      </View>
    </View>
  );
};

export default ReturnCard;

import React from 'react';
import {
  TouchableOpacity,
  View,
  SafeAreaView,
  TouchableWithoutFeedback,
  FlatList,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import Modal from 'react-native-modal';
import {ImageIcon, Label, ProductCardVertical, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {useMemo} from 'react';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';

type Props = {
  viewChildModule?: boolean;
  onClose?: (visible: boolean) => void;
};
const data = [
  {
    sku: 'WALXX00772',
    object_id: '14016',
    name: 'Waldent Dental Implant Handpiece 20:1 (W-772)',
    thumbnail_url:
      'https://images1.dentalkart.com/media/catalog/product//i/m/implant_handpiece_3__1.jpg',
    image_url:
      '//images1.dentalkart.com/media/catalog/product//i/m/implant_handpiece_3__1.jpg',
    is_in_stock: true,
    demo_available: 'No',
    manufacturer: 'Waldent',
    price: {
      minimalPrice: {
        amount: {
          value: 6990,
          currency: 'INR',
          currency_symbol: '₹',
        },
      },
      regularPrice: {
        amount: {
          value: 25000,
          currency: 'INR',
          currency_symbol: '₹',
        },
      },
    },
    rating_count: 1,
    rating: '5.00',
    short_description: 'Dental Contra Angle Handpiece 20:1',
    type_id: 'simple',
    url_key: '/waldent-implant-dental-handpiece-20-1.html',
    _queryID: null,
    _position: null,
  },
  {
    sku: 'WALXX00772',
    object_id: '14016',
    name: 'Waldent Dental Implant Handpiece 20:1 (W-772)',
    thumbnail_url:
      'https://images1.dentalkart.com/media/catalog/product//i/m/implant_handpiece_3__1.jpg',
    image_url:
      '//images1.dentalkart.com/media/catalog/product//i/m/implant_handpiece_3__1.jpg',
    is_in_stock: true,
    demo_available: 'No',
    manufacturer: 'Waldent',
    price: {
      minimalPrice: {
        amount: {
          value: 6990,
          currency: 'INR',
          currency_symbol: '₹',
        },
      },
      regularPrice: {
        amount: {
          value: 25000,
          currency: 'INR',
          currency_symbol: '₹',
        },
      },
    },
    rating_count: 1,
    rating: '5.00',
    short_description: 'Dental Contra Angle Handpiece 20:1',
    type_id: 'simple',
    url_key: '/waldent-implant-dental-handpiece-20-1.html',
    _queryID: null,
    _position: null,
  },
  {
    sku: 'WALXX00772',
    object_id: '14016',
    name: 'Waldent Dental Implant Handpiece 20:1 (W-772)',
    thumbnail_url:
      'https://images1.dentalkart.com/media/catalog/product//i/m/implant_handpiece_3__1.jpg',
    image_url:
      '//images1.dentalkart.com/media/catalog/product//i/m/implant_handpiece_3__1.jpg',
    is_in_stock: true,
    demo_available: 'No',
    manufacturer: 'Waldent',
    price: {
      minimalPrice: {
        amount: {
          value: 6990,
          currency: 'INR',
          currency_symbol: '₹',
        },
      },
      regularPrice: {
        amount: {
          value: 25000,
          currency: 'INR',
          currency_symbol: '₹',
        },
      },
    },
    rating_count: 1,
    rating: '5.00',
    short_description: 'Dental Contra Angle Handpiece 20:1',
    type_id: 'simple',
    url_key: '/waldent-implant-dental-handpiece-20-1.html',
    _queryID: null,
    _position: null,
  },
];
const ChildProductModal = ({viewChildModule, onClose}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View>
      {viewChildModule === true ? (
        <Modal
          onBackButtonPress={() => onClose?.(!viewChildModule)}
          isVisible={viewChildModule}
          animationIn="fadeIn"
          animationOut="fadeOut"
          animationInTiming={75}
          animationOutTiming={75}
          backdropOpacity={0.01}
          style={styles.modalStyle}>
          <SafeAreaView style={styles.flexContainer}>
            <View style={styles.centeredView}>
              <TouchableWithoutFeedback
                onPress={() => {
                  onClose?.(!viewChildModule);
                }}>
                <View style={styles.modalCloseBtnContainer}>
                  <TouchableOpacity
                    onPress={() => onClose?.(!viewChildModule)}
                    style={styles.modalCloseButton}>
                    <ImageIcon icon="close" size="x6l" />
                  </TouchableOpacity>
                </View>
              </TouchableWithoutFeedback>
              <View style={styles.viewProduct}>
                <View style={styles.offerZoneBannerView}>
                  <Spacer size="l" />

                  <View style={styles.offerBannerView}>
                    <View style={styles.offerBannerSubView}>
                      <ImageIcon icon="dis" size="xxl" />
                      <Spacer size="sx" type="Horizontal" />
                      <Label
                        color="text"
                        size="l"
                        weight="500"
                        text={'AVALIBALE OFFERS'}
                      />
                    </View>

                    <ImageIcon icon="Exclude" size="x4l" />
                  </View>
                  <Spacer size="xxl" />
                  <View style={styles.SubtotalView}>
                    <Label
                      color="text2"
                      size="l"
                      weight="500"
                      text={`Subtotal : `}
                    />
                    <Label
                      color="text"
                      size="l"
                      weight="500"
                      text={'₹120.00'}
                    />
                  </View>
                  <Spacer size="s" />
                  <View style={styles.SubtotalView}>
                    <Label
                      color="grey3"
                      size="mx"
                      weight="500"
                      text={'MRP - '}
                    />
                    <Label
                      color="grey3"
                      size="m"
                      weight="500"
                      text={'₹120.00'}
                      textDecorationLine="line-through"
                    />
                    <Spacer size="xx" type="Horizontal" />
                    <Label
                      color="green2"
                      size="m"
                      weight="500"
                      text={'38.39% OFF'}
                    />
                  </View>
                  <Spacer size="s" />
                  <View style={styles.itemView}>
                    <Label
                      color="categoryTitle"
                      size="mx"
                      weight="600"
                      text={'Item - 00'}
                    />
                    <View style={styles.detailsView}>
                      <Label
                        color="categoryTitle"
                        size="mx"
                        weight="500"
                        text={'View Details'}
                      />
                      <Spacer size="xx" type="Horizontal" />
                      <ImageIcon
                        icon="doubleArrowRight"
                        size="xxl"
                        tintColor="categoryTitle"
                      />
                    </View>
                  </View>
                  <Spacer size="s" />
                  <View style={styles.savingView}>
                    <View style={styles.savingSubView}>
                      <View style={styles.savingView}>
                        <Label
                          color="categoryTitle"
                          size="mx"
                          weight="600"
                          text={'₹ 179'}
                        />
                        <Label
                          color="green2"
                          size="mx"
                          weight="500"
                          text={'Add on Saving 3.24%  '}
                        />
                      </View>
                      <Spacer size="s" />
                      <Label
                        color="categoryTitle"
                        size="m"
                        weight="400"
                        text={'Buy 2 or above for ₹ 179 each'}
                      />
                    </View>
                    <Spacer size="m" type="Horizontal" />
                    <View style={styles.savingSubView}>
                      <View style={styles.savingView}>
                        <Label
                          color="categoryTitle"
                          size="mx"
                          weight="600"
                          text={'₹ 179'}
                        />
                        <Label
                          color="green2"
                          size="mx"
                          weight="500"
                          text={'Add on Saving 3.24%  '}
                        />
                      </View>
                      <Spacer size="s" />
                      <Label
                        color="categoryTitle"
                        size="m"
                        weight="400"
                        text={'Buy 2 or above for ₹ 179 each'}
                      />
                    </View>
                  </View>
                  <OptimizedFlatList
                    horizontal
                    data={data}
                    style={styles.productView}
                    keyExtractor={(_, i) => i.toString()}
                    renderItem={({item, index}) => (
                      <ProductCardVertical
                        index={index}
                        size="medium"
                        actionBtn={item?.action_btn}
                        imageWithBorder={true}
                        maxWidth={0.48}
                        item={item}
                        onCartPress={() => null}
                        onPressWishlist={() => null}
                        onPressShare={() => null}
                        freeProducts={[]}
                      />
                    )}
                  />
                </View>
              </View>
            </View>
          </SafeAreaView>
        </Modal>
      ) : null}
    </View>
  );
};

export default ChildProductModal;

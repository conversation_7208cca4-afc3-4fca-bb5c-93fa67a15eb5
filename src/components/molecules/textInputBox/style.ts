import {Sizes, Fonts} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      width: '100%',
    },
    input: {
      backgroundColor: colors.whiteColor,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      color: colors.text2,
      textAlign: 'auto',
    },
    rightPadding: {
      paddingRight: Sizes.xxxl,
    },
    borderStyle: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      // borderColor: colors.grey2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      color: colors.text2,
    },
    leftView: {
      height: Sizes.xl,
      width: Sizes.x26,
      marginRight: -8,
      marginLeft: 0,
    },
    leftIcons: {
      height: Sizes.xx,
      width: Sizes.xx,
      tintColor: colors.text,
    },
    content: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      color: colors.text2,
      flex: Sizes.x,
      paddingRight: Sizes.xms,
    },
    placeHolderStyle: {
      fonts: {
        regular: {
          fontFamily: Fonts.Regular,
          fontSize: Sizes.m,
        },
      },
      colors: {
        primary: colors.grey,
        onSurfaceVariant: colors.grey,
      },
    },

    countryCodeText: {
      fontSize: Sizes.mx,
      // marginLeft: 8,
      top:1,
      fontFamily: Fonts.Medium,
      color: colors.text2,
      textAlign: 'center',
      justifyContent: 'center',

    },
    countryCodeView: {
      justifyContent: 'center',
    },
    labelStyle: {
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
      color: colors.grey,
      backgroundColor: colors.whiteColor,
    },
    rightIcons: {
      height: Sizes.xxl,
      width: Sizes.x3l,
      marginRight: -Sizes.x,
    },
    nRightIcons: {
      height: Sizes.xl,
      width: Sizes.x3l,
      marginRight: -Sizes.x,
    },
    startStyle: {
      color: colors.red3,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
    },
  });

export default styles;

import Api from 'services';
import {apiRoute, apiEndpoints, subEndpoint} from 'config/apiEndpoint';
const API = new Api();

export const generateCart = async () => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}`;
  const resp = await API.post(url, {});
  return resp;
};
export const mergeCart = async (
  data: {
    destination_cart_id: string;
    buy_now: boolean;
  },
  guestCartId: string,
) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${guestCartId}/${subEndpoint.carts.merge}`;
  const resp = await API.post(url, data);
  return resp;
};
export const getCart = async (cartId: string) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}`;
  const resp = await API.get(url);
  return resp;
};
export const cartPaymentMethods = async (
  countryCode: string,
  postcode: string,
  cartAmount: number,
  cartWeight: number, //in grams
  isCodEligible: boolean,
) => {
  const url = `${apiRoute.utilityPy}${apiEndpoints.delivery}/${subEndpoint.delivery.cartPaymentMethods}?country_code=${countryCode}&postcode=${postcode}&cart_amount=${cartAmount}&cart_weight=${cartWeight}&is_cod_eligible=${isCodEligible}`;
  const resp = await API.get(url);
  return resp;
};
export const addToCartProduct = async (cartId: string, data: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.items}`;
  const resp = await API.post(url, data);
  return resp;
};
export const setCartAddress = async (cartId: string, data: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.addresses}`;
  const resp = await API.post(url, data);
  return resp;
};
export const updateCartItem = async (cartId: string, data: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.items}`;
  const resp = await API.put(url, data);
  return resp;
};

export const deleteCartItem = async (
  cartId: string,
  itemId: number,
  buyNow?: boolean,
) => {
  let url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.items}/${itemId}`;
  if (buyNow) {
    url = `${url}?buy_now=${buyNow}`;
  }
  const resp = await API.delete(url);
  return resp;
};
export const checkServiceAvailability = async (data: any) => {
  const url = `${apiRoute.utilityPy}${apiEndpoints.delivery}/${subEndpoint.delivery.productDeliveryOptions}`;
  const resp = await API.post(url, data);
  return resp;
};
export const shippingRates = async (countryCode: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${apiEndpoints.countries}/${countryCode}/${subEndpoint.carts.shippingRates}`;
  const resp = await API.get(url);
  return resp;
};
export const applyDiscountElement = async (cartId: string, data: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.applyDiscountElement}`;
  const resp = await API.post(url, data);
  return resp;
};
export const removeCouponFromCart = async (cartId: string, data: any) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.removeCoupon}`;
  const resp = await API.post(url, data);
  return resp;
};
export const getCouponList = async (cartId: string) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.coupons}`;
  const resp = await API.get(url);
  return resp;
};
export const getApplicableRewardsPoints = async (
  cartId: string,
  buyNow: boolean,
) => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${cartId}/${subEndpoint.carts.rewardPoints}?buy_now=${buyNow}`;
  const resp = await API.get(url);
  return resp;
};
export const getCustomerRegistration = async () => {
  const url = `${apiRoute.orderMg}${apiEndpoints.customerRegistrations}`;
  const resp = await API.get(url);
  return resp;
};
export const addNewRegId = async (data: any) => {
  const url = `${apiRoute.orderMg}${apiEndpoints.customerRegistrations}/`;
  const resp = await API.post(url, data);
  return resp;
};
export const deleteRegId = async (regId: any) => {
  const url = `${apiRoute.orderMg}${apiEndpoints.customerRegistrations}/${regId}/`;
  const resp = await API.delete(url);
  return resp;
};
export const updateRegId = async (regId: any, data: any) => {
  const url = `${apiRoute.orderMg}${apiEndpoints.customerRegistrations}/${regId}/`;
  const resp = await API.put(url, data);
  return resp;
};
export const getFreeProduct = async () => {
  const url = `${apiRoute.cart}${apiEndpoints.carts}/${subEndpoint.carts.amount}`;
  const resp = await API.get(url);
  return resp;
};

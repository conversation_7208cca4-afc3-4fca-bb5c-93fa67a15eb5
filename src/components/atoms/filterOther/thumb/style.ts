import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const THUMB_RADIUS = Sizes.m;

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    root: {
      width: THUMB_RADIUS * Sizes.xs,
      marginTop: Sizes.xs,
      height: THUMB_RADIUS * Sizes.xs,
      borderRadius: THUMB_RADIUS,
      borderWidth: Sizes.xs,
      shadowColor: colors.blackTransparent,
      shadowOffset: {
        width: 1,
        height: 1,
      },
      shadowOpacity: 0.2,
      shadowRadius: Sizes.xs,
      elevation: Sizes.xs,
    },
  });

export default styles;

import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    titleProduct: {
      backgroundColor: colors.background,
      padding: Sizes.xm,
      paddingHorizontal: Sizes.m,
    },
    image: {
      borderWidth: Sizes.x,
      padding: Sizes.sx,
      borderRadius: Sizes.xm,
      borderColor: colors.imageBorderColor,
      justifyContent: 'center',
    },
    imageSize: {
      width: Sizes.x9l,
      height: Sizes.x9l,
      overflow: 'hidden',
      borderRadius: Sizes.xm,
    },
    subReqBtn: {
      width: '100%',
      alignSelf: 'center',
      alignItems: 'center',
      borderRadius: Sizes.xms,
    },
    listView: {
      paddingHorizontal: Sizes.m,
      flex: Sizes.x,
    },
    itemView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    imageBox: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    cardView: {
      marginBottom: Sizes.xms,
      borderRadius: Sizes.xm,
      padding: Sizes.xm,
      backgroundColor: colors.background,
      flex: Sizes.x,
    },
    subCardView: {
      flexDirection: 'row',
      flex: Sizes.x,
    },
    nameView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      flex: Sizes.x,
    },
    savedView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    checkBoxView: {
      paddingRight: Sizes.xms,
    },
    checkBoxStyle: {
      height: Sizes.l,
      width: Sizes.l,
    },
    reqSave: {
      maxWidth: 55,
      marginHorizontal: 5,
    },
    editView: {
      paddingHorizontal: 5,
    },
    rightArrow: {
      transform: [{rotate: '180deg'}],
    },
    error: {
      color: colors.torchRed,
      fontSize: Sizes.m,
      marginTop: Sizes.s,
    },
    subView: {
      flex: Sizes.x,
    },
    flatListView: {
      flex: Sizes.x,
      backgroundColor: colors.background2,
    },
    buttonView: {
      backgroundColor: colors.whiteColor,
      padding: 15,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    flexOne: {
      flex: Sizes.x,
    },
    inputDescription: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      color: colors.text2,
      height: Sizes.exl,
      textAlignVertical: 'top',
      paddingBottom: Sizes.s,
      paddingTop: Sizes.xms,
    },
    addressMainView: {
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      height: Sizes.exl,
    },
    qtyView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    lineStyle: {
      backgroundColor: colors.grey2,
      height: Sizes.xx,
      width: Sizes.x,
      marginHorizontal: Sizes.xm,
    },
  });

export default styles;

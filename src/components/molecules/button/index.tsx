import React from 'react';
import {
  TouchableOpacity,
  TextProps,
  FlexAlignType,
  TextStyle,
  ViewProps,
  ActivityIndicator,
  Pressable,
  View,
} from 'react-native';
import {ImageIcon, Label, WithGradient} from 'components/atoms';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
import {withOnPressDebounce} from 'components/hoc';
import {LinearGradientProps} from 'react-native-linear-gradient';
import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';
import FastImage from 'react-native-fast-image';
import {useMemo} from 'react';

type Props = {
  text?: string;
  keyName?: string;
  filterCountText?: string;
  labelStyle?: TextProps['style'];
  disabled?: boolean;
  onPress?: () => void;
  type?: 'primary' | 'secondary' | 'bulk' | 'bordered' | 'ghost' | 'disabled';
  iconRight?: keyof typeof Icons;
  iconCenter?: keyof typeof Icons;
  filterCounts?: boolean;
  iconLeft?: keyof typeof Icons | null;
  fastImageLeft?: keyof typeof Icons | null;
  loading?: boolean;
  withDebounce?: boolean;
  ghost?: boolean;
  fastImageSize?: keyof typeof Sizes;
  style?: ViewProps['style'];
  fastImageStyle?: ViewProps['style'];
  filterCountStyle?: string;
  styleLeftIcon?: ViewProps['style'];
  iconStyle?: keyof typeof Icons | null;
  iconSize?: keyof typeof Sizes;
  sourceType?: string;
  source?: any;
  labelSize?: keyof typeof Sizes;
  radius?: keyof typeof Sizes;
  paddingHorizontal?: keyof typeof Sizes;
  iconCenterStyle?: ViewProps['style'];
  selfAlign?: FlexAlignType;
  weight?: TextStyle['fontWeight'];
  tintColor?: keyof Theme['colors'];
  leftIconColor?: keyof Theme['colors'];
  borderColor?: keyof Theme['colors'];
  labelColor?: keyof Theme['colors'];
  withGradient?: boolean;
  gradientColors?: (string | number)[];
  gradientStyle?: LinearGradientProps['style'];
  stopPropagation?: boolean;
  loaderColor?: string;
  size?:
    | 'extra-small'
    | 'small'
    | 'medium'
    | 'large'
    | 'extra-large'
    | 'zero-height';
};

export const RenderGradient = ({
  children,
  showGradient,
  gradientStyle,
  gradientColors = ['#6ACADD', '#B5D784'],
}: {
  children: React.ReactElement;
  showGradient?: boolean;
  gradientStyle?: LinearGradientProps['style'];
  gradientColors?: (string | number)[];
}) => {
  return showGradient ? (
    <WithGradient gradientColors={gradientColors} gradientStyle={gradientStyle}>
      {children}
    </WithGradient>
  ) : (
    children
  );
};

const Button = ({
  text,
  type = 'ghost',
  size = 'medium',
  paddingHorizontal = 'xm',
  radius,
  iconRight,
  iconLeft,
  fastImageLeft,
  iconCenter,
  disabled,
  onPress,
  selfAlign,
  loading,
  ghost,
  style,
  styleLeftIcon,
  fastImageStyle,
  labelStyle,
  iconStyle,
  iconSize,
  iconCenterStyle,
  withDebounce,
  tintColor,
  withGradient,
  gradientStyle,
  gradientColors = ['#6ACADD', '#B5D784'],
  filterCounts = false,
  filterCountText,
  filterCountStyle,
  borderColor,
  labelColor,
  labelSize,
  weight,
  keyName = 'key',
  leftIconColor = 'whiteColor',
  stopPropagation = false,
  loaderColor,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const getPaddingVertical = (value: string) => {
    switch (value) {
      case 'extra-small':
        return {paddingVertical: Sizes.xm};
      case 'small':
        return {paddingVertical: Sizes.xms};
      case 'medium':
        return {paddingVertical: Sizes.m};
      case 'large':
        return {paddingVertical: Sizes.mx};
      case 'extra-large':
        return {paddingVertical: Sizes.l};
      case 'zero-height':
        return {paddingVertical: 0};
      default:
        return {};
    }
  };

  const getTypeStyles = (types: string) => {
    switch (types) {
      case 'bordered':
        return {
          borderWidth: 1,
          borderColor: borderColor ? colors[borderColor] : colors.categoryTitle,
        };
      case 'primary':
        return {backgroundColor: colors.sunnyOrange4};
      case 'secondary':
        return {backgroundColor: colors.categoryTitle};
      case 'disabled':
        return {backgroundColor: colors.placeholderColor};
      case 'pressed':
        return {
          backgroundColor: colors.verifyPopupButton1,
          borderWidth: 1,
          borderColor: colors.categoryTitle,
        };
      default:
        return {};
    }
  };
  return (
    <Pressable 
      disabled={disabled} 
      pointerEvents="box-none" 
      style={({pressed}) => [
      pressed ? {opacity: 0.5} : {opacity: 1}, 
      ]}
      onPress={(event) => {
        if (stopPropagation) {
          event.stopPropagation(); // Prevent event bubbling
        }
        onPress?.();
      }}
      >
      <RenderGradient
        key={keyName}
        showGradient={withGradient}
        gradientColors={gradientColors}
        gradientStyle={[
          gradientStyle,
          radius && {borderRadius: Sizes[radius]},
        ]}>
        <View
          style={[
            getPaddingVertical(size),
            {
              paddingHorizontal: paddingHorizontal
                ? Sizes[paddingHorizontal]
                : Sizes.xm,
            },
            {borderRadius: radius ? Sizes[radius] : 0},
            {alignSelf: selfAlign ? selfAlign : 'center'},
            styles.bthStyle,
            getTypeStyles(type),
            style,
          ]}>
          {iconLeft && (
            <ImageIcon
              tintColor={tintColor ? tintColor : undefined}
              icon={iconLeft}
              resizeMode="contain"
              size={iconSize}
              style={[styles.iconLeft, styleLeftIcon, iconStyle]}
            />
          )}
          {fastImageLeft && (
            <FastImage
              source={Icons[fastImageLeft]}
              resizeMode="cover"
              style={[fastImageStyle]}
              tintColor={tintColor ? tintColor : undefined}
            />
          )}
          {iconCenter && (
            <ImageIcon
              tintColor={tintColor ? tintColor : undefined}
              icon={iconCenter}
              resizeMode="contain"
              size={iconSize}
              style={[iconCenterStyle, iconStyle]}
            />
          )}
          {loading ? (
            <ActivityIndicator size="small" color={loaderColor || colors.whiteColor} />
          ) : (
            <Label
              style={labelStyle}
              text={text}
              weight={weight ? weight : '500'}
              size={labelSize ? labelSize : 'm'}
              color={labelColor ? labelColor : 'categoryTitle'}
            />
          )}
          {iconRight && (
            <ImageIcon
              tintColor={tintColor ? tintColor : undefined}
              icon={iconRight}
              resizeMode="contain"
              size={iconSize}
              style={styles.iconRight}
            />
          )}
        </View>
      </RenderGradient>
    </Pressable>
  );
};

export default React.memo(Button);

import React, {useEffect, useMemo, useState, useCallback} from 'react';
import {
  View,
  FlatList,
  TextInput,
  SafeAreaView,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
} from 'react-native';
import {
  CustomRatingBar,
  Label,
  Spacer,
  ImageIcon,
  ReviewSuccessModal,
  ImagePickerDialog,
} from 'components/atoms';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {useDispatch, useSelector} from 'react-redux';
import {But<PERSON>, Header} from 'components/molecules';
import ErrorHandler from 'utils/ErrorHandler';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import FastImage from 'react-native-fast-image';
import {showErrorMessage} from 'utils/show_messages';
import {GetPresignedUrl, getReviews, saveReviews} from 'services/orders';
import {handleErrMsg, sDevice, mDevice} from 'utils/utils';
import {RatingReview} from 'skeletonLoader';
import {setLoading} from 'app-redux-store/slice/appSlice';

const RateReview = props => {
  const TAG = 'RateReviewScreen';
  const {colors} = useTheme();
  const dispatch = useDispatch();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [maxRating, setMaxRating] = useState([1, 2, 3, 4, 5]);
  const [selectRate, setSelectRate] = useState(0);
  const [lastSelectRate, setLastSelectRate] = useState(0);
  const [selectIndex, setSelectIndex] = useState();
  const [reviewSuccess, setReviewSuccess] = useState(false);
  const [imageVisible, setImageVisible] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const {orderId, reload} = props?.route.params;
  const [editReview, setEditReview] = useState([]);
  const [productReview, setProductReview] = useState([]);
  const [btnShow, setBtnShow] = useState(false);

  useEffect(() => {
    getProductReview();
  }, []);

  const getProductReview = async () => {
    setIsLoading(true);
    const {data, status} = await getReviews(orderId);
    setIsLoading(false);
    if (status) {
      const rate = data?.order_rating ? data?.order_rating : 0;
      setSelectRate(rate);
      setLastSelectRate(rate);
      const updatedData =
        data?.reviews?.length > 0
          ? data?.reviews?.map(item => ({
              ...item,
              title: item.title || '',
              media: Array.isArray(item.media) ? item.media : [],
            }))
          : [];
      setProductReview(updatedData);
      setEditReview(updatedData);
    } else {
      showErrorMessage(handleErrMsg(data));
    }
  };

  const onRateChange = (rating, index) => {
    setProductReview(prev => {
      const updated = [...prev];
      updated[index] = {...updated[index], rating};
      return updated;
    });
    if (selectIndex !== index) {
      setSelectIndex(index);
    }
  };

  const onChangeDes = (text, index) => {
    setProductReview(prev => {
      const updated = [...prev];
      updated[index] = {...updated[index], title: text};
      return updated;
    });
  };

  useEffect(() => {
    const checkUpdatedItems = async () => {
      const updatedItems = await getUpdatedItems(editReview, productReview);
      setBtnShow(updatedItems?.length > 0 || lastSelectRate !== selectRate);
    };
    checkUpdatedItems();
  }, [productReview, editReview, lastSelectRate, selectRate]);

  const getUpdatedItems = (originalArray, updatedArray) => {
    return updatedArray.filter((item, index) => {
      const original = originalArray[index];
      const isRateChanged = item?.rating !== original?.rating;
      const isDesChanged = item?.title.trim() !== original?.title.trim();
      const isPhotoChanged =
        item?.media?.length !== original?.media?.length ||
        (item?.media &&
          item?.media?.some((photoUrl, i) => photoUrl !== original?.media[i]));

      return isRateChanged || isDesChanged || isPhotoChanged;
    });
  };

  const onSubmitReview = async () => {
    const updatedItems = await getUpdatedItems(editReview, productReview);
    const reviewItem = updatedItems.map(review => ({
      product_id: review.product_id,
      source: Platform.OS,
      media: review.media,
      title: review.title,
      rating: review.rating,
    }));
    const obj = {
      items: reviewItem,
    };
    if (selectRate > 0) {
      obj['order_rating'] = selectRate;
    }
    if (reviewItem?.length > 0 || lastSelectRate !== selectRate) {
      dispatch(setLoading(true));
      const {data, status} = await saveReviews(orderId, obj);
      dispatch(setLoading(false));
      if (status) {
        setReviewSuccess(true);
        setTimeout(() => {
          setReviewSuccess(false);
          if (reload) {
            reload();
          }
          props.navigation.goBack();
        }, 3000);
      } else {
        showErrorMessage(handleErrMsg(data));
      }
    } else {
      showErrorMessage(t('rate.reviewChange'));
    }
  };

  const onSelectImage = async images => {
    setImageVisible(false);

    if (!images || (Array.isArray(images) && images.length === 0)) {
      showErrorMessage(t('otherText.noImage'));
      return;
    }

    try {
      const uploadImage = async image => {
        const {path, mime} = image;
        const fileName = path.substring(path.lastIndexOf('/') + 1);
        const formData = new FormData();
        formData.append('file', {
          uri: path,
          name: fileName,
          type: mime,
        });

        const {data, status} = await GetPresignedUrl(formData);
        if (status && data?.file_url) {
          return data.file_url;
        } else {
          showErrorMessage(handleErrMsg(data));
          return '';
        }
      };

      const imageArray = Array.isArray(images) ? images : [images];

      const uploadedUrls = [];
      for (const image of imageArray) {
        const url = await uploadImage(image);
        if (url) {
          uploadedUrls.push({url, type: 'image'});
        }
      }

      setProductReview(prev => {
        const updated = [...prev];
        const current = updated[selectIndex] || {};
        const existingPhotos = current.media || [];
        updated[selectIndex] = {
          ...current,
          media: [...existingPhotos, ...uploadedUrls],
        };
        return updated;
      });
    } catch (err) {
      console.error(err);
      showErrorMessage(err.message || t('validations.someThingWrong'));
    }
  };

  const onSelectImg = index => {
    setSelectIndex(index);
    setImageVisible(true);
  };

  const removeImg = (index, i) => {
    setProductReview(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        media: updated[index].media.filter((_, index) => index !== i),
      };
      return updated;
    });
  };

  const renderProductReview = (item, index) => {
    return (
      <View key={index}>
        <TouchableOpacity
          style={styles.pReviewView}
          onPress={() => setSelectIndex(selectIndex === index ? null : index)}>
          <View style={styles.pImg}>
            <ImageIcon
              size="x8l"
              source={item.image}
              sourceType="url"
              style={styles.imgStyle}
            />
          </View>
          <Spacer size="s" type="Horizontal" />
          <View style={styles.fOne}>
            <Label
              text={item.name}
              size="m"
              fontFamily="Medium"
              weight="500"
              color="text"
            />
            <Spacer size="xm" />
            <CustomRatingBar
              maxRating={maxRating}
              selectRate={item.rating || 0}
              onPress={rate => onRateChange(rate, index)}
            />
          </View>
        </TouchableOpacity>
        {selectIndex === index && (
          <View style={styles.otherView}>
            <TextInput
              testID="txtReview"
              style={styles.inputStyle}
              value={item.title}
              onChangeText={text => onChangeDes(text, index)}
              placeholder={t('rate.tellExp')}
              placeholderTextColor={colors.text2}
              allowFontScaling={false}
              multiline={true}
            />
            {item?.media?.length > 0 ? (
              <View style={styles.rowCenter}>
                {item?.media?.map((data, i) => {
                  return (
                    <View key={i} style={styles.selectImg}>
                      <ImageIcon
                        size="x9l"
                        source={data?.url}
                        sourceType="url"
                        style={styles.sImgStyle}
                      />
                      <TouchableOpacity
                        style={styles.cancelView}
                        onPress={() => removeImg(index, i)}>
                        <ImageIcon size="xm" icon="cancel" tintColor="red2" />
                      </TouchableOpacity>
                    </View>
                  );
                })}
                <TouchableOpacity
                  style={styles.addView}
                  onPress={() => onSelectImg(index)}>
                  <ImageIcon
                    size="xxl"
                    icon="gallery"
                    resizeMode="contain"
                    style={styles.sImgStyle}
                  />
                  <Spacer size="xs" />
                  <Label
                    text={t('buttons.add')}
                    size="m"
                    fontFamily="SemiBold"
                    weight="600"
                    color="text"
                  />
                </TouchableOpacity>
              </View>
            ) : (
              <Button
                onPress={() => onSelectImg(index)}
                type="bordered"
                labelSize="mx"
                radius="xms"
                selfAlign="flex-start"
                labelColor="text"
                borderColor="grey2"
                text={t('rate.addPhotos')}
                iconLeft="gallery"
                iconSize="xl"
                style={styles.btnStyle}
              />
            )}
          </View>
        )}
      </View>
    );
  };

  const itemSeparator = useCallback(() => {
    return <Spacer size="xm" />;
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.headerSection}>
        <ErrorHandler
          componentName={`${TAG} Header`}
          onErrorComponent={<View />}>
          <Header
            searchIcon
            customIcon={true}
            navigation={props.navigation}
            backButton={true}
            bagIcon={true}
            text={t('rate.ratingTitle')}
            useInsets
          />
        </ErrorHandler>
      </View>
      {isLoading ? (
        <RatingReview />
      ) : (
        <View style={styles.fOne}>
          <View style={styles.subView}>
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              style={styles.fOne}>
              <ScrollView keyboardShouldPersistTaps="always" bounces={false}>
                <View style={styles.orderReviewView}>
                  <View style={styles.fOne}>
                    <Label
                      color="text"
                      text={t('rate.orderExp')}
                      size={sDevice ? 'm' : mDevice ? 'mx' : 'l'}
                      fontFamily="SemiBold"
                      weight="600"
                    />
                    <Spacer size="s" />
                    <Label
                      color="text"
                      text={t('rate.withDentalkart')}
                      size="m"
                      fontFamily="Medium"
                      weight="500"
                    />
                    <Spacer size="xm" />
                    <CustomRatingBar
                      maxRating={maxRating}
                      selectRate={selectRate}
                      onPress={rate => setSelectRate(rate)}
                    />
                  </View>
                  <FastImage
                    source={Icons.deliveryBoy}
                    style={styles.gifStyle}
                    resizeMode="cover"
                  />
                </View>
                <Spacer size="l" />
                <Label
                  color="text"
                  text={t('rate.itemOrder')}
                  size="m"
                  fontFamily="SemiBold"
                  weight="600"
                />
                <Spacer size="xm" />
                <FlatList
                  data={productReview}
                  keyExtractor={(_, i) => i.toString()}
                  renderItem={({item, index}) =>
                    renderProductReview(item, index)
                  }
                  ItemSeparatorComponent={itemSeparator}
                />
                <Spacer size="ex1" />
                <Label
                  color="text2"
                  text={`"${t('rate.trust')}"`}
                  size="m"
                  fontFamily="Medium"
                  weight="500"
                  align="center"
                />
                <Spacer size="ex1" />
              </ScrollView>
            </KeyboardAvoidingView>
          </View>
          <View style={styles.btnContainer}>
            <Button
              text={t('buttons.submit')}
              onPress={() => (btnShow ? onSubmitReview() : {})}
              labelSize="mx"
              labelColor="whiteColor"
              radius="m"
              withGradient
              gradientColors={
                btnShow
                  ? [colors.softRed, colors.brightOrange, colors.softRed]
                  : [colors.lightGrey, colors.lightGrey, colors.lightGrey]
              }
              selfAlign="stretch"
              style={styles.saveStyle}
            />
          </View>
          {reviewSuccess && (
            <ReviewSuccessModal
              visible={reviewSuccess}
              onClose={() => setReviewSuccess(false)}
            />
          )}
          {imageVisible && (
            <ImagePickerDialog
              visible={imageVisible}
              onConfirm={(image: any) => onSelectImage(image)}
              onClose={() => setImageVisible(false)}
            />
          )}
        </View>
      )}
      <SafeAreaView style={styles.safeView} />
    </View>
  );
};

export default RateReview;

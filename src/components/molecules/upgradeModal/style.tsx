import {Fonts, Sizes} from 'common';
import {Dimensions, Platform, StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';
const {width, height} = Dimensions.get('window');

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    overlay: {
      flex: Sizes.x,
      backgroundColor: colors.blackTransparent,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    whiteBg: {
      backgroundColor: colors.whiteColor,
    },
    modalContainer: {
      width: width,
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
      overflow: 'hidden',
    },
    backgroundImage: {
      width: width,
      height: 400,
      // alignItems: 'center',
    },
    backgroundImageTablet: {
      width: width,
      // height: 700,
      // Adjust height or other properties for tablet
      height: height * 0.6, // Example: Take up 70% of the screen height
    },
    imageStyle: {
      // resizeMode: 'contain',
    },
    skipButton: {
      marginLeft: checkDevice() ? Sizes.xl : Sizes.m,
      height: Sizes.xxxl,
      width: Sizes.ex92,
      borderRadius: Sizes.xl,
      zIndex: Sizes.xms,
      backgroundColor: colors.whiteColor,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      top: checkDevice() ? 110 : Platform.OS === 'ios' ? 92 : 90,
    },
    contentWrapper: {
      alignItems: 'center',
    },
    title: {
      fontSize: Sizes.xxxl,
      fontWeight: '600',
      textAlign: 'center',
      fontFamily: Fonts.SemiBold,
    },
    titleTablet: {
      fontSize: Sizes.x6l,
    },
    upgradeButtonContainer: {
      minWidth: width - 30,
      borderRadius: Sizes.xm,
      overflow: 'hidden',
    },
    upgradeButton: {
      height: Sizes.x7l,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

export default styles;

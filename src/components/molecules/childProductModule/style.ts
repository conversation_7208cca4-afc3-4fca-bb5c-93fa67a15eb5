import {StyleSheet} from 'react-native';
import {Sizes} from 'common';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {margin: 0},
    flexContainer: {flex: Sizes.x},

    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    offerZoneBannerView: {
      backgroundColor: colors.background2,

      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      flex: Sizes.x,
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
    },

    offerBannerView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    offerBannerSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productView: {
      paddingVertical: Sizes.xm,
    },
    offerBannerImageView: {width: 105, height: Sizes.xxl},
    viewProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.sx + Sizes.x,
    },
    SubtotalView: {flexDirection: 'row'},
    itemView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    detailsView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    savingView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    savingSubView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xm,
      padding: Sizes.xm,
      flex: Sizes.x,
    },
  });

export default styles;

import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';
import {checkDevice} from 'utils/utils';

const {width} = Dimensions.get('window');

const CategoryDetailLoader = () => {
  const {colors} = useTheme();
  const tab = checkDevice();
  const boxWidth = (width - (tab ? 50 : 30)) / (tab ? 4 : 2);
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={2100}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x={10} y="20" rx="5" ry="5" width={width - 20} height="110" />
        <Rect x={10} y="140" rx="5" ry="5" width={width - 20} height="110" />

        <Rect x={10} y="265" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="265"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="270"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="270"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="630" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="630"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="630"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="630"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="990" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="990"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="990"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="990"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="1350" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="1350"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="1350"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="1350"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}

        <Rect x={10} y="1710" rx="5" ry="5" width={boxWidth} height="350" />
        <Rect
          x={boxWidth + 20}
          y="1710"
          rx="5"
          ry="5"
          width={boxWidth}
          height="350"
        />
        {tab && (
          <>
            <Rect
              x={boxWidth * 2 + 30}
              y="1710"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
            <Rect
              x={boxWidth * 3 + 40}
              y="1710"
              rx="5"
              ry="5"
              width={boxWidth}
              height="350"
            />
          </>
        )}
      </ContentLoader>
    </ScrollView>
  );
};

export default CategoryDetailLoader;

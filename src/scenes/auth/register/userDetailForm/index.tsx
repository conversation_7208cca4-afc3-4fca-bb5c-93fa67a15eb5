import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  Keyboard,
  Linking,
  FlatList,
  Pressable,
  TouchableOpacity,
  Text,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Formik} from 'formik';
import {Button, PhoneInputText} from 'components/molecules';
import stylesWithOutColor from './style';
import {
  Label,
  Spacer,
  CheckBox,
  WebViewModal,
  ImageIcon,
} from 'components/atoms';
import {useTranslation} from 'react-i18next';
import {RouteProp, useTheme} from '@react-navigation/native';
import {RootStackParamsList} from 'routes';
import OtpScene from 'components/organisms/otpScene';
import tokenClass from 'utils/token';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {useDispatch} from 'react-redux';
import {
  generateNewCart,
  getUserInfo,
  setIsLoggedIn,
} from 'app-redux-store/slice/appSlice';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {AnalyticsEvents} from 'components/organisms';
import {createCustomer, userLogin, verifyOTP} from 'services/auth';
import {registerValidationSchema} from 'utils/validationError';
import {
  termsAndConditions,
  privacyPolicy,
  supportEmail,
} from 'config/environment';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import { trackEvent } from 'components/organisms/appEventsLogger/FacebookEventTracker';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'RegisterUser'>;
};

const RegisterUserScene = ({navigation, route}: Props) => {
  const TAG = 'RegisterUserScreen';
  const {colors} = useTheme();
  const dispatch = useDispatch();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [otpScene, setOtpScene] = useState(false);
  const [passwords] = useState(null);
  const {t} = useTranslation();
  const [apiError, setApiError] = useState(null);
  const [isPasswordSecure, setIsPasswordSecure] = useState(true);
  const [selectedTnC, setSelectedTnC] = useState(true);
  const {source, refer, goBack} = route.params;
  const password = route.params?.password;
  const [type, setType] = useState('email');
  const [webViewModel, setWebViewModel] = useState(false);
  const [webLink, setWebLink] = useState('');

  const otp = route.params?.otp;
  const emailPhoneNumber = route?.params?.emailPhoneNumber;
  const sourceValue = route?.params?.emailPhoneNumberType;
  const [shouldShow, setShouldShow] = useState(true);

  const createNewUser = useCallback(async (values: any) => {
    dispatch(setLoading(true));
    let obj = {
      firstname: values.firstname.trim(),
      lastname: values.lastname.trim(),
      password: values.password.trim(),
      email: values.email.trim(),
      mobile: values.mobileNumber.trim(),
    };
    if (refer) {
      obj['referralCode'] = refer;
    }
    const {data} = await createCustomer(obj);

    dispatch(setLoading(false));
    if (data) {
      if (data.token) {
        setApiError(null);
        const token = data.token;
        await tokenClass.setToken(token);
        dispatch(setIsLoggedIn(true));
        await dispatch(generateNewCart());
        dispatch(getUserInfo());
        dispatch(setLoading(false));
        AnalyticsEvents(
          'SIGN_UP_EMAIL_PHONE',
          'Sign Up-Email/Phone',
          data,
          {},
          false,
        );
        trackEvent('COMPLETE_REGISTRATION',{
          registrationId: data?.id, 
          params: { cart_id: data?.cart?.cart_id }
        })
        showSuccessMessage(t('toastMassages.accountSuccess'));
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'Tab',
              state: {
                routes: [
                  {
                    name: 'Shop',
                  },
                ],
              },
            },
          ],
        });
      } else {
        setApiError(data?.message);
      }
    } else {
      setApiError(res?.message);
    }
  }, []);

  const [apiErrorOtp, setApiErrorOtp] = useState(null);
  const [isVerify, setIsVerify] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  const verifyOtp = useCallback(
    async (values: any) => {
      dispatch(setLoading(true));
      const {data, status} = await verifyOTP({
        recipient: emailPhoneNumber,
        action: 'signup',
        verification_type: 'otp',
        authentication_type: emailPhoneNumber.includes('@')
          ? 'email'
          : 'mobile',
        credential: values,
        new_password: '',
      });
      dispatch(setLoading(false));
      if (data && status) {
        setApiErrorOtp(null);
        navigation.navigate('RegisterUser', {
          emailPhoneNumber: emailPhoneNumber,
          emailPhoneNumberType: emailPhoneNumber.includes('@')
            ? 'email'
            : 'mobile',
          source: 'registerWithEmail',
          otp: values.otp,
          password: '',
          // refer: referCode,
        });
        showSuccessMessage(data?.message);
      } else {
        setApiErrorOtp(data?.message);
      }
    },
    [emailPhoneNumber],
  );
  const LoginWithOtpResend = useCallback(async () => {
    dispatch(setLoading(true));
    const {data, status} = await userLogin({
      recipient: emailPhoneNumber,
      action: 'signup',
      authentication_type: emailPhoneNumber.includes('@') ? 'email' : 'mobile',
    });
    dispatch(setLoading(false));
    if (data && status) {
      setShouldShow(true);
      showSuccessMessage(data?.message);
    } else {
      showErrorMessage(data?.message);
    }
  }, [emailPhoneNumber, dispatch]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  const openLink = (url: string) => {
    Linking.openURL(url);
  };

  const completeOtpTimer = () => {
    setShouldShow(false);
  };

  const back = () => {
    navigation.pop(3);
    // if (goBack) {
    //   goBack();
    // }
  };

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={['']}
        renderItem={() => (
          <>
            {otpScene ? (
              <ErrorHandler
                componentName={`${TAG} OtpScene`}
                onErrorComponent={<View />}>
                <OtpScene
                  emailPhoneNumber={emailPhoneNumber}
                  onSubmit={verifyOtp}
                  navigation={navigation}
                  apiErrorOtp={apiErrorOtp}
                  isVerify={isVerify}
                  onResend={LoginWithOtpResend}
                  shouldShow={shouldShow}
                  completeOtpTimer={completeOtpTimer}
                  onEditPress={() => setOtpScene(false)}
                  setApiErrorOtp={setApiErrorOtp}
                  source={
                    type === 'mobile'
                      ? 'registerWithMobile'
                      : 'registerWithEmail'
                  }
                />
              </ErrorHandler>
            ) : (
              <View style={styles.imageBackGround}>
                <Formik
                  initialValues={{
                    requiredEmail: source === 'registerWithEmail',
                    requiredPhone: source === 'registerWithMobile',

                    email: sourceValue === 'email' ? emailPhoneNumber : '',
                    mobileNumber:
                      sourceValue === 'mobile' ? emailPhoneNumber : '',
                    firstname: '',
                    lastname: '',
                    password: password ?? '',
                  }}
                  validationSchema={registerValidationSchema()}
                  onSubmit={createNewUser}>
                  {({handleSubmit, handleChange, values, errors, isValid}) => (
                    <>
                      <View style={styles.containerForm}>
                        <View style={styles.body}>
                          <Spacer type="Vertical" size="x6l" />
                          <View style={styles.title}>
                            <Label
                              text={t('registerForm.almostThere')}
                              color="text"
                              weight="500"
                              size="xl"
                            />
                            <TouchableOpacity onPress={() => back()}>
                              <ImageIcon
                                icon="cancel"
                                size="mx"
                                tintColor="text"
                              />
                            </TouchableOpacity>
                          </View>
                          <Spacer type="Vertical" size="xxl" />
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <PhoneInputText
                              testID="txtRegisterUserFirstName"
                              inputStyle={styles.inputView}
                              placeholderColor={colors.text}
                              style={styles.inputStyleView}
                              icon="newUser"
                              tintColor="text"
                              placeholder={String(
                                t('registerForm.firstName') + ' *',
                              )}
                              onChangeText={handleChange('firstname')}
                              value={values.firstname}
                              error={t(errors.firstname)}
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="m" />
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <PhoneInputText
                              testID="txtRegisterUserLastName"
                              inputStyle={styles.inputView}
                              placeholderColor={colors.text}
                              style={styles.inputStyleView}
                              tintColor="text"
                              icon="newUser"
                              placeholder={String(
                                t('registerForm.lastName') + ' *',
                              )}
                              onChangeText={handleChange('lastname')}
                              value={values.lastname || ''}
                              error={t(errors.lastname)}
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="m" />
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <PhoneInputText
                              testID="txtRegisterUserEmail"
                              inputStyle={styles.inputView}
                              placeholderColor={colors.text}
                              style={styles.inputStyleView}
                              tintColor="text"
                              icon="newmail"
                              placeholder={String(
                                t('registerForm.emailAddress') + ' *',
                              )}
                              onChangeText={handleChange('email')}
                              value={values.email}
                              editable={sourceValue == 'email' ? false : true}
                              error={t(errors.email)}
                              type={'email-address'}
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="m" />
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <PhoneInputText
                              testID="txtRegisterUserMobileNumber"
                              inputStyle={styles.inputView}
                              placeholderColor={colors.text}
                              style={styles.inputStyleView}
                              maxLength={10}
                              tintColor="text"
                              icon="newphone"
                              placeholder={String(
                                t('registerForm.phone') + ' *',
                              )}
                              onChangeText={handleChange('mobileNumber')}
                              value={values.mobileNumber || ''}
                              editable={sourceValue == 'mobile' ? false : true}
                              error={t(errors.mobileNumber)}
                              type="phone-pad"
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="m" />
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <PhoneInputText
                              testID="txtRegisterUserPassword"
                              inputStyle={styles.inputView}
                              placeholderColor={colors.text}
                              style={styles.inputStyleView}
                              tintColor="text"
                              onClear={() =>
                                isPasswordSecure && values.password.length > 0
                                  ? setIsPasswordSecure(false)
                                  : setIsPasswordSecure(true)
                              }
                              clearIcon={
                                isPasswordSecure ? 'eyeHideIcon' : 'eyeShowIcon'
                              }
                              tintColorIconRight={
                                values.password.length === 0 ? 'grey2' : 'text'
                              }
                              rightIconStyle={styles.eyeStyle}
                              icon="lock"
                              placeholder={String(
                                t('registerForm.password') + ' *',
                              )}
                              onChangeText={handleChange('password')}
                              value={values.password}
                              secureTextEntry={isPasswordSecure}
                              error={t(errors.password)}
                            />
                          </ErrorHandler>
                          <Spacer type="Vertical" size="l" />

                          <View style={styles.rowDirection}>
                            <ErrorHandler
                              componentName={`${TAG} CheckBox`}
                              onErrorComponent={<View />}>
                              <CheckBox
                                style={styles.checkBox}
                                value={selectedTnC}
                                onValueChange={value => setSelectedTnC(!value)}
                                selected={selectedTnC}
                              />
                            </ErrorHandler>
                            <Spacer size="m" type="Horizontal" />
                            <View style={styles.termsView}>
                              <Label
                                text={t('login.footerTerm&Con')}
                                size="m"
                                color="grayTextColor"
                              />
                              <Pressable
                                onPress={() => {
                                  setWebViewModel(true);
                                  setWebLink(termsAndConditions);
                                }}>
                                <Label
                                  textDecorationLine="underline"
                                  color="newPrimary"
                                  size="m"
                                  text={t('login.footerTermService')}
                                />
                              </Pressable>

                              <Label
                                text={t('login.and')}
                                size="m"
                                color="grayTextColor"
                              />
                              <Pressable
                                onPress={() => {
                                  setWebViewModel(true);
                                  setWebLink(privacyPolicy);
                                }}>
                                <Label
                                  color="newPrimary"
                                  textDecorationLine="underline"
                                  size="m"
                                  text={t('login.footerPolicy')}
                                />
                              </Pressable>
                            </View>
                          </View>

                          <Spacer type="Vertical" size="l" />
                          <View>
                            <ErrorHandler
                              componentName={`${TAG} Button`}
                              onErrorComponent={<View />}>
                              <Button
                                labelStyle={styles.buttonLabel}
                                radius="m"
                                text={t('profile.completeProfile')}
                                labelSize="mx"
                                onPress={handleSubmit}
                                type={
                                  selectedTnC &&
                                  isValid &&
                                  values.email &&
                                  values.mobileNumber &&
                                  values.firstname &&
                                  values.lastname &&
                                  values.password
                                    ? 'secondary'
                                    : 'disabled'
                                }
                                selfAlign="stretch"
                                labelColor="whiteColor"
                                disabled={!isValid || !selectedTnC}
                              />
                            </ErrorHandler>
                          </View>
                          <Spacer size="s" />
                          <View>
                            <Label
                              color="grey3"
                              align="center"
                              size="m"
                              text={t('login.needHelp')}>
                              <Label
                                onPress={() =>
                                  openLink(`mailto:${supportEmail}`)
                                }
                                color="newPrimary"
                                size="m"
                                text={t('login.supportEmail')}
                              />
                            </Label>
                          </View>
                        </View>
                      </View>
                      {webViewModel && (
                        <ErrorHandler
                          componentName={`${TAG} WebViewModal`}
                          onErrorComponent={<View />}>
                          <WebViewModal
                            visible={webViewModel}
                            url={webLink}
                            onClose={() => setWebViewModel(!webViewModel)}
                          />
                        </ErrorHandler>
                      )}
                    </>
                  )}
                </Formik>
              </View>
            )}
          </>
        )}
      />
    </SafeAreaView>
  );
};

export default RegisterUserScene;

import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import DashedLine from 'react-native-dashed-line';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {t} from 'i18next';
import {Label, ImageIcon, Spacer, WithGradient} from 'components/atoms';
import {RenderCustomHTML} from 'components/molecules';
import stylesWithOutColor from './style';
import {useMemo} from 'react';

type Props = {
  features: JSX.Element;
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  product: ProductData | null;
};

const ProductHighlights = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {features, navigation, product} = props;

  const renderersProps = {
    a: {
      onPress: () => {
        navigation.navigate(
          'ProductDescription',
          {productId: product?.product_id},
          {product: product},
        );
      },
    },
  };

  return (
    <View style={styles.highlightView}>
      <View style={styles.highlightSubView}>
        <Label
          text={t('PDP.productHighlights')}
          size="l"
          fontFamily="SemiBold"
          color="text"
        />
        <Spacer size="xm" />
      </View>
      <DashedLine
        dashLength={2}
        dashThickness={1}
        dashColor={colors.placeholderColor}
      />
      <View style={styles.featureView}>
        <Label
          text={t('PDP.features')}
          size="l"
          fontFamily="Medium"
          color="text2"
        />
        <Spacer size="xm" />
        <RenderCustomHTML
          html={features?.slice(0, 310) + '...'}
          tagsStyles={styles.tagStyle}
          renderersProps={renderersProps}
          contentWidth={300}
        />
      </View>
      <View style={styles.productDescriptionView}>
        <TouchableOpacity
          style={styles.productDesView}
          onPress={() => {
            navigation.navigate('ProductDescription', {
              productId: product?.product_id,
              product: product,
            });
          }}>
          <Label
            text={t('PDP.viewAllDetails')}
            size="l"
            fontFamily="Medium"
            color="newSunnyOrange"
            style={styles.detailsView}
          />
          <WithGradient
            gradientColors={[colors.mandyPink, colors.whiteColor]}
            gradientAngle={267}
            gradientStyle={styles.arrowBottomView}>
            <ImageIcon
              icon="arrowBottom"
              size="xl"
              style={{transform: [{rotate: '270deg'}]}}
            />
          </WithGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ProductHighlights;

import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    wrapper: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    bgImage: {
      width: Sizes.ex3l,
      height: Sizes.ex2l,
    },
    imageView: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyImage: {
      position: 'absolute',
      width: Sizes.ex1 + Sizes.sx,
      height: Sizes.ex1 + Sizes.sx,
    },
    titleStyle: {
      marginHorizontal: Sizes.x6l,
    },
    btnStyle: {
      alignSelf: 'center',
      height: Sizes.x7l,
      width: Sizes.ex180,
    },
    btnTxt: {
      fontFamily: Fonts.Medium,
      fontSize: Sizes.mx,
    },
  });

export default styles;

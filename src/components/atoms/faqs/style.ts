import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    questionAndAnswer: {
      marginTop: Sizes.s,
      padding: Sizes.s,
      borderTopWidth: 0.5,
      width: '95%',
      alignSelf: 'center',
      borderColor: colors.border,
    },
    paddingSpace: {
      paddingBottom: Sizes.s,
    },
    questionAndAnswerSubText: {
      width: '90%',
      paddingHorizontal: Sizes.s,
      flexDirection: 'row',
      color: colors.textLight,
    },
    answerText: {
      flexDirection: 'row',
      width: '90%',
    },

    likeDislikeView: {
      marginTop: Sizes.s,
    },

    likeImage: {
      transform: [{rotate: '180deg'}],
    },
    dislikeBtn: {
      paddingRight: Sizes.s,
    },
    alignRowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    likeCountBtn: {
      paddingRight: Sizes.s,
    },
    disLikeView: {
      paddingLeft: Sizes.l,
    },
    questionTextView: {
      flexDirection: 'row',
      width: '80%',
    },
    dateView: {
      marginTop: Sizes.s,
    },
    dateText: {
      marginLeft: 'auto',
    },
    spaceJustify: {justifyContent: 'space-between'},
    itemsEnd: {alignSelf: 'flex-end'},
  });

export default styles;

import React, {memo, useEffect, useState, useRef} from 'react';
import {View, TouchableOpacity, SafeAreaView, FlatList} from 'react-native';
import Modal from 'react-native-modal';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Spacer} from 'components/atoms';
import ImageViewer from 'react-native-image-zoom-viewer';
import getImageUrl from 'utils/imageUrlHelper';
import FastImage from 'react-native-fast-image';
import {useMemo} from 'react';
import Icons from 'common/icons';

type Props = {
  visible: boolean;
  onClose?: (visible: boolean) => void;
  setCarouselIndex: (index: number) => void;
  productImages: MediaGalleryEntry[];
  carouselIndex: number;
};

const FullSizeProductImagesModal = ({
  visible,
  onClose,
  productImages,
  carouselIndex,
  setCarouselIndex,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [imageIndex, setImageIndex] = useState(carouselIndex);

  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    setImageIndex(carouselIndex);
  }, [carouselIndex]);

  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex({
        index: imageIndex,
        animated: true,
        viewPosition: 0.5,
      });
    }
  }, [imageIndex]);

  return (
    <Modal
      onBackButtonPress={() => {
        onClose?.(!visible);
        setCarouselIndex(imageIndex);
      }}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <SafeAreaView style={styles.flexContainer}>
        <View style={styles.centeredView}>
          <TouchableOpacity
            onPress={() => onClose?.(!visible)}
            style={styles.modalCloseButton}>
            <ImageIcon icon="crossIcon" size="xxl" />
          </TouchableOpacity>
          <View style={styles.modelBg}>
            {productImages?.length > 0 ? (
              <ImageViewer
                backgroundColor={colors.background}
                index={imageIndex}
                onChange={setImageIndex}
                index={imageIndex}
                imageUrls={
                  productImages?.length
                    ? productImages.map(imageItem => ({
                        url: imageItem?.file ? getImageUrl(imageItem.file) : '',
                        props: {
                          source: imageItem?.file
                            ? { uri: getImageUrl(imageItem.file) }
                            : Icons.defaultImage, 
                        },
                      }))
                    : [
                        {
                          url: '',
                          props: {
                            source: Icons.defaultImage,
                          },
                        },
                      ]
                }
                enablePreload={true}
                useNativeDriver={true}
                enableSwipeDown={false}
                saveToLocalByLongPress={false}
                renderIndicator={() => <></>}
                renderArrowLeft={() => (
                  <View style={styles.p16}>
                    <ImageIcon
                      icon="arrowUp"
                      size="x4l"
                      style={{transform: [{rotate: '270deg'}]}}
                      tintColor="text"
                    />
                  </View>
                )}
                renderArrowRight={() => (
                  <View style={styles.p16}>
                    <ImageIcon
                      icon="arrowUp"
                      size="x4l"
                      style={{transform: [{rotate: '90deg'}]}}
                      tintColor="text"
                    />
                  </View>
                )}
                containerStyle={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
                style={{flex: 1, justifyContent: 'center'}}
                renderImage={(props) => {
                  return (
                    <FastImage
                      {...props}
                      style={{
                        ...props.style,
                        alignSelf: 'center'
                      }}
                    />
                  );
                }}
              />
            ) : null}
          </View>
          <Spacer size="s" />
          <View style={styles.alignItemsCenter}>
            <FlatList
              ref={flatListRef}
              showsHorizontalScrollIndicator={false}
              horizontal={true}
              data={productImages}
              keyExtractor={(_, index) => index?.toString()}
              renderItem={({item, index}) => {
                return (
                  <TouchableOpacity
                    style={[
                      styles.imageContainer,
                      index === imageIndex && styles.active,
                    ]}
                    onPress={() => {
                      setImageIndex(index);
                    }}>
                    <FastImage
                      resizeMode="contain"
                      style={styles.productImageSmall}
                      source={
                        item?.file
                          ? { uri: getImageUrl(item.file) }
                          : Icons.defaultImage
                      }
                    />
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default memo(FullSizeProductImagesModal);

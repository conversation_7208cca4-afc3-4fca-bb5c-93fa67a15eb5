import {Fonts, Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    continuerDirection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    subContinuerDirac: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: colors.background2,
      width: '100%',
      paddingHorizontal: Sizes.l,
    },
    modalComponent: {
      width: '60%',
      padding: Sizes.xm,
    },
    separator: {
      height: Sizes.xs,
      backgroundColor: colors.blackIcons,
    },
    listBg: {
      backgroundColor: colors.background,
    },
    center: {
      alignItems: 'center',
    },
    fRow: {
      flexDirection: 'row',
    },
    tabBtn: {
      width: DeviceWidth / Sizes.xs,
      paddingVertical: Sizes.m,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderLeftWidth: 0,
      borderRightWidth: 0,
    },
    gradient: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.xms,
      justifyContent: 'center',
      borderRadius: Sizes.s,
    },
    moreList: {
      width: '100%',
      paddingRight: Sizes.sx,
    },
    wishlistName: {
      height: 33,
      borderWidth: Sizes.x,
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.sx,
      paddingHorizontal: Sizes.xx,
      borderColor: colors.categoryTitle,
    },
    mainMore: {
      flexDirection: 'row',
      padding: Sizes.mx,
    },
    currentWishlist: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: Sizes.xms,
    },
    bgCategory: {
      borderColor: colors.newSunnyOrange,
      borderWidth: Sizes.x,
      overflow: 'hidden',
      borderRadius: Sizes.x26,
    },
    bgWhite: {
      backgroundColor: colors.background,
      overflow: 'hidden',
      borderRadius: Sizes.x26,
    },
    currentWishlistButton: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.x26,
      borderColor: colors.text,
      paddingHorizontal: Sizes.l,
      height: Sizes.x3l,
      alignItems: 'center',
      justifyContent: 'center',
    },
    cipLabel: {
      marginBottom: -Sizes.xs,
    },
    view: {
      marginHorizontal: Sizes.xms,
      flexShrink: Sizes.x,
    },
    footerView: {
      alignItems: 'center',
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingBottom: Sizes.xms,
    },
    modelInputView: {
      borderColor: colors.grey2,
      paddingVertical: 0,
    },
    modelInputBox: {
      color: colors.text2,
      fontSize: Sizes.m,
    },
    modelEditInputBox: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    labelColor: {
      color: colors.text2,
    },
    buttonText: {
      color: colors.whiteColor,
      textTransform: 'capitalize',
      marginTop: -Sizes.xs,
    },
    modelBtn: {
      height: Sizes.x7l,
      width: '100%',
      marginTop: Sizes.xms,
      alignSelf: 'center',
      borderRadius: Sizes.xms,
      backgroundColor: colors.categoryTitle,
    },
    sureView: {
      justifyContent: 'center',
      maxWidth: '64%',
      alignSelf: 'center',
    },
    radioView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    wishlistSubView: {paddingHorizontal: Sizes.xl},
    currentWishlistView: {
      paddingHorizontal: Sizes.xl,
      flexDirection: 'row',
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingBottom: Sizes.s,
    },
    listNameView: {
      paddingHorizontal: Sizes.xl,
      paddingTop: Sizes.mx,
    },
    sureWantView: {
      paddingHorizontal: Sizes.xl,
      paddingTop: Sizes.x60,
    },
    otherListRenderView: {
      paddingHorizontal: Sizes.xl,
      paddingTop: Sizes.xms,
    },
    topRightView: {
      paddingRight: Sizes.sx,
      flexDirection: 'row',
      justifyContent: 'flex-end',
      height: Sizes.x3l,
    },
    tabLabel: {
      fontFamily: Fonts.Medium,
    },
    flex: {
      flex: Sizes.x,
    },
    textCap: {
      textTransform: 'capitalize',
    },
    paddingH20: {
      paddingHorizontal: Sizes.xl,
    },
    a1: {
      position: 'relative',
      width: '100%',
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      justifyContent: 'center',
    },
    input: {
      height: Sizes.x7l,
      borderColor: colors.grey2,
      paddingVertical: 0,
    },
    rightText: {
      position: 'absolute',
      right: Sizes.l,
      fontSize: Sizes.l,
      color: 'gray',
    },
    lineStyle: {
      backgroundColor: colors.text,
      height: Sizes.l,
      width: Sizes.x,
    },
    statusStyle: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

stages:
  - build

variables:
  FASTLANE_SKIP_DOCS: "1"
  LC_ALL: "en_US.UTF-8"
  LANG: "en_US.UTF-8"
  BUNDLE_PATH: "vendor/bundle"

cache:
  key: ${CI_JOB_NAME}
  paths:
    - vendor/bundle

ios_build:
  stage: build
  tags:
    - saas-macos-medium-m1
  image: macos-15-xcode-16
  before_script:
    # Install Yarn 3 (Berry)
    - corepack enable
    - corepack prepare yarn@3.6.4 --activate
    - yarn --version
    - yarn install

    # Install Ruby gems
    - gem install bundler
    - bundle config set path "${BUNDLE_PATH}"
    - bundle install

    - cd ios
    - pod install
    
  script:
    - |
      if [ "$CI_COMMIT_REF_NAME" = "staging" ]; then
        fastlane deploy environment:production
      elif [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        fastlane deploy environment:production
      elif [ "$CI_COMMIT_REF_NAME" = "hotfix_release" ]; then
        fastlane deploy environment:production        
      fi
  rules:
    - if: '$CI_COMMIT_REF_NAME == "staging" && $CI_PIPELINE_SOURCE == "push"'
      when: always
    - if: '$CI_COMMIT_REF_NAME == "main" && $CI_PIPELINE_SOURCE == "push"'
      when: always
    - if: '$CI_COMMIT_REF_NAME == "hotfix_release" && $CI_PIPELINE_SOURCE == "push"'
      when: always      
    - when: never
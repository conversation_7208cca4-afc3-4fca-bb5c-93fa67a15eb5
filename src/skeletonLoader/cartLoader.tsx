import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const CartLoader = () => {
  const {colors} = useTheme();
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1750}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="10" y="5" rx="0" ry="0" width={width - 20} height="25" />
        <Rect x="12" y="36" rx="0" ry="0" width={width - 20} height="37" />
        <Rect x="12" y="81" rx="0" ry="0" width={width - 20} height="21" />
        <Rect x="12" y="111" rx="0" ry="0" width={width - 20} height="24" />
        <Rect x="12" y="147" rx="0" ry="0" width="125" height="118" />
        <Rect x="146" y="147" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="146" y="179" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="147" y="210" rx="0" ry="0" width="97" height="26" />
        <Rect x="254" y="210" rx="0" ry="0" width={width - 240} height="26" />
        <Rect x="149" y="240" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="12" y="276" rx="0" ry="0" width="125" height="118" />
        <Rect x="146" y="276" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="146" y="308" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="147" y="339" rx="0" ry="0" width="97" height="26" />
        <Rect x="254" y="339" rx="0" ry="0" width={width - 240} height="26" />
        <Rect x="149" y="369" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="11" y="404" rx="0" ry="0" width="125" height="118" />
        <Rect x="145" y="404" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="145" y="436" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="146" y="467" rx="0" ry="0" width="97" height="26" />
        <Rect x="255" y="469" rx="0" ry="0" width={width - 240} height="26" />
        <Rect x="148" y="497" rx="0" ry="0" width={width - 145} height="26" />
        <Rect x="10" y="530" rx="0" ry="0" width={width - 20} height="44" />
        <Rect x="9" y="582" rx="0" ry="0" width={width - 20} height="25" />
        <Rect x="9" y="617" rx="0" ry="0" width={width - 20} height="88" />
        <Rect x="11" y="714" rx="0" ry="0" width={width - 20} height="28" />

        <Rect x={10} y="750" rx="5" ry="5" width={150} height="300" />
        <Rect x={170} y="750" rx="5" ry="5" width={150} height="300" />
        <Rect x={330} y="750" rx="5" ry="5" width={150} height="300" />
        <Rect x={490} y="750" rx="5" ry="5" width={150} height="300" />
        <Rect x={650} y="750" rx="5" ry="5" width={150} height="300" />
        <Rect x={810} y="750" rx="5" ry="5" width={150} height="300" />

        <Rect x={10} y="1060" rx="5" ry="5" width={width - 20} height="300" />
        <Rect x={10} y="1370" rx="5" ry="5" width={width - 20} height="300" />
      </ContentLoader>
    </ScrollView>
  );
};

export default CartLoader;

import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {TextInputProps, TextStyle, View} from 'react-native';
import {Label, Spacer} from 'components/atoms';
import InputBox from '../input-box';
import {Sizes} from 'common';

type Props = {
  label: string;
  size?: keyof typeof Sizes;
  weight?: TextStyle['fontWeight'];
  color?: TextStyle['color'];
  style?: TextInputProps['style'];
  textStyle?: string;
  placeholderTextColor?: TextInputProps['placeholderTextColor'];
  secureTextEntry?: boolean;
  onSubmitEditing?: () => void;
  maxLength?: TextInputProps['maxLength'];
  keyboardType?: TextInputProps['keyboardType'];
  defaultValue?: TextInputProps['defaultValue'];
  fieldMainetry?: boolean;
} & InputBoxType;

const InputLabel = ({
  label,
  textStyle,
  fieldMainetry = false,
  size,
  maxLength = undefined,
  weight,
  style,
  color = 'textLight',
  placeholderTextColor = 'placeHoldersTextColor',
  secureTextEntry,
  onSubmitEditing,
  keyboardType = 'name-phone-pad',
  textInputColor,
  ...props
}: Props) => {
  return (
    <>
      <View style={[fieldMainetry && {flexDirection: 'row'}]}>
        <Label
          style={textStyle}
          text={label}
          size={size}
          weight={weight}
          color={color}
        />
        {fieldMainetry && <Label color="textError" text={'*'} />}
      </View>
      {/* <Spacer type="Vertical" size="xm" /> */}
      <InputBox
        textInputColor={textInputColor}
        keyboardType={keyboardType}
        maxLength={maxLength}
        placeholderTextColor={placeholderTextColor}
        secureTextEntry={secureTextEntry}
        onSubmitEditing={onSubmitEditing}
        style={style}
        {...props}
      />
    </>
  );
};

export default InputLabel;

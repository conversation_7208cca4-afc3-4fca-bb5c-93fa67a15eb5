import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    tagListView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    tagView: {
      borderColor: colors.categoryTitle,
      borderWidth: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: Sizes.sx,
      height: Sizes.x26,
      paddingHorizontal: Sizes.m,
      marginRight: Sizes.xm,
    },
    selectedTag: {
      backgroundColor: colors.categoryTitle,
    },
    labelStyle: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

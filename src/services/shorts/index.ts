import Api from 'services';
import {
  apiRoute,
  apiEndpoints,
  FEEDS_API_BASE_URL,
  subEndpoint,
} from 'config/apiEndpoint';

const API = new Api();

export const getShorts = async (
  page: number,
  pageLimit: number,
  sort?: string,
) => {
  const url = `${apiRoute.feed}${apiEndpoints.feeds}?order=desc&page=${page}&limit=${pageLimit}&sort=${sort}`;
  const resp = await API.get(url);
  return resp;
};

export const getFeedDetail = async (videoId?: string) => {
  const url = `${FEEDS_API_BASE_URL}${apiEndpoints.feeds}/${videoId}`;
  const resp = await API.get(url);
  return resp;
};

export const addFeedLike = async (videoId: string) => {
  const url = `${apiRoute.feed}${apiEndpoints.videos}/${videoId}/${subEndpoint.feeds.likes}`;
  const resp = await API.post(url, {});
  return resp;
};

export const addFeedsComment = async (data: any) => {
  const url = `${FEEDS_API_BASE_URL}${apiEndpoints.feedsComment}`;
  const resp = await API.post(url, data);
  return resp;
};

export const getFeedsComment = async (videoId?: string) => {
  const url = `${FEEDS_API_BASE_URL}${apiEndpoints.feedsComments}?video_id=${videoId}`;
  const resp = await API.get(url);
  return resp;
};

export const addFeedHistory = async (data?: any) => {
  const url = `${FEEDS_API_BASE_URL}${apiEndpoints.history}`;
  const resp = await API.post(url, data);
  return resp;
};

export const addFeedEngagement = async (data?: any) => {
  const url = `${FEEDS_API_BASE_URL}${apiEndpoints.engagement}`;
  const resp = await API.post(url, data);
  return resp;
};

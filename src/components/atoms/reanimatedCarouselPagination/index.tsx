import React, {useRef, useEffect, useMemo} from 'react';
import {View, FlatList} from 'react-native';
import {PaginationItem} from 'components/atoms';
import {Sizes} from 'common';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';

type Props = {
  activeIndex: number;
  dotsLength: number;
  paginationType: 'normal' | 'capsule';
  progress?: number;
  dotColor?: object;
  maxVisibleDots?: number;
  paginationSize: boolean;
  pdpSliderStyle?: boolean;
  pdpImageSliderDots?: boolean;
};

const ReanimatedCarouselPagination = ({
  dotsLength,
  activeIndex,
  dotColor,
  paginationType,
  progress,
  maxVisibleDots = 15,
  paginationSize = false,
  pdpSliderStyle = false,
  pdpImageSliderDots = false,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const flatListRef = useRef<FlatList>(null);
  
  // Calculate visible dots range
  const visibleDotsRange = useMemo(() => {
    let startIndex = Math.max(0, activeIndex - Math.floor(maxVisibleDots / 2));
    let endIndex = Math.min(dotsLength, startIndex + maxVisibleDots);
    
    // Adjust startIndex if we're at the end
    if (endIndex === dotsLength) {
      startIndex = Math.max(0, dotsLength - maxVisibleDots);
    }
    
    return Array.from(
      {length: Math.min(dotsLength, maxVisibleDots)}, 
      (_, i) => startIndex + i
    );
  }, [activeIndex, dotsLength, maxVisibleDots]);

  // Memoize scroll handler to prevent recreations
  const scrollToIndex = useMemo(() => 
    (index: number) => {
      if (flatListRef.current && index >= 0) {
        try {
          flatListRef.current.scrollToIndex({
            index: Math.min(index, visibleDotsRange.length - 1),
            animated: true,
            viewOffset: Sizes.screenWidth * 0.08,
          });
        } catch (error) {
          // Silently handle scroll errors
        }
      }
    }, 
  [visibleDotsRange.length]);

  // Handle scroll failures
  const onScrollToIndexFailed = useMemo(() => 
    (info: {index: number}) => {
      // Instead of logging, try to recover by scrolling to a valid index
      const validIndex = Math.min(info.index, visibleDotsRange.length - 1);
      setTimeout(() => {
        if (flatListRef.current) {
          flatListRef.current.scrollToIndex({
            index: Math.max(0, validIndex),
            animated: false
          });
        }
      }, 10);
    },
  [visibleDotsRange.length]);

  // Only scroll when activeIndex changes
  useEffect(() => {
    const localIndex = visibleDotsRange.indexOf(activeIndex);
    if (localIndex >= 0) {
      scrollToIndex(localIndex);
    }
  }, [activeIndex, scrollToIndex, visibleDotsRange]);

  // Memoize render item function
  const renderPaginationItem = useMemo(() => 
    ({item}) => (
      <PaginationItem
        index={item}
        activeIndex={activeIndex}
        customDotColor={dotColor}
        paginationType={paginationType}
        opacity={activeIndex !== item ? 0.5 : 1}
        progress={progress}
        paginationSize={paginationSize}
        pdpImageSliderDots={pdpImageSliderDots}
        dotsLength={dotsLength}
      />
    ), 
  [activeIndex, dotColor, paginationType, progress, paginationSize, pdpImageSliderDots, dotsLength]);
  
  // Memoize container style
  const containerStyle = useMemo(() => 
    pdpSliderStyle ? styles.pdpPaginationContainer : styles.paginationContainer, 
  [pdpSliderStyle, styles]);

  // Skip rendering if no dots
  if (!dotsLength || dotsLength <= 0) return null;

  return (
    <View style={containerStyle}>
      <FlatList
        data={visibleDotsRange}
        horizontal
        ref={flatListRef}
        showsHorizontalScrollIndicator={false}
        keyExtractor={item => item.toString()}
        onScrollToIndexFailed={onScrollToIndexFailed}
        style={{width: "100%"}}
        renderItem={renderPaginationItem}
        getItemLayout={(_, index) => ({
          length: 16, // Approximate width of each dot
          offset: 16 * index,
          index,
        })}
        initialNumToRender={Math.min(5, dotsLength)}
        maxToRenderPerBatch={3}
        windowSize={3}
        removeClippedSubviews={true}
      />
    </View>
  );
};

export default React.memo(ReanimatedCarouselPagination);
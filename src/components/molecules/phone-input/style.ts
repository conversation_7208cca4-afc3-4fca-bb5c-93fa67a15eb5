import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: Sizes.xms,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      flexDirection: 'row',
      borderColor: colors.grayTextColor,
    },
    countryCodeContainer: {
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'row',
      marginVertical: Sizes.m,
    },
    dashLine: {
      marginLeft: 5,
    },
    inputContainer: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: Sizes.sx,
    },
    input: {
      flex: Sizes.x,
      paddingLeft: Sizes.sx,
      fontSize: Sizes.mx,
      color: colors.text2,
      fontWeight: '400',
      fontFamily: Fonts.Regular,
      height: Sizes.x46,
      marginTop: Sizes.xs,
    },
    errorContainer: {
      marginTop: Sizes.s,
    },
    errorBorder: {
      borderColor: colors.border,
    },
    icons: {
      marginHorizontal: Sizes.s,
    },
    marginL2: {
      marginLeft: Sizes.s,
    },
    gradientView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
    },
    gradientSubView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

export default styles;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    container: {
      borderRadius: Sizes.xms,
    },
    removeModalView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xms,
      padding: Sizes.xm,
      margin: Sizes.xms,
    },
    removeModalSubView: {
      justifyContent: 'center',
      flexDirection: 'row',
    },
    productImgView: {
      borderRadius: Sizes.xm,
      borderColor: colors.placeholderColor,
      width: Sizes.exl,
      height: Sizes.exl,
      borderWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    removeImage: {
      width: Sizes.exl - Sizes.xs,
      height: Sizes.exl - Sizes.xs,
      borderRadius: Sizes.xm,
    },
    removeView: {
      flex: Sizes.xs,
    },
    borderBottomView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    deleteCartItemView: {
      justifyContent: 'space-around',
      flexDirection: 'row',
      paddingVertical: Sizes.xms,
    },
    deleteCartSubView: {
      flex: Sizes.x,
      alignItems: 'center',
    },
    wishlistView: {
      justifyContent: 'center',
      flexDirection: 'row',
      flex: Sizes.x,
    },
    outStock: {
      borderColor: colors.grey2,
      backgroundColor: colors.orient20,
      borderWidth: Sizes.x,
      height: Sizes.x4l,
      width: Sizes.ex90,
      borderRadius: Sizes.sx,
      paddingVertical: 0,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
    },
    labelBottom: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

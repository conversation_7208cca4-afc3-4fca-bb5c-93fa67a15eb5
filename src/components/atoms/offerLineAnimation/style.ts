import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    animatedText: {
      justifyContent: 'center',
      alignItems: 'center',
      flex: Sizes.x,
      paddingHorizontal: Sizes.xms,
      position: 'absolute',
    },
    linerColor: {
      height: Sizes.x4l,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      flexDirection: 'row',
    },
    celebrateGifView: {
      width: Sizes.x60,
      height: Sizes.x4l,
      position: 'absolute',
    },
    leftGif: {
      left: Sizes.sx,
    },
    rightGif: {
      right: Sizes.sx,
    },
  });
export default styles;

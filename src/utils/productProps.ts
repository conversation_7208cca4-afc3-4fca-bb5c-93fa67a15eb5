// import { ProductData } from "@types/local";

export const getProductCardProps = (item: ProductData) => ({
  actionBtn: item?.action_btn,
  skuId: item?.sku,
  inStock: item?.is_in_stock,
  productType: item?.type,
  maxSaleQty: item?.max_sale_qty,
  demoAvailable: item?.demo_available,
  msrp: item?.msrp,
  isBestSeller: item?.is_best_seller,
  image: item?.media?.mobile_image,
  name: item?.name,
  rewardPoint: item?.reward_points,
  description: item?.short_description,
  rating: (item?.rating === 'null' || item?.average_rating === null
    ? 0
    : Number(item?.rating) || Number(item?.average_rating)
  ).toFixed(1),
  ratingCount: item?.rating_count ? `(${item?.rating_count})` : '(0)',
  price: item?.price,
  sellingPrice: item?.selling_price,
  currencySymbol: item?.currency_symbol,
  discount: item?.discount?.label,
  item,
});

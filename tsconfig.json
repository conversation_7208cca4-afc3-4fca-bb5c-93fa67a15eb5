// prettier-ignore
{
  "extends": "@tsconfig/react-native/tsconfig.json",
  "compilerOptions": {

    /* Completeness */
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "react-native",
    "lib": ["es2017"],
    "moduleResolution": "node",
    "noEmit": true,
    "strict": true,
    "target": "esnext",
    "baseUrl": ".",
    "paths": {
      "*": ["./src/*"],
    },
    "typeRoots": [
      "./node_modules/@react-navigation/core/lib/typescript/src",
      "./node_modules/@types",
      "./src/@types",
    ]
  }
}

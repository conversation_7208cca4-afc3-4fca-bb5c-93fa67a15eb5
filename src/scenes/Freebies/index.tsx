import React from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {RouteProp} from '@react-navigation/native';
import {Header} from 'components/molecules';
import <PERSON>rror<PERSON>andler from 'utils/ErrorHandler';
import { View } from 'react-native';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'Freebies'>;
};

const Freebies = ({navigation}: Props) => {
  const TAG='Freebies'
  return (
    <SafeAreaView>
       <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
      <Header backButton navigation={navigation} text="Freebies" />
      </ErrorHandler>
    </SafeAreaView>
  );
};

export default Freebies;

import React from 'react';
import {
  TouchableOpacity,
  View,
  FlatList,
  TouchableWithoutFeedback,
} from 'react-native';
import Modal from 'react-native-modal';
import {useTheme} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {t} from 'i18next';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {Sizes} from 'common';
import {useMemo} from 'react';

type Props = {
  visible?: boolean;
  onClose?: any;
  infoIcon?: any;
  MRP?: number;
  sellinPrice?: number;
  deliveryStatusData?: any;
};
const convertErrorsToListItems = (errors: string[]) => {
  return errors.map(error => ({
    dot: true,
    text: error,
  }));
};
const DeliveryInfoModal = (props: Props) => {
  const {visible, onClose, infoIcon, deliveryStatusData, sellinPrice, MRP} =
    props;
  const {colors} = useTheme();
  const insets = useSafeAreaInsets();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const off = (100 - (sellinPrice * 100) / MRP).toFixed(2);
  const offPrice = (MRP * off) / 100;

  const renderList = data => {
    return (
      <FlatList
        keyExtractor={(item, index) => index.toString()}
        data={data}
        renderItem={({item, index}) => {
          return (
            <View
              key={index}
              style={[
                styles.textItem,
                !item?.dot && {paddingBottom: Sizes.s, marginLeft: Sizes.s},
              ]}>
              {item?.dot && (
                <Label
                  style={styles.dot}
                  size="l"
                  text="."
                  color="text2"
                  fontFamily="Medium"
                />
              )}
              <Spacer type="Horizontal" size="s" />
              <Label
                size="m"
                text={item?.text}
                color={item?.bold ? 'text' : 'text2'}
                fontFamily={item?.bold ? 'Bold' : 'Medium'}
              />
            </View>
          );
        }}
        // ItemSeparatorComponent={<Spacer size="xm" />}
      />
    );
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.3}
      style={styles.modalStyle}>
      <View style={styles.imageModel} pointerEvents="box-none">
        <View style={[styles.subImageView, {paddingBottom: insets.bottom}]}>
          <TouchableWithoutFeedback onPress={onClose}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={onClose}
                testID='tOModalCloseButton'
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x5l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <View style={styles.modelSubView}>
            {/* {deliveryStatusData?.service_availability?.[0]?.errors?.[0] ? (
              <View style={styles.mainViewReturn}>
                <Label
                  size="m"
                  text={
                    deliveryStatusData?.service_availability?.[0]?.errors?.[0]
                  }
                  color="text2"
                  fontFamily="Medium"
                />
              </View>
            ) : (
              <View />
            )} */}
            {infoIcon === 'codCheck' ? (
              renderList(
                deliveryStatusData?.service_availability?.[0]?.errors?.length > 0 ? convertErrorsToListItems(deliveryStatusData?.service_availability?.[0]?.errors) :
                [
                {dot: true, text: t('address.cod')},
                {dot: true, text: t('address.cod1')},
                {dot: true, text: t('address.cod2')},
              ])
            ) : infoIcon === 'deliveryInfo' ? (
              renderList([
                {dot: false, text: t('address.deliveryDays')},
                {dot: false, text: t('address.deliveryDays1')},
                {dot: false, text: t('address.deliveryDays2')},
                {dot: false, text: t('address.deliveryDays3')},
                {dot: false, text: t('address.deliveryDays4')},
                {dot: false, text: t('address.deliveryDays5')},
              ])
            ) : infoIcon === 'wrongPinCode' ? (
              renderList([
                {dot: false, text: t('address.wrongPin1')},
                {dot: false, text: t('address.wrongPin2')},
                {dot: false, text: t('address.wrongPin3')},
                {dot: false, text: t('address.wrongPin4')},
                {dot: false, text: t('address.wrongPin5')},
                {dot: false, text: t('address.wrongPin6')},
              ])
            ) : infoIcon === 'compliances' ? (
              renderList([
                {dot: true, bold: true, text: t('address.compliances')},
                {dot: false, text: t('address.compliancesSub')},
                {dot: true, bold: true, text: t('address.compliances1')},
                {dot: false, text: t('address.compliancesSub1')},
                {dot: true, bold: true, text: t('address.compliances2')},
                {dot: false, text: t('address.compliancesSub2')},
              ])
            ) : infoIcon === 'delivery' ? (
              renderList([
                {dot: true, text: t('address.deliveryPartner')},
                {dot: true, text: t('address.deliveryPartner1')},
                {dot: true, text: t('address.deliveryPartner2')},
              ])
            ) : infoIcon === 'phoneNumber' ? (
              renderList([
                {dot: false, bold: true, text: t('address.numInfo')},
                {dot: false, bold: false, text: t('address.numInfo1')},
              ])
            ) : infoIcon === 'earnCoin' ? (
              renderList([
                {dot: false, bold: false, text: t('orderListing.coinNote')},
              ])
            ) : infoIcon === 'expiryDate' ? (
              <View>
                <View style={styles.textItem}>
                  <Label
                    style={styles.dot}
                    size="l"
                    text="."
                    color="text2"
                    fontFamily="Medium"
                  />
                  <Spacer type="Horizontal" size="s" />
                  <Label
                    size="m"
                    text={t('PDP.expiryDateInfo')}
                    color="text2"
                    fontFamily="Medium"
                  />
                </View>
                <Spacer size="xm" />
                <View style={styles.textItem}>
                  <Label
                    style={styles.dot}
                    size="l"
                    text="."
                    color="text2"
                    fontFamily="Medium"
                  />
                  <Spacer type="Horizontal" size="s" />
                  <Label
                    size="m"
                    text={t('PDP.expiryInfo')}
                    color="text2"
                    fontFamily="Medium"
                  />
                </View>
              </View>
            ) : infoIcon === 'priceDetails' ? (
              <View style={styles.mainViewReturn}>
                <View style={styles.flex}>
                  <Label
                    size="l"
                    weight="500"
                    color="text"
                    text={t('cart.priceDetails')}
                  />
                  <Spacer size="xm" />
                  <View style={[styles.fRow, styles.priceView]}>
                    <Label
                      color="text2"
                      size="mx"
                      weight="400"
                      text={t('otherText.mrp')}
                    />
                    <Label
                      text={` ₹ ${MRP}`}
                      color="text"
                      size="mx"
                      weight="400"
                      textDecorationLine="line-through"
                    />
                  </View>
                  <Spacer size="s" />
                  <Label
                    color="text2"
                    size="m"
                    weight="400"
                    text={t('PDP.addedTax')}
                  />
                  <Spacer size="xm" />
                  <View style={styles.finalPriceView}>
                    <Label
                      color="text"
                      size="mx"
                      weight="400"
                      text={t('PDP.finalPrice')}
                    />
                    <Label
                      text={` ₹ ${sellinPrice}`}
                      color="text"
                      size="mx"
                      weight="400"
                    />
                  </View>
                  <View style={styles.priceOff}>
                    <Label
                      text={`${t('PDP.saveingtext1')} ${MRP - sellinPrice} (${
                        isNaN((100 - (sellinPrice * 100) / MRP).toFixed(2))
                          ? '0%'
                          : off + '%'
                      }) ${t('PDP.saveingtext2')}`}
                      color="green2"
                      size="mx"
                      weight="400"
                    />
                  </View>
                </View>
              </View>
            ) : (
              <View>
                {deliveryStatusData?.return_info?.message_arr?.map(
                  (item: any) => (
                    <View style={styles.mainViewReturn}>
                      <Spacer type="Horizontal" size="s" />
                      <Label
                        size="mx"
                        text={item}
                        color="text2"
                        fontFamily="Medium"
                      />
                    </View>
                  ),
                )}
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DeliveryInfoModal;

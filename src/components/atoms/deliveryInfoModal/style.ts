import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    imageModel: {
      backgroundColor: colors.blackTransparent,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    subImageView: {
      backgroundColor: colors.whiteColor,
      width: '100%',
      marginTop: '40%',
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    fRow: {flexDirection: 'row', justifyContent: 'space-between'},
    priceView: {
      borderTopWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingTop: Sizes.xm,
    },
    finalPriceView: {
      borderTopWidth: Sizes.x,
      borderColor: colors.grey2,
      borderStyle: 'dashed',
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: Sizes.xm,
    },
    priceOff: {
      borderTopWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingTop: Sizes.xm,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x6l,
    },
    modelSubView: {
      padding: Sizes.m,
      paddingBottom: Sizes.l,
    },
    mainViewReturn: {
      flexDirection: 'row',
    },
    textItem: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.l,
      alignItems: 'flex-start',
    },
    dot: {
      marginTop: Sizes.s,
      lineHeight: Sizes.m,
    },
  });

export default styles;

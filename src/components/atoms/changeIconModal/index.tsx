import React, { useMemo, useState } from 'react';
import { ImageIcon, Label } from "components/atoms";
import { 
    View, 
    Modal, 
    Text, 
    TouchableOpacity,
    Platform,
    NativeModules,
    Alert
} from "react-native";
import { changeIcon, resetIcon, getIcon } from 'react-native-change-icon';
import { useTheme } from '@react-navigation/native';
import stylesWithOutColor from './style';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import { setMembershipType } from 'app-redux-store/slice/appSlice';
import SuccessModal from '../memberSuccesModal/memberSuccess';
import { MEMBERSHIP_TYPE } from './constants';
import { t } from 'i18next';

type props = {
    modalVisible: boolean
    setModalVisible: React.Dispatch<React.SetStateAction<boolean>>
}

function ChangeIconModal({ modalVisible, setModalVisible }: props) {
    const { colors } = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
    const dispatch = useDispatch();
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const { RNChangeIcon } = NativeModules;

    const changeIconToPremium = () => {
        setModalVisible(false);
        dispatch(setMembershipType(MEMBERSHIP_TYPE.ANDROID_PREMIUM))
        setShowSuccessModal(true);

        setTimeout(() => {
            if(Platform.OS==='ios'){
                changeIcon(MEMBERSHIP_TYPE.IOS_PREMIUM)
            }else{
                Alert.alert(
                    t('iconUpdate.iconChange'),
                    t('iconUpdate.changeWarning'),
                    [
                        {
                            text: t('iconUpdate.ok'),
                            onPress: () => {
                                RNChangeIcon.changeIcon(MEMBERSHIP_TYPE.ANDROID_PREMIUM)
                            }
                        }
                    ]
                );
            }
        }, 1200);
    }
    
    const handleNo = async () => {
        setModalVisible(false);
        AsyncStorage.setItem('dk_icon', 'true');
        const currentIcon = await getIcon()
        dispatch(setMembershipType(MEMBERSHIP_TYPE.DEFAULT))

       // This logic may be needed in the future when we allow users 
       // to revert the app icon manually from the settings screen

        // setTimeout(() => {
        //     if(currentIcon!==MEMBERSHIP_TYPE.DEFAULT){
        //         if(Platform.OS === 'ios'){
        //             resetIcon()
        //         }else{
        //             RNChangeIcon.changeIcon(MEMBERSHIP_TYPE.DEFAULT)
        //         }
        //     }
        // }, 100);
    };

    const handleYes = async () => {
        AsyncStorage.setItem('dk_icon', 'true');        
        setTimeout(() => {
            changeIconToPremium();
        }, 100); 
    };

    return (
        <>
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        {/* Header */}
                        <Label
                            text={t('iconUpdate.premiumBegins')}
                            size="xl"
                            weight="600"
                            style={styles.modalTitle}
                        />
                        {/* Subtitle */}
                        <Label
                            text={t('iconUpdate.updgrageSubtitle')}
                            size="mx"
                            weight="400"
                            style={styles.modalSubtitle}
                        />
                        {/* Images Container */}
                        <View style={styles.imagesContainer}>
                            <View style={styles.imageWrapper}>
                                <ImageIcon
                                    resizeMode="contain"
                                    style={styles.icon}
                                    icon="defaultIcon"
                                />
                            </View>
                            
                            <View style={styles.arrowContainer}>
                                <Text style={styles.arrow}>→</Text>
                            </View>
                            
                            <View style={styles.imageWrapper}>
                                <ImageIcon
                                    resizeMode="contain"
                                    style={styles.icon}
                                    icon="premiumIcon"
                                />
                            </View>
                        </View>

                        {/* Buttons */}
                        <View style={styles.buttonsContainer}>
                            <TouchableOpacity 
                                style={[styles.button, styles.noButton]} 
                                onPress={handleNo}
                            >
                                <Text style={styles.noButtonText}>No</Text>
                            </TouchableOpacity>
                            
                            <TouchableOpacity 
                                style={[styles.button, styles.yesButton]} 
                                onPress={handleYes}
                            >
                                <Text style={styles.yesButtonText}>Yes</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>

            {/* Success Modal */}
            <SuccessModal
                modalVisible={showSuccessModal}
                // modalVisible={true}
                setModalVisible={setShowSuccessModal}
            />
        </>
    );
}

export default ChangeIconModal;
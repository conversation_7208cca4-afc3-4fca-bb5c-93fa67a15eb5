import {Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      marginBottom: - Sizes.xs,
    },
    brandsMainView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.l,
    },
    brandsSubView: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: Sizes.x,
    },
    brandView: {
      marginLeft: Sizes.l,
    },
    logoView: {
      alignItems: 'center',
      paddingTop: Sizes.s,
      marginRight: Sizes.xm,
    },
    circleBrand: {
      backgroundColor: colors.whiteColor,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      marginBottom: Sizes.m,
      borderColor: colors.grey2,
      height: checkDevice()
        ? Sizes.ex0
        : DeviceWidth > 320 && DeviceWidth <= 380
        ? Sizes.x9l
        : Sizes.x74,
      width: checkDevice()
        ? (DeviceWidth - 100) / 6
        : (DeviceWidth - 52) / (DeviceWidth <= 320 ? 4 : 5),
      alignItems: 'center',
      justifyContent: 'center',
    },
    imageCecile: {
      height: checkDevice() ? Sizes.exl : Sizes.x56,
      width: checkDevice() ? Sizes.ex110 : Sizes.x56,
      borderRadius: Sizes.m,
    },
    rightArrowStyle: {
      paddingHorizontal: Sizes.s,
    },
    titleStyle: {
      maxWidth: Sizes.ex,
    },
  });

export default styles;

import Icons from 'common/icons';
import React, {useCallback} from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {View, ViewProps} from 'react-native';
import Label from '../label';
import Spacer from '../spacer';
import ListView from '../listView';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {t} from 'i18next';
import Separator from '../separator';
import {productDummyImage} from 'utils/imageUrlHelper';
import {useMemo} from 'react';

type Props = {
  icon?: keyof typeof Icons;
  text?: string;
  numColumns?: number | undefined;
  horizontal?: boolean;
  rewardsPoints?: boolean;
  style?: ViewProps['style'];
  data: any;
  index?: number;
  TrackItems?: OrderDetailsV1;
  ListHeaderComponent?: React.ReactElement | undefined;
};

const PackageItem = ({TrackItems, index}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const renderItem = useCallback(({item, index}: any) => {
    return (
      <View key={index} style={[styles.durationsBox, styles.box]}>
        <View style={styles.imageBox}>
          <FastImage
            resizeMode="contain"
            onError={e => {
              e.target.uri = productDummyImage;
            }}
            style={styles.image}
            source={{
              uri: item?.image !== undefined ? item?.image : productDummyImage,
            }}
            // source={{
            //   uri: getImageUrl(item?.image),
            // }}
          />
        </View>

        <View style={styles.viewContinuer}>
          <Label style={styles.subHeading} text={item.name} />
          <Spacer size="xs" />
          <View style={styles.durationsBox}>
            <View style={styles.durationsBox}>
              <Label
                color="lightGray"
                size="m"
                weight="400"
                // style={styles.subHeading}
                text={t('packageItem.quantity')}
              />

              <Label
                style={styles.subHeading}
                text={' ' + item?.qty_ordered + '  '}
              />
            </View>

            <View style={styles.durationsBox}>
              <Label
                color="lightGray"
                size="m"
                weight="400"
                text={t('packageItem.unit')}
              />
              <Spacer type="Horizontal" size="xm" />
              <Label style={styles.subHeading} text={'₹' + item.price} />
            </View>
          </View>

          <Spacer size="xs" />
          <View style={styles.durationsBox}>
            <Label
              color="lightGray"
              size="m"
              weight="400"
              text={t('packageItem.total') + ' '}
            />
            <Spacer type="Horizontal" size="xm" />
            <Label
              style={styles.subHeading}
              text={'₹' + item?.price * item?.qty_ordered?.toFixed(2)}
            />
          </View>
          <Spacer size="s" />
          {item.rewards && (
            <View style={styles.durationsBox}>
              <Label
                color="lightGray"
                style={styles.subHeading}
                text={t('packageItem.rewards') + ' '}
              />
              <Spacer type="Horizontal" size="xm" />
              <Label
                style={styles.subHeading}
                text={item.rewards ? item.rewards : 0}
              />
            </View>
          )}
        </View>
      </View>
    );
  }, []);
  return (
    <View key={index}>
      <ListView
        keyExtractor={listViewKeyExtractor}
        data={TrackItems}
        renderItem={renderItem}
        ItemSeparatorComponent={
          <>
            <Spacer size="s" />
            <Separator color="smoothBlue" thickness="sx" />
            <Spacer size="s" />
          </>
        }
      />
    </View>
  );
};

export default PackageItem;

import React, {useMemo, memo, useState} from 'react';
import {View, Image, Text, Platform} from 'react-native';
import {TextInput as Input} from 'react-native-paper';
import {Label, Spacer} from 'components/atoms';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import Icons from 'common/icons';

const TextInputBox = ({
  errorText,
  description,
  errorStyle,
  countryCode = '',
  autoFocus = false,
  allowOnlyAlphabets = false,
  rightIcons = true,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  // Function to filter input for alphabets and spaces only
  const handleTextChange = text => {
    if (allowOnlyAlphabets) {
      // Remove any character that is not a letter or space
      const filteredText = text.replace(/[^a-zA-Z\s]/g, '');
      props.onChangeText && props.onChangeText(filteredText);
    } else {
      props.onChangeText && props.onChangeText(text);
    }
  };

  // iOS specific props to disable password suggestions
  const iosPasswordProps =
    Platform.OS === 'ios' && props.secureTextEntry
      ? {
          textContentType: 'none',
          autoComplete: 'off',
          autoCorrect: false,
          spellCheck: false,
        }
      : {};

  // Performance optimization props
  const performanceProps = {
    maxFontSizeMultiplier: 1.5, // Prevents text scaling issues
    returnKeyType: props.returnKeyType || 'done',
    blurOnSubmit: props.blurOnSubmit !== false,
    enablesReturnKeyAutomatically: true,
  };

  return (
    <View style={styles.container}>
      <Input
        style={[
          styles.input,
          props?.inputStyle,
          !isFocused && props?.rightIcon && styles.rightPadding,
        ]}
        contentStyle={[
          styles.content,
          {
            marginLeft: props?.leftIcon || countryCode ? 50 : 16,
            marginRight: isFocused && props?.value?.length > 0 ? 30 : 0,
          },
          props?.containerStyle,
        ]}
        underlineColor="transparent"
        mode="outlined"
        activeOutlineColor={colors.text}
        outlineColor={colors.grey2}
        outlineStyle={styles.borderStyle}
        underlineStyle={styles.borderStyle}
        placeholderTextColor={colors.text2}
        autoCapitalize="none"
        autoFocus={autoFocus}
        theme={styles.placeHolderStyle}
        onFocus={() => setIsFocused(true)}
        onBlur={() => [setIsFocused(false), props.onBlur]}
        left={
          props?.leftIcon ? (
            <Input.Icon
              icon={() => (
                <Image
                  source={props?.leftIcon}
                  resizeMode="contain"
                  style={[styles.leftIcons, props.leftIconsStyle]}
                  style={[styles.leftIcons, props.leftIconsStyle]}
                />
              )}
              style={styles.leftView}
              forceTextInputFocus={false}
              onPress={() => (props?.leftPress ? props?.leftPress() : {})}
            />
          ) : countryCode ? (
            <Input.Icon
              icon={() =>
                countryCode ? (
                  <Text style={styles.countryCodeText}>{countryCode}</Text>
                ) : null
              }
              forceTextInputFocus={false}
              style={styles.countryCodeView}
            />
          ) : (
            <View />
          )
        }
        right={
          (rightIcons && isFocused && props?.value?.length > 0) ||
          props?.rightIcon ? (
            <Input.Icon
              icon={() => (
                <Image
                  source={props?.rightIcon || Icons.closeIcons}
                  resizeMode="contain"
                  style={[
                    props?.rightIcon ? styles.nRightIcons : styles.rightIcons,
                    props.rightStyle,
                  ]}
                />
              )}
              style={[
                props?.rightIcon ? styles.nRightIcons : styles.rightIcons,
                props.rightIconsStyle,
              ]}
              forceTextInputFocus={false}
              onPress={() =>
                props?.onRightIconPress
                  ? props?.onRightIconPress()
                  : props?.onChangeText('')
              }
            />
          ) : (
            <View />
          )
        }
        {...props}
        onChangeText={handleTextChange}
        {...iosPasswordProps}
        {...performanceProps}
        label={
          <Text style={[styles.labelStyle, props.labelStyle]}>
            {props.label}
            {props?.fieldMandatory && (
              <Text style={[styles.startStyle, props.labelStyle]}> *</Text>
            )}
          </Text>
        }
      />
      {description && !errorText ? (
        <>
          <Spacer size="s" />
          <Label
            text={String(t(description) || '')}
            size="m"
            weight="400"
            color="text"
          />
        </>
      ) : null}
      {errorText ? (
        <View style={errorStyle ? errorStyle : {}}>
          <Spacer size="s" />
          <Label
            text={String(t(errorText) || '')}
            size="m"
            weight="400"
            color="red3"
          />
        </View>
      ) : null}
    </View>
  );
};

export default memo(TextInputBox);

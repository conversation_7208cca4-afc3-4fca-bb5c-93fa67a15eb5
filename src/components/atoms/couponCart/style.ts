import {Sizes, Fonts} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    applyCouponView: {
      marginHorizontal: Sizes.xm,
      paddingVertical: Sizes.xms,
      borderRadius: Sizes.mx,
      borderColor: colors.grey8,
      borderWidth: Sizes.x,
      backgroundColor: colors.background,
    },
    subApplyCouponView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginHorizontal: Sizes.xms,
    },
    brandTagView: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: Sizes.s,
      justifyContent: 'space-between',
    },
    brandView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    couponCodeView: {
      flex: Sizes.x,
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
      borderColor: colors.skyBlue5,
      backgroundColor: colors.skyBlue6,
      flexDirection: 'row',
      paddingHorizontal: Sizes.xms,
      height: Sizes.x6l,
      alignItems: 'center',
      gap: Sizes.m,
    },
    couponAppliedLabelContainer: {
      position: 'absolute',
      top: -Sizes.m,
      left: '10%',
      zIndex: 20,
      backgroundColor: '#E7F6F8',
      paddingHorizontal: Sizes.m,
    },
    couponAppliedLabel: {
      fontSize: Sizes.m,
      // textAlign: 'center',
    },

    extraView: {
      paddingHorizontal: Sizes.sx,
      alignItems: 'center',
      flexDirection: 'row',
    },
    couponCodeMainView: {
      height: Sizes.x7l,
      width: Sizes.ex1,
      resizeMode: 'contain',
    },
    couponCodeSubView: {
      height: Sizes.x4l,
      width: Sizes.exl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    couponMainView: {
      paddingHorizontal: Sizes.xms,
    },
    couponView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    fRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    offerOffView: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    flexView: {
      flex: Sizes.x,
    },
    couponCodeMainViewApplied: {
      height: Sizes.xxl,
      width: Sizes.exl,
      resizeMode: 'contain',
    },
    couponCodeSubViewApplied: {
      height: Sizes.xxl,
      width: Sizes.exl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    btnLabel: {
      fontFamily: Fonts.Medium,
    },
    selectCoupon: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: Sizes.xs,
    },
    couponApplied: {
      flex: Sizes.x,
      position: 'relative',
    },
    couponInput: {
      flex: Sizes.x,
      color: colors.grey,
    },
  });

export default styles;

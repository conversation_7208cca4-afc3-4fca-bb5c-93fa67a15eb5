import {Fonts, Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {Platform, StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    screenContainer: {
      flex: Sizes.x,
    },
    subView: {
      flex: Sizes.x,
      marginHorizontal: Sizes.m,
    },
    itemContainer: {
      borderRadius: Sizes.xm,
      width: (DeviceWidth - Sizes.x5l) / Sizes.xs,
      height: Sizes.ex270 - Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    notFound: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: Sizes.x,
    },
    bannerImage: {
      width: '100%',
      height: Sizes.ex0 - Sizes.s,
      borderRadius: Sizes.m,
    },
    titlesView: {
      position: 'absolute',
      bottom: Sizes.xm,
      paddingHorizontal: Sizes.m,
    },
    textCap: {
      textTransform: 'capitalize',
    },
    reelIcon: {
      position: 'absolute',
      right: Sizes.xms,
      bottom: Sizes.ex,
    },
    gradient: {
      position: 'absolute',
      zIndex: Sizes.s,
      bottom: Platform.OS === 'android' ? 0 : Sizes.exl,
      width: '100%',
      height: Sizes.ex2l,
      borderRadius: Sizes.xm,
    },
    img: {
      width: '100%',
      height: '100%',
      borderRadius: Sizes.xm,
    },
    notFounds: {
      width: Sizes.exl,
    },
    filterBtnLabel: {
      color: colors.text,
      fontSize: Sizes.l,
      fontFamily: Fonts.Regular,
    },
    filterIcon: {
      height: Sizes.xxl,
      width: Sizes.xxl,
    },
    line: {
      flex: Sizes.x,
      height: Sizes.x,
      color: colors.grey2,
      backgroundColor: '#ccc',
    },
    text: {
      paddingHorizontal: Sizes.sx,
    },
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: Sizes.m,
    },
    listView: {
      marginBottom: -Sizes.xms,
      marginVertical: -Sizes.xl,
    },
    flex: {
      flex: Sizes.x,
    },
    videoFList: {
      justifyContent: 'space-between',
    },
    filterView: {
      backgroundColor: colors.white1,
      flexDirection: 'row',
      alignItems: 'center',
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: Sizes.xs + Sizes.x,
    },
    filterBox: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      width: DeviceWidth / Sizes.xs,
      paddingVertical: Sizes.mx,
    },
    lineView: {
      backgroundColor: colors.grey2,
      height: Sizes.l,
      width: Sizes.x,
    },
    fCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    filterSelectText: {
      // marginLeft: Sizes.x26,
      textTransform: 'capitalize',
    },
    loaderStyle: {
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: Sizes.xms,
    },
    sortBox: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      height: Sizes.x46,
      width: Sizes.ex190,
      borderRadius: Sizes.xm,
      borderColor: colors.grey,
      borderWidth: Sizes.x,
    },
  });
export default styles;

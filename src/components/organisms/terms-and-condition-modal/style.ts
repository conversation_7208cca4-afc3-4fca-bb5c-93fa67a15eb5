import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    closeContainer: {
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      width: Sizes.xxl,
      height: Sizes.xxl,
      borderRadius: Sizes.m,
      position: 'absolute',
      zIndex: Sizes.m,
      alignSelf: 'center',
    },
    directionRow: {
      flexDirection: 'row',
    },
    verticalAlignCenter: {
      alignItems: 'center',
      paddingVertical: Sizes.xms,
      flexDirection: 'row',
    },
    subHeading: {
      fontSize: Sizes.l,
      fontWeight: '700',
      paddingVertical: Sizes.s,
      color: colors.text2,
    },
    body: {
      fontSize: Sizes.mx,
      flex: Sizes.x,
      color: colors.text2,
    },
    listStyle: {
      fontSize: Sizes.mx,
      color: colors.text2,
    },
    withPadding: {
      paddingVertical: Sizes.s,
    },
    paddingH: {
      paddingHorizontal: Sizes.l,
    },
    flex: {
      flex: Sizes.x,
    },
    back: {
      paddingHorizontal: Sizes.l,
    },
    terms: {
      color: colors.blue,
    },
  });

export default styles;

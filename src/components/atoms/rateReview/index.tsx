import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {t} from 'i18next';
import {
  Label,
  ImageIcon,
  Spacer,
  Separator,
  Tag,
  LikeDisLikeButton,
} from 'components/atoms';
import {dateTimeFormat} from 'utils/formatter';
import stylesWithOutColor from './style';
import {useMemo} from 'react';

type Props = {
  faqLoading?: boolean;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  review: ProductReviewResponse | any;
  rateProductBtn: () => void;
  viewAllReview: () => void;
};

const RateReview = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {review, rateProductBtn, viewAllReview, faqLoading, navigation} = props;

  return (
    <View style={styles.ratingsView}>
      <View style={styles.reviewView}>
        <View style={styles.ratingMainView}>
          <View style={styles.ratingSubView}>
            <Label
              text={t('PDP.ratingsReviews')}
              size="l"
              fontFamily="SemiBold"
              color="text"
            />
            <Spacer size="xm" />
            <View style={styles.avgRatingView}>
              <Label
                text={Number(
                  review?.review_meta?.average_rating ?? 0.0,
                ).toFixed(1)}
                size="mx"
                fontFamily="Regular"
                color="text"
              />
              <Spacer type="Horizontal" size="xm" />
              <ImageIcon size="l" icon="starFill" tintColor="green" />
              <Spacer type="Horizontal" size="xm" />
              <Separator Vertical color="grey2" height="l" />
              <Spacer type="Horizontal" size="xm" />
              <Label
                text={`${review?.review_meta?.total_reviews ?? 0} ${t(
                  'PDP.reviews',
                )}`}
                size="mx"
                fontFamily="Regular"
                color="text"
              />
            </View>
          </View>
          <View style={styles.rateProdView}>
            <TouchableOpacity
              style={styles.rateProductView}
              onPress={() => rateProductBtn()}>
              <Label
                text={t('PDP.rateProduct')}
                color="newSunnyOrange"
                size="mx"
                fontFamily="Medium"
              />
            </TouchableOpacity>
            <Spacer size="s" />
            <Label
              text={`${!!review?.review_meta?.verified_buyers || 0} ${t(
                'PDP.verifiedBuyers',
              )}`}
              size="mx"
              fontFamily="Medium"
              color="text"
              align="center"
            />
          </View>
        </View>
        {!!review?.review_meta?.total_reviews > 0 ? (
          <>
            <Spacer size="m" />
            <Separator color="grey2" />
            {/* <Spacer size="m" />
            <Label
              text={`${t('PDP.customerPhotos')} (46)`}
              size="mx"
              fontFamily="SemiBold"
              color="text2"
            />
            <Spacer size="m" />
            <View style={styles.fRow}>
              {Array.from({length: 3}, (v, i) => i).map(i => (
                <View key={i.toString()} style={styles.priceIcon}>
                  <ImageIcon
                    icon="underPrizeIcon"
                    size="x56"
                    style={styles.reviewImageView}
                  />
                </View>
              ))}
              <View>
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('CustomerPhotosScene');
                  }}
                  style={styles.underPrizeIconView}>
                  <Label
                    text="43+"
                    color="background"
                    size="mx"
                    fontFamily="SemiBold"
                  />
                </TouchableOpacity>
                <ImageIcon
                  icon="underPrizeIcon"
                  size="x56"
                  style={styles.reviewImageView}
                />
              </View>
            </View> */}
            <Spacer size="m" />
            <Label
              text={`${t('PDP.customerReview')} (${
                review?.review_meta?.total_reviews || 0
              })`}
              size="mx"
              fontFamily="SemiBold"
              color="text2"
            />
            <View style={styles.ratingView}>
              <View style={styles.reviewRate}>
                <Tag
                  style={styles.starIconView}
                  label={(review?.reviews?.[0]?.rating || 0).toFixed(1)}
                  icon="starIcon"
                  color="green2"
                  isRightIcon
                />
                <Spacer size="xm" type="Horizontal" />
                <Label
                  text={review?.reviews?.[0]?.title}
                  size="mx"
                  fontFamily="Regular"
                  color="text2"
                />
              </View>
              <Spacer size="xm" />
              <Label
                style={styles.flex}
                numberOfLines={2}
                text={review?.reviews?.[0]?.content}
                size="mx"
                fontFamily="SemiBold"
                color="text2"
              />
              {/* <Spacer size="m" />
              <View style={styles.fRow}>
                {Array.from({length: 3}, (v, i) => i).map(i => (
                  <View key={i.toString()} style={styles.priceIcon}>
                    <ImageIcon
                      icon="underPrizeIcon"
                      size="x56"
                      style={styles.reviewImageView}
                    />
                  </View>
                ))}
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('CustomerPhotosScene');
                  }}>
                  <View style={styles.underPrizeIconView}>
                    <Label
                      text="43+"
                      color="background"
                      size="mx"
                      fontFamily="SemiBold"
                    />
                  </View>
                  <ImageIcon
                    icon="underPrizeIcon"
                    size="x56"
                    style={styles.reviewImageView}
                  />
                </TouchableOpacity>
              </View> */}
              <Spacer size="m" />
              <View style={styles.rateProdSection}>
                <View style={styles.nicNameView}>
                  <Label
                    text={review?.reviews?.[0]?.author?.substring(0, 24)}
                    size="mx"
                    fontFamily="Regular"
                    color="text2"
                  />
                  <Spacer size="xm" type="Horizontal" />
                  <Separator color="grey3" Vertical thickness="z" height="l" />
                  <Spacer size="xm" type="Horizontal" />
                  <Label
                    text={dateTimeFormat(
                      review?.reviews?.[0]?.date,
                      'D MMM YYYY',
                    )}
                    size="mx"
                    fontFamily="Regular"
                    color="text2"
                  />
                </View>
                {/* <View style={styles.likeDisView}>
                  <LikeDisLikeButton
                    count={0}
                    onPress={() => {}}
                    type="like"
                    loading={faqLoading}
                  />
                  <Spacer size="s" type="Horizontal" />
                  <Label text="xl" size="mx" weight="400" color="grey3" />
                  <Spacer size="mx" type="Horizontal" />
                  <LikeDisLikeButton
                    count={0}
                    onPress={() => {}}
                    type="dislike"
                    loading={faqLoading}
                  />
                  <Spacer size="s" type="Horizontal" />
                  <Label text="xl" size="mx" weight="400" color="grey3" />
                </View> */}
              </View>
            </View>
          </>
        ) : null}
        {review?.review_meta?.total_reviews > 1 ? (
          <TouchableOpacity
            onPress={() => viewAllReview()}
            style={styles.totalReview}>
            <Label
              text={`${t('PDP.viewAll1')} ${
                review?.review_meta?.total_reviews
              } ${t('PDP.reviews').toLowerCase()}`}
              size="m"
              fontFamily="Medium"
              color="newSunnyOrange"
            />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default RateReview;

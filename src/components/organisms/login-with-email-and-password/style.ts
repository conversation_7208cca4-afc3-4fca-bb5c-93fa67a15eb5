import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {flex: Sizes.x, padding: Sizes.l},
    body: {
      justifyContent: 'flex-end',
      backgroundColor:'red'
    },
    title: {
      width: '100%',
      alignItems: 'center',
    },
    imageBackGround: {
      flex: Sizes.x,
      padding: Sizes.xl,
    },
    containerForm: {
      flex: Sizes.x,
      padding: Sizes.xl,
    },
    textOtherLogin: {
      color: colors.newPrimary,
      fontSize: Sizes.mx,
    },
    bottomButton: {
      alignItems: 'center',
      justifyContent: 'flex-start',
    },

    itemsCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      paddingHorizontal: Sizes.xl,
      textAlign: 'center',
      color: colors.grayTextColor,
    },
    separatorLine: {
      width: '45%',
      height: Sizes.x,
      backgroundColor: colors.smoothGrey,
    },

    whatsapp: {
      flexDirection: 'row',
      alignItems: 'center',
      borderColor: colors.border,
      padding: Sizes.xs,
      borderRadius: Sizes.s,
      width: '97%',
      backgroundColor: colors.socialButton,
      elevation: Sizes.xm,
      shadowOffset: {width: 0.5, height: 0.5},
      shadowOpacity: 0.5,
      shadowRadius: Sizes.xs,
      height: Sizes.x7l,
      paddingHorizontal: Sizes.l,
    },
    whatsAppText: {
      textAlign: 'center',
      alignSelf: 'center',
      width: '97%',
    },

    dentalkartLogo: {width: Sizes.ex1 + Sizes.xm, height: Sizes.ex1 + Sizes.xm},
    marginSpace: {marginTop: Sizes.x3l},

    //=============New Design================//

    newCreateAccView: {
      alignItems: 'center',
    },

    newOrCreateAccView: {
      flexDirection: 'row',
    },

    newUseEmailView: {
      alignSelf: 'flex-end',
    },

    dropView: {
      flexDirection: 'row',
      width: '100%',
      borderWidth: 0.5,
      borderRadius: Sizes.xms,
      borderColor: colors.grayTextColor,
    },
    dropDownView: {
      borderRightWidth: 0.5,
      borderColor: colors.grayTextColor,
    },
    itemContainer: {
      width: Sizes.exl,
    },
    selectedText: {
      color: colors.newPrimary,
      fontSize: Sizes.mx,
    },
    itemText: {
      color: colors.newPrimary,
      fontSize: Sizes.mx,
    },
    dropDownStyle: {
      width: Sizes.xll + Sizes.xl + Sizes.xs,
      borderWidth: 0,
      paddingHorizontal: Sizes.sx,
      paddingTop: 0,
      marginTop: Sizes.s + Sizes.x,
    },
    errorContainer: {
      marginTop: Sizes.s,
      marginLeft: Sizes.xs,
    },
    phoneNumberView: {
      fontSize: Sizes.mx,
      color: colors.newPrimary,
      flex: Sizes.x,
    },
    loginMainView: {
      justifyContent: 'flex-end',
    },
    loginView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    linkingView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
  });

export default styles;

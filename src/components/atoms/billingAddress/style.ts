import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    btnView: {
      width: Sizes.ex110,
      borderRadius: Sizes.m,
      height: Sizes.xx4l,
      alignItems: 'center',
      justifyContent: 'center',
    },
    btnContainer: {
      flexDirection: 'row',
      alignSelf: 'flex-end',
      marginBottom: 10,
    },
    addressMainView: {
      height: Sizes.exl,
    },
    mainView: {
      paddingHorizontal: Sizes.l,
    },
    styleDropDown: {
      height: Sizes.x7l,
      color: colors.grey2,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      fontFamily: Sizes.mx,
    },
    dropDownTextStyle: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    btnTxt: {
      fontFamily: Fonts.Medium,
      marginTop: -Sizes.xs,
    },
    fOne: {
      flex: Sizes.x,
    },
    rowView: {
      flexDirection: 'row',
    },
    downImg: {
      transform: [{rotate: '180deg'}],
    },
    dropDownItem: {
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.sx,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemSelected: {
      backgroundColor: colors.text,
    },
    labelTxt: {
      height: Sizes.l,
      marginVertical: Sizes.xs,
    },
    closeIcons: {
      position: 'absolute',
      top: Sizes.x,
      right: -5,
      zIndex: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    dropDownItemView: {
      marginBottom: Sizes.xms,
    },
    inputTextView: {
      color: colors.text,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      marginBottom: -Sizes.xs,
    },
    errorBorder: {
      borderColor: colors.red3,
      borderWidth: Sizes.x,
    },
    labelStyle: {
      top: -Sizes.xm,
    },
    placeTextStyle: {
      color: colors.grey,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
      marginBottom: -Sizes.s,
    },
    placeStyle: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    textStyle: {
      color: colors.text,
    },
  });

export default styles;

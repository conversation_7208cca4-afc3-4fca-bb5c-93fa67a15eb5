import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'flex-end',
      flex: 1,
    },
    box: {
      alignItems: 'center',
    },
    addButton: {
      alignItems: 'flex-end',
      backgroundColor: 'red',
      justifyContent: 'center',
    },
    addButtonInner: {
      padding: Sizes.xm,
    },
    item: {
      position: 'absolute',
      alignItems: 'center',
      padding: Sizes.xm,
    },
    itemsContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    touchableViewContainer: {
      flex: 1,
    },
  });

export default styles;

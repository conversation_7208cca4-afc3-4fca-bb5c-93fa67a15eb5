import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const CategoriesLoader = () => {
  const {colors} = useTheme();
  const boxWidth = (width - 60) / 4;
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={1750}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect
          x={width / 2 - 100}
          y="10"
          rx="5"
          ry="5"
          width="200"
          height="30"
        />

        <Rect x="10" y="60" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="60" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="60" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="60" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="170" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="170" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="170" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="170" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="280" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="280" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="280" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="280" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="390" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="390" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="390" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="390" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="500" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="500" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="500" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="500" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="610" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="610" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="610" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="610" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="720" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="720" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="720" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="720" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="830" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="830" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="830" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="830" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="940" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="940" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="940" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="940" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="1050" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="1050" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="1050" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="1050" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="1160" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="1160" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="1160" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="1160" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="1270" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="1270" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="1270" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="1270" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="1380" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="1380" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="1380" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="1380" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="1490" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="1490" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="1490" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="1490" rx="5" ry="5" width={boxWidth} height="100" />

        <Rect x="10" y="1600" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth + 20} y="1600" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 2 + 30} y="1600" rx="5" ry="5" width={boxWidth} height="100" />
        <Rect x={boxWidth * 3 + 40} y="1600" rx="5" ry="5" width={boxWidth} height="100" />
      </ContentLoader>
    </ScrollView>
  );
};

export default CategoriesLoader;

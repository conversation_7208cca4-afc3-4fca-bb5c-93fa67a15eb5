import React from 'react';
import {View, ViewStyle} from 'react-native';
import {Sizes} from 'common';

import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
type Props = {
  style?: ViewStyle;
  Vertical?: boolean;
  thickness?: keyof typeof Sizes | undefined;
  color?: keyof Theme['colors'];
  height?: keyof typeof Sizes;
};

const Separator = ({style, Vertical, color, thickness, height}: Props) => {
  const {colors} = useTheme();
  const styles = stylesWithOutColor(colors, thickness, color);

  return (
    <View
      style={[
        styles.container,
        Vertical && styles.deviedLine,
        height && {height: Sizes[height]},
        style,
      ]}
    />
  );
};

export default Separator;

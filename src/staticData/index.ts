import accountMenu from './accountMenu';
import accountMenu2 from './accountMenu2';
import accountMenuBoxList from './accountMenuBoxList';
import wishListProducts from './wishListProducts';
import {cartPositionStatus, checkoutPositionStatus} from './cartPositionStatus';
import orderFilterData from './orderFilterData';
import returnFilterData from './returnFilterData';
import statusMapping from './statusMapping';
import termsAndConditions from './termsAndConditions';
import faqData from './faqStory';
import whyRegisterWithDentalKart from './whyRegister';
import {faqQuestions, helpFaq} from './helpCenter';
import {
  magazine,
  blogs,
  buttons,
  news,
  shorts,
  months,
} from './savedContentList';
import smallProduct from './smallProduct';
import freeProduct from './freeProduct';
import benefits from './benefits';
import {
  bankData,
  allBankName,
  debitCartData,
  payLaterData,
  paymentOptions,
  paymentMethods,
} from './payment';
import {returnPolicy, productPolicy, productCondition} from './returnPolicy';
import socialData from './socialData';
import {sort} from './sorting';
import addressTag from './addressTag';
import speciality from './speciality';

export {
  accountMenu,
  accountMenu2,
  accountMenuBoxList,
  wishListProducts,
  cartPositionStatus,
  checkoutPositionStatus,
  orderFilterData,
  returnFilterData,
  statusMapping,
  magazine,
  blogs,
  buttons,
  news,
  shorts,
  months,
  faqData,
  termsAndConditions,
  whyRegisterWithDentalKart,
  faqQuestions,
  smallProduct,
  freeProduct,
  bankData,
  allBankName,
  debitCartData,
  payLaterData,
  paymentOptions,
  benefits,
  paymentMethods,
  returnPolicy,
  productPolicy,
  productCondition,
  helpFaq,
  socialData,
  sort,
  addressTag,
  speciality,
};

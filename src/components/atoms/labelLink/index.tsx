import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {TextProps, TextStyle} from 'react-native';
import {Sizes} from 'common';
import {useTheme} from '@react-navigation/native';

const fontProps = {
  title1: {fontSize: Sizes.xl, fontWeight: '700'},
  title2: {fontSize: Sizes.l, fontWeight: '600'},
  title3: {fontSize: Sizes.m, fontWeight: '500'},
  title4: {fontSize: Sizes.xm, fontWeight: '400'},
  title5: {fontSize: Sizes.s, fontWeight: '300'},
};

export type Props = TextProps & {
  children: React.ReactNode;
  size?: 'title1' | 'title2' | 'title3' | 'title4' | 'title5';
  color?: TextStyle['color'];
  bold?: boolean;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
};
// eslint-disable-next-line react-hooks/rules-of-hooks
const {colors} = useTheme();

const Text = ({align, children, size = 'title1', color, onPress}: Props) => (
  <Text
    onPress={onPress}
    style={[
      {
        textAlign: align,
        color: colors[color],
      },
      size && fontProps[size],
    ]}>
    {children}
  </Text>
);
export default React.memo<Props>(Text);

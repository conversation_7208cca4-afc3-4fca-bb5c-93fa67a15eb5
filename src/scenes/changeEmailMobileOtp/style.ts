import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    saveButton: {fontSize: Sizes.l, fontWeight: '700'},
    button: {backgroundColor: colors.textError},
    titleView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: Sizes.xl,
    },
    modelContainer: {
      width: '90%',
      alignSelf: 'center',
    },
    modelInputBox: {
      height: Sizes.x6l,
    },
    modelBtn: {
      height: Sizes.x7l,
      width: '100%',
      marginTop: Sizes.xms,
      alignSelf: 'center',
      borderRadius: Sizes.xms,
    },
    modelBtnView: {
      paddingVertical: Sizes.xl,
    },

    horizontalSpace: {paddingHorizontal: Sizes.l},
    buttonText: {color: colors.whiteColor},
    otpText: {color: colors.textLight},
    otpInputContainer: {
      width: '100%',
      height: Sizes.x7l + Sizes.xs,
    },
    otpView: {height: Sizes.x7l, alignSelf: 'center', marginHorizontal: 15},
    countdown: {
      justifyContent: 'center',
      flexDirection: 'row',
    },
    otplabel: {
      justifyContent: 'center',
      flexDirection: 'row',
      textAlign: 'center',
      color: colors.categoryTitle,
    },
    otherlabel: {
      padding: Sizes.xms,
      color: colors.grey,
      justifyContent: 'center',
      alignSelf: 'center',
      flexDirection: 'row',
    },
    updateEmailLabel: {
      backgroundColor: colors.gray10,
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
    },
    otpInput: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      color: colors.text,
      fontSize: Sizes.xl,
      fontWeight: '700',
      height: Sizes.x7l + Sizes.xs,
      backgroundColor: colors.background,
    },
    error: {
      borderColor: colors.textError,
    },
    otpErrorLabel: {
      alignSelf: 'flex-start',
      paddingLeft: Sizes.xm,
      paddingTop: Sizes.xs,
    },
    subContainer: {
      backgroundColor: colors.background,
      paddingHorizontal: Sizes.xms,
    },
    resendOtpTimer: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'center',
    },
    resendOtpText: {
      color: colors.lightDisabled,
    },
    resendOtpTextRem: {
      color: colors.lightDisabled,
      fontSize: Sizes.mx,
    },
    subReqBtn: {
      width: '100%',
      alignSelf: 'center',
      alignItems: 'center',

      borderRadius: Sizes.xms,
    },
    digitTxtStyle: {
      color: colors.newPrimary,
      fontWeight: '400',
      fontSize: Sizes.xl,
      fontFamily: Fonts.Medium,
    },
    separatorStyle: {
      fontSize: Sizes.mx,
      color: colors.lightDisabled,
      width: Sizes.sx,
    },
    resendOtpView: {alignSelf: 'center', alignItems: 'center'},
    coinImage: {width: Sizes.mx, height: Sizes.mx},
    subCautionerBox: {
      width: '100%',
      borderRadius: Sizes.sx,
      marginBottom: Sizes.xms,
      height: Sizes.x46,
    },
    pincodeStyle: {
      fontSize: 18,
      color: '#000',
      fontWeight: '600',
    },
    fRow: {
      flexDirection: 'row',
    },
    shouldShowView: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    digitView: {
      backgroundColor: colors.whiteColor,
    },
    otpInputBox: {
      borderColor: colors.grey,
      borderWidth: 1,
      backgroundColor: colors.whiteColor,
    },
    stepView: {
      alignItems: 'center',
      flexDirection: 'row',
    },
  });

export default styles;

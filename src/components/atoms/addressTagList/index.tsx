import React, {useMemo, memo} from 'react';
import {View, TouchableOpacity, FlatList} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer, ImageIcon} from 'components/atoms';
import {addressTag} from 'staticData';

type Props = {
  selected: string;
  onPress: (value: string) => void;
};

const AddressTagList = ({selected, onPress}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={styles.tagListView}>
      <FlatList
        data={addressTag}
        horizontal={true}
        keyExtractor={(_, i) => i.toString()}
        renderItem={({item, index}) => {
          const title =
            selected === 'Home' || selected === 'Clinic'
              ? selected
              : selected
              ? 'Other'
              : '';
          const selectedVal = title === item.title;
          return (
            <TouchableOpacity
              key={index}
              style={[styles.tagView, selectedVal && styles.selectedTag]}
              onPress={() => onPress(item.title)}>
              <ImageIcon
                size="mx"
                tintColor={selectedVal ? 'whiteColor' : 'categoryTitle'}
                icon={item.image}
                resizeMode="contain"
              />
              <Spacer size="s" type="Horizontal" />
              <Label
                text={item.title}
                size="mx"
                fontFamily="Medium"
                color={selectedVal ? 'whiteColor' : 'categoryTitle'}
                style={styles.labelStyle}
              />
            </TouchableOpacity>
          );
        }}
      />
    </View>
  );
};

export default memo(AddressTagList);

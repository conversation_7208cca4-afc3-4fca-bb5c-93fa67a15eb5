import useDeviceConfig from 'components/customHooks/useDeviceConfig';
import React, { forwardRef } from 'react';
import { FlatList } from 'react-native';

/**
 * Higher Order Component that wraps FlatList with device-specific optimizations
 * Uses useDeviceConfig() for default values when not provided in props
 */
const OptimizedFlatList = forwardRef((props, ref) => {
  // Get device-specific configuration values from the hook
  const {
    numColumns: defaultNumColumns,
    initialNumToRender: defaultInitialNumToRender,
    maxToRenderPerBatch: defaultMaxToRenderPerBatch,
    // windowSize: defaultWindowSize
  } = useDeviceConfig();

  // Use provided props if available, otherwise use the defaults from useDeviceConfig
  const numColumns = props.horizontal ? undefined : (props.numColumns ?? defaultNumColumns);
  const initialNumToRender = props.initialNumToRender ?? defaultInitialNumToRender;
  const maxToRenderPerBatch = props.maxToRenderPerBatch ?? defaultMaxToRenderPerBatch;
  const windowSize = props.windowSize;

  // Default values for performance optimization when not provided
  const scrollEventThrottle = props.scrollEventThrottle ?? 16;
  const updateCellsBatchingPeriod = props.updateCellsBatchingPeriod ?? 50;
  const removeClippedSubviews = props.removeClippedSubviews ?? true;

  return (
    <FlatList
      ref={ref}
      {...props}
      numColumns={numColumns}
      initialNumToRender={initialNumToRender}
      maxToRenderPerBatch={maxToRenderPerBatch}
      windowSize={windowSize}
      scrollEventThrottle={scrollEventThrottle}
      updateCellsBatchingPeriod={updateCellsBatchingPeriod}
      removeClippedSubviews={removeClippedSubviews}
    />
  );
});

export default OptimizedFlatList;
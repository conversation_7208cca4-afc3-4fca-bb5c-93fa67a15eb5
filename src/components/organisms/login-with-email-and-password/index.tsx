import React, {use<PERSON><PERSON>back, useContext, useState} from 'react';
import {View, ScrollView, TouchableOpacity, Linking} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Formik} from 'formik';
import type {RootStackParamsList} from '../../../routes';
import {Button, PhoneInputText} from 'components/molecules';
import stylesWithOutColor from './style';
import {
  Label,
  Spacer,
  Link,
  Separator,
  LoginGoogle,
  ImageIcon,
  DropDown,
} from 'components/atoms';
import {useTranslation} from 'react-i18next';
import * as yup from 'yup';
import tokenClass from 'utils/token';
import {Platform} from 'react-native';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import context from 'context/context';
import {useTheme} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {
  generateNewCart,
  getReferralLink,
  getUserInfo,
  setIsLoggedIn,
} from 'app-redux-store/slice/appSlice';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {socialLogin, verifyOTP} from 'services/auth';
import AnalyticsEvents from '../analytics-Events';
import {Sizes} from 'common';
import {useMemo} from 'react';
import { debugError } from 'utils/debugLog';

type params = {
  nextScreenName?: string;
  nextScreenParams?: any;
};
type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  setOtpLogin: (screen: string) => void;
  params: params;
};
const LoginWithEmailAndPassword = ({
  navigation,
  setOtpLogin,
  params,
}: Props) => {
  const {t} = useTranslation();
  const {store} = useContext(context);
  const dispatch = useDispatch();
  const [apiError, setApiError] = useState(null);
  const [validationError] = useState('');
  const [isPasswordSecure, setIsPasswordSecure] = useState(true);
  const [emailMobileLogin, setEmailMobileLogin] = useState(false);

  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  // const [mutateFunction] = useMutation(LOGIN, {client: customerClient});
  const validationSchema = yup.object().shape({
    emailNumber: yup
      .string()
      .required(
        emailMobileLogin == false
          ? 'validations.emailRequried'
          : 'mobile number is required',
      )
      .test(
        'emailNumber',
        emailMobileLogin == false
          ? 'validations.emailInvalid'
          : 'Mobile number is invalid',
        value => {
          return emailMobileLogin
            ? validatePhone(parseInt(value ?? '0'))
            : validateEmail(value);
        },
      ),
    password: yup.string().required('validations.password'),
  });
  const validateEmail = (email: string | undefined) => {
    return yup.string().email('validations.validEmail').isValidSync(email);
  };

  const validatePhone = (phone: number | undefined) => {
    return yup
      .string()
      .min(10, 'validations.phoneNumberRequired')
      .max(10, 'validations.phoneNumberRequired')
      .isValidSync(phone);
  };
  const navigateToLastScreen = useCallback(() => {
    let lastScreen = params?.nextScreenName;
    let nextRouterState = params?.nextRouterState;
    try {
      if (lastScreen) {
        return navigation.navigate(lastScreen, params?.nextScreenParams);
      }
      if (nextRouterState) {
        return navigation.reset(nextRouterState);
      }
      return navigation.reset({
        index: 0,
        routes: [{ name: 'Tab', params: { screen: 'Shop' } }],
      });;
    } catch (e) {
      return navigation.navigate('Tab', {screen: 'Shop'});
    }
  }, [navigation, params]);
  const loginSubmit = useCallback(
    async (values: any) => {
      try {
        dispatch(setLoading(true));
        const res = await verifyOTP({
          recipient: values.emailNumber,
          action: 'login_w_pass',
          verification_type: 'password',
          authentication_type: values.phoneNumber.includes('@')
            ? 'email'
            : 'mobile',
          credential: values.password,
          new_password: '',
        });
        const errors = '';
        if (res?.data?.verifyOTP.token) {
          setApiError(null);
          await tokenClass.setToken(res?.data?.verifyOTP.token);
          showSuccessMessage('Login Success');
          dispatch(setIsLoggedIn(true));
          dispatch(getUserInfo());
          await dispatch(generateNewCart());
          dispatch(getReferralLink({refer_type: 'USER'}));
          AnalyticsEvents(
            'USER_LOGGED_IN',
            'Login-Email/Phone',
            res?.data,
            {},
            false,
          );
          navigateToLastScreen();
          dispatch(setLoading(false));
        } else if (
          String(errors).includes(`"errorCode":"NC01"`) ||
          String(errors).includes(`"NC01"`)
        ) {
        } else if (errors) {
          showErrorMessage(String(errors));
        } else {
          setApiError(data?.login?.message);
        }
      } catch (error) {
        dispatch(setLoading(false));
        showErrorMessage(
          String(error).replace('[Error: GraphQL error: ', '').replace(']', ''),
        );
      }
    },
    [navigateToLastScreen, dispatch],
  );

  const getUserData = useCallback(
    async (googleResponse: any) => {
      try {
        dispatch(setLoading(true));
  
        const { data } = await socialLogin({
          type: 'google',
          token: googleResponse.idToken,
        });
  
        if (data?.socialLogin?.token) {
          setApiError(null);
  
          // Run all async actions in parallel for better performance
          await Promise.all([
            tokenClass.setToken(data.socialLogin.token),
            dispatch(setIsLoggedIn(true)),
            dispatch(getUserInfo()),
            dispatch(generateNewCart()),
            dispatch(getReferralLink({ refer_type: 'USER' })),
          ]);
  
          showSuccessMessage('Login Success');
          AnalyticsEvents('USER_LOGGED_IN', 'Login-Email/Phone', data, {}, false);
          navigateToLastScreen();
        } else {
          setApiError(data?.dkgenerateSocialLoginCustomerToken?.token || 'Login failed');
        }
      } catch (error) {
        debugError('Login Error:', error);
        setApiError('Login failed. Please try again.');
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch,navigation,navigateToLastScreen]
  );
  

  return (
    <>
      <ScrollView contentContainerStyle={styles.containerForm}>
        <View style={{flex: 1}}>
          <Formik
            initialValues={{
              emailNumber: '',
              password: '',
            }}
            validationSchema={validationSchema}
            onSubmit={loginSubmit}>
            {({handleSubmit, handleChange, values, errors, isValid}) => (
              <>
                <View
                  style={
                    Platform.OS === 'ios'
                      ? styles.containerForm
                      : styles.marginSpace
                  }>
                  <View style={styles.body}>
                    <View style={styles.newCreateAccView}>
                      <Label
                        text={t('login.signIn')}
                        color="newPrimary"
                        weight="600"
                        size="xl"
                      />
                      <Spacer size="s" />
                      <View style={styles.newOrCreateAccView}>
                        <Label
                          text={t('login.Or')}
                          size="m"
                          color="newPrimary"
                        />
                        <Spacer size="s" />
                        <Link
                          onPress={() => {
                            navigation.navigate('RegisterOtp');
                          }}
                          text={t('login.orCreateNewAccount')}
                          size="m"
                          color="newSunnyOrange"
                        />
                      </View>
                    </View>

                    <Spacer type="Vertical" size="xxxl" />
                    <View style={styles.newUseEmailView}>
                      <Link
                        isUnderlined={false}
                        onPress={() => {
                          setEmailMobileLogin(!emailMobileLogin);
                        }}
                        style={[styles.textOtherLogin, {fontSize: Sizes.m}]}
                        text={
                          emailMobileLogin
                            ? t('login.useEmai')
                            : t('login.useMobile')
                        }
                      />
                    </View>
                    <Spacer size="s" />

                    {emailMobileLogin ? (
                      <>
                        <View style={styles.dropView}>
                          <View style={styles.dropDownView}></View>

                          <PhoneInputText
                            testID="txtLoginEmailId"
                            inputStyle={{
                              fontSize: 14,
                              color: colors.newPrimary,
                            }}
                            style={{borderWidth: 0}}
                            pageType={'SignUp'}
                            autoComplete="off"
                            placeholderColor={colors.newPrimary}
                            autoFocus={true}
                            placeholder={String(
                              emailMobileLogin
                                ? t('login.enterPhoneNumber')
                                : t('login.enterMailId'),
                            )}
                            value={values.emailNumber}
                            onChangeText={handleChange('emailNumber')}
                          />
                        </View>
                        {errors?.emailNumber ? (
                          <View style={styles.errorContainer}>
                            <Label
                              color="textError"
                              size={'m'}
                              text={String(
                                t(
                                  errors.emailNumber
                                    ? (errors.emailNumber as string)
                                    : apiError
                                    ? String(apiError)
                                    : validationError
                                    ? String(validationError)
                                    : '',
                                ),
                              )}
                            />
                          </View>
                        ) : null}
                      </>
                    ) : (
                      <PhoneInputText
                      testID="txtLoginPhoneNumber"
                        inputStyle={styles.phoneNumberView}
                        pageType={'SignUp'}
                        autoComplete="off"
                        placeholderColor={colors.newPrimary}
                        autoFocus={true}
                        placeholder={String(
                          emailMobileLogin
                            ? t('login.enterPhoneNumber')
                            : t('login.enterMailId'),
                        )}
                        value={values.emailNumber}
                        onChangeText={handleChange('emailNumber')}
                        error={String(
                          t(
                            errors.emailNumber
                              ? (errors.emailNumber as string)
                              : apiError
                              ? String(apiError)
                              : validationError
                              ? String(validationError)
                              : '',
                          ),
                        )}
                      />
                    )}

                    <Spacer type="Vertical" size="xm" />
                    <PhoneInputText
                      testID="txtLoginPassword"
                      inputStyle={{fontSize: 14, color: colors.newPrimary}}
                      placeholderColor={colors.newPrimary}
                      searchIconStyle={{width: 20, height: 20}}
                      autoComplete="off"
                      onClear={() =>
                        isPasswordSecure
                          ? setIsPasswordSecure(false)
                          : setIsPasswordSecure(true)
                      }
                      clearIcon={isPasswordSecure ? 'viewHide' : 'eyeShowIcon'}
                      icon="lock"
                      placeholder={t('loginWithIdPassword.password')}
                      onChangeText={handleChange('password')}
                      value={values.password}
                      secureTextEntry={isPasswordSecure}
                      error={String(
                        t(
                          errors.password
                            ? (errors.password as string)
                            : apiError
                            ? String(apiError)
                            : '',
                        ),
                      )}
                    />
                    <Spacer type="Vertical" size="xm" />

                    <View>
                      <Link
                        onPress={() => {
                          navigation.navigate('ForgetPassword');
                        }}
                        style={styles.textOtherLogin}
                        text={t('login.forgotPassword')}
                      />
                    </View>
                    <Spacer type="Vertical" size="xms" />
                  </View>

                  <Link
                    onPress={setOtpLogin('otpLogin')}
                    style={styles.textOtherLogin}
                    text={t('loginWithIdPassword.otherLogin')}
                  />

                  <Spacer type="Vertical" size="xm" />

                  <View style={styles.bottomButton}>
                    <Button
                      weight="400"
                      labelSize="mx"
                      type={!isValid ? 'disabled' : 'secondary'}
                      labelColor="whiteColor"
                      selfAlign="stretch"
                      radius="m"
                      size="large"
                      disabled={!isValid}
                      text={t('login.continue')}
                      onPress={handleSubmit}
                    />
                    <Spacer type="Vertical" size="xl" />
                    <View style={styles.itemsCenter}>
                      <Separator style={styles.separatorLine} />
                      <Label
                        text={t('login.Or')}
                        size="mx"
                        style={styles.labelText}
                      />
                      <Separator style={styles.separatorLine} />
                    </View>
                    <Spacer type="Vertical" size="l" />

                    {Platform.OS === 'android' ? (
                      <>
                        <LoginGoogle
                          textStyle={{color: colors.newPrimary}}
                          getUserData={getUserData}
                        />
                        <Spacer type="Vertical" size="sx" />
                      </>
                    ) : null}

                    {/* <LoginGoogle
                      textStyle={{color: colors.newPrimary}}
                      isWhatsapp
                      onPressWhatsapp={() => {
                        Linking.openURL(
                          'https://dentalkart.authlink.me/?redirectUri=dentalkart://dentalkart',
                        );
                      }}
                    /> */}

                    {store?.validationRules?.whatsappsignup && (
                      <TouchableOpacity
                        onPress={() =>
                          Linking.openURL(
                            'https://dentalkart.authlink.me/?redirectUri=dentalkart://dentalkart',
                          )
                        }
                        style={styles.whatsapp}>
                        <ImageIcon size="xxl" icon="whatsappSmallIcon" />
                        <Spacer type="Horizontal" size="m" />
                        <Label
                          weight="bold"
                          color="textLight"
                          size="mx"
                          style={styles.whatsAppText}
                          text={t('loginWithIdPassword.whatsapp')}
                        />
                      </TouchableOpacity>
                    )}
                    <Spacer type="Vertical" size="xl" />
                  </View>
                </View>
              </>
            )}
          </Formik>
        </View>

        <View style={styles.loginMainView}>
          <View style={styles.loginView}>
            <Label
              text={t('login.footerTerm&Con')}
              size="m"
              color="grayTextColor"
            />
            <Link
              onPress={() =>
                Linking.openURL(
                  'https://www.dentalkart.com/terms-and-conditions',
                )
              }
              isUnderlined
              color="newPrimary"
              size="m"
              text={t('login.footerTermService')}
            />
            <Label text={t('login.and')} size="m" color="grayTextColor" />
            <Link
              onPress={() =>
                Linking.openURL('https://www.dentalkart.com/privacy-policy')
              }
              isUnderlined
              color="newPrimary"
              size="m"
              text={t('login.footerPolicy')}
            />
          </View>
          <Spacer size="m" />
          <View style={styles.linkingView}>
            <Label color="grey3" size="m" text={t('login.needHelp')} />
            <Link
              onPress={() => Linking.openURL('mailto:<EMAIL>')}
              color="newPrimary"
              size="m"
              text={t('login.supportEmail')}
            />
          </View>
        </View>
      </ScrollView>
    </>
  );
};

export default LoginWithEmailAndPassword;

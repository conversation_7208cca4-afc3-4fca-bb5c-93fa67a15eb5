// import SyncStorage from '@helpers/async_storage';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const getUrlData = async (url: any) => {
  const data = await AsyncStorage.getItem(url);
  return data;
};

export const setUrlData = async (url: string, data: string) => {
  await AsyncStorage.setItem(url, data);
};

export const removeUrlData = async (url: string) => {
  await AsyncStorage.removeItem(url);
};

export const extractQueryParams = (url: string) => {
  var regex = /[?&]([^=#]+)=([^&#]*)/g,
    params: any = {},
    match;
  while ((match = regex.exec(url))) {
    params[match[1]] = match[2];
  }
  return params;
};

import Api from 'services';
import {apiRoute, apiEndpoints, subEndpoint} from 'config/apiEndpoint';

const API = new Api();

export const getWishlist = async () => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}`;
  const resp = await API.get(url);
  return resp;
};
export const addProductToWishlist = async (data: {product_id: number}) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.addToWishList}`;
  const resp = await API.post(url, data);
  return resp;
};
export const getDefaultWishlist = async () => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${subEndpoint.wishlists.default}`;
  const resp = await API.get(url);
  return resp;
};

export const getSingleWishlist = async (id: String) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${id}`;
  const resp = await API.get(url);
  return resp;
};

export const createWishlist = async (data: any) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}`;
  const resp = await API.post(url, data);
  return resp;
};

export const updateUserWishlist = async (id: String, data: any) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${id}`;
  const resp = await API.put(url, data);
  return resp;
};

export const deleteUserWishlist = async (id: String) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${id}`;
  const resp = await API.delete(url);
  return resp;
};
export const deleteProductFromWishlist = async (wishList_id, product_id) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${wishList_id}/${subEndpoint.carts.items}?product_id=${product_id}`;
  const resp = await API.delete(url);
  return resp;
};

export const deleteFriendWishlist = async (id: String) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${subEndpoint.wishlists.friends}/${id}`;
  const resp = await API.delete(url);
  return resp;
};

export const addFriendWishlist = async (id: String) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${subEndpoint.wishlists.friends}/${id}`;
  const resp = await API.post(url);
  return resp;
};

export const getFriendWishlist = async () => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${subEndpoint.wishlists.friends}`;
  const resp = await API.get(url);
  return resp;
};

export const moveToWishlist = async (data: any) => {
  const url = `${apiRoute.wishlist}${apiEndpoints.wishlists}/${subEndpoint.wishlists.moveProduct}`;
  const resp = await API.patch(url, data);
  return resp;
};

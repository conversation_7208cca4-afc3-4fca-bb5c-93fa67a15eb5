import React from 'react';
import {View} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer} from 'components/atoms';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {useMemo} from 'react';


const JailBroken = () => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View style={styles.container}>
      <View style={styles.subContainer}>
        <FastImage
          source={Icons.wentWrongGif}
          resizeMode="contain"
          style={styles.image}
        />
        <Label
          text={t('validations.rootSorry')}
          size="xl"
          align="center"
          weight="600"
          textTransform="capitalize"
          color="textError"
        />
        <Spacer size="m" />
        <Label
          text={t('validations.rootedDevices')}
          size="l"
          align="center"
          weight="500"
          textTransform="capitalize"
          color="text"
        />
      </View>
    </View>
  );
};

export default JailBroken;

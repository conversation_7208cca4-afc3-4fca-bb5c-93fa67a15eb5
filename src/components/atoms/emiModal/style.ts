import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
    },
    header: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xms,
    },
    flexShrink: {
      flexShrink: Sizes.x,
    },
    flatlist: {backgroundColor: colors.background},
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
    },

    bankView: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: 3,
    },
    emiPlansView: {
      backgroundColor: colors.background,
      flex: Sizes.xs,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
    },
    emiPlansSubView: {
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      justifyContent: 'space-around',
      flexDirection: 'row',
      backgroundColor: colors.background3,
      padding: Sizes.m,
      alignItems: 'center',
    },
    creditCardView: {
      justifyContent: 'space-around',
      flexDirection: 'row',
      backgroundColor: colors.background3,
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.m,
    },
    creditCardSubView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: Sizes.l,
    },
    contentTypeMainView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
    },
    contentTypeSubView: {
      alignItems: 'center',
      flex: Sizes.x,
    },
    arrowRightView: {
      flexDirection: 'row',
      borderBottomWidth: Sizes.x,
      borderColor: colors.text2,
      padding: Sizes.xm,
      paddingVertical: Sizes.mx,
    },
    arrowRightSubView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xm,
    },
    enterView: {
      paddingLeft: Sizes.xl,
      paddingHorizontal: Sizes.xms,
    },
    debitView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: Sizes.xms,
    },
    cardLessView: {
      paddingHorizontal: Sizes.xms,
      paddingVertical: Sizes.m,
    },
    stateView: {
      paddingHorizontal: Sizes.xx,
      paddingVertical: Sizes.x3l,
    },
    payLaterDataView: {
      flexDirection: 'row',
      borderBottomWidth: Sizes.x,
      borderColor: colors.text2,
      padding: Sizes.xm,
      paddingVertical: Sizes.xsl,
    },
    payLaterSubDataView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xm,
      flexDirection: 'row',
    },
    mobileNoView: {
      paddingLeft: Sizes.xl,
      paddingHorizontal: Sizes.xms,
    },
    subInput: {
      width: '80%',
      borderRadius: Sizes.m,
      color: colors.text,
      borderColor: colors.placeholderColor,
    },
    searchIconStyle: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    inputBorderStyle: {
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      height: Sizes.x6l,
      backgroundColor: colors.background,
      borderColor: colors.placeholderColor,
    },
    paddingH4: {
      paddingHorizontal: Sizes.s,
    },
    paddingH8: {
      paddingHorizontal: Sizes.xm,
    },
    debitCardView: {
      backgroundColor: colors.background3,
    },
    viewRight: {
      alignItems: 'flex-end',
    },
    viewCenter: {
      alignItems: 'center',
    },
    bankNameView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.text2,
    },
    padding8: {
      padding: Sizes.xm,
    },
    subViewStyle: {
      paddingHorizontal: 0,
    },
  });

export default styles;

import { Sizes } from 'common';
import { Dimensions, StyleSheet } from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    container2: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    containerGif: {
      height: '40%',
      position: 'relative',
      overflow: 'hidden',
      // backgroundColor: '#FF6A13'
    },
    thankYouImage: {
      height: '115%',
      width: '100%',
      position: 'absolute',
      // resizeMode: 'cover',
      alignSelf: 'center',
      backgroundColor: colors.sunnyOrange3,
    },
    bottomFade: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 100, // Adjust height as needed for shadow depth
    },
    title: {
      width: '100%',
      alignItems: 'center',
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    imageFullWidth: {
      // marginTop: 200,
      width: '100%',
    },
    logoView: {
      alignSelf: 'center',
      padding: 0,
      margin: 0,
      width: Dimensions.get('window').width,
      height: 69,
      // marginTop: 70,
    },
    rulesView: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Sizes.s,
      gap: Sizes.s
    },
    rules: {
      gap: Sizes.s,
      marginTop: Sizes.s,
    },
    signUpLogin: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 5,
      marginTop: Sizes.mx,
    },
    crossIconStyle: {
      height: Sizes.x3l,
      width: Sizes.x3l,
      position: 'absolute',
      top: 15,
      right: 20,
      zIndex: 2,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xl,
      // backgroundColor:colors.background
    },
    body: {
      // flex: Sizes.x,
      paddingHorizontal: Sizes.xxxl,
      height: '60%',
      bottom: 30
    },

    otherLogin: {
      width: '100%',
      marginTop: Sizes.xm - Sizes.x,
      flexDirection: 'row',
    },
    imageBackGround: {
      flex: Sizes.x,
      padding: Sizes.xl,
    },
    containerForm: {
      flex: Sizes.x,
    },
    textOtherLogin: {
      color: colors.newPrimary,
      fontSize: Sizes.mx,
    },
    dentLogo: {
      width: Sizes.ex1 + Sizes.xm,
      height: Sizes.ex1 + Sizes.xm,
    },
    containerView: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
    },

    dropView: {
      flexDirection: 'row',
      width: '100%',
      borderWidth: 0.5,
      borderRadius: Sizes.xms,
      borderColor: colors.grayTextColor,
    },
    dropDownView: {
      borderRightWidth: 0.5,
      borderColor: colors.grayTextColor,
    },
    itemContainer: {
      width: Sizes.exl,
    },
    selectedText: {
      color: colors.newPrimary,
      fontSize: Sizes.mx,
    },
    itemText: {
      color: colors.newPrimary,
      fontSize: Sizes.mx,
    },
    dropDownStyle: {
      width: 72,
      borderWidth: 0,
      paddingHorizontal: Sizes.sx,
      paddingTop: 0,
      marginTop: Sizes.s,
    },

    newUseEmailView: {
      alignSelf: 'flex-end',
    },
    errorContainer: {
      marginTop: Sizes.s,
      marginLeft: 0,
    },

    // ==================new design============

    underLine: { textDecorationLine: 'underline' },

    newOrCreateAccView: {
      flexDirection: 'row',
      alignItems: 'center',
    },

    labelText: {
      paddingHorizontal: Sizes.xl,
      textAlign: 'center',
      color: colors.grayTextColor,
    },
    separatorLine: {
      width: '45%',
      height: Sizes.xs,
      backgroundColor: colors.smoothGrey,
    },
    itemsCenter: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingRight: Sizes.x3l,
    },

    inputView: {
      borderWidth: Sizes.x,
      color: colors.grey2,
      borderColor: colors.grey2,
      // paddingHorizontal: 10,
    },
    inputStyleView: {
      fontSize: Sizes.mx,
      color: colors.text,
      // flex: Sizes.x,
      borderColor: colors.grey2,
      width: '80%',
    },
    searchView: { width: Sizes.xl, height: Sizes.xl },
    loginView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginLeft: Sizes.xl,
    },
    shouldShowView: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    coinImage: {
      width: Sizes.l,
      height: Sizes.l,
    },
  });

export default styles;

import React, {memo} from 'react';
import {View} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

const Rail = ({color}: {color?: keyof Theme['colors']}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View
      style={[
        styles.root,
        {backgroundColor: color ? colors[color] : colors.smoothBlue},
      ]}
    />
  );
};

export default memo(Rail);

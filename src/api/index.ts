import {client, customerClient} from './apollo_client';
import {BOOKMARK_NEWS, GET_NEWS, LIKE_NEWS, NOTIFY_ME} from './newsApi';
import SAVED_NEWS from './newsApi/query/saved_news.gpl';
import MULTIMEDIA from './newsApi/query/multimedia.gql';
import {showErrorMessage} from 'utils/show_messages';
import { debugError } from 'utils/debugLog';

// ------------ News api -------------------
const news = async () => {
  try {
    const {data, errors, loading} = await client.query({
      query: GET_NEWS,
    });
    return {data, errors, loading};
  } catch (err) {
    return {data: null, errors: err, loading: false};
  }
};

// --------- saved api --
const bookmarkNews = async (variables: {id: number}) => {
  try {
    const {data, errors, loading} = await client.mutate({
      mutation: BOOKMARK_NEWS,
      variables: variables,
    });

    return {data, errors, loading};
  } catch (err) {}
};
// --------- Like news  api --
const likeNews = async (variables: {id: number}) => {
  try {
    const {data, errors, loading} = await client.mutate({
      mutation: LIKE_NEWS,
      variables: variables,
    });

    return {data, errors, loading};
  } catch (err) {
    debugError(err);
  }
};

// --------- notify api me ---------------

const notifyMe = async (productid: number) => {
  try {
    const {data, errors, loading} = await client.mutate({
      mutation: NOTIFY_ME,
      variables: {productid},
    });
    return {data, errors, loading};
  } catch (err: any) {
    if (err?.graphQLErrors) {
      const gqlErrors = err.graphQLErrors.map((e: any) => e.message).join(', ');
      setTimeout(()=>{
        showErrorMessage(gqlErrors);
      },200)
      return {data: null, errors: gqlErrors, loading: false};
    } else {
      const errorMessage = err?.message;
      setTimeout(()=>{
        showErrorMessage(errorMessage);
      },200)
    }

    return {data: null, errors: errorMessage, loading: false};
  }
};

// ----------- get saved news  -----
const savedNews = async () => {
  try {
    const {data, errors, loading} = await client.query({
      query: SAVED_NEWS,
      fetchPolicy: 'network-only',
    });

    return {data, errors, loading};
  } catch (err) {
    return {data: null, errors: err, loading: false};
  }
};
// ----------- get saved news  -----
const multimediaAPi = async (variables: {type: 'video'}) => {
  try {
    const {data, errors, loading} = await client.query({
      query: MULTIMEDIA,
      variables: variables,
    });
    return {data, errors, loading};
  } catch (err) {
    return {data: null, errors: err, loading: false};
  }
};

export {news, savedNews, bookmarkNews, likeNews, multimediaAPi, notifyMe};

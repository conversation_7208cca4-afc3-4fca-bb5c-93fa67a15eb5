import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {View, ViewStyle, TouchableOpacity, Linking} from 'react-native';
// import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import Label from '../label';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {useMemo} from 'react';


const infoHeader = () => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View style={styles.infoBG}>
      <TouchableOpacity onPress={() => Linking.openURL('tel:+91 7289999456')}>
        <Label
          style={styles.item}
          color="whiteColor"
          text={t('infoHeader.customer')}
        />
      </TouchableOpacity>
    </View>
  );
};

export default infoHeader;

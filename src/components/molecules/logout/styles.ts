import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    removeModalView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xms,
      paddingHorizontal: Sizes.mx,
      paddingVertical: Sizes.m,
    },
    removeModalSubView: {
      justifyContent: 'center',
      flexDirection: 'row',
    },
    memberShipView: {
      borderRadius: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
      flex: Sizes.x,
    },
    removeImageView: {
      width: Sizes.ex,
      height: Sizes.exl,
    },
    removeView: {
      flex: Sizes.xs,
    },
    borderBottomView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    borderLine: {
      width: 1.5,
      height: Sizes.x3l,
      backgroundColor: colors.aliceBlue3,
    },
    deleteCartItemView: {
      justifyContent: 'space-around',
      flexDirection: 'row',
      alignItems: 'center',
    },
    deleteCartSubView: {
      flex: Sizes.x,
      alignItems: 'center',
    },
    wishlistView: {
      justifyContent: 'space-around',
      flexDirection: 'row',
      flex: Sizes.x,
    },
    flex: {
      flex: Sizes.x,
    },
  });

export default styles;

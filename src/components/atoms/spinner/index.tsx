import {useTheme} from '@react-navigation/native';
import React from 'react';
import {ActivityIndicator, View, Modal} from 'react-native';
import stylesWithOutColor from './style';
import {useSelector} from 'react-redux';
import {useMemo} from 'react';

type Props = {
  transparent?: boolean;
  size?: 'large' | 'small';
  visible?: boolean;
};

const Spinner = ({transparent = false, size = 'large'}: Props) => {
  const {loading} = useSelector((state: RootState) => state.app);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <Modal transparent visible={loading}>
      <View
        style={[styles.indicatorBackground, transparent && styles.transparent]}>
        <ActivityIndicator color={colors.primary} size={size} />
      </View>
    </Modal>
  );
};

export default Spinner;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.topItemsBg,
      // backgroundColor: colors.myWishlistBG,
      borderRadius: Sizes.s,
      marginTop: Sizes.m,
      borderColor: colors.itemsBorderColor,
      borderWidth: 0.3,
    },
    pulsImgStyleSold: {
      position: 'absolute',
      zIndex: Sizes.xs,
      backgroundColor: colors.soldOutColor,
      height: Sizes.ex1 + Sizes.xxl + Sizes.xs,
      width: Sizes.ex2l - Sizes.xms,
      alignItems: 'center',
      justifyContent: 'center',
    },
    soldImage: {
      position: 'absolute',
      zIndex: Sizes.xs,
      backgroundColor: colors.soldOutColor,
      width: '100%',
      height: '100%',
    },
    soldOutTag: {
      backgroundColor: colors.textError,
      padding: 2,
      height: Sizes.xl,
      marginTop: Sizes.s,
    },
    soldOutLabel: {color: colors.whiteColor},
    descriptionsText: {
      paddingTop: Sizes.s,

      width: '60%',
      paddingRight: Sizes.s,
    },
    descriptionSubText: {
      paddingTop: Sizes.xs,

      width: '60%',
    },
    ratingView: {
      paddingTop: Sizes.s,
    },
    ratingImg: {
      marginLeft: Sizes.s,
    },
    checkBoxContainer: {
      right: Sizes.m,
      padding: Sizes.xm,
    },
    checkBox: {padding: Sizes.s},
    prizeTextView: {
      justifyContent: 'space-between',
      paddingTop: Sizes.s,
    },
    prizeTextViewHoz: {
      flexDirection: 'row',
      paddingTop: Sizes.s,
      width: '48%',
      justifyContent: 'space-between',
    },
    removePrize: {
      textDecorationLine: 'line-through',
      textDecorationColor: colors.whiteColor,
      color: colors.textError,
      paddingHorizontal: Sizes.s,
      fontSize: Sizes.m,
      fontWeight: '400',
    },
    prizeText: {
      left: Sizes.xs,
    },

    offText: {
      fontSize: Sizes.m,
      fontWeight: '400',
      left: Sizes.s,
    },
    checkOtherProduct: {
      fontSize: Sizes.m,
      fontWeight: '400',
      textDecorationLine: 'underline',
    },
    subContainer: {
      width: '38%',
      alignItems: 'center',
      justifyContent: 'center',
      margin: Sizes.xm,
      // backgroundColor: 'red',
      borderWidth: Sizes.x,
      borderRadius: Sizes.s,
      borderColor: colors.itemsBorderColor, // elevation: Sizes.x,
    },
    descriptionContinuer: {
      width: '50%',
      marginHorizontal: Sizes.m,
    },
    descriptionContinuerHorizontal: {
      width: '100%',
    },

    checkContinuer: {
      width: '58%',
      alignSelf: 'flex-end',
    },
    image: {width: '100%', height: Sizes.exl + Sizes.xms},
    reviewText: {marginLeft: Sizes.s},
    binButton: {
      height: Sizes.x4l - Sizes.xs,
      width: Sizes.x4l - Sizes.xs,
      backgroundColor: colors.lightRed,
      borderRadius: Sizes.x4l - Sizes.xs,
      justifyContent: 'center',
    },
    leftIcon: {top: 0, left: 0, right: 0, bottom: 0},
    buttonDirections: {
      width: '85%',
      justifyContent: 'space-evenly',
      alignSelf: 'flex-end',
    },
    tagView: {
      width: Sizes.x6l + Sizes.s,
      borderRadius: Sizes.s,
      backgroundColor: colors.grassGreen,
    },
    addTwoCart: {
      backgroundColor: colors.sunnyOrange,
      height: Sizes.x4l,
      width: '40%',
    },
    moveButton: {
      backgroundColor: colors.lightGray,
      height: Sizes.x4l,
      width: '28%',
    },
    directionView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rewardIcon: {
      width: Sizes.l,
      height: Sizes.l,
    },
    row: {flexDirection: 'row'},
    widthLabel: {width: '80%'},
    widthWithExpiryProduct: {width: '85%'},
    buttonView: {marginVertical: Sizes.s, marginTop: Sizes.xl},
    buttonText: {color: colors.textLight, fontSize: Sizes.mx},
    addToCartText: {fontSize: Sizes.mx},
    otherButtonsView: {
      justifyContent: 'space-around',
      paddingBottom: Sizes.m,
    },
    iconStyle: {width: Sizes.xx, height: Sizes.xx},
    buttonBackground: {backgroundColor: colors.primary},
    pointProduct: {left: Sizes.s},
    checkBoxRadius: {borderRadius: Sizes.xxxl},
    opacity: {opacity: 0.33},
    addButtonText: {fontSize: Sizes.m, color: colors.whiteColor},
    labelAddToCart: {fontSize: Sizes.l, fontWeight: '500'},
    labelsColor: {
      color: colors.whiteColor,
      fontSize: Sizes.m,
    },

    boxOffer: {
      backgroundColor: '#d8ffdccc',
      borderWidth: 0.5,
      borderColor: '#2A7A21',
      borderRadius: Sizes.xm,
      padding: Sizes.s,
      paddingHorizontal: Sizes.s,
      marginTop: Sizes.s,
      flex: 1,
      maxWidth: 365,
    },

    tierPrice: {
      fontSize: 12.5,
      color: '#00a324',
      marginLeft: 5,
    },
    AvailOfferView: {
      color: '#000000',
      fontSize: Sizes.mx,
      fontWeight: '400',
      marginLeft: Sizes.s,
    },
    offer: {flexDirection: 'row', marginVertical: 5},
    offerImg: {width: Sizes.l, height: 15},
    itemView: {paddingHorizontal: Sizes.xs},
    auto: {width: 'auto'},
    static: {width: 361},
    padding: {
      padding: Sizes.s,
    },
    promotionBySkuPadding: {paddingTop: Sizes.xm},
    paddingView: {
      padding: Sizes.s,
    },
    messageWidth: {width: '95%'},
    separator: {width: 5},
  });

export default styles;

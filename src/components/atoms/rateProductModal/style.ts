import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
    },
    centeredView: {
      flex: Sizes.s,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    scrollViewStyle: {
      marginTop: 'auto',
    },
    modalCloseBtnContainer: {
      flex: Sizes.xs,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    viewProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
    },
    offerZoneBannerView: {
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      backgroundColor: colors.grey7,
    },
    dropDown: {
      color: colors.text2,
      fontSize: Sizes.mx,
    },
    selectedInputView: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    styleDropView: {
      paddingLeft: Sizes.m,
      paddingRight: Sizes.l,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      height: Sizes.x8l,
    },
    styleDropSubView: {
      width: Sizes.xxxl,
      height: Sizes.xxxl,
      tintColor: colors.grey,
    },
    itemContainerView: {
      paddingHorizontal: Sizes.xm,
      borderRadius: Sizes.xm,
    },
    rateView: {
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      borderTopColor: colors.grey2,
      borderTopWidth: 1,
    },
    rateSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    submitView: {
      justifyContent: 'flex-start',
      alignSelf: 'flex-start',
    },
    fRow: {
      flexDirection: 'row',
    },
    selectView: {
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.xm,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
    },
    inputBox: {
      borderColor: colors.grey2,
      color: colors.text2,
    },
    rateReviewView: {
      padding: Sizes.m,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
    },
    borderR8: {
      borderRadius: Sizes.xm,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    starView: {
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.s,
    },
    accordionView: {
      padding: Sizes.m,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
    },
    accordionItem: {
      borderTopColor: colors.placeholderColor,
      borderTopWidth: Sizes.x,
      paddingVertical: Sizes.m,
    },
    ratingImage: {
      borderRadius: Sizes.xm,
      height: Sizes.x70 + Sizes.sx,
      width: Sizes.x70 + Sizes.sx,
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    ratingButton: {
      alignSelf: 'center',
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.persimmonOrange44,
      paddingVertical: Sizes.xms,
      paddingHorizontal: Sizes.x8l,
    },
    starRating: {
      flexDirection: 'row',
      flex: Sizes.x,
      alignItems: 'center',
    },
    flexShrink: {
      flexShrink: Sizes.x,
    },
    starItem: {
      marginRight: Sizes.s,
    },
  });

export default styles;

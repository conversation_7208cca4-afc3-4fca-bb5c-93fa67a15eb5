import React, { useEffect, useState } from 'react';
import {TouchableOpacity, View, ScrollView, KeyboardAvoidingView, Platform, Keyboard, SafeAreaView} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {Button, PhoneInputText} from 'components/molecules';
import {t} from 'i18next';
import {Formik} from 'formik';
import {productFeedback} from 'services/productDetail';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {productFeedbackSchema} from 'utils/validationError';
import {useSelector} from 'react-redux';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import {WEBSITE_URL} from 'config/environment';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import Icons from 'common/icons';
import Clipboard from '@react-native-community/clipboard';
import {useMemo} from 'react';
import Toast from 'react-native-toast-message';
import useToastConfig from '../toastConfig/toastConfig';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  visible: boolean;
  onClose: () => void;
  product: ProductData | null;
  onSuccess: () => void;
};

const ValuableFeedbackModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {userInfo} = useSelector((state: RootState) => state.app);
  const {toastConfig} = useToastConfig();
  const {visible, onClose, product, onSuccess} = props;
  const insets = useSafeAreaInsets();
  
  const sendFeedback = (values: Feedback) => {
    let payload = {
      product_id: product?.product_id,
      product_name: product?.name,
      product_sku: product?.sku,
      product_price: +values.price,
      product_quality: values.quality,
      other_feedback: values.feedback,
      user: userInfo?.email || 'guest_user',
    };

    productFeedback(payload)
      .then((res: any) => {
        if (res?.status) {
          onSuccess();
        } else {
          showErrorMessage(t('validations.someThingWrong'));
        }
        onClose();
      })
      .catch(() => {
        showErrorMessage(t('validations.someThingWrong'));
      });
  };

  const handleCopyToClipboard = () => {
    const url = `${WEBSITE_URL}${product?.seo?.url_key}.html`;
    Clipboard.setString(url);
    showSuccessMessage('copy to clipboard');
  };
 const [isKeyboardVisible, setKeyboardVisible] = useState(false);
 useEffect(() => {
     const keyboardDidShowListener = Keyboard.addListener(
       'keyboardDidShow',
       () => setKeyboardVisible(true),
     );
     const keyboardDidHideListener = Keyboard.addListener(
       'keyboardDidHide',
       () => setKeyboardVisible(false),
     );
 
     const defaultAddress = userInfo?.addresses.find(
       item => item?.default_shipping,
     );
    }, [])

  return (
    <DynamicHeightModal
      // useInsets
      visible={visible}
      onClose={() => onClose()}
      content={
        <KeyboardAvoidingView
          // behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={[styles.flex,
            // {paddingTop: isKeyboardVisible ? insets.top : 0},
          ]}
        >
          <View style={[styles.viewProduct]}>
            <View style={styles.valuableView}>
              <Spacer size="l" />
              <Label
                text={t('feedback.valuableFeedback')}
                size="l"
                fontFamily="SemiBold"
                color="text"
              />
              <Spacer size="l" />
              <ScrollView
                bounces={false}
                keyboardShouldPersistTaps="handled"
                nestedScrollEnabled={true}
                contentContainerStyle={{flexGrow: 1}}
                showsVerticalScrollIndicator={false}>
                <View style={styles.removeModalSubView}>
                  <View style={styles.memberShipView}>
                    <FastImage
                      source={
                        product?.media?.find(i => i.file)?.file
                          ? {
                              uri: getImageUrl(
                                product?.media?.find(i => i.file)?.file,
                              ),
                            }
                          : Icons.defaultImage
                      }
                      style={styles.removeImageView}
                      resizeMode="contain"
                    />
                  </View>
                  <Spacer size="xm" type="Horizontal" />
                  <View style={styles.removeView}>
                    <Label
                      text={product?.name}
                      size="mx"
                      fontFamily="Medium"
                      color="text2"
                    />
                    {product?.seo?.url_key ? (
                      <View style={styles.fRow}>
                        <Label
                          text={`${t('otherText.shareText')} :`}
                          size="mx"
                          fontFamily="Medium"
                          color="text2"
                        />
                        <Spacer size="sx" type="Horizontal" />
                        <TouchableOpacity
                          style={styles.flex}
                          onPress={handleCopyToClipboard}>
                          <Label
                            text={`${WEBSITE_URL}${product?.seo?.url_key}.html`}
                            size="m"
                            numberOfLines={3}
                            fontFamily="Medium"
                            color="sunnyDark"
                          />
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <Label
                        text={`${t('validations.someThingWrong')}`}
                        size="mx"
                        color="textError"
                        align="center"
                      />
                    )}
                  </View>
                </View>
                <Spacer size="l" />
                <Label
                  text={t('feedback.purchasedProduct')}
                  size="mx"
                  fontFamily="Medium"
                  color="text2"
                />
                <Spacer size="l" />
                <Label
                  text={t('feedback.feedbackNote')}
                  size="xl"
                  textTransform="capitalize"
                  fontFamily="SemiBold"
                  color="text2"
                />
                <Spacer size="l" />
                <Formik
                  initialValues={{
                    quality: '',
                    price: '',
                    feedback: '',
                  }}
                  validationSchema={productFeedbackSchema}
                  onSubmit={(values: Feedback) => {
                    sendFeedback(values);
                  }}>
                  {({
                    handleChange,
                    handleBlur,
                    handleSubmit,
                    values,
                    errors,
                    touched,
                  }) => (
                    <>
                      <Label
                        text={`${t('feedback.productQuality')} :`}
                        size="mx"
                        fontFamily="Medium"
                        color="categoryTitle"
                      />
                      <Spacer size="xm" />
                      <PhoneInputText
                        testID="txtFeedbackModalQquality"
                        inputStyle={styles.input}
                        searchIconStyle={styles.searchIconStyle}
                        style={styles.inputBorderStyle}
                        placeholder={t('feedback.productQualityFeedback')}
                        placeholderTextColor={colors.text2}
                        onChangeText={handleChange('quality')}
                        onBlur={handleBlur('quality')}
                        value={values.quality}
                        error={String(
                          t(touched.quality ? (errors.quality as string) : ''),
                        )}
                      />
                      <Spacer size="l" />
                      <Label
                        text={`${t('feedback.productPrice')} :`}
                        size="mx"
                        fontFamily="Medium"
                        color="categoryTitle"
                      />
                      <Spacer size="xm" />
                      <PhoneInputText
                        testID="txtFeedbackModalProductPriceFeedback"
                        inputStyle={styles.input}
                        searchIconStyle={styles.searchIconStyle}
                        style={styles.inputBorderStyle}
                        placeholder={t('feedback.productPriceFeedBack')}
                        placeholderTextColor={colors.text2}
                        onChangeText={handleChange('price')}
                        onBlur={handleBlur('price')}
                        value={values.price}
                        error={String(
                          t(touched.price ? (errors.price as string) : ''),
                        )}
                        type="numeric"
                      />
                      <Spacer size="l" />
                      <Label
                        text={`${t('feedback.otherFeedback')} :`}
                        size="mx"
                        fontFamily="Medium"
                        color="categoryTitle"
                      />
                      <Spacer size="xm" />
                      <PhoneInputText
                        testID="txtFeedbackModalFeedback"
                        inputStyle={styles.input}
                        searchIconStyle={styles.searchIconStyle}
                        style={styles.inputBorderStyle}
                        placeholder={t('feedback.Feedback')}
                        placeholderTextColor={colors.text2}
                        onChangeText={handleChange('feedback')}
                        onBlur={handleBlur('feedback')}
                        value={values.feedback}
                        error={String(
                          t(touched.feedback ? (errors.feedback as string) : ''),
                        )}
                        keyboardType="default"
                      />
                      <Spacer size="l" />

                      <Button
                        onPress={() => {
                          handleSubmit(values, touched);
                        }}
                        radius="xms"
                        labelSize="mx"
                        // size="large"
                        paddingHorizontal="x7l"
                        text={t('buttons.submitFeedback')}
                        type="bordered"
                        borderColor="sunnyOrange3"
                        style={styles.submitView}
                        labelColor="newSunnyOrange"
                      />
                      <Spacer size="xx" />
                    </>
                  )}
                </Formik>
              </ScrollView>
            </View>
            <Toast config={toastConfig} swipeable={false} />
          </View>
        </KeyboardAvoidingView>
      }
    />
  );
};

export default React.memo(ValuableFeedbackModal);
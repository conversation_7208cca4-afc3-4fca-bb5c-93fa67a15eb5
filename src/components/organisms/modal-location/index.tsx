import React, {useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/FontAwesome';
import {useTranslation} from 'react-i18next';
import {Label, Spacer} from 'components/atoms';
import {Button, InputBox} from 'components/molecules';

import stylesWithOutColor from './style';
import {Sizes} from 'common';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  onSave: (text: string) => void;
};

const ModalLocation = ({visible, onSave, onClose}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const {t} = useTranslation();
  const [location, setLocation] = useState<string>('');
  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View style={styles.container}>
        <View style={styles.body}>
          <TouchableOpacity onPress={onClose} style={styles.closeContainer}>
            <Icon name="close" size={Sizes.l} />
          </TouchableOpacity>
          <Spacer type="Vertical" size="x6l" />
          <Label text={t('productList.location')} size={'m'} />
          <InputBox
            placeholder={String(t('productList.enterLocation'))}
            onChangeText={setLocation}
            value={location}
          />
          <Spacer type="Vertical" size="xxl" />
          <Button text={t('buttons.save')} onPress={() => onSave(location)} />
        </View>
      </View>
    </Modal>
  );
};

export default ModalLocation;

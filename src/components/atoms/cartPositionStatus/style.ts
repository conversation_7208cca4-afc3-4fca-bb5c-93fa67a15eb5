import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    addressView: {
      alignItems: 'center',
      padding: Sizes.m,
      flexDirection: 'row',
    },
    addressSubView: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    flexView: {
      flex: Sizes.x,
    },
    labelStyle: {
      marginHorizontal: Sizes.xs + Sizes.x,
    },
  });

export default styles;

import analytics from '@react-native-firebase/analytics';
import ReactMoE, {MoEProperties} from 'react-native-moengage';
import {sendCustomEvent, setCurrentScreenName} from '@microsoft/react-native-clarity';
import {NativeModules, Platform} from 'react-native';
import { debugError, debugLog } from 'utils/debugLog';

const eventNameMapping: Record<string, string> = {
  'Product Viewed': 'view_item',
  'Added to Cart': 'add_to_cart',
  'Product Searched': 'search',
  'Cart View': 'view_cart',
  'Checkout Completed': 'purchase',
  'Login-Email/Phone': 'login',
  'Sign Up/Login Google': 'login',
  'Sign Up-Email/Phone': 'sign_up',
  'Remove From Cart': 'remove_from_cart',
  'Shipping Details Selected': 'add_shipping_info',
  'Checkout Viewed': 'begin_checkout',
};
const eventDataKeyMapping: Record<string, string> = {
  search_keyword: 'search_term',
  // total_price: 'value',
  total_amount: 'value',
  sku: 'item_id',
  product_name: 'item_name',
  brand: 'item_brand',
  category: 'item_category',
  order_id: 'transaction_id',
  shipping_charge: 'shipping',
  tax_amount: 'tax',
  customer_id: 'user_id',
};
// Logs event to Firebase Analytics

function getScreenNameForAction(actionName: string) {
  const screenNameMap = {
    BRAND_VIEW: 'Brand Category',
    VIEW_PAGE: 'Home',
    BANNER_CLICKED: 'Home',
    PRODUCT_SEARCHED: 'Search',
    PRODUCT_SCREEN: 'PDP page',
    CART_VIEWED: 'Cart page',
    USER_LOGGED_IN: 'Sign in',
    VIEW_ALL_HOMEPAGE: 'Home',
    PAYMENT_FAILURE: 'Payment page',
    CHECKOUT_STARTED: 'Cart page',
    CHECKOUT_VIEW: 'Cart page',
    CHECKOUT_COMPLETED: 'Payment page',
    SHIPPING_DETAILS_SELECTED: 'Address',
    SHIPPING_DETAILS_UPDATED: 'Address',
    BUY_NOW: 'Buy now',
    ORDER_CANCELLED: 'Orders',
    RETRY_PAYMENT: 'Payments',
    SIGN_UP_LOGIN_GOOGLE: 'Sign in with google',
    SIGN_UP_EMAIL_PHONE: 'Signup',
    REMOVE_FROM_CART: 'Cart page',
    BUY_AGAIN: 'Buy again',
    MEMBERSHIP_ADD_TO_CART: 'My membership',
    TRACK_ORDER: 'Orders',
    RETURN_ORDER: 'Orders',
    USER_UPDATE: 'My profile',
    CATEGORY_VIEWED: 'Brand Category',
    MAGAZINE: 'Magazine',
    SHORTS: 'Shorts',
    WISHLIST: 'My Wishlist',
    SPLASH_SCREEN: 'Splash screen',
    ERROR: 'Error 404',
    PRIVACY: 'Privacy',
    HELP_CENTER: 'Help center',
    RETURN: 'Return',
    ACCOUNT: 'Account',
    ALL_BRANDS: 'ALL brands directory',
    DIRECTORY_FOR_CATEGORY: 'Full store directory for category',
    REWARD_ZONE: 'Reward zone',
    MY_REWARDS: 'My rewards',
    RETURN_POLICY: 'Return Policy',
    THANK_YOU: 'Thank you pages',
    NEWS: 'News tab',
    BLOGS: 'Blogs tab',
    EMPTY_CART: 'Empty cart'
  };

  return screenNameMap[actionName as keyof typeof screenNameMap] || '';
}
export const logAnalyticsEvent = async (eventName: string, data = {}) => {
  try {
    const firebaseEventName = eventNameMapping[eventName] || eventName;

    // Convert eventName to lowercase and replace spaces with underscores
    const formattedEventName = firebaseEventName
      .toLowerCase()
      .replace(/\s+/g, '_');
    // Format data keys to lowercase with underscores
    const formattedData: Record<string, any> = {};

    Object.entries(data).forEach(([key, value]) => {
      const formattedKey = key.toLowerCase().replace(/\s+/g, '_');
      const mappedKey = eventDataKeyMapping[formattedKey] || formattedKey;
      if (mappedKey === 'quantity' && Array.isArray(value)) {
        formattedData[mappedKey] = value.reduce((acc, curr) => acc + curr, 0);
      } else {
        formattedData[mappedKey] = value;
      }
    });
    debugLog(
      'Firebase------>',
      JSON.stringify(formattedEventName),
      JSON.stringify(formattedData),
    );
    await analytics().logEvent(formattedEventName, formattedData);
  } catch (error) {
    debugError(
      `Error logging event to Firebase Analytics: ${eventName}`,
      error,
    );
  }
};

// Logs event to MoEngage
export const logMoEngageEvent = (eventName: string, data = {}) => {
  try {
    // Add initialization check
    if (!ReactMoE) {
      debugError('MoEngage SDK not initialized');
      return;
    }

    const properties = new MoEProperties();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        properties.addAttribute(key, value);
      }
    });
    debugLog(
      'logMoEngageEvent------>',
      JSON.stringify(eventName),
      JSON.stringify(properties),
    );

    ReactMoE.trackEvent(eventName, properties);
  } catch (error) {
    debugError(`Error logging event to MoEngage: ${eventName}`, error);
  }
};

// Logs event to Clarity-MICROSOFT
export const logClarityEvent = async (
  eventName: string,
  actionName: string | undefined,
) => {
  if (actionName) {
    const screenName = getScreenNameForAction(actionName);
    setCurrentScreenName(screenName);
  }
  const formattedEventName =
    eventName.charAt(0).toUpperCase() + eventName.slice(1).toLowerCase();

  sendCustomEvent(formattedEventName)
    .then(v => {
      debugLog(
        '*** clarity added ' + JSON.stringify(v) + formattedEventName,
      );
    })
    .catch(e => {
      debugLog(
        '*** clarity error ' + JSON.stringify(e) + formattedEventName,
      );
    });
};

// Logs event to EasyEnsight
export const logEasyEnsightEvent = async (eventName: string, data = {}) => {
  if(Platform.OS === 'android') {
  try {
    const {EISDKModule} = NativeModules;
    debugLog(`🔹 [EasyEnsight] Event Triggered: ${eventName}`);

    // ✅ Convert event data into a JSON string
    const formattedData = JSON.stringify(
      Object.fromEntries(
        Object.entries(data).map(([key, value]) => [
          key,
          value?.toString() ?? '',
        ]),
      ),
    );

    debugLog(
      `ℹ️ [EasyEnsight] Formatted Data for Event '${eventName}':`,
      formattedData,
    );

    // ✅ Ensure logEvent returns a promise (without callbacks)
    const res = await EISDKModule.logEvent(eventName, formattedData);

    debugLog(
      `🎯 [EasyEnsight] Event '${eventName}' Successfully Logged!`,
      res,
    );
  } catch (err) {
    debugError(`❌ [EasyEnsight] Error Logging Event '${eventName}':`, err);
  }
  }
};

// Main function that logs events to both Firebase and MoEngage
export const LogEvents = async (
  eventName: string,
  data = {},
  actionName?: string,
) => {
  await Promise.all([
    logMoEngageEvent(eventName, data),
    logAnalyticsEvent(eventName, data),
    logClarityEvent(eventName, actionName),
    logEasyEnsightEvent(eventName, data),
  ]);
};

// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* DentalkartTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* DentalkartTests.m */; };
		066CB2F42D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 066CB2F32D5DD9F8006E07D6 /* LaunchScreen.storyboard */; };
		066CB2F62D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 066CB2F32D5DD9F8006E07D6 /* LaunchScreen.storyboard */; };
		066CB2F72D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 066CB2F32D5DD9F8006E07D6 /* LaunchScreen.storyboard */; };
		066CB2F82D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 066CB2F32D5DD9F8006E07D6 /* LaunchScreen.storyboard */; };
		066CB2F92D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 066CB2F32D5DD9F8006E07D6 /* LaunchScreen.storyboard */; };
		13AA0C648FA44ABC861F0FD0 /* Poppins-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 22687AA83878440C97F0EE65 /* Poppins-ExtraBoldItalic.ttf */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		14085F406625C42DD490E679 /* Pods_NSEDev.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 09BF20FFE53761E7A92B27BE /* Pods_NSEDev.framework */; };
		1B6D8A89861760DBA44C2FC9 /* Pods_Staging.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95BEB8D7B561D419A4CECBB6 /* Pods_Staging.framework */; };
		26485917246547D48F5BCD68 /* Poppins-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 87FAAD71C81642B8A4AA98EE /* Poppins-BlackItalic.ttf */; };
		303A558B368349CE9D4F1F81 /* Poppins-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 320439185C6442C393B4A842 /* Poppins-ExtraBold.ttf */; };
		32D3E96118774C1EB14E8024 /* Poppins-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC37B5D4EFA74A728B1E3C54 /* Poppins-Light.ttf */; };
		38403EAD5CA24F1191F5EEB7 /* OFL.txt in Resources */ = {isa = PBXBuildFile; fileRef = CAFEE9EC2E644D4CB72EF053 /* OFL.txt */; };
		3F9B26AE241441319BD92608 /* Poppins-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 52F4B6CFDF9B4C1CA5AA268A /* Poppins-Bold.ttf */; };
		410D3FACC77345208F5B4115 /* Poppins-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF50B8179A8C487F9A668FC9 /* Poppins-SemiBoldItalic.ttf */; };
		445F135C4A9E4833B0AA22F4 /* Poppins-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 39134CF061F843A3A2490705 /* Poppins-BoldItalic.ttf */; };
		44B100B5DFEC4F85A2CCFE7B /* Poppins-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2312947C41124E99ABB7A536 /* Poppins-Italic.ttf */; };
		46D44EB76CB34495AE77D022 /* Poppins-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 46BEF89AE0D241DC94666FBB /* Poppins-ExtraLightItalic.ttf */; };
		5073A1CC6CFD460BAD325ABC /* Poppins-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2FC994F87FBE4854B9A62EF1 /* Poppins-Medium.ttf */; };
		535E97F759AD4446B44FA6C6 /* Poppins-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EFB9314EAB3645979E5F0BDF /* Poppins-Black.ttf */; };
		6C82B5DBA935472480E58E14 /* Poppins-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5A8A4917FD974831AFCCB8DC /* Poppins-LightItalic.ttf */; };
		7199B8513BECA562F93EBD74 /* libPods-DentalkartTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 15FE6D994A99898ED5860572 /* libPods-DentalkartTests.a */; };
		7EBBEF6D713546839244EC1F /* Poppins-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 76492A843E134DCB885F7145 /* Poppins-SemiBold.ttf */; };
		82A211048E9E480BB4F752C6 /* Poppins-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9877317A806640269D2FAED7 /* Poppins-Thin.ttf */; };
		A9A0BDFBA93A47C88B1F9043 /* Poppins-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83856419591A4BCA9321EF12 /* Poppins-ThinItalic.ttf */; };
		D746DF0A2C9311790042689E /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = D746DF082C9311790042689E /* GoogleService-Info.plist */; };
		D746DF0F2C9311860042689E /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = D746DF0C2C9311860042689E /* GoogleService-Info.plist */; };
		D746DF232C9322570042689E /* dev_info.plist in Resources */ = {isa = PBXBuildFile; fileRef = D746DF222C9322570042689E /* dev_info.plist */; };
		D746DF262C9323D20042689E /* staging_info.plist in Resources */ = {isa = PBXBuildFile; fileRef = D746DF242C9323CE0042689E /* staging_info.plist */; };
		D7838E5E2D167C1100958C96 /* NSEDev.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = D7838E572D167C1100958C96 /* NSEDev.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		D7AB5F1D2C92C0EC00819EBB /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		D7AB5F1E2C92C0EC00819EBB /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		D7AB5F202C92C0EC00819EBB /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBC50A492B342AAC005ECC80 /* AdSupport.framework */; };
		D7AB5F242C92C0EC00819EBB /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		D7AB5F262C92C0EC00819EBB /* Poppins-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EFB9314EAB3645979E5F0BDF /* Poppins-Black.ttf */; };
		D7AB5F272C92C0EC00819EBB /* Poppins-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 87FAAD71C81642B8A4AA98EE /* Poppins-BlackItalic.ttf */; };
		D7AB5F282C92C0EC00819EBB /* Poppins-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 52F4B6CFDF9B4C1CA5AA268A /* Poppins-Bold.ttf */; };
		D7AB5F292C92C0EC00819EBB /* Poppins-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 39134CF061F843A3A2490705 /* Poppins-BoldItalic.ttf */; };
		D7AB5F2A2C92C0EC00819EBB /* Poppins-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 320439185C6442C393B4A842 /* Poppins-ExtraBold.ttf */; };
		D7AB5F2B2C92C0EC00819EBB /* Poppins-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 22687AA83878440C97F0EE65 /* Poppins-ExtraBoldItalic.ttf */; };
		D7AB5F2C2C92C0EC00819EBB /* Poppins-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE9A7C84A4F2461097245683 /* Poppins-ExtraLight.ttf */; };
		D7AB5F2D2C92C0EC00819EBB /* Poppins-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 46BEF89AE0D241DC94666FBB /* Poppins-ExtraLightItalic.ttf */; };
		D7AB5F2E2C92C0EC00819EBB /* Poppins-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2312947C41124E99ABB7A536 /* Poppins-Italic.ttf */; };
		D7AB5F2F2C92C0EC00819EBB /* Poppins-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC37B5D4EFA74A728B1E3C54 /* Poppins-Light.ttf */; };
		D7AB5F302C92C0EC00819EBB /* Poppins-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5A8A4917FD974831AFCCB8DC /* Poppins-LightItalic.ttf */; };
		D7AB5F312C92C0EC00819EBB /* Poppins-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2FC994F87FBE4854B9A62EF1 /* Poppins-Medium.ttf */; };
		D7AB5F322C92C0EC00819EBB /* Poppins-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 145D07697A9241D6B852CA3C /* Poppins-MediumItalic.ttf */; };
		D7AB5F332C92C0EC00819EBB /* Poppins-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 018535EF69F44E49B72EEAC1 /* Poppins-Regular.ttf */; };
		D7AB5F342C92C0EC00819EBB /* Poppins-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 76492A843E134DCB885F7145 /* Poppins-SemiBold.ttf */; };
		D7AB5F352C92C0EC00819EBB /* Poppins-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF50B8179A8C487F9A668FC9 /* Poppins-SemiBoldItalic.ttf */; };
		D7AB5F362C92C0EC00819EBB /* Poppins-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9877317A806640269D2FAED7 /* Poppins-Thin.ttf */; };
		D7AB5F372C92C0EC00819EBB /* Poppins-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83856419591A4BCA9321EF12 /* Poppins-ThinItalic.ttf */; };
		D7AB5F382C92C0EC00819EBB /* OFL.txt in Resources */ = {isa = PBXBuildFile; fileRef = CAFEE9EC2E644D4CB72EF053 /* OFL.txt */; };
		D7AB5F392C92C0EC00819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5F3A2C92C0EC00819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5F3B2C92C0EC00819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5F3C2C92C0EC00819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5F3D2C92C0EC00819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5F872C92C16200819EBB /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		D7AB5F882C92C16200819EBB /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		D7AB5F8A2C92C16200819EBB /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBC50A492B342AAC005ECC80 /* AdSupport.framework */; };
		D7AB5F8E2C92C16200819EBB /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		D7AB5F902C92C16200819EBB /* Poppins-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EFB9314EAB3645979E5F0BDF /* Poppins-Black.ttf */; };
		D7AB5F912C92C16200819EBB /* Poppins-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 87FAAD71C81642B8A4AA98EE /* Poppins-BlackItalic.ttf */; };
		D7AB5F922C92C16200819EBB /* Poppins-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 52F4B6CFDF9B4C1CA5AA268A /* Poppins-Bold.ttf */; };
		D7AB5F932C92C16200819EBB /* Poppins-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 39134CF061F843A3A2490705 /* Poppins-BoldItalic.ttf */; };
		D7AB5F942C92C16200819EBB /* Poppins-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 320439185C6442C393B4A842 /* Poppins-ExtraBold.ttf */; };
		D7AB5F952C92C16200819EBB /* Poppins-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 22687AA83878440C97F0EE65 /* Poppins-ExtraBoldItalic.ttf */; };
		D7AB5F962C92C16200819EBB /* Poppins-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE9A7C84A4F2461097245683 /* Poppins-ExtraLight.ttf */; };
		D7AB5F972C92C16200819EBB /* Poppins-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 46BEF89AE0D241DC94666FBB /* Poppins-ExtraLightItalic.ttf */; };
		D7AB5F982C92C16200819EBB /* Poppins-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2312947C41124E99ABB7A536 /* Poppins-Italic.ttf */; };
		D7AB5F992C92C16200819EBB /* Poppins-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC37B5D4EFA74A728B1E3C54 /* Poppins-Light.ttf */; };
		D7AB5F9A2C92C16200819EBB /* Poppins-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5A8A4917FD974831AFCCB8DC /* Poppins-LightItalic.ttf */; };
		D7AB5F9B2C92C16200819EBB /* Poppins-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2FC994F87FBE4854B9A62EF1 /* Poppins-Medium.ttf */; };
		D7AB5F9C2C92C16200819EBB /* Poppins-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 145D07697A9241D6B852CA3C /* Poppins-MediumItalic.ttf */; };
		D7AB5F9D2C92C16200819EBB /* Poppins-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 018535EF69F44E49B72EEAC1 /* Poppins-Regular.ttf */; };
		D7AB5F9E2C92C16200819EBB /* Poppins-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 76492A843E134DCB885F7145 /* Poppins-SemiBold.ttf */; };
		D7AB5F9F2C92C16200819EBB /* Poppins-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF50B8179A8C487F9A668FC9 /* Poppins-SemiBoldItalic.ttf */; };
		D7AB5FA02C92C16200819EBB /* Poppins-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9877317A806640269D2FAED7 /* Poppins-Thin.ttf */; };
		D7AB5FA12C92C16200819EBB /* Poppins-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83856419591A4BCA9321EF12 /* Poppins-ThinItalic.ttf */; };
		D7AB5FA22C92C16200819EBB /* OFL.txt in Resources */ = {isa = PBXBuildFile; fileRef = CAFEE9EC2E644D4CB72EF053 /* OFL.txt */; };
		D7AB5FA32C92C16200819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5FA42C92C16200819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5FA52C92C16200819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5FA62C92C16200819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7AB5FA72C92C16200819EBB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		D7C2C44F2D1696A200991430 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBAF5A782BC17FC30016DD1F /* UserNotifications.framework */; };
		D7C2C4502D1696A200991430 /* UserNotificationsUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBAF5A7A2BC17FC30016DD1F /* UserNotificationsUI.framework */; };
		D7C2C45A2D1696A200991430 /* NCEDev.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = D7C2C44E2D1696A200991430 /* NCEDev.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		DD1EC94F676B4FE8A1410B14 /* Poppins-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 018535EF69F44E49B72EEAC1 /* Poppins-Regular.ttf */; };
		E4E963B41583DFB583D0597C /* Pods_NCEDev.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 35001DB42729DEB3B9FA3D43 /* Pods_NCEDev.framework */; };
		E7EC9C19BFCF8FCDAE51AC22 /* Pods_Dev.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F74AC62B7F0C5B67D70640F7 /* Pods_Dev.framework */; };
		F4C96F59F023392567808FAB /* Pods_Dentalkart.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D13E5EB85CADF63F960028B9 /* Pods_Dentalkart.framework */; };
		FB1FFB6D2B270155000494E1 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = FB1FFB6C2B270155000494E1 /* GoogleService-Info.plist */; };
		FBC50A4B2B342AE4005ECC80 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBC50A492B342AAC005ECC80 /* AdSupport.framework */; };
		FD848726EFFC40E787D0D9EB /* Poppins-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE9A7C84A4F2461097245683 /* Poppins-ExtraLight.ttf */; };
		FDC1530060C34510BDE14847 /* Poppins-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 145D07697A9241D6B852CA3C /* Poppins-MediumItalic.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = Dentalkart;
		};
		D7838E5C2D167C1100958C96 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D7838E562D167C1100958C96;
			remoteInfo = NSEDev;
		};
		D7C2C4582D1696A200991430 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D7C2C44D2D1696A200991430;
			remoteInfo = NCEDev;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		D7AB5F3F2C92C0EC00819EBB /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 8;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 1;
		};
		D7AB5FA92C92C16200819EBB /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				D7838E5E2D167C1100958C96 /* NSEDev.appex in Embed Foundation Extensions */,
				D7C2C45A2D1696A200991430 /* NCEDev.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		FBAF5A722BC17F990016DD1F /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* DentalkartTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DentalkartTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* DentalkartTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DentalkartTests.m; sourceTree = "<group>"; };
		018535EF69F44E49B72EEAC1 /* Poppins-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Regular.ttf"; path = "../src/assets/fonts/Poppins-Regular.ttf"; sourceTree = "<group>"; };
		066CB2F32D5DD9F8006E07D6 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		09BF20FFE53761E7A92B27BE /* Pods_NSEDev.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NSEDev.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0D9A1CFF420FCB8CC28466A5 /* Pods-Dentalkart.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dentalkart.release.xcconfig"; path = "Target Support Files/Pods-Dentalkart/Pods-Dentalkart.release.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Dentalkart.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Dentalkart.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = Dentalkart/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = Dentalkart/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Dentalkart/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Dentalkart/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = Dentalkart/main.m; sourceTree = "<group>"; };
		145D07697A9241D6B852CA3C /* Poppins-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-MediumItalic.ttf"; path = "../src/assets/fonts/Poppins-MediumItalic.ttf"; sourceTree = "<group>"; };
		15FE6D994A99898ED5860572 /* libPods-DentalkartTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-DentalkartTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		22687AA83878440C97F0EE65 /* Poppins-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraBoldItalic.ttf"; path = "../src/assets/fonts/Poppins-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		2312947C41124E99ABB7A536 /* Poppins-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Italic.ttf"; path = "../src/assets/fonts/Poppins-Italic.ttf"; sourceTree = "<group>"; };
		24910D2E98D448E18173559B /* Pods-DentalkartTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DentalkartTests.release.xcconfig"; path = "Target Support Files/Pods-DentalkartTests/Pods-DentalkartTests.release.xcconfig"; sourceTree = "<group>"; };
		2B17CC0C2CA1C5C7EEF4B7A6 /* Pods-NCEDev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NCEDev.debug.xcconfig"; path = "Target Support Files/Pods-NCEDev/Pods-NCEDev.debug.xcconfig"; sourceTree = "<group>"; };
		2FC994F87FBE4854B9A62EF1 /* Poppins-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Medium.ttf"; path = "../src/assets/fonts/Poppins-Medium.ttf"; sourceTree = "<group>"; };
		320439185C6442C393B4A842 /* Poppins-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraBold.ttf"; path = "../src/assets/fonts/Poppins-ExtraBold.ttf"; sourceTree = "<group>"; };
		35001DB42729DEB3B9FA3D43 /* Pods_NCEDev.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NCEDev.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		39134CF061F843A3A2490705 /* Poppins-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-BoldItalic.ttf"; path = "../src/assets/fonts/Poppins-BoldItalic.ttf"; sourceTree = "<group>"; };
		4194D42F6585AC34286506B7 /* Pods-Dev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dev.release.xcconfig"; path = "Target Support Files/Pods-Dev/Pods-Dev.release.xcconfig"; sourceTree = "<group>"; };
		46BEF89AE0D241DC94666FBB /* Poppins-ExtraLightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraLightItalic.ttf"; path = "../src/assets/fonts/Poppins-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		4B8680EA853F94DF09833D49 /* Pods-NSEDev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NSEDev.debug.xcconfig"; path = "Target Support Files/Pods-NSEDev/Pods-NSEDev.debug.xcconfig"; sourceTree = "<group>"; };
		52F4B6CFDF9B4C1CA5AA268A /* Poppins-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Bold.ttf"; path = "../src/assets/fonts/Poppins-Bold.ttf"; sourceTree = "<group>"; };
		5A8A4917FD974831AFCCB8DC /* Poppins-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-LightItalic.ttf"; path = "../src/assets/fonts/Poppins-LightItalic.ttf"; sourceTree = "<group>"; };
		76492A843E134DCB885F7145 /* Poppins-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-SemiBold.ttf"; path = "../src/assets/fonts/Poppins-SemiBold.ttf"; sourceTree = "<group>"; };
		83856419591A4BCA9321EF12 /* Poppins-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ThinItalic.ttf"; path = "../src/assets/fonts/Poppins-ThinItalic.ttf"; sourceTree = "<group>"; };
		87FAAD71C81642B8A4AA98EE /* Poppins-BlackItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-BlackItalic.ttf"; path = "../src/assets/fonts/Poppins-BlackItalic.ttf"; sourceTree = "<group>"; };
		95BEB8D7B561D419A4CECBB6 /* Pods_Staging.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Staging.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9877317A806640269D2FAED7 /* Poppins-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Thin.ttf"; path = "../src/assets/fonts/Poppins-Thin.ttf"; sourceTree = "<group>"; };
		98EA3CDD7F880897CBB2C0FB /* Pods-NCEDev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NCEDev.release.xcconfig"; path = "Target Support Files/Pods-NCEDev/Pods-NCEDev.release.xcconfig"; sourceTree = "<group>"; };
		9D9ED936741E019EEC6ACABF /* Pods-DentalkartTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DentalkartTests.debug.xcconfig"; path = "Target Support Files/Pods-DentalkartTests/Pods-DentalkartTests.debug.xcconfig"; sourceTree = "<group>"; };
		AF50B8179A8C487F9A668FC9 /* Poppins-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-SemiBoldItalic.ttf"; path = "../src/assets/fonts/Poppins-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		B86819497E5DFCC41950B7B8 /* Pods-Dentalkart.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dentalkart.debug.xcconfig"; path = "Target Support Files/Pods-Dentalkart/Pods-Dentalkart.debug.xcconfig"; sourceTree = "<group>"; };
		BA62FCF07BFF11AB19B47E06 /* Pods-Dev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dev.debug.xcconfig"; path = "Target Support Files/Pods-Dev/Pods-Dev.debug.xcconfig"; sourceTree = "<group>"; };
		BAC3B521BD53748296043FC4 /* Pods-Staging.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Staging.release.xcconfig"; path = "Target Support Files/Pods-Staging/Pods-Staging.release.xcconfig"; sourceTree = "<group>"; };
		BB3ED2A85D3F8CC98B112D03 /* Pods-NSEDev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NSEDev.release.xcconfig"; path = "Target Support Files/Pods-NSEDev/Pods-NSEDev.release.xcconfig"; sourceTree = "<group>"; };
		BE9A7C84A4F2461097245683 /* Poppins-ExtraLight.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-ExtraLight.ttf"; path = "../src/assets/fonts/Poppins-ExtraLight.ttf"; sourceTree = "<group>"; };
		CAFEE9EC2E644D4CB72EF053 /* OFL.txt */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = OFL.txt; path = ../src/assets/fonts/OFL.txt; sourceTree = "<group>"; };
		CC37B5D4EFA74A728B1E3C54 /* Poppins-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Light.ttf"; path = "../src/assets/fonts/Poppins-Light.ttf"; sourceTree = "<group>"; };
		D0EAD00F2A16A012978415C9 /* Pods-Staging.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Staging.debug.xcconfig"; path = "Target Support Files/Pods-Staging/Pods-Staging.debug.xcconfig"; sourceTree = "<group>"; };
		D13E5EB85CADF63F960028B9 /* Pods_Dentalkart.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Dentalkart.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D2BA10D1293DDD390025CFCA /* Lato-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-BoldItalic.ttf"; path = "../src/assets/fonts/Lato-BoldItalic.ttf"; sourceTree = "<group>"; };
		D2BA10D2293DDD390025CFCA /* Lato-ThinItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-ThinItalic.ttf"; path = "../src/assets/fonts/Lato-ThinItalic.ttf"; sourceTree = "<group>"; };
		D2BA10D3293DDD390025CFCA /* Lato-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-Bold.ttf"; path = "../src/assets/fonts/Lato-Bold.ttf"; sourceTree = "<group>"; };
		D2BA10D4293DDD390025CFCA /* Lato-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-LightItalic.ttf"; path = "../src/assets/fonts/Lato-LightItalic.ttf"; sourceTree = "<group>"; };
		D2BA10D5293DDD390025CFCA /* Lato-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-Regular.ttf"; path = "../src/assets/fonts/Lato-Regular.ttf"; sourceTree = "<group>"; };
		D2BA10D6293DDD390025CFCA /* Lato-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-Black.ttf"; path = "../src/assets/fonts/Lato-Black.ttf"; sourceTree = "<group>"; };
		D2BA10D8293DDD390025CFCA /* Lato-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-Light.ttf"; path = "../src/assets/fonts/Lato-Light.ttf"; sourceTree = "<group>"; };
		D2BA10D9293DDD390025CFCA /* Lato-BlackItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-BlackItalic.ttf"; path = "../src/assets/fonts/Lato-BlackItalic.ttf"; sourceTree = "<group>"; };
		D2BA10DA293DDD390025CFCA /* Lato-Thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Lato-Thin.ttf"; path = "../src/assets/fonts/Lato-Thin.ttf"; sourceTree = "<group>"; };
		D746DF082C9311790042689E /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "Staging/GoogleService-Info.plist"; sourceTree = "<group>"; };
		D746DF0C2C9311860042689E /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "Dev/GoogleService-Info.plist"; sourceTree = "<group>"; };
		D746DF222C9322570042689E /* dev_info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = dev_info.plist; sourceTree = "<group>"; };
		D746DF242C9323CE0042689E /* staging_info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = staging_info.plist; sourceTree = "<group>"; };
		D75E02992CB517A400FF4151 /* WebEngage.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:S7X7V9YPX7:Sarthak Sharma"; lastKnownFileType = wrapper.xcframework; name = WebEngage.xcframework; path = Pods/WebEngage/WebEngage.xcframework; sourceTree = "<group>"; };
		D75E029D2CB5180E00FF4151 /* WebEngage.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WebEngage.xcframework; path = ../../../../../../Downloads/WebEngageFramework/WebEngage.xcframework; sourceTree = "<group>"; };
		D7838E572D167C1100958C96 /* NSEDev.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NSEDev.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		D7AB5F482C92C0EC00819EBB /* Staging.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Staging.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D7AB5FB22C92C16200819EBB /* Dev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Dev.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D7C2C44E2D1696A200991430 /* NCEDev.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NCEDev.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		E7762B51C42E480195913A68 /* Lato-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Italic.ttf"; path = "../src/assets/fonts/Lato-Italic.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		EFB9314EAB3645979E5F0BDF /* Poppins-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Black.ttf"; path = "../src/assets/fonts/Poppins-Black.ttf"; sourceTree = "<group>"; };
		F74AC62B7F0C5B67D70640F7 /* Pods_Dev.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Dev.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FB12518D2B077EAA0039B82B /* Dentalkart.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = Dentalkart.entitlements; path = Dentalkart/Dentalkart.entitlements; sourceTree = "<group>"; };
		FB1FFB6C2B270155000494E1 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		FBAF5A782BC17FC30016DD1F /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		FBAF5A7A2BC17FC30016DD1F /* UserNotificationsUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotificationsUI.framework; path = System/Library/Frameworks/UserNotificationsUI.framework; sourceTree = SDKROOT; };
		FBC50A492B342AAC005ECC80 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		D7838E5F2D167C1100958C96 /* Exceptions for "NSEDev" folder in "NSEDev" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = D7838E562D167C1100958C96 /* NSEDev */;
		};
		D7C2C45B2D1696A200991430 /* Exceptions for "NCEDev" folder in "NCEDev" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = D7C2C44D2D1696A200991430 /* NCEDev */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		D7838E582D167C1100958C96 /* NSEDev */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				D7838E5F2D167C1100958C96 /* Exceptions for "NSEDev" folder in "NSEDev" target */,
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = NSEDev;
			sourceTree = "<group>";
		};
		D7C2C4512D1696A200991430 /* NCEDev */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				D7C2C45B2D1696A200991430 /* Exceptions for "NCEDev" folder in "NCEDev" target */,
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = NCEDev;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				7199B8513BECA562F93EBD74 /* libPods-DentalkartTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				FBC50A4B2B342AE4005ECC80 /* AdSupport.framework in Frameworks */,
				F4C96F59F023392567808FAB /* Pods_Dentalkart.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7838E542D167C1100958C96 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				14085F406625C42DD490E679 /* Pods_NSEDev.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7AB5F1F2C92C0EC00819EBB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				D7AB5F202C92C0EC00819EBB /* AdSupport.framework in Frameworks */,
				1B6D8A89861760DBA44C2FC9 /* Pods_Staging.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7AB5F892C92C16200819EBB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				D7AB5F8A2C92C16200819EBB /* AdSupport.framework in Frameworks */,
				E7EC9C19BFCF8FCDAE51AC22 /* Pods_Dev.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7C2C44B2D1696A200991430 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				D7C2C4502D1696A200991430 /* UserNotificationsUI.framework in Frameworks */,
				D7C2C44F2D1696A200991430 /* UserNotifications.framework in Frameworks */,
				E4E963B41583DFB583D0597C /* Pods_NCEDev.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* DentalkartTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* DentalkartTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = DentalkartTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* Dentalkart */ = {
			isa = PBXGroup;
			children = (
				066CB2F32D5DD9F8006E07D6 /* LaunchScreen.storyboard */,
				FB1FFB6C2B270155000494E1 /* GoogleService-Info.plist */,
				D746DF082C9311790042689E /* GoogleService-Info.plist */,
				D746DF0C2C9311860042689E /* GoogleService-Info.plist */,
				FB12518D2B077EAA0039B82B /* Dentalkart.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = Dentalkart;
			sourceTree = "<group>";
		};
		16D283788B184CBE89AAA619 /* Resources */ = {
			isa = PBXGroup;
			children = (
				E7762B51C42E480195913A68 /* Lato-Italic.ttf */,
				EFB9314EAB3645979E5F0BDF /* Poppins-Black.ttf */,
				87FAAD71C81642B8A4AA98EE /* Poppins-BlackItalic.ttf */,
				52F4B6CFDF9B4C1CA5AA268A /* Poppins-Bold.ttf */,
				39134CF061F843A3A2490705 /* Poppins-BoldItalic.ttf */,
				320439185C6442C393B4A842 /* Poppins-ExtraBold.ttf */,
				22687AA83878440C97F0EE65 /* Poppins-ExtraBoldItalic.ttf */,
				BE9A7C84A4F2461097245683 /* Poppins-ExtraLight.ttf */,
				46BEF89AE0D241DC94666FBB /* Poppins-ExtraLightItalic.ttf */,
				2312947C41124E99ABB7A536 /* Poppins-Italic.ttf */,
				CC37B5D4EFA74A728B1E3C54 /* Poppins-Light.ttf */,
				5A8A4917FD974831AFCCB8DC /* Poppins-LightItalic.ttf */,
				2FC994F87FBE4854B9A62EF1 /* Poppins-Medium.ttf */,
				145D07697A9241D6B852CA3C /* Poppins-MediumItalic.ttf */,
				018535EF69F44E49B72EEAC1 /* Poppins-Regular.ttf */,
				76492A843E134DCB885F7145 /* Poppins-SemiBold.ttf */,
				AF50B8179A8C487F9A668FC9 /* Poppins-SemiBoldItalic.ttf */,
				9877317A806640269D2FAED7 /* Poppins-Thin.ttf */,
				83856419591A4BCA9321EF12 /* Poppins-ThinItalic.ttf */,
				CAFEE9EC2E644D4CB72EF053 /* OFL.txt */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				D75E02992CB517A400FF4151 /* WebEngage.xcframework */,
				D75E029D2CB5180E00FF4151 /* WebEngage.xcframework */,
				FBC50A492B342AAC005ECC80 /* AdSupport.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				FBAF5A782BC17FC30016DD1F /* UserNotifications.framework */,
				FBAF5A7A2BC17FC30016DD1F /* UserNotificationsUI.framework */,
				D13E5EB85CADF63F960028B9 /* Pods_Dentalkart.framework */,
				15FE6D994A99898ED5860572 /* libPods-DentalkartTests.a */,
				F74AC62B7F0C5B67D70640F7 /* Pods_Dev.framework */,
				35001DB42729DEB3B9FA3D43 /* Pods_NCEDev.framework */,
				09BF20FFE53761E7A92B27BE /* Pods_NSEDev.framework */,
				95BEB8D7B561D419A4CECBB6 /* Pods_Staging.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				D746DF242C9323CE0042689E /* staging_info.plist */,
				D746DF222C9322570042689E /* dev_info.plist */,
				D2BA10D6293DDD390025CFCA /* Lato-Black.ttf */,
				D2BA10D9293DDD390025CFCA /* Lato-BlackItalic.ttf */,
				D2BA10D3293DDD390025CFCA /* Lato-Bold.ttf */,
				D2BA10D1293DDD390025CFCA /* Lato-BoldItalic.ttf */,
				D2BA10D8293DDD390025CFCA /* Lato-Light.ttf */,
				D2BA10D4293DDD390025CFCA /* Lato-LightItalic.ttf */,
				D2BA10D5293DDD390025CFCA /* Lato-Regular.ttf */,
				D2BA10DA293DDD390025CFCA /* Lato-Thin.ttf */,
				D2BA10D2293DDD390025CFCA /* Lato-ThinItalic.ttf */,
				13B07FAE1A68108700A75B9A /* Dentalkart */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* DentalkartTests */,
				D7838E582D167C1100958C96 /* NSEDev */,
				D7C2C4512D1696A200991430 /* NCEDev */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				16D283788B184CBE89AAA619 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Dentalkart.app */,
				00E356EE1AD99517003FC87E /* DentalkartTests.xctest */,
				D7AB5F482C92C0EC00819EBB /* Staging.app */,
				D7AB5FB22C92C16200819EBB /* Dev.app */,
				D7838E572D167C1100958C96 /* NSEDev.appex */,
				D7C2C44E2D1696A200991430 /* NCEDev.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				B86819497E5DFCC41950B7B8 /* Pods-Dentalkart.debug.xcconfig */,
				0D9A1CFF420FCB8CC28466A5 /* Pods-Dentalkart.release.xcconfig */,
				9D9ED936741E019EEC6ACABF /* Pods-DentalkartTests.debug.xcconfig */,
				24910D2E98D448E18173559B /* Pods-DentalkartTests.release.xcconfig */,
				BA62FCF07BFF11AB19B47E06 /* Pods-Dev.debug.xcconfig */,
				4194D42F6585AC34286506B7 /* Pods-Dev.release.xcconfig */,
				2B17CC0C2CA1C5C7EEF4B7A6 /* Pods-NCEDev.debug.xcconfig */,
				98EA3CDD7F880897CBB2C0FB /* Pods-NCEDev.release.xcconfig */,
				4B8680EA853F94DF09833D49 /* Pods-NSEDev.debug.xcconfig */,
				BB3ED2A85D3F8CC98B112D03 /* Pods-NSEDev.release.xcconfig */,
				D0EAD00F2A16A012978415C9 /* Pods-Staging.debug.xcconfig */,
				BAC3B521BD53748296043FC4 /* Pods-Staging.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* DentalkartTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "DentalkartTests" */;
			buildPhases = (
				6661DD06A368EC4A5EAEDBF6 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = DentalkartTests;
			productName = DentalkartTests;
			productReference = 00E356EE1AD99517003FC87E /* DentalkartTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* Dentalkart */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Dentalkart" */;
			buildPhases = (
				3C0B2EF4CDCEFBE0B2DD26DE /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				FBAF5A722BC17F990016DD1F /* Embed Foundation Extensions */,
				A60DDEEFAFE922E714B80789 /* [CP] Embed Pods Frameworks */,
				8F8DEAE9A8C440F2D881BC2E /* [CP] Copy Pods Resources */,
				4A0775946253FD93671E8EC1 /* [CP-User] [RNFB] Core Configuration */,
				2FB5A34A2BFB337AE6492695 /* [CP-User] [RNFB] Crashlytics Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Dentalkart;
			productName = Dentalkart;
			productReference = 13B07F961A680F5B00A75B9A /* Dentalkart.app */;
			productType = "com.apple.product-type.application";
		};
		D7838E562D167C1100958C96 /* NSEDev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D7838E602D167C1100958C96 /* Build configuration list for PBXNativeTarget "NSEDev" */;
			buildPhases = (
				C305CBF98F6E6625A158FE0C /* [CP] Check Pods Manifest.lock */,
				D7838E532D167C1100958C96 /* Sources */,
				D7838E542D167C1100958C96 /* Frameworks */,
				D7838E552D167C1100958C96 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				D7838E582D167C1100958C96 /* NSEDev */,
			);
			name = NSEDev;
			productName = NSEDev;
			productReference = D7838E572D167C1100958C96 /* NSEDev.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		D7AB5F152C92C0EC00819EBB /* Staging */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D7AB5F452C92C0EC00819EBB /* Build configuration list for PBXNativeTarget "Staging" */;
			buildPhases = (
				8ABF867E4CF451F996ED30FC /* [CP] Check Pods Manifest.lock */,
				D7AB5F1B2C92C0EC00819EBB /* Start Packager */,
				D7AB5F1C2C92C0EC00819EBB /* Sources */,
				D7AB5F1F2C92C0EC00819EBB /* Frameworks */,
				D7AB5F222C92C0EC00819EBB /* Resources */,
				D7AB5F3E2C92C0EC00819EBB /* Bundle React Native code and images */,
				D7AB5F3F2C92C0EC00819EBB /* Embed Foundation Extensions */,
				04CD033F6EA392C6718E0B90 /* [CP] Embed Pods Frameworks */,
				E283E77095711895718762EF /* [CP] Copy Pods Resources */,
				66B8ED2A2D916042E93F9A35 /* [CP-User] [RNFB] Core Configuration */,
				93FACDAC1B718496E2565835 /* [CP-User] [RNFB] Crashlytics Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Staging;
			productName = Dentalkart;
			productReference = D7AB5F482C92C0EC00819EBB /* Staging.app */;
			productType = "com.apple.product-type.application";
		};
		D7AB5F7F2C92C16200819EBB /* Dev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D7AB5FAF2C92C16200819EBB /* Build configuration list for PBXNativeTarget "Dev" */;
			buildPhases = (
				8D5312116B4172DD6216125F /* [CP] Check Pods Manifest.lock */,
				D7AB5F852C92C16200819EBB /* Start Packager */,
				D7AB5F862C92C16200819EBB /* Sources */,
				D7AB5F892C92C16200819EBB /* Frameworks */,
				D7AB5F8C2C92C16200819EBB /* Resources */,
				D7AB5FA82C92C16200819EBB /* Bundle React Native code and images */,
				D7AB5FA92C92C16200819EBB /* Embed Foundation Extensions */,
				A851CF1AFF74104026940883 /* [CP] Embed Pods Frameworks */,
				AEF64B48FC591E8682C76280 /* [CP] Copy Pods Resources */,
				652BF3662C22D26D284F0358 /* [CP-User] [RNFB] Core Configuration */,
				FA6BE3FA4D714AC2F3042035 /* [CP-User] [RNFB] Crashlytics Configuration */,
			);
			buildRules = (
			);
			dependencies = (
				D7838E5D2D167C1100958C96 /* PBXTargetDependency */,
				D7C2C4592D1696A200991430 /* PBXTargetDependency */,
			);
			name = Dev;
			productName = Dentalkart;
			productReference = D7AB5FB22C92C16200819EBB /* Dev.app */;
			productType = "com.apple.product-type.application";
		};
		D7C2C44D2D1696A200991430 /* NCEDev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D7C2C45C2D1696A200991430 /* Build configuration list for PBXNativeTarget "NCEDev" */;
			buildPhases = (
				2D01FBE12571C611634CEEB0 /* [CP] Check Pods Manifest.lock */,
				D7C2C44A2D1696A200991430 /* Sources */,
				D7C2C44B2D1696A200991430 /* Frameworks */,
				D7C2C44C2D1696A200991430 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				D7C2C4512D1696A200991430 /* NCEDev */,
			);
			name = NCEDev;
			productName = NCEDev;
			productReference = D7C2C44E2D1696A200991430 /* NCEDev.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
					D7838E562D167C1100958C96 = {
						CreatedOnToolsVersion = 16.0;
					};
					D7C2C44D2D1696A200991430 = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Dentalkart" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Dentalkart */,
				D7AB5F152C92C0EC00819EBB /* Staging */,
				D7AB5F7F2C92C16200819EBB /* Dev */,
				00E356ED1AD99517003FC87E /* DentalkartTests */,
				D7838E562D167C1100958C96 /* NSEDev */,
				D7C2C44D2D1696A200991430 /* NCEDev */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				066CB2F42D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				066CB2F72D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				FB1FFB6D2B270155000494E1 /* GoogleService-Info.plist in Resources */,
				535E97F759AD4446B44FA6C6 /* Poppins-Black.ttf in Resources */,
				26485917246547D48F5BCD68 /* Poppins-BlackItalic.ttf in Resources */,
				3F9B26AE241441319BD92608 /* Poppins-Bold.ttf in Resources */,
				445F135C4A9E4833B0AA22F4 /* Poppins-BoldItalic.ttf in Resources */,
				303A558B368349CE9D4F1F81 /* Poppins-ExtraBold.ttf in Resources */,
				13AA0C648FA44ABC861F0FD0 /* Poppins-ExtraBoldItalic.ttf in Resources */,
				FD848726EFFC40E787D0D9EB /* Poppins-ExtraLight.ttf in Resources */,
				46D44EB76CB34495AE77D022 /* Poppins-ExtraLightItalic.ttf in Resources */,
				44B100B5DFEC4F85A2CCFE7B /* Poppins-Italic.ttf in Resources */,
				32D3E96118774C1EB14E8024 /* Poppins-Light.ttf in Resources */,
				6C82B5DBA935472480E58E14 /* Poppins-LightItalic.ttf in Resources */,
				5073A1CC6CFD460BAD325ABC /* Poppins-Medium.ttf in Resources */,
				FDC1530060C34510BDE14847 /* Poppins-MediumItalic.ttf in Resources */,
				DD1EC94F676B4FE8A1410B14 /* Poppins-Regular.ttf in Resources */,
				7EBBEF6D713546839244EC1F /* Poppins-SemiBold.ttf in Resources */,
				410D3FACC77345208F5B4115 /* Poppins-SemiBoldItalic.ttf in Resources */,
				82A211048E9E480BB4F752C6 /* Poppins-Thin.ttf in Resources */,
				A9A0BDFBA93A47C88B1F9043 /* Poppins-ThinItalic.ttf in Resources */,
				38403EAD5CA24F1191F5EEB7 /* OFL.txt in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7838E552D167C1100958C96 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				066CB2F92D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7AB5F222C92C0EC00819EBB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D746DF262C9323D20042689E /* staging_info.plist in Resources */,
				D7AB5F242C92C0EC00819EBB /* Images.xcassets in Resources */,
				D7AB5F262C92C0EC00819EBB /* Poppins-Black.ttf in Resources */,
				D7AB5F272C92C0EC00819EBB /* Poppins-BlackItalic.ttf in Resources */,
				D7AB5F282C92C0EC00819EBB /* Poppins-Bold.ttf in Resources */,
				D7AB5F292C92C0EC00819EBB /* Poppins-BoldItalic.ttf in Resources */,
				D7AB5F2A2C92C0EC00819EBB /* Poppins-ExtraBold.ttf in Resources */,
				D7AB5F2B2C92C0EC00819EBB /* Poppins-ExtraBoldItalic.ttf in Resources */,
				D7AB5F2C2C92C0EC00819EBB /* Poppins-ExtraLight.ttf in Resources */,
				D7AB5F2D2C92C0EC00819EBB /* Poppins-ExtraLightItalic.ttf in Resources */,
				D7AB5F2E2C92C0EC00819EBB /* Poppins-Italic.ttf in Resources */,
				D7AB5F2F2C92C0EC00819EBB /* Poppins-Light.ttf in Resources */,
				D746DF0A2C9311790042689E /* GoogleService-Info.plist in Resources */,
				D7AB5F302C92C0EC00819EBB /* Poppins-LightItalic.ttf in Resources */,
				D7AB5F312C92C0EC00819EBB /* Poppins-Medium.ttf in Resources */,
				D7AB5F322C92C0EC00819EBB /* Poppins-MediumItalic.ttf in Resources */,
				D7AB5F332C92C0EC00819EBB /* Poppins-Regular.ttf in Resources */,
				D7AB5F342C92C0EC00819EBB /* Poppins-SemiBold.ttf in Resources */,
				D7AB5F352C92C0EC00819EBB /* Poppins-SemiBoldItalic.ttf in Resources */,
				D7AB5F362C92C0EC00819EBB /* Poppins-Thin.ttf in Resources */,
				D7AB5F372C92C0EC00819EBB /* Poppins-ThinItalic.ttf in Resources */,
				D7AB5F382C92C0EC00819EBB /* OFL.txt in Resources */,
				D7AB5F392C92C0EC00819EBB /* BuildFile in Resources */,
				D7AB5F3A2C92C0EC00819EBB /* BuildFile in Resources */,
				D7AB5F3B2C92C0EC00819EBB /* BuildFile in Resources */,
				066CB2F82D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */,
				D7AB5F3C2C92C0EC00819EBB /* BuildFile in Resources */,
				D7AB5F3D2C92C0EC00819EBB /* BuildFile in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7AB5F8C2C92C16200819EBB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D7AB5F8E2C92C16200819EBB /* Images.xcassets in Resources */,
				D7AB5F902C92C16200819EBB /* Poppins-Black.ttf in Resources */,
				D746DF0F2C9311860042689E /* GoogleService-Info.plist in Resources */,
				D7AB5F912C92C16200819EBB /* Poppins-BlackItalic.ttf in Resources */,
				D7AB5F922C92C16200819EBB /* Poppins-Bold.ttf in Resources */,
				D7AB5F932C92C16200819EBB /* Poppins-BoldItalic.ttf in Resources */,
				D7AB5F942C92C16200819EBB /* Poppins-ExtraBold.ttf in Resources */,
				D7AB5F952C92C16200819EBB /* Poppins-ExtraBoldItalic.ttf in Resources */,
				D7AB5F962C92C16200819EBB /* Poppins-ExtraLight.ttf in Resources */,
				D7AB5F972C92C16200819EBB /* Poppins-ExtraLightItalic.ttf in Resources */,
				D7AB5F982C92C16200819EBB /* Poppins-Italic.ttf in Resources */,
				D7AB5F992C92C16200819EBB /* Poppins-Light.ttf in Resources */,
				D7AB5F9A2C92C16200819EBB /* Poppins-LightItalic.ttf in Resources */,
				D7AB5F9B2C92C16200819EBB /* Poppins-Medium.ttf in Resources */,
				D7AB5F9C2C92C16200819EBB /* Poppins-MediumItalic.ttf in Resources */,
				D7AB5F9D2C92C16200819EBB /* Poppins-Regular.ttf in Resources */,
				D7AB5F9E2C92C16200819EBB /* Poppins-SemiBold.ttf in Resources */,
				D7AB5F9F2C92C16200819EBB /* Poppins-SemiBoldItalic.ttf in Resources */,
				D7AB5FA02C92C16200819EBB /* Poppins-Thin.ttf in Resources */,
				D7AB5FA12C92C16200819EBB /* Poppins-ThinItalic.ttf in Resources */,
				D7AB5FA22C92C16200819EBB /* OFL.txt in Resources */,
				D7AB5FA32C92C16200819EBB /* BuildFile in Resources */,
				D7AB5FA42C92C16200819EBB /* BuildFile in Resources */,
				D7AB5FA52C92C16200819EBB /* BuildFile in Resources */,
				D7AB5FA62C92C16200819EBB /* BuildFile in Resources */,
				066CB2F62D5DD9F8006E07D6 /* LaunchScreen.storyboard in Resources */,
				D746DF232C9322570042689E /* dev_info.plist in Resources */,
				D7AB5FA72C92C16200819EBB /* BuildFile in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7C2C44C2D1696A200991430 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		04CD033F6EA392C6718E0B90 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Staging/Pods-Staging-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Staging/Pods-Staging-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Staging/Pods-Staging-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2D01FBE12571C611634CEEB0 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NCEDev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		2FB5A34A2BFB337AE6492695 /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		3C0B2EF4CDCEFBE0B2DD26DE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Dentalkart-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		4A0775946253FD93671E8EC1 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		652BF3662C22D26D284F0358 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		6661DD06A368EC4A5EAEDBF6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DentalkartTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		66B8ED2A2D916042E93F9A35 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		8ABF867E4CF451F996ED30FC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Staging-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8D5312116B4172DD6216125F /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Dev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8F8DEAE9A8C440F2D881BC2E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dentalkart/Pods-Dentalkart-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dentalkart/Pods-Dentalkart-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Dentalkart/Pods-Dentalkart-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		93FACDAC1B718496E2565835 /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		A60DDEEFAFE922E714B80789 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dentalkart/Pods-Dentalkart-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dentalkart/Pods-Dentalkart-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Dentalkart/Pods-Dentalkart-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A851CF1AFF74104026940883 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dev/Pods-Dev-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dev/Pods-Dev-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Dev/Pods-Dev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AEF64B48FC591E8682C76280 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dev/Pods-Dev-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dev/Pods-Dev-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Dev/Pods-Dev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C305CBF98F6E6625A158FE0C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NSEDev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D7AB5F1B2C92C0EC00819EBB /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		D7AB5F3E2C92C0EC00819EBB /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		D7AB5F852C92C16200819EBB /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		D7AB5FA82C92C16200819EBB /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		E283E77095711895718762EF /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Staging/Pods-Staging-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Staging/Pods-Staging-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Staging/Pods-Staging-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FA6BE3FA4D714AC2F3042035 /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* DentalkartTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7838E532D167C1100958C96 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7AB5F1C2C92C0EC00819EBB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D7AB5F1D2C92C0EC00819EBB /* AppDelegate.mm in Sources */,
				D7AB5F1E2C92C0EC00819EBB /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7AB5F862C92C16200819EBB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D7AB5F872C92C16200819EBB /* AppDelegate.mm in Sources */,
				D7AB5F882C92C16200819EBB /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7C2C44A2D1696A200991430 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* Dentalkart */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		D7838E5D2D167C1100958C96 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D7838E562D167C1100958C96 /* NSEDev */;
			targetProxy = D7838E5C2D167C1100958C96 /* PBXContainerItemProxy */;
		};
		D7C2C4592D1696A200991430 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D7C2C44D2D1696A200991430 /* NCEDev */;
			targetProxy = D7C2C4582D1696A200991430 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9D9ED936741E019EEC6ACABF /* Pods-DentalkartTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = DentalkartTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dentalkart.app/Dentalkart";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 24910D2E98D448E18173559B /* Pods-DentalkartTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = DentalkartTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dentalkart.app/Dentalkart";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B86819497E5DFCC41950B7B8 /* Pods-Dentalkart.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Dentalkart/Dentalkart.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X4M7C74S2Y;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = Dentalkart/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Dentalkart;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 12.4.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental;
				PRODUCT_NAME = Dentalkart;
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.dentalkart.vasadental";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0D9A1CFF420FCB8CC28466A5 /* Pods-Dentalkart.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Dentalkart/Dentalkart.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X4M7C74S2Y;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = Dentalkart/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Dentalkart;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 12.4.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental;
				PRODUCT_NAME = Dentalkart;
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.dentalkart.vasadental";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
					"-ld_classic",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
					"-ld_classic",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D7838E612D167C1100958C96 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4B8680EA853F94DF09833D49 /* Pods-NSEDev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NSEDev/NSEDev.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X4M7C74S2Y;
				ENABLE_ON_DEMAND_RESOURCES = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NSEDev/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NSEDev;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.dev.NSEDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental.staging.NotificationServiceStaging";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.dentalkart.vasadental.staging.NotificationServiceStaging";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D7838E622D167C1100958C96 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BB3ED2A85D3F8CC98B112D03 /* Pods-NSEDev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NSEDev/NSEDevRelease.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 11;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X4M7C74S2Y;
				ENABLE_ON_DEMAND_RESOURCES = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NSEDev/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NSEDev;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.dev.NSEDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental.staging.NotificationServiceStaging";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.dentalkart.vasadental.staging.NotificationServiceStaging";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		D7AB5F462C92C0EC00819EBB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D0EAD00F2A16A012978415C9 /* Pods-Staging.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Dentalkart/Dentalkart.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = staging_Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Staging Dentalkart";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 12.4.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.staging;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental.staging";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		D7AB5F472C92C0EC00819EBB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BAC3B521BD53748296043FC4 /* Pods-Staging.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Dentalkart/Dentalkart.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = staging_Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Staging Dentalkart";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 12.4.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.staging;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental.staging";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		D7AB5FB02C92C16200819EBB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BA62FCF07BFF11AB19B47E06 /* Pods-Dev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Dentalkart/Dentalkart.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = dev_Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Dev Dentalkart";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 12.4.9;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		D7AB5FB12C92C16200819EBB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4194D42F6585AC34286506B7 /* Pods-Dev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Dentalkart/Dentalkart.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = dev_Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Dev Dentalkart";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 12.4.9;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		D7C2C45D2D1696A200991430 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2B17CC0C2CA1C5C7EEF4B7A6 /* Pods-NCEDev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NCEDev/NCEDev.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 11;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X4M7C74S2Y;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NCEDev/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NCEDev;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.dev.NCEDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental.staging.NotificationViewControllerStaging";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.dentalkart.vasadental.staging.NotificationViewControllerStaging";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D7C2C45E2D1696A200991430 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 98EA3CDD7F880897CBB2C0FB /* Pods-NCEDev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NCEDev/NCEDev.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 11;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = X4M7C74S2Y;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X4M7C74S2Y;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NCEDev/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NCEDev;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.dentalkart.vasadental.dev.NCEDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.dentalkart.vasadental.staging.NotificationViewControllerStaging";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.dentalkart.vasadental.staging.NotificationViewControllerStaging";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "DentalkartTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Dentalkart" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Dentalkart" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D7838E602D167C1100958C96 /* Build configuration list for PBXNativeTarget "NSEDev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D7838E612D167C1100958C96 /* Debug */,
				D7838E622D167C1100958C96 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D7AB5F452C92C0EC00819EBB /* Build configuration list for PBXNativeTarget "Staging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D7AB5F462C92C0EC00819EBB /* Debug */,
				D7AB5F472C92C0EC00819EBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D7AB5FAF2C92C16200819EBB /* Build configuration list for PBXNativeTarget "Dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D7AB5FB02C92C16200819EBB /* Debug */,
				D7AB5FB12C92C16200819EBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D7C2C45C2D1696A200991430 /* Build configuration list for PBXNativeTarget "NCEDev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D7C2C45D2D1696A200991430 /* Debug */,
				D7C2C45E2D1696A200991430 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}

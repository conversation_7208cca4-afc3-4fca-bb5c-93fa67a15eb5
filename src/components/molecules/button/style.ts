import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      paddingVertical: Sizes.xm,
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'stretch',
      backgroundColor: colors.newPrimary,
      flexDirection: 'row',
      paddingHorizontal: Sizes.m,
    },
    buttonLabel: {
      fontSize: Sizes.l,
    },
    ghost: {backgroundColor: colors.transparentColor},
    secondaryButton: {
      backgroundColor: colors.background,
      borderWidth: Sizes.x,
      borderColor: colors.primary,
    },
    secondaryBulkButton: {
      backgroundColor: colors.background,
      borderWidth: Sizes.x,
      borderColor: colors.primary,
    },
    bordered: {
      backgroundColor: 'transparent',
      borderWidth: Sizes.x,
      borderColor: colors.primary,
    },
    disabled: {
      backgroundColor: colors.lightGray,
      opacity: 0.6,
    },
    disabledLabel: {
      color: colors.disabledText,
    },
    secondaryLabel: {
      color: colors.textLight,
    },
    bulkButton: {
      color: colors.primary,
    },
    iconRight: {
      justifyContent: 'center',
      paddingHorizontal: Sizes.s,
      left: Sizes.xm,
    },
    iconLeft: {
      justifyContent: 'center',
      paddingHorizontal: Sizes.s,
      marginRight: Sizes.s,
    },
    iconCenter: {
      justifyContent: 'center',
      paddingHorizontal: Sizes.s,
    },
    text: {fontSize: Sizes.m},
    gradient: {backgroundColor: colors.transparentColor},
    gradientSpace: {
      bottom: Sizes.sx,
    },
    bthStyle: {
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

export default styles;

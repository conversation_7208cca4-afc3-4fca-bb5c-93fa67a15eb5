import React, {useCallback, useEffect, useState} from 'react';
import {TouchableOpacity, View, FlatList, BackHandler} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../routes';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {ImageIcon, Label, Spacer, Separator} from 'components/atoms';
import {useTheme, RouteProp} from '@react-navigation/native';
import getSavingAmount from 'utils/getSavingAmount';
import {t} from 'i18next';
import {useDispatch, useSelector} from 'react-redux';
import {addToCart} from 'app-redux-store/slice/appSlice';
import {AnalyticsEvents} from 'components/organisms';
import {
  memberShipsPlanApi,
  memberShipsFaqApi,
  getMemberShipData,
  myMemberShipsData,
} from 'services/mambership';
import {showErrorMessage} from 'utils/show_messages';
import LinearGradient from 'react-native-linear-gradient';
import {MembershipLoader} from 'skeletonLoader';
import {checkDevice, debounce} from 'utils/utils';
import Icons from 'common/icons';
import FastImage from 'react-native-fast-image';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'MembershipPage'>;
};

const MembershipPageScene = ({navigation, route}: Props) => {
  const TAG = 'MembershipPageScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [faqsData, setFaqsData] = useState([]);
  const [membership, setMembership] = useState({});
  const [selectValue, setSelectValue] = useState(5000);
  const [plans, setPlans] = useState([]);
  const [showMore, setShowMore] = useState(false);
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const [total, setTotal] = useState<
    {
      label: string;
      gain_per_price: number;
      value: number;
    }[]
  >([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [loading, setLoadingState] = useState(true);
  const [activeScreen, setActiveScreen] = useState(false);
  const dispatch = useDispatch();
  const FREE_DELIVERY_PRICE = 95;
  const NO_OF_MONTHS = +(selectedPlan?.days_duration / 30).toFixed(0);
  const total_gain_price = total.reduce(function (accumulator, currentValue) {
    return accumulator + currentValue?.value;
  }, 0);

  useEffect(() => {
    return setTotal([
      {
        label: t('memberShip.rewardBenefit'),
        gain_per_price: getSavingAmount(selectValue),
        value: getSavingAmount(selectValue) * NO_OF_MONTHS,
      },
      {
        label: t('memberShip.freeDelivery'),
        gain_per_price: FREE_DELIVERY_PRICE,
        value: FREE_DELIVERY_PRICE * NO_OF_MONTHS,
      },
    ]);
  }, [selectedPlan, NO_OF_MONTHS, selectValue]);

  useEffect(() => {
    setLoadingState(true);
    Promise.allSettled([faqApi(), planeApi(), memberShipLocalData()]).then(
      res => {
        setLoadingState(false);
      },
    );
    myMembershipApis();
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const faqApi = useCallback(async () => {
    const {data} = await memberShipsFaqApi({parents: ['Membership']});
    if (data?.result) {
      const membershipData = data?.result
        ?.filter(item => item.parents.includes('Membership'))
        .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
      setFaqsData(membershipData);
    }
  }, [faqsData]);

  const memberShipLocalData = useCallback(async () => {
    const {data, status} = await getMemberShipData();
    if (data && status) {
      const membershipData = data?.response;
      setMembership(membershipData);
    }
  }, []);

  const planeApi = useCallback(async () => {
    const {data, status} = await memberShipsPlanApi();
    if (data && status) {
      setPlans(data?.collection[0]?.products);
      let INDExx = data?.collection[0]?.products?.findIndex(
        item => item.is_default,
      );
      setSelectedPlan(
        data?.collection[0]?.products?.[INDExx !== -1 ? INDExx : 0],
      );
    } else {
      showErrorMessage(data?.message);
    }
  }, []);

  const myMembershipApis = async () => {
    setLoadingState(true);
    const {data, status} = await myMemberShipsData();
    setLoadingState(false);
    if (data) {
      setActiveScreen(
        data?.memberships.length > 0 &&
          data?.memberships?.filter(item => item.is_active).length > 0
          ? true
          : false,
      );
    }
  };

  const [qty, setQty] = useState(1);

  const debouncedCall = callBack => {
    debouncedUpdate(callBack);
  };

  const debouncedUpdate = useCallback(
    debounce(callBack => {
      callBack();
    }, 500),
    [],
  );

  const addToCartButton = async (product: ProductData, qty: number) => {
    const membershipData = {
      plan: product?.name,
    };
    AnalyticsEvents(
      'MEMBERSHIP_ADD_TO_CART',
      ' Membership Add To Cart',
      membershipData,
      userInfo,
      isLoggedIn,
    );
    await dispatch(
      addToCart({
        cart_items: [{data: {quantity: qty, sku: product?.sku}}],
        image: 'membershipImage',
      }),
    );
  };

  const renderPlanList = () => {
    return (
      <View style={styles.planCardContainer}>
        {plans?.map((plan, index) => {
          const month = Math.round(Number(plan?.days_duration) / (365.25 / 12));
          return (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setSelectedPlan(plan);
              }}
              style={[
                selectedPlan?.sku === plan?.sku && styles.monthBox,
                styles.notSelected,
                {marginRight: index % 2 == 0 ? 12 : 0},
                checkDevice() && styles.flex,
              ]}>
              {selectedPlan?.sku === plan?.sku && (
                <View style={styles.checkedICon}>
                  <ImageIcon size="xl" icon="checkRightIcon" />
                </View>
              )}
              <Label
                color={
                  selectedPlan?.sku === plan?.sku ? 'whiteColor' : 'newPrimary'
                }
                size="mx"
                fontFamily="SemiBold"
                text={plan?.name}
                align="center"
              />
              <Spacer size="m" />
              <Label
                text={
                  '₹' +
                  (Number(plan?.price) / month).toFixed(1) +
                  '/' +
                  t('memberShip.month')
                }
                fontFamily="SemiBold"
                color={
                  selectedPlan?.sku === plan?.sku ? 'electricBlue' : 'buttercup'
                }
                size="l"
              />

              <Spacer size="sx" />

              <View style={styles.fRow}>
                <Label
                  color={
                    selectedPlan?.sku === plan?.sku
                      ? 'whiteColor'
                      : 'newPrimary'
                  }
                  size="xms"
                  text={'₹' + plan?.price}
                  fontFamily="SemiBold"
                />
                <Spacer size="s" />
                <Label
                  size="xms"
                  fontFamily="SemiBold"
                  color={
                    selectedPlan?.sku === plan?.sku
                      ? 'whiteColor'
                      : 'newPrimary'
                  }
                  text={
                    ` (${month}` +
                    ' ' +
                    t('memberShip.months') +
                    ' ' +
                    `${t('memberShip.plane')})`
                  }
                />
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const renderCoin = () => {
    return (
      <View style={[styles.plusMember, checkDevice() && styles.coinTabView]}>
        <View style={checkDevice() ? styles.coinView1 : styles.coinView}>
          <View style={styles.underBox}>
            <ImageIcon icon="coin" size="l" />
            <Spacer type="Horizontal" size="s" />
            <Label
              size="mx"
              text={t('memberShip.coin1Rupee')}
              color="whiteColor"
              fontFamily="SemiBold"
            />
          </View>
          <Spacer
            type={checkDevice() ? 'Vertical' : 'Horizontal'}
            size={checkDevice() ? 'xx' : 'm'}
          />
          <View style={styles.flex}>
            <Label
              color="whiteColor"
              size={checkDevice() ? 'mx' : 'xms'}
              text={t('memberShip.plusMembership')}
              fontFamily="SemiBold"
              align={checkDevice() ? 'center' : 'left'}
            />
            <Label
              color="whiteColor"
              size={checkDevice() ? 'm' : 'xms'}
              fontFamily="Medium"
              text={t('memberShip.plusMembershipDes')}
              align={checkDevice() ? 'center' : 'left'}
            />
          </View>
        </View>

        <Spacer size={checkDevice() ? 'xx' : 'm'} />
        <View style={checkDevice() ? styles.coinView1 : styles.coinView}>
          <View style={styles.underBox}>
            <ImageIcon icon="coin" size="l" />
            <Spacer type="Horizontal" size="s" />
            <Label
              size="mx"
              text={t('memberShip.coinHalfRupee')}
              color="whiteColor"
              fontFamily="SemiBold"
            />
          </View>
          <Spacer
            type={checkDevice() ? 'Vertical' : 'Horizontal'}
            size={checkDevice() ? 'xx' : 'm'}
          />
          <View style={styles.flex}>
            <Label
              color="whiteColor"
              size={checkDevice() ? 'mx' : 'xms'}
              text={t('memberShip.plusNonMembership')}
              fontFamily="SemiBold"
              align={checkDevice() ? 'center' : 'left'}
            />
            <Label
              color="whiteColor"
              size={checkDevice() ? 'm' : 'xms'}
              fontFamily="Medium"
              text={t('memberShip.plusNonMembershipDes')}
              align={checkDevice() ? 'center' : 'left'}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderHowIt = () => {
    return (
      <FlatList
        data={
          membership?.section_3?.media?.length > 0
            ? membership?.section_3?.media
            : []
        }
        renderItem={({item, index}) => {
          return (
            <View key={index} style={styles.howItWorksView}>
              <LinearGradient
                colors={[colors.lightSkyBlue, colors.cerulean]}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 1}}
                // locations={[-0.0155, 1.0323]}
                style={styles.linearHowItWorks}>
                <Label
                  size="mx"
                  text={item.image_desc}
                  color="whiteColor"
                  fontFamily="Medium"
                  style={styles.workTitle}
                />
                <ImageIcon
                  resizeMode="stretch"
                  sourceType="url"
                  source={item.image_url}
                  size="ex88"
                />
              </LinearGradient>
            </View>
          );
        }}
        ItemSeparatorComponent={<Spacer size="xm" />}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  };
  const month = Math.round(Number(selectedPlan?.days_duration) / (365.25 / 12));
  return (
    <SafeAreaView style={styles.container}>
      {loading ? (
        <MembershipLoader />
      ) : (
        <>
          <ErrorHandler
            componentName={`${TAG} Header`}
            onErrorComponent={<View />}>
            <Header
              text={t('memberShip.membership')}
              backButton={true}
              navigation={navigation}
              onPressNavigation={() => {
                navigation.goBack();
                if (route.params?.goBack) {
                  route.params?.goBack();
                }
              }}
              searchIcon={true}
              bagIcon={true}
              heartIcon={true}
            />
          </ErrorHandler>
          <View style={styles.flex}>
            <FlatList
              data={['']}
              showsVerticalScrollIndicator={false}
              renderItem={() => (
                <>
                  <ErrorHandler
                    componentName={`${TAG} MembershipList`}
                    onErrorComponent={<View />}>
                    <FastImage
                      resizeMode="stretch"
                      style={
                        checkDevice() ? styles.bannerImg1 : styles.bannerImg
                      }
                      source={Icons.memberShipGif}
                    />
                    <View style={styles.membershipCalculation}>
                      {!checkDevice() && renderPlanList()}
                      <Spacer size="l" />
                      <View style={styles.journey}>
                        <Label
                          color="text2"
                          size={checkDevice() ? 'xl' : 'm'}
                          fontFamily="Medium"
                          text={t('memberShip.notes') + ' '}
                          align="center">
                          <Label
                            color="newSunnyOrange"
                            size={checkDevice() ? 'xl' : 'm'}
                            fontFamily="Medium"
                            text={t('memberShip.membership')}
                          />
                        </Label>
                      </View>
                      <Spacer size="l" />

                      <View style={styles.saprater}>
                        <Separator style={styles.flex} color="grey2" />
                        <Label
                          style={styles.benefits}
                          size={checkDevice() ? 'xl' : 'm'}
                          text={t('memberShip.benefits')}
                          fontFamily="Medium"
                          color="text2"
                        />
                        <Separator style={styles.flex} color="grey2" />
                      </View>
                      <Spacer size="l" />
                      <FlatList
                        data={
                          membership?.section_2?.media?.length > 0
                            ? membership?.section_2?.media
                            : []
                        }
                        numColumns={2}
                        showsVerticalScrollIndicator={false}
                        ItemSeparatorComponent={<Spacer size="m" />}
                        renderItem={({item, index}) => {
                          return (
                            <View
                              key={index}
                              style={[
                                styles.freeShippingBoxView,
                                {marginRight: index % 2 == 0 ? 12 : 0},
                              ]}>
                              <LinearGradient
                                colors={[colors.spray, colors.bostonBlue]}
                                start={{x: 0, y: 0}}
                                end={{x: 1, y: 1}}
                                // locations={[0.3313, 1.1555]}
                                style={styles.linearContainer}>
                                <View
                                  style={
                                    checkDevice()
                                      ? styles.benefitsStyle
                                      : styles.flex
                                  }>
                                  <View style={styles.flex}>
                                    <Label
                                      size={checkDevice() ? 'xl' : 'm'}
                                      color="whiteColor"
                                      text={item?.alt}
                                      fontFamily="Bold"
                                      textTransform="uppercase"
                                      numberOfLines={2}
                                    />
                                    <Spacer size="sx" />
                                    <Label
                                      size={checkDevice() ? 'mx' : 'xms'}
                                      color="whiteColor"
                                      text={item?.image_desc}
                                      fontFamily="Regular"
                                      numberOfLines={3}
                                      textTransform="capitalize"
                                    />
                                  </View>
                                  <Spacer size="sx" />
                                  <ImageIcon
                                    source={item?.image_url}
                                    size={checkDevice() ? 'exl' : 'x9l'}
                                    sourceType="url"
                                    style={styles.truckViewIcon}
                                  />
                                </View>
                              </LinearGradient>
                            </View>
                          );
                        }}
                      />
                    </View>
                    <Spacer size="xl" />
                    <View style={styles.choosePlanView}>
                      <Label
                        size={checkDevice() ? 'xx' : 'l'}
                        text={t('memberShip.choosePlan')}
                        fontFamily="Bold"
                        color="text2"
                        align="center"
                      />
                      <Spacer size="l" />
                      <View style={checkDevice() && styles.choosePlanLine}>
                        <View style={styles.flex}>
                          <View style={styles.choosePlanLine}>
                            <TouchableOpacity
                              onPress={() => setSelectValue(5000)}
                              style={[
                                selectValue >= 5000
                                  ? {}
                                  : styles.circleContainer,
                              ]}>
                              <ImageIcon
                                icon={
                                  selectValue >= 5000
                                    ? 'rightCheckDarkBlueIcon'
                                    : 'lock'
                                }
                                size={selectValue >= 5000 ? 'x6l' : 'l'}
                              />
                            </TouchableOpacity>

                            <Separator
                              thickness="sx"
                              style={styles.flex}
                              color={
                                selectValue > 5000 ? 'newPrimary' : 'whiteColor'
                              }
                            />

                            <TouchableOpacity
                              onPress={() => setSelectValue(10000)}
                              style={[
                                selectValue >= 10000
                                  ? {}
                                  : styles.circleContainer,
                              ]}>
                              <ImageIcon
                                icon={
                                  selectValue >= 10000
                                    ? 'rightCheckDarkBlueIcon'
                                    : 'lock'
                                }
                                size={selectValue >= 10000 ? 'x6l' : 'l'}
                              />
                            </TouchableOpacity>

                            <Separator
                              thickness="sx"
                              style={styles.flex}
                              color={
                                selectValue > 10000
                                  ? 'newPrimary'
                                  : 'whiteColor'
                              }
                            />

                            <TouchableOpacity
                              onPress={() => setSelectValue(15000)}
                              style={[
                                selectValue >= 15000
                                  ? {}
                                  : styles.circleContainer,
                              ]}>
                              <ImageIcon
                                icon={
                                  selectValue >= 15000
                                    ? 'rightCheckDarkBlueIcon'
                                    : 'lock'
                                }
                                size={selectValue >= 15000 ? 'x6l' : 'l'}
                              />
                            </TouchableOpacity>
                          </View>
                          <Spacer size="xs" />
                          <View style={styles.moneyView}>
                            <Label
                              style={styles.sliderAmount}
                              color="text2"
                              size="m"
                              fontFamily="Regular"
                              text={t('memberShip.monthOne')}
                            />
                            <Label
                              style={styles.sliderAmount}
                              color="text2"
                              size="m"
                              fontFamily="Regular"
                              text={t('memberShip.monthTwo')}
                            />
                            <Label
                              style={styles.sliderAmount}
                              color="text2"
                              size="m"
                              fontFamily="Regular"
                              text={t('memberShip.monthThree')}
                            />
                          </View>

                          <Spacer size="xxl" />

                          <View style={styles.totalPiceView}>
                            <Label
                              color="text2"
                              text={'₹' + total_gain_price}
                              size="l"
                              weight="bold"
                            />
                          </View>

                          <Spacer size="xl" />

                          <View>
                            <Label
                              size="mx"
                              color="text2"
                              fontFamily="Medium"
                              text={t('memberShip.breakDown')}
                            />
                            <Spacer size="s" />
                            <View style={styles.priceView}>
                              <Label
                                text={t('memberShip.rewardBenefit')}
                                size="m"
                                color="text2"
                                fontFamily="Medium"
                              />
                              <Label
                                text={
                                  '₹' +
                                  total?.[0]?.gain_per_price +
                                  ' x ' +
                                  NO_OF_MONTHS +
                                  '= ₹' +
                                  total?.[0]?.value
                                }
                                size="m"
                                color="text2"
                                fontFamily="Medium"
                              />
                            </View>

                            <Spacer size="s" />

                            <View style={styles.priceView}>
                              <Label
                                size="m"
                                color="text2"
                                fontFamily="Medium"
                                text={t('memberShip.freeDelivery')}
                              />
                              <Label
                                size="m"
                                color="text2"
                                fontFamily="Medium"
                                text={
                                  '₹' +
                                  total?.[1]?.gain_per_price +
                                  ' x ' +
                                  NO_OF_MONTHS +
                                  ' = ₹' +
                                  total?.[1]?.value
                                }
                              />
                            </View>

                            <Spacer size="s" />

                            <View style={styles.priceView}>
                              <Label
                                text={t('memberShip.total')}
                                size="mx"
                                color="text2"
                                fontFamily="Medium"
                              />
                              <Label
                                text={'₹' + total_gain_price}
                                size="mx"
                                color="text2"
                                fontFamily="Medium"
                              />
                            </View>

                            <Spacer size="sx" />

                            <Label
                              color="text2"
                              size="m"
                              fontFamily="Regular"
                              style={styles.underText}
                              text={t('memberShip.assumingMonth')}
                            />
                          </View>
                        </View>

                        <Spacer
                          size="l"
                          type={checkDevice() ? 'Horizontal' : 'Vertical'}
                        />
                        <View style={checkDevice() && styles.planCardContainer}>
                          {renderPlanList()}
                        </View>
                      </View>
                    </View>

                    <Spacer size="xl" />

                    <View style={styles.howItWorksContainer}>
                      <Label
                        text={t('memberShip.howWorks')}
                        size="l"
                        textTransform="capitalize"
                        color="text2"
                        fontFamily="SemiBold"
                        align="center"
                      />
                      <Spacer size="l" />
                      <View style={checkDevice() && styles.planCardContainer}>
                        <View style={styles.flex}>
                          {checkDevice() ? renderCoin() : renderHowIt()}
                        </View>
                        <Spacer
                          size="xl"
                          type={checkDevice() ? 'Horizontal' : 'Vertical'}
                        />
                        <View style={styles.flex}>
                          {checkDevice() ? renderHowIt() : renderCoin()}
                        </View>
                      </View>
                      <Spacer size="xl" />

                      <Label
                        color="text2"
                        size="xxl"
                        text={t('memberShip.faqTitle')}
                        fontFamily="Medium"
                      />

                      <Spacer size="m" />

                      {faqsData
                        .slice(0, showMore ? faqsData.length : 4)
                        .map((element, index) => {
                          return (
                            <View key={index} style={styles.borderBox}>
                              <View style={styles.fRow}>
                                <Label size="m" color="text2" text={'•  '} />
                                <Label
                                  size="m"
                                  color="text2"
                                  text={element?.question?.replace(
                                    /<\/?[^>]+(>|$)/g,
                                    '',
                                  )}
                                  fontFamily="Medium"
                                />
                              </View>
                              <Spacer size="xm" />
                              <Label
                                size="m"
                                color="text2"
                                text={element?.answer?.replace(
                                  /<\/?[^>]+(>|$)/g,
                                  '',
                                )}
                                fontFamily="Regular"
                              />
                            </View>
                          );
                        })}

                      <Spacer size="s" />

                      <TouchableOpacity
                        onPress={() => setShowMore(!showMore)}
                        style={styles.showMoreView}>
                        <Label
                          color="newPrimary"
                          text={
                            showMore
                              ? t('memberShip.showLess')
                              : t('memberShip.showMore')
                          }
                          size="mx"
                          fontFamily="Regular"
                        />
                        <Spacer size="xms" type="Horizontal" />
                        <ImageIcon
                          icon={showMore ? 'arrowUp' : 'arrowBottom'}
                          size="l"
                        />
                      </TouchableOpacity>

                      <Spacer size="xl" />
                      <View style={styles.fRow}>
                        <Label
                          color="text2"
                          size="xx"
                          text={t('memberShip.terms')}
                          textTransform="capitalize"
                          fontFamily="Medium"
                        />
                        <Spacer size="x26" type="Horizontal" />
                        <View style={styles.line} />
                      </View>
                      <Spacer size="xms" />
                      <FlatList
                        data={[
                          t('memberShip.plane1'),
                          t('memberShip.plane2'),
                          t('memberShip.plane3'),
                          t('memberShip.plane4'),
                          t('memberShip.plane5'),
                          t('memberShip.plane6'),
                          t('memberShip.plane7'),
                        ]}
                        renderItem={({item, index}) => {
                          return (
                            <View key={index} style={styles.fRow}>
                              {/* <View style={styles.dotView} /> */}
                              <Label
                                lineHeight="xl"
                                size="xms"
                                color="text2"
                                text={item}
                                style={styles.flex}
                              />
                            </View>
                          );
                        }}
                        ItemSeparatorComponent={<Spacer size="xm" />}
                        keyExtractor={(item, index) => index.toString()}
                      />
                      <Spacer size="xms" />
                    </View>
                  </ErrorHandler>
                </>
              )}
            />
          </View>
          <View style={styles.mainView}>
            <Button
              style={[styles.btnStyle, activeScreen && styles.disableStyle]}
              iconSize="xl"
              labelStyle={styles.btnText}
              onPress={() =>
                !activeScreen &&
                debouncedCall(() => addToCartButton(selectedPlan, qty))
              }
              radius="m"
              iconRight={'doubleArrowRight'}
              disabled={activeScreen}
              text={
                `${t('memberShip.continueWith')} ${month}` +
                ' ' +
                t('memberShip.months') +
                ' ' +
                `${t('memberShip.plane')} `
              }
              labelColor="whiteColor"
              selfAlign="stretch"
            />
            <Spacer size="s" />
          </View>
        </>
      )}
    </SafeAreaView>
  );
};

export default MembershipPageScene;

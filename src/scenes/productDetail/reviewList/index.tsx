import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import {RouteProp, useTheme} from '@react-navigation/native';
import {
  ImageIcon,
  Label,
  Radio,
  Separator,
  Spacer,
  Tag,
} from 'components/atoms';
import {dateTimeFormat} from 'utils/formatter';
import stylesWithOutColor from './style';
import {getChildProductsData, getReviewsData} from 'services/productDetail';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import Header from 'components/molecules/header';
import {t} from 'i18next';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {useDispatch} from 'react-redux';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  gethomepagebrands: BasicBannerDetails;
  route: RouteProp<RootStackParamsList, 'ReviewListScene'>;
};

const ReviewListScreen = ({navigation, route}: Props) => {
  const {productId} = route.params;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [currentText, setCurrentText] = useState('most_recent');
  const [expandedReviews, setExpandedReviews] = useState({});
  const [loader, setLoader] = useState<boolean>(true);
  const [groupProduct, setGroupProduct] = useState<Array<ChildProduct>>([]);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [pageLimit, setPageLimit] = useState<number>(20);
  const [pageNo, setPageNo] = useState<number>(1);
  const [moreLoading, setMoreLoading] = useState<boolean>(false);
  const [reviews, setReviews] = useState<Review[] | []>([]);
  const [visible, setVisible] = useState(false);
   const insets = useSafeAreaInsets();
  const TAG = 'ReviewList';
  const toggleReadMore = useCallback(index => {
    setExpandedReviews(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  }, []);

  useEffect(() => {
    getChildProducts();
  }, []);

  useEffect(() => {
    if (pageNo > 1) {
      getReviews(
        pageNo,
        groupProduct.map(e => e?.product_id) ?? [],
        currentText,
      );
    }
  }, [pageNo]);

  const getReviews = async (
    page: number,
    childIds: Array<number>,
    filter?: string,
  ) => {
    if (page === 1) {
      setLoader(true);
    } else {
      setMoreLoading(true);
    }
    const response = await getReviewsData(
      productId,
      filter,
      page,
      pageLimit,
      childIds,
    );
    setLoader(false);
    setMoreLoading(false);
    const {status, data} = response;
    if (status) {
      if (page === 1) {
        setReviews(data?.reviews);
        setTotalPages(data?.pagination?.total_pages);
      } else {
        setReviews([...reviews, ...data.reviews]);
      }
    }
  };

  const fetchMoreData = useCallback(() => {
    if (!loader && !moreLoading && pageNo < totalPages) {
      setPageNo(pageNo + 1);
    }
  }, [loader, moreLoading, pageNo, totalPages]);

  const renderFooter = () => {
    return moreLoading ? (
      <View style={styles.loaderStyle}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    ) : null;
  };

  const getChildProducts = async () => {
    setLoader(true);
    const {data} = await getChildProductsData(productId);
    if (data?.child_products) {
      setGroupProduct(data?.child_products);
    }
    getReviews(
      1,
      data?.child_products.map(e => e?.product_id) ?? [],
      currentText,
    );
  };

  const handleFilterChange = filter => {
    setCurrentText(filter);
    setReviews([]);
    setPageNo(1);
    getReviews(1, groupProduct.map(e => e?.product_id) ?? [], filter);
  };

  return (
    <SafeAreaView style={[styles.flex, { paddingTop: insets.top }]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          backButton={true}
          navigation={navigation}
          text={t('PDP.customerReview')}
        />
      </ErrorHandler>
      <View style={styles.mainSearchView}>
        <TouchableOpacity
          style={styles.searchView}
          onPress={() => {
            setVisible(true);
          }}>
          <>
            <ImageIcon icon="sortIcons" tintColor="categoryTitle" size="xx" />
            <Spacer size="xm" type="Horizontal" />
            <Label
              text={
                currentText !== 'most_recent' ? 'Most Relevant' : 'Most Recent'
              }
              size="mx"
              weight="500"
              color="categoryTitle"
            />
          </>
        </TouchableOpacity>
        <View style={styles.reviewChangeView}>
          {loader ? (
            <View style={[styles.loaderStyle, styles.flex]}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : (
            <>
              <FlatList
                data={reviews}
                keyExtractor={(_, i) => i.toString()}
                nestedScrollEnabled={true}
                renderItem={({item, index}) => {
                  const isExpanded = expandedReviews[index];
                  return (
                    <View style={styles.ratingView}>
                      <View style={styles.relevantMainView}>
                        <ErrorHandler
                          componentName={`${TAG} Tag`}
                          onErrorComponent={<View />}>
                          <Tag
                            style={styles.starIconView}
                            label={(item?.rating || 0).toFixed(1)}
                            icon="starIcon"
                            color="green2"
                            isRightIcon
                          />
                        </ErrorHandler>
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          text={item?.title}
                          size="mx"
                          weight="600"
                          color="text2"
                        />
                      </View>
                      <Spacer size="xm" />
                      <Label
                        text={
                          item?.content.length > 70 && !isExpanded
                            ? item?.content.substring(0, 70) + '...'
                            : item?.content
                        }
                        size="mx"
                        fontFamily="Medium"
                        color="text2"
                      />
                      {item?.content.length > 70 && (
                        <TouchableOpacity onPress={() => toggleReadMore(index)}>
                          <Label
                            text={isExpanded ? 'Read Less' : 'Read More'}
                            size="mx"
                            fontFamily="Medium"
                            color="text"
                          />
                        </TouchableOpacity>
                      )}
                      <Spacer size="m" />
                      <View style={styles.mainNameView}>
                        <View style={styles.nicNameView}>
                          <Label
                            text={item?.author?.substring(0, 24)}
                            size="mx"
                            weight="500"
                            color="grey3"
                          />
                          <Spacer size="xm" type="Horizontal" />
                          <Separator
                            color="grey3"
                            Vertical
                            thickness="z"
                            height="l"
                          />
                          <Spacer size="xm" type="Horizontal" />
                          <Label
                            text={dateTimeFormat(item?.date, 'D MMM YYYY')}
                            size="mx"
                            weight="500"
                            color="grey3"
                          />
                        </View>
                      </View>
                    </View>
                  );
                }}
                showsVerticalScrollIndicator={false}
                ListFooterComponent={renderFooter()}
                onEndReachedThreshold={0.8}
                onEndReached={fetchMoreData}
              />
              <Spacer size="xxxl" />
            </>
          )}
        </View>
      </View>
      <ErrorHandler
        componentName={`${TAG} DynamicHeightModal`}
        onErrorComponent={<View />}>
        <DynamicHeightModal
          useInsets
          visible={visible}
          onClose={() => setVisible(false)}
          content={
            <SafeAreaView style={styles.flex}>
              <View style={styles.postQuestionView}>
                <TouchableOpacity
                  onPress={() => {
                    handleFilterChange('most_relevant'), setVisible(false);
                  }}>
                  <View style={styles.relevantView}>
                    <ErrorHandler
                      componentName={`${TAG} Radio`}
                      onErrorComponent={<View />}>
                      <Radio
                        selected={currentText === 'most_relevant'}
                        fillColor="text2"
                        style={{borderColor: colors.text}}
                        onPress={() => {
                          handleFilterChange('most_relevant'),
                            setVisible(false);
                        }}
                      />
                    </ErrorHandler>
                    <Spacer size="m" type="Horizontal" />
                    <View style={styles.flex}>
                      <Label
                        text={t('PDP.mostrelevant')}
                        size="mx"
                        fontFamily="Medium"
                        color={
                          currentText === 'most_relevant'
                            ? 'newSunnyOrange'
                            : 'text2'
                        }
                        weight="500"
                      />
                    </View>
                  </View>
                </TouchableOpacity>
                <Spacer size="m" />
                <TouchableOpacity
                  onPress={() => {
                    handleFilterChange('most_recent'), setVisible(false);
                  }}>
                  <View style={styles.relevantView}>
                    <Radio
                      selected={currentText === 'most_recent'}
                      fillColor="text2"
                      style={{borderColor: colors.text}}
                      onPress={() => {
                        handleFilterChange('most_recent'), setVisible(false);
                      }}
                    />
                    <Spacer size="m" type="Horizontal" />
                    <View style={styles.flex}>
                      <Label
                        text={t('PDP.mostRecent')}
                        size="mx"
                        fontFamily="Medium"
                        color={
                          currentText !== 'most_relevant'
                            ? 'newSunnyOrange'
                            : 'text2'
                        }
                        weight="500"
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
            </SafeAreaView>
          }
        />
      </ErrorHandler>
    </SafeAreaView>
  );
};

export default ReviewListScreen;

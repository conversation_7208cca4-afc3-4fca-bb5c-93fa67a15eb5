import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    centeredView: {
      // flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.modelBackgroundTransparent,
    },
    modelBg: {
      width: '90%',
      backgroundColor: colors.background,
      borderRadius: Sizes.s,
      shadowColor: colors.blackIcons,

      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.25,
      shadowRadius: Sizes.s,
      elevation: Sizes.s,
    },
    modelView: {flex: Sizes.x},
  });

export default styles;

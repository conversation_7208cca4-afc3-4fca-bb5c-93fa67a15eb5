import React, {useContext, useEffect} from 'react';
import {View, SafeAreaView} from 'react-native';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from 'routes';
import {Header} from 'components/molecules';
import context from 'context/context';
import stylesWithOutColor from './style';
import YoutubePlayer from 'react-native-youtube-iframe';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RouteProp, useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'VideoPlay'>;
};

const VideoPlayScene = ({route, navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {updateStore} = useContext(context);
  const {source = ''} = route.params || {};
  useEffect(() => {
    updateStore({loading: true});
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <Header backButton navigation={navigation} />
      <View style={styles.containerView}>
        <YoutubePlayer
          height={300}
          play={true}
          videoId={source}
          onReady={() => {
            updateStore({loading: false});
          }}
          webViewStyle={{opacity: 0.99}}
        />
      </View>
    </SafeAreaView>
  );
};
export default VideoPlayScene;

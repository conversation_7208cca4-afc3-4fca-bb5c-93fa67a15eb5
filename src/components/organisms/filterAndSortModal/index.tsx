import React, {
  memo,
  useCallback,
  useEffect,
  useState,
  useMemo,
  useRef,
} from 'react';
import {
  View,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  ScrollView,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {
  CheckBox,
  FooterButton,
  ImageIcon,
  Label,
  Radio,
  Separator,
  Spacer,
} from 'components/atoms';
import Thumb from 'components/atoms/filterOther/thumb';
import {t} from 'i18next';
import {Sizes, Fonts} from 'common';
import {TouchableWithoutFeedback} from 'react-native';
import {checkDevice, defaultPrice} from 'utils/utils';
import MultiSlider from '@ptomasroos/react-native-multi-slider';

type Props = {
  visible: boolean;
  onClose?: (visible: boolean) => void;
  filterOptions?: CategoryFilters[];
  appliedFilter?: SelectCategoryFilters;
  applyFilters: (newFilters: SelectCategoryFilters) => void;
  applySortType?: (sortType: string) => void;
  modalType: 'filter' | 'sort';
  pageType: 'search' | 'category';
  priceSlag?: string;
  sortType?: string;
  onClear?: () => void;
  totalProduct?: number;
  onChange: (newFilters: SelectCategoryFilters) => void;
  resetApplyFilters: () => void;
};

const FilterAndSortModal = ({
  visible,
  onClose,
  appliedFilter,
  filterOptions,
  applyFilters,
  applySortType,
  modalType,
  priceSlag = '',
  pageType = 'category',
  sortType = '',
  onClear,
  totalProduct,
  onChange,
  resetApplyFilters,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const [tab, setTab] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [low, setLow] = useState(0);
  const [high, setHigh] = useState(defaultPrice);

  const [searchedBrandOptions, setSearchedBrandOptions] = useState<
    Array<CategoryFiltersOptions>
  >([]);
  const [filters, setFilters] = useState({});

  const sort = [
    {label: 'Recommended', key: pageType === 'search' ? '' : 'rcm'},
    {
      label: 'Price - High to Low',
      key: pageType === 'search' ? 'DSC' : 'price_hl',
    },
    {
      label: 'Price - Low to High',
      key: pageType === 'search' ? 'ASC' : 'price_lh',
    },
    // {label: 'Better Discount', key: pageType === 'search' ? '' : 'discount_hl'},
  ];

  const sortForShorts = [
    {label: 'Newest', key: ''},
    {label: 'Duration', key: ''},
    {label: 'Popularity', key: ''},
  ];

  const renderThumb = useCallback(
    () => <Thumb fillColor="whiteColor" borderColor="newSunnyOrange" />,
    [],
  );

  const handleValuesChangeStart = useCallback(() => {}, []);

  const handleValuesChangeFinish = useCallback(
    (values: [number, number]) => {
      const [lowValue, highValue] = values;
      setLow(lowValue);
      setHigh(highValue);

      const filterObj = {
        ...filters,
        [pageType === 'category' ? 'price_range' : 'price']: {
          ['min' + priceSlag]: lowValue,
          ['max' + priceSlag]: highValue,
        },
      };

      setFilters(filterObj);
      if (onChange) {
        onChange(filterObj);
      }
    },
    [pageType, priceSlag, filters],
  );

  const onValueChanged = useCallback(
    (lowValue: number, highValue: number) => {
      if (lowValue !== low || highValue !== high) {
        setLow(lowValue);
        setHigh(highValue);
        setFilters(prev => ({
          ...prev,
          [pageType === 'category' ? 'price_range' : 'price']: {
            ['min' + priceSlag]: lowValue,
            ['max' + priceSlag]: highValue,
          },
        }));
      }
    },
    [low, high, pageType, priceSlag],
  );

  const selectCategory = useCallback(
    (value: string, valueKey: string) => {
      var arr = new Set(filters[valueKey]);
      if (value === 'select all') {
        if (!checkAll) {
          setFilters({
            ...filters,
            [valueKey]: [
              ...(filterOptions?.[tab]?.options?.map(e => e.value) || []),
            ],
          });
        } else {
          setFilters({
            ...filters,
            [valueKey]: [],
          });
        }
        return;
      }

      if (arr.has(value)) {
        arr.delete(value);
      } else {
        arr.add(value);
      }
      setFilters({...filters, [valueKey]: [...arr]});
    },
    [filters, filterOptions, tab],
  );

  const selectRating = useCallback(
    (value: string, valueKey: string) => {
      setFilters({...filters, [valueKey]: value});
    },
    [filters],
  );

  const onApplyFilter = useCallback(() => {
    onClose?.(!visible);
    setTimeout(() => {
      applyFilters(filters);
    }, 200);
  }, [applyFilters, filters, onClose]);

  const clearFilter = useCallback(() => {
    setLow(
      filterOptions?.find(o => o?.type === 'range')?.['min' + priceSlag] || 0,
    );
    setHigh(
      filterOptions?.find(o => o?.type === 'range')?.['max' + priceSlag] ||
        defaultPrice,
    );
    let newFilters = {
      ...filters,
    };
    delete newFilters?.manufacturer;
    delete newFilters?.price;
    delete newFilters?.brand_ids;
    delete newFilters?.category;
    delete newFilters?.rating;
    setFilters(newFilters);
    resetApplyFilters();
  }, [filterOptions, filters, high, low, priceSlag, visible]);

  const clearCategoryFilter = useCallback(() => {
    setLow(0);
    setHigh(defaultPrice);

    let newFilters = {
      page_no: 1,
      applyFilters: {},
      [pageType === 'category' ? 'sort_by' : 'sortBy']:
        pageType === 'search' ? '' : 'rcm',
    };
    setFilters(newFilters);
    resetApplyFilters();
  }, [visible, applyFilters]);

  const applySort = useCallback(
    value => {
      if (pageType === 'search' || pageType === 'shorts') {
        onClose?.(!visible);
        applySortType(value);
      } else {
        let newFilters = {
          ...filters,
          [pageType === 'category' ? 'sort_by' : 'sortBy']: value,
        };
        if (!value) {
          delete newFilters.sortBy;
        }
        onClose?.(!visible);
        applyFilters({...newFilters});
      }
    },
    [filters, pageType, visible],
  );

  useEffect(() => {
    if (pageType !== 'shorts') {
      const rangeFilter = Array.isArray(filterOptions)
        ? filterOptions.find(o => o?.type === 'range') || {}
        : {};
      let minLow = rangeFilter?.['min' + priceSlag] ?? 0;
      let maxHigh = rangeFilter?.['max' + priceSlag] ?? defaultPrice;
      const {price_range = {}, price = {}} = appliedFilter || {};
      if (price_range || price) {
        minLow = price_range?.min ?? price?.min ?? minLow ?? 0;
        maxHigh = price_range?.max ?? price?.max ?? maxHigh ?? defaultPrice;
      }
      setLow(minLow);
      setHigh(maxHigh);
      setFilters(appliedFilter);
    }
  }, [filterOptions, appliedFilter, priceSlag]);

  useEffect(() => {
    if (searchText.length > 0) {
      let op = filterOptions[tab]?.options?.filter(text =>
        text.label.toLowerCase().includes(searchText.toLowerCase()),
      );
      setSearchedBrandOptions(op);
    } else {
      setSearchedBrandOptions(filterOptions[tab]?.options || []);
    }
  }, [searchText]);

  useEffect(() => {
    if (!visible) {
      setFilters(appliedFilter);
    }
  }, [visible]);

  const renderLabel = useCallback(
    value => (
      <Label
        color="textLight"
        size="m"
        fontFamily="Bold"
        text={'₹' + ' ' + value}
      />
    ),
    [],
  );
  const checkAll = useMemo(() => {
    const filterKeyMap = {
      manufacturer: filters?.manufacturer,
      category: filters?.category,
      brand_ids: filters?.brand_ids,
    };

    const selectedFilter = filterOptions[tab]?.value;
    const filterValues = filterKeyMap[selectedFilter];

    if (filterValues && filterOptions[tab]?.options?.length > 0) {
      const optionsValues = filterOptions[tab].options.map(
        option => option.value,
      );
      const allMatch = filterValues.every(item => optionsValues.includes(item));
      const extraKeysExist = optionsValues.some(
        item => !filterValues.includes(item),
      );
      return allMatch && !extraKeysExist;
    }
    return false;
  }, [filters, filterOptions, tab]);

  const isDefaultFilters = filters => {
    const filterKeyMap = {
      manufacturer: filters?.manufacturer,
      category: filters?.category,
      brand_ids: filters?.brand_ids,
      rating: filters?.rating,
      price_range: filters?.price_range,
    };

    const validKeys = filterOptions?.map(item => item.value);
    const requiredKeys = Object.keys(filterKeyMap).filter(key =>
      validKeys.includes(key),
    );

    const isFilterActive = requiredKeys.some(key => {
      const value = filters?.[key];
      if (key === 'manufacturer' || key === 'category' || key === 'brand_ids') {
        return value && value.length > 0;
      } else if (key === 'rating') {
        return Boolean(value);
      } else if (key === 'price_range') {
        const priceData = filterOptions.find(item => item.value === key) || {};
        const {min = 0, max = defaultPrice} = priceData;
        return value && (value.min !== min || value.max !== max);
      }
      return false;
    });

    return !isFilterActive;
    // return (
    //   filters?.applyFilters &&
    //   Object.keys(filters.applyFilters).length === 0 &&
    //   (!filters?.brand_ids || filters.brand_ids.length === 0) &&
    //   filters?.page_no === 1 &&
    //   filters?.price_range?.min === 0 &&
    //   filters?.price_range?.max === 200000 &&
    //   !filters?.rating
    // );
  };

  const isEmptyFilters = filters => {
    return (
      (!filters?.category || filters.category.length === 0) &&
      (!filters?.manufacturer || filters.manufacturer.length === 0) &&
      (!filters?.price ||
        (filters.price.min === 0 && filters.price.max === defaultPrice)) &&
      (!filters?.rating || filters.rating === 0)
    );
  };

  return (
    <Modal
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      isVisible={visible}
      useNativeDriver={true}
      style={styles.modalStyle}
      backdropOpacity={0.45}
      onBackButtonPress={() => {
        onClose?.(!visible);
      }}>
      <View style={styles.flexContainer}>
        <SafeAreaView />
        <View style={styles.centeredView}>
          <TouchableWithoutFeedback onPress={() => onClose?.(!visible)}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={() => onClose?.(!visible)}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x6l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <View
            style={[
              styles.modelBg,
              {
                flex:
                  modalType === 'filter'
                    ? Sizes.s
                    : Platform.OS === 'ios'
                    ? 0.3
                    : 0.45,
              },
            ]}>
            <View style={styles.modalHeader}>
              <Label
                text={
                  modalType === 'filter'
                    ? t('filters.filter')
                    : t('filters.sortBy')
                }
                size="l"
                fontFamily="SemiBold"
                color="text"
                style={styles.titleTxt}
              />
              {onClear && (
                <TouchableOpacity
                  onPress={() => [onClose?.(!visible), onClear()]}>
                  <Label
                    text={t('filters.clearTxt')}
                    size="mx"
                    fontFamily="SemiBold"
                    color="text"
                  />
                </TouchableOpacity>
              )}
            </View>
            <Separator color="grey2" />
            {modalType === 'filter' ? (
              <View style={styles.flexRow}>
                <View style={styles.filterListLeftContainer}>
                  <View style={styles.quickFilterSectionHeader}>
                    <Label
                      text={t('filters.quickFilters')}
                      size="mx"
                      fontFamily="SemiBold"
                      color="text"
                      weight="500"
                    />
                  </View>
                  <View style={styles.filterListLeft}>
                    {filterOptions?.map((filterOption, index) => (
                      <TouchableOpacity
                        key={index.toString()}
                        onPress={() => setTab(index)}
                        style={[
                          styles.listItemLeft,
                          {
                            backgroundColor:
                              tab === index
                                ? colors.whiteColor
                                : colors.offWhite,
                          },
                        ]}>
                        <Label
                          text={filterOption?.key}
                          size="mx"
                          fontFamily={tab === index ? 'SemiBold' : 'Medium'}
                          color={tab === index ? 'text' : 'text2'}
                          weight={tab === index ? '500' : '400'}
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
                <View style={styles.filterListRightContainer}>
                  <View
                    style={[
                      styles.rightSideHeader,
                      filterOptions?.[tab]?.type === 'checkBox' && {
                        borderBottomColor: colors.grey2,
                        borderBottomWidth: Sizes.z,
                      },
                    ]}>
                    {filterOptions?.map((item, index) => {
                      return (
                        <View key={index.toString()}>
                          {!!item?.isSearch &&
                          tab === index &&
                          filterOptions?.[tab]?.options?.length > 1 ? (
                            <>
                              <View style={styles.searchInput}>
                                <Spacer size="x4l" type="Horizontal" />
                                <ImageIcon
                                  icon="search1"
                                  size="l"
                                  tintColor="grey"
                                />
                                <Spacer size="sx" type="Horizontal" />
                                <TextInput
                                  testID="txtFilterAndSortSearch"
                                  placeholderTextColor={colors.text2}
                                  placeholder={t('filters.search')}
                                  style={styles.searchStyle}
                                  onChangeText={setSearchText}
                                  value={searchText}
                                  allowFontScaling={false}
                                />
                              </View>
                            </>
                          ) : null}
                          {item?.type === 'checkBox' &&
                          tab === index &&
                          filterOptions?.[tab]?.options?.length > 1 ? (
                            <>
                              {searchText.length === 0 ? (
                                <TouchableOpacity
                                  onPress={() =>
                                    selectCategory(
                                      'select all',
                                      filterOptions[tab]?.value,
                                    )
                                  }
                                  style={styles.selectAll}>
                                  <ImageIcon
                                    icon={
                                      filters[
                                        filterOptions[tab]?.value
                                      ]?.includes('select all') || checkAll
                                        ? 'checkBox'
                                        : 'uncheckBox'
                                    }
                                    size="xx"
                                  />
                                  <Spacer size="xm" type="Horizontal" />
                                  <Label
                                    text={t('filters.selectAll')}
                                    size="mx"
                                    fontFamily={
                                      item?.isSearch ? 'Regular' : 'Medium'
                                    }
                                    color={item?.isSearch ? 'text2' : 'text'}
                                  />
                                </TouchableOpacity>
                              ) : null}
                            </>
                          ) : null}
                        </View>
                      );
                    })}
                  </View>
                  {filterOptions[tab]?.isSearch &&
                    searchText.length === 0 &&
                    filterOptions?.[tab]?.options?.length > 1 && (
                      <Spacer size="x34" />
                    )}
                  <View style={styles.filterList}>
                    {filterOptions[tab]?.type === 'checkBox' ? (
                      <View style={styles.scrollView}>
                        <ScrollView
                          style={styles.listView}
                          contentContainerStyle={{flexGrow: 1}}
                          keyboardShouldPersistTaps="handled"
                          keyboardDismissMode="on-drag">
                          {(searchText.length > 0
                            ? searchedBrandOptions
                            : filterOptions?.[tab]?.options
                          )?.map((option, index) => (
                            <View key={index.toString()}>
                              <TouchableOpacity
                                onPress={() =>
                                  selectCategory(
                                    option?.value,
                                    filterOptions[tab]?.value,
                                  )
                                }
                                style={styles.rowCenteredItems}>
                                <ImageIcon
                                  icon={
                                    filters[
                                      filterOptions[tab]?.value
                                    ]?.includes(option?.value)
                                      ? 'checkBox'
                                      : 'uncheckBox'
                                  }
                                  size="xx"
                                />
                                <Spacer size="xm" type="Horizontal" />
                                <Label
                                  text={option?.label}
                                  size="mx"
                                  fontFamily="Regular"
                                  color="text2"
                                />
                              </TouchableOpacity>
                              <Spacer size="m" />
                            </View>
                          ))}
                        </ScrollView>
                      </View>
                    ) : filterOptions?.[tab]?.type === 'range' ? (
                      <View style={styles.sliderView}>
                        <View style={styles.sliderMainView}>
                          <View style={styles.rnSlider}>
                            <MultiSlider
                              vertical={true}
                              values={[
                                filters?.price?.min ||
                                  filters?.price_range?.min ||
                                  low,
                                filters?.price?.max ||
                                  filters?.price_range?.max ||
                                  high,
                              ]}
                              sliderLength={200}
                              onValuesChangeStart={handleValuesChangeStart}
                              onValuesChange={([low, high]) =>
                                onValueChanged(low, high)
                              }
                              onValuesChangeFinish={handleValuesChangeFinish}
                              min={filterOptions?.[tab]?.min}
                              max={filterOptions?.[tab]?.max}
                              minMarkerOverlapDistance={1}
                              step={1}
                              markerStyle={styles.markerview}
                              selectedStyle={styles.selectedView}
                              unselectedStyle={styles.unselectedView}
                              customMarker={e => (
                                <TouchableOpacity style={styles.roundView}>
                                  {renderThumb(e.currentValue)}
                                </TouchableOpacity>
                              )}
                            />
                          </View>

                          <View style={styles.rangeLabelContainer}>
                            <Label
                              color="textLight"
                              size="mx"
                              fontFamily="Medium"
                              weight="500"
                              text={t('filters.max')}>
                              <Label
                                color="textLight"
                                size="mx"
                                fontFamily="Medium"
                                weight="500"
                                text={'  ₹' + high}
                              />
                            </Label>

                            <Label
                              color="textLight"
                              size="mx"
                              fontFamily="Medium"
                              weight="500"
                              text={t('filters.min')}>
                              <Label
                                color="textLight"
                                size="mx"
                                fontFamily="Medium"
                                weight="500"
                                text={'  ₹' + low}
                              />
                            </Label>
                          </View>
                        </View>
                        <Label
                          color="text2"
                          size="m"
                          fontFamily="Medium"
                          weight="500"
                          text={`${totalProduct} ${t('filters.productsFound')}`}
                          style={styles.productTxt}
                        />
                      </View>
                    ) : (
                      <>
                        <View
                          style={
                            filterOptions[tab]?.options?.length > 0
                              ? styles.ratingView
                              : {}
                          }>
                          {filterOptions[tab]?.options?.map((option, index) => (
                            <TouchableOpacity
                              key={index.toString()}
                              onPress={() =>
                                selectRating(
                                  filters?.rating === option.value
                                    ? 0
                                    : option.value,
                                  'rating',
                                )
                              }
                              activeOpacity={1}
                              style={styles.ratingCheckboxRow}>
                              <ImageIcon
                                icon={
                                  filters?.rating === option.value
                                    ? 'checkBox'
                                    : 'uncheckBox'
                                }
                                size="l"
                              />
                              <Spacer size="xm" type="Horizontal" />
                              <View style={styles.starIconRow}>
                                {[0, 1, 2, 3, 4].map((value, index) => (
                                  <View key={index.toString()}>
                                    <ImageIcon
                                      icon="starRate"
                                      tintColor={
                                        Number(parseInt(option?.value)) > value
                                          ? 'categoryTitle'
                                          : 'grey2'
                                      }
                                      size="xl"
                                      style={styles.rateIconStyle}
                                    />
                                  </View>
                                ))}
                              </View>
                              <Spacer size="s" type="Horizontal" />
                              <Label
                                text={option.label}
                                size="mx"
                                fontFamily="Medium"
                                color="text2"
                              />
                            </TouchableOpacity>
                          ))}
                        </View>
                      </>
                    )}
                  </View>
                </View>
              </View>
            ) : (
              <View style={styles.sortContainer}>
                {(pageType === 'shorts' ? sortForShorts : sort).map(
                  (item, index) => {
                    const checkRadio = true;
                    const selectValue =
                      pageType === 'shorts' ? item.label : item.key;
                    return (
                      <View key={index.toString()}>
                        <TouchableOpacity
                          onPress={() => applySort(selectValue)}
                          style={styles.rowCenteredItems}>
                          {checkRadio && (
                            <>
                              <TouchableOpacity
                                onPress={() => applySort(selectValue)}>
                                <ImageIcon
                                  icon={
                                    sortType === selectValue
                                      ? 'circleFill'
                                      : 'circle1'
                                  }
                                  size="l"
                                />
                              </TouchableOpacity>
                              <Spacer size="xms" type="Horizontal" />
                            </>
                          )}
                          <Label
                            text={item.label}
                            color={sortType === selectValue ? 'text' : 'text2'}
                            size="mx"
                            fontFamily="Medium"
                            style={styles.flexOne}
                          />
                          {!checkRadio && (
                            <TouchableOpacity
                              onPress={() => applySort(selectValue)}>
                              <ImageIcon
                                icon={
                                  sortType === selectValue
                                    ? 'checkVrifiedIcon'
                                    : 'unCheckVrifiedIcon'
                                }
                                size="xsl"
                              />
                            </TouchableOpacity>
                          )}
                        </TouchableOpacity>
                        <Spacer size="l" />
                      </View>
                    );
                  },
                )}
              </View>
            )}
          </View>
        </View>
        {modalType === 'filter' ? (
          <FooterButton
            footerStyle={styles.footerSection}
            shadowProps={false}
            useInsets={false}
            buttons={[
              (pageType === 'category' && !isDefaultFilters(filters)) ||
              (pageType === 'search' && !isEmptyFilters(filters))
                ? {
                    key: 'category_clear',
                    labelSize: 'mx',
                    radius: 'm',
                    fontFamily: Fonts.Medium,
                    labelColor: 'sunnyOrange4',
                    selfAlign: 'stretch',
                    text: t('buttons.clear'),
                    onPress:
                      pageType === 'category'
                        ? clearCategoryFilter
                        : clearFilter,
                    style: checkDevice() ? styles.btnClearTab : styles.btnClear,
                  }
                : null,

              {
                key: 'apply_filter',
                text: t('buttons.applyFilter'),
                onPress:
                  (pageType === 'category' && !isDefaultFilters(filters)) ||
                  (pageType === 'search' && !isEmptyFilters(filters))
                    ? onApplyFilter
                    : null,
                type:
                  (pageType === 'category' && !isDefaultFilters(filters)) ||
                  (pageType === 'search' && !isEmptyFilters(filters))
                    ? 'primary'
                    : 'disabled',
                radius: 'm',
                labelSize: 'mx',
                fontFamily: Fonts.Medium,
                selfAlign: 'stretch',
                labelColor: 'background',
                style: checkDevice() ? styles.btnApplyTab : styles.btnApply,
              },
            ]}
          />
        ) : null}
      </View>
      <SafeAreaView style={styles.safeView} />
    </Modal>
  );
};

export default memo(FilterAndSortModal);

import {Fonts, Sizes} from 'common';
import {StyleSheet, Dimensions} from 'react-native';
const {width, height} = Dimensions.get('window');

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flexView: {
      flex: Sizes.x,
    },
    screenContainer: {
      flex: Sizes.x,
    },
    iconSize: {
      width: Sizes.xxl,
      height: Sizes.xxl,
    },
    fontStyle: {
      fontSize: Sizes.l,
      fontFamily: Fonts.Medium,
      color: colors.background,
      alignSelf: 'center',
    },
    actionBox: {
      alignSelf: 'flex-end',
      justifyContent: 'center',
    },
    headerTopView: {
      position: 'absolute',
      width: '100%',
      height: Sizes.ex2l,
    },
    headerView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.mx,
      padding: Sizes.s,
    },
    headerTitle: {
      textTransform: 'capitalize',
      flex: Sizes.x,
      marginRight: Sizes.x3l,
    },
    goBackIcon: {
      marginRight: Sizes.xms,
      width: Sizes.xxxl,
      height: Sizes.xxxl,
    },
    mainContinuer: {
      marginHorizontal: Sizes.l,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    descriptionContainer: {
      flex: Sizes.x,
    },
    description: {
      textTransform: 'capitalize',
    },
    featuredProductContainer: {
      marginTop: Sizes.xxl,
      flexDirection: 'row',
      alignItems: 'center',
    },
    featuredProductText: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.SemiBold,
      color: colors.background,
    },
    featuredProductArrowIcon: {
      marginLeft: Sizes.xms,
      width: Sizes.l,
      height: Sizes.l,
    },
    continuer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: Sizes.xm,
    },
    userNameText: {
      color: colors.commentDate,
      paddingTop: Sizes.m,
    },
    commentDate: {
      color: colors.commentDate,
      paddingVertical: Sizes.xs,
    },
    comments: {
      color: colors.textLight,
      bottom: Sizes.xs,
      textTransform: 'capitalize',
    },
    closeIcon: {
      width: Sizes.xsl,
      height: Sizes.xsl,
    },
    commentHeading: {
      textAlign: 'center',
      fontSize: Sizes.l,
      fontFamily: Fonts.Bold,
      color: colors.textLight,
    },
    commentHeadingCont: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      paddingTop: Sizes.m,
    },
    commentHeadingMainCont: {
      paddingHorizontal: Sizes.l,
      justifyContent: 'flex-end',
      flex: Sizes.x,
      paddingBottom: Sizes.xl,
    },
    commentInput: {
      borderColor: colors.lightGrayBackground,
      borderRadius: Sizes.xl,
      width: '90%',
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'center',
      borderWidth: 0.5,
      bottom: Sizes.xms,
      marginHorizontal: Sizes.l,
    },
    profileImage: {
      width: Sizes.xxxl,
      height: Sizes.xxxl,
    },
    profileName: {
      textTransform: 'capitalize',
      color: colors.background,
      marginLeft: Sizes.xms,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
    },
    featuredProductContentContainer: {
      flex: Sizes.x,
      marginBottom: Sizes.xms,
      marginTop: Sizes.xms,
    },
    productContainer: {
      width: Sizes.ex3l + Sizes.xm + Sizes.x,
      height: Sizes.ex92 + Sizes.x,
      backgroundColor: colors.whiteColor,
      marginRight: Sizes.m,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      flexDirection: 'row',
      padding: Sizes.xm,
    },
    productBody: {
      width: Sizes.x76,
      height: Sizes.x76,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.orient16,
      borderRadius: Sizes.xm,
    },
    productPriceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    price: {
      fontFamily: Fonts.SemiBold,
      fontSize: Sizes.mx,
      marginLeft: Sizes.s,
      textDecorationLine: 'line-through',
      textDecorationStyle: 'solid',
      color: colors.velvetLightPink,
    },
    priceOff: {
      fontFamily: Fonts.SemiBold,
      fontSize: Sizes.mx,
      marginLeft: Sizes.s,
      color: colors.grassGreen,
    },
    productViewDetails: {
      color: colors.notice,
      marginVertical: Sizes.s,
      marginRight: Sizes.l,
      textAlign: 'right',
      textDecorationLine: 'underline',
    },
    featuredLoading: {
      marginTop: '50%',
    },
    featuredProductLoading: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    gradient: {
      position: 'absolute',
      width: '100%',
      bottom: 0,
      zIndex: Sizes.xms + Sizes.x,
      height: Sizes.ex5l,
    },
    itemView: {
      position: 'absolute',
      left: Sizes.l,
      right: Sizes.l,
      zIndex: Sizes.m,
    },
    productPriceText: {
      fontFamily: Fonts.Bold,
      fontSize: Sizes.xx,
    },
    videoPlayerContainer: {
      width: width,
      height: height,
      backgroundColor: colors.textLight,
    },
    profileContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Sizes.xms,
    },
    likeContainer: {
      alignSelf: 'flex-end',
      justifyContent: 'flex-end',
    },
    left: {
      marginLeft: Sizes.s,
    },
    sendIcon: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    input: {
      width: '90%',
      marginLeft: Sizes.s,
    },
    list: {
      flex: Sizes.x,
      marginBottom: Sizes.xl,
    },
    saprater: {
      marginVertical: Sizes.m,
    },
    countTxt: {
      color: colors.whiteColor,
      fontSize: Sizes.m,
    },
    rowAlignCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    coinNumber: {
      height: Sizes.xl,
    },
    productView: {
      position: 'absolute',
      bottom: Sizes.xl,
      marginTop: Sizes.l,
      marginLeft: Sizes.l,
    },
    musicIcon: {
      width: Sizes.x3l,
    },
  });
export default styles;

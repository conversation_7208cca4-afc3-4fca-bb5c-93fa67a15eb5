import {Sizes} from 'common';
import {Dimensions, StyleSheet} from 'react-native';
const {width: SCREEN_WIDTH} = Dimensions.get('window');
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    backgroundImage: {
        resizeMode: 'contain',
        justifyContent: 'center',
    },
    sellerView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: Sizes.l,
    },
    listDataView: {
        flex: Sizes.x,
        paddingHorizontal: Sizes.m,
        paddingBottom: Sizes.xxl,
    },
  });
export default styles;

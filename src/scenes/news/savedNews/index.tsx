// import React, {useCallback} from 'react';
// import {SafeAreaView} from 'react-native-safe-area-context';
// // eslint-disable-next-line @typescript-eslint/no-unused-vars
// import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
// // eslint-disable-next-line @typescript-eslint/no-unused-vars
// import {RootStackParamsList} from '../../../routes';
// import {Header, NewsList} from 'components/molecules';
// import stylesWithOutColor from './style';
// import {LikeShare} from 'components/atoms';
// // eslint-disable-next-line @typescript-eslint/no-unused-vars
// import {RouteProp, useTheme} from '@react-navigation/native';
// import {t} from 'i18next';
// type Props = {
//   navigation: NativeStackNavigationProp<RootStackParamsList>;
//   route: RouteProp<RootStackParamsList, 'Saved'>;
// };

// const SavedScene = ({navigation, route}: Props) => {
//   const {colors} = useTheme();
//   const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
//   const likeShare = useCallback((item: News) => {
//     return (
//       <LikeShare
//         isSaved={true}
//         onSavePress={() => {}}
//         likeText={'Like'}
//         shareText={'Share'}
//         saveText={'Save'}
//         news={item}
//       />
//     );
//   }, []);

//   return (
//     <SafeAreaView style={styles.container}>
//       <Header
//         text={t('news.savedNews')}
//         hadarLogo={false}
//         navigation={navigation}
//       />
//       <NewsList
//         type="save"
//         route={route}
//         likeShare={likeShare}
//         navigation={navigation}
//       />
//     </SafeAreaView>
//   );
// };

// export default SavedScene;

// import React, {useCallback, useEffect, useState} from 'react';
// import {SafeAreaView} from 'react-native-safe-area-context';
// // eslint-disable-next-line @typescript-eslint/no-unused-vars
// import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
// // eslint-disable-next-line @typescript-eslint/no-unused-vars
// import {RootStackParamsList} from 'routes';
// import {Header, NewsList} from 'components/molecules';
// import tokenClass from 'utils/token';
// import {bookmarkNews, savedNews} from 'api';
// import {showInfoMessage, showSuccessMessage} from 'utils/show_messages';
// import {LikeShare} from 'components/atoms';
// import {useTheme} from '@react-navigation/native';
// import {t} from 'i18next';

// type Props = {
//   navigation: NativeStackNavigationProp<RootStackParamsList>;
//   item: News;
//   onPress: () => void;
// };

// const NewsScene = ({navigation, route}: Props) => {
//   const [savedNewsData, setSavedNewsData] = useState([]);
//   const {colors} = useTheme();
//   const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

//   const saveNews = useCallback(async () => {
//     const {data} = await savedNews();
//     setSavedNewsData(data?.savedNews);
//   }, []);

//   useEffect(() => {
//     saveNews();
//   }, [route]);

//   const likeShare = useCallback(
//     (item: News) => {
//       const onSavePress = async () => {
//         if (await tokenClass.loginStatus()) {
//           const {data} = await bookmarkNews({id: item.id});
//           if (data.bookmarkNews) {
//             saveNews();
//             showSuccessMessage(t('toastMassages.saveSuccess'));
//           }
//         } else {
//           showInfoMessage(t('toastMassages.loginInfo'));
//           navigation.navigate('Login', {nextScreenName: 'NewsTab'});
//         }
//       };

//       return (
//         <LikeShare
//           style={styles.likesComponent}
//           isSaved={savedNewsData.some((e: News) => e.id === item.id)}
//           onSavePress={onSavePress}
//           likeText={'Like'}
//           shareText={'Share'}
//           saveText={'Save'}
//           news={item}
//         />
//       );
//     },
//     [savedNewsData, saveNews],
//   );

//   return (
//     <SafeAreaView style={styles.container}>
//       {/* <Header
//         text={t('news.recent')}
//         navigation={navigation}
//         hadarLogo={false}
//       /> */}
//       <Header
//         navigation={navigation}
//         backButton={true}
//         heartIcon
//         tintColorGOBack="text"
//         bagIcon
//         searchIcon
//         text={t('News')}
//         // text={t('news.News')}
//       />
//       <NewsList
//         type="news"
//         route={route}
//         likeShare={likeShare}
//         navigation={navigation}
//       />
//     </SafeAreaView>
//   );
// };

// export default NewsScene;

// * News List *//

import React, {useCallback, useContext, useEffect, useState} from 'react';
import {
  TouchableOpacity,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  ButtonProps,
  View,
  SafeAreaView,
} from 'react-native';
import {
  FooterButton,
  ImageIcon,
  Label,
  Separator,
  Spacer,
} from 'components/atoms';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from 'routes';
import {useIsFocused, useTheme} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import {FlatList} from 'react-native-gesture-handler';
import Button from 'components/molecules/button';
import {Icon} from 'react-native-vector-icons/Icon';
import {Sizes} from 'common';
import {Header} from 'components/molecules';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: any;
  text?: string;
  icon?: keyof typeof Icons | null;
  onPress?: () => void;
  style?: ButtonProps['style'];
  type: 'news' | 'save';
  likeShare: (item: News) => React.ReactElement;
};

const SavedScene = ({type = 'news', navigation, likeShare, route}: Props) => {
  const dispatch = useDispatch();
  const [recentTopic, setRecentTopic] = useState<News[]>([]);
  const isFocused = useIsFocused();
  const [showNewsLink, setShowNewsLink] = useState<null | string>(null);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isPressed, setIsPressed] = useState(false);
  // *xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

  const memoizedSeparator = useCallback(() => {
    return <Separator Vertical color="grey2" />;
  }, []);
  const [isModelType, setIsModelType] = useState<'filter' | 'sort'>('');
  const [modelVisible, setModelVisible] = useState(false);

  const openFilterModel = () => {
    setIsModelType('filter');
    setModelVisible(true);
  };
  const openSortModel = () => {
    setIsModelType('sort');
    setModelVisible(true);
  };

  // const [error, setError] = useState({});
  // const {store} = useContext(context);

  // ----------------News apii--------------------

  // const newsData = useCallback(async () => {
  //   dispatch(setLoading(true));
  //   const resNews = await news();
  //   const resSavedNews = await savedNews();
  //   if (type === 'news') {
  //     setRecentTopic(resNews?.data?.news || []);
  //     dispatch(setLoading(false));
  //   } else {
  //     if (!(await tokenClass.loginStatus())) {
  //       showInfoMessage(t('toastMassages.loginInfo'));
  //       return navigation.navigate('Login', {
  //         nextScreenName: 'NewsTab',
  //         nextScreenParams: {
  //           screen: 'Saved',
  //         },
  //       });
  //     }
  //     setRecentTopic(resSavedNews?.data?.savedNews || []);
  //     dispatch(setLoading(false));
  //   }
  // }, [dispatch, navigation, type]);
  // ``
  const data1 = [
    {
      id: '1',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner,
    },
    {
      id: '2',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner,
    },
    {
      id: '3',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner,
    },
    {
      id: '4',
      name: 'Name',
      speciality: 'Speciality',
      description:
        'Navigating the CAD/CAM and workflow in modern and more Navigating the CAD/CAM ...',
      date: '1 February, 2024',
      readTime: '3 mins read',
      imageSource: Icons.memberShipBanner,
    },
    // Add more data objects here
  ];

  const renderItem1 = ({item}) => (
    <>
      <Spacer size="m" />
      <View style={styles.card4}>
        <View style={styles.sec4View1}>
          <View style={{flexDirection: 'column'}}>
            <View style={styles.sec4LabelView}>
              <Label color="categoryTitle" size="m" text={item.name} />
              <Label
                style={styles.labelBorder}
                color="categoryTitle"
                size="m"
                text={'|'}
              />
              <Label color="categoryTitle" size="m" text={item.speciality} />
            </View>
            <Label
              text={'----------------------------'}
              color="grey2"
              lineHeight="mx"
              weight="500"
              size="mx"
            />
            <View>
              <Label text={item.description} color="categoryTitle" size="mx" />
            </View>
          </View>
          <Spacer size="xm" />
          <View style={styles.sec4LabelView}>
            <Spacer type="Horizontal" size="xm" />
            <Label weight="600" size="m" text={'•'} color="grey" />
            <Spacer type="Horizontal" size="sx" />
            <Label color="grey" size="m" text={item.date} />
            <Spacer type="Horizontal" size="xm" />
            <Label weight="600" size="m" text={'•'} color="grey" />
            <Spacer type="Horizontal" size="sx" />
            <Label color="grey" size="m" text={item.readTime} />
          </View>
          <Spacer size="xm" />
          <View style={styles.iconContainer}>
            <TouchableOpacity>
              <ImageIcon tintColor="text2" icon="likeIcon" size="xxl" />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xl" />
            <TouchableOpacity>
              <ImageIcon tintColor="text2" icon="newBookmarkIcon" size="xxl" />
            </TouchableOpacity>
            <Spacer type="Horizontal" size="xl" />
            <TouchableOpacity>
              <ImageIcon tintColor="text2" icon="forwardIcon" size="xxl" />
            </TouchableOpacity>
          </View>
        </View>
        <Spacer type="Horizontal" size="m" />
        <View style={styles.sec4View2}>
          <FastImage
            source={item.imageSource}
            resizeMode="stretch"
            style={styles.cardImage}
          />
        </View>
      </View>
    </>
  );

  return (
    <>
      <Header
        navigation={navigation}
        backButton={true}
        heartIcon
        tintColorGOBack="text"
        bagIcon
        searchIcon
        text={t('Saved')}
        // text={t('news.News')}
      />
      <SafeAreaView style={styles.maincontainer}>
        <FlatList
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          data={[' ']}
          renderItem={() => (
            <>
              <View>
                <FlatList
                  data={data1}
                  renderItem={renderItem1}
                  keyExtractor={item => item.id}
                />
              </View>
            </>
          )}
        />
        {/* footer */}
      </SafeAreaView>
      <FooterButton
        shadowProps={true}
        useInsets={true}
        // footerStyle={{borderWidth: 1, width: '100%', borderRadius: 0}}
        separator={memoizedSeparator}
        buttons={[
          {
            text: 'Saved',
            onPress: openFilterModel,
            ghost: true,
            iconLeft: 'newBookmarkIcon',
            labelSize: 'l',
            weight: '400',
            labelColor: 'text',
            tintColor: 'text',
            size: 'large',
            selfAlign: 'stretch',
          },
          {
            text: 'Sort By',
            height: 42,
            iconLeft: 'sortIcons',
            onPress: openSortModel,
            ghost: true,
            labelSize: 'l',
            weight: '400',
            labelColor: 'text',
            selfAlign: 'stretch',
          },
        ]}
      />
    </>
  );
};
export default SavedScene;

import React, {useCallback, useEffect} from 'react';
import {SafeAreaView, ScrollView, TouchableOpacity, View} from 'react-native';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from '../../routes';
import {ImageIcon, Label, ListView, Spacer} from 'components/atoms';
import {But<PERSON>, Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {navigate} from 'utils/navigationRef';
import {showInfoMessage, showSuccessMessage} from 'utils/show_messages';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {useDispatch, useSelector} from 'react-redux';
import {setLogout} from 'app-redux-store/slice/appSlice';
import <PERSON>rrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  item: ProductData | GetAttributesBySkuOutput | CustomerType;
};
type ListItem = {
  id: number;
  userIcon: string;
  name: string;
  screenName: string;
  screenParams: any;
};

const ProfileScene = ({item, navigation}: Props) => {
  const TAG = 'ProfileScreen';
  const {colors} = useTheme();
  const dispatch = useDispatch();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {isLoggedIn, userInfo} = useSelector((state: RootState) => state.app);
  const user = userInfo ? userInfo : {};
  const data = [
    {
      id: 1,
      userIcon: 'userIcon',
      name: t('profile.profileDetails'),
      screenName: 'ProfileDetails',
      screenParams: {},
    },
    {
      id: 2,
      userIcon: 'locationIcon',
      name: t('profile.manageAddress'),
      screenName: 'AddressList',
      screenParams: {},
    },
    {
      id: 3,
      userIcon: 'orderIcon',
      name: t('profile.myOrder'),
      screenName: 'OrderList',
      screenParams: {},
    },
    {
      id: 4,
      userIcon: 'heartIcon',
      name: t('profile.myWishList'),
      screenName: 'WishList',
      screenParams: {},
    },

    {
      id: 5,
      userIcon: 'bitcoinIcon',
      name: t('profile.myRewards'),
      screenName: 'MyRewords',
      screenParams: {},
    },
    {
      id: 6,
      userIcon: 'bitcoinIcon',
      name: 'Reward Zone',
      screenName: 'RewardZoneScene',
      screenParams: {},
    },
    {
      id: 7,
      userIcon: 'solidUserGroupIcon',
      name: 'My Referral',
      screenName: 'MyReferral',
      screenParams: {},
    },
    {
      id: 8,
      userIcon: 'returnIcon',
      name: t('profile.membership'),
      screenName: 'MyMembership',
      screenParams: {},
    },
    {
      id: 9,
      userIcon: 'helpIcon',
      name: t('profile.helpCenter'),
      screenName: 'HelpCenter',
      screenParams: {id: item?.id},
    },
    {
      id: 10,
      userIcon: 'coupon',
      name: t('profile.coupons'),
      screenName: 'CouponsScene',
      screenParams: {id: item?.id},
    },
    {
      id: 11,
      userIcon: 'saved',
      name: 'Saved Shorts',
      screenName: 'SavedShorts',
      screenParams: {id: item?.id},
    },
  ];

  const navigateToScreen = useCallback(
    async (item: ListItem) => {
      // if (!isLoggedIn) {
      //   showInfoMessage(t('toastMassages.loginMessage'));
      // }
      navigate(
        isLoggedIn ? item?.screenName : 'HelpCenter',
        isLoggedIn
          ? {
              nextScreenName: item?.screenName,
              nextScreenParams: {...item.screenParams},
            }
          : {
              nextRouterState: {
                index: 0,
                routes: [
                  {
                    name: 'Tab',
                    state: {
                      routes: [
                        {
                          name: 'Shop',
                        },
                      ],
                    },
                  },
                  {
                    name: 'Tab',
                    state: {
                      routes: [
                        {
                          name: 'Profile',
                        },
                      ],
                    },
                  },
                  {
                    name: item?.screenName,
                    params: {
                      nextScreenParams: {...item.screenParams},
                    },
                  },
                ],
              },
            },
      );
    },
    [isLoggedIn],
  );

  // ----------logOut -------------
  const logout = useCallback(async () => {
    dispatch(setLogout('Login'));
    showSuccessMessage(t('toastMassages.logOut'));
  }, [dispatch]);

  const renderItems = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-shadow
    ({item, index}: {item: OrderDetailsV1; index: number}) => {
      return (
        <View key={index} style={[styles.profileContainer, styles.user]}>
          <View style={styles.userList}>
            <ImageIcon
              size="xx"
              resizeMode="contain"
              tintColor="primary"
              icon={item.userIcon}
            />
          </View>
          <Spacer type="Horizontal" size="m" />
          <TouchableOpacity
            onPress={() => navigateToScreen(item)}
            style={[styles.itemDerication, styles.user]}>
            <Label text={item?.name} size="l" />
            <ImageIcon size="xm" tintColor="textLight" icon="nextVectorIcon" />
          </TouchableOpacity>
        </View>
      );
    },
    [navigateToScreen],
  );

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );
  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header bagIcon={true} navigation={navigation} searchIcon={true} />
      </ErrorHandler>
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.mainContainer}>
        <Spacer type="Vertical" size="x5l" />
        <TouchableOpacity
          onPress={() => navigate('ProfileDetails')}
          style={[styles.profileContainer, styles.user]}>
          <ImageIcon
            tintColor="primary"
            style={styles.profileImage}
            icon="profileIcon"
          />
          <Spacer type="Horizontal" size="m" />
          <View>
            <View style={styles.user}>
              <Label
                color="textLight"
                text={user?.firstname || t('profile.guest')}
              />
              <Spacer size="s" />
              <Label color="textLight" text={user?.lastname} />
            </View>
            <Label
              numberOfLines={1}
              color="textLight"
              text={user?.email || t('profile.withOutLogin')}
            />
          </View>
        </TouchableOpacity>
        <Spacer type="Vertical" size="x5l" />
        <ErrorHandler
          componentName={`${TAG} ListView`}
          onErrorComponent={<View />}>
          <ListView
            keyExtractor={listViewKeyExtractor}
            data={data}
            renderItem={renderItems}
            ItemSeparatorComponent={<Spacer type="Vertical" size="l" />}
          />
        </ErrorHandler>
        <Spacer type="Vertical" size="x6l" />
        <Spacer type="Vertical" size="xl" />
        <ErrorHandler
          componentName={`${TAG} Button`}
          onErrorComponent={<View />}>
          <Button
            tintColor="whiteColor"
            leftIconColor="whiteColor"
            iconStyle={styles.buttonIcon}
            style={styles.logOutButton}
            labelStyle={styles.logout}
            iconLeft="logOutIcon"
            text={
              isLoggedIn === true ? t('buttons.logout') : t('buttons.login')
            }
            onPress={() => {
              isLoggedIn === true ? logout() : navigate('Login');
            }}
          />
        </ErrorHandler>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileScene;

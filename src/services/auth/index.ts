import Api from 'services';
import {apiRoute, apiEndpoints} from 'config/apiEndpoint';
import CryptoJS from 'crypto-js';
import {SECRETKEY} from 'config/environment';

const API = new Api();

const encryption = recipient => {
  const getTwoHalf = input => {
    const midpoint = Math.floor(input.length / 2);
    const firstHalf = input.substr(0, midpoint);
    const secondHalf = input.substr(midpoint);
    return {firstHalf, secondHalf};
  };

  const secret_key = SECRETKEY;
  const inputType = String(recipient);
  const {firstHalf, secondHalf} = getTwoHalf(inputType);
  const stringToBeEncrypted = firstHalf + secret_key + secondHalf;

  const key = CryptoJS.enc.Hex.parse(secret_key);
  const encrypted = CryptoJS.AES.encrypt(stringToBeEncrypted, key, {
    mode: CryptoJS.mode.ECB,
  });

  return encrypted.toString();
};

// export const userLogin = async (data: {
//   recipient: string;
//   action: string;
//   authentication_type: string | undefined;
// }) => {
//   const url = `${apiRoute.customers}${apiEndpoints.signIn}`;
//   const resp = await API.post(url, data);
//   return resp;
// };

export const userLogin = async (data: {
  recipient: string;
  action: string;
  authentication_type: string | undefined;
}) => {
  const encryptedETag = encryption(data.recipient);
  const url = `${apiRoute.customers}${apiEndpoints.signIn}`;
  const resp = await API.post(url, data, encryptedETag);
  return resp;
};

export const verifyOTP = async (data: {
  recipient: string;
  action: string;
  verification_type: string;
  authentication_type: string;
  credential: string;
  new_password?: string;
}) => {
  const url = `${apiRoute.customers}${apiEndpoints.verify}`;
  const resp = await API.post(url, data);
  return resp;
};

export const createCustomer = async (data: {
  firstname: string;
  lastname: string;
  email: string;
  password: string;
  mobile: string;
  // custom_attributes: string;
}) => {
  const url = `${apiRoute.customers}${apiEndpoints.createUser}`;
  const resp = await API.post(url, data);
  return resp;
};

export const socialLogin = async data => {
  const url = `${apiRoute.customers}${apiEndpoints.socialLogin}`;
  const resp = await API.post(url, data);
  return resp;
};

export const updatePassword = async (data: any) => {
  const url = `${apiRoute.customers}${apiEndpoints.password}`;
  const resp = await API.put(url, data);
  return resp;
};

export const updateProfile = async (data: {
  name: string;
  password: string;
  speciality: string;
  referral_code: string;
  registration_id: string;
  registration_state: string;

}) => {
  const url = `${apiRoute.initialUpdateProfile}${apiEndpoints.profile}`;
  const resp = await API.patch(url, data);
  return resp;
};

import {Fonts} from 'common';
import {Dimensions, StyleSheet} from 'react-native';
const btnDimensions = 44;

const NetworkLoggerStyles = () =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    textContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      margin: 10,
    },
    backText: {
      fontFamily: Fonts.SemiBold,
      color: 'blue',
      fontSize: 18,
      textAlign: 'center',
    },
    buttonStyle: {
      position: 'absolute',
      right: 10,
      top: Dimensions.get('window').height / 2,
      zIndex: 99,
    },
    containerStyle: {
      width: btnDimensions,
      height: btnDimensions,
      borderRadius: btnDimensions / 2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    textInputStyle: {
      fontFamily: Fonts.SemiBold,
      color: 'white',
      fontSize: 12,
      textAlign: 'center',
    },
    versionTextStyle: {
      fontFamily: Fonts.Regular,
      color: 'white',
      fontSize: 10,
      textAlign: 'center',
    },
  });

export default NetworkLoggerStyles;

import React from 'react';
import {ScrollView, Dimensions} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {useTheme} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ReferLink = () => {
  const {colors} = useTheme();
  const boxWidth = (width - 80) / 6;
  return (
    <ScrollView>
      <ContentLoader
        speed={1}
        width={width}
        height={120}
        backgroundColor={colors.whiteSmoke}
        foregroundColor={colors.whisper}>
        <Rect x="5" y="0" rx="5" ry="5" width={width - 20} height={50} />
        <Rect x={boxWidth - 50} y="60" rx="5" ry="5" width="90" height="50" />
        <Rect x={boxWidth + 50} y="60" rx="5" ry="5" width="50" height="50" />
        <Rect
          x={boxWidth * 2 + 50}
          y="60"
          rx="5"
          ry="5"
          width="50"
          height="50"
        />
        <Rect
          x={boxWidth * 3 + 50}
          y="60"
          rx="5"
          ry="5"
          width="50"
          height="50"
        />
        <Rect
          x={boxWidth * 4 + 50}
          y="60"
          rx="5"
          ry="5"
          width="50"
          height="50"
        />
        <Rect
          x={boxWidth * 5 + 50}
          y="60"
          rx="5"
          ry="5"
          width="50"
          height="50"
        />
      </ContentLoader>
    </ScrollView>
  );
};

export default ReferLink;

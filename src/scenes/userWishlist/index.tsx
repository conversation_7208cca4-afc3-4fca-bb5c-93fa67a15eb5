import React, {useCallback, useEffect, useState, useRef} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from '../../routes';
import {<PERSON><PERSON>, Header, InputBox} from 'components/molecules';
import {
  TouchableOpacity,
  View,
  Animated,
  Dimensions,
  FlatList,
  BackHandler,
} from 'react-native';
import {
  ImageIcon,
  Label,
  Link,
  ListView,
  Separator,
  Spacer,
  Radio,
  ProductCardVertical,
} from 'components/atoms';
import ModalComponent from 'components/atoms/modalComponent';
import stylesWithOutColor from './style';
import {RouteProp, useTheme, useFocusEffect} from '@react-navigation/native';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {t} from 'i18next';
import {WishlistDetailLoader} from 'skeletonLoader';
import {useDispatch, useSelector} from 'react-redux';
import {addToCart, deleteWishlistThunk, moveWishlistItem, setLoading} from 'app-redux-store/slice/appSlice';
import {WishListFooterModal} from 'components/organisms';
import EmptyWishlist from 'components/atoms/emptyWishlist';
import LinearGradient from 'react-native-linear-gradient';
import {
  deleteProductFromWishlist,
  getFriendWishlist,
  getWishlist,
  moveToWishlist,
  updateUserWishlist,
} from 'services/wishlist';
import Clipboard from '@react-native-community/clipboard';
import {checkDevice} from 'utils/utils';
import {WEBSITE_URL} from 'config/environment';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';

const {width} = Dimensions.get('window');
const shareWishlistLink = `${WEBSITE_URL}account/wishlist/friend?id=`;

const ModalContent = ({
  modalType,
  currentWishlist,
  setModalType,
  setVisible,
  onDeleteSubmit,
  styles,
  onSubmit,
  allWishlists,
  moveWishListItem,
  showDelete = true,
  currentProduct,
}: {
  modalType:
    | 'wishlistOptions'
    | 'edit'
    | 'delete'
    | 'share'
    | 'productOptions'
    | 'shareProduct'
    | 'deleteProduct'
    | 'MoveWishListItem';
  currentWishlist: GetWishlistOutput;
}) => {
  const [values, setValues] = useState({
    wishlistName: currentWishlist?.title,
  });
  const [wishlistError, setWishlistError] = useState(false);
  let showVisibilityText =
    currentWishlist?.visibility == 'private' ? 'Public' : 'Private';

  let setVisibilityText =
    currentWishlist?.visibility == 'private' ? 'public' : 'private';

  const copyToClipboard = (textToCopy: String) => {
    Clipboard.setString(String(textToCopy));
    setVisible(false);
    showSuccessMessage(t('toastMassages.copyLinkText'));
  };

  const otherListRenderItems = useCallback(
    ({item}: {item: GetWishlistOutput}) => {
      return (
        <TouchableOpacity
          onPress={() =>
            currentWishlist?.id !== item?.id ? moveWishListItem(item) : {}
          }>
          <View>
            <View style={styles.radioView}>
              <Radio
                selected={currentWishlist?.id === item?.id}
                fillColor="black"
                borderColor="black"
                onPress={() =>
                  currentWishlist?.id !== item?.id ? moveWishListItem(item) : {}
                }
              />
              <Spacer size="xm" type="Horizontal" />
              <View style={styles.wishlistNameView}>
                <Label
                  size="mx"
                  fontFamily="Medium"
                  color="grey"
                  text={item?.title}
                  textTransform="capitalize"
                />
              </View>
            </View>
          </View>
          <Spacer size="l" />
        </TouchableOpacity>
      );
    },
    [],
  );

  const onSaveWishlist = () => {
    if (values.wishlistName.trim().length === 0) {
      setWishlistError(true);
    } else {
      setWishlistError(false);
      onSubmit(values?.wishlistName);
    }
  };

  return (
    <>
      {modalType === 'wishlistOptions' ? (
        <View>
          <Spacer size="xms" />
          <View style={styles.wishlistSubView}>
            <Label
              size="l"
              fontFamily="Medium"
              color="text"
              text={currentWishlist?.title}
              textTransform="capitalize"
            />
          </View>
          <View style={styles.currentWishlistView}>
            <Label
              size="xms"
              color="grey"
              text={currentWishlist?.visibility}
              fontFamily="Regular"
              textTransform="capitalize"
            />
            <Spacer size="xm" type="Horizontal" />
            <Label
              size="xms"
              color="grey"
              text={currentWishlist?.is_default ? '|' : ''}
            />
            <Spacer size="xm" type="Horizontal" />
            <Label
              size="xms"
              color="grey"
              text={currentWishlist?.is_default ? t('wishList.default') : ''}
              fontFamily="Regular"
              textTransform="capitalize"
            />
          </View>

          {currentWishlist?.visibility !== 'private' && (
            <>
              <Spacer size="xms" />
              <TouchableOpacity
                onPress={() => {
                  setModalType('share');
                  setVisible(true);
                }}>
                <View style={styles.footerView}>
                  <Label
                    size="mx"
                    color="text2"
                    text={t('wishList.shareText')}
                    fontFamily="Medium"
                  />
                  <ImageIcon size="xx" icon="share" tintColor="text2" />
                </View>
              </TouchableOpacity>
            </>
          )}
          <Spacer size="xms" type="Vertical" />
          <Separator style={styles.catSep} />
          <TouchableOpacity
            onPress={() => {
              setModalType('edit');
              setVisible(true);
            }}>
            <View style={styles.footerView}>
              <Label
                size="mx"
                color="text2"
                text={t('wishList.rename')}
                fontFamily="Medium"
              />
              <ImageIcon size="mx" icon="wishListEditIcon" tintColor="text2" />
            </View>
          </TouchableOpacity>
          {!currentWishlist?.is_default && <><Spacer size="xms" type="Vertical" /><Separator style={styles.catSep} /><TouchableOpacity
            onPress={() => {
              setModalType('delete');
              setVisible(true);
            } }>
            <View style={styles.footerView}>
              <Label
                size="mx"
                color="text2"
                text={t('wishList.delete')}
                fontFamily="Medium" />

              <ImageIcon size="m" icon="cancel" tintColor="text2" />
            </View>
          </TouchableOpacity></>}

          <Spacer size="xms" type="Vertical" />
          <Separator style={styles.catSep} />

          <TouchableOpacity
            onPress={() => {
              onSubmit(currentWishlist?.title, setVisibilityText);
              setVisible(false);
            }}>
            <View style={styles.footerView}>
              <Label
                size="mx"
                color="text2"
                text={`${t('wishList.make')} ${showVisibilityText}`}
                fontFamily="Medium"
              />
              <ImageIcon size="mx" icon="lock1" tintColor="text2" />
            </View>
          </TouchableOpacity>

          <Spacer size="xms" type="Vertical" />
          <Separator style={styles.catSep} />
        </View>
      ) : null}
      {modalType === 'edit' ? (
        <>
          <Spacer size="l" type="Vertical" />
          <View style={styles.footerView}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.editWishList')}
            />
          </View>
          <View style={styles.listNameView}>
            <Spacer size="xms" type="Vertical" />
            <Separator style={styles.catSep} />
            <Label
              size="m"
              fontFamily="SemiBold"
              color="categoryTitle"
              text={t('wishList.yourLastName')}
            />
            <Spacer size="xm" />
            <View style={styles.a1}>
              <InputBox
                style={styles.input}
                textInputColor={styles.modelEditInputBox}
                onChangeText={text =>
                  setValues(prev => {
                    const name = text.trimStart();
                    if (name.length > 0) {
                      setWishlistError(false);
                    }
                    return {...prev, wishlistName: name};
                  })
                }
                value={values?.wishlistName}
                keyboardType="name-phone-pad"
                maxLength={20}
              />
              <Label
                style={styles.rightText}
                text={
                  values?.wishlistName?.length == undefined || 0
                    ? '0/20'
                    : `${values?.wishlistName?.trimStart()?.length}/20`
                }
                color="grey2"
              />
            </View>
            {wishlistError && (
              <Label
                color="textError"
                text={t('validations.wishListNameRequired')}
                size="m"
                weight="400"
              />
            )}
            <Button
              labelStyle={styles.buttonText}
              type="secondary"
              text={t('buttons.confirm')}
              style={styles.modelBtn}
              onPress={() => onSaveWishlist()}
              labelSize="l"
              size="l"
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'delete' ? (
        <>
          <View style={styles.sureWantView}>
            <View style={styles.sureView}>
              <Label
                size="mx"
                color="text2"
                text={t('wishList.deleteWishListMsg')}
                fontFamily="Medium"
                align="center"
              />
            </View>
            <Spacer size="xms" type="Vertical" />
            <Button
              labelStyle={styles.buttonText}
              type="primary"
              text={t('buttons.delete')}
              style={styles.modelBtn}
              onPress={() => onDeleteSubmit(modalType, currentWishlist?.id)}
              labelSize="l"
              size="large"
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'share' ? (
        <>
          <Spacer size="xxl" type="Vertical" />
          <View style={styles.footerView}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.shareWishList')}
            />
          </View>
          <View style={styles.paddingH20}>
            <Spacer size="xms" type="Vertical" />
            <Separator style={styles.catSep} />
            <Spacer size="xxl" />
            <InputBox
              style={styles.modelInputView}
              textInputColor={styles.modelInputBox}
              editable={false}
              value={shareWishlistLink + currentWishlist?.id}
              multiline={true}
              numberOfLines={3}
            />
            <Spacer size="xms" />
            <Button
              labelStyle={styles.buttonText}
              type="primary"
              text={t('buttons.copy')}
              style={styles.modelBtn}
              onPress={() =>
                copyToClipboard(shareWishlistLink + currentWishlist?.id)
              }
              labelSize="l"
              size="large"
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'productOptions' ? (
        <>
          <Spacer size="xl" />
          {allWishlists?.length > 1 && (
            <>
              <TouchableOpacity
                onPress={() => {
                  setModalType('MoveWishListItem');
                  setVisible(true);
                }}>
                <View style={styles.footerView}>
                  <Label
                    size="mx"
                    color="text2"
                    text={t('wishList.moveAnotherWish')}
                    fontFamily="Medium"
                  />
                  <ImageIcon
                    size="l"
                    icon="moveWishListIcon"
                    tintColor="text2"
                  />
                </View>
              </TouchableOpacity>
              <Spacer size="xms" type="Vertical" />
            </>
          )}
          <Separator style={styles.catSep} />
          <TouchableOpacity
            onPress={() => {
              setModalType('shareProduct');
              setVisible(true);
            }}>
            <View style={styles.footerView}>
              <Label
                color="text2"
                text={t('wishList.shareText')}
                size="mx"
                fontFamily="Medium"
              />
              <ImageIcon size="xl" icon="share" tintColor="text2" />
            </View>
          </TouchableOpacity>
          <Spacer size="xms" type="Vertical" />
          <Separator style={styles.catSep} />
          {showDelete && (
            <>
              <TouchableOpacity
                onPress={() => {
                  setModalType('deleteProduct');
                  setVisible(true);
                }}>
                <View style={styles.footerView}>
                  <Label
                    size="mx"
                    fontFamily="Medium"
                    text={t('wishList.delete')}
                    color="text2"
                  />
                  <ImageIcon size="m" icon="cancel" tintColor="text2" />
                </View>
              </TouchableOpacity>
              <Spacer size="xms" type="Vertical" />
              <Separator style={styles.catSep} />
            </>
          )}
        </>
      ) : null}
      {modalType === 'shareProduct' ? (
        <>
          <Spacer size="xxl" type="Vertical" />
          <View style={styles.footerView}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.shareProduct')}
            />
          </View>
          <View style={styles.listNameView}>
            <Separator style={styles.catSep} />
            <Spacer size="xsl" />
            <InputBox
              style={styles.modelInputView}
              textInputColor={styles.modelInputBox}
              editable={false}
              value={`${WEBSITE_URL}${currentProduct?.url_key}.html?productSku=${currentProduct.sku}`}
              multiline={true}
              numberOfLines={3}
            />
            <Spacer size="xms" />
            <Button
              labelStyle={styles.buttonText}
              type="primary"
              text={t('buttons.copy')}
              style={styles.modelBtn}
              onPress={() =>
                copyToClipboard(
                  `${WEBSITE_URL}${currentProduct?.url_key}.html?productSku=${currentProduct.sku}`,
                )
              }
              labelSize="l"
              size="l"
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'deleteProduct' ? (
        <>
          <View style={styles.sureWantView}>
            <View style={styles.sureView}>
              <Label
                size="mx"
                color="text2"
                text={t('wishList.deleteProductMsg')}
                fontFamily="Medium"
                align="center"
              />
            </View>
            <Spacer size="xms" type="Vertical" />
            <Button
              labelStyle={styles.buttonText}
              type="primary"
              text={t('wishList.delete')}
              style={styles.modelBtn}
              onPress={() => onDeleteSubmit(modalType)}
              labelSize="l"
              size="l"
            />
            <Spacer size="xms" type="Vertical" />
          </View>
        </>
      ) : null}
      {modalType === 'MoveWishListItem' ? (
        <>
          <Spacer size="l" type="Vertical" />

          <View style={styles.footerView}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="text2"
              text={t('wishList.moveAnotherWish')}
            />
            <Spacer size="l" type="Vertical" />
          </View>
          <View style={styles.otherListRenderView}>
            <Separator style={styles.catSep} />

            <FlatList
              data={allWishlists}
              renderItem={otherListRenderItems}
              keyExtractor={(_, i) => i.toString()}
            />
            <Spacer size="x3l" type="Vertical" />
          </View>
        </>
      ) : null}
    </>
  );
};

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'UserWishlist'>;
  item: GetWishlistOutput;
};
const UserWishlistScene = ({navigation, route}: Props) => {
  const TAG = 'UserWishlistScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentWishlist, setCurrentWishlist] = useState<
    GetWishlistOutput | {[key: string]: any}
  >({});
  const [activeIndex, setActiveIndex] = useState(0);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const [allWishlists, setAllWishlists] = useState<GetWishlistOutput[] | []>(
    [],
  );
  const {wishlist_id, updateData, defaultTab, friendData} = route.params;
  const [currentProduct, setCurrentProduct] = useState();
  const [deleteProductId, setDeleteProductId] = useState([]);
  const [currentTab, setCurrentTab] = useState(defaultTab || 'My Lists');
  const [selectedProducts, setSelectedProducts] = useState([]);
  const dispatch = useDispatch();
  const [friendWishlist, setFriendWishlist] = useState<
    GetWishlistOutput[] | []
  >([]);
  let isFriendTab: any = currentTab == 'Friend';
  const [loader, setLoader] = useState(false);
  const {loading, isLoggedIn, userInfo} = useSelector(
    (state: RootState) => state.app,
  );
  const [visible, setVisible] = useState(false);
  const [isFirstTime, setIsFirstTime] = useState(true);
  const [modalType, setModalType] = useState<
    'wishlistOptions' | 'edit' | 'delete' | 'share' | 'productOptions'
  >('wishlistOptions');
  const wishlistRef = useRef(null);

  useFocusEffect(
    useCallback(() => {
      if (currentTab === 'Friend') {
        getFriendWishlistData(true);
      } else {
        wishListData(wishlist_id, true);
      }
    }, [route.params.wishlist_id]),
  );

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const productDelete = async (wishlistId, productId) => {
    dispatch(setLoading(true));
    let {data, status}: any = await deleteProductFromWishlist(
      wishlistId,
      productId,
    );
    dispatch(setLoading(false));
    setVisible(false);
    if (status && data) {
      updateData && (await updateData());
      showSuccessMessage(data?.message);
      wishListData(currentWishlist?.id);
    } else {
      showErrorMessage(data?.message);
    }
  };

  const getFriendWishlistData = async (check = false, wid = wishlist_id) => {
    setLoader(true);
    let data = await getFriendWishlist();
    if (data?.status) {
      data = data?.data?.friend_wishlists;
      setFriendWishlist(data);
      const wishlist = data?.find(e => e.id === wid);
      const selectWishlist = wishlist || data[0];
      setCurrentWishlist(selectWishlist);
      if (check) {
        const index = data?.findIndex(item => item?.id === selectWishlist?.id);
        const ITEM_WIDTH = data?.length > 0 && data[index]?.title?.length + 55;
        if (wishlistRef?.current && index !== -1) {
          setTimeout(() => {
            wishlistRef.current?.scrollToOffset({
              offset: ITEM_WIDTH * index,
              animated: true,
            });
          }, 1000);
        }
      }
    }
    setLoader(false);
  };

  const wishListData = async (wid = wishlist_id, check = false) => {
    setLoader(true);
    let wishlistData = await getWishlist();
    let data = wishlistData?.data?.wishlists;
    setLoader(false);
    if (data) {
      let wishlist = data?.find((e: any) => e.id === wid);
      const selectWishlist = wishlist || data[0];
      setCurrentWishlist(selectWishlist);
      setAllWishlists(data);
      if (check) {
        const index = data?.findIndex(item => item?.id === selectWishlist?.id);
        const ITEM_WIDTH = data?.length > 0 && data[index]?.title?.length + 55;
        if (wishlistRef?.current && index !== -1) {
          setTimeout(() => {
            wishlistRef.current?.scrollToOffset({
              offset: ITEM_WIDTH * index,
              animated: true,
            });
          }, 1000);
        }
      }
    }
  };

  const selectWishlist = async (
    currentWish: React.SetStateAction<{} | GetWishlistOutput>,
  ) => {
    setCurrentWishlist(currentWish);
  };

  //Delete Wishlist
  const deleteWishListItem = async (
    type: string,
    selectedWishlist_id: String,
  ) => {
    if (type !== 'delete') {
      productDelete(currentWishlist?.id, currentProduct?.product_id);
    } else {
      dispatch(setLoading(true));
      const {data, status} = await 
      dispatch(
        deleteWishlistThunk({
          wishlistId: selectedWishlist_id,
          isFriendWishlist: false
        })
      ).unwrap();
      setVisible(false);
      if (status && data) {
        updateData && (await updateData());
        await wishListData(currentWishlist?.id);
        setSelectedProducts([]);
        showSuccessMessage(data?.message);
      }
      dispatch(setLoading(false));
    }
  };

  //move Wishlist
  const moveWishListItem = async (targetWishlist: GetWishlistOutput) => {
    const wid = currentWishlist?.id ? currentWishlist?.id : wishlist_id;
    setVisible(false);
    dispatch(setLoading(true));
    const {data, status} = await moveToWishlist({
      source_wishlist_id: wid,
      target_wishlist_id: targetWishlist?.id,
      product_id: currentProduct?.product_id,
    });
    setModalVisible(false);
    dispatch(setLoading(false));
    if (data && status) {
      dispatch(moveWishlistItem({
        sourceWishlistId: wid,
        targetWishlistId: targetWishlist?.id,
        productId: currentProduct?.product_id
      }));
      updateData && (await updateData());
      await wishListData(wid);
      setVisible(false);
      showSuccessMessage(
        `${t('wishList.productMoved')} ${targetWishlist?.title || data?.title}`,
        'top',
      );
    } else {
      showErrorMessage(data?.message);
    }
  };

  const editWishList = async (
    name?: String,
    visibility?: any,
    type?: boolean,
    hideToast?: string,
  ) => {
    if (name?.trim().length !== 0) {
      const payloadData = {
        title: name,
        visibility: visibility || currentWishlist.visibility,
      };
      if (type === false || type === true) {
        payloadData.is_default = type;
      }
      dispatch(setLoading(true));
      const res = await updateUserWishlist(currentWishlist.id, payloadData);
      dispatch(setLoading(false));
      setVisible(false);
      if (res?.status && res?.data) {
        wishListData(currentWishlist.id);
        updateData && (await updateData());
        if (!hideToast) {
          showSuccessMessage(t('toastMassages.updateSuccess'));
        }
      }
    }
  };

  const otherRenderItems = ({
    item,
    index,
  }: {
    item: GetWishlistOutput;
    index: number;
  }) => {
    return (
      <>
        <TouchableOpacity
          key={index}
          onPress={() => {
            setModalVisible(!modalVisible);
            moveWishListItem(item);
          }}
          style={styles.continuerDirection}>
          <Label text={item?.title} />
          <ImageIcon size="l" tintColor="blackIcons" icon="nextVectorIcon" />
        </TouchableOpacity>
      </>
    );
  };

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  const otherListRenderItems = ({
    item,
    index,
  }: {
    item: GetWishlistOutput;
    index: number;
  }) => {
    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.currentWishlistButton,
          currentWishlist?.id === item?.id ? styles.bgCategory : styles.bgWhite,
        ]}
        onPress={() => {
          // Animated.spring(animatedValue, {
          //   toValue: index,
          //   friction: 1000,
          //   useNativeDriver: true,
          // }).start();
          setActiveIndex(index);
          selectWishlist(item);
        }}>
        <Label
          size="mx"
          weight="300"
          textTransform="capitalize"
          color={currentWishlist === item ? 'newSunnyOrange' : 'text'}
          text={isFriendTab ? item?.friend_name : item?.title}
          style={styles.cipLabel}
        />
      </TouchableOpacity>
    );
  };

  // const translateX = animatedValue.interpolate({
  //   inputRange: [activeIndex - 1, activeIndex, activeIndex + 1],
  //   outputRange: [width, 0, width],
  //   extrapolate: 'clamp',
  // });

  return (
    <SafeAreaView style={styles.flex}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          text={t('wishList.wishList')}
          backButton={true}
          navigation={navigation}
          searchIcon={true}
          bagIcon={true}
        />
      </ErrorHandler>
      <ErrorHandler componentName={`${TAG} TabBar`} onErrorComponent={<View />}>
        <View style={styles.fRow}>
          <Button
            type={currentTab === 'My Lists' ? 'Primary' : 'bulk'}
            style={[
              styles.tabBtn,
              currentTab === 'My Lists'
                ? {backgroundColor: colors.categoryTitle}
                : {},
            ]}
            onPress={() => {
              setCurrentTab('My Lists');
              wishListData();
            }}
            text={t('wishList.myLists')}
            labelStyle={[
              styles.tabLabel,
              {
                color:
                  currentTab === 'My Lists' ? colors.background : colors.text2,
              },
            ]}
            labelSize="l"
          />
          <Button
            type={currentTab === 'Friend' ? 'Primary' : 'bulk'}
            style={[
              styles.tabBtn,
              currentTab === 'Friend'
                ? {backgroundColor: colors.categoryTitle}
                : {},
            ]}
            onPress={() => {
              setCurrentTab('Friend');
              getFriendWishlistData();
            }}
            text={t('wishList.shareWithMe')}
            labelStyle={[
              styles.tabLabel,
              {
                color:
                  currentTab === 'Friend' ? colors.background : colors.text2,
              },
            ]}
            labelSize="l"
          />
        </View>
      </ErrorHandler>
      {loader ? (
        <WishlistDetailLoader />
      ) : (
        <View style={styles.flex}>
          <ErrorHandler
            componentName={`${TAG} Selected List`}
            onErrorComponent={<View />}>
            <View style={styles.mainMore}>
              <FlatList
                ref={wishlistRef}
                horizontal
                data={currentTab === 'Friend' ? friendWishlist : allWishlists}
                ItemSeparatorComponent={() => (
                  <Spacer type="Horizontal" size="sx" />
                )}
                renderItem={otherListRenderItems}
                keyExtractor={(_, i) => i.toString()}
                showsHorizontalScrollIndicator={false}
              />
            </View>
          </ErrorHandler>
          {/* <Animated.View style={[styles.flex, { transform: [{ translateX }] }]}> */}
          <View style={styles.flex}>
            {currentTab === 'My Lists' && currentWishlist?.title && (
              <ErrorHandler
                componentName={`${TAG} My List`}
                onErrorComponent={<View />}>
                <FlatList
                  data={['']}
                  showsVerticalScrollIndicator={false}
                  renderItem={() => (
                    <>
                      <View style={styles.continuerDirection}>
                        <View
                          style={[
                            styles.subContinuerDirac,
                            currentWishlist?.is_default && styles.center,
                          ]}>
                          <View>
                            <View style={styles.currentWishlist}>
                              <Label
                                fontFamily="Medium"
                                color="text"
                                text={currentWishlist?.title}
                                size="l"
                              />
                              <Spacer type="Horizontal" size="xms" />
                              <View style={styles.lineStyle} />
                              <Spacer type="Horizontal" size="xms" />
                              <LinearGradient
                                style={styles.gradient}
                                start={{x: 0, y: 0}}
                                end={{x: 1, y: 1}}
                                colors={
                                  currentWishlist?.visibility === 'public'
                                    ? [
                                        colors.mandarianOrange,
                                        colors.coralOrange,
                                        colors.mandarianOrange,
                                      ]
                                    : [
                                        colors.prussianBlue,
                                        colors.cobaltBlue,
                                        colors.prussianBlue,
                                      ]
                                }>
                                <ImageIcon
                                  tintColor="whiteColor"
                                  size="xms"
                                  icon={
                                    currentWishlist?.visibility === 'public'
                                      ? 'userIcon'
                                      : 'lockIcon'
                                  }
                                />
                                <Spacer type="Horizontal" size="s" />
                                <Label
                                  color="whiteColor"
                                  fontFamily="Medium"
                                  size="m"
                                  text={currentWishlist?.visibility}
                                  textTransform="capitalize"
                                  style={styles.statusStyle}
                                />
                              </LinearGradient>
                              <Spacer type="Horizontal" size="xms" />
                              <View>
                                {currentWishlist?.is_default === true ? (
                                  <Label
                                    size="xms"
                                    fontFamily="Medium"
                                    text={t('wishList.default')}
                                  />
                                ) : null}
                              </View>
                            </View>
                            <Label
                              fontFamily="Medium"
                              size="m"
                              color="text"
                              text={`${t('wishList.items')}- ${
                                currentWishlist?.items?.length
                              }`}
                              textTransform="capitalize"
                            />
                            <Spacer type="Vertical" size="xms" />
                          </View>

                          <View>
                            <TouchableOpacity
                              onPress={() => {
                                setModalType('wishlistOptions');
                                setVisible(true);
                              }}>
                              <ImageIcon icon="moreVerticalIcon" size="xxl" />
                            </TouchableOpacity>
                          </View>
                          {visible === true ? (
                            <WishListFooterModal
                              visible={visible}
                              onClose={setVisible}
                              Content={
                                <ModalContent
                                  modalType={modalType}
                                  currentWishlist={currentWishlist}
                                  setModalType={setModalType}
                                  setVisible={setVisible}
                                  onSubmit={editWishList}
                                  styles={styles}
                                  onDeleteSubmit={deleteWishListItem}
                                  allWishlists={allWishlists}
                                  moveWishListItem={moveWishListItem}
                                  currentProduct={currentProduct}
                                />
                              }
                            />
                          ) : null}
                        </View>
                      </View>
                      <Spacer type="Vertical" size="l" />
                      <View style={styles.view}>
                        <OptimizedFlatList
                          numColumns={checkDevice() ? 4 : 2}
                          data={currentWishlist?.items || []}
                          keyExtractor={(_, i) => i.toString()}
                          ItemSeparatorComponent={() => <Spacer size="m" />}
                          renderItem={({item, index}) => {
                            return (
                              <ProductCardVertical
                                index={index}
                                skuId={item?.sku}
                                actionBtn={item?.action_btn}
                                productType={item?.type}
                                maxWidth={checkDevice() ? 0.24 : 0.48}
                                item={item}
                                maxSaleQty={item?.max_sale_qty}
                                demoAvailable={item?.demo_available}
                                msrp={item?.msrp}
                                inStock={item?.is_in_stock}
                                image={item?.media?.mobile_image}
                                name={item?.name}
                                rewardPoint={item?.reward_point_product}
                                description={item?.short_description}
                                rating={(item?.rating === 'null' ||
                                item.average_rating === null
                                  ? 0
                                  : Number(item?.rating) ||
                                    Number(item?.average_rating)
                                ).toFixed(1)}
                                ratingCount={
                                  !!item?.rating_count
                                    ? `(${item?.rating_count})`
                                    : '(0)'
                                }
                                price={item?.price}
                                sellingPrice={item?.selling_price}
                                currencySymbol={item?.currency_symbol}
                                discount={item?.discount?.label}
                                onPress={() => {
                                  navigation.setParams({ 
                                    ...route.params, 
                                    wishlist_id: currentWishlist.id 
                                  });
                                  navigation.navigate('ProductDetail', {
                                    productId: item?.product_id,
                                    ProductItems: item,
                                  });
                                }}
                                navigation={navigation}
                                topRightComponent={
                                  <TouchableOpacity
                                    onPress={() => {
                                      setModalType('productOptions');
                                      setVisible(true);
                                      setDeleteProductId(item.product_id);
                                      setCurrentProduct(item);
                                    }}
                                    style={styles.topRightView}>
                                    <ImageIcon
                                      icon="moreVerticalIcon"
                                      size="xx"
                                    />
                                  </TouchableOpacity>
                                }
                              />
                            );
                          }}
                          onEndReachedThreshold={0.8}
                          ListEmptyComponent={
                            !loading ? (
                              <EmptyWishlist
                                text={t('wishList.addProducts')}
                                // onCall={() => {
                                //   if (!currentWishlist?.is_default) {
                                //     editWishList(
                                //       currentWishlist?.title,
                                //       null,
                                //       true,
                                //       true,
                                //     );
                                //   }
                                // }}
                                btnShow={true}
                              />
                            ) : null
                          }
                        />
                      </View>
                      <Spacer type="Vertical" size="xl" />
                    </>
                  )}
                />
              </ErrorHandler>
            )}

            {currentTab === 'Friend' && (
              <ErrorHandler
                componentName={`${TAG} Friend List`}
                onErrorComponent={<View />}>
                <FlatList
                  data={['']}
                  showsVerticalScrollIndicator={false}
                  renderItem={() => (
                    <>
                      <View style={styles.continuerDirection}>
                        <View
                          style={[
                            styles.subContinuerDirac,
                            currentWishlist?.is_default && styles.center,
                          ]}>
                          {currentWishlist?.friend_name && (
                            <View>
                              <View style={styles.currentWishlist}>
                                <Label
                                  fontFamily="Medium"
                                  color="text"
                                  text={currentWishlist?.friend_name}
                                  size="l"
                                />
                              </View>
                              <Label
                                fontFamily="Medium"
                                size="m"
                                color="text"
                                text={`${t('wishList.items')}- ${
                                  currentWishlist?.wishlist?.items?.length
                                }`}
                                textTransform="capitalize"
                              />
                              <Spacer type="Vertical" size="xms" />
                            </View>
                          )}
                          {visible === true ? (
                            <WishListFooterModal
                              visible={visible}
                              onClose={setVisible}
                              Content={
                                <ModalContent
                                  modalType={modalType}
                                  currentWishlist={currentWishlist}
                                  setModalType={setModalType}
                                  setVisible={setVisible}
                                  editWishList={editWishList}
                                  onSubmit={editWishList}
                                  styles={styles}
                                  onDeleteSubmit={deleteWishListItem}
                                  allWishlists={allWishlists}
                                  moveWishListItem={moveWishListItem}
                                  showDelete={false}
                                  currentProduct={currentProduct}
                                />
                              }
                            />
                          ) : null}
                        </View>
                      </View>
                      <Spacer type="Vertical" size="l" />
                      <View style={styles.view}>
                        <OptimizedFlatList
                          numColumns={2}
                          data={currentWishlist?.wishlist?.items || []}
                          keyExtractor={(_, i) => i.toString()}
                          ItemSeparatorComponent={() => <Spacer size="m" />}
                          renderItem={({
                            item,
                            index,
                          }: {
                            item: simpleProductData;
                            index: number;
                          }) => {
                            return (
                              <ProductCardVertical
                                index={index}
                                skuId={item?.sku}
                                productType={item?.type}
                                actionBtn={item?.action_btn}
                                maxWidth={0.49}
                                item={item}
                                inStock={item.is_in_stock}
                                maxSaleQty={item?.max_sale_qty}
                                demoAvailable={item?.demo_available}
                                msrp={item?.msrp}
                                image={item?.media?.mobile_image}
                                name={item?.name}
                                rewardPoint={item?.reward_point_product}
                                description={item?.short_description}
                                rating={(item?.rating === 'null' ||
                                item.average_rating === null
                                  ? 0
                                  : Number(item?.rating) ||
                                    Number(item?.average_rating)
                                ).toFixed(1)}
                                ratingCount={
                                  !!item?.rating_count
                                    ? `(${item?.rating_count})`
                                    : '(0)'
                                }
                                price={item?.price}
                                sellingPrice={item?.selling_price}
                                currencySymbol={item?.currency_symbol}
                                discount={item?.discount?.label}
                                onPress={() => {
                                  navigation.setParams({ 
                                    ...route.params, 
                                    wishlist_id: currentWishlist.id 
                                  });
                                  navigation.navigate('ProductDetail', {
                                    productId: item?.product_id,
                                    ProductItems: item,
                                  });
                                }}
                                navigation={navigation}
                              />
                            );
                          }}
                          onEndReachedThreshold={0.8}
                          ListEmptyComponent={
                            !loading ? (
                              <EmptyWishlist
                                title={t('wishList.noShareWishlist')}
                                hideIcon={true}
                              />
                            ) : null
                          }
                        />
                      </View>
                      <Spacer type="Vertical" size="xl" />
                    </>
                  )}
                />
              </ErrorHandler>
            )}
          </View>
        </View>
      )}
      {modalVisible && (
        <ModalComponent
          modelStyle={styles.modalComponent}
          visible={modalVisible}
          onClose={() => setModalVisible(!modalVisible)}>
          <View style={styles.listBg}>
            <Spacer type="Vertical" size="s" />
            <Link
              onPress={() => {
                setModalVisible(!modalVisible);
              }}
              text={t('wishList.wishList')}
            />
            <Spacer type="Vertical" size="xs" />
            <Separator style={styles.separator} />
            <Spacer type="Vertical" size="xm" />
            <ErrorHandler
              componentName={`${TAG} ModalComponent AllWishlists`}
              onErrorComponent={<View />}>
              <ListView
                keyExtractor={listViewKeyExtractor}
                data={allWishlists}
                renderItem={otherRenderItems}
                ItemSeparatorComponent={<Spacer type="Vertical" size="xm" />}
              />
            </ErrorHandler>
          </View>
        </ModalComponent>
      )}
    </SafeAreaView>
  );
};

export default UserWishlistScene;

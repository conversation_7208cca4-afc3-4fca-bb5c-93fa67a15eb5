import {useMemo} from 'react';

import React, {useCallback, useState} from 'react';
import {Pressable, View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import DashedLine from 'react-native-dashed-line';
import {t} from 'i18next';
import {
  ImageIcon,
  Label,
  ListView,
  Spacer,
  CheckBox,
  Separator,
} from 'components/atoms';
import stylesWithOutColor from './style';
import DeliveryInfoModal from '../deliveryInfoModal';
import {mDevice, sDevice} from 'utils/utils';

type Props = {
  cartData?: Cart;
  priceDetails?: PricingDetails;
  onSetWeight?: () => void;
  agreePolicy?: boolean;
  termsClick?: () => void;
  onCheckBoxClick?: () => void;
  selectTerms?: boolean;
  onCouponClick?: () => void;
};

const CartPriceSection = (props: Props) => {
  const {
    cartData,
    priceDetails,
    onSetWeight,
    agreePolicy,
    termsClick,
    onCheckBoxClick,
    selectTerms,
    onCouponClick,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [infoModel, setInfoModel] = useState(false);
  const totalSaving = cartData?.pricing_details?.total_savings;
  const renderItem = useCallback(({item, index}: any) => {
    const gTotal = item?.key === 'Grand Total';
    return item?.visibility ? (
      <>
        {item?.borderTop ? (
          <View style={styles.priceRow}>
            <Spacer size="xm" />
            <DashedLine
              dashLength={4}
              dashThickness={1.5}
              dashColor={colors.grey2}
            />
            <Spacer size="xm" />
          </View>
        ) : null}
        <View key={index} style={styles.amountList}>
          <View style={styles.rowWarpView}>
            <Label
              size={gTotal ? 'l' : sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
              text={item?.key}
              weight={item?.keyWeight}
              style={item?.savedAmount && styles.titleStyle}
              color={item?.keyColor}>
              {item?.modal === true ? (
                <>
                  <Label
                    text={` (${t('cart.viewDetails')})`}
                    color="text2"
                    size={sDevice ? 'xm' : mDevice ? 'xms' : 'm'}
                    fontFamily="Regular"
                    textDecorationLine="underline"
                    onPress={() => onSetWeight && onSetWeight()}
                  />
                </>
              ) : (
                <>
                  {item?.subTitle ? (
                    <>
                      <Label
                        color="text2"
                        size={sDevice ? 'xm' : mDevice ? 'xms' : 'm'}
                        fontFamily="Regular"
                        text={` (${item?.subTitle})`}
                      />
                    </>
                  ) : null}
                </>
              )}
            </Label>
            {item?.savedAmount ? (
              <View style={styles.savedStyle}>
                <Label
                  size={sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
                  fontFamily="Medium"
                  color="green2"
                  text={item?.savedAmount}
                />
              </View>
            ) : (
              <View />
            )}
            {(item?.deliveryValue === 0 || item?.deliveryValue) && (
              <Pressable onPress={() => setInfoModel(!infoModel)}>
                <ImageIcon size="xxl" icon="infoCircle" tintColor="text2" />
              </Pressable>
            )}
          </View>
          <Pressable
            onPress={() =>
              item?.value === 'Apply Coupon' ? onCouponClick() : {}
            }>
            <View style={styles.rowView}>
              {item?.regularValue ? (
                <>
                  <Label
                    fontFamily="Medium"
                    size={sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
                    color="grey3"
                    textDecorationLine="line-through"
                    text={item?.currency + item?.regularValue}
                  />
                  <Spacer size="xm" type="Horizontal" />
                </>
              ) : (
                <View />
              )}
              {item?.deliveryValue !== 0 ? (
                <Label
                  size={sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
                  weight={item?.regularValue ? '600' : item?.valueWeight}
                  color={item?.regularValue ? 'text' : item?.valueColor}
                  text={item?.currency + ' ' + item?.value}
                  textDecorationLine={
                    item?.deliveryValue === 0 ? 'line-through' : 'none'
                  }
                />
              ) : (
                <View />
              )}
              {item?.deliveryValue === 0 ? (
                <>
                  {item?.value !== 0 && (
                    <>
                        <Label
                        fontFamily="Medium"
                        size={sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
                        color="grey3"
                        textDecorationLine="line-through"
                        text={item?.currency + item?.value}
                      />
                      <Spacer size="s" type="Horizontal" />
                      <Separator
                        height="mx"
                        Vertical
                        thickness="x"
                        color={item?.valueColor}
                      />
                      <Spacer size="s" type="Horizontal" />
                    </>
                  )}
                  <Label
                    fontFamily="Medium"
                    size={sDevice ? 'xms' : mDevice ? 'm' : 'mx'}
                    color="text"
                    text={t('cart.free')}
                    textTransform="uppercase"
                  />
                </>
              ) : (
                <View />
              )}
            </View>
          </Pressable>
        </View>
        {item?.borderBottom ? (
          <View style={styles.priceRow}>
            <Spacer size="xm" />
            <DashedLine
              dashLength={4}
              dashThickness={1.5}
              dashColor={colors.grey2}
            />
          </View>
        ) : null}
      </>
    ) : null;
  }, []);

  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );

  return (
    <View style={styles.priceDetailView}>
      <View style={styles.priceTitleView}>
        <Label
          text={t('cart.priceDetails')}
          size="l"
          fontFamily="Medium"
          color="text"
          style={styles.weight}
        />
        <Spacer size="l" />
        <DashedLine
          dashLength={4}
          dashThickness={1.5}
          dashColor={colors.grey2}
        />
      </View>
      <ListView
        keyExtractor={listViewKeyExtractor}
        style={styles.paddingSpace}
        data={Object.values(priceDetails)}
        renderItem={renderItem}
      />
      {agreePolicy ? (
        <>
          <TouchableOpacity
            testID='tOTermsClick'
            activeOpacity={1}
            onPress={termsClick}
            style={styles.conditionView}>
            <CheckBox onValueChange={onCheckBoxClick} selected={selectTerms} />
            <Spacer size="xm" type="Horizontal" />
            <Label
              text={t('checkOut.read')}
              color="text2"
              size="mx"
              weight="600"
              style={styles.flex}
              textTransform="capitalize">
              <Label
                onPress={termsClick}
                weight="600"
                size="mx"
                color="text2"
                text={t('checkOut.terms')}
              />
            </Label>
          </TouchableOpacity>
          <Spacer size="xms" />
          <View style={styles.priceRow}>
            <DashedLine
              dashLength={4}
              dashThickness={1.5}
              dashColor={colors.grey2}
            />
          </View>
          <Spacer size="xms" />
        </>
      ) : (
        <View />
      )}
      <View style={styles.priceRow}>
        <View style={styles.rowView}>
          <Label
            text={t('cart.earn')}
            size="m"
            weight="600"
            color="text2"
            textTransform="capitalize"
          />
          <Spacer size="s" type="Horizontal" />
          <ImageIcon icon="coin" size="xms" />
          <Spacer size="s" type="Horizontal" />
          <Label
            text={`${cartData?.rewards?.total_coins} `}
            color="yellow"
            size="m"
            weight="600">
            <Label
              size="m"
              weight="600"
              color="text2"
              text={t('cart.pointText')}
            />
          </Label>
        </View>
        <Label
          size="m"
          fontFamily="Medium"
          color="grey"
          textTransform="capitalize"
          text={t('cart.orderDelivery')}
        />
      </View>
      <Spacer size={totalSaving?.amount?.value ? 'l' : 'm'} />
      {totalSaving?.amount?.value ? (
        <View style={styles.savingView}>
          <View style={styles.flex}>
            <Label
              size="mx"
              fontFamily="SemiBold"
              color="green2"
              text={t('cart.yourTotalSaving')}
              style={styles.weight}
            />
            {totalSaving?.amount?.msg ? (
              <Label
                size="m"
                fontFamily="Medium"
                color="green2"
                text={totalSaving?.amount?.msg}
              />
            ) : (
              <View />
            )}
          </View>
          <Spacer size="s" />
          <Label
            size="mx"
            fontFamily="Medium"
            color="green2"
            textTransform="capitalize"
            text={
              cartData?.cart_currency?.currency_symbol +
              totalSaving?.amount?.value
            }
          />
        </View>
      ) : (
        <View />
      )}
      {infoModel && (
        <DeliveryInfoModal
          visible={infoModel}
          onClose={() => setInfoModel(false)}
          infoIcon="delivery"
        />
      )}
    </View>
  );
};

export default React.memo(CartPriceSection);

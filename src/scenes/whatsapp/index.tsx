import React from 'react';
import {TouchableOpacity} from 'react-native';
import {ImageIcon} from 'components/atoms';
import {useSelector} from 'react-redux';
import {openWhatsApp} from 'utils/utils';

const WhatsappScene = () => {
  const {whatsAppLink} = useSelector((state: RootState) => state.app);

  return (
    <TouchableOpacity onPress={() => openWhatsApp(whatsAppLink?.app_link)}>
      <ImageIcon resizeMode="contain" size="xxxl" icon="whatsAppIcon" />
    </TouchableOpacity>
  );
};

export default WhatsappScene;

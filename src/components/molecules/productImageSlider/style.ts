import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    simillerProductView: {
      alignSelf: 'flex-end',
      paddingBottom: Sizes.xxl,
    },
    simillerProductSubView: {
      marginRight: Sizes.s,
      justifyContent: 'flex-end',
      borderRadius: Sizes.x8l,
      backgroundColor: colors.heartBG,
    },
    similartImage: {height: Sizes.x6l, width: Sizes.x6l},
    imageIcon: {
      padding: Sizes.sx,
      borderRadius: Sizes.x8l,
      backgroundColor: colors.heartBG,
      alignSelf: 'center',
    },
    deliveryView: {
      position: 'absolute',
      zIndex: Sizes.xs,
      alignItems: 'flex-end',
      width: '100%',
      justifyContent: 'space-between',
      bottom: Sizes.screenHeight * 0.0,
      padding: Sizes.xms,
      pointerEvents: 'box-none', // 👈 Fix: Allows touches to pass through non-touchable areas
    },
    tagView: {
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.s,
    },
    tagLabel: {
      color: colors.text2,
    },
    ratingMainView: {
      bottom: Sizes.mx,
      alignItems: 'flex-end',
      flex: Sizes.x,
    },
    scrollToView: {
      borderWidth: Sizes.x,
      alignItems: 'center',
      borderColor: colors.grey2,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.xs,
      borderRadius: Sizes.xm,
      backgroundColor: 'rgba(248, 242, 242, 0.35)',
    },
    scrollToSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingIcon: {marginBottom: Sizes.s, width: Sizes.xxxl, height: Sizes.xxxl},
    scrollToMainView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    visibleFullScreenView: {
      borderBottomLeftRadius: Sizes.l,
      borderBottomRightRadius: Sizes.l,
    },
    fullScreenImgView: {
      position: 'absolute',
      zIndex: Sizes.x,
      flexDirection: 'row',
      width: '100%',
      paddingTop: Sizes.m,
      justifyContent: 'space-between',
    },
    bestSellerTag: {
      height: Sizes.x3l,
      width: Sizes.exl,
    },
    iconView: {
      paddingRight: Sizes.m,
    },
    visibleSubScreenView: {
      maxHeight: checkDevice()
        ? Sizes.screenHeight * 0.9
        : Sizes.ex4l + Sizes.x3l,
      height: '100%',
      width: '100%',
      borderBottomLeftRadius: Sizes.mx,
      borderBottomRightRadius: Sizes.mx,
    },
    placeholderWrapper: {
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      height: '100%',
      paddingHorizontal: Sizes.x4l,
    },
    placeholderImage: {
      width: '80%',
      height: '80%',
      tintColor: '#ccc',
    },
    flexContainer: {
      flex: Sizes.x,
    },
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    modalCloseBtnContainer: {
      flex: Sizes.s,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    offerZoneBannerView: {
      backgroundColor: colors.background,

      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
      flex: Sizes.x,
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
    },
    viewProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: 3.6,
    },
    excludeView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    productView: {
      paddingVertical: Sizes.xm,
    },
    excludeMainView: {
      transform: [{rotate: '180deg'}],
    },

    videoContainer: {
      borderBottomLeftRadius: Sizes.mx,
      borderBottomRightRadius: Sizes.mx,
      marginVertical: Sizes.ex
    },

    fullScreenModalContainer: {
      flex: Sizes.x,
      backgroundColor: colors.black,
    },
    closeButton: {
      position: 'absolute',
      top: Sizes.x6l,
      right: Sizes.xl,
    },
    closeButtonText: {
      color: colors.background,
      fontSize: Sizes.xx,
      fontWeight: 'bold',
    },
    carouselContainer: {
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      height:Sizes.s,
    },
    paginationContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: Sizes.s,
      height: Sizes.xm,
    },
    paginationDot: {
      width: Sizes.sx,
      height: Sizes.sx,
      borderRadius: Sizes.sx / 2,
      backgroundColor: colors.categoryTitle,
      marginHorizontal: Sizes.xs,
      opacity: 0.5,
    },
    paginationDotActive: {
      backgroundColor: colors.categoryTitle,
      width: Sizes.xl,
      opacity: 1,
    },
  });
export default styles;

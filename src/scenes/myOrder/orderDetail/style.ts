import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.grey7,
    },
    tagStyle: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xs,
      borderColor: colors.textError,
    },
    processingContinuer: {
      flex: Sizes.x,
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    shipmentView: {
      backgroundColor: colors.quartz,
      height: Sizes.x26,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: Sizes.s,
      borderRadius: Sizes.s,
    },
    textColor: {
      color: colors.textError,
    },
    detailBox: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
    },
    retryPaymentBtn: {
      textAlign: 'center',
    },
    renderCards: {
      padding: Sizes.xms,
      borderRadius: Sizes.xm,
      backgroundColor: colors.whiteColor,
    },
    subRenderCards: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    codTag: {
      width: Sizes.ex122,
      height: Sizes.xl,
    },
    onlineTag: {
      width: Sizes.ex92,
      height: Sizes.xl,
    },
    mainView: {
      backgroundColor: colors.grey7,
      paddingHorizontal: Sizes.xms,
    },
    detailMainView: {
      backgroundColor: colors.background,
      padding: Sizes.m,
      borderRadius: Sizes.mx,
    },
    flexOne: {
      flex: Sizes.x,
    },
    lineStyle: {
      backgroundColor: colors.grey2,
      height: Sizes.xs,
      marginVertical: Sizes.xm,
    },
    successTitle: {
      fontSize: Sizes.xl,
    },
    statusView: {
      flexDirection: 'row',
      flex: Sizes.xs,
      justifyContent: 'flex-end',
    },
    noteView: {
      marginTop: Sizes.m,
      padding: Sizes.xms,
      borderRadius: Sizes.xm,
      backgroundColor: colors.whiteColor,
    },
    items: {
      flex: Sizes.x,
      alignItems: 'center',
    },
    paymentMsg: {
      width: '60%',
    },
    paymentBtnView: {
      width: '35%',
    },
    rateView: {
      padding: Sizes.xm,
      borderRadius: Sizes.xm,
      backgroundColor: colors.whiteColor,
      flexDirection: 'row',
      alignItems: 'center',
    },
    starView: {
      backgroundColor: colors.background3,
      width: Sizes.xx4l,
      height: Sizes.xx4l,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xms,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    btnLeftIcon: {
      // paddingLeft: Sizes.s,
      marginRight: Sizes.xm,
    },
    btnStyle: {
      height: Sizes.xx4l,
    },
    returnStatusView: {
      width: '30%',
    },
    orderStatusView: {
      width: '30%',
      alignItems: 'flex-end',
    },
  });

export default styles;

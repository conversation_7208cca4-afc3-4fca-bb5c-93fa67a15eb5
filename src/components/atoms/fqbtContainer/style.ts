import {Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    feedbackMainView: {
      borderWidth: Sizes.x,
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.m,
      borderColor: colors.grey5,
      borderRadius: Sizes.m,
      backgroundColor: colors.background,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: Sizes.xs + Sizes.x,
    },
    hideAddToCartView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    buyBothView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xl,
      alignItems: 'center',
    },
    subProductNameView: {paddingHorizontal: Sizes.m},
    alignmentCenter: {alignItems: 'center', justifyContent: 'center'},
    infoBottomIcon: {marginBottom: Sizes.xs},
    fqbtBtn: {paddingHorizontal: Sizes.l, paddingBottom: Sizes.l},
    fqbtIcon: {
      right: Sizes.xx4l,
      alignSelf: 'flex-start',
    },
    buttonWidth: {maxWidth: '90%'},
  });

export default styles;

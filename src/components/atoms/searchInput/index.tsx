import React, {useCallback, useState, memo, useRef, useEffect} from 'react';
import {TextInput, TouchableOpacity, View, ViewStyle} from 'react-native';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {
  Spacer,
  Separator,
  ImageIcon,
  Label,
  RollingText,
} from 'components/atoms';
import {useMemo} from 'react';
import { Easing, useSharedValue, withTiming } from 'react-native-reanimated';

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};

type Props = {
  onChangeText: (text: string) => void;
  onPressIn?: () => void;
  onBackPress?: () => void;
  isStatic?: boolean;
  searchPage?: boolean;
  placeHolder?: string;
  withDebounce?: boolean;
  value?: string | undefined;
  autoFocus?: boolean;
  inputStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  editable?: boolean;
  // placeHolderText?: boolean;
};

const SearchInput = ({
  onChangeText,
  onPressIn,
  isStatic,
  onBackPress,
  searchPage,
  placeHolder,
  withDebounce = false,
  value,
  autoFocus,
  inputStyle,
  editable = true,
  containerStyle,
  // placeHolderText = false,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [text, setText] = useState(value);
  const debouncedTextChange = debounce(onChangeText, 500);
  // const [displayedSearchText, setDisplayedSearchText] = useState('');
  // const isTyping = useRef(true);
  // const typingIndex = useSharedValue(0);

  const handleTextChange = useCallback(
    (textValue: string) => {
      const trimmedValue = textValue.replace(/\s+/g, ' ');
      setText(trimmedValue);
      if (withDebounce) {
        debouncedTextChange(trimmedValue);
      } else {
        onChangeText(trimmedValue);
      }
    },
    [withDebounce],
  );

    // useEffect(() => {
    //     let title = t('homePage.searchProducts')
    //     const typeCharacter = () => {
    
    //       if (isTyping.current) {
    //         setDisplayedSearchText(title.slice(0, Math.round(typingIndex.value)));
    
    //         typingIndex.value = withTiming(typingIndex.value + 1, {
    //           duration: 0, 
    //           easing: Easing.linear,
    //         });
    
    //         if (Math.round(typingIndex.value) > title.length) {
    //           isTyping.current = false;
    //           setTimeout(() => {
    //             typingIndex.value = withTiming(0, { duration: 0 });
    //             isTyping.current = true;
    //           }, 1000);
    //         }
    //       }
    //     };
    
    //     const intervalId = setInterval(typeCharacter, 0);
    
    //     return () => clearInterval(intervalId);
    // }, [displayedSearchText]);

  return (
    <View
      style={[
        styles.searchBarView,
        searchPage ? styles.greyBackground : {},
        containerStyle,
      ]}>
      <View style={styles.searchBar}>
        {searchPage ? (
          <TouchableOpacity style={styles.searchIconView} onPress={onBackPress}>
            <Spacer type="Horizontal" size="xm" />
            <ImageIcon icon="arrowLeft" size="xxl" />
            <Spacer type="Horizontal" size="xs" />
          </TouchableOpacity>
        ) : null}
        <View style={[isStatic ? styles.searchView : styles.fOne, inputStyle]}>
          {isStatic ? (
            <View style={styles.staticView}>
              <Label
                text={placeHolder ? placeHolder : 'Search'}
                size="l"
                fontFamily="Medium"
                color="text2"
                lineHeight="xsl"
              />
              <Spacer size="sx" type="Horizontal" />
              <RollingText />
            </View>
          ) : (
            <TextInput 
            testID="txtSearchProducts"
              style={styles.searchSubView}
              // placeholder={
              //   placeHolder ? placeHolder : placeHolderText ? displayedSearchText : t('searchProduct.searchProducts')
              // }
              placeholder={
                placeHolder ? placeHolder  : t('searchProduct.searchProducts')
              }
              onChangeText={handleTextChange}
              onPressIn={onPressIn}
              placeholderTextColor={colors.text2}
              selectionColor={colors.text2}
              value={text}
              autoFocus={autoFocus}
              editable={editable}
              allowFontScaling={false}
            />
          )}
        </View>

        <View style={styles.searchIconView}>
          <TouchableOpacity onPressIn={onPressIn}>
            {/* {searchPage ? (
              <ImageIcon icon="search" size="xxl" tintColor="text" />
            ) : ( */}
            <>
              {!text ? (
                <ImageIcon icon="search" size="xxl" tintColor="text" />
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    onChangeText(''), setText('');
                  }}
                  style={styles.searchCancelIcon}
                  >
                  <ImageIcon icon="crossIcon" size="xl" />
                </TouchableOpacity>
              )}
            </>
            {/* )} */}
          </TouchableOpacity>
          <Spacer type="Horizontal" size="m" />
          {/* <Spacer type="Horizontal" size="xm" />
          <Separator Vertical height="xl" />
          <Spacer type="Horizontal" size="xm" />
          <TouchableOpacity>
            <ImageIcon tintColor="black" size="xxl" icon={'microPhone'} />
          </TouchableOpacity> */}
        </View>
      </View>
    </View>
  );
};

export default memo(SearchInput);

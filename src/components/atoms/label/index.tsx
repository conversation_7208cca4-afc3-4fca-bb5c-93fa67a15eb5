import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {Text, TextStyle, TextProps} from 'react-native';
import {Sizes, Fonts} from 'common';
import {useTheme} from '@react-navigation/native';

export type Props = {
  size?: keyof typeof Sizes;
  color?: keyof Theme['colors'];
  onPress?: () => void;
  weight?: TextStyle['fontWeight'];
  allowFontScaling?: TextProps['allowFontScaling'];
  fontFamily?: keyof typeof Fonts;
  style?: TextProps['style'];
  textTransform?:TextStyle['textTransform'];
  index?: number;
  letterSpacing?: TextStyle['letterSpacing'];
  numberOfLines?: TextProps['numberOfLines'];
  ellipsizeMode?: TextProps['ellipsizeMode'];
  textDecorationLine?: TextStyle['textDecorationLine'];
  lineHeight?: keyof typeof Sizes;
  children?: React.ReactElement;
} & LabelType;

const Label = ({
  text,
  children,
  size = 'l',
  textTransform,
  color = 'textLight',
  weight = 'normal',
  fontStyle = 'normal',
  fontFamily,
  style,
  align,
  letterSpacing = 0,
  numberOfLines,
  ellipsizeMode,
  textDecorationLine,
  index,
  onPress,
  allowFontScaling = false,
  lineHeight, //= 'l',
  ...other
}: Props) => {
  const {colors}: {colors: Theme['colors']} = useTheme();
  const fontFamilyNew = !fontFamily
    ? weight === '500'
      ? 'Medium'
      : weight === '400'
      ? 'Regular'
      : weight === '600'
      ? 'SemiBold'
      : weight === '700'
      ? 'Bold'
      : 'Regular'
    : fontFamily;

  return (
    <Text
      onPress={onPress}
      allowFontScaling={allowFontScaling}
      key={index}
      ellipsizeMode={ellipsizeMode}
      numberOfLines={numberOfLines}
      style={[
        {
          fontSize: Sizes[size],
          textTransform: textTransform,
          color: colors[color],
          fontWeight: weight,
          fontFamily: Fonts[fontFamilyNew],
          fontStyle: fontStyle,
          textAlign: align,
          letterSpacing: letterSpacing,
          textDecorationLine: textDecorationLine,
        },
        lineHeight ? {lineHeight: Sizes[lineHeight]} : undefined,
        style,
      ]}
      {...other}>
      {text}
      {children}
    </Text>
  );
};

export default Label;

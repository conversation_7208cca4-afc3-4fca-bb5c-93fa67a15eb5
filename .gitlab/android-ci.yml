build:
  stage: build
  image: reactnativecommunity/react-native-android:latest
  before_script:
    - yarn install
    - cd android
    - gem install bundler
    - bundle install
  script:
    - |
      if [ "$CI_COMMIT_REF_NAME" = "staging" ]; then
        bundle exec fastlane deploy_production_firebase
      elif [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        bundle exec fastlane deploy_production
      elif [ "$CI_COMMIT_REF_NAME" = "hotfix_release" ]; then
        bundle exec fastlane deploy_production_firebase        
      fi
  artifacts:
    paths:
      - android/app/build/outputs/bundle/*/*.aab
      - android/app/build/outputs/apk/*/*.apk      
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_REF_NAME == "hotfix_release" && $CI_PIPELINE_SOURCE == "push"'
      when: always  
    - if: '$CI_COMMIT_REF_NAME == "staging" && $CI_PIPELINE_SOURCE == "push"'
      when: always
    - if: '$CI_COMMIT_REF_NAME == "main" && $CI_PIPELINE_SOURCE == "push"'
      when: always
    - when: never


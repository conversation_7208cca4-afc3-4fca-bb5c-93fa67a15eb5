import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    modalView: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.blackTransparent,
    },
    mainView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.l,
      padding: Sizes.xms,
      paddingHorizontal: 50,
    },
    centerView: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    rateImg: {
      width: Sizes.x54,
      height: Sizes.x54,
    },
  });

export default styles;

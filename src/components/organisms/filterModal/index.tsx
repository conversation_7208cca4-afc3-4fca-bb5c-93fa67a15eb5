import React, {useCallback, useEffect, useState} from 'react';
import {View, TouchableOpacity, SafeAreaView, ScrollView} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import Modal from 'react-native-modal';
import {CheckBox, ImageIcon, Label, Spacer} from 'components/atoms';
import {disableFragmentWarnings} from 'graphql-tag';
import {t} from 'i18next';
import {TouchableWithoutFeedback} from 'react-native';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose?: () => void;
  modelVisible: boolean;
  filters: Object | any;
  onApplyFilters: (filters: any) => void;
  publishers: Object | any;
  appliedFilters: Object | any;
};

const FilterModal = ({
  visible,
  onClose,
  appliedFilters,
  filters,
  onApplyFilters,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const [localFilters, setLocalFilters] = useState({
    magazine_name: '',
    publisher: [],
    year: [],
    month: [],
  });
  const [selectedKey, setSelectedKey] = useState<null | object>('');
  const [selectedItems, setSelectedItems] = useState({});
  const [isSelectAll, setIsSelectAll] = useState(false);

  const handleApplyFilter = () => {
    onApplyFilters(localFilters);
  };

  const areFiltersApplied = () => {
    return Object.values(localFilters).some(
      filterArray => filterArray.length > 0,
    );
  };

  const handleClearAll = () => {
    const clearedFilters = {
      magazine_name: '',
      publisher: [],
      year: [],
      month: [],
    };
    setLocalFilters(clearedFilters);
    setSelectedItems({});
    setIsSelectAll(false);
    onApplyFilters(clearedFilters);
  };

  const handleSelectAll = () => {
    if (isSelectAll) {
      setSelectedItems(prev => {
        const newSelectedItems = {...prev};
        newSelectedItems[selectedKey || ''] = [];
        setLocalFilters(prevFilters => ({
          ...prevFilters,
          [selectedKey || '']: [],
        }));
        return newSelectedItems;
      });
    } else {
      setSelectedItems(prev => {
        const newSelectedItems = {...prev};
        const allValues =
          filters[selectedKey || '']?.map(
            item => item.value || item.publisher,
          ) || [];
        newSelectedItems[selectedKey || ''] = allValues;
        setLocalFilters(prevFilters => ({
          ...prevFilters,
          [selectedKey || '']: allValues,
        }));
        return newSelectedItems;
      });
    }
    setIsSelectAll(!isSelectAll);
  };

  const handleCheckBoxChange = useCallback(
    item => {
      const itemValue = item?.value || item?.publisher;
      setSelectedItems(prev => {
        const newSelectedItems = {...prev};
        if (newSelectedItems[selectedKey || '']?.includes(itemValue)) {
          newSelectedItems[selectedKey || ''] = newSelectedItems[
            selectedKey || ''
          ].filter(i => i !== itemValue);
        } else {
          newSelectedItems[selectedKey || ''] = [
            ...(newSelectedItems[selectedKey || ''] || []),
            itemValue,
          ];
        }
        setLocalFilters(prevFilters => ({
          ...prevFilters,
          [selectedKey || '']: newSelectedItems[selectedKey || ''] || [],
        }));
        return newSelectedItems;
      });
    },
    [selectedKey],
  );

  const isSelected = value => {
    if (!selectedKey) return false;
    return (
      localFilters[selectedKey]?.includes(value.value || value.publisher) ||
      false
    );
  };

  useEffect(() => {
    setSelectedKey(Object.keys(filters)?.[0]);
  }, []);

  useEffect(() => {
    setLocalFilters(appliedFilters);
  }, [appliedFilters]);

  return (
    <SafeAreaView style={styles.flex}>
      <Modal
        onBackButtonPress={onClose}
        isVisible={visible}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={75}
        animationOutTiming={75}
        backdropOpacity={0.01}
        style={styles.modalStyle}>
        <View style={styles.centeredView}>
          <TouchableWithoutFeedback onPress={onClose}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={onClose}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x8l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <View style={styles.container}>
            <View style={styles.modalContainer}>
              <View style={styles.filterView}>
                <Label
                  color="text"
                  size="l"
                  fontFamily="SemiBold"
                  text={t('filters.filter')}
                />
              </View>
              <View style={styles.modalView2}>
                <View style={styles.quickFilters}>
                  <View style={styles.flex}>
                    <Label
                      size="mx"
                      fontFamily="SemiBold"
                      color="text"
                      text={t('filters.quickFilters')}
                    />
                  </View>
                  <View style={styles.filterOpacity}>
                    {selectedKey && (
                      <TouchableOpacity
                        onPress={handleSelectAll}
                        style={styles.filterView2}>
                        <CheckBox
                          value={isSelectAll} // Use isSelectAll to toggle the CheckBox
                          onValueChange={handleSelectAll}
                          style={styles.checkBoxHeader}
                          selected={
                            filters[selectedKey]?.length ===
                            selectedItems[selectedKey]?.length
                          }
                        />
                        <Spacer size="xm" type="Horizontal" />
                        <Label
                          size="mx"
                          fontFamily="SemiBold"
                          color="text"
                          text={t('filters.selectAll')}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              </View>
              <View style={styles.modalView3}>
                <View style={styles.col1}>
                  <View style={styles.filterSection}>
                    {Object.keys(filters).map((key, index) => (
                      <View key={index}>
                        <TouchableOpacity
                          style={
                            selectedKey === key
                              ? styles.selectedLabel
                              : styles.normalLabel
                          }
                          onPress={() => setSelectedKey(key)}>
                          <Label
                            text={key}
                            fontFamily="Medium"
                            color={selectedKey === key ? 'text' : 'text2'}
                            style={styles.titleStyle}
                          />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </View>
                <View style={styles.col2}>
                  {selectedKey ? (
                    <ScrollView>
                      {filters?.[selectedKey]?.length > 0
                        ? filters?.[selectedKey]?.map((value, index) => (
                            <TouchableOpacity
                              key={index}
                              onPress={() => handleCheckBoxChange(value)}>
                              <Spacer size="xms" />
                              <View style={styles.col2Item}>
                                <CheckBox
                                  selected={isSelected(value)}
                                  value={
                                    selectedItems[selectedKey]?.includes(
                                      value.value || value.publisher,
                                    ) || false
                                  }
                                  onValueChange={() =>
                                    handleCheckBoxChange(value)
                                  }
                                  style={styles.checkBox}
                                />
                                <Spacer size="xm" type="Horizontal" />
                                <Label
                                  size="mx"
                                  color="text2"
                                  fontFamily="Medium"
                                  text={value.label || value.publisher}
                                />
                              </View>
                            </TouchableOpacity>
                          ))
                        : null}
                    </ScrollView>
                  ) : (
                    <>
                      <Label
                        text={t('validations.noSelectFilter')}
                        fontFamily="Regular"
                      />
                    </>
                  )}
                </View>
              </View>
              <View style={styles.buttonView}>
                {areFiltersApplied() && (
                  <TouchableOpacity
                    style={styles.button1Opacity}
                    onPress={handleClearAll}>
                    <Label
                      size="l"
                      color="orange1"
                      fontFamily="Medium"
                      text={t('filters.clear')}
                    />
                  </TouchableOpacity>
                )}
                <View style={styles.flex} />
                <TouchableOpacity
                  style={[
                    styles.buttonStyle,
                    {
                      backgroundColor: areFiltersApplied()
                        ? colors.orange1
                        : colors.grey2,
                    },
                  ]}
                  onPress={
                    areFiltersApplied()
                      ? handleApplyFilter
                      : disableFragmentWarnings
                  }>
                  <Label
                    size="l"
                    color={areFiltersApplied() ? 'whiteColor' : 'text2'}
                    fontFamily="Medium"
                    text={t('filters.apply')}
                  />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default FilterModal;

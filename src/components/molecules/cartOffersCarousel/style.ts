import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    disView: {
      position: 'absolute',
      zIndex: Sizes.xl,
      alignSelf: 'center',
      top: -Sizes.s,
    },
    offerImage: {
      width: Sizes.x3l,
      height: Sizes.x3l,
    },
    offerLinear: {
      paddingBottom: Sizes.s,
      borderRadius: Sizes.xms,
      alignItems: 'center',
    },
    alignCenter: {
      alignItems: 'center',
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.xm,
    },
    gradientLine: {
      height: Sizes.xs,
      width: '50%',
      marginTop: Sizes.xms,
      alignSelf: 'center',
    },
  });

export default styles;

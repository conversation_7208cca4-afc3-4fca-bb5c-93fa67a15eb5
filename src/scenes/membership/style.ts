import {Sizes, Fonts} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    centerView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    mainView: {
      paddingVertical: Sizes.xms,
      paddingHorizontal: Sizes.m,
      backgroundColor: colors.whiteColor,
    },
    btnStyle: {
      backgroundColor: colors.categoryTitle,
      height: Sizes.x7l,
    },
    btnText: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    flex: {
      flex: Sizes.x,
    },
    flexHalf: {
      flex: Sizes.x + Sizes.z,
    },
    flex2: {
      flex: Sizes.xs,
    },
    subBannerImg: {
      width: '100%',
      height: Sizes.ex330,
    },
    arrowView: {
      position: 'absolute',
      top: Sizes.xms,
      left: Sizes.xms,
      zIndex: Sizes.x,
    },
    onCheckoutView: {
      paddingVertical: Sizes.mx,
      borderRadius: Sizes.xms,
      alignItems: 'center',
      backgroundColor: colors.categoryTitle,
    },
    dashLineView: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: Sizes.xm,
    },
    totalView: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      padding: Sizes.mx,
      flexDirection: 'row',
      alignItems: 'center',
    },
    expiredView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      borderColor: colors.red20,
      backgroundColor: colors.lightPink1,
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.sx,
    },
    previousView: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      flexDirection: 'row',
      padding: Sizes.xms,
      alignItems: 'center',
    },
    activeView: {
      borderWidth: Sizes.x,
      borderColor: colors.greenRgb3,
      borderRadius: Sizes.sx,
      backgroundColor: colors.green4,
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.sx,
      marginLeft: Sizes.xm,
    },
    currentView: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      flexDirection: 'row',
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.xx,
      alignItems: 'center',
      backgroundColor: colors.jordyBlue34,
    },
    cardsMainView: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.mx,
      backgroundColor: colors.background,
      borderColor: colors.background,
      elevation: Sizes.xs,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.mx,
      marginHorizontal: Sizes.xs,
      shadowColor: colors.dimGray,
      shadowOffset: {
        width: 0,
        height: 0,
      },
      shadowOpacity: 0.1,
      shadowRadius: Sizes.mx,
    },
    cardSection: {
      justifyContent: 'space-between',
      flexDirection: 'row',
      flex: Sizes.x,
    },
    validityView: {
      backgroundColor: colors.offWhite,
    },
    activeContainer: {
      paddingHorizontal: Sizes.xm,
    },
    validitySubView: {
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
      flexDirection: 'row',
      backgroundColor: colors.categoryTitle,
      paddingVertical: Sizes.l,
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.m,
      marginHorizontal: Sizes.xs,
    },
    premiumContainer: {
      marginHorizontal: Sizes.xm,
    },
    premiumView: {
      backgroundColor: colors.hawkesBlue1,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.xm,
      borderRadius: Sizes.mx,
    },
    cardBox: {
      backgroundColor: colors.grey7,
      borderRadius: Sizes.m,
      padding: Sizes.xl,
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'center',
      width: (DeviceWidth - Sizes.xxxl) / Sizes.xs,
      height: Sizes.ex116,
      marginBottom: Sizes.m,
    },
    cardImg: {
      width: Sizes.x8l,
      height: Sizes.x8l,
    },
    titleStyle: {
      textAlign: 'center',
      fontSize: Sizes.mx,
      fontFamily: Fonts.SemiBold,
    },
  });

export default styles;

name: Deploy iOS

on:
  push:
    branches:
      - development  # Trigger workflow on push to the development branch
      - staging      # Trigger workflow on push to the staging branch
      - master       # Trigger workflow on push to the master branch

permissions:
  contents: write  # Set permission to allow pushing commits

jobs:
  cancel-previous-runs:
    runs-on: macos-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ secrets.PAT_TOKEN }}  # Use the new PAT_TOKEN secret
  build:
    runs-on: macos-latest
    needs: cancel-previous-runs
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18.20'  # Or your desired Node.js version
        

      - name: Install Dependencies
        run: yarn install

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.1.2'
          bundler-cache: true

      - name: Install Fastlane
        run: gem install fastlane bundler && bundle install

      - name: Setup iOS environment
        run: |
          sudo gem install cocoapods
          cd ios && pod install

      - name: Set environment variable
        run: |
          if [ "${{ github.ref }}" == "refs/heads/development" ]; then
            echo "ENVIRONMENT=dev" >> $GITHUB_ENV
          elif [ "${{ github.ref }}" == "refs/heads/staging" ]; then
            echo "ENVIRONMENT=staging" >> $GITHUB_ENV
          elif [ "${{ github.ref }}" == "refs/heads/master" ]; then
            echo "ENVIRONMENT=production" >> $GITHUB_ENV
          else
            echo "Invalid branch for deployment"
            exit 1
          fi

      - name: Deploy iOS with Fastlane
        id: deploy  # Assign an ID to this step to reference later
        env:
          APP_STORE_CONNECT_KEY_ID: ${{ secrets.APP_STORE_CONNECT_KEY_ID }}
          APP_STORE_CONNECT_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
          APP_STORE_CONNECT_KEY_FILEPATH: ${{ secrets.APP_STORE_CONNECT_KEY_FILEPATH }}
          MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}
          TEAM_ID: ${{ secrets.TEAM_ID }}
          CODE_SIGN_IDENTITY: ${{ secrets.CODE_SIGN_IDENTITY }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          PRODUCTION_PROFILE_NAME: ${{ secrets.PRODUCTION_PROFILE_NAME }}
          PRODUCTION_NOTIFICATION_SERVICE_PROFILE_NAME: ${{ secrets.PRODUCTION_NOTIFICATION_SERVICE_PROFILE_NAME }}
          PRODUCTION_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME: ${{ secrets.PRODUCTION_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME }}
          DEV_PROFILE_NAME: ${{ secrets.DEV_PROFILE_NAME }}
          DEV_NOTIFICATION_SERVICE_PROFILE_NAME: ${{ secrets.DEV_NOTIFICATION_SERVICE_PROFILE_NAME }}
          DEV_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME: ${{ secrets.DEV_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME }}
          STAGING_PROFILE_NAME: ${{ secrets.STAGING_PROFILE_NAME }}
          STAGING_NOTIFICATION_SERVICE_PROFILE_NAME: ${{ secrets.STAGING_NOTIFICATION_SERVICE_PROFILE_NAME }}
          STAGING_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME: ${{ secrets.STAGING_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME }}
          ENVIRONMENT: ${{ env.ENVIRONMENT }}
        run: |
          cd ios && fastlane deploy environment:$ENVIRONMENT
        continue-on-error: true  # Continue the workflow even if this step fails

      - name: Deploy iOS with Fastlane (Retry)
        if: ${{ steps.deploy.outcome == 'failure' }}  # Run this step only if the previous step failed
        env:
          APP_STORE_CONNECT_KEY_ID: ${{ secrets.APP_STORE_CONNECT_KEY_ID }}
          APP_STORE_CONNECT_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
          APP_STORE_CONNECT_KEY_FILEPATH: ${{ secrets.APP_STORE_CONNECT_KEY_FILEPATH }}
          MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}
          TEAM_ID: ${{ secrets.TEAM_ID }}
          CODE_SIGN_IDENTITY: ${{ secrets.CODE_SIGN_IDENTITY }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          PRODUCTION_PROFILE_NAME: ${{ secrets.PRODUCTION_PROFILE_NAME }}
          PRODUCTION_NOTIFICATION_SERVICE_PROFILE_NAME: ${{ secrets.PRODUCTION_NOTIFICATION_SERVICE_PROFILE_NAME }}
          PRODUCTION_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME: ${{ secrets.PRODUCTION_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME }}
          DEV_PROFILE_NAME: ${{ secrets.DEV_PROFILE_NAME }}
          DEV_NOTIFICATION_SERVICE_PROFILE_NAME: ${{ secrets.DEV_NOTIFICATION_SERVICE_PROFILE_NAME }}
          DEV_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME: ${{ secrets.DEV_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME }}
          STAGING_PROFILE_NAME: ${{ secrets.STAGING_PROFILE_NAME }}
          STAGING_NOTIFICATION_SERVICE_PROFILE_NAME: ${{ secrets.STAGING_NOTIFICATION_SERVICE_PROFILE_NAME }}
          STAGING_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME: ${{ secrets.STAGING_NOTIFICATION_VIEW_CONTROLLER_PROFILE_NAME }}
          ENVIRONMENT: ${{ env.ENVIRONMENT }}
        run: |
          cd ios && fastlane retry_deploy environment:$ENVIRONMENT
      
      - name: Commit source code on success
        if: success()
        run: |
          git config --global user.name "Github"
          git config --global user.email "<EMAIL>"
          git fetch origin
          git checkout ${{ github.ref_name }}
          git pull origin ${{ github.ref_name }}  # Update local branch with remote changes
          git restore --staged package.json yarn.lock  # Unstage package.json and yarn.lock
          git restore package.json yarn.lock  # Revert changes to package.json and yarn.lock
          git add .
          git commit -m "Automated commit after deployment" || echo "No changes to commit"
          git push origin ${{ github.ref_name }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # Use GITHUB_TOKEN to authenticate push
          continue-on-error: true  # Ensure that failure to commit does not affect workflow completion  
  
      - name: Push changes to GitLab
        if: success()
        run: |
          git config --global user.name "Github"
          git config --global user.email "<EMAIL>"
          git remote add gitlab https://oauth2:${{ secrets.GITLAB_TOKEN }}@gitlab.com/VASA-Denticity/********************.git
          git checkout ${{ github.ref_name }}
          git fetch gitlab
          git merge origin/${{ github.ref_name }}
          git push -u gitlab ${{ github.ref_name }}
        env:
          GITLAB_TOKEN: ${{ secrets.GITLAB_TOKEN }}
          continue-on-error: true
  

import React, {useState} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer, ReanimatedCarousel} from 'components/atoms';
import stylesWithOutColor from './style';
import {freeProduct} from 'staticData';
import {t} from 'i18next';
import {useMemo} from 'react';

const OfferSlider = () => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [offerModal, setOfferModal] = useState(false);

  return (
    <>
      <View style={styles.fRow}>
        <TouchableOpacity
          onPress={() => {
            setOfferModal(!offerModal);
          }}
          style={styles.offersView}>
          <ImageIcon icon="dis" size="xxl" />
          <Spacer size="xms" type="Horizontal" />
          <Label text={t('PDP.offers')} weight="500" size="l" color="text" />
          <Spacer size="xms" type="Horizontal" />
          <ImageIcon
            icon={!offerModal == true ? 'arrowBottom' : 'arrowUp'}
            size="xxl"
            tintColor="text"
          />
        </TouchableOpacity>
        <View style={styles.flex} />
      </View>
      {offerModal == true && (
        <View style={styles.offerView}>
          <View style={styles.offerMainView}>
            <View style={styles.disView}>
              <ImageIcon icon="dis" size="xxl" />
            </View>
            <Spacer size="xs" />
            <ReanimatedCarousel
              carouselStyle={styles.sliderItem}
              pagination
              urlResolverKey="link"
              data={freeProduct}
              screenName={'Offers'}
              loop
              autoPlay
              renderItem={({item}) => (
                <TouchableOpacity
                  activeOpacity={0.8}
                  // onPress={onPress}
                  // key={index.toString()}
                >
                  <ImageIcon
                    icon="freecarouselImage"
                    style={styles.offerItemImg}
                  />
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      )}
    </>
  );
};

export default OfferSlider;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    renderContainer: {
      padding: Sizes.m,
      backgroundColor: colors.whiteColor,
      flexDirection: 'row',
      paddingVertical: Sizes.xm,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    flex: {
      flex: Sizes.x,
    },
    tagView: {
      backgroundColor: colors.aliceBlue,
      height: Sizes.xxl,
      width: Sizes.xxl,
      borderRadius: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: Sizes.xm,
    },
    defaultView: {
      backgroundColor: colors.blue,
      borderRadius: Sizes.s,
      width: Sizes.x70,
      height: Sizes.xl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    toggleOff: {
      backgroundColor: colors.text2,
      margin: 0,
      padding: 0,
      height: Sizes.xsl,
      width: Sizes.xsl,
      borderRadius: Sizes.x8l,
    },
    toggleOn: {
      height: Sizes.xsl,
      width: Sizes.xsl,
      borderRadius: Sizes.x8l,
    },
    toggleView: {
      shadowColor: colors.black25,
      shadowOpacity: 0.2,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowRadius: Sizes.s,
      elevation: Sizes.xs,
    },
    fRow: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    row: {
      flexDirection: 'row',
    },
    awayView: {
      backgroundColor: colors.limeGreen,
      paddingHorizontal: Sizes.xm,
      height: Sizes.xl,
      borderRadius: Sizes.s,
      justifyContent: 'center',
      alignItems: 'center',
    },
    defaultGif: {
      width: Sizes.x70,
      height: Sizes.xl,
      borderRadius: Sizes.s,
      overflow: 'hidden',
    },
    editBtn: {
      paddingTop: Sizes.s,
      paddingRight: Sizes.sx,
      flexDirection: 'row',
      alignItems: 'center',
    },
    deleteBtn: {
      paddingTop: Sizes.s,
      paddingHorizontal: Sizes.sx,
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

export default styles;

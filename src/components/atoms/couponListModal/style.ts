import {Sizes} from 'common';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      borderRadius: Sizes.m,
    },
    subView: {
      backgroundColor: colors.background2,
      borderRadius: Sizes.xl,
      height: Sizes.screenHeight * 0.8,
      padding: Sizes.xl,
    },
    noCouponAvailBackground: {
      width: Sizes.ex3l,
      height: Sizes.ex3l,
    },
    noCouponAvailView: {
      padding: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
    },
    image: {
      height: Sizes.ex0,
      width: Sizes.ex0,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      resizeMode: 'contain',
    },
    excitingView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    couponRenderView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.xl,
    },
    btnStyle:{
      paddingHorizontal: Sizes.xl,
    },
    flexView: {
      flex: Sizes.x,
    },
    couponValidView: {
      height: Sizes.exl,
      resizeMode: 'stretch',
      
    },
    couponValidSubView: {
      height: Sizes.exl,
     
    },
    cardSection: {
      paddingVertical: Sizes.xms,
      height: Sizes.exl,
      justifyContent: 'center',
      width:'77%'
    },
    cardDateSection: {
      height: Sizes.exl,
      justifyContent: 'center',
      width:'23%'
    },
    couponCardContentContainer: {
      paddingVertical: Sizes.m,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: Sizes.exl,
      width:'100%'
    },
    discountOffView: {
      transform: [{rotate: '270deg'}],
      marginRight: checkDevice() ? Sizes.xxl : 0,
      alignItems: 'center'
    },
    discountLabelView: {
      flexWrap: 'wrap',
      width: '70%',
      marginLeft: Sizes.xms
    },
  });

export default styles;

import Label from './label';
import Link from './link';
import Radio from './radio';
import Separator from './separator';
import Spacer from './spacer';
import Spinner from './spinner';
import LoginGoogle from './googleLogin';
import CarouselPagination from './carouselPagination';
import CarouselCardItem from './carouselCardItem';
import CarouselCards from './carouselCards';
import ImageIcon from './imageIcon';
import ReqPriceModal from './requestPriceModal';
import OfferItems from './offerItems';
import TopBrandsItems from './topItem/topBrandItems';
import TierPricingDetails from './productTierPrice';
import PrizeCategoryUnder from './prizeCategoryUnder';
import productList from './productList';
import WithBackground from './withBackground';
import WithGradient from './withGradient';
import ListView from './listView';
import SubCategories from './subCategories';
import TopItemWithLabel from './topItem/topItemWithLabel';
import ProductItems from './productItems';
import ProductItemsHorizontal from './productItemsHorizontal';
import ProductCardHorizontal from './productCardHorizontal';
import FooterButton from './footer';
import CheckBox from './checkBox';
import SubCategriesImage from './subCategriesImage';
import Quantity from './quentitySeclater';
import Tag from './tag';
import Address from './locationAddress';
import DropDown from './dropDown';
import SwitchButton from './switch';
import LikeShare from './likeShare';
import NewsCardItem from './newsCardItem';
import CartTotalPrize from './cartTotalPrize';
import PackageItem from './packageItem';
import FastImagesItem from './fastImages';
import Rail from './filterOther/rails';
import RailSelected from './filterOther/railsSelected';
import Thumb from './filterOther/thumb';
import PaginationItem from './paginationItem';
import ReanimatedCarousel from './reanimatedCarousel';
import ReanimatedCarouselPagination from './reanimatedCarouselPagination';
import InfoHeader from './infoHeader';
import ProductCardVertical from './productCardVertical';
import OrderCard from './orderBuyAgain';
import WishListCard from './wishlistCards';
import LikeDisLikeButton from './likeDisLikeButton';
import SearchInput from './searchInput';
import CarouselItem from './carouselItem';
import CategoriesItem from './categoriesItem';
import TopBrands from './topBrands';
import RecentlyViewedItem from './RecentlyViewedItem';
import HotSellingSection from './HotSellingSection';
import MostSearchedSection from './MostSearchedSection';
import StandardCarouselSectionWithTag from './StandardCarouselSectionWithTag';
import TopCategoriesSection from './TopCategoriesSection';
import HtmlModal from './htmlModal';
import FaqItem from './faqItem';
import CartItem from './cartItem';
import ModalComponent from './modalComponent';
import CouponListModal from './couponListModal';
import CartRemoveModal from './cartRemoveModal';
import CouponCart from './couponCart';
import CartEmptyView from './cartEmptyView';
import EmptyWishlist from './emptyWishlist';
import CartPriceSection from './cartPriceSection';
import OrderFilter from './orderFilter';
import OrderImageModal from './orderImageModal';
import OrderCancelModal from './orderCancelModal';
import ReturnCard from './returnCard';
import OrderReturnDetailModal from './orderReturnDetailModal';
import CircularProgress from './circularProgress';
import CartPositionStatus from './cartPositionStatus';
import PaymentSupportCart from './paymentSupportCart';
import SuccessModal from './successModal';
import RegistrationModal from './registrationModal';
import WishlistButton from './wishlistButton';
import RateReview from './rateReview';
import SuggestionProductView from './suggestionProductView';
import ProductHighlights from './productHighlights';
import PDPSliderItem from './pdpSliderItem';
import OfferSlider from './offerSlider';
import ProductDetailCard from './productDetailCard';
import GroupProductView from './groupProductView';
import FreeBieModal from './freeBieModal';
import TierPricesCard from './tierPricesCard';
import EmiModal from './emiModal';
import ReferralModal from './referralModal';
import ShareModal from './shareModal';
import PostQuestionModal from './postQuestionModal';
import RateProductModal from './rateProductModal';
import SimilarProductModal from './similarProductModal';
import BulkModal from './bulkModal';
import ValuableFeedbackModal from './valuableFeedbackModal';
import KnowMoreFooterModal from './knowMoreFooterModal';
import AccordionView from './accordionView';
import CatalogueModal from './catalogueModal';
import GradientText from './gradientText';
import RollingText from './rollingText';
import TypingEffect from './typingEffect';
import HorizontalScrollBar from './horizontalScroll';
import ShortsItem from './shortsItem';
import PickupModal from './pickupModal';
import WebViewModal from './webViewModal';
import DownloadCatalogue from './downloadCatalogue';
import TireSuccessModal from './tireSuccessModal';
import ErrorFallback from './errorBoundary';
import OfferLineAnimation from './offerLineAnimation';
import DeliveryInfoModal from './deliveryInfoModal';
import RetryPaymentModal from './retryPayment';
import OrderShipmentCard from './orderShipmentCard';
import CustomToast from './customToast';
import MarqueeText from './marqueeText';
import EmptyView from './emptyView';
import PreviousOrderReturn from './previousOrderReturn';
import FlatListCarousel from './flastlistCarousel';
import FlatListCarouselCardItem from './flastlistCarouselCardItem';
import Help from './help';
import MapsModal from './mapsModal';
import AddressTagList from './addressTagList';
import AddressAutoPlaces from './addressAutoPlaces';
import AddressConfirmModal from './addressConfirmModal';
import AddressCard from './addressCard';
// import OfflineNotice from './offlineNotice';
import CommonModal from './commonModal/commonModal';
import OTPTextView from './OTPTextView';
import BillingAddress from './billingAddress';
export {
  Label,
  Tag,
  Link,
  Radio,
  Separator,
  Spacer,
  Spinner,
  LoginGoogle,
  CarouselPagination,
  ReqPriceModal,
  CarouselCards,
  CarouselCardItem,
  OfferItems,
  TopBrandsItems,
  PrizeCategoryUnder,
  productList,
  WithBackground,
  ImageIcon,
  ListView,
  DownloadCatalogue,
  SubCategories,
  TopItemWithLabel,
  ProductItems,
  SubCategriesImage,
  Quantity,
  Address,
  DropDown,
  SwitchButton,
  CheckBox,
  LikeShare,
  NewsCardItem,
  FreeBieModal,
  CartTotalPrize,
  PackageItem,
  ProductItemsHorizontal,
  ProductCardHorizontal,
  FooterButton,
  FastImagesItem,
  WithGradient,
  Rail,
  Thumb,
  RailSelected,
  CustomToast,
  ShareModal,
  PaginationItem,
  ReanimatedCarousel,
  ReanimatedCarouselPagination,
  InfoHeader,
  ProductCardVertical,
  OrderCard,
  WishListCard,
  LikeDisLikeButton,
  SearchInput,
  CarouselItem,
  CategoriesItem,
  TopBrands,
  RecentlyViewedItem,
  HotSellingSection,
  MostSearchedSection,
  StandardCarouselSectionWithTag,
  TopCategoriesSection,
  HtmlModal,
  FaqItem,
  CartItem,
  ModalComponent,
  EmptyWishlist,
  CouponListModal,
  CartRemoveModal,
  CouponCart,
  CartEmptyView,
  CartPriceSection,
  OrderFilter,
  OrderImageModal,
  OrderCancelModal,
  ReturnCard,
  OrderReturnDetailModal,
  CircularProgress,
  CartPositionStatus,
  PaymentSupportCart,
  SuccessModal,
  RegistrationModal,
  WishlistButton,
  RateReview,
  SuggestionProductView,
  ProductHighlights,
  PDPSliderItem,
  OfferSlider,
  ProductDetailCard,
  GroupProductView,
  TierPricesCard,
  EmiModal,
  ReferralModal,
  PostQuestionModal,
  RateProductModal,
  SimilarProductModal,
  BulkModal,
  ValuableFeedbackModal,
  KnowMoreFooterModal,
  AccordionView,
  CatalogueModal,
  GradientText,
  RollingText,
  TypingEffect,
  HorizontalScrollBar,
  ShortsItem,
  PickupModal,
  WebViewModal,
  TireSuccessModal,
  ErrorFallback,
  OfferLineAnimation,
  DeliveryInfoModal,
  RetryPaymentModal,
  TierPricingDetails,
  OrderShipmentCard,
  MarqueeText,
  EmptyView,
  PreviousOrderReturn,
  FlatListCarousel,
  FlatListCarouselCardItem,
  Help,
  MapsModal,
  AddressTagList,
  AddressAutoPlaces,
  AddressConfirmModal,
  AddressCard,
  // OfflineNotice,
  CommonModal,
  OTPTextView,
  BillingAddress,
};

import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {View, ViewProps, ModalBaseProps} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import Modal from 'react-native-modal';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose?: (visible: boolean) => void;
  children: React.ReactElement;
  style?: ViewProps['style'];
  modelBackStyle?: ViewProps['style'];
  modelStyle?: ViewProps['style'];
  animationType?: ModalBaseProps['animationType'];
  alignBottom?: boolean;
};

const ModalComponent = ({
  visible,
  onClose,
  children,
  style,
  modelBackStyle,
  modelStyle,
  animationType = 'slide',
  alignBottom = false,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={[styles.centeredView, style]}>
      <Modal
        onBackButtonPress={() => {
          onClose?.(!visible);
        }}
        onBackdropPress={() => {
          onClose?.(!visible); // Close modal when tapping outside
        }}
        isVisible={visible}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={75}
        animationOutTiming={75}
        backdropOpacity={0.01}
        style={styles.modalStyle}>
        <View style={[styles.centeredView, modelBackStyle]}>
          {alignBottom && <View style={styles.modelView} />}
          <View style={[styles.modelBg, modelStyle]}>{children}</View>
        </View>
      </Modal>
    </View>
  );
};

export default ModalComponent;

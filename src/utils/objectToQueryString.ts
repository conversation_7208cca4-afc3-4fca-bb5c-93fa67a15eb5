const objectToQueryString = obj => {
  let str = [];
  for (const p in obj) {
    if (obj.hasOwnProperty(p)) {
      let value;
      // Check if the value is an object or array and handle accordingly
      if (typeof obj[p] === 'object') {
        value = JSON.stringify(obj[p]);
      } else {
        value = obj[p];
      }
      // Encode the key and value
      let objStr = encodeURIComponent(p) + '=' + encodeURIComponent(value);
      str.push(objStr);
    }
  }
  return str.join('&');
};

export default objectToQueryString;

// const objectToQueryString = (obj) => {
//   let str = [];
//   for (var p in obj)
//     if (obj.hasOwnProperty(p)) {
//       str.push(encodeURIComponent(p) + "=" + encodeURIComponent(obj[p]));
//     }
//   return str.join("&");
// }

// export default objectToQueryString;

import React, {useMemo, useCallback} from 'react';
import {
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import DashedLine from 'react-native-dashed-line';
import {t} from 'i18next';
import {Label, ImageIcon, Spacer, Radio} from 'components/atoms';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import {Sizes} from 'common';
import Modal from 'react-native-modal';
import {btnClickCallBack} from 'utils/utils';

const RegistrationModal = props => {
  const {colors} = useTheme();
  const inputRef = React.useRef(null);
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {
    visible,
    onClose,
    regIds,
    selectedRegId,
    onSelectRadio,
    selectedRegIdForEdit,
    regIdEditValue,
    setRegIdEditValue,
    onEditRegId,
    onDeleteRegId,
    updateRegId,
    showAddNewInput,
    setRegIdValue,
    regIdValue,
    onAddReg,
    onAddNewRegId,
    showEditInput,
    regIdEdit,
    setRegIdEdit,
  } = props;

  return (
    <Modal
      style={styles.modalView}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.25}
      onBackButtonPress={() => onClose()}
      isVisible={visible}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0} // Adjust as needed
      >
        <View style={styles.mainView}>
          <TouchableWithoutFeedback onPress={() => onClose()}>
            <View style={styles.modalCloseView}>
              <TouchableOpacity
                onPress={() => onClose()}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x44" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <TouchableWithoutFeedback onPress={() => onClose()}>
            <View style={styles.excitingView}>
              <Label
                size="l"
                text={t('registrationId.selectReg')}
                color="text2"
                fontFamily="Medium"
              />
            </View>
          </TouchableWithoutFeedback>
          <Spacer size="xms" />
          <DashedLine
            dashLength={4}
            dashThickness={1.5}
            dashColor={colors.grey2}
          />
          <Spacer size="xm" />
          <View>
            <FlatList
              data={regIds}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              ItemSeparatorComponent={<Spacer size="s" />}
              keyExtractor={(_, index) => index.toString()}
              renderItem={({
                item,
                index,
              }: {
                item: Registration;
                index: number;
              }) => (
                <View key={index}>
                  {selectedRegIdForEdit === item?.id && (
                    <Label
                      size="mx"
                      text={t('registrationId.registration')}
                      color="categoryTitle"
                      fontFamily="Medium"
                    />
                  )}

                  <View style={styles.renderView}>
                    <Radio
                      selected={selectedRegId?.id === item?.id}
                      fillColor="text2"
                      borderColor="text2"
                      onPress={() => (showEditInput ? {} : onSelectRadio(item))}
                    />
                    <Spacer size="xm" type="Horizontal" />
                    <View
                      style={[
                        styles.renderRow,
                        {
                          paddingHorizontal:
                            selectedRegIdForEdit === item?.id ? Sizes.sx : 0,
                          borderWidth:
                            selectedRegIdForEdit === item?.id ? Sizes?.x : 0,
                        },
                      ]}>
                      {selectedRegIdForEdit === item?.id ? (
                        <TextInput
                        testID="txtRegistrationPlaceId"
                          value={regIdEdit}
                          onChangeText={setRegIdEdit}
                          placeholder={t('registrationId.placeId')}
                          placeholderTextColor={colors.text}
                          style={styles.inputBox}
                          allowFontScaling={false}
                        />
                      ) : (
                        <Label
                          style={styles.flex}
                          text={item?.registration_no}
                          color="text"
                          size="mx"
                          fontFamily="Medium"
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        />
                      )}
                      <Spacer size="xm" type="Horizontal" />
                      {selectedRegIdForEdit === item?.id ? (
                        <Button
                          onPress={() =>
                            btnClickCallBack(() => updateRegId(item, regIdEdit))
                          }
                          text={t('buttons.save')}
                          disabled={
                            regIdEdit === item?.registration_no ||
                            !!!regIdEdit.trim()
                          }
                          type={
                            regIdEdit === item?.registration_no
                              ? 'disabled'
                              : !!regIdEdit.trim()
                              ? 'secondary'
                              : 'disabled'
                          }
                          size="extra-small"
                          labelColor={
                            regIdEdit === item?.registration_no
                              ? 'grey9'
                              : !!regIdEdit.trim()
                              ? 'whiteColor'
                              : 'grey9'
                          }
                          labelSize="mx"
                          weight="400"
                          radius="sx"
                          style={styles.saveBtn}
                        />
                      ) : (
                        <View style={styles.fRow}>
                          <Button
                            onPress={() => onEditRegId(item)}
                            iconCenter="editPencil"
                          />
                          {!item?.is_default ? (
                            <Button
                              onPress={() =>
                                btnClickCallBack(() => onDeleteRegId(item))
                              }
                              iconCenter="delete"
                            />
                          ) : null}
                        </View>
                      )}
                    </View>
                  </View>
                </View>
              )}
              ListFooterComponent={
                <View>
                  {showAddNewInput ? (
                    <>
                      <View style={styles.renderView}>
                        <Radio fillColor="text2" borderColor="text2" />
                        <Spacer size="m" type="Horizontal" />
                        <View style={styles.addInput}>
                          <TextInput 
                            testID="txtRegistrationPlaceId"
                            ref={inputRef}
                            value={regIdValue}
                            onChangeText={setRegIdValue}
                            placeholder={t('registrationId.placeId')}
                            placeholderTextColor={colors.text}
                            style={styles.addInputStyle}
                            allowFontScaling={false}
                          />
                          <Button
                            onPress={onAddNewRegId}
                            onPress={() =>
                              btnClickCallBack(() => {
                                inputRef.current?.blur();
                                Keyboard.dismiss();
                                onAddNewRegId();
                              })
                            }
                            text={t('buttons.save')}
                            disabled={!!!regIdValue.trim()}
                            type={
                              !!regIdValue.trim() ? 'secondary' : 'disabled'
                            }
                            size="extra-small"
                            labelColor={
                              !!regIdValue.trim() ? 'whiteColor' : 'grey9'
                            }
                            labelSize="mx"
                            weight="400"
                            radius="sx"
                            style={styles.saveBtn}
                          />
                        </View>
                      </View>
                      <Spacer size="xm" />
                    </>
                  ) : null}

                  <TouchableOpacity onPress={() => onAddReg()}>
                    <Label
                      size="mx"
                      text={t('registrationId.addNewReg')}
                      color="categoryTitle"
                      fontFamily="Medium"
                    />
                  </TouchableOpacity>
                </View>
              }
            />
          </View>
          <Spacer size="m" />
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default RegistrationModal;

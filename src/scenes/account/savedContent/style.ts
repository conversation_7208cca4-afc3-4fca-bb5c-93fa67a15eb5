import {Platform, StyleSheet} from 'react-native';
import {Sizes} from 'common';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    mainView: {
      paddingHorizontal: Sizes.m,
      flex: Sizes.x,
    },
    buttonView: {marginVertical: Sizes.m},
    buttons: {
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      marginRight: Sizes.xm,
      paddingHorizontal: Sizes.xx,
      borderColor: colors.categoryTitle,
      paddingVertical: Sizes.sx,
    },
    // blog style ...............................
    mainContainer: {
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.mx,
      padding: Sizes.m,
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
    },
    image: {
      padding: Sizes.xm,
      borderRadius: Sizes.xl,
      width: '100%',
      height: Sizes.ex1,
    },
    subContainer: {
      flexDirection: 'row',
    },
    icons: {
      flexDirection: 'row',
    },
    // shorts style ...............................

    itemContainer: {
      borderRadius: Sizes.xm,
      width: '48.5%',
      height: Sizes.ex2l + Sizes.ex,
      backgroundColor: colors.whiteColor,
    },
    reelIcon: {
      position: 'absolute',
      right: 0,
      bottom: Sizes.ex + Sizes.m,
      paddingRight: Sizes.xm,
    },
    gradient: {
      position: 'absolute',
      zIndex: Sizes.s,
      bottom: Platform.OS === 'android' ? 0 : Sizes.exl,
      width: '100%',
      height: Sizes.ex2l,
      borderRadius: Sizes.xm,
    },
    img: {
      width: '100%',
      height: '100%',
      borderRadius: Sizes.xm,
    },
    titles: {
      paddingVertical: Sizes.xm,
      paddingHorizontal: Sizes.m,
      position: 'absolute',
      bottom: 0,
    },

    // magazine style ...............................

    magCardView: {
      width: '48%',
      borderRadius: Sizes.xms,
      borderWidth: Sizes.z,
      borderColor: colors.grey2,
      backgroundColor: colors.offWhite,
      alignItems: 'center',
      padding: Sizes.m,
    },
    readBtn: {
      borderWidth: Sizes.z,
      borderRadius: Sizes.sx,
      borderColor: colors.categoryTitle,
      paddingVertical: Sizes.sx,
      paddingHorizontal: Sizes.m,
    },
    title: {
      width: '100%',
      flex: Sizes.x,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },

    monnthText: {
      width: '100%',
    },
    magImage: {
      borderRadius: Sizes.xms,
      width: Sizes.ex1,
      height: Sizes.ex2l - (Sizes.x + Sizes.xs),
    },
    footerCard: {
      flex: Sizes.x,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    subFooterCard: {
      justifyContent: 'space-evenly',
      flexDirection: 'row',
      alignItems: 'center',
    },

    cardFlatList: {
      marginBottom: Sizes.xx,
    },

    //news...................................//
    iconContainer: {
      flexDirection: 'row',
      flex: Sizes.x,
      alignItems: 'flex-end',
    },
    flex: {flex: Sizes.x},
    nameLabel: {
      paddingVertical: Sizes.xm,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
      borderStyle: 'dashed',
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
    },
    dateLabel: {
      flexDirection: 'row',
      justifyContent: 'center',
    },
    cardMainView: {
      borderWidth: Sizes.x,
      flex: Sizes.x,
      flexDirection: 'row',
      height: Sizes.ex2l,
      paddingHorizontal: Sizes.m,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
      borderColor: colors.grey2,
      paddingVertical: Sizes.l,
    },
    labelBorder: {paddingHorizontal: Sizes.sx},
    cardIconView: {
      backgroundColor: colors.whiteColor,
      padding: Sizes.sx,
      position: 'absolute',
      right: Sizes.xms,
      bottom: Sizes.m,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: colors.black,
      elevation: Sizes.xs,
      borderRadius: Sizes.x8l,
    },
    cardText: {
      width: '100%',
      height: Sizes.exl,
      padding: Sizes.m,
      borderBottomLeftRadius: Sizes.xms,
      borderBottomRightRadius: Sizes.xms,
      backgroundColor: colors.skyBlue16,
    },
    textLabel: {
      overflow: 'hidden',
      marginBottom: Sizes.m,
    },
    cardImage1: {
      borderWidth: Sizes.z,
      borderColor: colors.text2,
      width: Sizes.exl + Sizes.xms,
      height: Sizes.ex1,
      borderRadius: Sizes.mx,
    },
    viewData: {
      flexDirection: 'column',
    },
    listStyle: {
      justifyContent: 'space-between',
    },
  });

export default styles;

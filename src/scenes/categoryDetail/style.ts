import {Sizes, Fonts} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';
import {checkDevice} from 'utils/utils';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    boxBrands: {
      borderWidth: 0.4,
      elevation: Sizes.xm,
      borderColor: colors.border,
    },
    emptyView: {
      flex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
    },
    notFoundImg: {
      width: Sizes.ex3l,
      height: Sizes.ex3l,
    },
    flex: {
      flex: Sizes.x,
    },
    listContainer: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.xm,
    },
    headerList: {
      flex: Sizes.x,
      marginHorizontal: Sizes.s,
    },
    justifyLeft: {
      justifyContent: 'flex-start',
    },
    justifyCenter: {
      justifyContent: 'center',
    },
    listFooterLoader: {
      height: '100%',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    collapsedText: {
      overflow: 'hidden',
      width: '100%',
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      borderColor: colors.grey2,
      padding: Sizes.xm,
      marginTop: Sizes.m,
    },
    filterItemBetweenSpace: {
      padding: Sizes.s,
    },
    filterItemTag: {
      paddingHorizontal: Sizes.m,
      height: Sizes.x26,
      alignItems: 'center',
      borderRadius: Sizes.xl,
    },
    filterItemLabel: {
      color: colors.categoryTitle,
      fontFamily: Fonts.Regular,
      marginTop: -Sizes.x,
    },
    filtersListRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    readTxt: {
      marginBottom: -Sizes.xs,
    },
    clearText: {
      fontSize: Sizes.mx,
      fontFamily: Fonts.SemiBold,
      color: colors.text,
      textTransform: 'uppercase',
      alignSelf: 'center',
    },
    clearView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectClearView: {
      marginTop: -7,
      marginBottom: Sizes.sx,
    },
    carouselCardItemStyle: {
      paddingHorizontal: Sizes.xm,
      height: checkDevice() ? Sizes.ex218 : Sizes.ex116,
    },
    sliderSubItemView: {
      height: checkDevice() ? Sizes.ex218 : Sizes.ex116,
    },
    descText: {
      color: colors.text2,
      fontFamily: Fonts.Regular,
    },
    htmlRenderStyle: {
      p: {color: colors.text2, fontFamily: Fonts.Medium, margin: 0, padding: 0},
      li: {color: colors.text2, fontFamily: Fonts.Medium},
      ul: {color: colors.text2, fontFamily: Fonts.Medium},
      div: {color: colors.text2, fontFamily: Fonts.Medium},
      body: {
        color: colors.text2,
        whiteSpace: 'normal',
        fontFamily: Fonts.Medium,
      },
      h1: {color: colors.text2, fontFamily: Fonts.Medium},
      span: {color: colors.text2, fontFamily: Fonts.Medium},
      strong: {color: colors.text2, fontFamily: Fonts.SemiBold},
    },
    topIconView: {
      position: 'absolute',
      height: Sizes.xxxl,
      borderRadius: Sizes.x3l,
      backgroundColor: colors.black2,
      paddingHorizontal: Sizes.xms,
      elevation: Sizes.s,
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
      flexDirection: 'row',
    },
    goTop: {
      marginBottom: -Sizes.xs,
    },
  });

export default styles;

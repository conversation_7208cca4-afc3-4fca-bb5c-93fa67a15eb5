import React from 'react';
import {
  View,
  TouchableOpacity,
  ViewProps,
  ScrollView,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Modal from 'react-native-modal';
import useStyle from './style';
import {ImageIcon} from 'components/atoms';
import {useTheme} from '@react-navigation/native';
import {Sizes} from 'common';

type Props = {
  visible: boolean;
  onClose: () => void;
  content?: React.ReactElement;
  useInsets?: boolean;
  height?: number;
  subViewStyle?: ViewProps['style'];
  maxHeight?: number;
  bounceFalse?: boolean;
};

const DynamicHeightModal = ({
  visible,
  onClose,
  useInsets,
  content,
  height,
  subViewStyle,
  maxHeight,
  bounceFalse,
}: Props) => {
  const insets = useSafeAreaInsets();
  const {colors} = useTheme();
  const styles = useStyle(colors);

  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <KeyboardAvoidingView
        style={styles.modalflex}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0} // Adjust offset as needed
      >
        {/* <TouchableWithoutFeedback onPress={Keyboard.dismiss}> */}
          <View style={styles.centeredView}>
            <TouchableWithoutFeedback onPress={onClose}>
              <View style={styles.modalCloseBtnContainer}>
                <TouchableOpacity
                  onPress={onClose}
                  style={styles.modalCloseButton}>
                  <ImageIcon icon="close" size="x7l" />
                </TouchableOpacity>
              </View>
            </TouchableWithoutFeedback>
            <View
              style={[
                styles.modalView,
                {
                  width: Sizes.screenWidth,
                  height: height && Sizes.screenHeight * height,
                  paddingHorizontal:0,
                  maxHeight: maxHeight && Sizes.screenHeight * maxHeight
                },
                subViewStyle,
              ]}>
              {/* ScrollView for handling content overflow */}
              <SafeAreaView>
                <ScrollView
                  showsVerticalScrollIndicator={false}
                  showsHorizontalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  nestedScrollEnabled={true}
                  scrollEnabled={true}
                  horizontal={false}
                  contentContainerStyle={[
                    styles.modalContent,
                    useInsets && {paddingBottom: insets.bottom},
                  ]}
                  bounces={!bounceFalse}>
                  {content}
                </ScrollView>
              </SafeAreaView>
            </View>
          </View>
        {/* </TouchableWithoutFeedback> */}
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default DynamicHeightModal;

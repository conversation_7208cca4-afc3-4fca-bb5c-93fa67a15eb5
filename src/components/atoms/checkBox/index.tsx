import React from 'react';
import { TouchableOpacity, ViewProps } from 'react-native';
import stylesWithOutColor from './style';
import { useTheme } from '@react-navigation/native';
import ImageIcon from '../imageIcon';
import { useMemo } from 'react';

type Props = {
  selected?: boolean;
  style?: ViewProps['style'];
  value?: string | null | number | boolean;
  onValueChange?: (value: any) => void;
  borderColor?: keyof Theme['colors'];
  fillColor?: keyof Theme['colors'];
  checkTintColor?: keyof Theme['colors'];
  disabled?: boolean;
};
const CheckBox = ({
  selected,
  style,
  value,
  onValueChange,
  checkTintColor,
  borderColor,
  fillColor,
  disabled,
}: Props) => {
  const { colors } = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <TouchableOpacity
      testID='tOCheckBox'
      onPress={() => onValueChange(value)}
      disabled={disabled}
      style={[
        styles.container,
        selected && {
          backgroundColor: fillColor ? colors[fillColor] : colors.text,
        },
        { borderColor: borderColor ? colors[borderColor] : colors.border },
        style,
      ]}>
      {selected && (
        <ImageIcon
          icon="check"
          tintColor={checkTintColor ? checkTintColor : 'whiteColor'}
          size="xm"
        />
      )}
    </TouchableOpacity>
  );
};

export default CheckBox;

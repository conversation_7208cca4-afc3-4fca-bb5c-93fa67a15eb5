import React, {useCallback, useState, useMemo, useEffect} from 'react';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../../routes';
import {AnalyticsEvents, LoginWithOtp} from 'components/organisms';
import stylesWithOutColor from './style';
import tokenClass from 'utils/token';
import {RouteProp, useTheme} from '@react-navigation/native';
import OtpScene from 'components/organisms/otpScene';
import {useDispatch} from 'react-redux';
import {
  generateNewCart,
  getUserInfo,
  setIsLoggedIn,
} from 'app-redux-store/slice/appSlice';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {userLogin, verifyOTP} from 'services/auth';
import ErrorHandler from 'utils/ErrorHandler';
import {Platform, StatusBar, View} from 'react-native';
import {CustomStatusBar} from 'components/molecules';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';
import appsFlyer from 'react-native-appsflyer';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'Login'>;
};

const LoginScene = ({navigation, route}: Props) => {
  const TAG = 'Login';
  const otpScreen = route?.params?.otpScreen;
  const [otpLogin, setOtpLogin] = useState('otpLogin');
  const [pageType, setPageType] = useState('');
  const [actionType, setActionType] = useState('');
  const [showPassInput, setShowPassInput] = useState(false);
  const [editableAccess, setEditableAccess] = useState(false);
  const [phoneFocus, setPhoneFocus] = useState(false);
  const dispatch = useDispatch();
  const [mobileNumber, setMobileNumber] = useState('');
  const [apiErrorOtp, setApiErrorOtp] = useState(null);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [isVerify, setIsVerify] = useState(false);
  const [shouldShow, setShouldShow] = useState(true);
  const [loginType, setLoginType] = useState('phone');

  const navigateToLastScreen = useCallback(() => {
    let lastScreen = route.params?.nextScreenName;
    let lastScreenLink = route.params?.nextScreenNameLink;
    let goBack = route.params?.back;
    let nextRouterState = route.params?.nextRouterState;
    if (nextRouterState?.routes === 'ProductDetail') {
      return navigation.navigate('ProductDetail', {
        productId: nextRouterState?.productId,
      });
    }

    try {
      if (goBack) {
        return navigation.goBack();
      } else if (lastScreen) {
        setTimeout(() => {
          return navigation.navigate(
            lastScreen,
            route.params?.nextScreenParams,
          );
        }, 1000);
      } else if (lastScreenLink) {
        setTimeout(() => {
          return navigation.reset({
            index: 1,
            routes: [
              {
                name: 'Tab',
                params: {screen: 'Shop'},
              },
              {
                name: lastScreenLink,
                params: route.params?.nextScreenParams || {},
              },
            ],
          });
        }, 1000);
      } else if (
        nextRouterState &&
        nextRouterState.routes &&
        Array.isArray(nextRouterState.routes)
      ) {
        return navigation.reset({
          index: nextRouterState.index || 0,
          routes: nextRouterState.routes.map(route => ({
            key: route.key,
            name: route.name,
            params: route.params || {},
          })),
        });
      } else {
        return navigation.reset({
          index: 0,
          routes: [{name: 'Tab', params: {screen: 'Shop'}}],
        });
      }
    } catch (e) {
      return navigation.navigate('Tab', {screen: 'Shop'});
    }
  }, [navigation, route]);

  const verifyOtp = useCallback(
    async (values: string) => {
      dispatch(setLoading(true));
      setEditableAccess(true);
      const {data, status} = await verifyOTP({
        recipient: mobileNumber,
        // action: mobileNumber.includes('@') ? 'login' : actionType,
        action: actionType,
        verification_type: 'otp',
        authentication_type: mobileNumber.includes('@') ? 'email' : 'mobile',
        credential: values,
        new_password: '',
      });
      dispatch(setLoading(false));

      if (status) {
        if (data?.token) {
          setApiErrorOtp(null);
          // setIsVerify(true);
          tokenClass.setToken(data.token);
          dispatch(setIsLoggedIn(true));
          dispatch(setLoading(true));

          if (data?.is_created === true) {
            navigation.navigate('ProfileCompletion', {
              phoneNumber: mobileNumber,
              source: 'registerWithMobile',
              customerId: data?.customer?.id,
              // email: string;
            });
            appsFlyerEvent('Signup', {
              mobileNumber: mobileNumber,
            });
          } else {
            await Promise.all([
              dispatch(getUserInfo()),
              dispatch(generateNewCart()),
            ]).then(() => {});
            AnalyticsEvents(
              'USER_LOGGED_IN',
              'Login-Email/Phone',
              data,
              {},
              false,
            );
            appsFlyer.setCustomerUserId(data?.customer?.id);
            appsFlyerEvent('Login');
            navigateToLastScreen();
          }
          showSuccessMessage(data?.message);
        } else {
          navigation.navigate('ForgetPasswordScene', {
            mobileNumber: mobileNumber,
            credential: values,
            back: () => loginView(),
          });
        }
        dispatch(setLoading(false));
      } else {
        const match = data?.message?.match(
          /(\d+) wrong attempts, (\d+) more left/,
        );
        if (match?.length > 1 && parseInt(match[1]) === 5) {
          navigation.goBack();
        }
        setEditableAccess(false);
        dispatch(setLoading(false));
        showErrorMessage(data?.message);
      }
    },
    [mobileNumber, navigateToLastScreen, actionType],
  );

  const LoginWithOtpResend = useCallback(async () => {
    dispatch(setLoading(true));
    const {data, status} = await userLogin({
      recipient: mobileNumber,
      action:
        actionType === 'forgot_password'
          ? 'forgot_password'
          : mobileNumber.includes('@')
          ? 'login'
          : 'm_login_signup',
      authentication_type: mobileNumber.includes('@') ? 'email' : 'mobile',
    });
    dispatch(setLoading(false));
    if (data && status) {
      setShouldShow(true);
      showSuccessMessage(data?.message);
    } else {
      showErrorMessage(data?.message);
    }
  }, [mobileNumber, actionType, dispatch]);

  const completeOtpTimer = () => {
    setShouldShow(false);
  };
  useEffect(() => {
    if (route?.params?.otpScreen) {
      setOtpLogin(route?.params?.otpScreen);
    }
    if (route?.params?.loginType) {
      setLoginType(route?.params?.loginType || 'phone');
    }
  }, []);

  const loginView = () => {
    setOtpLogin('otpLogin');
    setEditableAccess(false);
  };

  return (
    <>
      {otpLogin === 'otpLogin' && (
        <CustomStatusBar backgroundColor={colors.sunnyOrange3} />
      )}
      <View style={styles.container}>
        <View style={styles.container2}>
          {otpLogin === 'otpLogin' ? (
            <ErrorHandler
              componentName={`${TAG} LoginWithOtp`}
              onErrorComponent={<View />}>
              <LoginWithOtp
                showPass={showPassInput}
                phoneFocus={phoneFocus}
                setShowPass={setShowPassInput}
                setOtpLogin={setOtpLogin}
                navigation={navigation}
                setMobileNumber={setMobileNumber}
                setShouldShow={setShouldShow}
                setPageType={setPageType}
                mobileNumber={mobileNumber}
                actionType={actionType}
                setActionType={setActionType}
                navigateToLastScreen={navigateToLastScreen}
                loginType={loginType}
              />
            </ErrorHandler>
          ) : otpLogin === 'OtpScene' ? (
            <ErrorHandler
              componentName={`${TAG} OtpScene`}
              onErrorComponent={<View />}>
              <OtpScene
                setOtpLogin={val => {
                  setOtpLogin(val);
                  setEditableAccess(false);
                }}
                setShowPassInput={setShowPassInput}
                setPhoneFocus={setPhoneFocus}
                pageType={pageType}
                source={'login'}
                showPass={showPassInput}
                emailPhoneNumber={mobileNumber}
                navigation={navigation}
                route={route}
                onSubmit={verifyOtp}
                isVerify={isVerify}
                actionType={actionType}
                apiErrorOtp={apiErrorOtp}
                onResend={LoginWithOtpResend}
                shouldShow={shouldShow}
                completeOtpTimer={completeOtpTimer}
                onEditPress={() => loginView()}
                setApiErrorOtp={setApiErrorOtp}
                editableAccess={editableAccess}
                setLoginType={setLoginType}
              />
            </ErrorHandler>
          ) : null}
        </View>
      </View>
    </>
  );
};

export default LoginScene;

import {Platform, StyleSheet} from 'react-native';
import {Sizes} from 'common';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    content: {
      marginTop: '10%',
    },

    header: {
      backgroundColor: colors.background,
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
      padding: Sizes.xm,
    },
    footer: {
      height: '8%',
      width: '100%',
      backgroundColor: colors.transparentColor,
      justifyContent: 'space-around',
      flexDirection: 'row',
      shadowColor: colors.blackColor,
      shadowOffset: {width: 0, height: Sizes.x},
      shadowOpacity: 0.8,
      shadowRadius: Sizes.xs,
      elevation: Sizes.s,
    },

    filterClear: {
      paddingRight: '5%',
    },
    AccordionContainer: {
      alignSelf: 'center',
      width: '94%',
    },
    fill_star: {
      fontSize: Sizes.l,
      color: colors.goldenColor,
    },
    empty_star: {
      fontSize: Sizes.l,
      color: colors.blankGray,
    },
    checkboxContainer: {
      flexDirection: 'row',
    },

    rating: {
      marginLeft: Sizes.xm,
      flexDirection: 'row',
      alignItems: 'center',
    },

    checkBoxContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      left: 0,
      marginBottom: '1.5%',
    },

    checkBoxText: {
      marginLeft: Sizes.xm,
    },

    applyButton: {
      width: '45%',
      height: Sizes.x6l,
      backgroundColor: colors.smoothOrange,
    },
    cancelButton: {
      width: '45%',
      height: Sizes.x6l,
      backgroundColor: colors.lightGray,
    },
    clearAllFilter: {
      borderRadius: Sizes.xl,
      backgroundColor: colors.lightGray,
      justifyContent: 'center',
      padding: Sizes.xm,
    },

    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    rangeSliderStyle: {
      paddingTop: '1%',
      paddingBottom: '1%',
    },
    sliderValues: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingTop: '1%',
      paddingBottom: '5%',
    },

    counter: {fontSize: Sizes.mx, marginBottom: Sizes.xms},
    itemCenter: {
      textAlign: 'center',
    },
    saprater: {
      height: Sizes.s,
      width: '100%',
      backgroundColor: colors.smoothBlue,
      marginVertical: Sizes.xm,
    },
    scrollStyle: {flex: Sizes.x},
    range: {
      paddingVertical: Sizes.s,
      marginTop: Sizes.xs,
    },
    leftItem: {marginLeft: Sizes.s},
  });

export default styles;

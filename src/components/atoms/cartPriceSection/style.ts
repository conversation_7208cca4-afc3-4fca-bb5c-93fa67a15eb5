import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    priceDetailView: {
      marginHorizontal: Sizes.xm,
      paddingTop: Sizes.l,
      borderRadius: Sizes.mx,
      borderColor: colors.grey7,
      borderWidth: Sizes.x,
      backgroundColor: colors.background,
      shadowColor: colors.grey7,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: Sizes.s,
    },
    priceTitleView: {
      paddingHorizontal: Sizes.xm,
    },
    priceRow: {
      paddingHorizontal: Sizes.xm,
    },
    paddingSpace: {
      paddingVertical: Sizes.m,
    },
    rowView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    amountList: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: Sizes.m,
      paddingVertical: Sizes.s,
    },
    conditionView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.m,
    },
    flex: {
      flex: Sizes.x,
    },
    weight: {
      fontWeight: '500',
    },
    savedStyle: {
      backgroundColor: colors.whiteIce,
      borderRadius: Sizes.xm,
      paddingVertical: Sizes.xs,
      paddingHorizontal: Sizes.s,
      marginRight: Sizes.s,
    },
    savingView: {
      flexDirection: 'row',
      backgroundColor: colors.whiteIce,
      borderBottomLeftRadius: Sizes.xm,
      borderBottomRightRadius: Sizes.xm,
      paddingVertical: Sizes.xm,
      paddingHorizontal: Sizes.m,
    },
    rowWarpView: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
    },
    titleStyle: {
      marginRight: Sizes.xm,
    },
  });

export default styles;

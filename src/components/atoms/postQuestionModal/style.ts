import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
    },
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    postQuestionView: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.s,
    },
    answerView: {
      backgroundColor: colors.background,
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    postInput: {
      borderRadius: Sizes.sx,
      borderColor: colors.grey2,
      fontFamily: Fonts.Regular,
    },
    btnStyle: {
      alignSelf: 'flex-start',
    },
    onCheckoutView: {
      paddingVertical: Sizes.sx,
      borderRadius: Sizes.xms,
      width: '60%',
      height: Sizes.x34,
    },
    onCheckoutMainView: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

export default styles;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    modalContainer: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.modalShadow,
    },
    gifView: {
      width: Sizes.exl,
      height: Sizes.exl,
    },
    absoluteGifView: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
    },
  });

export default styles;

import React, { useMemo } from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import stylesWithOutColor from '../../../scenes/productDetail/style';
import { ImageIcon, Label, Separator, Spacer } from 'components/atoms';
import DashedLine from 'react-native-dashed-line';
import <PERSON>rror<PERSON>and<PERSON> from 'utils/ErrorHandler';
import { t } from 'i18next';
import { Sizes } from 'common';

const TAG = 'PoductDetailScreen';

type Props = {
    faqQuestions: any
    review: any
    filteredQuestions: any
    searchQuery: string
    getFaqLoading: boolean
    handleTextChange: (text: any) => void
    setSearchQuery: React.Dispatch<React.SetStateAction<string>>
    setShowFaqListModal: React.Dispatch<React.SetStateAction<boolean>>
    renderFaqItem: ({ item, index }: {
        item: any;
        index: any;
    }) => React.JSX.Element
  };

const FAQsComponent = ({
  faqQuestions,
  review,
  filteredQuestions,
  searchQuery,
  getFaqLoading,
  handleTextChange,
  setSearchQuery,
  setShowFaqListModal,
  renderFaqItem,
}: Props) => {
    const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View style={styles.fAQsView}>
      <View style={styles.viewSpace}>
        <View style={styles.fAQsMainView}>
          <View style={styles.fAQsSubView}>
            <Label
              text={t('PDP.FAQs')}
              size="l"
              weight="600"
              color="text"
            />
            {!!review?.review_meta?.verified_buyers > 0 && (
              <Label
                text={`${review?.review_meta?.verified_buyers} ${t(
                  'PDP.verifiedBuyers',
                )}`}
                size="mx"
                color="text2"
                weight="500"
              />
            )}
          </View>
        </View>
        <Spacer size="m" />
        <DashedLine
          dashLength={Sizes.xs}
          dashThickness={Sizes.x}
          dashColor={colors.grey5}
        />
        <Spacer size="m" />

        <View style={styles.deshedMainView}>
          <ImageIcon icon="search" size="xxl" tintColor="text2" />
          <Spacer size="xm" type="Horizontal" />
          <Separator color="pattensBlue" Vertical height="xl" />
          <Spacer size="xm" type="Horizontal" />
          <ErrorHandler
            componentName={`${TAG} TextInput`}
            onErrorComponent={<View />}>
            <TextInput
              testID="txtPdpSearchQuestion"
              onChangeText={handleTextChange}
              placeholder={t('PDP.searchQuestion')}
              placeholderTextColor={colors.text2}
              value={searchQuery}
              style={styles.searchByNameInput}
              allowFontScaling={false}
            />
          </ErrorHandler>
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Spacer size="xm" type="Horizontal" />
              <ImageIcon icon="cross" size="xsl" tintColor="text2" />
            </TouchableOpacity>
          )}

          {getFaqLoading ? <ActivityIndicator size="small" /> : null}
        </View>
        {filteredQuestions?.length > 0 ? (
          <FlatList
            data={filteredQuestions.slice(0, 2)}
            keyExtractor={(_, i) => i.toString()}
            renderItem={renderFaqItem}
            removeClippedSubviews={true}
            windowSize={5}
            maxToRenderPerBatch={5}
            updateCellsBatchingPeriod={50}
          />
        ) : (
          <Label
            color="textError"
            text={t('PDP.noResultFound')}
            size="lg"
            weight="500"
            style={styles.noResult}
          />
        )}
        {faqQuestions?.count > 2 && filteredQuestions?.length > 2 ? (
          <TouchableOpacity
            onPress={() => {
              setShowFaqListModal(true);
            }}
            style={styles.faqQuestionsView}>
            <Label
              text={`${t('PDP.viewAll1')} ${faqQuestions?.count} ${t(
                'PDP.questions',
              )}`}
              size="mx"
              weight="500"
              textTransform="capitalize"
              color="newSunnyOrange"
            />
            <ImageIcon icon="arrowRight" size="xx" tintColor="text" />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default  React.memo(FAQsComponent);
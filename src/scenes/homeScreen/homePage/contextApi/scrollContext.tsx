import React, {createContext, useRef, useState, useCallback} from 'react';
export const ScrollContext = createContext({
  flatListRefTop: null,
  scrollToTop: () => {},
  showScrollToTopButton: false,
  handleScroll: () => {},
});
export const ScrollProvider = ({children}) => {
  const flatListRefTop = useRef(null);
  const previousOffsetY = useRef(0);
  const [showScrollToTopButton, setShowScrollToTopButton] = useState(false);
  const scrollToTop = useCallback(() => {
    if (flatListRefTop.current) {
      flatListRefTop.current.scrollToOffset({offset: 0, animated: true});
    }
  }, []);
  // const handleScroll = useCallback((event) => {
  //   const offsetY = event.nativeEvent.contentOffset.y;
  //   debugLog(offsetY)
  //   if (offsetY > 50) {
  //     setShowScrollToTopButton(true);
  //   } else {
  //     setShowScrollToTopButton(false);
  //   }
  // }, []);

  const handleScroll = useCallback(event => {
    const offsetY = event.nativeEvent.contentOffset.y;
    if (offsetY > 0 && offsetY < previousOffsetY.current) {
      setShowScrollToTopButton(true);
    } else {
      setShowScrollToTopButton(false);
    }

    previousOffsetY.current = offsetY;
  }, []);

  return (
    <ScrollContext.Provider
      value={{
        flatListRefTop,
        scrollToTop,
        showScrollToTopButton,
        handleScroll,
      }}>
      {children}
    </ScrollContext.Provider>
  );
};

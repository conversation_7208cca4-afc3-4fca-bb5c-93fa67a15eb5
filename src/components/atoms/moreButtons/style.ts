import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    moreButton: {
      width: Sizes.x3l,
      height: Sizes.x3l,
      flex: 0,
    },
    container: {
      borderRadius: Sizes.xm,
      backgroundColor: colors.background,
      marginTop: Sizes.x4l,
      marginRight: Sizes.xl,
      borderWidth: Sizes.x,
      borderColor: colors.border,
      height: Sizes.ex + Sizes.xms,
      paddingVertical: Sizes.s,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.25,
      shadowRadius: Sizes.s,
      elevation: Sizes.s,
    },
    moreOptions: {
      width: Sizes.x3l,
      height: Sizes.x3l,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: colors.border,
    },
  });

export default styles;

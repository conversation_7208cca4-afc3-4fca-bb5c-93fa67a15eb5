import Api from 'services';
import {apiRoute, apiEndpoints, subEndpoint} from 'config/apiEndpoint';

const API = new Api();
export const getAllCategories = async () => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.categories}`;
  const resp = await API.get(url);
  return resp;
};
export const getCategoryDetails = async (categoryId: number, params?: any) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.categories}/${categoryId}/${subEndpoint.categories.details}`;
  const resp = await API.post(url, params);
  return resp;
};

export const getFilterProductData = async (category_id: number) => {
  const url = `${apiRoute.nodeServerless}${apiEndpoints.categories}/${subEndpoint.categories.filters}?category_id=${category_id}`;
  const resp = await API.get(url);
  return resp;
};

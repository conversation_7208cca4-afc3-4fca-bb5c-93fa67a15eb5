import {Platform} from 'react-native';
import axios from 'axios';
import {X_API_KEY, Version} from 'config/apiEndpoint';
import {showErrorMessage} from 'utils/show_messages';
import tokenClass from 'utils/token';
import perf from '@react-native-firebase/perf';
import {debugLog} from 'utils/debugLog';
import DeviceInfo from 'react-native-device-info';
import { useNavigation } from '@react-navigation/native';
import { store } from 'app-redux-store/store';
import { setLogoutAPI } from 'app-redux-store/slice/appSlice';


const headers = (data: any) => {
  return {
    'Content-Type':
      data && data.constructor === FormData
        ? 'multipart/form-data'
        : 'application/json',
  };
};
export default class Api {

  // Method to dispatch Redux actions
  private dispatchAction(action: any) {
    store.dispatch(action);
  }

  instance = async (
    headerConfig?: any,
    useToken = true,
    customApiKey?: string,
  ) => {
    const token = await tokenClass.getToken();
    // debugLog('token=========================', token);

    let header: ApiHeader = {
      Accept: 'application/json',
      'x-api-key': customApiKey || X_API_KEY,
      'user-agent': Platform.OS,
      Etag: customApiKey,
      platform: Platform.OS,
      version: Version,
      app_version: DeviceInfo.getVersion(),
      device_id: await DeviceInfo.getUniqueId(),
    };
    if (!!token) {
      header['Authorization'] = useToken ? 'Bearer ' + token : '';
    }
    header = {...header, ...headerConfig};
    // return axios.create({
    //   headers: header,
    //   timeout: 90000,
    // });
    const axiosInstance = axios.create({
      headers: header,
      timeout: 90000,
    });

    // Add request interceptor
    axiosInstance.interceptors.request.use(
      async config => {
        try {
          const httpMetric = perf().newHttpMetric(config.url, config.method);
          config.metadata = {httpMetric};

          // add any extra metric attributes, if required
          // httpMetric.putAttribute('userId', '12345678');

          await httpMetric.start();
        } finally {
          return config;
        }
        // return config;
      },
      error => {
        debugLog('Request Error:', error);
        // Handle request error
        return Promise.reject(error);
      },
    );

    // Add response interceptor
    axiosInstance.interceptors.response.use(
      async response => {
        try {
          // Request was successful, e.g. HTTP code 200

          const {httpMetric} = response.config.metadata;

          // add any extra metric attributes if needed
          // httpMetric.putAttribute('userId', '12345678');

          httpMetric.setHttpResponseCode(response.status);
          httpMetric.setResponseContentType(response.headers['content-type']);
          await httpMetric.stop();
        } finally {
          return response;
        }
      },
      async error => {
        try {
          // Request failed, e.g. HTTP code 500

          const {httpMetric} = error.config.metadata;

          // add any extra metric attributes if needed
          // httpMetric.putAttribute('userId', '12345678');

          httpMetric.setHttpResponseCode(error.response.status);
          httpMetric.setResponseContentType(
            error.response.headers['content-type'],
          );
          await httpMetric.stop();
        } finally {
          // Ensure failed requests throw after interception
          return Promise.reject(error);
        }
      },
    );

    return axiosInstance;
  };

  sendResponse = (response: any, resolve: any) => {
    if (response.status === 200 || 201) {
      if (response.data) {
        resolve({status: true, data: response.data});
      } else {
        resolve(response);
        resolve({status: true, data: response});
      }
    } else {
      showErrorMessage(response.problem);
      resolve(false);
      resolve({status: false, data: null});
    }
  };

  sendErrorResponse = async (error: any, resolve: any) => {
    const currentToken = await tokenClass.getToken();
    if (axios.isAxiosError(error)) {
      if (error.response) {
        // debugLog('Response data:', error.response.data);
        // debugLog('Response status:', error.response.status);
        // debugLog('Response headers:', error.response.headers);
        if (error?.response?.data?.message) {
          if (
            error?.response?.data?.code !== 'CART_NOT_FOUND' &&
            !error?.response?.data?.message.includes('dentalkart_custom_fee')
          ) {
            showErrorMessage(error?.response?.data?.message);
            if(currentToken && error.status === 401){this.dispatchAction(setLogoutAPI('Login'))}
          }
        }
        resolve({status: false, data: error.response.data});
      } else if (error.request) {
        debugLog('Request data:', error.request);
        resolve(false);
      } else {
        debugLog('Error message:', error.message);
        if (error?.message) {
          showErrorMessage(error.message);
        }
        resolve(false);
      }
    } else {
      debugLog('Unexpected error:', error);
      showErrorMessage(error);
      resolve(false);
    }
  };

  get = async (
    url: string,
    useToken?: boolean,
    params?: any,
    customApiKey?: string,
    header?: any,
  ) => {
    return new Promise(async resolve => {
      // debugLog('GET : ', url, params);
      const instance = await this.instance(
        header ? header : '',
        useToken,
        customApiKey,
      );
      instance
        .get(url, {params: params})
        .then(response => {
          this.sendResponse(response, resolve);
        })
        .catch((error: any) => {
          this.sendErrorResponse(error, resolve);
        });
    });
  };

  post = (url: string, data: any, customApiKey?: string) => {
    return new Promise(async resolve => {
      // debugLog('POST :======= ', url, data, customApiKey);
      const instance = await this.instance(headers(data), true, customApiKey);

      instance
        .post(url, data)
        .then(response => {
          this.sendResponse(response, resolve);
        })
        .catch((error: any) => {
          this.sendErrorResponse(error, resolve);
        });
    });
  };
  put = (url: string, data: any, customApiKey?: string) => {
    return new Promise(async resolve => {
      debugLog('PUT : ', url, data);
      const instance = await this.instance(headers(data), true, customApiKey);
      instance
        .put(url, data)
        .then(response => {
          this.sendResponse(response, resolve);
        })
        .catch((error: any) => {
          this.sendErrorResponse(error, resolve);
        });
    });
  };
  patch = (url: string, data: any) => {
    return new Promise(async resolve => {
      debugLog('PATCH : ', url);
      const instance = await this.instance(headers(data));
      instance
        .patch(url, data)
        .then(response => {
          this.sendResponse(response, resolve);
        })
        .catch((error: any) => {
          this.sendErrorResponse(error, resolve);
        });
    });
  };
  delete = (url: string) => {
    return new Promise(async resolve => {
      debugLog('DELETE : ', url);
      const instance = await this.instance(headers(''));
      instance
        .delete(url)
        .then(response => {
          this.sendResponse(response, resolve);
        })
        .catch((error: any) => {
          this.sendErrorResponse(error, resolve);
        });
    });
  };
}

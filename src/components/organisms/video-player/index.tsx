import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  TouchableWithoutFeedback,
  TouchableOpacity,
  View,
  Platform,
  ActivityIndicator,
} from 'react-native';
import Video from 'react-native-video';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {ImageIcon} from 'components/atoms';
import {useMemo} from 'react';
import FastImage from 'react-native-fast-image';

type Props = {
  id?: string;
  paused?: boolean;
  videoURL?: string;
  muteVideo?: boolean;
  getChildProgress?: (progress: PlaybackProgress, id: string) => void;
  item: Shorts;
  videoEnd?: boolean;
  previousVideoId?: number | undefined;
  onVideoEnd: (val: boolean, id: string) => void;
  onPause: () => void;
};

let disable = true;

const VideoPlayer = ({
  id,
  paused,
  videoURL,
  getChildProgress,
  muteVideo,
  item,
  videoEnd,
  onVideoEnd,
  onPause,
  previousVideoId,
}: Props) => {
  const refVideo = useRef([]);
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(null);
  const [duration, setDuration] = useState(0);
  const [videoReady, setVideoReady] = useState(false);

  useEffect(() => {
    if (previousVideoId !== id && videoEnd && disable) {
      onVideoEnd(false, id);
      disable = false;
      getChildProgress(progress, id);
      setTimeout(() => {
        disable = true;
      }, 2500);
    }
  }, [getChildProgress, paused, progress, id]);

  const onProgress = useCallback((data: any) => {
    setProgress(data);
  }, []);

  const onLoad = useCallback((data: any) => {
    setVideoReady(true);
    setDuration(data?.duration);
    setLoading(false);
  }, []);

  return (
    <View style={styles.container}>
      <TouchableWithoutFeedback onPress={() => onPause()} key={id}>
        <Video
          source={{
            uri: videoURL,
            type: 'mp4',
            headers: {
              range: 'bytes-0',
            },
          }}
          ref={(el: any) => (refVideo.current[id] = el)}
          paused={paused}
          muted={muteVideo || false}
          style={styles.player}
          repeat={true}
          resizeMode="contain"
          volume={1.0}
          rate={1.0}
          autoPlay={true}
          posterResizeMode="cover"
          poster={item?.thumbnail_url}
          bufferConfig={{
            minBufferMs: 15000, // Buffer size optimization
            maxBufferMs: 50000,
            bufferForPlaybackMs: 2500,
            bufferForPlaybackAfterRebufferMs: 5000,
          }}
          onProgress={onProgress}
          onLoad={onLoad}
          onEnd={() => onVideoEnd(true, id)}
          playInBackground={false}
          playWhenInactive={false}
          hideShutterView
          useTextureView={false}
          disableFocus
          progressUpdateInterval={Platform.OS === 'ios' ? 250 : 500}
          ignoreSilentSwitch={'ignore'}
          onBuffer={({ isBuffering }) => {
            setVideoReady(!isBuffering);
          }}
        />
      </TouchableWithoutFeedback>
      {!videoReady && (
        <FastImage
          style={styles.videoPlayerContainer}
          source={{uri: item?.thumbnail_url}}
        />
      )}
      {loading && (
        <View style={styles.playIconContainer}>
          <ActivityIndicator size="large" color={colors.background} />
        </View>
      )}

      {paused ? (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => onPause()}
          style={styles.playIconContainer}>
          <ImageIcon
            size="x5l"
            tintColor="white1"
            icon="play"
            style={styles.iconStyle}
          />
        </TouchableOpacity>
      ) : null}
      {progress ? (
        <View style={styles.progressBackground}>
          <View
            style={[
              styles.progressForground,
              {width: (progress.currentTime / duration) * 100 + '%'},
            ]}
          />
        </View>
      ) : null}
    </View>
  );
};

export default VideoPlayer;

import {Sizes} from 'common';
import {Platform, StyleSheet} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    subProductNameView: {paddingHorizontal: Sizes.m},
    optionalView: {
      borderColor: colors.sunnyOrange2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.sx,
    },
    tableItemLeft: {
      flex: Sizes.x + Sizes.z,
      borderTopColor: colors.grey2,
      borderRightColor: colors.grey2,
      borderTopWidth: Sizes.x,
      borderRightWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      padding: Sizes.s,
    },
    row: {flexDirection: 'row'},
    tableItemRight: {
      flex: Sizes.x,
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
    },
    thRight: {
      flex: Sizes.x,
      height: Sizes.x3l,
      justifyContent: 'center',
      alignItems: 'center',
    },
    thLeft: {
      flex: Sizes.x + Sizes.z,
      height: Sizes.x3l,
      justifyContent: 'center',
      alignItems: 'center',
      borderRightColor: colors.grey2,
      borderRightWidth: Sizes.x,
      paddingHorizontal: Sizes.s,
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    listContainer: {
      borderColor: colors.grey2,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
    },
    deliveryCont: {
      flexDirection: 'row',
      backgroundColor: colors.categoryTitle,
      borderTopLeftRadius: Sizes.xm,
      borderTopRightRadius: Sizes.xm,
    },
    tierPrices: {
      marginBottom: Sizes.sx,
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.categoryTitle2,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: Sizes.s,
      paddingHorizontal: Sizes.mx,
      marginRight: Sizes.xm,
    },
    transformedTierView: {
      flexDirection: 'row',
      // justifyContent: 'space-between',
    },
    seperator: {
      marginHorizontal: Sizes.xm,
    },
    infoIcon: {
      marginTop: -Sizes.xs,
    },
  });

export default styles;

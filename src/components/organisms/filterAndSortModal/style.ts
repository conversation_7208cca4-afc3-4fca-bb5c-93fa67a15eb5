import {Sizes, Fonts} from 'common';
import {StyleSheet} from 'react-native';

const Styles = (colors: Theme['colors']) => {
  return StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    flexContainer: {flex: Sizes.x},
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    modelBg: {
      flex: Sizes.s,
      backgroundColor: colors.background,
      borderTopLeftRadius: Sizes.mx,
      borderTopRightRadius: Sizes.mx,
      paddingHorizontal: Sizes.s,
    },
    flexOne: {
      flex: Sizes.x,
    },
    filterListLeftContainer: {
      flex: Sizes.xs,
      backgroundColor: colors.background,
    },
    filterListLeft: {
      flex: Sizes.x,
      backgroundColor: colors.offWhite,
      borderTopRightRadius: Sizes.m,
    },
    listItemLeft: {
      paddingVertical: Sizes.mx,
      paddingHorizontal: Sizes.xx,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
    },
    searchInput: {
      flexDirection: 'row',
      alignItems: 'center',
      height: Sizes.x8l,
    },
    filterListRightContainer: {
      flex: Sizes.xs + Sizes.x,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.m,
    },
    modalHeader: {
      paddingVertical: Sizes.xms,
      paddingTop: Sizes.l,
      paddingHorizontal: Sizes.xl,
      flexDirection: 'row',
      alignItems: 'center',
    },
    titleTxt: {
      flex: Sizes.x,
      textTransform: 'capitalize',
    },
    flexRow: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    filterSectionHeader: {
      height: Sizes.x54 + Sizes.x,
      paddingTop: Sizes.l,
      paddingLeft: Sizes.xx,
      borderBottomColor: colors.grey2,
      borderBottomWidth: 1,
    },
    quickFilterSectionHeader: {
      height: Sizes.x54 + Sizes.x,
      paddingTop: Sizes.l,
      paddingLeft: Sizes.xx,
    },
    rangeSliderStyle: {
      width: Sizes.ex3l,
    },
    rnSlider: {
      height: Sizes.ex218,
      justifyContent: 'center',
      alignItems: 'center',
      width: Sizes.x56,
      marginLeft: -Sizes.x3l,
    },
    sortContainer: {
      flex: Sizes.x,
      margin: Sizes.l,
    },
    rowCenteredItems: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rightSideHeader: {
      height: Sizes.x54,
    },
    selectAll: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingLeft: Sizes.x7l,
      paddingTop: Sizes.l,
      paddingRight: Sizes.xms,
    },
    filterList: {
      paddingTop: Sizes.m,
      paddingRight: Sizes.xms,
      flex: Sizes.x,
    },
    scrollView: {
      flex: Sizes.x,
    },
    listView: {
      paddingLeft: Sizes.x7l,
    },
    footerSection: {
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
      paddingVertical: Sizes.m,
      // paddingBottom: Sizes.x6l,
    },
    footerClearBtn: {
      fontSize: Sizes.mx,
      fontWeight: '500',
      color: colors.newSunnyOrange,
    },
    footerApplyBtn: {
      fontSize: Sizes.l,
      fontWeight: '500',
      color: colors.text2,
    },
    rangeLabelContainer: {
      // alignItems: 'center',
      justifyContent: 'space-between',
      // width: '50%',
      marginLeft: -5,
    },
    ratingView: {
      backgroundColor: colors.offWhite,
      borderRadius: Sizes.xm,
      paddingTop: Sizes.xm,
      paddingVertical: Sizes.l,
      paddingHorizontal: Sizes.m,
      marginLeft: Sizes.l,
      alignSelf: 'center',
    },
    ratingCheckboxRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: Sizes.xm,
    },
    starIconRow: {
      paddingBottom: Sizes.s,
      flexDirection: 'row',
    },
    rateIconStyle: {
      marginRight: 3,
    },
    checkBoxStyle: {
      height: Sizes.l,
      width: Sizes.l,
      fontFamily: Fonts.Medium,
    },
    btnClearTab: {
      marginHorizontal: Sizes.xl,
      height: Sizes.x8l + Sizes.x,
      borderWidth: Sizes.x,
      borderColor: colors.newSunnyOrange,
    },
    btnApplyTab: {
      marginRight: Sizes.xl,
      height: Sizes.x46 + Sizes.x,
    },
    btnClear: {
      width: Sizes.ex172 + Sizes.x,
      height: Sizes.x46 + Sizes.x,
      alignSelf: 'flex-start',
      marginLeft: Sizes.xl,
      padding: 0,
    },
    btnApply: {
      width: Sizes.ex172 + Sizes.x,
      height: Sizes.x46 + Sizes.x,
      alignSelf: 'flex-end',
      marginRight: Sizes.xl,
      padding: 0,
    },
    listCheckBox: {
      height: Sizes.xx,
      width: Sizes.xx,
    },
    markerview: {
      backgroundColor: colors.newSunnyOrange,
      height: Sizes.l,
      width: Sizes.l,
    },
    selectedView: {
      backgroundColor: colors.newSunnyOrange,
      borderWidth: Sizes.x + Sizes.z,
      borderColor: colors.newSunnyOrange,
    },
    unselectedView: {
      backgroundColor: colors.grey2,
      borderWidth: Sizes.x + Sizes.z,
      borderColor: colors.grey2,
    },
    sliderView: {
      marginLeft: Sizes.x3l,
    },
    sliderMainView: {
      flexDirection: 'row',
    },
    productTxt: {
      marginLeft: Sizes.xxl,
      marginTop: Sizes.xm,
    },
    searchStyle: {
      flex: Sizes.x,
      color: colors.text2,
      fontFamily: Fonts.Regular,
      fontSize: Sizes.mx,
      marginBottom: -Sizes.sx,
      height: Sizes.x46,
    },
    touchable: {backgroundColor: 'green'},
    safeView: {backgroundColor: colors.background},
    rangeLabel: {
      alignItems: 'center',
      justifyContent: 'space-between',
      width: Sizes.x3l,
    },
    roundView: {
      marginBottom: -Sizes.x,
    },
  });
};

export default Styles;

import React from 'react';
import {Text, TouchableOpacity, View, SafeAreaView} from 'react-native';
import NetworkLogger from 'react-native-network-logger';
import {NavigationProp, ParamListBase} from '@react-navigation/native';
import NetworkLoggerStyles from './style';

interface ScreenNetworkLoggerProps {
  navigation: NavigationProp<ParamListBase>;
}
const styles = NetworkLoggerStyles();
const ScreenNetworkLogger: React.FC<ScreenNetworkLoggerProps> = ({
  navigation,
}) => (
  <SafeAreaView style={styles.container}>
    <NetworkLogger />
    <TouchableOpacity onPress={() => navigation.goBack()}>
      <View style={styles.textContainer}>
        <Text style={styles.backText}>Back</Text>
      </View>
    </TouchableOpacity>
  </SafeAreaView>
);

export default ScreenNetworkLogger;

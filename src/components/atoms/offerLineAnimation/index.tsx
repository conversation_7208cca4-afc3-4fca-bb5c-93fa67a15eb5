import React, {useEffect, useRef, useState} from 'react';
import {Animated} from 'react-native';
import {useTheme} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import FastImage from 'react-native-fast-image';
import {Label} from 'components/atoms';
import {DeviceWidth} from 'config/environment';
import Icons from 'common/icons';
import stylesWithOutColor from './style';
import {useMemo} from 'react';

type props = {
  text: string;
  text1: string;
};

const OfferLineAnimation = (props: props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {text, text1} = props;
  const translateY = useRef(new Animated.Value(DeviceWidth)).current;
  const translateY1 = useRef(new Animated.Value(DeviceWidth)).current;
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (!isAnimating) {
      animationText();
      const interval = setInterval(() => {
        if (!isAnimating) {
          animationText();
        }
      }, 3000);
      // 2 text timer 5500 and single text 3000
      return () => clearInterval(interval);
    }
  }, [translateY, translateY1, isAnimating]);

  const animationText = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    Animated.sequence([
      // Animated.timing(translateY, {
      //   toValue: 0,
      //   duration: 1000,
      //   useNativeDriver: true,
      // }),
      // Animated.delay(1000),
      // Animated.timing(translateY, {
      //   toValue: -30,
      //   duration: 1000,
      //   useNativeDriver: true,
      // }),
      // Animated.delay(500),
      Animated.timing(translateY1, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.delay(2000),
      Animated.timing(translateY1, {
        toValue: -30,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // translateY.setValue(DeviceWidth);
      translateY1.setValue(DeviceWidth);
      setIsAnimating(false);
    });
  };

  return (
    <LinearGradient
      useAngle
      angle={90}
      style={styles.linerColor}
      colors={[colors.skyBlue15, colors.red1]}>
      <FastImage
        resizeMode="stretch"
        style={[styles.celebrateGifView, styles.leftGif]}
        source={Icons.celebrateGif}
      />
      {/* <Animated.View style={[styles.animatedText, {transform: [{translateY}]}]}>
        <Label
          text={text}
          fontFamily="Medium"
          size="m"
          color="whiteColor"
          align="center"
          textTransform="capitalize"
        />
      </Animated.View> */}
      <Animated.View
        style={[styles.animatedText, {transform: [{translateY: translateY1}]}]}>
        <Label
          text={text1}
          fontFamily="Medium"
          size="m"
          color="whiteColor"
          align="center"
          textTransform="capitalize"
        />
      </Animated.View>
      <FastImage
        resizeMode="stretch"
        style={[styles.celebrateGifView, styles.rightGif]}
        source={Icons.celebrateGif}
      />
    </LinearGradient>
  );
};

export default React.memo(OfferLineAnimation);
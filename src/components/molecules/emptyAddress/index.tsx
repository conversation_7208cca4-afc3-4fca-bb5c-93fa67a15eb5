import React, {useMemo} from 'react';
import {View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import FastImage from 'react-native-fast-image';
import {Button} from 'components/molecules';
import Icons from 'common/icons';
import {Spacer, Label} from 'components/atoms';
import {t} from 'i18next';

const EmptyAddress = props => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View style={styles.mainView}>
      <View style={styles.wrapper}>
        <View style={styles.imageView}>
          <FastImage
            resizeMode="contain"
            style={styles.bgImage}
            source={Icons.emptyAddressBG}
          />
          <FastImage
            resizeMode="contain"
            style={styles.emptyImage}
            source={Icons.emptyAddressGif}
          />
        </View>
        <Spacer size="xxl" />
        <Label
          color="text"
          text={t('addressCard.addAddressNow')}
          fontFamily="SemiBold"
          size="l"
        />
        <Spacer size="l" />
        <Button
          onPress={() => props?.onClick()}
          radius="m"
          type="secondary"
          labelSize="mx"
          labelStyle={styles.labelStyle}
          labelColor="whiteColor"
          text={t('addressList.addAddress')}
        />
      </View>
      <Spacer size="xl" />
    </View>
  );
};

export default EmptyAddress;

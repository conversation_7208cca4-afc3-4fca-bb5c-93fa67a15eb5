import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    modalView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xms,
      paddingHorizontal: Sizes.mx,
      paddingVertical: Sizes.m,
    },
    modalSubView: {
      justifyContent: 'center',
      flexDirection: 'row',
    },
    subView: {
      flex: Sizes.xs,
    },
    borderBottomView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    borderLine: {
      width: 1.5,
      height: Sizes.x3l,
      backgroundColor: colors.aliceBlue3,
    },
    btnItemView: {
      justifyContent: 'space-around',
      flexDirection: 'row',
      alignItems: 'center',
    },
    btn1View: {
      flex: Sizes.x,
      alignItems: 'center',
    },
    btn2View: {
      justifyContent: 'space-around',
      flexDirection: 'row',
      flex: Sizes.x,
    },
    flex: {
      flex: Sizes.x,
    },
  });

export default styles;

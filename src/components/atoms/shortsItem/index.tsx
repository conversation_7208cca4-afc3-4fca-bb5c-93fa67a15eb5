import React, {useMemo, memo} from 'react';
import {TouchableOpacity, View, Share} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {Label, Spacer, ImageIcon} from 'components/atoms';
import {RootStackParamsList} from 'routes';
import LinearGradient from 'react-native-linear-gradient';
import {useSelector} from 'react-redux';
import {showInfoMessage} from 'utils/show_messages';
import {t} from 'i18next';
import { debugLog } from 'utils/debugLog';
import FastImage from 'react-native-fast-image';

let disable = true;

type Props = {
  item: Shorts;
  index: number;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  callBack?: () => void;
  onAddLike?: (item: Shorts, i: number) => void;
};

const ShortsItem = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {navigation, item, index, callBack, onAddLike} = props;
  const {isLoggedIn} = useSelector((state: RootState) => state.app);

  const onShare = () => {
    Share.share(
      {
        message: `${item.title} \n\n${item.video_url} \n\n${item?.caption}`,
      },
      {dialogTitle: 'Share on ..', tintColor: 'green'},
    ).catch(err => debugLog(err));
  };

  const onLike = () => {
    if (isLoggedIn) {
      if (disable) {
        disable = false;
        onAddLike(item, index);
        setTimeout(() => {
          disable = true;
        }, 500);
      }
    } else {
      showInfoMessage(t('shorts.loginLikeVideo'));
      navigation.navigate('Login');
    }
  };

  return (
    <TouchableOpacity
      style={[styles.itemContainer, {marginRight: index % 2 == 0 ? 8 : 0}]}
      onPress={() => {
        navigation.navigate('ShortVideos', {
          videoId: item.id,
          source: 'THUMBNAIL',
          videoItem: item,
          callBack,
        });
      }}
      key={index}>
      <FastImage
        style={styles.img}
        source={{uri: item.thumbnail_url}}
        resizeMode={FastImage.resizeMode.stretch}
      />
      <LinearGradient
        colors={[
          colors.blackTransparent, 
          colors.black0
        ]}
        style={styles.gradient}
        start={{x: 0, y: 1}}
        end={{x: 0, y: 0}}
        >
        <View style={styles.reelIcon}>
          {/* <TouchableOpacity onPress={() => onShare()}>
          <TouchableOpacity onPress={() => onShare()} 
           hitSlop={styles.hitSlopStyle} 
           style={styles.shareButton}>
            <ImageIcon size="xl" tintColor="white1" icon="newShare" />
          </TouchableOpacity> */}
          <Spacer size="m" />
          <ImageIcon size="xxl" tintColor="white1" icon="eyeWhite" />
          <Label
            text={item.totalWatchCount}
            style={styles.countTxt}
            align="center"
          />
          <TouchableOpacity onPress={() => onLike()}>
            <ImageIcon
              size="xxl"
              tintColor={item?.isLiked === 1 ? 'redIcon' : 'white1'}
              icon={item.isLiked === 1 ? 'heartLikedIcon' : 'heartNotLikedIcon'}
            />
            <Label
              text={item.like_count}
              style={styles.countTxt}
              align="center"
            />
          </TouchableOpacity>

          {/* <TouchableOpacity>
            <ImageIcon
              size="xl"
              tintColor="white1"
              icon={false ? 'saved' : 'newBookmarkIcon'}
            />
          </TouchableOpacity>
          <Spacer size="xm" /> */}
        </View>
        <View style={styles.titlesView}>
          <Label
            text={item.author_name}
            size="mx"
            numberOfLines={1}
            ellipsizeMode="tail"
            fontFamily="Medium"
            color="whiteColor"
            style={styles.textCap}
          />
          <Label
            text={item.title}
            size="mx"
            numberOfLines={2}
            ellipsizeMode="tail"
            fontFamily="Regular"
            color="whiteColor"
            style={styles.textCap}
          />
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default memo(ShortsItem);

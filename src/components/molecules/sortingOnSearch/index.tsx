import React, {useCallback} from 'react';
import {
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  ViewProps,
} from 'react-native';
import {ImageIcon, Label, Radio} from 'components/atoms';
import stylesWithOutColor from './style';
import {Sizes} from 'common';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  onSelect?: () => void;
  isEdit?: boolean;
  style?: ViewProps['style'];
  cartAddress?: boolean;
  addressList?: [CustomerAddressV2];
} & AddressType;

const SortingOnSearch = ({
  setModelVisible,
  setSelectedSortValue,
  selectedSortValue,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const sort = [
    {label: 'Popularity', key: ''},
    {label: 'Price -- Low to High', key: 'ASC'},
    {label: 'Price -- High to Low', key: 'DSC'},
  ];
  const onSortSelect = useCallback(
    (type: string) => {
      setSelectedSortValue(type);
      setModelVisible(false);
    },
    [setModelVisible, setSelectedSortValue],
  );
  return (
    <View style={Platform.OS === 'ios' ? styles.modelStyleIos : null}>
      <TouchableOpacity
        onPress={() => {
          setModelVisible(false);
        }}
        style={styles.closeButton}>
        <ImageIcon size="xl" icon={'closeIcons'} />
      </TouchableOpacity>
      <View style={[styles.modelStyleIos, {paddingHorizontal: Sizes.xl}]}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {sort.map((sortby, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => onSortSelect(sortby.key)}
                style={styles.optionsStyle}>
                <Radio
                  onPress={() => onSortSelect(sortby.key)}
                  selected={selectedSortValue === sortby.key}
                />
                <Label style={styles.modelText} text={sortby.label} />
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    </View>
  );
};

export default SortingOnSearch;

import React, {useMemo} from 'react';
import {View, SafeAreaView} from 'react-native';
import {useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {Label, Spacer} from 'components/atoms';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import Icons from 'common/icons';
import {t} from 'i18next';

type OfflineNoticeProps = {
  retry: () => void;
};

const OfflineNotice: React.FC<OfflineNoticeProps> = ({retry}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.view}>
        <FastImage
          style={styles.image}
          source={Icons.noInternet}
          resizeMode="contain"
        />
        <Spacer size="l" />
        <Label text={t('internet.title')} size="l" weight="600" color="text" />
        <Spacer size="s" />
        <Label text={t('internet.msg')} size="mx" weight="500" color="text2" />
        <Spacer size="xxl" />
        <Button
          onPress={retry}
          style={styles.btnStyle}
          radius="xm"
          type="secondary"
          labelSize="mx"
          labelColor="whiteColor"
          labelStyle={styles.btnText}
          text={t('buttons.retry')}
        />
      </View>
    </SafeAreaView>
  );
};

export default OfflineNotice;

import React, {useMemo, useState} from 'react';
import {View, TouchableOpacity, FlatList} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {benefits} from 'staticData';

const AccordionView = () => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [accordionActiveIndex, setAccordionActiveIndex] = useState<number>(-1);

  return (
    <View style={styles.accordionMainView}>
      <FlatList
        data={benefits}
        keyExtractor={(_, i) => i.toString()}
        ItemSeparatorComponent={<View style={styles.accordionLine} />}
        renderItem={({item, index}: {item: BenefitsInfo; index: number}) => {
          return (
            <View key={index}>
              <TouchableOpacity
                testID='tOAccordionView'
                style={styles.accordionView}
                onPress={() =>
                  setAccordionActiveIndex(
                    accordionActiveIndex === index ? -1 : index,
                  )
                }>
                <Label
                  text={item.label}
                  size="l"
                  fontFamily="Medium"
                  color="text"
                  numberOfLines={1}
                  style={styles.flex}
                />
                <Spacer type="Vertical" size="xm" />
                <ImageIcon
                  icon={
                    accordionActiveIndex === index
                      ? 'upArrowBlack'
                      : 'downArrowBlack'
                  }
                  tintColor="text"
                  size="xl"
                  resizeMode="contain"
                />
              </TouchableOpacity>
              {accordionActiveIndex === index &&
                item.content.map((content: string, i: number) => (
                  <View style={styles.childList} key={i}>
                    <Label
                      text={`•  `}
                      color="text2"
                      size="xms"
                      fontFamily="Regular"
                    />
                    <Label
                      text={content}
                      color="text2"
                      size="xms"
                      fontFamily="Regular"
                      style={styles.flex}
                    />
                  </View>
                ))}
            </View>
          );
        }}
      />
    </View>
  );
};

export default AccordionView;

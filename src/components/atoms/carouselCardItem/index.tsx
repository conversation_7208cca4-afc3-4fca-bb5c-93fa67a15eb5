import Icons from 'common/icons';
import React, {useMemo} from 'react';
import {TouchableOpacity, ViewProps} from 'react-native-gesture-handler';
import FastImage from 'react-native-fast-image';
import styles from './style';
import getImageUrl from 'utils/imageUrlHelper';

type Item = {
  mobile_image?: string;
  mobile_img?: string;
  thumbnail_url?: string;
  file?: string;
  media?: {mobile_image?: string};
};

type Props = {
  icon?: keyof typeof Icons;
  card?: boolean;
  index: number;
  style?: ViewProps['style'];
  onPress: () => void;
  item: Item;
  mode: string;
  bannerImgStyle?: ViewProps['style'];
};

const CarouselCardItem: React.FC<Props> = ({
  item,
  card = false,
  style,
  onPress,
  mode,
  bannerImgStyle,
}) => {
  const image = useMemo(
    () =>
      getImageUrl(
        item.mobile_image ||
          item.mobile_img ||
          item.file ||
          item.thumbnail_url ||
          item.media?.mobile_image ||
          'https://via.placeholder.com/500', // Default fallback image
      ),
    [item],
  );

  return (
    <TouchableOpacity
    testID='tOCarouselCardItem'
      activeOpacity={0.8}
      onPress={onPress}
      style={[card && styles.imageCard, style]}>
      <FastImage
        style={[
          styles.banner,
          card && styles.card,
          mode !== 'parallax' && styles.subBanner,
          bannerImgStyle,
        ]}
        resizeMode="stretch"
        source={{uri: image, priority: FastImage.priority.high}}
      />
    </TouchableOpacity>
  );
};

export default React.memo(CarouselCardItem);

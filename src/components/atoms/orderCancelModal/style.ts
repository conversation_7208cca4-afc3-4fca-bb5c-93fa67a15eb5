import {Fonts, Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    mainViewModel: {
      backgroundColor: colors.modalShadow,
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      // paddingHorizontal: Sizes.xl,
      overflow: 'hidden',
    },
    subViewModel: {
      width: '100%',
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
      marginTop: Sizes.xms,
      paddingVertical: Sizes.xx,
      paddingHorizontal: Sizes.xms,
    },
    subViewModel1: {
      width: '100%',
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
      paddingHorizontal: Sizes.xms,
      paddingBottom:Sizes.ex3l,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      position: 'absolute',
      top: -Sizes.x8l,
      alignSelf: 'center',
    },
    placeHolderText: {
      color: colors.text2,
      fontFamily: Fonts.Medium,
      fontSize: Sizes.mx,
    },
    dropDown: {
      height: Sizes.x7l,
      borderRadius: Sizes.xm,
      borderColor: colors.grey2,
    },
    reasonView: {
      justifyContent: 'space-between',
      padding: Sizes.xm,
      paddingVertical: Sizes.s,
    },
    btnView: {
      marginTop: Sizes.xl,
    },
    dropDownTextStyle: {
      color: 'red',
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
      marginBottom: -Sizes.xs,
    },
    searchTextStyle: {
      color: colors.text2,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
      paddingBottom: Sizes.s,
    },
    downImg: {
      transform: [{rotate: '180deg'}],
    },
    listContainer: {
      paddingBottom: Sizes.xms,
      borderRadius: Sizes.xs,
      marginTop: Sizes.xs,
    },
    iconStyle: {
      marginTop: Sizes.x,
    },
  });

export default styles;

default_platform(:android)

# Helper method to automatically get and increment the new version name
def get_new_version_name(current_version_name)
  # Split the current version name into its parts
  parts = current_version_name.split('.').map(&:to_i)

  if parts.length == 3
    major, minor, patch = parts

    # Determine how to increment the version
    if patch < 9
      patch += 1
    elsif minor < 9
      patch = 0
      minor += 1
    else
      patch = 0
      minor = 0
      major += 1
    end

    # Construct the new version name
    new_version_name = "#{major}.#{minor}.#{patch}"
    return new_version_name
  else
    UI.user_error!("Invalid version format: #{current_version_name}")
  end
end

# Lane for deploying to the internal track based on flavor
lane :deploy_internal do |options|
  # Read the flavor from options or set a default one
  flavor = options[:flavor] || "production"

  # Set the package name based on the flavor
  package_name = case flavor
  when "production"
    "com.vasadental.dentalkart"
  when "staging"
    "com.vasadental.dentalkart.staging"
  when "dev"
    "com.vasadental.dentalkart.dev"
  else
    UI.user_error!("Unknown flavor: #{flavor}")
  end

  # Increment version code
  gradle_file_path = "app/build.gradle"
  increment_version_code(gradle_file_path: gradle_file_path)
  puts "flavor: #{flavor}"

  # # Only update version name for production
  # if flavor == "production"
  #   new_version_name = android_get_version_name(gradle_file: gradle_file_path)
  #   puts "Current Version Name: #{new_version_name}"
  #   new_version_name = get_new_version_name(new_version_name)

  #   android_set_version_name(
  #     gradle_file: gradle_file_path,
  #     version_name: new_version_name
  #   )
  # else
  #   puts "Skipping version name update for flavor: #{flavor}"
  # end
  aab_path = "app/build/outputs/bundle/#{flavor}Release/app-#{flavor}-release.aab"
  puts "Bundle file: #{aab_path}"
  # Build APKs for production and .aab for dev and staging
  if flavor == "production"
      gradle(
        task: "bundle#{flavor.capitalize}",
       build_type: "Release"
      )
     # Path to the generated AAB file
    firebase_app_distribution(
      app: "1:285108782909:android:7f1bc9cf93ee9152", # Firebase App ID from the Firebase Console
     # testers: "<EMAIL>,<EMAIL>", # Optional: List of testers
      groups: "primary-testing-team", # Optional: List of tester groups
      release_notes_file: "./fastlane/release_notes.txt",
      service_credentials_file: "./fastlane/dentalkart-app-test-firebase-adminsdk-c8t95-e94749f5e1.json",
      android_artifact_type:"AAB",
      android_artifact_path: aab_path, # Path to the built APK
      debug: false
    )
    # Upload the AAB to Google Play
    upload_to_play_store(
      track: 'internal',
      aab: aab_path,
      skip_upload_aab: false,
      release_status: 'completed',
      package_name: package_name
    )
  # end
  else
     gradle(
        task: "assemble#{flavor.capitalize}",
       build_type: "Release"
      )
    firebase_app_distribution(
      app: "1:285108782909:android:7f1bc9cf93ee9152", # Firebase App ID from the Firebase Console
      #groups: "primary-testing-team", # Optional: List of tester groups
      release_notes_file: "./fastlane/release_notes.txt",
      service_credentials_file: "./fastlane/dentalkart-app-test-firebase-adminsdk-c8t95-e94749f5e1.json",
      android_artifact_type:"APK",
      android_artifact_path: "app/build/outputs/apk/staging/release/app-staging-release.apk", # Path to the built APK
      debug: false
    )
  end
end

# Specific lanes for each flavor
lane :deploy_production do
  deploy_internal(flavor: "production")
end

lane :deploy_production_firebase do
     gradle(
        task: "assembleproduction",
       build_type: "Release"
      )
    firebase_app_distribution(
      app: "1:285108782909:android:7f1bc9cf93ee9152", # Firebase App ID from the Firebase Console
      groups: "primary-testing-team", # Optional: List of tester groups
      release_notes_file: "./fastlane/release_notes.txt",
      service_credentials_file: "./fastlane/dentalkart-app-test-firebase-adminsdk-c8t95-e94749f5e1.json",
      android_artifact_type:"APK",
      android_artifact_path: "app/build/outputs/apk/production/release/app-production-release.apk", # Path to the built APK
      debug: false
    )
end

lane :deploy_staging do
  deploy_internal(flavor: "staging")
end

lane :deploy_dev do
  deploy_internal(flavor: "dev")
end

lane :DevAPK do 
  gradle(
    task: "assemble",
    build_type: "Release",
     flavor: 'Dev'
  )
end

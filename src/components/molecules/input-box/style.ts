import {Sizes, Fonts} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      width: '100%',
      borderWidth: Sizes.x,
      borderColor: colors.border,
      borderRadius: Sizes.xm,
      flexDirection: 'row',
    },
    textInputColor: {
      color: colors.grey2,
    },
    input: {
      paddingHorizontal: Sizes.m,
      fontSize: Sizes.l,
      fontFamily: Fonts.Regular,
      backgroundColor: colors.background,
      width: '100%',
      color: colors.textLight,
      borderRadius: Sizes.xm,
      height: Sizes.x46,
    },
    errorContainer: {
      marginTop: Sizes.s,
      alignSelf: 'flex-start',
      paddingLeft: Sizes.xs,
    },
    errorBorder: {
      borderColor: colors.textError,
    },
  });

export default styles;

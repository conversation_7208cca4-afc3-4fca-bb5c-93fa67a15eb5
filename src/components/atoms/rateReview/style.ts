import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    ratingsView: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.m,
      backgroundColor: colors.background,
      shadowColor: colors.black,
      marginHorizontal: Sizes.m,
      shadowOffset: {
        width: 0,
        height: Sizes.x,
      },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
      elevation: Sizes.xs,
    },
    reviewView: {
      padding: Sizes.m,
    },
    rateProdView: {
      flex: Sizes.x,
      alignItems: 'flex-end',
    },
    ratingMainView: {
      flex: Sizes.x,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    ratingSubView: {
      flex: Sizes.x,
      borderRightColor: colors.grey7,
      borderRightWidth: Sizes.x,
    },
    reviewImageView: {
      borderRadius: Sizes.sx,
    },
    avgRatingView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xm,
      paddingVertical: Sizes.s,
      borderRadius: Sizes.s,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      alignItems: 'center',
      alignSelf: 'flex-start',
      height: 27,
    },
    rateProductView: {
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
      borderColor: colors.newSunnyOrange,
      paddingHorizontal: Sizes.xl,
      paddingVertical: Sizes.sx,
      backgroundColor: colors.whiteColor,
      height: Sizes.x34,
    },
    ratingView: {
      borderBottomColor: colors.grey2,
      borderBottomWidth: Sizes.x,
      paddingVertical: Sizes.m,
    },
    starIconView: {
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.s,
    },
    flex: {
      flex: Sizes.x,
    },
    nicNameView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    totalReview: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: Sizes.m,
    },
    likeDisView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    fRow: {
      flexDirection: 'row',
    },
    underPrizeIconView: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.darkBlue,
      zIndex: Sizes.x,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xm,
    },
    priceIcon: {
      paddingRight: Sizes.xm,
    },
    reviewRate: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rateProdSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
  });

export default styles;

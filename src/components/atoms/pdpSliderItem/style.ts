import {Sizes, Fonts} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    visibleFullScreenView: {
      borderBottomLeftRadius: Sizes.l,
      borderBottomRightRadius: Sizes.l,
    },
    visibleSubScreenView: {
      height: 423,
      width: '100%',
      borderBottomLeftRadius: Sizes.mx,
      borderBottomRightRadius: Sizes.mx,
    },
    sliderView: {
      height: 430,
    },
    fullScreenImgView: {
      position: 'absolute',
      zIndex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
      justifyContent: 'space-between',
      top: Sizes.m,
    },
    productImgView: {
      position: 'absolute',
      zIndex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      bottom: Sizes.x8l,
      right: Sizes.m,
      height: Sizes.x4l,
      width: Sizes.x4l,
      backgroundColor: colors.white46,
      borderRadius: Sizes.l,
    },
    bestSellerTag: {
      height: Sizes.xxl,
      width: Sizes.ex,
    },
    heartView: {
      marginRight: Sizes.m,
      backgroundColor: colors.white46,
      height: Sizes.x4l,
      width: Sizes.x4l,
      borderRadius: Sizes.l,
      justifyContent: 'center',
      alignItems: 'center',
    },
    shareView: {
      right: Sizes.m,
      backgroundColor: colors.white46,
      height: Sizes.x4l,
      width: Sizes.x4l,
      borderRadius: Sizes.l,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      top: Sizes.x52,
    },
    deliveryView: {
      position: 'absolute',
      zIndex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
      justifyContent: 'space-between',
      bottom: Sizes.mx,
      paddingHorizontal: Sizes.m,
    },
    tagView: {
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.s,
      height: Sizes.xxxl,
    },
    tagLabel: {
      color: colors.text2,
      fontSize: 11,
      fontFamily: Fonts.SemiBold,
    },
    scrollToView: {
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      backgroundColor: colors.whiteSmoke35,
      height: Sizes.xxxl,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: Sizes.xm,
    },
    rateFillStar: {
      marginTop: -Sizes.xs,
    },
  });

export default styles;

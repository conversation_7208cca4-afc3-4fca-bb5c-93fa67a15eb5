import {Fonts, Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    modalHeaderView: {
      width: '100%',
      height: '100%',
      backgroundColor: colors.modalShadow,
      paddingTop: '12%',
    },
    accountMainContainer: {
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
      backgroundColor: colors.whiteColor,
      flex: Sizes.x,
    },
    alignCenter: {
      alignItems: 'center',
    },
    accountSubContainer: {
      backgroundColor: colors.text,
      borderTopLeftRadius: Sizes.xl,
      borderTopRightRadius: Sizes.xl,
      position: 'relative',
    },
    topBarArrowView: {
      alignItems: 'center',
      paddingTop: Sizes.m,
    },
    topBarArrowSubContainer: {
      paddingHorizontal: Sizes.mx,
    },
    linearContainer: {
      alignItems: 'center',
      alignSelf: 'flex-end',
      justifyContent: 'center',
      paddingBottom: Sizes.x6l,
    },
    linearGradient: {
      height: Sizes.x4l,
      paddingHorizontal: Sizes.xx,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      justifyContent: 'center',
    },
    orderSectionView: {
      borderRadius: Sizes.xms,
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.sx,
      backgroundColor: colors.whiteColor,
      position: 'absolute',
      alignSelf: 'center',
      bottom: -(Sizes.x6l + Sizes.m),
      elevation: Sizes.s,
      justifyContent: 'space-between',
      flexDirection: 'row',
    },
    itemView: {
      flex: Sizes.x,
      alignItems: 'center',
    },
    accountContainer: {
      paddingHorizontal: Sizes.mx,
      backgroundColor: colors.whiteColor,
    },
    accountContainerList: {
      marginTop: Sizes.x70,
      backgroundColor: colors.aliceBlue1,
    },
    menuListView: {
      flex: Sizes.x,
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
      borderBottomWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingVertical: Sizes.mx,
    },
    menuSubListView: {
      flex: Sizes.x,
      alignItems: 'center',
      flexDirection: 'row',
    },
    boxViewList: {
      backgroundColor: colors.aliceBlue1,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.m,
    },
    boxViewListView: {
      width: (DeviceWidth - Sizes.x5l) / Sizes.xs,
      alignItems: 'center',
      justifyContent: 'center',
      padding: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.xm,
    },
    notificationView: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.newSunnyOrange,
      width: Sizes.l,
      height: Sizes.l,
      borderRadius: Sizes.xms,
      position: 'absolute',
      right: 0,
      top: -Sizes.sx,
    },
    notificationContainer: {
      width: '8%',
      position: 'relative',
    },
    didUKnowView: {
      marginTop: Sizes.xm,
      backgroundColor: colors.whiteColor,
      padding: Sizes.mx,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
    },
    triangle: {
      width: 0,
      height: 0,
      backgroundColor: 'transparent',
      borderStyle: 'solid',
      borderLeftWidth: Sizes.xl,
      borderRightWidth: Sizes.xl,
      borderBottomWidth: Sizes.xl,
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderBottomColor: colors.whiteColor,
      position: 'absolute',
      top: -Sizes.s,
      right: Sizes.sx,
    },
    marginL10: {
      marginLeft: Sizes.xm,
    },
    spaceList: {
      justifyContent: 'space-between',
    },
    flexOne: {
      flex: Sizes.x,
      pointerEvents: 'box-none', // Allows touches to pass through
    },
    textCap: {
      textTransform: 'capitalize',
    },
    percentageInfoContainer: {
      left: Sizes.xl,
      paddingVertical: Sizes.x,
      paddingHorizontal: Sizes.xm,
      gap: Sizes.s,
      borderRadius: Sizes.xs,
      backgroundColor: colors.green4,
    },
    percentageText: {
      fontFamily: Fonts.Medium,
      fontSize: Sizes.xms,
      textAlign: 'left',
      color: colors.green2,
    },
    rowContainer: {
      flexDirection: 'row', // Align items in a row
      justifyContent: 'space-between', // Space out the elements evenly
      marginBottom: 10, // Adds spacing between rows
    },
    memberStyle: {
      fontSize: Sizes.m,
      fontFamily: Fonts.Bold,
    },
  });

export default styles;

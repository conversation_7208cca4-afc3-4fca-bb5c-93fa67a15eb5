import {Sizes, Fonts} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalView: {
      flex: Sizes.x,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.blackTransparent,
    },
    mainView: {
      backgroundColor: colors.background,
      borderRadius: Sizes.xl,
      padding: Sizes.xms,
    },
    centerView: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    imageView: {
      height: Sizes.ex176,
      width: Sizes.ex176,
      alignSelf: 'center',
    },
    titleStyle: {
      textAlign: 'center',
      fontSize: Sizes.mx,
      textTransform: 'capitalize',
      fontFamily: Fonts.Bold,
    },
    desStyle: {
      textAlign: 'center',
      textTransform: 'capitalize',
      fontSize: Sizes.xx,
      fontFamily: Fonts.Medium,
    },
    emptyImage: {
      width: Sizes.ex2l + Sizes.x54,
      height: Sizes.ex2l + Sizes.xms,
    },
  });

export default styles;

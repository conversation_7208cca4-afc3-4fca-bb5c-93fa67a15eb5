import React, {useEffect, useState} from 'react';
import {
  TouchableOpacity,
  View,
  FlatList,
  ScrollView,
  BackHandler,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../routes';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import {ImageIcon, Label, Spacer, GradientText} from 'components/atoms';
import {useTheme, RouteProp} from '@react-navigation/native';
import {t} from 'i18next';
import {myMemberShipsData} from 'services/mambership';
import DashedLine from 'react-native-dashed-line';
import moment from 'moment';
import {MembershipLoader} from 'skeletonLoader';
import {checkDevice, sDevice, mDevice} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'Membership'>;
};

const Membership = ({navigation, route}: Props) => {
  const TAG = 'MembershipScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [activeScreen, setActiveScreen] = useState(false);
  const [membershipData, setMembershipData] = useState<
    MembershipInfoResponseType | []
  >([]);
  const [loading, setLoadingState] = useState(true);

  useEffect(() => {
    myMembershipApis();
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const myMembershipApis = async () => {
    setLoadingState(true);
    const {data, status} = await myMemberShipsData();
    setLoadingState(false);
    if (data) {
      setMembershipData({...data, memberships: data?.memberships?.reverse()});
      setActiveScreen(data?.memberships.length > 0 ? false : true);
    }
  };

  const renderItem = ({item, index}) => {
    const day = moment(item?.expiry_date, 'YYYY-MM-DD, HH:mm:ss').diff(
      moment(item?.created_at, 'YYYY-MM-DD, HH:mm:ss'),
      'days',
      true,
    );
    const month = Math.round(Number(day) / (365.25 / 12));
    return (
      <View key={index} style={styles.cardsMainView}>
        <View style={styles.cardSection}>
          <Label
            size="m"
            color="text"
            fontFamily="Medium"
            text={item?.order_id || item?.product_sku}
            style={styles.flex2}
          />
          <Label
            size="m"
            color="text"
            fontFamily="Medium"
            text={item?.created_at?.split(',')[0]}
            style={styles.flexHalf}
          />
          <Label
            size="m"
            color="text"
            fontFamily="Medium"
            text={`${month} ${t(
              month === 1 ? 'memberShip.month' : 'memberShip.months',
            )}`}
            style={styles.flex}
          />
        </View>
        <Spacer size="m" />
        <Label
          size="m"
          color="text"
          fontFamily="Medium"
          text={`${t('memberShip.status')} - `}>
          <Label
            size="m"
            color={item.is_active ? 'green' : 'textError'}
            fontFamily="Medium"
            text={
              item?.is_active ? t('memberShip.active') : t('memberShip.expired')
            }
            textTransform="capitalize"
          />
        </Label>
      </View>
    );
  };

  const activeMembershipsList = membershipData?.memberships?.filter(
    membership => membership,
  );
  const activeMemberships =
    activeMembershipsList?.length > 0 ? [activeMembershipsList[0]] : [];

  const planShow =
    activeMemberships &&
    activeMemberships?.filter(membership => !membership?.is_active)?.length > 0
      ? false
      : true;
  const saveAmount =
    membershipData?.memberships?.length > 0
      ? membershipData?.memberships?.reduce(
          (sum, item) => sum + item?.monetory_value,
          0,
        )
      : 0;

  return (
    <SafeAreaView style={styles.container}>
      {loading ? (
        <MembershipLoader />
      ) : (
        <View style={styles.flex}>
          <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
            <ErrorHandler
              componentName={`${TAG} Header`}
              onErrorComponent={<View />}>
              <TouchableOpacity
                style={styles.arrowView}
                onPress={() => {
                  navigation.goBack();
                  if (route.params?.goBack) {
                    route.params?.goBack();
                  }
                }}>
                <ImageIcon
                  icon="rightArrow"
                  size="x3l"
                  tintColor="whiteColor"
                />
              </TouchableOpacity>
            </ErrorHandler>
            <ImageIcon
              resizeMode="stretch"
              style={styles.subBannerImg}
              icon="activeMamberShipIcon"
            />
            {activeScreen ? (
              <>
                <View style={styles.flex}>
                  <FlatList
                    data={['']}
                    showsVerticalScrollIndicator={false}
                    scrollEnabled={false}
                    renderItem={() => (
                      <View style={styles.premiumContainer}>
                        <Spacer size="m" />
                        <View style={styles.premiumView}>
                          <Label
                            color="text2"
                            size={checkDevice() ? 'l' : 'm'}
                            fontFamily="SemiBold"
                            text={t('memberShip.premiumTxt')}
                            align="center"
                            textTransform="capitalize"
                          />
                        </View>

                        <Spacer size="m" />

                        <View style={styles.centerView}>
                          <View style={styles.cardBox}>
                            <ImageIcon
                              resizeMode="contain"
                              style={styles.cardImg}
                              icon="shipping"
                            />
                            <Spacer size="xm" />
                            <GradientText
                              linearColor={[
                                colors.cornflowerBlue2,
                                colors.paleMagenta,
                              ]}
                              style={styles.titleStyle}>
                              {t('memberShip.freeShipping1')}
                            </GradientText>
                          </View>
                          <Spacer size="m" type="Horizontal" />
                          <View style={styles.cardBox}>
                            <ImageIcon
                              resizeMode="contain"
                              style={styles.cardImg}
                              icon="rewardValue"
                            />
                            <Spacer size="xm" />
                            <GradientText
                              linearColor={[
                                colors.cornflowerBlue2,
                                colors.paleMagenta,
                              ]}
                              style={styles.titleStyle}>
                              {t('memberShip.doubleRewardCoins')}
                            </GradientText>
                          </View>
                        </View>
                        <View style={styles.cardBox}>
                          <ImageIcon
                            resizeMode="contain"
                            style={styles.cardImg}
                            icon="valueCoin"
                          />
                          <Spacer size="xm" />
                          <GradientText
                            linearColor={[
                              colors.cornflowerBlue2,
                              colors.paleMagenta,
                            ]}
                            style={styles.titleStyle}>
                            {t('memberShip.valueAndManyMore')}
                          </GradientText>
                        </View>
                      </View>
                    )}
                  />
                </View>
              </>
            ) : (
              <>
                <View style={styles.flex}>
                  <FlatList
                    data={['']}
                    showsVerticalScrollIndicator={false}
                    scrollEnabled={false}
                    renderItem={() => (
                      <>
                        <View style={styles.activeContainer}>
                          {!planShow && (
                            <>
                              <Spacer size="xl" />
                              <Label
                                size="mx"
                                color="text2"
                                textTransform="capitalize"
                                fontFamily="Medium"
                                text={t('memberShip.membershipHasExpired')}>
                                <Spacer type="Horizontal" size="s" />
                                <Label
                                  onPress={() => {
                                    navigation.navigate('MembershipPage');
                                  }}
                                  size="mx"
                                  color="newSunnyOrange"
                                  fontFamily="Medium"
                                  text={t('memberShip.renew')}
                                />
                              </Label>
                            </>
                          )}
                          <Spacer size="xl" />
                          <ErrorHandler
                            componentName={`${TAG} ActiveMembershipsList`}
                            onErrorComponent={<View />}>
                            <FlatList
                              data={activeMemberships}
                              renderItem={({item, index}) => {
                                const activePlan = item.is_active;
                                return (
                                  <View
                                    key={index}
                                    style={
                                      activePlan
                                        ? styles.currentView
                                        : styles.previousView
                                    }>
                                    <Label
                                      size={
                                        sDevice ? 'xms' : mDevice ? 'm' : 'mx'
                                      }
                                      color="text"
                                      fontFamily="SemiBold"
                                      style={styles.flex}
                                      textTransform="capitalize"
                                      text={`${t(
                                        activePlan
                                          ? 'memberShip.currentPlan'
                                          : 'memberShip.previousPlan',
                                      )} - ${item?.price} (${item?.duration})`}
                                    />
                                    <View
                                      style={
                                        activePlan
                                          ? styles.activeView
                                          : styles.expiredView
                                      }>
                                      <Label
                                        size="mx"
                                        color={activePlan ? 'green2' : 'red2'}
                                        textTransform="capitalize"
                                        fontFamily="Medium"
                                        text={
                                          activePlan
                                            ? t('memberShip.active')
                                            : t('memberShip.expired')
                                        }
                                      />
                                    </View>
                                  </View>
                                );
                              }}
                              ItemSeparatorComponent={<Spacer size="xl" />}
                              keyExtractor={(item, index) => index.toString()}
                            />
                          </ErrorHandler>
                          <ErrorHandler
                            componentName={`${TAG} CurrentPlan`}
                            onErrorComponent={<View />}>
                            <View style={styles.activeContainer}>
                              {planShow && (
                                <>
                                  <Spacer size="xl" />
                                  <View>
                                    <Label
                                      size="mx"
                                      color="green2"
                                      fontFamily="SemiBold"
                                      text={`${t('memberShip.daysLeft')} =  ${
                                        membershipData?.daysLeft
                                      }`}
                                    />
                                  </View>
                                </>
                              )}
                              <Spacer size="xl" />
                              <Label
                                size="mx"
                                color="text2"
                                fontFamily="SemiBold"
                                text={`${t('orderListing.orderID')} - ${
                                  membershipData?.currentPlan?.order_id
                                    ? membershipData?.currentPlan?.order_id
                                    : membershipData?.memberships?.length > 0
                                    ? membershipData?.memberships[0].order_id ||
                                      membershipData?.memberships[0].product_sku
                                    : ''
                                }`}
                              />
                              {activeMemberships?.map((membership, index) => (
                                <View key={index}>
                                  <Spacer size="m" />
                                  <Label
                                    size="mx"
                                    color={
                                      membership.is_active ? 'text2' : 'red2'
                                    }
                                    fontFamily="Medium"
                                    textTransform="capitalize"
                                    text={`${
                                      membership?.is_active
                                        ? t('memberShip.validTill')
                                        : t('memberShip.endOn')
                                    } ${membership?.expiry_date.split(',')[0]}`}
                                  />
                                  <Spacer size="m" />
                                  <Label
                                    size="mx"
                                    color="text2"
                                    fontFamily="Medium"
                                    text={`${t('memberShip.purchasedOn')} ${
                                      membership?.created_at.split(',')[0]
                                    }`}
                                  />
                                </View>
                              ))}
                            </View>
                          </ErrorHandler>
                          <Spacer size="xl" />
                          <View style={styles.totalView}>
                            <Label
                              size="xl"
                              color="green2"
                              fontFamily="SemiBold"
                              text={`${t('memberShip.rs')} ${saveAmount}`}
                            />
                            <Spacer size="xm" type="Horizontal" />
                            <Label
                              size="mx"
                              color="green2"
                              fontFamily="Medium"
                              textTransform="capitalize"
                              text={t('memberShip.savingNow')}
                            />
                          </View>

                          <Spacer size="xl" />
                          <View style={styles.dashLineView}>
                            <Label
                              size="l"
                              color="text"
                              fontFamily="SemiBold"
                              text={t('memberShip.history')}
                            />
                            <Spacer size="xm" type="Horizontal" />
                            <View style={styles.flex}>
                              <DashedLine
                                dashLength={6}
                                dashThickness={1.5}
                                dashColor={colors.grey2}
                              />
                            </View>
                          </View>
                          <Spacer size="xl" />
                          <View style={styles.validityView}>
                            <View style={styles.validitySubView}>
                              <Label
                                size="mx"
                                color="whiteColor"
                                fontFamily="SemiBold"
                                text={t('orderListing.orderID')}
                                style={styles.flex2}
                              />
                              <Label
                                size="mx"
                                color="whiteColor"
                                fontFamily="SemiBold"
                                text={t('memberShip.purchasedOn')}
                                style={styles.flexHalf}
                              />
                              <Label
                                size="mx"
                                color="whiteColor"
                                fontFamily="SemiBold"
                                text={t('memberShip.validity')}
                                style={styles.flex}
                              />
                            </View>
                            <Spacer size="l" />
                            <ErrorHandler
                              componentName={`${TAG} MemberShipList`}
                              onErrorComponent={<View />}>
                              <FlatList
                                keyExtractor={(item, index) => index.toString()}
                                data={membershipData?.memberships}
                                showsVerticalScrollIndicator={false}
                                renderItem={renderItem}
                                ItemSeparatorComponent={<Spacer size="xm" />}
                              />
                            </ErrorHandler>
                            <Spacer size="l" />
                          </View>
                        </View>
                      </>
                    )}
                  />
                </View>
                {!planShow && (
                  <>
                    <View style={styles.mainView}>
                      <TouchableOpacity
                        onPress={() => {
                          navigation.navigate('MembershipPage');
                        }}
                        style={styles.onCheckoutView}>
                        <Label
                          text={t('memberShip.renewPlan')}
                          size="mx"
                          fontFamily="Regular"
                          color="background"
                          align="center"
                        />
                      </TouchableOpacity>
                    </View>
                  </>
                )}
              </>
            )}
          </ScrollView>
          {activeScreen && (
            <View style={styles.mainView}>
              <Button
                style={styles.btnStyle}
                iconSize="xl"
                labelStyle={styles.btnText}
                onPress={() => navigation.navigate('MembershipPage')}
                radius="m"
                text={'View Details'}
                labelColor="whiteColor"
                selfAlign="stretch"
              />
              <Spacer size="s" />
            </View>
          )}
        </View>
      )}
    </SafeAreaView>
  );
};

export default Membership;

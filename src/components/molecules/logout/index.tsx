import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './styles';
import Modal from 'react-native-modal';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  userLogout: () => void;
};

const LogoutUserModal = (props: Props) => {
  const {visible, onClose, userLogout} = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <Modal
      onBackButtonPress={() => onClose()}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.25}>
      <>
        <View style={styles.removeModalView}>
          <View style={styles.removeModalSubView}>
            <View style={styles.removeView}>
              <Spacer size="s" />
              <Label
                text={t('buttons.logout')}
                size="l"
                weight="600"
                color="text"
              />
              <Spacer size="xm" />
              <Label
                text={t('profileDetails.logoutMessage')}
                size="mx"
                weight="500"
                color="text2"
              />
            </View>
          </View>
          <Spacer size="xm" />
          <View style={styles.borderBottomView} />
          <Spacer size="m" />
          <View style={styles.deleteCartItemView}>
            <TouchableOpacity
              style={styles.deleteCartSubView}
              onPress={() => onClose()}>
              <Label
                text={t('buttons.cancel')}
                size="mx"
                weight="500"
                color="text"
              />
            </TouchableOpacity>
            <View style={styles.borderLine} />
            <Spacer size="xm" type="Horizontal" />
            <TouchableOpacity
              style={styles.wishlistView}
              onPress={() => userLogout()}>
              <Label
                text={t('buttons.logout')}
                size="mx"
                weight="500"
                color="text"
              />
            </TouchableOpacity>
          </View>
          <Spacer size="s" />
        </View>
      </>
    </Modal>
  );
};

export default React.memo(LogoutUserModal);

import React from 'react';
import { ScrollView, Dimensions } from 'react-native';
import ContentLoader, { Rect } from 'react-content-loader/native';
import { useTheme } from '@react-navigation/native';
import { checkDevice } from 'utils/utils';

const { width } = Dimensions.get('window');

const HomeLoader = () => {
  const { colors } = useTheme();
  const tab = checkDevice();
  const boxWidth = (width - (tab ? 60 : 30)) / (tab ? 4 : 2);
  return (
    <>
      {tab ?
        <ScrollView>
          <ContentLoader
            speed={1}
            width={width}
            height={2040}
            backgroundColor={colors.whiteSmoke}
            foregroundColor={colors.whisper}>
            {/* <Rect x="10" y="5" rx="5" ry="5" width={width - 20} height="30" /> */}

            <Rect x="10" y="5" rx="10" ry="10" width={120} height="120" />
            <Rect x="10" y="130" rx="10" ry="10" width={70} height="30" />
            <Rect x="140" y="5" rx="10" ry="10" width={120} height="120" />
            <Rect x="140" y="130" rx="10" ry="10" width={70} height="30" />
            <Rect x="270" y="5" rx="10" ry="10" width={120} height="120" />
            <Rect x="270" y="130" rx="10" ry="10" width={70} height="30" />
            <Rect x="400" y="5" rx="10" ry="10" width={120} height="120" />
            <Rect x="400" y="130" rx="10" ry="10" width={70} height="30" />
            <Rect x="530" y="5" rx="10" ry="10" width={120} height="120" />
            <Rect x="530" y="130" rx="10" ry="10" width={70} height="30" />
            <Rect x="660" y="5" rx="10" ry="10" width={120} height="120" />
            <Rect x="660" y="130" rx="10" ry="10" width={70} height="30" />
            <Rect x="790" y="5" rx="10" ry="10" width={120} height="120" />
            <Rect x="790" y="130" rx="10" ry="10" width={70} height="30" />

            <Rect x="10" y="200" rx="5" ry="5" width={width - 20} height="190" />

            <Rect x="10" y="400" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="400" rx="5" ry="5" width="25" height="30" />

            <Rect x="10" y="440" rx="20" ry="20" width={140} height="140" />
            <Rect x="160" y="440" rx="20" ry="20" width={140} height="140" />
            <Rect x="310" y="440" rx="20" ry="20" width={140} height="140" />
            <Rect x="460" y="440" rx="20" ry="20" width={140} height="140" />
            <Rect x="610" y="440" rx="20" ry="20" width={140} height="140" />
            <Rect x="760" y="440" rx="20" ry="20" width={140} height="140" />
            <Rect x="910" y="440" rx="20" ry="20" width={140} height="140" />

            <Rect x="10" y="620" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="620" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={170} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={330} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={490} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={650} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={810} y="660" rx="5" ry="5" width={150} height="300" />

            <Rect x="10" y="970" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="970" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="1010" rx="5" ry="5" width={boxWidth} height="300" />
            <Rect
              x={boxWidth + 20}
              y="1010"
              rx="5"
              ry="5"
              width={boxWidth}
              height="300"
            />
            {tab && (
              <>
                <Rect
                  x={boxWidth * 2 + 30}
                  y="1010"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
                <Rect
                  x={boxWidth * 3 + 40}
                  y="1010"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
              </>
            )}

            <Rect x="10" y="1320" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="1320" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="1360" rx="5" ry="5" width={boxWidth} height="300" />
            <Rect
              x={boxWidth + 20}
              y="1360"
              rx="5"
              ry="5"
              width={boxWidth}
              height="300"
            />
            {tab && (
              <>
                <Rect
                  x={boxWidth * 2 + 30}
                  y="1360"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
                <Rect
                  x={boxWidth * 3 + 40}
                  y="1360"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
              </>
            )}

            <Rect x="10" y="1670" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="1670" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="1710" rx="5" ry="5" width={boxWidth} height="300" />
            <Rect
              x={boxWidth + 20}
              y="1710"
              rx="5"
              ry="5"
              width={boxWidth}
              height="300"
            />
            {tab && (
              <>
                <Rect
                  x={boxWidth * 2 + 30}
                  y="1710"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
                <Rect
                  x={boxWidth * 3 + 40}
                  y="1710"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
              </>
            )}
          </ContentLoader>
        </ScrollView>
        :
        <ScrollView>
          <ContentLoader
            speed={1}
            width={width}
            height={2040}
            backgroundColor={colors.whiteSmoke}
            foregroundColor={colors.whisper}>

            {/* Category Icons */}
            <Rect x="5%" y="10" rx="10" ry="10" width="70" height="70" />
            <Rect x="25%" y="10" rx="10" ry="10" width="70" height="70" />
            <Rect x="45%" y="10" rx="10" ry="10" width="70" height="70" />
            <Rect x="65%" y="10" rx="10" ry="10" width="70" height="70" />
            <Rect x="85%" y="10" rx="10" ry="10" width="70" height="70" />

            {/* Category Text */}
            <Rect x="5%" y="90" rx="5" ry="5" width="70" height="18" />
            <Rect x="25%" y="90" rx="5" ry="5" width="70" height="18" />
            <Rect x="45%" y="90" rx="5" ry="5" width="70" height="18" />
            <Rect x="65%" y="90" rx="5" ry="5" width="70" height="18" />
            <Rect x="85%" y="90" rx="5" ry="5" width="70" height="18" />

            {/* Banner */}
            <Rect x="10" y="120" rx="10" ry="10" width={width - 20} height="180" />

            {/* Top Brands Label */}
            <Rect x="10" y="320" rx="5" ry="5" width={150} height="25" />
            <Rect x={width - 40} y="320" rx="5" ry="5" width={30} height="25" />

            {/* Top Brands Logos */}
            <Rect x={15} y={360} rx="15" ry="15" width="60" height={60} />
            <Rect x={15 + 60 + 15} y={360} rx="15" ry="15" width="60" height={60} />
            <Rect x={15 + (60 + 15) * 2} y={360} rx="15" ry="15" width="60" height={60} />
            <Rect x={15 + (60 + 15) * 3} y={360} rx="15" ry="15" width="60" height={60} />
            <Rect x={15 + (60 + 15) * 4} y={360} rx="15" ry="15" width="60" height={60} />
           
            {/* Hot Selling Section */}
            <Rect x="10" y="450" rx="5" ry="5" width={150} height="25" />
            <Rect x={width - 40} y="450" rx="5" ry="5" width={30} height="25" />

            {/* Hot Selling Products */}
            <Rect x="15" y="490" rx="10" ry="10" width={160} height={220} />
            <Rect x={15 + 160 + 15} y={490} rx="10" ry="10" width={160} height={220} />
            <Rect x={15 + (160 + 15) * 2} y={490} rx="10" ry="10" width={160} height="220" />

            <Rect x="10" y="620" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="620" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={170} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={330} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={490} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={650} y="660" rx="5" ry="5" width={150} height="300" />
            <Rect x={810} y="660" rx="5" ry="5" width={150} height="300" />

            <Rect x="10" y="970" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="970" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="1010" rx="5" ry="5" width={boxWidth} height="300" />
            <Rect
              x={boxWidth + 20}
              y="1010"
              rx="5"
              ry="5"
              width={boxWidth}
              height="300"
            />
            {tab && (
              <>
                <Rect
                  x={boxWidth * 2 + 30}
                  y="1010"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
                <Rect
                  x={boxWidth * 3 + 40}
                  y="1010"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
              </>
            )}

            <Rect x="10" y="1320" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="1320" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="1360" rx="5" ry="5" width={boxWidth} height="300" />
            <Rect
              x={boxWidth + 20}
              y="1360"
              rx="5"
              ry="5"
              width={boxWidth}
              height="300"
            />
            {tab && (
              <>
                <Rect
                  x={boxWidth * 2 + 30}
                  y="1360"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
                <Rect
                  x={boxWidth * 3 + 40}
                  y="1360"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
              </>
            )}

            <Rect x="10" y="1670" rx="5" ry="5" width={150} height="30" />
            <Rect x={width - 35} y="1670" rx="5" ry="5" width="25" height="30" />

            <Rect x={10} y="1710" rx="5" ry="5" width={boxWidth} height="300" />
            <Rect
              x={boxWidth + 20}
              y="1710"
              rx="5"
              ry="5"
              width={boxWidth}
              height="300"
            />
            {tab && (
              <>
                <Rect
                  x={boxWidth * 2 + 30}
                  y="1710"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
                <Rect
                  x={boxWidth * 3 + 40}
                  y="1710"
                  rx="5"
                  ry="5"
                  width={boxWidth}
                  height="300"
                />
              </>
            )}
          </ContentLoader>
        </ScrollView>

      }
    </>
  );
};

export default HomeLoader;

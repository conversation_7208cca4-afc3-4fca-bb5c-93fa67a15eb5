import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
    },
    filtersFooter: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background,
      shadowColor: colors.shadowColor,
    },
    shadowProp: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 3,
      },
      shadowOpacity: 0.27,
      shadowRadius: 4.65,
      elevation: Sizes.xms,
    },
    buttonIcon: {width: Sizes.xx, height: Sizes.xx},
    sortSubText: {
      textAlign: 'center',
      marginTop: -Sizes.mx,
      marginBottom: Sizes.sx,
    },
  });

export default styles;

import React, {useMemo} from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ImageBackground,
  GestureResponderEvent,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';
import {checkDevice} from 'utils/utils';
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import Icons from 'common/icons';
import {Label, Spacer} from 'components/atoms';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Svg, {
  Defs,
  LinearGradient as Linear,
  Stop,
  Text as SvgText,
} from 'react-native-svg';
import {Fonts} from 'common';
interface UpgradeModalProps {
  visible: boolean;
  onUpgradePress: (event: GestureResponderEvent) => void;
  onSkipPress: (event: GestureResponderEvent) => void;
  appConfig: any;
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({
  visible,
  onUpgradePress,
  onSkipPress,
  appConfig,
}) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const insets = useSafeAreaInsets();
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ImageBackground
            source={Icons.upgradeBG}
            style={
              checkDevice()
                ? styles.backgroundImageTablet
                : styles.backgroundImage
            }
            imageStyle={styles.imageStyle}>
            {appConfig && !appConfig.force_update && (
              <TouchableOpacity
                activeOpacity={0.5}
                style={styles.skipButton}
                onPress={onSkipPress}>
                <Label
                  text={t('forceUpgrade.skip')}
                  size="m"
                  weight="500"
                  fontFamily="Medium"
                  color="text2"
                />
              </TouchableOpacity>
            )}
            {/* Text & Button */}
            <Spacer size={checkDevice() ? 'ex330' : 'ex170'} />
            <View style={styles.contentWrapper}>
              <Svg height={checkDevice() ? 50 : 40} width="100%">
                <Defs>
                  <Linear id="grad" x1="0" y1="0" x2="1" y2="0">
                    <Stop offset="0" stopColor="#FC865D" />
                    <Stop offset="0.5" stopColor="#F9845F" />
                    <Stop offset="1" stopColor="#6224B9" />
                  </Linear>
                </Defs>
                <SvgText
                  fill="url(#grad)"
                  fontSize={checkDevice() ? 35 : 28}
                  fontWeight="600"
                  x="50%"
                  y={28}
                  fontFamily={Fonts.SemiBold}
                  textAnchor="middle">
                  {t('forceUpgrade.upgradeTitle')}
                </SvgText>
              </Svg>
              {/* <MaskedView
                maskElement={
                  <Text
                    style={[styles.title, checkDevice() && styles.titleTablet]}>
                    {t('forceUpgrade.upgradeTitle')}
                  </Text>
                }>
                <LinearGradient
                  colors={['#FC865D', '#F9845F', '#6224B9']}
                  start={{x: 0, y: 0}}
                  end={{x: 1, y: 0}}>
                  <Text
                    style={[
                      styles.title,
                      {opacity: 0},
                      checkDevice() && styles.titleTablet,
                    ]}>
                    {t('forceUpgrade.upgradeTitle')}
                  </Text>
                </LinearGradient>
              </MaskedView> */}
              <Spacer size={checkDevice() ? 'ex' : 'x4l'} />
              <Label
                text={t('forceUpgrade.upgradeMsg')}
                size={checkDevice() ? 'xsl' : 'mx'}
                weight="500"
                fontFamily="Medium"
                color="text2"
                align="center"
              />
              <Spacer size={checkDevice() ? 'ex' : 'x4l'} />
              <TouchableOpacity
                activeOpacity={0.3}
                onPress={onUpgradePress}
                style={styles.upgradeButtonContainer}>
                <LinearGradient
                  colors={['#FF8A50', '#EB5003']}
                  style={styles.upgradeButton}
                  start={{x: 0, y: 0}}
                  end={{x: 1, y: 0}}>
                  <Label
                    text={t('forceUpgrade.btnText')}
                    size={checkDevice() ? 'xx' : 'mx'}
                    weight="400"
                    fontFamily="Regular"
                    color="whiteColor"
                  />
                </LinearGradient>
              </TouchableOpacity>
              <View style={{height: insets.bottom}} />
            </View>
          </ImageBackground>
        </View>
      </View>
    </Modal>
  );
};

export default UpgradeModal;

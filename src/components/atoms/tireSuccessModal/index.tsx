import Icons from 'common/icons';
import React, {useEffect, useState} from 'react';
import {TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import FastImage from 'react-native-fast-image';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
};

const TireSuccessModal = (props: Props) => {
  const {visible, onClose} = props;

  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  // const [isModalVisible, setIsModalVisible] = useState(visible);

  useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        onClose();
      }, 2500);

      return () => clearTimeout(timer);
    }
  }, [visible]);

  const handleGifPress = () => {
    onClose();
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={50}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <TouchableOpacity style={styles.modalContainer} onPress={handleGifPress}>
        <FastImage
          resizeMode="contain"
          style={styles.absoluteGifView}
          source={Icons.crackers}
        />
      </TouchableOpacity>
    </Modal>
  );
};

export default TireSuccessModal;

import Icons from 'common/icons';
import React from 'react';
import {ImageBackground, ImageProps, ViewProps} from 'react-native';

type Props = {
  image: keyof typeof Icons;
  source: ImageProps['source'];
  children: React.ReactNode;
  style?: ViewProps['style'];
  imageStyle?: ImageProps['style'];
};

const WithBackground = ({
  image,
  source,
  children,
  style,
  imageStyle,
}: Props) => {
  return (
    <ImageBackground
      source={source ? source : Icons[image]}
      imageStyle={imageStyle}
      style={style}>
      {children}
    </ImageBackground>
  );
};

export default WithBackground;

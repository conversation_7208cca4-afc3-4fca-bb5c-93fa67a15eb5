import Api from 'services';
import { apiRoute, apiEndpoints, subEndpoint } from 'config/apiEndpoint';

const API = new Api();

export const getMagazineBanner = async () => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.magazines}/${subEndpoint.magazines.banners}`;
  const resp = await API.get(url);
  return resp;
};
export const getPublisher = async () => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.magazines}/${subEndpoint.magazines.publishers}`;
  const resp = await API.get(url);
  return resp;
};

export const getMagazineById = async (magazineId: any) => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.magazines}/${subEndpoint.magazines.detail}?id=${magazineId}`;
  const resp = await API.get(url);
  return resp;
};

export const getMagazineList = async filterData => {
  const url = `${apiRoute.nodeBkAdmin}${apiEndpoints.magazines}`;
  const resp = await API.post(url, filterData);
  return resp;
};

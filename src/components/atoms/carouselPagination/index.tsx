import {useMemo} from 'react';
import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {View, ViewStyle} from 'react-native';
import {Pagination} from 'react-native-snap-carousel';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
type Props = {
  index: number;
  dotsLength: number;
  dotStyle?: ViewStyle['style'];
  style?: ViewStyle;
  endDot?: boolean;
};
const CarouselPagination = ({
  index,
  dotsLength,
  dotStyle,
  style,
  endDot,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={[endDot ? styles.dotStyles : styles.paginationView, style]}>
      <Pagination
        dotsLength={dotsLength}
        activeDotIndex={index}
        dotStyle={[styles.dotStyles, dotStyle]}
        inactiveDotOpacity={0.4}
        inactiveDotScale={0.6}
      />
    </View>
  );
};

export default CarouselPagination;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    box: {
      backgroundColor: colors.background,
      marginHorizontal: Sizes.l,
    },
    subHeading: {
      fontSize: Sizes.m,
      fontWeight: '400',

      color: colors.textLight,
    },
    durationsBox: {flexDirection: 'row', alignItems: 'center'},
    imageBox: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.border,
      height: Sizes.exl,
      backgroundColor: colors.background,
      justifyContent: 'center',
      padding: Sizes.s,
    },
    // image: {width: Sizes.ex - Sizes.xms, height: Sizes.ex - Sizes.xms},
    image: {width: Sizes.ex + Sizes.s, height: Sizes.exl + Sizes.s},
    viewContinuer: {
      paddingHorizontal: Sizes.m,
      width: '78%',
    },
  });

export default styles;

import {Platform, Linking, Alert} from 'react-native';
import {
  PERMISSIONS,
  requestMultiple,
  openSettings,
  checkMultiple,
} from 'react-native-permissions';
import <PERSON><PERSON>iewer from 'react-native-file-viewer';
import DeviceInfo from 'react-native-device-info';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {t} from 'i18next';
import RNFS from 'react-native-fs';
import moment from 'moment';
import {DeviceWidth, WEBSITE_URL} from 'config/environment';
import {debugLog} from './debugLog';
import Geolocation from 'react-native-geolocation-service';
import {addressTag} from 'staticData';
import localStorage from 'utils/localStorage';
import {checkValidateGst} from 'services/address';

let disable = true;
const defaultPrice = 500000;
const stringReg = /^[a-zA-Z ]*$/;
const fullNameReg = /^[a-zA-Z]+( [a-zA-Z]+)+$/;
const emailRegExp = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,4})+$/;
const phoneReg = /^[56789][0-9]{9}$/;
const pinCodeRegExp = /^\d{6}$/;
const numberRegExp = /^[0-9]+$/;
const decimalNumRegExp = /^[0-9.]+$/;
const videoSizeLimit = 25 * 1024 * 1024;
const imageSizeLimit = 5 * 1024 * 1024;
const sAllDevice = DeviceWidth <= 380;
const mDevice = DeviceWidth > 320 && DeviceWidth <= 380;
const sDevice = DeviceWidth <= 320;
const urlReg = /^(https?:\/\/)?(www\.)?([\w-]+\.)+[a-zA-Z]{2,}([/?#].*)?$/;
const htmlReg = /<\/?[a-z][\s\S]*>/i;
const fullNameRegex = /^[a-zA-Z]+\s+[a-zA-Z ]+$/;
const isIos = Platform.OS === 'ios';

const getFileExtension = (fileUrl: string) => {
  return /[.]/.exec(fileUrl) ? /[^.]+$/.exec(fileUrl) : undefined;
};

const formatDate = (date, format) => {
  return moment(date).format(format);
};

const formatBytes = (bytes: any) => {
  if (bytes < 1024) {
    return `${bytes} Bytes`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(2)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  }
};

const coinInfoShow = (status: string) => {
  return status === 'Delivered' ||
    status === 'Partially Delivered' ||
    status === 'Order Placed' ||
    status === 'Order Confirmed' ||
    status === 'Packed' ||
    status === 'Partially Packed' ||
    status === 'Shipped' ||
    status === 'Partially Shipped'
    ? true
    : false;
};

const orderStatusColor = (title: string, colors: any) => {
  switch (title) {
    case 'Lost':
    case 'Damaged':
    case 'Cancellation Requested':
    case 'Cancellation Failed':
    case 'Cancelled':
    case 'Returned':
    case 'Order Closed':
      return {
        bgColor: colors.lightRed1,
        color: 'persianRed',
        status: title === 'Order Closed' ? 'Cancelled' : title,
      };
    case 'Delivered':
    case 'Partially Delivered':
      return {
        bgColor: colors.green4,
        color: 'green2',
        status: title,
      };
    case 'Order Placed':
    case 'Order Confirmed':
      return {bgColor: colors.green4, color: 'green2', status: 'Order Placed'};
    case 'Partially Shipped':
    case 'Shipped':
      return {bgColor: colors.oasis, color: 'darkGoldenrod', status: title};
    case 'Packed':
    case 'Partially Packed':
      return {
        bgColor: colors.softPeach,
        color: 'buccaneer',
        status: title,
      };
    case 'Payment Pending':
    case 'STATUS_PENDING':
      return {
        bgColor: colors.solitude,
        color: 'cobalt',
        status: 'Payment Pending',
      };
    case 'rejected':
      return {
        bgColor: colors.cosmos,
        color: 'persianRed',
        status: 'Rejected',
      };
    case 'in_process':
      return {
        bgColor: colors.skyBlue22,
        color: 'whiteColor',
        status: 'In Process',
      };
    case 'complete':
      return {
        bgColor: colors.green3,
        color: 'britishRacingGree',
        status: 'Complete',
      };
    case 'request_raised':
      return {
        bgColor: colors.whiteIce1,
        color: 'britishRacingGreen',
        status: 'Return Raised',
      };
    default:
      return {bgColor: colors.green4, color: 'green2', status: title || ''};
  }
};

const getOrderStatusIcon = (
  status: string,
  isBeforeCurrent: boolean,
  isCurrent: boolean,
) => {
  const statusIcons: OrderStatusIcons = {
    request_raised: ['requestunRaise', 'requestRaiseOrange', 'requestRaise'],
    approved: ['requestunRaise', 'requestRaiseOrange', 'requestRaise'],
    pick_up_complete: [
      'picUpComplete',
      'picUpCompleteOrange',
      'shippedInActive',
    ],
    under_inspection: [
      'requestActive',
      'requestActiveOrange',
      'requestInActive',
    ],
    replaced: ['refundInactive', 'refundOrange', 'refund'],
    refund_initiated: ['deliveredActive', 'refundOrange', 'refund'],
    refund_done: [
      'refundInactive',
      'refundCompleteOrange',
      'deliveredInActive',
    ],
    'Refund Complete': [
      'refundInactive',
      'refundCompleteOrange',
      'deliveredInActive',
    ],
    'Order Confirmed': [
      'orderConfirmedActive',
      'orderConfirmedActive',
      'orderConfirmedActive',
    ],
    Shipped: ['shippedActive', 'shippedOrange', 'shippedInActive'],
    delivered: ['deliveredActive', 'deliveredActive', 'deliveredInActive'],
    Delivered: ['deliveredActive', 'deliveredActive', 'deliveredInActive'],
    Processing: ['inTransitActive', 'inTransitOrange', 'inTransInActive'],
    Packed: ['atWarehouseActive', 'atWarehouseActive', 'atWarehouseInActive'],
    repair: ['refundInactive', 'refundInactive', 'refund'],
    Cancelled: ['deliveredInActive', 'deliveredInActive', 'deliveredActive'],
  };

  const defaultIcon = 'tracknullIcon';

  const getIcon = (icons: string[]) =>
    isBeforeCurrent ? icons[0] : isCurrent ? icons[1] : icons[2];

  return statusIcons[status] ? getIcon(statusIcons[status]) : defaultIcon;
};

const onDownload = async (fileUrl: string, title?: string, callBack?: any) => {
  const fileName = fileUrl?.substring(fileUrl?.lastIndexOf('/') + 1);
  const downloadPath = `${
    Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.ExternalDirectoryPath
  }/${fileName}`;
  const fileExists = await RNFS.exists(downloadPath);
  if (fileExists) {
    FileViewer.open(downloadPath, {
      showOpenWithDialog: true,
      showAppsSuggestions: true,
    });
  } else {
    try {
      callBack(true);
      const downloadResult = await RNFS.downloadFile({
        fromUrl: fileUrl,
        toFile: downloadPath,
      }).promise;
      callBack(false);
      if (downloadResult.statusCode === 200) {
        if (title) {
          showSuccessMessage(`${title} ${t('otherText.fileSaved')}`);
        } else {
          showSuccessMessage(t('otherText.invoiceSave'));
        }
        FileViewer.open(downloadPath, {
          showOpenWithDialog: true,
          showAppsSuggestions: true,
        });
      }
    } catch (e) {
      callBack(false);
      FileViewer.open(downloadPath, {
        showOpenWithDialog: true,
        showAppsSuggestions: true,
      });
    }
  }
};

const downloadFile = async (
  fileUrl: string,
  title?: string,
  callBack?: any,
) => {
  if (disable) {
    disable = false;
    try {
      onDownload(fileUrl, title, callBack);
      // if (Platform.OS === 'ios') {
      //   onDownload(fileUrl, title, callBack);
      // } else {
      //   const deviceAPI = await DeviceInfo.getApiLevel();
      //   let permission =
      //     deviceAPI >= 33
      //       ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
      //       : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
      //   requestMultiple([
      //     PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
      //     permission,
      //   ]).then(statuses => {
      //     if (
      //       'granted' ===
      //         statuses[PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE] ||
      //       'granted' === statuses[permission]
      //     ) {
      //       onDownload(fileUrl, title, callBack);
      //     } else {
      //       Alert.alert(
      //         t('validations.permissionAllow'),
      //         t('validations.permissionMsg'),
      //         [
      //           {text: 'No'},
      //           {
      //             text: 'Yes',
      //             style: 'cancel',
      //             onPress: () => openSettings().catch(() => {}),
      //           },
      //         ],
      //       );
      //     }
      //   });
      // }
    } catch (e) {
      debugLog('file check error ===', e);
    }
    setTimeout(() => {
      disable = true;
    }, 2500);
  }
};

const handleErrMsg = (res: any) => {
  let errMsg = 'Something went wrong!';

  // Check if res contains an array of errors
  if (Array.isArray(res?.errors)) {
    errMsg = res.errors[0]?.message || res.errors[0] || errMsg;
  }
  // Check if res.message is an array
  else if (Array.isArray(res?.message)) {
    errMsg = res.message[0] || errMsg;
  }
  // Check if res.message or res.errors are strings
  else if (
    typeof res?.message === 'string' ||
    typeof res?.errors === 'string'
  ) {
    errMsg = res.message || res.errors || errMsg;
  }
  // Check if res contains an object with nested errors
  else if (typeof res === 'object' && res !== null) {
    const errorKey = Object.keys(res)[0];
    if (Array.isArray(res[errorKey])) {
      errMsg = res[errorKey][0] || errMsg;
    } else if (typeof res[errorKey] === 'object') {
      const nestedKey = Object.keys(res[errorKey])[0];
      errMsg = res[errorKey][nestedKey]?.[0] || errMsg;
    }
  }

  return errMsg;
};

const debounce = (func: any, delay: number) => {
  let timeoutId: any;
  return (...args: any) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};

function getSectionPriority(section: Section) {
  const {code} = section.layout_type;
  return code;
}

const orderHomeData = (dataList: Section) => {
  const priorityOrder = [
    'notice',
    'top_categories',
    'main_slider',
    'top_brands',
    'recently_vw_carousel',
  ];
  const recentlyData = dataList?.filter(
    (item: Section) => item.layout_type?.code === 'recently_vw_carousel',
  );
  let recently = [
    {
      description: 'Recently Viewed Description',
      elements: [],
      header_type: 'tag',
      landing_url: null,
      layout_type: {
        code: 'recently_vw_carousel',
        label: 'RECENTLY_VW_CAROUSEL',
        total_elements: 20,
        type: 'product_carousel',
      },
      sort_order: 7,
      section_code: 'recently_viewed',
      title: 'Recently Viewed',
    },
  ];
  const data =
    recentlyData.length === 0 ? [...dataList, ...recently] : dataList;
  // Map sections to priority and filter out sections that don't match any priority
  const sortedData = data
    .filter((section: Section) =>
      priorityOrder.includes(getSectionPriority(section)),
    )
    .sort((a, b) => {
      const priorityA = priorityOrder.indexOf(getSectionPriority(a));
      const priorityB = priorityOrder.indexOf(getSectionPriority(b));
      return priorityA - priorityB;
    });
  const filteredData = data?.filter(
    (item: Section) =>
      item.layout_type?.code !== 'notice' &&
      item.layout_type?.code !== 'top_categories' &&
      item.layout_type?.code !== 'main_slider' &&
      item.layout_type?.code !== 'top_brands' &&
      item.layout_type?.code !== 'recently_vw_carousel',
  );
  return [...sortedData, ...filteredData];
};

const checkDevice = () => {
  const tablet = DeviceInfo.isTablet();
  const deviceModel = DeviceInfo.getDeviceId();
  return tablet || deviceModel?.startsWith('iPad') ? true : false;
};

const openWhatsApp = async (link: string) => {
  try {
    const url = 'whatsapp://send?text=Hello';

    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(link);
    } else {
      const storeUrl =
        Platform.OS === 'ios'
          ? 'https://apps.apple.com/app/whatsapp-messenger/id310633997'
          : 'https://play.google.com/store/apps/details?id=com.whatsapp';

      await Linking.openURL(storeUrl);
    }
  } catch (error) {
    debugLog('Error checking WhatsApp:', error);
  }
};

const isVideoUrl = (url: string) => {
  const indexOfTheAssetType = url.split('.').length - 1; // because the extension will always be at the end
  const assetType = url.split('.')[indexOfTheAssetType].toUpperCase();
  const isTheAssetVideoType = [
    'WEBM',
    'MPG',
    'MPEG',
    'MPE',
    'MP4',
    'M4P',
    'M4V',
    'AVI',
    'WMV',
    'MOV',
  ].includes(assetType);
  return isTheAssetVideoType;
};

const formaNumber = (x: string) => {
  x = x.toString();
  var lastThree = x.substring(x.length - 3);
  var otherNumbers = x.substring(0, x.length - 3);
  if (otherNumbers != '') {
    lastThree = ',' + lastThree;
  }
  return otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
};

const nameSplit = (str: string) => {
  const lastSpaceIndex = str.lastIndexOf(' ');
  const fname = lastSpaceIndex !== -1 ? str.substring(0, lastSpaceIndex) : str;
  const lname = lastSpaceIndex !== -1 ? str.substring(lastSpaceIndex + 1) : '';
  return {fname, lname};
};
function generateRandomString(length = 20) {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
const getWebLink = (str: string) => {
  if (!str) return WEBSITE_URL;
  if (str.startsWith('http')) return str;
  const path = str === '/magazine' ? 'magazine' : str.replace(/^\/+/, '');
  return `${WEBSITE_URL}${path}`;
};

const truncateText = (s: string | null | undefined, maxL: number): string => {
  if (typeof s !== 'string' || maxL <= 0) {
    return '';
  }
  return s.length <= maxL ? s : s.slice(0, maxL) + '...';
};

const btnClickCallBack = callBack => {
  if (disable) {
    disable = false;
    callBack();
    setTimeout(() => {
      disable = true;
    }, 2000);
  }
};

const toRadians = degrees => {
  var pi = Math.PI;
  return degrees * (pi / 180);
};

const distance = (origin, destination, unit) => {
  let lat1 = origin[0];
  let lat2 = destination[0];
  let lon1 = origin[1];
  let lon2 = destination[1];
  let radius = 6371; // earth radius in km
  // let radius = 6371000 // meters
  let dlat = toRadians(lat2 - lat1);
  let dlon = toRadians(lon2 - lon1);
  let a =
    Math.sin(dlat / 2) * Math.sin(dlat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dlon / 2) *
      Math.sin(dlon / 2);
  let c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  let km = radius * c;
  let miles = km * 0.621371;
  return unit === 'km'
    ? km
      ? km.toFixed(2)
      : 0
    : miles
    ? miles.toFixed(2)
    : 0;
};

const calculateDistance = (lat1, lat2, long1, long2) => {
  let p = 0.017453292519943295; // Math.PI / 180
  let c = Math.cos;
  let a =
    0.5 -
    c((lat1 - lat2) * p) / 2 +
    (c(lat2 * p) * c(lat1 * p) * (1 - c((long1 - long2) * p))) / 2;
  let dis = 12742 * Math.asin(Math.sqrt(a)); // 2 * R; R = 6371 km
  return dis;
};

const getCurrentLocation = (callBack, hide = true) => {
  checkMultiple(
    Platform.OS === 'ios'
      ? [PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]
      : [PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION],
  )
    .then(async result => {
      if (
        result[
          Platform.OS === 'ios'
            ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
            : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
        ] === 'granted'
      ) {
        try {
          if (Platform.OS === 'ios') {
            await Geolocation.requestAuthorization('whenInUse');
          }
          Geolocation.getCurrentPosition(
            position => callBack(position),
            async error => {},
            {enableHighAccuracy: false, timeout: 10000, maximumAge: 600000},
          );
        } catch (error) {}
      } else {
        if (hide) {
          setTimeout(() => {
            Alert.alert(t('address.locPerTitle'), t('address.locPerMsg'), [
              {text: t('buttons.notNow')},
              {
                text: t('buttons.openSetting'),
                style: 'cancel',
                onPress: () => openSettings(),
              },
            ]);
          }, 200);
        }
      }
    })
    .catch(() => {});
};

const getTag = (name: string) => {
  const title =
    name === 'Home' || name === 'Clinic' ? name : name ? 'Other' : '';
  const tag = addressTag?.find(item => item.title === title)?.image || 'home2';
  return tag;
};
const openInBrowser = async finalUrl => {
  if (!finalUrl) return;

  try {
    if (Platform.OS === 'android') {
      // Attempt to open in Chrome using the Chrome-specific URI scheme
      const chromeUrl = `googlechrome://navigate?url=${finalUrl}`;
      const supported = await Linking.canOpenURL(chromeUrl);
      if (supported) {
        await Linking.openURL(chromeUrl);
      } else {
        // Fallback to default browser
        await Linking.openURL(finalUrl);
      }
    } else if (Platform.OS === 'ios') {
      // Attempt to open in Chrome using the Chrome-specific URI scheme
      const chromeUrl = finalUrl.replace(/^https?:\/\//, 'googlechrome://');
      const supported = await Linking.canOpenURL(chromeUrl);
      if (supported) {
        await Linking.openURL(chromeUrl);
      } else {
        // Fallback to Safari
        await Linking.openURL(finalUrl);
      }
    } else {
      // For other platforms, open in default browser
      await Linking.openURL(finalUrl);
    }
  } catch (error) {
    console.error('An error occurred while opening the URL:', error);
    // Fallback to default browser
    await Linking.openURL(finalUrl);
  }
};

const cardTabType = {
  Bestdeal: 'Bestdeal',
  Wishlist: 'Wishlist',
};
const billingAdd = async (addressId, addresses) => {
  const shippingAddressId = await localStorage.get('shippingAddressId');
  const billingList = addresses?.filter(
    item => item?.default_billing && !item?.default_shipping,
  );
  const checkBillingAdd = billingList?.length > 0 ? true : false;
  let billing_address = null;
  if (checkBillingAdd && Number(shippingAddressId) == addressId) {
    const billingAddress = billingList[0];
    billing_address = {
      country_code: billingAddress?.country_code ?? billingAddress?.country_id,
      country_id: billingAddress?.country_id ?? 'IN',
      region_code: billingAddress?.region?.region_code,
      street: billingAddress.street,
      postcode: billingAddress.postcode,
      city: billingAddress.city,
      firstname: billingAddress.firstname,
      lastname: billingAddress.lastname,
      telephone: billingAddress.telephone,
      gst_id: billingAddress?.vat_id,
      same_as_billing: false,
      alternate_mobile:
        billingAddress?.custom_attributes?.find(
          attribute => attribute?.attribute_code === 'alternate_telephone',
        ).value ?? null,
      region_id: billingAddress?.region?.region_id ?? 0,
      region: billingAddress?.region?.region,
    };
  }
  return billing_address;
};
const onCheckGst = async (gst, name) => {
  const gstResp = await checkValidateGst({
    gstin: gst,
    state_name: name,
  });
  if (!gstResp?.data?.success) {
    const res = gstResp?.data?.data;
    const errMsg =
      res?.errors[0]?.message || res?.errors[0] || t('addressCard.gstValid');
    showErrorMessage(handleErrMsg(res));
    return {status: false, error: errMsg};
  }
  return {status: true, error: ''};
};

const fullNameText = address => {
  return address?.firstname === address?.lastname
    ? address?.firstname || ''
    : `${address?.firstname || ''} ${address?.lastname || ''}`.trim();
};

const recommendedTabType = {
  Similar: 'Similar Products',
  Related: 'Related Products',
  More: 'More From This Brand',
};

export {
  getFileExtension,
  formatBytes,
  orderStatusColor,
  getOrderStatusIcon,
  downloadFile,
  handleErrMsg,
  debounce,
  orderHomeData,
  checkDevice,
  openWhatsApp,
  isVideoUrl,
  formaNumber,
  defaultPrice,
  formatDate,
  stringReg,
  fullNameReg,
  emailRegExp,
  phoneReg,
  fullNameRegex,
  videoSizeLimit,
  imageSizeLimit,
  sAllDevice,
  mDevice,
  sDevice,
  urlReg,
  htmlReg,
  nameSplit,
  pinCodeRegExp,
  numberRegExp,
  decimalNumRegExp,
  generateRandomString,
  getWebLink,
  truncateText,
  btnClickCallBack,
  distance,
  getCurrentLocation,
  getTag,
  openInBrowser,
  isIos,
  coinInfoShow,
  cardTabType,
  billingAdd,
  onCheckGst,
  fullNameText,
  recommendedTabType,
};

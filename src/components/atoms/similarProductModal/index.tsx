import React, {useCallback, useRef, useState} from 'react';
import {View, TouchableOpacity, FlatList, Dimensions} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer, ProductCardVertical} from 'components/atoms';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from '../../../routes';
import {useMemo} from 'react';
// import useToastConfig from '../toastConfig/toastConfig';
// import Toast from 'react-native-toast-message';
import OptimizedFlatList from 'components/hoc/optimizedFlatList';

type Props = {
  visible: boolean;
  similarProducts: Array<Product>;
  onClose: () => void;
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const SimilarProductModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {visible, onClose, similarProducts, navigation} = props;

  const flatListRef = useRef(null);
  const {width: screenWidth} = Dimensions.get('window');
  const [scrollX, setScrollX] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);
  const ITEM_WIDTH = screenWidth * 0.45;
  const ITEMS_TO_SCROLL = 2;
  // const {toastConfig} = useToastConfig();
  const handleScroll = useCallback(event => {
    const offsetX = event.nativeEvent.contentOffset.x;
    setScrollX(offsetX);
  }, []);

  const scrollToEnd = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollX + ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [scrollX, ITEM_WIDTH, ITEMS_TO_SCROLL]);

  const scrollToStart = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: scrollX - ITEM_WIDTH * ITEMS_TO_SCROLL,
        animated: true,
      });
    }
  }, [scrollX, ITEM_WIDTH, ITEMS_TO_SCROLL]);

  const createProductPressHandler = useCallback(
    (product: Product) => () => {
      navigation.push('ProductDetail', {
        productId: product?.product_id,
        ProductItems: {media: {mobile_image: product?.media?.mobile_image}},
      });
      onClose();
    },
    [onClose],
  );

  const emptyProduct = useMemo(() => ({}), []);

  const onContentSizeChange = useCallback(contentWidth => {
    setContentWidth(contentWidth);
  }, []);

  // Create a map of stable handler references
  const productHandlers = useMemo(() => {
    return similarProducts.reduce((handlers, product) => {
      handlers[product.product_id] = createProductPressHandler(product);
      return handlers;
    }, {});
  }, [similarProducts, createProductPressHandler]);

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={onClose}
      content={
        <View
          onLayout={event => setContainerWidth(event.nativeEvent.layout.width)}
          style={styles.viewProduct}>
          <View style={styles.offerZoneBannerView}>
            <View style={styles.excludeView}>
              <View>
                <Label
                  text={t('PDP.similarProduct')}
                  size="l"
                  fontFamily="SemiBold"
                  color="text"
                />
              </View>
              <View style={styles.fRow}>
                {scrollX > 0 && (
                  <TouchableOpacity onPress={scrollToStart}>
                    <ImageIcon icon="Include" size="x4l" />
                  </TouchableOpacity>
                )}
                <Spacer size="xms" />
                {scrollX + containerWidth <= contentWidth && (
                  <TouchableOpacity onPress={scrollToEnd}>
                    <ImageIcon icon="Exclude" size="x4l" />
                  </TouchableOpacity>
                )}
              </View>
            </View>
            <Spacer size="m" />
            <OptimizedFlatList
              horizontal
              data={similarProducts}
              style={styles.productView}
              ref={flatListRef}
              keyExtractor={(_, i) => i.toString()}
              renderItem={({item, index}) => {
                const handleProductPress = productHandlers[item.product_id];
                return (
                  <ProductCardVertical
                    index={index}
                    actionBtn={item?.action_btn}
                    size="small"
                    imageWithBorder={true}
                    maxWidth={0.45}
                    item={item}
                    skuId={item?.sku}
                    inStock={item.is_in_stock}
                    productType={item?.type}
                    maxSaleQty={item?.max_sale_qty}
                    demoAvailable={item?.is_demo}
                    msrp={item?.msrp}
                    image={item?.media?.mobile_image}
                    name={item?.name}
                    rewardPoint={item?.reward_points}
                    description={item?.short_description}
                    rating={(!!item?.average_rating
                      ? Number(item?.average_rating)
                      : 0
                    ).toFixed(1)}
                    ratingCount={
                      !!item?.rating_count ? `(${item?.rating_count})` : '(0)'
                    }
                    price={item?.price}
                    sellingPrice={item?.selling_price}
                    currencySymbol={item?.currency_symbol}
                    discount={item?.discount?.label}
                    onPress={handleProductPress}
                    freeProducts={emptyProduct}
                    showWishlist={true}
                    onClose={onClose}
                    navigation={navigation}
                    type='modal'
                  />
                );
              }}
              showsHorizontalScrollIndicator={false}
              onScroll={handleScroll}
              scrollEventThrottle={16}
              onContentSizeChange={onContentSizeChange}
            />
            <Spacer size="l" />
          </View>
          {/* <Toast config={toastConfig} swipeable={false} /> */}
        </View>
      }
    />
  );
};

export default React.memo(SimilarProductModal);

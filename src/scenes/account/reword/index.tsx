import React, {useCallback} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Header} from 'components/molecules';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from 'routes';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {useTheme} from '@react-navigation/native';
import stylesWithOutColor from './style';
import {ListView, Spacer} from 'components/atoms';
import {View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {t} from 'i18next';
import {productDummyImage} from 'utils/imageUrlHelper';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};
const data = [
  {
    id: 1,
    main: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Reward+page/Reward-Page_01.jpg',
  },
  {
    id: 2,
    main: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Reward+page/Reward-Page_02.jpg',
  },
  {
    id: 3,
    main: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Reward+page/Reward-Page_03.jpg',
  },
  {
    id: 4,
    main: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Reward+page/Reward-Page_04.jpg',
  },
  {
    id: 5,
    main: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Reward+page/Reward-Page_05.jpg',
  },
  {
    id: 6,
    main: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Reward+page/Reward-Page_06.jpg',
  },
  {
    id: 7,
    main: 'https://s3.ap-south-1.amazonaws.com/dentalkart-media/React/Reward+page/Reward-Page_07.jpg',
  },
];
const RewordScene = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const listViewKeyExtractor = useCallback(
    (item, index) => index.toString(),
    [],
  );
  const renderItems = useCallback(({item, index}: {item: any; index: any}) => {
    return (
      <View key={index}>
        <FastImage
          onError={e => {
            e.target.uri = productDummyImage;
          }}
          resizeMode="stretch"
          style={styles.img}
          source={{
            uri: item.main,
          }}
        />
      </View>
    );
  }, []);
  return (
    <SafeAreaView style={styles.container}>
      <Header
        backButton
        searchIcon
        bagIcon
        navigation={navigation}
        text={t('profile.myRewards')}
      />
      <Spacer size="l" />
      <ListView
        keyExtractor={listViewKeyExtractor}
        data={data}
        renderItem={renderItems}
      />
    </SafeAreaView>
  );
};

export default RewordScene;

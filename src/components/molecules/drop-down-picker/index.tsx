import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {TextInputProps, View} from 'react-native';
import {Label, Spacer} from 'components/atoms';
import {SelectList} from 'react-native-dropdown-select-list';
import {Fonts} from 'common';

import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useMemo} from 'react';

type Props = {
  options: Array<DropDownOptionsType>;
  onSelect: (text: string) => void;
  selected?: DropDownOptionsType;
  placeholder: string;
  searchPlaceholder: string;
  notFoundText: string;
  error?: string;
  text?: string;
  fontFamily?: keyof typeof Fonts;
  styleBox?: TextInputProps['style'];
  save?: string;
  defaultOption?: any;
  fieldMainetry?: boolean;
};

const DropDownPicker = ({
  options,
  onSelect,
  searchPlaceholder,
  notFoundText,
  error,
  fontFamily = 'Regular',
  styleBox,
  text = 'hjk',
  save = 'value',
  defaultOption,
  fieldMainetry = false,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <>
      <View style={[fieldMainetry && {flexDirection: 'row'}]}>
        <Label text={text} />
        {fieldMainetry && <Label color="textError" text={'*'} />}
      </View>
      <Spacer type="Vertical" size="xm" />
      <SelectList
        defaultOption={defaultOption}
        searchPlaceholder={searchPlaceholder}
        setSelected={onSelect}
        data={options}
        save={save}
        notFoundText={notFoundText}
        boxStyles={[
          styles.container,
          error ? styles.errorBorder : {},
          styleBox,
        ]}
        inputStyles={styles.input}
        dropdownStyles={styles.dropDownContainer}
        fontFamily={Fonts[fontFamily]}
      />
      {error && (
        <View style={styles.errorContainer}>
          <Label color="textError" size={'m'} text={error} />
        </View>
      )}
    </>
  );
};

export default React.memo(DropDownPicker);

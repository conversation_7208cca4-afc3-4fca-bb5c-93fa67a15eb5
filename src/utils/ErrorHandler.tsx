import React, { Component, ReactNode } from 'react';
import { View, Text } from 'react-native';
import { logComponentError } from './captureError';
import { debugError } from './debugLog';

const TAG = 'ErrorDentalKart: ';

interface Props {
  children: ReactNode;
  componentName?: string;
  onErrorComponent?: ReactNode;
}

interface State {
  hasError: boolean;
}

class ErrorHandler extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    debugError(TAG, "Error in component:", error);
    return { hasError: true };
  }

  componentDidCatch(error: Error, info: any) {
    debugError(TAG, { error, info });

    const { componentName } = this.props;
    const fileName = componentName || 'UnknownComponent';

    logComponentError(error, fileName);
  }

  render() {
    const { hasError } = this.state;
    const { children, onErrorComponent } = this.props;

    if (hasError) {
      return <View>{onErrorComponent ? onErrorComponent : null}</View>; // Renders nothing if an error occurs
    }

    return children;
  }
}

export default ErrorHandler;
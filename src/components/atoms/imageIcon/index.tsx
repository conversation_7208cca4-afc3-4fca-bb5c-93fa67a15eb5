import React, {useMemo} from 'react';
import {ImageProps, ViewProps} from 'react-native';
import Icons from 'common/icons';
import {useTheme} from '@react-navigation/native';
import FastImagesItem from '../fastImages';
import {Sizes} from 'common';

type Props = {
  icon?: keyof typeof Icons;
  tintColor?: keyof Theme['colors'] | string;
  resizeMode?: ImageProps['resizeMode'];
  style?: ViewProps['style'];
  sourceType?: 'local' | 'url';
  source?: string;
  size?: keyof typeof Sizes;
};

const ImageIcon = ({
  icon,
  tintColor,
  style,
  resizeMode = 'contain',
  sourceType = 'local',
  source,
  size = 'l',
}: Props) => {
  const {colors} = useTheme();

  const fastImageStyle = useMemo(() => {
    return [
      size && {height: Sizes[size], width: Sizes[size]},
      tintColor ? {tintColor: colors[tintColor]} : {},
      style,
    ];
  }, [size, tintColor, colors, style]);

  const tintColorValue = useMemo(() => {
    if (!tintColor) return undefined;
    return colors[tintColor as keyof Theme['colors']] || tintColor;
  }, [tintColor, colors]);

  const imageSource = useMemo(
    () => (sourceType === 'local' ? Icons[icon] : {uri: source}),
    [sourceType, icon, source],
  );

  return (
    <>
      <FastImagesItem
        resizeMode={resizeMode}
        tintColor={tintColorValue}
        FastImageStyle={fastImageStyle}
        source={imageSource}
        index={0}
      />
    </>
  );
};

export default ImageIcon;

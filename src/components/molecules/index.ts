import AddOnsList from './add-ons-list';
import AddressCard from './address-card';
import Button from './button';
import DropDownPicker from './drop-down-picker';
import Header from './header';
import InputBox from './input-box';
import InputLabel from './input-label';
import TextInputBox from './textInputBox';
import PhoneInputText from './phone-input';
import ProfileListRow from './profile-list-row';
import CountDown from '../organisms/countDown';
import WishListHeader from '../molecules/Wishlist-header';
import EditWishList from '../molecules/edit-Wishlist';
// import NewsList from '../molecules/newsList';
import NewsList from 'scenes/news/newsList';
import SuggestProduct from '../molecules/suggest_Product';
import Filters from '../molecules/filters';
import Accordion from '../molecules/accordion';
import SortingOnSearch from './sortingOnSearch';
import RenderOrderedItems from './renderOrderedItems';
import DropDownList from './drop-down-list';
import RenderCustomHTML from './render-html';
import ChildProductModal from './childProductModule';
import AccountModal from './accountModal';
import CartOfferCarousel from './cartOffersCarousel';
import errorPage from './404page';
import LogoutUserModal from '../molecules/logout';
import EmptyAddress from './emptyAddress';
import ProductImageSlider from './productImageSlider';
import ConnectWithUs from './connect-with-us';
import CustomStatusBar from './customStatusBar';
import CustomDialog from './customDialog';

export {
  AddOnsList,
  AddressCard,
  Button,
  DropDownPicker,
  Header,
  InputBox,
  InputLabel,
  PhoneInputText,
  ProfileListRow,
  CountDown,
  WishListHeader,
  EditWishList,
  NewsList,
  SuggestProduct,
  Filters,
  Accordion,
  SortingOnSearch,
  RenderOrderedItems,
  DropDownList,
  RenderCustomHTML,
  ChildProductModal,
  AccountModal,
  CartOfferCarousel,
  errorPage,
  LogoutUserModal,
  EmptyAddress,
  ProductImageSlider,
  TextInputBox,
  ConnectWithUs,
  CustomStatusBar,
  CustomDialog,
};

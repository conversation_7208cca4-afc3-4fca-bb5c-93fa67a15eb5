import GET_NEWS from '../newsApi/query/get_news.gql';
import ADD_TO_NEWS_HISTORY from '../newsApi/mutation/add_news_to_history.gpl';
import BOOKMARK_NEWS from '../newsApi/mutation/bookmark_news.gpl';
import LIKE_NEWS from '../newsApi/mutation/like_news.gpl';
import MULTIMEDIA from '../newsApi/mutation/like_news.gpl';
import NOTIFY_ME from '../newsApi/mutation/notify_me.gpl';

export {
  GET_NEWS,
  ADD_TO_NEWS_HISTORY,
  BOOKMAR<PERSON>_NEWS,
  LIKE_NEWS,
  MULTIMEDIA,
  NOTIFY_ME,
};

import React, {useCallback, useState} from 'react';
import {TouchableOpacity, View, Pressable} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import {
  Label,
  OrderImageModal,
  ImageIcon,
  Spacer,
  Separator,
} from 'components/atoms';
import {Button} from 'components/molecules';
import {RootStackParamsList} from 'routes';
import {snakeToTitleCase} from 'utils/formatter';
import getImageUrl from 'utils/imageUrlHelper';
import {
  orderStatusColor,
  formatDate,
  sAllDevice,
  coinInfoShow,
} from 'utils/utils';
import {useMemo} from 'react';

type Props = {
  index: number;
  item: OrderResponse;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
  totalPrize: boolean;
  type: string;
  currency: string;
  setModalVisible: (val: boolean) => void;
  dropDownIndex: number;
  setDropDownIndex: (val: number | null) => void;
  canCancel: CanCancel;
  setOrderId: (id: string) => void;
  orderId: string;
  onSelectMethod: (orderId: number) => void;
  circularProgress: number;
  onInfoClick: (id: string) => void;
};

const OrderCard = ({
  index,
  item,
  totalPrize,
  type,
  navigation,
  currency,
  setModalVisible,
  setDropDownIndex,
  dropDownIndex,
  canCancel,
  setOrderId,
  orderId,
  onSelectMethod,
  circularProgress,
  onInfoClick,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [modalVisibleImage, setModalVisibleImage] = useState<boolean>(false);
  const [productModalData, setProductModalData] = useState<OrderProductItem[]>(
    item?.data?.items || [],
  );
  const [activeOrderId, setActiveOrderId] = useState<string | null>(null);
  const statusObj = orderStatusColor(item?.data?.status?.label, colors);

  const handleOpenModal = useCallback(() => {
    setProductModalData(item?.data?.items || []);
    setModalVisibleImage(true);
  }, [item?.data?.items]);

  const handleToggle = (id: string) => {
    setActiveOrderId(prevOrderId => (prevOrderId === id ? null : id));
  };

  const productIds = item?.data?.items?.map(
    (data: OrderProductItem) => data?.product_id,
  );

  const calculateEarnedCoin = (data: OrderProductItem) => {
    return data?.reduce(
      (total: any, val: OrderProductItem) =>
        total + val?.reward_earned_coins * val?.ordered_qty,
      0,
    );
  };

  const totalEarnedCoin = calculateEarnedCoin(productModalData);

  const getEstimateStatus = () => {
    if (!item?.data) return '';
    let statusColor = 'text2';
    let dateColor = 'green2';
    let expectedDate = '';
    let status = '';
    let estimateDate;
    if (item.data.dates?.estimated_delivery_date && !returnShow) {
      status = t('orderTrack.estOn');
      estimateDate = item.data.dates?.estimated_delivery_date;
    } else if (item.data.dates?.delivered?.date) {
      status = t('orderListing.deliveredOn');
      estimateDate = item.data.dates?.delivered?.date;
    } else if (item.data.dates?.cancelled?.date && !returnShow) {
      status = t('orderTrack.cancelOn');
      estimateDate = item.data.dates?.cancelled?.date;
      statusColor = 'persianRed';
      dateColor = 'persianRed';
    } else if (item.data.dates?.returned?.date && !returnShow) {
      status = t('orderListing.returnedOn');
      estimateDate = item.data.dates?.returned?.date;
      statusColor = 'persianRed';
      dateColor = 'persianRed';
    }
    if (!estimateDate) return '';
    expectedDate = formatDate(estimateDate, 'dddd, DD MMM YYYY');
    return {status, expectedDate, statusColor, dateColor};
  };

  const estimate = getEstimateStatus();

  const statusContainerStyle = useMemo(() => {
    return [styles.approvedView, {backgroundColor: statusObj?.bgColor}];
  }, [statusObj?.bgColor, styles.approvedView]);
  const returnShow =
    item?.data?.status?.label === 'Delivered' ||
    item?.data?.status?.label === 'Partially Delivered';
  return (
    <View key={index}>
      <View style={styles.box}>
        <View style={styles.processingContinuer}>
          <View style={styles.durationsBox}>
            <Label
              size="m"
              color="text2"
              text={`${t('orderListing.orderID')} - `}
              fontFamily="Medium"
            />
            <Spacer type="Horizontal" size="s" />
            <Label
              text={item?.data?.order_id}
              fontFamily="Medium"
              size="m"
              color="text2"
            />
          </View>
          <View style={statusContainerStyle}>
            <Label
              size="m"
              color={statusObj?.color}
              text={
                statusObj?.status
                  ? statusObj?.status
                  : snakeToTitleCase(
                      item?.data?.status?.label,
                      'Approved',
                      'Request Approved',
                    )
              }
              fontFamily="Medium"
            />
          </View>
        </View>
        <Label
          size="m"
          color="text2"
          text={
            type === 'return'
              ? `${t(
                  'orderListing.requestedOn',
                )} - ${item?.data?.dates?.ordered?.date?.substr(0, 10)}`
              : `${t(
                  'orderListing.orderedOn',
                )} - ${item?.data?.dates?.ordered?.date?.substr(0, 10)}`
          }
          fontFamily="Medium"
        />
        {estimate && estimate?.expectedDate && (
          <Label
            fontFamily="Medium"
            color={estimate?.statusColor}
            text={estimate?.status}
            size="m">
            <Spacer type="Horizontal" size="s" />
            <Label
              size="m"
              text={estimate?.expectedDate}
              fontFamily="Medium"
              color={estimate?.dateColor}
            />
          </Label>
        )}
        <Spacer type="Vertical" size="sx" />
        {type !== 'return' ? (
          <Label
            size="mx"
            color="categoryTitle"
            text={`${t('orderListing.totalProducts')} - ${
              item?.data?.items?.length < 9 ? '0' : ''
            }${item?.data?.items?.length}`}
            fontFamily="SemiBold"
          />
        ) : (
          <Label
            size="mx"
            color="categoryTitle"
            text={`${item?.data?.name?.substring(0, 25) + '...'}`}
            fontFamily="SemiBold"
          />
        )}
        <Label
          size="m"
          color="text"
          text={`${t('cart.quantity')} ${
            item?.data?.order_summary?.total_product_qty < 9 ? '0' : ''
          }${item?.data?.order_summary?.total_product_qty}`}
          fontFamily="Medium"
        />
        <Spacer type="Vertical" size="sx" />
        <Label
          color="text"
          text={`${t('orderListing.orderAmount1')} - ${
            currency?.symbol ? currency?.symbol : t('PDP.rupee')
          }${parseFloat(item?.data?.order_summary?.order_amount).toFixed(2)}`}
          fontFamily="SemiBold"
          size="mx"
        />
        {type === 'order' && (
          <>
            {item?.data?.order_summary?.total_savings > 0 && (
              <Label
                size="mx"
                color="green2"
                text={`${t('orderListing.savedRs')} ${
                  item?.data?.order_summary?.total_savings
                }`}
                fontFamily="Medium"
              />
            )}

            {(item?.data?.rewards?.earned_points > 0 ||
              item?.data?.rewards?.spent_points > 0) && (
              <View style={styles.earnedView}>
                {item?.data?.rewards?.earned_points > 0 && (
                  <View style={styles.earnedView}>
                    <ImageIcon
                      resizeMode="contain"
                      style={styles.rewardIconSmall}
                      sourceType={
                        item?.data?.rewards?.reward_icon ? 'url' : 'local'
                      }
                      icon="coin"
                      source={getImageUrl(
                        item?.data?.rewards?.reward_icon,
                        'coin',
                      )}
                    />
                    <Spacer type="Horizontal" size="xm" />
                    <Label
                      size="mx"
                      color="text"
                      text={`${t('orderListing.earned')} - ${
                        item?.data?.rewards?.earned_points
                      } `}
                      fontFamily="Medium"
                    />
                    {coinInfoShow(item?.data?.status?.label) && (
                      <Pressable onPress={() => onInfoClick()}>
                        <ImageIcon
                          size="xl"
                          icon="infoCircle"
                          tintColor="text2"
                          style={styles.infoStyle}
                        />
                      </Pressable>
                    )}
                    <Spacer type="Horizontal" size="m" />
                  </View>
                )}
                {item?.data?.rewards?.spent_points > 0 && (
                  <View style={styles.earnedView}>
                    <ImageIcon
                      resizeMode="contain"
                      style={styles.rewardIconSmall}
                      sourceType={
                        item?.data?.rewards?.reward_icon ? 'url' : 'local'
                      }
                      icon="coin"
                      source={getImageUrl(
                        item?.data?.rewards?.reward_icon,
                        'coin',
                      )}
                    />
                    <Spacer type="Horizontal" size="xm" />
                    <Label
                      size="mx"
                      color="text"
                      text={`${t('myRewords.spent')} - ${
                        item?.data?.rewards?.spent_points
                      }`}
                      fontFamily="Medium"
                    />
                  </View>
                )}
              </View>
            )}
          </>
        )}

        {totalPrize ? null : <Label text={t('orderListing.finger')} />}
        <Spacer size="xm" />
        <View style={styles.imageBox}>
          <View style={styles.mainImage}>
            {item?.data?.items ? (
              item?.data?.items?.slice(0, 1).map((data: any, i: number) => (
                <TouchableOpacity
                  key={i}
                  style={styles.image}
                  onPress={handleOpenModal}>
                  {item?.data?.items?.length > 1 && (
                    <View style={styles.underPrizeIconView}>
                      <TouchableOpacity
                        onPress={() => setModalVisibleImage(true)}>
                        <Label
                          text={`+${item?.data?.items?.length - 1}`}
                          size="mx"
                          color="whiteColor"
                          fontFamily="Medium"
                        />
                      </TouchableOpacity>
                    </View>
                  )}
                  <ImageIcon
                    resizeMode="contain"
                    style={styles.imageSize}
                    sourceType="url"
                    source={getImageUrl(data?.thumbnail, 'product')}
                  />
                </TouchableOpacity>
              ))
            ) : (
              <View style={styles.image}>
                <ImageIcon
                  resizeMode="contain"
                  style={styles.imageSize}
                  sourceType={item?.image ? 'url' : 'local'}
                  icon="dentalkartLogo"
                  source={item?.image ? item?.image : 'lockIcon'}
                />
              </View>
            )}
          </View>

          <View style={styles.buttonStyle}>
            <Button
              onPress={() => navigation.navigate('OrderDetail', {item, type})}
              text={t('buttons.viewOrder')}
              type={
                item?.data?.status?.label === 'Payment Pending'
                  ? 'disabled'
                  : 'secondary'
              }
              paddingHorizontal="m"
              labelColor="whiteColor"
              weight="500"
              labelSize={sAllDevice ? 'm' : 'mx'}
              radius="xms"
              size="extra-small"
              disabled={
                item?.data?.status?.label === 'Payment Pending' ? true : false
              }
            />
            {type === 'return' ? null : (
              <>
                {item?.data?.status?.label === 'Payment Pending' ? (
                  <>
                    <Spacer
                      type="Horizontal"
                      size={sAllDevice ? 'sx' : 'xms'}
                    />
                    <Button
                      onPress={() => {
                        onSelectMethod(item?.data?.order_id);
                      }}
                      text={
                        circularProgress !== item?.data?.order_id
                          ? t('retryPaymentButton.retryPayment')
                          : t('retryPaymentButton.Retrying')
                      }
                      paddingHorizontal="m"
                      labelColor="whiteColor"
                      weight="500"
                      labelSize={sAllDevice ? 'm' : 'mx'}
                      radius="xms"
                      withGradient
                      size="extra-small"
                      gradientColors={[colors.coral, colors.persimmon]}
                    />
                  </>
                ) : (
                  <>
                    <Spacer
                      type="Horizontal"
                      size={sAllDevice ? 'sx' : 'xms'}
                    />
                    <Button
                      onPress={() =>
                        navigation?.navigate('BuyAgainScene', {
                          productIds: productIds,
                          item,
                        })
                      }
                      text={t('orderListing.buyAgain')}
                      paddingHorizontal="m"
                      labelColor="whiteColor"
                      weight="500"
                      labelSize={sAllDevice ? 'm' : 'mx'}
                      radius="xms"
                      withGradient
                      size="extra-small"
                      gradientColors={[colors.coral, colors.persimmon]}
                    />
                  </>
                )}
              </>
            )}
            {type === 'return' ? null : (
              <>
                <Spacer type="Horizontal" size="xm" />
                <TouchableOpacity
                  onPress={() => {
                    setOrderId(item?.data?.order_id);
                    handleToggle(item?.data?.order_id);
                    setDropDownIndex(dropDownIndex === index ? null : index);
                  }}
                  style={styles.buyAgainDropView}>
                  <ImageIcon
                    style={styles.buyAgainSubView}
                    icon={
                      dropDownIndex === index &&
                      activeOrderId === item?.data?.order_id
                        ? 'arrowUp'
                        : 'arrowBottom'
                    }
                  />
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>

        <Spacer type="Vertical" size="xm" />
        {modalVisibleImage && (
          <OrderImageModal
            visible={modalVisibleImage}
            onClose={() => setModalVisibleImage(false)}
            data={productModalData}
            item={item}
            totalPrice={item?.data?.order_summary?.order_amount || 0}
            navigation={navigation}
            totalEarnedCoin={totalEarnedCoin}
            spentPoint={item?.data?.rewards?.spent_points}
            type="orderBuyAgain"
          />
        )}
      </View>
      {dropDownIndex === index && item?.data?.order_id === orderId ? (
        <View style={styles.canCancelView}>
          <TouchableOpacity
            onPress={() =>
              canCancel?.is_cancelable === true ? setModalVisible(true) : {}
            }
            style={styles.returnOrderView}>
            <ImageIcon
              icon="closeIcons"
              style={styles.iconStyle}
              tintColor={canCancel?.is_cancelable === true ? 'text2' : 'grey2'}
              resizeMode="stretch"
            />
            <Label
              text={t('buttons.cancel')}
              size="mx"
              color={canCancel?.is_cancelable === true ? 'text2' : 'grey2'}
              weight="500"
            />
          </TouchableOpacity>
          <Separator height="xsl" Vertical thickness="x" color="grey" />
          <TouchableOpacity
            onPress={() =>
              returnShow
                ? navigation.navigate('OrderReturnListScene', {
                    orderId: item?.data?.order_id,
                  })
                : {}
            }
            style={styles.returnOrderView}>
            <ImageIcon
              icon="orderReturnIcon"
              style={styles.iconStyle}
              tintColor={returnShow ? 'text2' : 'grey2'}
              resizeMode="stretch"
            />
            <Label
              text={t('orderTrack.return')}
              size="mx"
              weight="500"
              color={returnShow ? 'text2' : 'grey2'}
            />
          </TouchableOpacity>
        </View>
      ) : null}
    </View>
  );
};

export default OrderCard;

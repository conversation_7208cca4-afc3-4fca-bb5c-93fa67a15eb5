/* eslint-disable react/no-unstable-nested-components */
import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {
  View,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  BackHandler,
  InteractionManager,
  Platform,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Button, EmptyAddress, Header} from 'components/molecules';
import {Spacer, AddressCard} from 'components/atoms';
import {RootStackParamsList} from 'routes';
import stylesWithOutColor from './style';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {useDispatch} from 'react-redux';
import {getUserInfo, setLoading} from 'app-redux-store/slice/appSlice';
import {RouteProp, useIsFocused, useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {getMe} from 'services/account';
import {deleteAddress, updateCostumerAddress} from 'services/address';
import {AddressLoader} from 'skeletonLoader';
import localStorage from 'utils/localStorage';
import ErrorHandler from 'utils/ErrorHandler';
import {debugLog} from 'utils/debugLog';
import {getCurrentLocation} from 'utils/utils';
import {
  requestMultiple,
  PERMISSIONS,
  openSettings,
} from 'react-native-permissions';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'AddressList'>;
};
const AddressListScene = ({navigation, route}: Props) => {
  const TAG = 'AddressListScreen';
  const isFocused = useIsFocused();
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [loader, setLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [state, setState] = useState({});
  const [coords, setCoords] = useState();
  const [toggleVal, setToggleVal] = useState();
  const [customerAddresses, setCustomerAddresses] = useState<
    CustomerAddressV2[] | []
  >([]);
  const dispatch = useDispatch();
  const insets = useSafeAreaInsets();
  const AddressData = useCallback(
    async (refresh = false) => {
      if (refresh) {
        setRefreshing(true);
      } else {
        setLoader(true);
      }
      const {data, status} = await getMe();
      setLoader(false);
      setRefreshing(false);
      if (status) {
        dispatch(getUserInfo());
        const addressData =
          data?.addresses?.length > 0
            ? data?.addresses
                ?.filter(
                  item =>
                    (!item?.default_billing && item?.default_shipping) ||
                    (item?.default_billing && item?.default_shipping) ||
                    (!item?.default_billing && !item?.default_shipping),
                )
                .sort((a, b) => b.default_shipping - a.default_shipping)
            : [];
        setCustomerAddresses(addressData);
      } else {
        showErrorMessage(errors?.message);
      }
    },
    [dispatch],
  );

  useEffect(() => {
    getCurrentLatLng();
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, [backAction]);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const getCurrentLatLng = () => {
    requestMultiple(
      Platform.OS === 'ios'
        ? [PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]
        : [PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION],
    ).then(result => {
      if (
        result[
          Platform.OS === 'ios'
            ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
            : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
        ] === 'granted'
      ) {
        getCurrentLocation(async position => {
          setCoords(position.coords);
        });
      } else {
        setTimeout(() => {
          Alert.alert(t('address.locPerTitle'), t('address.locPerMsg'), [
            {text: t('buttons.notNow')},
            {
              text: t('buttons.openSetting'),
              style: 'cancel',
              onPress: () => openSettings(),
            },
          ]);
        }, 200);
      }
    });
  };

  const addressDelete = useCallback(
    id => {
      const handleDelete = async () => {
        dispatch(setLoading(true));
        try {
          const {data, status} = await deleteAddress(id);
          dispatch(setLoading(false));
          if (data && status) {
            let deliveryAddress = await localStorage.get('delivery_address');
            if (deliveryAddress?.id === id) {
              const defaultAddress = customerAddresses.filter(
                item => item?.default_shipping,
              );
              if (defaultAddress?.length > 0) {
                await localStorage.set('delivery_address', defaultAddress[0]);
              }
            }
            AddressData();
            showErrorMessage(t('toastMassages.addressDeleted'));
          } else {
            AddressData();
            showErrorMessage(data?.message);
          }
        } catch (e) {
          dispatch(setLoading(false));
          AddressData();
          showErrorMessage(t('validations.someThingWrong'));
        }
      };

      Alert.alert(
        t('addressList.deletedAddress'),
        t('addressList.confirmDeleted'),
        [
          {
            text: 'Cancel',
            onPress: () => debugLog('Cancel Pressed'),
            style: 'cancel',
          },
          {
            text: 'Delete',
            onPress: handleDelete,
          },
        ],
        {cancelable: false},
      );
    },
    [dispatch, customerAddresses, AddressData, t],
  );

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      AddressData();
    });
  }, [isFocused]);

  const openMapsModal = useCallback(
    addressObj => {
      navigation.navigate('MapLocation', {
        deliverAddress: addressObj,
        coords: coords,
      });
    },
    [coords],
  );

  const onChangeToggle = useCallback(
    async (item, index) => {
      setToggleVal(item?.id);
      const updatedAddresses = customerAddresses
        .map((address, i) => ({
          ...address,
          default_shipping: i === index ? !item.default_shipping : false,
        }))
        .sort((a, b) => b.default_shipping - a.default_shipping);
      setTimeout(() => {
        setCustomerAddresses(updatedAddresses);
      }, 500);

      const obj = {...item, default_shipping: !item.default_shipping};
      const {data, status} = await updateCostumerAddress(obj);
      if (data && status) {
        const finalAddresses = updatedAddresses.map(addr =>
          addr.id === data.id
            ? {...item, default_shipping: data?.default_shipping}
            : addr,
        );
        setCustomerAddresses(finalAddresses);
        showSuccessMessage(t('toastMassages.defaultMessage'));
      }
      setTimeout(() => {
        setToggleVal(undefined);
        setState({});
      }, 500);
    },
    [customerAddresses, updateCostumerAddress],
  );

  const onEdit = useCallback(
    data => {
      const obj = {
        ...data,
        edit: true,
        lng: data?.longitude,
        lat: data?.latitude,
        formatted_address: data?.map_address,
      };
      openMapsModal(obj);
    },
    [openMapsModal],
  );

  const renderItem = useCallback(
    ({item, index}: {item: CustomerAddressV2; index: number}) => (
      <AddressCard
        item={item}
        index={index}
        coords={coords}
        toggleVal={toggleVal}
        onChangeToggle={(data, i) => onChangeToggle(data, i)}
        onDelete={id => addressDelete(id)}
        onEdit={data => onEdit(data)}
      />
    ),
    [coords, toggleVal, onChangeToggle, addressDelete, onEdit],
  );

  const itemSeparator = useCallback(() => <Spacer size="xm" />, []);

  const onPressNavigation = useCallback(() => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
  }, []);

  return (
    <SafeAreaView style={[styles.container, {paddingTop: insets.top}]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          style={styles.link}
          backButton
          navigation={navigation}
          searchIcon={true}
          bagIcon={true}
          text={t('manageAddress.manageAddress')}
          onPressNavigation={onPressNavigation}
        />
      </ErrorHandler>
      {loader ? (
        <AddressLoader />
      ) : customerAddresses?.length > 0 ? (
        <View style={styles.subView}>
          <Spacer size="xm" />
          <ErrorHandler
            componentName={`${TAG} CustomerAddresses List`}
            onErrorComponent={<View />}>
            <FlatList
              data={customerAddresses || []}
              extraData={customerAddresses}
              renderItem={renderItem}
              refreshing={refreshing}
              onRefresh={() => AddressData(true)}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) => index.toString()}
              ItemSeparatorComponent={itemSeparator}
            />
          </ErrorHandler>
          <View style={styles.btnContainer}>
            <Button
              style={styles.subCautionerBox}
              text={t('addressList.addNewAddress').toUpperCase()}
              onPress={openMapsModal}
              labelSize="l"
              type="secondary"
              radius="sx"
              labelColor="whiteColor"
              weight="500"
            />
          </View>
        </View>
      ) : (
        <EmptyAddress onClick={openMapsModal} />
      )}
    </SafeAreaView>
  );
};
export default AddressListScene;

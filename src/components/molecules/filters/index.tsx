import React, {useState, useCallback, useMemo} from 'react';
import {View, TouchableOpacity, ScrollView, Platform} from 'react-native';
import {
  CheckBox,
  ImageIcon,
  Label,
  Rail,
  RailSelected,
  Separator,
  Spacer,
  Thumb,
} from 'components/atoms';
import stylesWithOutColor from './style';
import Accordion from '../accordion';
import RangeSlider from 'rn-range-slider';
import Button from '../button';
import {Sizes} from 'common';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';

type Props = {
  searchInfo: number;
  defaultFilters: string;
  setFilterCount: number;
  setModelVisible: Boolean;
  applyFilters: string;
};

const Filters = ({
  searchInfo,
  defaultFilters,
  applyFilters,
  setModelVisible,
  setFilterCount,
}: Props) => {
  const [isPriceRangeOpen, setIsPriceRangeOpen] = useState(true);
  const [isRatingOpen, setIsRatingOpen] = useState(true);
  const [low, setLow] = useState(defaultFilters?.price?.min || 0);
  const [high, setHigh] = useState(defaultFilters?.price?.max || 500000);
  const [filters, setFilters] = useState({
    category: defaultFilters?.category,
    manufacturer: defaultFilters?.manufacturer,
    rating: defaultFilters?.rating,
    price: defaultFilters?.price,
  });
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const filterCount = useMemo(() => {
    let count =
      (filters?.category?.length || 0) +
      (filters?.manufacturer?.length || 0) +
      (filters?.rating ? 1 : 0) +
      (filters?.price && filters?.price?.min && filters?.price?.max ? 1 : 0);
    return count;
  }, [filters]);

  const renderThumb = useCallback(() => <Thumb />, []);
  const renderRail = useCallback(() => <Rail />, []);
  const renderRailSelected = useCallback(() => <RailSelected />, []);

  const selectCategories = useCallback(
    (category: unknown) => {
      const categorySet = new Set(filters?.category);
      if (categorySet.has(category)) {
        categorySet.delete(category);
      } else {
        categorySet.add(category);
      }
      const newCategories = [...categorySet];
      setFilters({...filters, category: newCategories});
    },
    [filters],
  );
  const selectManufacturer = useCallback(
    (manufacturer: unknown) => {
      const manufacturerSet = new Set(filters?.manufacturer);
      if (manufacturerSet.has(manufacturer)) {
        manufacturerSet.delete(manufacturer);
      } else {
        manufacturerSet.add(manufacturer);
      }
      const newManufacturer = [...manufacturerSet];
      setFilters({...filters, manufacturer: newManufacturer});
    },
    [filters],
  );

  const selectRating = useCallback(
    (rating: any) => {
      let newRating = null;
      if (filters.rating !== rating) {
        newRating = rating;
      }
      setFilters({...filters, rating: newRating});
    },
    [filters],
  );

  const onValueChanged = useCallback((lowValue: number, highValue: number) => {
    setLow(lowValue);
    setHigh(highValue);
    setFilters(prev => {
      return {...prev, price: {min: lowValue, max: highValue}};
    });
  }, []);

  const manufacturer = useMemo(() => {
    return (
      searchInfo?.filters?.filter(
        (item: {key: string}) => item?.key === 'manufacturer',
      )?.[0]?.value || []
    );
  }, [searchInfo?.filters]);

  const categories = useMemo(() => {
    return (
      searchInfo?.filters?.filter(item => item?.key === 'categories')?.[0]
        ?.value || []
    );
  }, [searchInfo?.filters]);

  const clearFilters = useCallback(() => {
    setFilters({});
    applyFilters({
      category: [],
      manufacturer: [],
      rating: null,
      price: {min: null, max: null},
    });
    setFilterCount(0);
    setModelVisible(false);
  }, [applyFilters, setFilterCount, setModelVisible]);

  const CustomRatingComponent = useCallback(() => {
    return (
      <View>
        <TouchableOpacity
          style={[styles.row, !isRatingOpen && {paddingVertical: Sizes.s}]}
          onPress={() => setIsRatingOpen(!isRatingOpen)}>
          <Label
            weight="600"
            color="textLight"
            size="l"
            text={t('filters.rate')}
          />
          <ImageIcon size="xx" icon={isRatingOpen ? 'minusIcon' : 'addIcon'} />
        </TouchableOpacity>
        <Spacer type="Vertical" size="xm" />

        {isRatingOpen &&
          [1, 2, 3, 4].map(rating => {
            return (
              <View style={styles.counter} key={'view' + rating}>
                <TouchableOpacity
                  onPress={() => selectRating(rating)}
                  style={styles.checkboxContainer}>
                  <CheckBox
                    selected={(5 - rating) * 20 === filters.rating}
                    onValueChange={() => selectRating((5 - rating) * 20)}
                    value={Number(filters.rating === (5 - rating) * 20)}
                    selected={(5 - rating) * 20 === filters.rating}
                  />
                  <TouchableOpacity
                    onPress={() => selectRating((5 - rating) * 20)}
                    style={styles.rating}>
                    {[4, 3, 2, 1, 0].map(value => (
                      <Label
                        key={`${value}`}
                        style={
                          rating <= value ? styles.fill_star : styles.empty_star
                        }
                        text={'★'}
                      />
                    ))}
                  </TouchableOpacity>
                  <Label
                    size="l"
                    style={styles.leftItem}
                    text={5 - rating + ' ' + t('filters.andAbove')}
                  />
                </TouchableOpacity>
              </View>
            );
          })}
      </View>
    );
  }, [isRatingOpen, filters]);

  const renderItem = useCallback(
    (item: string, type: string) => {
      return (
        <TouchableOpacity
          style={styles.checkBoxContainer}
          onPress={() => {
            type === 'Manufacturer'
              ? selectManufacturer(item)
              : selectCategories(item);
          }}>
          <CheckBox
            selected={
              type === 'Manufacturer'
                ? filters?.manufacturer?.includes(item)
                : filters?.category?.includes(item)
            }
            value={
              type === 'Manufacturer'
                ? filters?.manufacturer?.includes(item)
                : filters?.category?.includes(item)
            }
            onValueChange={() => {
              return Platform.OS === 'android'
                ? type === 'Manufacturer'
                  ? selectManufacturer(item)
                  : selectCategories(item)
                : null;
            }}
          />
          <Label
            color="textLight"
            size="m"
            style={styles.checkBoxText}
            text={item}
          />
        </TouchableOpacity>
      );
    },
    [filters],
  );

  return (
    <>
      <View style={styles.container}>
        <View style={[styles.content, styles.container]}>
          <View style={styles.header}>
            <Label
              color="textLight"
              weight="bold"
              text={t('filters.filters')}
            />
            <View style={styles.filterClear}>
              {filterCount > 0 && (
                <TouchableOpacity
                  style={styles.clearAllFilter}
                  onPress={clearFilters}>
                  <Label
                    color="textLight"
                    weight="bold"
                    size="m"
                    style={styles.itemCenter}
                    text={t('filters.clear')}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View style={styles.container}>
            <ScrollView
              showsVerticalScrollIndicator={false}
              style={styles.scrollStyle}>
              <View style={styles.AccordionContainer}>
                <Accordion
                  title="Manufacturer"
                  data={manufacturer?.sort()}
                  renderItem={renderItem}
                  isOpen={true}
                />
              </View>
              <Separator style={styles.saprater} />

              <View style={styles.AccordionContainer}>
                <Accordion
                  title="Categories"
                  data={categories?.sort()}
                  renderItem={renderItem}
                  isOpen={true}
                />
              </View>
              <Separator style={styles.saprater} />

              <View style={styles.AccordionContainer}>
                <TouchableOpacity
                  style={[styles.row, !isPriceRangeOpen && styles.range]}
                  onPress={() => setIsPriceRangeOpen(!isPriceRangeOpen)}>
                  <Label
                    weight="600"
                    color="textLight"
                    size="l"
                    text={t('filters.price')}
                  />
                  <ImageIcon
                    size="xx"
                    icon={isPriceRangeOpen ? 'minusIcon' : 'addIcon'}
                  />
                </TouchableOpacity>

                {isPriceRangeOpen && (
                  <>
                    <View style={styles.rangeSliderStyle}>
                      <RangeSlider
                        style={styles.rangeSliderStyle}
                        disableRange={false}
                        floatingLabel
                        low={filters?.price?.min || low}
                        high={filters?.price?.max || high}
                        renderThumb={renderThumb}
                        renderRail={renderRail}
                        renderRailSelected={renderRailSelected}
                        min={0}
                        max={500000}
                        step={1}
                        selectionColor="#1976d2"
                        blankColor="#abcbef"
                        onValueChanged={onValueChanged}
                        // onTouchEnd={onValueChangeEnd}
                      />
                    </View>
                    <View style={styles.sliderValues}>
                      <Label
                        color="textLight"
                        size="m"
                        weight="700"
                        text={'₹' + ' ' + low}
                      />
                      <Label
                        color="textLight"
                        size="m"
                        weight="700"
                        text={'₹' + ' ' + high}
                      />
                    </View>
                  </>
                )}
              </View>
              <Separator style={styles.saprater} />
              <View style={styles.AccordionContainer}>
                {CustomRatingComponent()}
              </View>
            </ScrollView>
          </View>
          <View style={styles.footer}>
            <Button
              radius="m"
              style={styles.cancelButton}
              onPress={() => {
                setModelVisible(false);
              }}
              text={t('buttons.cancelLower')}
            />
            <Button
              radius="m"
              style={styles.applyButton}
              onPress={() => {
                applyFilters(filters);
                setModelVisible(false);
                setFilterCount(filterCount);
              }}
              text={t('buttons.applyLower')}
            />
          </View>
        </View>
      </View>
    </>
  );
};

export default Filters;

import React, {useState} from 'react';
import {View, TouchableOpacity, Pressable, Share} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import Clipboard from '@react-native-community/clipboard';
import {t} from 'i18next';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {showSuccessMessage} from 'utils/show_messages';
import {useMemo} from 'react';
import {debugLog} from 'utils/debugLog';

type Props = {
  visible: boolean;
  onClose: () => void;
  refersRecord: RefersRecord;
  coin: number | undefined;
};

const ReferralModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [showGif, setShowGif] = useState(false);
  const {visible, onClose, refersRecord, coin} = props;
  const copyToClipboard = data => {
    Clipboard.setString(data);
    setShowGif(true);
    // showSuccessMessage('copy to clipboard');
  };
  const rupee = Math.floor(coin / 2);

  // const downloadImage = async (imageUrl: string) => {
  //   const {fs} = RNFetchBlob;
  //   const path = `${fs.dirs.CacheDir}/myimage.jpg`;

  //   const response = await RNFetchBlob.config({
  //     fileCache: true,
  //     path,
  //   }).fetch('GET', imageUrl);

  //   debugLog('Response: ' + response.path());

  //   return response.path(); // Returns local file path
  // };

  // const referralShare = async (url, socialName) => {
  //   try {
  //     if (socialName === 'INSTAGRAM') {
  //       debugLog(url, 'url***');
  //       const filePath = await downloadImage(url);
  //       debugLog(`file://${filePath}`);
  //       const shareOptions = {
  //         url: url,
  //         type: 'text',
  //         social: Share.Social.INSTAGRAM,
  //       };
  //       return await Share.shareSingle(shareOptions);
  //     }

  //     return await Share.shareSingle({
  //       url: url,
  //       social: Share?.Social[socialName],
  //     });
  //   } catch (error) {
  //     ToastAndroid.show(t('PDP.installed'), ToastAndroid.SHORT);
  //   }
  // };
  const referLink = refersRecord?.onelink_url
    ? refersRecord?.onelink_url?.replace('/checkout/login', '')
    : '';

  const onShare = () => {
    Share.share(
      {
        message: `${referLink}`,
      },
      {dialogTitle: 'Share on ..', tintColor: 'green'},
    ).catch(err => debugLog(err));
  };

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={() => onClose()}
      content={
        <View style={styles.referView}>
          <View style={styles.referLinkView}>
            <Label
              text={`${t('myReferral.referralLink')} : `}
              size="mx"
              weight="500"
              color="text"
            />
            <Label
              onPress={() => copyToClipboard(referLink)}
              style={styles.flex}
              text={referLink}
              size="mx"
              align="left"
              numberOfLines={1}
              fontFamily="Medium"
              color="orangeOffer"
            />
            <Spacer size="xm" type="Horizontal" />
            {!showGif ? (
              <Pressable
                onPress={() => copyToClipboard(referLink)}
                style={styles.referralLink}>
                <Spacer type="Horizontal" size="xm" />
                <ImageIcon
                  tintColor="silkBlue2"
                  icon="newCopyIcon"
                  size="xxl"
                />
              </Pressable>
            ) : (
              <FastImage
                source={Icons.copyGif}
                style={styles.copyGif}
                resizeMode="contain"
              />
            )}
          </View>
          {showGif && (
            <>
              <Spacer size="s" />
              <Label
                color="green2"
                fontFamily="SemiBold"
                size="mx"
                text={t('toastMassages.copyLinkText')}
              />
            </>
          )}
          {/* <Spacer size="l" />
          <View style={styles.codeView}>
            <TouchableOpacity
              onPress={() => copyToClipboard(refersRecord?.referral_code)}>
              <Label
                align="center"
                color="orangeOffer"
                weight="500"
                size="mx"
                textDecorationLine="underline"
                text={refersRecord?.referral_code}
              />
            </TouchableOpacity>
            <Spacer size="s" />
            <Label
              align="center"
              color="text"
              weight="500"
              size="mx"
              text={t('myReferral.referralCode1')}
            />
          </View> */}
          <Spacer size="m" />
          <View style={styles.dericationView}>
            <Label
              align="center"
              color="text"
              weight="600"
              size="l"
              text={t('myReferral.shareVia')}
            />
            <TouchableOpacity
              // onPress={() => referralShare(referLink, 'WHATSAPP')}>
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="ri_whatsappIcon" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(referLink, 'FACEBOOK')}>
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="uil_facebookIcon" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(referLink, 'INSTAGRAM')}>
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="ri_instagramFillIcon" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(referLink, 'TWITTER')}>
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="twitter" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(referLink, 'PINTEREST')}>
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="Pinterest" />
            </TouchableOpacity>
            <TouchableOpacity
              // onPress={() => referralShare(referLink, 'LINKEDIN')}>
              onPress={() => onShare()}>
              <ImageIcon size="x4l" icon="mdi_linkedinIcon" />
            </TouchableOpacity>
          </View>
          <Spacer size="l" />
        </View>
      }
    />
  );
};

export default React.memo(ReferralModal);

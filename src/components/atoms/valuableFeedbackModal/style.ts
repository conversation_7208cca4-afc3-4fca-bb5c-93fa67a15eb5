import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
    },
    centeredView: {
      flex: Sizes.x,
      justifyContent: 'center',
      backgroundColor: colors.modalShadow,
    },
    scrollViewStyle: {
      marginTop: 'auto',
    },
    overallView: {
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.l,
    },
    viewProduct: {
      borderTopRightRadius: Sizes.xms,
      borderTopLeftRadius: Sizes.xms,
      flex: Sizes.sx,
    },
    valuableView: {
      flex: Sizes.x,
      borderTopLeftRadius: Sizes.xms,
      borderTopRightRadius: Sizes.xms,
      paddingHorizontal: Sizes.xms,
    },
    memberShipCardImageView: {
      borderBottomWidth: Sizes.x,
      borderColor: colors.placeholderColor,
    },
    removeModalSubView: {
      justifyContent: 'center',
      flexDirection: 'row',
      borderRadius: Sizes.xms,
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
      paddingHorizontal: Sizes.xms,
      paddingVertical: Sizes.m,
    },
    memberShipView: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.placeholderColor,
      width: Sizes.exl,
      height: Sizes.exl,
    },
    removeImageView: {
      flex: Sizes.x,
      borderRadius: Sizes.xm,
    },
    removeView: {
      flex: Sizes.xs,
    },
    searchIconStyle: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    inputBorderStyle: {
      borderRadius: Sizes.m,
      borderWidth: Sizes.x,
      backgroundColor: colors.background,
      borderColor: colors.placeholderColor,
    },
    inputPostQuestionStyle: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      height: Sizes.ex176,
      borderColor: colors.text2,
    },
    input: {
      borderRadius: Sizes.xms,
      color: colors.text,
    },
    fRow: {
      flexDirection: 'row',
    },
    submitView: {
      alignSelf: 'flex-start',
    },
  });

export default styles;

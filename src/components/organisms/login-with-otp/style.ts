import {Sizes} from 'common';
import {StyleSheet, Dimensions} from 'react-native';
const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    container2: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    statusBarBackground: {
      height: 44, // Height of status bar on iOS
      backgroundColor: '#FF712B', // Same as Android status bar
    },
    body: {
      paddingHorizontal: Sizes.xx,
      backgroundColor:colors.background,
      height:'60%',
      bottom:78
    },
    containerGif: {
      height: '40%',
      position: 'relative',
      overflow: 'hidden',
      // backgroundColor: '#FF6A13'
    },
    bottomFade: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 140,
    },
    thankYouImage: {
      height: '115%',
      width: '100%',
      position: 'absolute',
      top: 0,
      // resizeMode: 'cover',
      alignSelf: 'center',
      backgroundColor: colors.sunnyOrange3,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    imageFullWidth: {
      width: '100%',
    },
    logoView: {
      alignSelf: 'center',
      padding: 0,
      margin: 0,
      width: Dimensions.get('window').width,
      height: 69,
    },
    anythingDetalView: {
      alignSelf: 'center',
    },
    containerForm: {
      height: '65%',
      bottom: 30,
    },
    coinImage: {
      height: 16,
      width: 16,
      marginLeft: 4,
      bottom: 2,
    },
    topShadow: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: 40,
      zIndex: 10,
    },
    errorContainer: {
      marginTop: Sizes.s,
      marginLeft: 0,
      flexDirection: 'row-reverse',
    },
    loginSubTitle: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    newOrCreateAccView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.sx,
      textAlign: 'center',
    },
    separatorLine: {
      flex: Sizes.x,
      height: Sizes.xs,
      backgroundColor: colors.grey2,
    },
    itemsCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    inputView: {
      borderWidth: Sizes.x,
      color: colors.grey2,
      borderColor: colors.grey2,
    },
    inputStyleView: {
      fontSize: Sizes.mx,
      color: colors.text,
      borderColor: colors.grey2,
    },
    error: {
      // alignSelf: 'flex-end'
    },
    searchView: {
      width: Sizes.xl,
      height: Sizes.xl,
    },
    otpSendView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
    },
    googleBtn: {
      color: colors.newPrimary,
    },
    pHor: {
      paddingHorizontal: Sizes.s,
    },
    termsTextContainer: {
      flexDirection: 'row',
    },
    helpView: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      // width: '20%'
    },
    signUpLogin: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 5,
      marginTop: Sizes.m,
    },
    eyeStyle: {
      height: Sizes.l,
      width: Sizes.l,
    },
    crossIconStyle: {
      height: Sizes.x3l,
      width: Sizes.x3l,
      position: 'absolute',
      top: 15,
      right: 20,
      zIndex: 2,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.xl,
    },
    earnMsgView: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginVertical: Sizes.s,
      alignItems: 'center',
    },
  });
export default styles;

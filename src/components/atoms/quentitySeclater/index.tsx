import React, {useEffect, useState, useCallback, useMemo} from 'react';
import {View, ViewProps, ActivityIndicator, Keyboard} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {TouchableOpacity} from 'react-native';
import ImageIcon from '../imageIcon';
import {TextInput} from 'react-native';
import {debounce} from 'utils/utils';
import Label from '../label';

type Props = {
  value: number | string;
  style?: ViewProps['style'];
  quantity?: number;
  min?: number;
  max?: number;
  tintColor?: keyof Theme['colors'];
  onUpdate: (newValue: number, onChange: boolean) => void;
  disabled?: boolean;
  backgroundColor?: keyof Theme['colors'];
  minusStyle?: ViewProps['style'];
  inputStyle?: ViewProps['style'];
  plusStyle?: ViewProps['style'];
  // Optional error/loading display props
  showLoadingState?: boolean;
  showErrorState?: boolean;
  cartItemScreen?: boolean;
  disabledLeft?: boolean;
  disabledRight?: boolean;
  hideCart?: boolean;
  hideCartBtn?: boolean;
  delayUpdate?: boolean;
  textInputRef?: React.RefObject<TextInput>;
  onFocus?: () => void;
};

const Quantity = ({
  value,
  tintColor,
  style,
  min = 0,
  max,
  onUpdate,
  disabled = false,
  backgroundColor,
  minusStyle,
  inputStyle,
  plusStyle,
  showLoadingState = false,
  showErrorState = false,
  cartItemScreen,
  disabledLeft = false,
  disabledRight = false,
  hideCart = false,
  hideCartBtn = false,
  delayUpdate = false,
  id,
  textInputRef,
  onFocus,
}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const [count, setCount] = useState(Number(value));
  const [displayCount, setDisplayCount] = useState(Number(value));
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);

  useEffect(() => {
    const newValue = Number(value);
    setCount(newValue);
    setDisplayCount(newValue);
  }, [value]);

  const handleUpdate = useCallback(
    (newCount: number, update = false, minus = false) => {
      if (
        newCount < min ||
        (max !== undefined &&
          newCount > (minus ? (newCount > max ? newCount : max) : max)) ||
        loading ||
        disabled
      ) {
        return;
      }
      setDisplayCount(newCount);
      setCount(newCount);
      onUpdate(newCount, update);
    },
    [min, max, loading, disabled, onUpdate],
  );

  const handleTextChange = useCallback(
    (text: string) => {
      const numericValue = Number(text.trim());
      if (
        !isNaN(numericValue) &&
        numericValue >= 0 &&
        (max === undefined || numericValue <= max)
      ) {
        setDisplayCount(numericValue);
        if (numericValue >= min && text.trim() !== '') {
          handleUpdate(numericValue, true);
        }
      }
    },
    [max, min, handleUpdate],
  );

  const handleTextBlur = useCallback(() => {
    let finalValue = displayCount;
    if (max !== undefined && displayCount > max) {
      finalValue = max;
    } else if (displayCount < min) {
      finalValue = min;
    }
    setDisplayCount(finalValue);
    if (finalValue !== count) {
      handleUpdate(finalValue, true);
    }
  }, [displayCount, max, min, count, handleUpdate]);

  const containerStyle = useMemo(
    () => [
      styles.container,
      {
        borderColor: backgroundColor
          ? colors[backgroundColor]
          : colors.categoryTitle,
      },
      style,
    ],
    [styles.container, backgroundColor, colors, style],
  );

  const decrementStyle = useMemo(
    () => [
      styles.decrementBtn,
      {
        backgroundColor: backgroundColor
          ? colors[backgroundColor]
          : colors.categoryTitle,
        // opacity: disabled || loading || displayCount <= min ? 0.5 : 1,
      },
      minusStyle,
      disabledLeft && styles.disableBG,
    ],
    [
      styles.decrementBtn,
      backgroundColor,
      colors,
      disabled,
      loading,
      displayCount,
      min,
      minusStyle,
      disabledLeft,
    ],
  );

  const incrementStyle = useMemo(
    () => [
      styles.incrementBtn,
      {
        backgroundColor: backgroundColor
          ? colors[backgroundColor]
          : colors.categoryTitle,
        // opacity:
        //   disabled || loading || (max !== undefined && displayCount >= max)
        //     ? 0.5
        //     : 1,
      },
      plusStyle,
      disabledRight && styles.disableBG,
    ],
    [
      styles.incrementBtn,
      backgroundColor,
      colors,
      disabled,
      loading,
      max,
      displayCount,
      plusStyle,
      disabledRight,
    ],
  );

  const decrement = useCallback(
    event => {
      event.stopPropagation();
      handleUpdate(displayCount - 1, false, true);
    },
    [displayCount, handleUpdate],
  );

  const increment = useCallback(
    event => {
      event.stopPropagation();
      handleUpdate(displayCount + 1);
    },
    [displayCount, handleUpdate],
  );

  return (
    <View style={containerStyle}>
      <TouchableOpacity
        activeOpacity={1}
        disabled={disabled || loading || displayCount < min}
        onPress={decrement}
        style={decrementStyle}>
        <ImageIcon icon="decrementIcon" size="xl" tintColor="background" />
      </TouchableOpacity>

      <TextInput
        id={id}
        ref={textInputRef}
        keyboardType="numeric"
        maxLength={3}
        value={String(displayCount)}
        style={[styles.inputBox, inputStyle]}
        onChangeText={handleTextChange}
        onEndEditing={handleTextBlur}
        allowFontScaling={false}
        editable={!loading && !disabled}
        returnKeyType="done"
        onSubmitEditing={Keyboard.dismiss}
        onFocus={onFocus}
      />

      {showLoadingState && loading && (
        <View style={styles.loadingIndicator}>
          <ActivityIndicator size="small" color={colors.primaryColor} />
        </View>
      )}

      {showErrorState && error && (
        <View style={styles.errorIndicator}>
          <Label text="!" size="xs" fontFamily="Bold" color="textError" />
        </View>
      )}

      <TouchableOpacity
        activeOpacity={1}
        disabled={
          disabled || loading || (max !== undefined && displayCount > max)
        }
        onPress={increment}
        style={incrementStyle}>
        <ImageIcon icon="plus" size="xl" tintColor="background" />
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(Quantity);

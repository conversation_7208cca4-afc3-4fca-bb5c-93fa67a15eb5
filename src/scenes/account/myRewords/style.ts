import {Sizes} from 'common';
import {DeviceWidth} from 'config/environment';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    buttonLabel: {
      fontSize: Sizes.l,
      color: colors.whiteColor,
    },
    rewardIconSmall: {
      width: Sizes.x3l,
      height: Sizes.x3l,
    },
    expireCoins: {
      flexDirection: 'row',
    },
    noTransactionContainer: {
      alignItems: 'center',
      width: '100%',
    },
    fRow: {
      flexDirection: 'row',
    },
    shopButton: {
      width: '45%',
      height: Sizes.x7l,
      backgroundColor: colors.sunnyOrange,
      alignSelf: 'center',
    },
    icons: {
      tintColor: colors.sunnyOrange,
    },
    loaderStyle: {
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: Sizes.xms,
    },
    flex: {
      flex: Sizes.x,
    },
    coinsLeftView: {
      padding: Sizes.sx,
      borderRadius: Sizes.sx,
      borderWidth: 0.6,
      flex: Sizes.x,
      borderColor: colors.whiteColor,
      backgroundColor: colors.white13,
      alignItems: 'center',
      justifyContent: 'center',
    },
    coinsLeftSubView: {
      padding: Sizes.sx,
      flexWrap: 'wrap',
      flex: Sizes.s - Sizes.x,
      borderRadius: Sizes.sx,
      borderWidth: 0.6,
      flexDirection: 'row',
      borderColor: colors.whiteColor,
      backgroundColor: colors.white13,
    },
    rowSpaceBetween: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    statusHeading: {
      flexDirection: 'row',
      backgroundColor: colors.categoryTitle,
      borderTopRightRadius: Sizes.mx,
      borderTopLeftRadius: Sizes.mx,
      padding: Sizes.xl,
      justifyContent: 'space-between',
    },
    earnedCoinList: {
      padding: Sizes.sx,
      paddingBottom: Sizes.m,
      backgroundColor: colors.offWhite,
      flex: Sizes.x,
    },
    historyListContainer: {
      width: '100%',
      backgroundColor: colors.whiteColor,
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.mx,
      borderRadius: Sizes.mx,
      shadowColor: colors.dimGray25,
      shadowOffset: {
        width: 0,
        height: 0,
      },
      shadowOpacity: Sizes.x,
      shadowRadius: Sizes.s,
      elevation: Sizes.s,
      marginBottom: Sizes.m,
      marginHorizontal: Sizes.xs,
    },
    expiredDateView: {
      flexDirection: 'column',
      alignItems: 'center',
      flex: Sizes.s - Sizes.x,
    },
    aboutToExpireView: {
      flexDirection: 'column',
      alignItems: 'center',
      flex: Sizes.x,
    },
    sapraterView: {
      height: Sizes.x,
      backgroundColor: colors.placeholderColor,
    },
    expiredView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    balanceMainView: {
      paddingHorizontal: Sizes.mx,
      flex: Sizes.x,
      paddingTop: Sizes.l,
    },
    balanceView: {
      justifyContent: 'space-between',
      paddingHorizontal: Sizes.xms,
      flexDirection: 'row',
    },
    balanceSubView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    accountsRewordView: {
      flex: Sizes.x,
      borderWidth: 0.6,
      borderColor: colors.whiteColor,
      borderRadius: Sizes.sx,
      backgroundColor: colors.white13,
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
      padding: Sizes.xm,
    },
  });

export default styles;

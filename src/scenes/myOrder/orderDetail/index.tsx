import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {<PERSON><PERSON>, Header} from 'components/molecules';
import {
  CartTotalPrize,
  ImageIcon,
  Label,
  Spacer,
  PickupModal,
  OrderImageModal,
  WebViewModal,
  OrderShipmentCard,
  PreviousOrderReturn,
  RetryPaymentModal,
  SuccessModal,
} from 'components/atoms';
import {
  View,
  TouchableOpacity,
  FlatList,
  LayoutAnimation,
  Platform,
} from 'react-native';
import stylesWithOutColor from './style';
import {RouteProp, useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {snakeToTitleCase} from 'utils/formatter';
import {
  NewGetOrderDetails,
  TrackShipmentApi,
  InvoiceLink,
  TrackReturnApi,
  PreviousOrderReturnList,
  RetryPayment,
} from 'services/orders';
import {getOrderStatusIcon, orderStatusColor, sAllDevice} from 'utils/utils';
import {OrderLoader} from 'skeletonLoader';
import {AnalyticsEvents} from 'components/organisms';
import {useSelector, useDispatch} from 'react-redux';
import {formatDate, orderStatusLabel} from 'utils/utils';
import ErrorHandler from 'utils/ErrorHandler';
import {fetchPayment} from 'services/checkout';
import RazorpayCheckout from 'react-native-razorpay';
import {showErrorMessage} from 'utils/show_messages';
import {debugLog} from 'utils/debugLog';
import {ImagePath} from 'config/apiEndpoint';
import {appsFlyerEvent} from 'components/organisms/analytics-Events/appsFlyerEvent';
import {setLoading} from 'app-redux-store/slice/appSlice';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  itemCountSummery?: boolean;
  ItemScoped?: boolean;
  route: RouteProp<RootStackParamsList, 'MyOrder'>;
};

const OrderDetail = ({navigation, route, ItemScoped = true}: Props) => {
  const TAG = 'OrderDetailScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {item, type, order_id} = route.params;
  const [orderDetail, setOrderDetail] = useState<OrderDetailsV1 | null>(null);
  const [previousReturnHistory, setPreviousReturnHistory] =
    useState<OrderDetailsV1 | null>(null);
  const [invoice, setInvoice] = useState<InvoiceLink>();
  const [isloading, setIsLoading] = useState<boolean>(true);
  const [allShipmentPackage, setAllShipmentPackage] = useState<Root | null>();
  const [returnTracking, setReturnTracking] = useState();
  const [statusHistory, setStatusHistory] = useState();
  const [isPickupModal, setIsPickupModal] = useState(false);
  const [itemData, setItemData] = useState<OrderItems[]>([]);
  const [awbNumber, setAwbNumber] = useState('');
  const [modalVisibleImage, setModalVisibleImage] = useState(false);
  const [activeIndex, setActiveIndex] = useState(null);
  const [webViewModel, setWebViewModel] = useState(false);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState([]);
  const [retryModalVisible, setRetryModalVisible] = useState<boolean>(false);
  // const [circularProgress, setCircularProgress] = useState(null);
  const [orderSuccessModel, setOrderSuccessModel] = useState(false);
  const [previousHistoryModel, setPreviousHistoryModel] = useState(false);
  const [trackingLink, setTrackingLink] = useState('');
  const [returnId, setReturnId] = useState();
  const {userInfo, isLoggedIn} = useSelector((state: RootState) => state.app);
  const dispatch = useDispatch();
  const orderId = useMemo(() => {
    return type === 'order' ? item?.data?.order_id : item?.order_id || order_id;
  }, [item, order_id, type]);

  // ------Order Total ---------------------
  const itemCount = useMemo(() => {
    const order = orderDetail?.packages;
    return order?.reduce((prev, curr) => prev + curr.items.length, 0);
  }, [orderDetail]);

  const priceDetail = useMemo(() => {
    const arr = [];
    const orderSummary = orderDetail?.OrderSummary;
    orderSummary?.map(o => {
      arr.push({
        key: o.label,
        value: o.value,
        currency: orderDetail?.currency,
        toFixed: true,
        code: o.code,
      });
    });
    return arr;
  }, [orderDetail, itemCount, colors]);

  const calculateTotalPrice = data => {
    const items = Array.isArray(data) ? data : [data];
    return items.reduce(
      (total, item) =>
        total +
        parseFloat(
          item?.is_free_product ? 0 : item?.price * item?.qty_ordered || 0,
        ),
      0,
    );
  };

  const totalPrice = calculateTotalPrice(itemData);

  const previousReturn = useCallback(
    async (productSku: string, returnID: number) => {
      const {data, status} = await PreviousOrderReturnList(
        productSku,
        returnID,
      );

      if (status && data?.item_previous_returns?.length > 0) {
        setPreviousReturnHistory(
          data?.item_previous_returns?.length > 0
            ? data?.item_previous_returns
            : [],
        );
      }
    },
    [],
  );

  // -----------Shipping Address----------
  const shippingAdd = useMemo(() => {
    return orderDetail?.shipping_address
      ? orderDetail?.shipping_address
      : undefined;
  }, [orderDetail]);

  const billingAdd = useMemo(() => {
    return orderDetail?.billing_address
      ? orderDetail?.billing_address
      : undefined;
  }, [orderDetail]);

  const getOrderDetailsData = useCallback(async () => {
    setIsLoading(true);
    const {data} = await NewGetOrderDetails(orderId);
    setIsLoading(false);
    if (data) {
      setOrderDetail(data);
    }
  }, [orderId]);

  // ======= inVoice ======
  const getInvoiceLink = useCallback(async () => {
    setIsLoading(true);
    const {data} = await InvoiceLink({
      awb_number: awbNumber,
      order_id: orderId,
    });
    setIsLoading(false);
    if (data) {
      setInvoice(data);
    }
  }, [awbNumber, orderId]);

  // =======================order tracking shipment====================
  const getTrackShipmentList = useCallback(async () => {
    setIsLoading(true);
    const {data} = await TrackShipmentApi(orderId);
    setIsLoading(false);
    if (data) {
      setAllShipmentPackage(data);
    }
  }, []);

  // =======================order return tracking ====================
  const getReturnTrackList = useCallback(
    async (productSku: string, returnId: number) => {
      setIsLoading(true);
      const {data} = await TrackReturnApi(productSku, returnId);
      setIsLoading(false);
      if (data) {
        setReturnTracking(data?.items);
      }
    },
    [],
  );
  const navigateToOrderSuccess = async (payload, cod = false) => {
    setOrderSuccessModel(cod ? false : true);
    setTimeout(
      () => {
        setOrderSuccessModel(false);
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'ThankYouPage',
              params: {order_id: payload},
            },
          ],
        });
      },
      cod ? 0 : 5000,
    ); 
  };

  const confirmPayment = useCallback(async payload => {
    try {
      const {rzp_signature, ...cleanedPayload} = payload;
      const {data} = await fetchPayment(cleanedPayload);
      dispatch(setLoading(false));
      if (data && data?.order_id) {
        if (data && data?.status === 'success') {
          setOrderSuccessModel(true);
          const paymentData = {
            ...data,
          };
          try {
            const addressParts = [
              paymentData?.addresses?.street?.join(', '),
              paymentData?.addresses?.city,
              paymentData?.addresses?.region?.label,
              paymentData?.addresses?.postcode,
              paymentData?.addresses?.country?.name,
            ].filter(Boolean);
            const paymentSuccessData = {
              'Order Id': paymentData?.order_id ?? 'Unknown Order ID',
              'Payment Mode': 'razorpay',
              Currency: 'INR',
              Coupon: paymentData?.coupon_code?.code ?? 'No Coupon',
              'Total Amount': paymentData?.amount / 100 ?? 0, // Convert from paisa to INR
              Brand: paymentData?.items?.map(
                item => item?.product?.manufacturer ?? 'Unknown Brand',
              ),
              Category: paymentData?.items?.map(item => 'Dental'), // No category provided, assuming Dental
              SKU: paymentData?.items?.map(
                item => item?.product?.sku ?? 'Unknown SKU',
              ),
              'Parent SKU': paymentData?.items?.map(
                item => item?.product?.sku ?? 'Unknown Parent SKU',
              ),
              'Product Name': paymentData?.items?.map(
                item => item?.product?.name ?? 'Unknown Product',
              ),
              'Total Price':
                paymentData?.pricing_details?.item_total_selling_price?.amount
                  ?.value ?? 0,
              'Product Details': paymentData?.items ?? [], // Full item details
              'Total Quantity': paymentData?.total_quantity ?? 1,
              'Total MRP':
                paymentData?.pricing_details?.item_total_regular_price?.amount
                  ?.value ?? 0,
              'Order Date': new Date().toISOString(), // Assuming the current date as Order Date
              'No. Of Products': paymentData?.items?.length ?? 1,
              'Reward Earned': paymentData?.rewards?.total_coins ?? 0,
              'Coupon Value':
                paymentData?.pricing_details?.discounts?.find(
                  d => d?.code === 'coupon',
                )?.amount?.value ?? 0,
              // Address: paymentData.data?.region?.region ?? 'Unknown Address',
              Address:
                addressParts.length > 0
                  ? addressParts.join(', ')
                  : 'Unknown Address',
              'Reward Used': paymentData?.rewards?.total_coins_used ?? 0, // Assuming a key for reward usage, update if needed
              'Product Price': paymentData?.items?.map(
                item => item?.product?.price?.minimalPrice?.amount?.value ?? 0,
              ),
              'Total Amount Saved':
                paymentData?.pricing_details?.total_savings?.amount?.value ?? 0,
              'Product Id': paymentData?.items?.map(
                item => item?.product?.id ?? 'Unknown Product ID',
              ),
              'Discount Amount':
                paymentData?.pricing_details?.discounts?.reduce(
                  (sum, d) => sum + (d?.amount?.value ?? 0),
                  0,
                ),
              'Shipping Charge':
                paymentData?.pricing_details?.shipping_charges?.amount?.value ??
                0,
              'Tax Amount': paymentData?.pricing_details?.applied_taxes?.reduce(
                (sum, tax) => sum + Number(tax?.amount?.value ?? 0),
                0,
              ),
            };

            // appsFlyer Online Retry Payment OrderData
            const appsFlyerOrderData = {
              estimatedRevenue: paymentSuccessData['Total Amount'],
              totalAmount: paymentSuccessData['Total Amount'],
              sku: paymentSuccessData['SKU'],
              productIds: paymentSuccessData['Product Id'],
              productNames: paymentSuccessData['Product Name'],
              currency: paymentSuccessData['Currency'],
              productQuantities: paymentSuccessData['No. Of Products'],
              orderId: paymentSuccessData['Order Id'],
              receiptId: paymentSuccessData['Order Id'],
            };
            appsFlyerEvent('CheckoutCompleted', appsFlyerOrderData);

            AnalyticsEvents(
              'CHECKOUT_COMPLETED',
              'Checkout Completed',
              paymentSuccessData,
              userInfo,
              isLoggedIn,
            );
            setTimeout(() => {
              setOrderSuccessModel(false);
              navigation.reset({
                index: 0,
                routes: [
                  {
                    name: 'ThankYouPage',
                    params: {order_id: data?.order_id},
                  },
                ],
              });
            }, 5000);
            // navigateToOrderSuccess(data?.order_id);
          } catch (error) {
            debugLog('Confirm Payment Error', error);
          } finally {
            setRetryModalVisible(false);
          }
        }
      }
      getOrderData();
    } catch (err) {
      dispatch(setLoading(false));
      getOrderData();
    }
  }, []);

  const initiateRazorPay = useCallback(
    (payload, rzpData) => {
      const options = {
        ...rzpData,
        description: '',
        image: ImagePath.dentalkart,
        name: 'Dentalkart',
        prefill: {
          email: userInfo.email,
          contact: userInfo?.mobile ? userInfo.mobile : '',
          name: `${userInfo.firstname} ${userInfo.lastname}`,
        },
        theme: {color: ''},
      };

      RazorpayCheckout.open(options)
        .then(response => {
          // handle success
          dispatch(setLoading(true));
          const rzpres = {
            rzp_payment_id: response.razorpay_payment_id,
            rzp_order_id: response.razorpay_order_id,
            rzp_signature: response.razorpay_signature,
          };
          const RzpPayload = {...payload, ...rzpres};

          setTimeout(() => {
            confirmPayment(RzpPayload);
          }, 5000);
        })
        .catch(error => {
          dispatch(setLoading(true));
          setRetryModalVisible(false);
          setTimeout(() => {
            confirmPayment(payload);
          }, 5000);

          showErrorMessage(
            `${
              error.error.description && error.error.description !== 'undefined'
                ? error.error.description
                : t('payment.payFail')
            }. ${t('payment.tryAgain')}`,
          );
        });
    },
    [userInfo?.email, userInfo?.firstname, userInfo?.lastname],
  );

  const onSelectedPaymentMethod = useCallback(
    async (PayData: string, paymentMethod: string) => {
      setRetryModalVisible(false);
      if (paymentMethod === 'cashondelivery') {
        dispatch(setLoading(true));
        const {data, status}: any = await RetryPayment({
          order_id: PayData?.order_id,
          payment_method_code: paymentMethod,
        });
        dispatch(setLoading(false));
        if (data && status) {
          // const matchedOrder = orderList?.find(order => order?.data?.order_id === orderId) || null;
          const finalData = {...data, order_data: {}, tracking_data: {}};
          const paymentSuccessData = {
            'Order Id': finalData?.order_id ?? 'Unknown Order ID',
            'Payment Mode': 'cashondelivery', // No direct mapping in provided JSON
            Currency: 'INR', // Hardcoded since it's missing from order data
            Coupon:
              finalData?.order_data?.order_summary?.coupon_code?.code ??
              'No Coupon', // Assuming coupon code field
            'Total Amount':
              finalData?.order_data?.order_summary?.order_amount ?? 0,
            Brand: finalData?.order_data?.items?.map(item => 'Unknown Brand'), // Brand missing in JSON
            Category: finalData?.order_data?.items?.map(() => 'Dental'), // Assuming dental category
            SKU: finalData?.order_data?.items?.map(
              item => item?.sku ?? 'Unknown SKU',
            ),
            'Parent SKU': finalData?.order_data?.items?.map(
              item => item?.sku ?? 'Unknown Parent SKU',
            ),
            'Product Name': finalData?.order_data?.items?.map(
              item => item?.name ?? 'Unknown Product',
            ),
            'Total Price': finalData?.order_data?.items?.reduce(
              (total, item) => total + (item?.price ?? 0),
              0,
            ),
            'Product Details': finalData?.order_data?.items ?? [], // Assuming full item details
            'Total Quantity':
              finalData?.order_data?.order_summary?.total_product_qty ?? 1,
            'Total MRP': finalData?.order_data?.order_summary?.total_mrp ?? 0, // Assuming MRP field
            'Order Date':
              finalData?.order_data?.dates?.ordered?.date ??
              new Date().toISOString(),
            'No. Of Products': finalData?.order_data?.items?.length ?? 1,
            'Reward Earned': finalData?.order_data?.items?.reduce(
              (total, item) => total + (item?.reward_earned_coins ?? 0),
              0,
            ),
            'Coupon Value':
              finalData?.order_data?.order_summary?.coupon_value ?? 0, // Assuming coupon discount
            Address:
              finalData?.order_data?.customer_address ?? 'No Address Provided', // Assuming address field
            'Reward Used':
              finalData?.order_data?.order_summary?.reward_used ?? 0, // Assuming reward used
            'Product Price': finalData?.order_data?.items?.map(
              item => item?.price ?? 0,
            ),
            'Total Amount Saved':
              finalData?.order_data?.order_summary?.total_savings ?? 0,
            'Product Id': finalData?.order_data?.items?.map(
              item => item?.product_id ?? 'Unknown Product ID',
            ),
            'Discount Amount':
              finalData?.order_data?.order_summary?.discount_amount ?? 0, // Assuming discount field
            'Shipping Charge':
              finalData?.order_data?.order_summary?.shipping_charge ?? 0, // Assuming shipping field
            'Tax Amount': finalData?.order_data?.order_summary?.tax_amount ?? 0, // Assuming tax field
          };

          // appsFlyer Online Retry Payment OrderData
          const appsFlyerOrderData = {
            estimatedRevenue: paymentSuccessData['Total Amount'],
            totalAmount: paymentSuccessData['Total Amount'],
            sku: paymentSuccessData['SKU'],
            productIds: paymentSuccessData['Product Id'],
            productNames: paymentSuccessData['Product Name'],
            currency: paymentSuccessData['Currency'],
            productQuantities: paymentSuccessData['No. Of Products'],
            orderId: paymentSuccessData['Order Id'],
            receiptId: paymentSuccessData['Order Id'],
          };
          appsFlyerEvent('CheckoutCompleted', appsFlyerOrderData);

          AnalyticsEvents(
            'CHECKOUT_COMPLETED',
            'Checkout Completed',
            paymentSuccessData,
            userInfo,
            isLoggedIn,
          );
          setRetryModalVisible(false);
          navigateToOrderSuccess(data?.order_id, true);
        }
      } else {
        const payload = {order_id: PayData?.order_id};
        const rzpData = {
          amount: PayData?.amount,
          order_id: PayData?.reference_number,
          key: PayData?.merchant_id,
          currency: PayData?.currency,
        };
        setTimeout(
          () => {
            initiateRazorPay(payload, rzpData);
          },
          Platform.OS === 'ios' ? 100 : 0,
        );
      }
    },
    [
      initiateRazorPay,
      isLoggedIn,
      navigateToOrderSuccess,
      orderId,
      // orderList,
      userInfo,
    ],
  );

  // ==============Available Payment Method =================
  const AvailablePaymentMethod = useCallback(async (order_Id: string) => {
    const {data, status}: any = await fetchPayment({order_id: order_Id});
    if (data && status) {
      setAvailablePaymentMethods(data);
      setRetryModalVisible(true);
      AnalyticsEvents(
        'RETRY_PAYMENT',
        'Retry Payment',
        data,
        userInfo,
        isLoggedIn,
      );
    }
  }, []);

  const hideRetryModal = useCallback(() => {
    setRetryModalVisible(false);
  }, []);

  useEffect(() => {
    getOrderData();
  }, []);

  const getOrderData = () => {
    getOrderDetailsData();
    getTrackShipmentList();
  };

  useEffect(() => {
    if (type === 'Returns') {
      setReturnId(item?.return_id);
      getReturnTrackList(item.sku, item.return_id);
      if (item?.sku && orderId) {
        previousReturn(item.sku, orderId);
      }
    }
  }, [type]);

  useEffect(() => {
    if (awbNumber) {
      getInvoiceLink();
    }
  }, [awbNumber]);

  const trackingData = useMemo(() => {
    const currentIndex = statusHistory?.findIndex(e => e.current === true);
    return statusHistory?.map((val, index) => {
      const isCurrent = index === currentIndex;
      const isBeforeCurrent = index < currentIndex;
      return {
        label: type === 'Returns' ? val.label : val.status,
        status: val.status,
        lineActive: isBeforeCurrent,
        showLine: statusHistory?.length !== index + 1,
        icon:
          type === 'Returns'
            ? getOrderStatusIcon(val.status, isBeforeCurrent, isCurrent)
            : isBeforeCurrent
            ? 'checkGreen'
            : isCurrent
            ? 'checkGreen'
            : 'circleGreen',
        isBeforeCurrent,
        isCurrent,
      };
    });
  }, [statusHistory, type]);

  const getEstimateStatus = item => {
    if (!item) return {};
    const {expected_delivery_time, status: statusValue} = item;
    if (!expected_delivery_time) {
      return {};
    }
    const statusMap = {
      Cancelled: t('orderTrack.cancelOn'),
      'Order Closed': t('orderTrack.closeOn'),
      Delivered: t('orderListing.deliveredOn'),
    };
    const status = statusMap[statusValue] || t('orderTrack.estOn');
    const expectedDate = formatDate(
      expected_delivery_time,
      'dddd, DD MMM YYYY',
    );
    if (expected_delivery_time && statusValue === 'Delivered') return {};
    return {status, expectedDate};
  };

  // =============================tracking End============================
  const renderItem = ({item, index}) => {
    const statusObj = orderStatusColor(item?.status, colors);
    const estimate = getEstimateStatus(item);
    const orderStatusList = item?.status_history?.filter(
      data => data?.status === item?.status,
    );
    const orderClose = ['Cancelled', 'Returned', 'Order Closed'].includes(
      item?.status,
    );
    return (
      <>
        <TouchableOpacity
          key={index}
          style={styles.renderCards}
          onPress={() => {
            setActiveIndex(activeIndex === index ? null : index);
            setStatusHistory(item?.status_history || []);
            setItemData(item?.items);
            setAwbNumber(
              type === 'Returns' ? item?.tracking_id : item?.tracking_number,
            );
            LayoutAnimation.configureNext(
              LayoutAnimation.Presets.easeInEaseOut,
            );
            if (activeIndex !== index) {
              const combinedData = {
                priceDetail, // Array of price details
                item, // Single item or array of items
                orderDetail, // Order summary details
              };

              AnalyticsEvents(
                'TRACK_ORDER',
                'Track Order',
                combinedData,
                userInfo,
                isLoggedIn,
              );
            }
          }}>
          <View style={styles.subRenderCards}>
            {type === 'Returns' ? (
              <View>
                <Label
                  text={snakeToTitleCase(
                    item?.status,
                    'Approved',
                    'Request Approved',
                  )}
                  size="m"
                  color="text2"
                  fontFamily="Medium"
                  textTransform="capitalize"
                />
                <Label
                  text={formatDate(
                    item.last_updated_date || orderDetail?.order_date,
                    'YYYY-MM-DD, hh:mm',
                  )}
                  size="m"
                  color="text2"
                  fontFamily="Medium"
                  textTransform="capitalize"
                />
              </View>
            ) : (
              <View style={styles.shipmentView}>
                <Label
                  text={`${t('orderTrack.shipment')} - ${index + 1}`}
                  size="m"
                  color="text2"
                  fontFamily="Medium"
                  textTransform="capitalize"
                />
              </View>
            )}
            <Spacer type="Horizontal" size="sx" />
            {/* {type === 'Returns' ? (
              <View style={styles.flexOne} />
            ) : (
              <View style={styles.statusView}>
                {item?.delivery_info?.label && (
                  <View style={styles.flexOne}>
                    <Spacer type="Horizontal" size="s" />
                    <Label
                      text={item?.delivery_info?.label}
                      size="m"
                      color="green2"
                      fontFamily="Medium"
                    />
                    <Spacer type="Horizontal" size="s" />
                  </View>
                )}
              </View>
            )} */}
            <View style={styles.items}>
              <Label
                text={`${t('orderTrack.noOfItems')} - ${
                  type === 'Returns'
                    ? `${t('PDP.qty')}-${
                        item?.qty_ordered ? item?.qty_ordered : item?.qty
                      }`
                    : item?.items?.length
                }`}
                size="m"
                color="text2"
                fontFamily="Medium"
              />
            </View>
            <Spacer type="Horizontal" size="sx" />
            <Label
              text={
                type === 'Returns'
                  ? snakeToTitleCase(item?.status)
                  : statusObj?.status || item?.status
              }
              fontFamily="Medium"
              textTransform="capitalize"
              size="m"
              color={
                type === 'Returns'
                  ? item?.status === 'repair'
                    ? 'silkBlue2'
                    : 'internationalOrange'
                  : statusObj?.color
              }
            />
            <Spacer type="Horizontal" size="s" />
            <ImageIcon
              icon={activeIndex === index ? 'arrowUp' : 'arrowRight'}
              size="xl"
            />
          </View>
        </TouchableOpacity>
        {activeIndex === index && (
          <OrderShipmentCard
            inx={index}
            item={item}
            type={type}
            estimate={estimate}
            orderClose={orderClose}
            orderDetail={orderDetail}
            orderStatusList={orderStatusList}
            trackingData={trackingData}
            itemData={itemData}
            orderId={orderId}
            invoice={invoice}
            ItemScoped={ItemScoped}
            openImage={() => setModalVisibleImage(!modalVisibleImage)}
            onTrackClick={(trackingUrl: string) => {
              setTrackingLink(trackingUrl);
              setWebViewModel(!webViewModel);
            }}
            pickUpModal={() => setIsPickupModal(!isPickupModal)}
            showPreviousReturn={
              previousReturnHistory?.length > 1 ? true : false
            }
            onTrackReturn={() => setPreviousHistoryModel(!previousHistoryModel)}
          />
        )}
      </>
    );
  };

  const renderAddress = (address, billing) => {
    const street = address?.street?.trim().endsWith(',')
      ? address?.street + ' '
      : address?.street + ', ';
    let street2 = '';
    if (address?.customer_street_2) {
      street2 = street + address?.customer_street_2 + ', ' || '';
    } else if (address?.map_address) {
      const parts = address?.map_address?.split(',');
      const beforePin = parts
        .slice(0, parts.length - 2)
        .join(',')
        .trim();
      street2 = street + beforePin + ', ' || '';
    } else {
      street2 = street;
    }
    return (
      <View>
        <Label
          fontFamily="Medium"
          color="text"
          text={t(
            billing ? 'addressCard.billingAddress' : 'myOrder.shippingAddress',
          )}
          size="l"
          textTransform="capitalize"
        />
        <Spacer size="xs" />
        <Label
          size="mx"
          textTransform="capitalize"
          fontFamily="Medium"
          color="text2"
          text={address?.name}
        />
        <Spacer size="xs" />
        <Label
          size="mx"
          fontFamily="Medium"
          color="text2"
          textTransform="capitalize"
          text={street2 + address?.city + ', ' + address?.region}
        />
        <Spacer size="xs" />
        <Label
          size="mx"
          fontFamily="Medium"
          color="text2"
          text={`${t('orderTrack.mobileNo')} - ${address?.telephone}`}
        />
        {address?.vat_id && (
          <>
            <Spacer size="xs" />
            <Label
              size="mx"
              fontFamily="Medium"
              color="text2"
              text={`${t('orderTrack.gstIn')} - ${address?.vat_id}`}
            />
          </>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          labelStyle={styles.textColor}
          tagStyle={styles.tagStyle}
          backButton={true}
          navigation={navigation}
          text={t('orderTrack.trackOrder')}
          searchIcon={true}
          bagIcon={true}
        />
      </ErrorHandler>
      {!orderDetail?.OrderSummary?.length > 0 ? (
        <OrderLoader />
      ) : (
        <>
          <FlatList
            data={['']}
            style={styles.mainView}
            showsVerticalScrollIndicator={false}
            renderItem={() => (
              <View>
                {type !== 'Returns' &&
                  allShipmentPackage?.shipment_header_text && (
                    <View style={styles.noteView}>
                      <Label
                        fontFamily="Medium"
                        color="lightSlateBlue"
                        text={allShipmentPackage?.shipment_header_text}
                        size="m"
                        textTransform="capitalize"
                      />
                    </View>
                  )}
                <Spacer size="m" />
                {allShipmentPackage?.packages[0]?.status ===
                  'Payment Pending' && (
                  <View style={styles.detailMainView}>
                    <View style={styles.detailBox}>
                      <View>
                        <Label
                          fontFamily="Medium"
                          color="red3"
                          text={t('orderTrack.tryAgainText')}
                          size="l"
                        />
                        <Label
                          fontFamily="Medium"
                          color="text"
                          text={t('orderTrack.retryNowText')}
                          size="l"
                        />
                      </View>
                        <Button
                          onPress={() => {
                            AvailablePaymentMethod(orderDetail?.order_id);
                          }}
                          text={t('retryPaymentButton.retryPayment')}
                          paddingHorizontal="m"
                          labelColor="whiteColor"
                          weight="500"
                          labelSize={sAllDevice ? 'm' : 'mx'}
                          radius="xms"
                          withGradient
                          size="extra-small"
                          gradientColors={[colors.coral, colors.persimmon]}
                        />
                    </View>
                  </View>
                )}
                <Spacer size="m" />
                <View style={styles.detailMainView}>
                  <View style={styles.detailBox}>
                    <Label
                      fontFamily="Medium"
                      color="text"
                      text={t('orderTrack.orderDetails')}
                      size="l"
                      textTransform="capitalize"
                    />
                    <ImageIcon
                      style={
                        orderDetail?.payment_method_code === 'cashondelivery'
                          ? styles.codTag
                          : styles.onlineTag
                      }
                      icon={
                        orderDetail?.payment_method_code === 'cashondelivery'
                          ? 'codTag'
                          : 'prepaid'
                      }
                      resizeMode="stretch"
                    />
                  </View>
                  <View style={styles.lineStyle} />
                  <Label
                    fontFamily="Medium"
                    color="text2"
                    text={`${t('orderListing.orderID')} - ${
                      orderDetail?.order_id
                    }`}
                    textTransform="capitalize"
                    size="mx"
                  />
                  <Spacer size="xs" />
                  <Label
                    fontFamily="Medium"
                    color="text2"
                    text={`${t(
                      type === 'Returns'
                        ? 'orderListing.orderedOn'
                        : 'orderTrack.orderedPlacedOn',
                    )} - ${formatDate(
                      orderDetail?.order_date,
                      'YYYY-MM-DD, hh:mm',
                    )}`}
                    size="mx"
                    textTransform="capitalize"
                  />
                </View>
                <View style={styles.processingContinuer}>
                  <ErrorHandler
                    componentName={`${TAG} OrderDetailsList`}
                    onErrorComponent={<View />}>
                    <FlatList
                      style={styles.flexOne}
                      keyExtractor={(item, index) => index.toString()}
                      data={
                        type === 'Returns'
                          ? returnTracking
                          : allShipmentPackage?.packages
                      }
                      renderItem={renderItem}
                      ItemSeparatorComponent={<Spacer size="m" />}
                    />
                  </ErrorHandler>
                </View>

                <Spacer size="m" />
                <View style={styles.detailMainView}>
                  {shippingAdd && renderAddress(shippingAdd, false)}
                  <Spacer size="xm" />
                  {billingAdd && renderAddress(billingAdd, true)}
                  <View style={styles.lineStyle} />
                  <ErrorHandler
                    componentName={`${TAG} CartTotalPrize`}
                    onErrorComponent={<View />}>
                    <CartTotalPrize
                      data={priceDetail}
                      rewards={orderDetail?.rewards}
                      type={type}
                    />
                  </ErrorHandler>
                </View>
              </View>
            )}
          />
          {/* *****************************Modal********************************* */}
          <Spacer size="m" />

          {modalVisibleImage && (
            <ErrorHandler
              componentName={`${TAG} OrderImageModal`}
              onErrorComponent={<View />}>
              <OrderImageModal
                visible={modalVisibleImage}
                onClose={() => setModalVisibleImage(false)}
                data={itemData}
                item={item}
                totalPrice={totalPrice}
                navigation={navigation}
                totalEarnedCoin={orderDetail?.rewards?.rewardpoints_earned}
                spentPoint={orderDetail?.rewards?.rewardpoints_used}
                type="orderDetails"
              />
            </ErrorHandler>
          )}

          {/* *****************************web View Modal**************************** */}
          {webViewModel && (
            <ErrorHandler
              componentName={`${TAG} WebViewModal`}
              onErrorComponent={<View />}>
              <WebViewModal
                visible={webViewModel}
                url={trackingLink}
                onClose={() => setWebViewModel(!webViewModel)}
              />
            </ErrorHandler>
          )}

          {/* =========================Retry Payment Modal====================== */}
          {retryModalVisible && (
            <ErrorHandler
              componentName={`${TAG} RetryPaymentModal`}
              onErrorComponent={<View />}>
              <RetryPaymentModal
                data={availablePaymentMethods}
                visible={retryModalVisible}
                onClose={hideRetryModal}
                onSelectMethod={onSelectedPaymentMethod}
              />
            </ErrorHandler>
          )}

          {/* =========================Order Success Modal====================== */}
          {orderSuccessModel && (
            <ErrorHandler
              componentName={`${TAG} SuccessModal`}
              onErrorComponent={<View />}>
              <SuccessModal
                visible={orderSuccessModel}
                type="orderSuccess"
                title={t('checkOut.paymentDone')}
                titleStyle={styles.successTitle}
                onClose={() => setOrderSuccessModel(!orderSuccessModel)}
              />
            </ErrorHandler>
          )}

          {/* *****************************PickUp Modal**************************** */}
          {isPickupModal && (
            <ErrorHandler
              componentName={`${TAG} PickupModal`}
              onErrorComponent={<View />}>
              <PickupModal
                visible={isPickupModal}
                onClose={() => setIsPickupModal(!isPickupModal)}
                returnId={item.return_id}
              />
            </ErrorHandler>
          )}

          {previousHistoryModel && (
            <ErrorHandler
              componentName={`${TAG} DeliveryInfoModal`}
              onErrorComponent={<View />}>
              <PreviousOrderReturn
                visible={previousHistoryModel}
                onClose={() => setPreviousHistoryModel(false)}
                data={previousReturnHistory.filter(
                  data => data.return_id !== returnId,
                )}
                onSelectReturn={(sku: string, rId: number) => {
                  setReturnId(rId);
                  getReturnTrackList(sku, rId);
                }}
              />
            </ErrorHandler>
          )}
        </>
      )}
    </SafeAreaView>
  );
};

export default React.memo(OrderDetail);

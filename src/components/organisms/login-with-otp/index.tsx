import React, { useCallback, useEffect, useState, useRef } from 'react';
import { 
  View, 
  TouchableOpacity, 
  Keyboard, 
  Platform, 
  Linking, 
  NativeModules, 
  Animated,
} from 'react-native';
// import { SafeAreaView } from 'react-native-safe-area-context';
import {KeyboardAvoidingView, ScrollView} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Formik} from 'formik';
import {RootStackParamsList} from '../../../routes';
import {Button, ConnectWithUs, PhoneInputText} from 'components/molecules';
import stylesWithOutColor from './style';
import SmsRetriever from 'react-native-sms-retriever';
import LinearGradient from 'react-native-linear-gradient';

import {
  Label,
  Spacer,
  Link,
  Separator,
  LoginGoogle,
  ImageIcon,
  WebViewModal,
} from 'components/atoms';
import {useTranslation} from 'react-i18next';
import {useDispatch, useSelector} from 'react-redux';
import {
  generateNewCart,
  getUserInfo,
  setIsLoggedIn,
  setLoading,
} from 'app-redux-store/slice/appSlice';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {socialLogin, userLogin, verifyOTP} from 'services/auth';
import tokenClass from 'utils/token';
import AnalyticsEvents from '../analytics-Events';
import {
  loginValidationSchema,
  loginPasswordValidationSchema,
  getValidationSchema,
  getUsePsswordValidationSchema,
} from 'utils/validationError';
import ErrorHandler from 'utils/ErrorHandler';
import {useMemo} from 'react';
import {debugError, debugLog} from 'utils/debugLog';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import MobileNumberModal from './mobileNumberModal/MobileNumberModal';
import TextInputBox from 'components/molecules/textInputBox';
import {RootState} from '@types/local';
import {Sizes} from 'common';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {appsFlyerEvent} from '../analytics-Events/appsFlyerEvent';
import appsFlyer from 'react-native-appsflyer';
import {privacyPolicy, termsAndConditions} from 'config/environment';
const {HintPicker} = NativeModules;

type LoginWithOtpProps = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  setMobileNumber: (mobileNumber: string) => void;
  setOtpLogin: (screen: string) => void;
  setPageType: (screen: string) => void;
  navigateToLastScreen: () => void;
  showPass: boolean;
  setShowPass?: (screen: boolean) => void;
  phoneFocus: boolean;
  loginType?: string;
  actionType?: string;
};
const LoginWithOtp = ({
  setOtpLogin,
  setMobileNumber,
  setShouldShow,
  navigation,
  setPageType,
  mobileNumber,
  setActionType,
  navigateToLastScreen,
  showPass,
  setShowPass,
  phoneFocus = false,
  loginType = 'phone',
  actionType,
}: LoginWithOtpProps) => {
  const TAG = 'LoginWithOtp';
  const {t} = useTranslation();
  const [apiError, setApiError] = useState(null);
  const [isPasswordSecure, setIsPasswordSecure] = useState(true);
  const [mobileNumberModal, setMobileNumberModal] = useState(true);
  const [showPassInput, setShowPassInput] = useState(showPass || false);
  const [showEmail, setShowEmail] = useState(loginType !== 'phone');
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const phoneRef = useRef(null);
  const passwordRef = useRef(null);
  const [activeField, setActiveField] = useState<
    'email' | 'phoneNumber' | null
  >(loginType !== 'phone' ? 'email' : 'phoneNumber');
  const insets = useSafeAreaInsets();
  const [termsModal, setTermsModal] = useState(false);
  const [policyModal, setPolicyModal] = useState(false);
  
  // Animation values for keyboard handling
  const animatedValue = useRef(new Animated.Value(0)).current;

  const handleTermsModalClose = useCallback(() => {
    setTermsModal(false);
  }, []);
  const handlePolicyModalClose = useCallback(() => {
    setPolicyModal(false);
  }, []);

  const handleInputFocus = useCallback(() => {
    const shiftValue = -Sizes.ex;
    Animated.timing(animatedValue, {
      toValue: shiftValue,
      duration: 250,
      useNativeDriver: true,
    }).start();
  }, [animatedValue]);
  
  const handleInputBlur = useCallback(() => {
    Animated.timing(animatedValue, {
      toValue: Platform.OS ==='ios'?0:-Sizes.x78,
      duration: 250,
      useNativeDriver: true,
    }).start();
  }, [animatedValue]);
  
  const openWhatsApp = async (link: string) => {
    try {
      const url = 'whatsapp://send?text=Hello';

      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(link);
      } else {
        const storeUrl =
          Platform.OS === 'ios'
            ? 'https://apps.apple.com/app/whatsapp-messenger/id310633997'
            : 'https://play.google.com/store/apps/details?id=com.whatsapp';

        await Linking.openURL(storeUrl);
      }
    } catch (error) {
      debugLog('Error checking WhatsApp:', error);
    }
  };
  useEffect(() => {
    if (showPass && !phoneFocus) {
      passwordRef?.current?.focus();
    } else {
      phoneRef?.current?.focus();
    }
  }, [showPass, phoneFocus]);

  const isValidEmail = (email: any) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  useFocusEffect(
    useCallback(() => {
      setShowPass && setShowPass(false);
    }, []),
  );
  const isValidPhoneNumber = (phone: any) => {
    const phoneRegex = /^\d{10}$/; // Adjust based on your phone format (e.g., for India: 10 digits)
    const val = phoneRegex.test(phone);
    return phoneRegex.test(phone);
  };

  const handleForgotPass = (values, setTouched, formikErrors) => {
    // Set touched state for appropriate field to trigger validation
    if (showEmail || values.email) {
      setTouched({email: true});
    } else {
      setTouched({phoneNumber: true});
    }

    if (
      (values.phoneNumber || values.email) &&
      (isValidPhoneNumber(values.phoneNumber) || isValidEmail(values.email))
    ) {
      loginSubmit({
        ...values,
        password: '',
        action: 'forgot_password',
      });
    }
  };
  const loginSubmit = useCallback(
    async (values: any) => {
      Keyboard.dismiss();
      if (values.password === '') {
        const phone = values?.phoneNumber
          ? values?.phoneNumber?.trim()
          : values?.email?.trim();
        dispatch(setLoading(true));
        const {data, status} = await userLogin({
          recipient: phone,
          // action: values.action,
          // action:phone.includes('@') ?"email_update":"m_login_signup",
          action:
            values.action === 'forgot_password'
              ? 'forgot_password'
              : values.phoneNumber.includes('@')
              ? 'email_update'
              : phone.includes('@')
              ? 'login'
              : 'm_login_signup',
          authentication_type: phone.includes('@') ? 'email' : 'mobile',
        });
        dispatch(setLoading(false));
        if (data) {
          if (data && status) {
            setShouldShow(true);
            setApiError(null);
            setMobileNumber(phone);
            setPageType('loginWotp');
            setOtpLogin('OtpScene');
            // setActionType(values.action);
            setActionType(
              values.action === 'forgot_password'
                ? 'forgot_password'
                : values.phoneNumber.includes('@')
                ? 'email_update'
                : values.action === 'login' && values.email.includes('@')
                ? 'login'
                : 'm_login_signup',
            );
            showSuccessMessage(data?.message);
          } else {
            dispatch(setLoading(false));
            setApiError(data?.message);
          }
        }
      } else {
        dispatch(setLoading(true));
        const phone = values?.phoneNumber
          ? values?.phoneNumber?.trim()
          : values?.email?.trim();
        const {data, status} = await verifyOTP({
          recipient: phone,
          action: values?.action,
          verification_type: 'password',
          authentication_type: phone.includes('@') ? 'email' : 'mobile',
          credential: values.password.trim(),
          new_password: '',
        });
        if (data?.token && status) {
          tokenClass.setToken(data.token);
          setApiError(null);
          dispatch(setIsLoggedIn(true));
          await dispatch(generateNewCart());
          dispatch(setLoading(false));
          dispatch(getUserInfo());
          AnalyticsEvents(
            'USER_LOGGED_IN',
            'Login-Email/Phone',
            data,
            {},
            false,
          );
          appsFlyer.setCustomerUserId(data?.customer?.id);
          appsFlyerEvent('Login');
          navigateToLastScreen();
          showSuccessMessage(data?.message);
        } else {
          dispatch(setLoading(false));
          setApiError(data?.message);
          showErrorMessage(data?.message);
        }
      }
    },
    [setMobileNumber, setOtpLogin, dispatch],
  );
  const {whatsAppLink} = useSelector((state: RootState) => state.app);

  // ================google login====================
  const getUserData = useCallback(
    async (googleResponse: any) => {
      try {
        dispatch(setLoading(true));
        const {data} = await socialLogin({
          entity_type: 'google',
          token: googleResponse.idToken,
        });
        if (data?.token) {
          setApiError(null);
          await Promise.all([
            tokenClass.setToken(data.token),
            dispatch(setIsLoggedIn(true)),
            dispatch(getUserInfo()),
            dispatch(generateNewCart()),
          ]);
          AnalyticsEvents(
            'SIGN_UP_LOGIN_GOOGLE',
            'Sign Up/Login Google',
            data,
            {},
            false,
          );
          appsFlyer.setCustomerUserId(data?.customer?.id);
          appsFlyerEvent('Login');
          navigateToLastScreen();
          showSuccessMessage('Login Success');
        } else {
          setApiError('Invalid token');
        }
      } catch (error) {
        debugError('Login Error:', error);
        setApiError('Login failed. Please try again.');
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch, navigation, navigateToLastScreen],
  );

  return (
    <TouchableOpacity 
  style={styles.container} 
  activeOpacity={1} 
  onPress={() => Keyboard.dismiss()}
>
      {/* First View - Static GIF container */}
      <View style={[
  styles.containerGif,
  Platform.OS === 'android' && {height: '49%',}
]}>
        <FastImage
          source={Icons.authGif}
          style={[styles.thankYouImage,
            Platform.OS === 'android'  && {height: '100%',}
          ]}
          resizeMode='cover'
        />
        <TouchableOpacity
          style={styles.crossIconStyle}
          onPress={() => navigation.goBack()}>
          <ImageIcon icon="whiteCross" size="xxl" />
        </TouchableOpacity>
      </View>

      {/* Second View - Animated container with form */}
      <Animated.View 
        style={[
          styles.container2,
          {
            transform: [{ translateY: animatedValue }],
          }
        ]}
      >
          {/* <TouchableOpacity activeOpacity={1} onPress={(e) => e.stopPropagation()} style={{flex:1}}> */}

            <Formik
              initialValues={{
                phoneNumber: '' || !showEmail ? mobileNumber : '',
                password: '',
                email: '' || showEmail ? mobileNumber : '',
              }}
              validationSchema={
                showPassInput && actionType != 'forgot_password'
                  ? getUsePsswordValidationSchema(activeField)
                  : getValidationSchema(activeField)
              }
              onSubmit={values => {
                loginSubmit({...values, action: 'login'});
              }}>
              {({
                handleSubmit,
                handleChange,
                values,
                errors,
                isValid,
                touched,
                setFieldValue,
                setTouched,
              }) => {
                useEffect(() => {
                  if (Platform.OS === 'android' && !showEmail) {
                    const showHintPicker = async () => {
                      try {
                        const phoneNumber = await HintPicker.showHintPicker();
                        if (phoneNumber) {
                          // Remove non-digit characters
                          const digitsOnly = phoneNumber.replace(/\D/g, '');
                          let localNumber = digitsOnly;
                          if (digitsOnly.length > 10) {
                            localNumber = digitsOnly.slice(
                              digitsOnly.length - 10,
                            );
                          }
                          setFieldValue('phoneNumber', localNumber);
                        }
                      } catch (error) {
                        debugError('Error showing phone picker:', error);
                      }
                    };
                    showHintPicker();
                  }
                }, []);
                useEffect(() => {
                  if (Platform.OS === 'ios') {
                    const keyboardDidShowListener = Keyboard.addListener(
                      'keyboardWillShow',
                      (e) => {
                        const shiftAmount = -e.endCoordinates.height + Sizes.ex226;
                        Animated.timing(animatedValue, {
                          toValue: shiftAmount,
                          duration: e.duration,
                          useNativeDriver: true,
                        }).start();
                      }
                    );
                  
                    const keyboardDidHideListener = Keyboard.addListener(
                      'keyboardWillHide',
                      (e) => {
                        Animated.timing(animatedValue, {
                          toValue: 0,
                          duration: e.duration,
                          useNativeDriver: true,
                        }).start();
                      }
                    );
                  
                    return () => {
                      keyboardDidShowListener.remove();
                      keyboardDidHideListener.remove();
                    };
                  } else {
                    const shiftValue = -Sizes.xxl;
                    if((errors.email && errors.password) || (errors.phoneNumber && errors.password)){
                      Animated.timing(animatedValue, {
                      toValue: shiftValue,
                      duration: 250,
                      useNativeDriver: true,
                    }).start();}
                    else
                    {Animated.timing(animatedValue, {
                      toValue: 0,
                      duration: 250,
                      useNativeDriver: true,
                    }).start();}
                  }
                },[values])
                return (
                <View style={styles.container}>
                  <View>
                    <LinearGradient
                        colors={['rgba(255,255,255,0)', 'rgba(255,255,255,1)', 'rgba(255,255,255,1)']}
                        style={styles.bottomFade}
                      />
                  </View>
                    <View style={styles.body}>
                      <View style={styles.headerContent}>
                        <View style={styles.imageFullWidth}>
                          <ImageIcon
                            style={styles.logoView}
                            icon="dentalkartUpdatedIcon"
                          />

                          <View style={styles.signUpLogin}>
                            <Label
                              text={t('login.logIn')}
                              color="skyBlue23"
                              fontFamily="SemiBold"
                              size="mx"
                              weight="600"
                            />
                            <Label
                              text={t('signUp.Or')}
                              size="mx"
                              color="skyBlue23"
                              fontFamily="Medium"
                              weight="600"
                            />
                            <Label
                              text={t('signUp.signUp')}
                              color="skyBlue23"
                              fontFamily="SemiBold"
                              size="mx"
                              weight="600"
                            />
                          </View>
                          <View style={styles.earnMsgView}>
                            <Label
                              text={t('login.earnMsg1')}
                              color="skyBlue23"
                              size="m"
                              weight="500"
                            />
                            <Label
                              text={t('login.earnMsg2')}
                              color="skyBlue23"
                              fontFamily="SemiBold"
                              size="m"
                              weight="600"
                            />
                            <FastImage
                              source={Icons.loginCoin}
                              style={styles.coinImage}
                            />
                            <Label
                              text={t('login.earnMsg3')}
                              size="m"
                              color="skyBlue23"
                              fontFamily="SemiBold"
                              weight="600"
                            />
                          </View>
                        </View>
                      </View>
                      <Spacer type="Vertical" size="xms" />
                      <ErrorHandler
                        componentName={`${TAG} PhoneInputText`}
                        onErrorComponent={<View />}>
                        {showEmail || values?.email ?
                          (
                            <TextInputBox
                              label={t('login.emailPlace')}
                              value={values.email}
                              onChangeText={handleChange('email')}
                              errorText={errors.email}
                              // fieldMandatory
                              description=""
                              error={
                                touched.email && errors.email
                                  ? String(errors.email)
                                  : undefined
                              }
                              errorStyle={styles.error}
                              leftIcon={Icons.email}
                              onFocus={handleInputFocus}
                              onBlur={handleInputBlur}
                            />
                          ) :
                          <TextInputBox
                            label={t('login.mobileNumberPlace')}
                            value={values.phoneNumber}
                            countryCode="+91"
                            iconI="phoneIcon"
                            // fieldMandatory
                            description=""
                            errorText={errors.phoneNumber}
                            onChangeText={text => {
                              const digitsOnly = text.replace(/\D/g, '');
                              let localNumber = digitsOnly;
                              if (digitsOnly.length > 10) {
                                localNumber = digitsOnly.slice(
                                  digitsOnly.length - 10,
                                );
                              }
                              setFieldValue('phoneNumber', localNumber);
                            }}
                            onEndEditing={() => {}}
                            error={
                              touched.phoneNumber && errors.phoneNumber
                                ? String(errors.phoneNumber)
                                : undefined
                            }
                            errorStyle={styles.error}
                            keyboardType={
                              Platform.OS === 'ios' ? 'number-pad' : 'numeric'
                            }
                            autoFocus={true}
                            maxLength={10}
                            onFocus={handleInputFocus}
                            onBlur={handleInputBlur}
                          />
                        
                      }
                      </ErrorHandler>

                      <Spacer type="Vertical" size="s" />
                      {showPassInput ? (
                        <>
                          <ErrorHandler
                            componentName={`${TAG} PhoneInputText`}
                            onErrorComponent={<View />}>
                            <TextInputBox
                              testID="txtLoginOTPPassword"
                              ref={passwordRef}
                              label={t('login.password')}
                              inputStyle={styles.inputStyleView}
                              placeholderColor={colors.text}
                              searchIconStyle={styles.searchView}
                              autoComplete="off"
                              tintColorIconRight="text2"
                              onRightIconPress={() =>
                                isPasswordSecure
                                  ? setIsPasswordSecure(false)
                                  : setIsPasswordSecure(true)
                              }
                              isClearIconSize={true}
                              rightIcon={
                                isPasswordSecure
                                  ? Icons.eyeHideIcon
                                  : Icons.eyeShowIcon
                              }
                              secureTextEntry={isPasswordSecure}
                              rightIconStyle={styles.eyeStyle}
                              onChangeText={handleChange('password')}
                              value={values.password}
                              errorText={errors.password}
                              error={
                                touched.password && errors.password
                                  ? String(errors.password)
                                  : undefined
                              }
                              errorStyle={styles.error}
                              autoFocus={true}
                              onFocus={handleInputFocus}
                              onBlur={handleInputBlur}
                            />
                          </ErrorHandler>
                        </>
                      ) : (
                        <View style={styles.loginSubTitle}>
                          <TouchableOpacity
                            onPress={() => {
                              setShowPassInput(true);
                              setShowPass(true);
                            }}>
                            <Label
                              text={t('login.usePassword')}
                              fontFamily="Medium"
                              size="m"
                              color="categoryTitle"
                            />
                          </TouchableOpacity>
                          {showEmail ? (
                            <TouchableOpacity
                              onPress={() => {
                                setShowEmail(false);
                                setActiveField('phoneNumber');
                                setFieldValue('email', '');
                              }}>
                              <Label
                                text={t('login.useMobileNumber')}
                                fontFamily="Medium"
                                size="m"
                                color="categoryTitle"
                              />
                            </TouchableOpacity>
                          ) : (
                            <TouchableOpacity
                              onPress={() => {
                                setShowEmail(true);
                                setActiveField('email');
                                setFieldValue('phoneNumber', '');
                              }}>
                              <Label
                                text={t('login.useEmail')}
                                fontFamily="Medium"
                                size="m"
                                color="categoryTitle"
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )}
                      <Spacer size="s" />
                      {showPassInput ? (
                        <View style={styles.otpSendView}>
                          <TouchableOpacity
                            onPress={() => {
                              setFieldValue('password', '');
                              setShowPassInput(false);
                            }}>
                            <Label
                              text={t('login.useOTP')}
                              fontFamily="Medium"
                              size="m"
                              color="categoryTitle"
                            />
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setActionType('forgot_password');
                              setTimeout(() => {
                                handleForgotPass(values, setTouched, errors);
                                setActionType('');
                              }, 200);
                            }}>
                            <Label
                              text={t('login.forgotPassword')}
                              fontFamily="Medium"
                              size="m"
                              color="categoryTitle"
                            />
                          </TouchableOpacity>
                        </View>
                      ) : null}
                      <Spacer type="Vertical" size="xm" />
                      <ErrorHandler
                        componentName={`${TAG} Button`}
                        onErrorComponent={<View />}>
                        <Button
                          radius="m"
                          type={'secondary'}
                          text={
                            showPassInput
                              ? t('login.continue')
                              : t('login.generateOTP')
                          }
                          onPress={handleSubmit}
                          selfAlign="stretch"
                          labelColor="whiteColor"
                          labelSize="mx"
                          size="large"
                        />
                      </ErrorHandler>
                      <Spacer type="Vertical" size="sx" />
                      {Platform.OS !== 'ios' && (
                        <View style={styles.itemsCenter}>
                          <Separator style={styles.separatorLine} />
                          <Label
                            text={t('login.Or')}
                            size="mx"
                            color="text2"
                            style={styles.labelText}
                          />
                          <Separator style={styles.separatorLine} />
                        </View>
                      )}
                      <Spacer type="Vertical" size="sx" />
                      <>
                        {Platform.OS !== 'ios' && (
                          <ErrorHandler
                            componentName={`${TAG} LoginGoogle`}
                            onErrorComponent={<View />}>
                            <LoginGoogle
                              textStyle={styles.googleBtn}
                              getUserData={getUserData}
                            />
                          </ErrorHandler>
                        )}
                        <Spacer type="Vertical" size="sx" />
                        <ConnectWithUs />
                        <Spacer type="Vertical" size="sx" />

                        <View style={styles.termsTextContainer}>
                          <Label
                            text={t('profileCompletion.login')}
                            size="m"
                            color="text"
                            weight="400"
                          />
                          <Link
                            onPress={() => setTermsModal(true)}
                            isUnderlined
                            color="skyBlue23"
                            size="m"
                            text={t('profileCompletion.terms')}
                            weight="500"
                          />
                          <Label
                            text={t('profileCompletion.&')}
                            size="m"
                            color="text"
                            style={styles.pHor}
                            weight="400"
                          />
                          <Link
                            onPress={() => setPolicyModal(true)}
                            isUnderlined
                            color="skyBlue23"
                            size="m"
                            text={t('profileCompletion.policy')}
                            weight="500"
                          />
                        </View>
                      </>
                    </View>
                  </View>
                );
              }}
            </Formik>
      </Animated.View>
        {termsModal && (
            <ErrorHandler
              componentName={`${TAG} WebViewModal`}
              onErrorComponent={<View />}>
              <WebViewModal
                visible={termsModal}
                url={termsAndConditions}
                onClose={handleTermsModalClose}
              />
            </ErrorHandler>
          )}
          {policyModal && (
            <ErrorHandler
              componentName={`${TAG} WebViewModal`}
              onErrorComponent={<View />}>
              <WebViewModal
                visible={policyModal}
                url={privacyPolicy}
                onClose={handlePolicyModalClose}
              />
            </ErrorHandler>
          )}
      </TouchableOpacity>
  );
};
export default LoginWithOtp;

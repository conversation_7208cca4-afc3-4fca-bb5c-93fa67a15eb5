import React, {useEffect, useCallback, useState, memo, useMemo, useRef} from 'react';
import {Dimensions, Linking, View, Animated} from 'react-native';
import {
  useSharedValue,
  withSpring,
  useAnimatedStyle,
} from 'react-native-reanimated';
import {
  CarouselCardItem,
  ReanimatedCarouselPagination,
  Spacer,
} from 'components/atoms';
import Carousel from 'react-native-reanimated-carousel';
import styles from './style';
import {navigate} from 'utils/navigationRef';
import {AnalyticsEvents} from 'components/organisms';
import {checkDevice} from 'utils/utils';
import {Sizes} from 'common';
import {useSelector} from 'react-redux';
import getImageUrl from 'utils/imageUrlHelper';
import FastImage from 'react-native-fast-image';
import { RootState } from '@types/local';

const {width: PAGE_WIDTH} = Dimensions.get('window');

// Move outside component to prevent recreation
const HEIGHT_MULTIPLIERS = {
  small: 0.45,
  medium: 0.52,
  large: 1.0,
  extraLarge: 1.71,
  doubleExtraLarge: 2
};

const calculateHeight = height => {
  return PAGE_WIDTH * (HEIGHT_MULTIPLIERS[height] || HEIGHT_MULTIPLIERS.medium);
};

// Component to handle pre-loading of images
const useImagePreloader = (data) => {
  useEffect(() => {
    if (!data || !data.length) return;
    
    const imagesToPreload = data.slice(0, 2).map(item => {
      const imageUrl = getImageUrl(
        item?.mobile_image ||
        item?.mobile_img ||
        item?.file ||
        item?.thumbnail_url ||
        item?.media?.mobile_image ||
        'https://via.placeholder.com/500'
      );
      return { uri: imageUrl, priority: FastImage.priority.high };
    });
    
    FastImage.preload(imagesToPreload);
    
    // Preload rest of images with normal priority
    setTimeout(() => {
      const remainingImages = data.slice(2).map(item => {
        const imageUrl = getImageUrl(
          item?.mobile_image ||
          item?.mobile_img ||
          item?.file ||
          item?.thumbnail_url ||
          item?.media?.mobile_image ||
          'https://via.placeholder.com/500'
        );
        return { uri: imageUrl, priority: FastImage.priority.normal };
      });
      
      FastImage.preload(remainingImages);
    }, 500);
  }, [data]);
};

const ReanimatedCarousel = ({
  autoplayInterval = 3000,
  data = [],
  autoPlay = false,
  height = 'medium',
  pagingEnabled = true,
  snapEnabled = true,
  loop = false,
  pagination = false,
  fullScreen = false,
  urlResolverKey,
  showDotsOnImage,
  onChangeIndex,
  paginationSpacer = false,
  onCardPress,
  style,
  customColor,
  carouselCardItemStyle,
  carouselStyle,
  renderItem,
  mode,
  carouselHeight,
  ref,
  paginationType,
  bannerImgStyle,
  screenName = 'Unknown',
  sectionType = 'Main Banner',
  paginationSize = false,
  description = 'Main Slider',
  ...props
}) => {
  const progressValue = useSharedValue(0);
  const scrollX = useMemo(() => new Animated.Value(0), []);
  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef(null);

  // Memoize selectors to prevent unnecessary re-renders
  const isLoggedIn = useSelector((state: RootState) => state.app.isLoggedIn);
  const userInfo = useSelector((state: RootState) => state.app.userInfo);

  // Calculate height only once
  const adjustedHeight = useMemo(() => calculateHeight(height), [height]);

  // Preload images strategically
  useImagePreloader(data);

  // Optimize base options
  const baseOptions = useMemo(
    () => ({
      vertical: false,
      width: PAGE_WIDTH,
      height: adjustedHeight,
    }),
    [adjustedHeight],
  );

  // Optimize styles
  const containerStyle = useMemo(() => [styles.container, style], [style]);
  const computedCarouselStyle = useMemo(
    () => [
      {
        width: PAGE_WIDTH,
        height: adjustedHeight,
      },
      carouselStyle,
    ],
    [adjustedHeight, carouselStyle],
  );
  const carouselContainerStyle = useMemo(
    () =>
      carouselHeight
        ? [styles.carouselContainer, {top: carouselHeight - Sizes.mx}]
        : null,
    [carouselHeight],
  );

  // Optimize mode config
  const modeConfig = useMemo(
    () =>
      mode === 'parallax'
        ? {
            parallaxScrollingScale: 0.9,
            parallaxScrollingOffset: checkDevice() ? 55 : 30,
            parallaxAdjacentItemScale: 0.9,
          }
        : null,
    [mode],
  );

  // Optimize navigation callback
  const navigateRoute = useCallback(
    item => {
      const updatedItem = {...item, PageName: screenName, Section: sectionType, Description: description};
      
      // Optimize analytics with early return for common case
      if (onCardPress) {
        AnalyticsEvents(
          'BANNER_CLICKED',
          'Banner Clicked',
          updatedItem,
          userInfo,
          isLoggedIn,
        );
        return onCardPress(item);
      }
      
      // Track analytics for specific cases
      switch (item?.title) {
        case 'News':
        case 'Blogs':
        case 'Shorts':
        case 'MAGAZINE':
          AnalyticsEvents(
            item.title.toUpperCase(),
            `${item.title} tab viewed`,
            { source: 'Saved Content Screen' },
            userInfo,
            isLoggedIn
          );
          break;
      }
      
      AnalyticsEvents(
        'BANNER_CLICKED',
        'Banner Clicked',
        updatedItem,
        userInfo,
        isLoggedIn,
      );

      // Handle navigation based on item properties
      if (item?.landing_page_entity?.category_id) {
        return navigate('CategoryDetail', {
          categoryId: item?.landing_page_entity.category_id,
        });
      } else if (item?.landing_page_entity?.product_id) {
        return navigate('ProductDetail', {
          productId: item?.landing_page_entity.product_id,
        });
      } else if (urlResolverKey) {
        return navigate('UrlResolver', {
          urlKey: item?.landing_page_entity?.[urlResolverKey],
        });
      } else if (item.relative) {
        if ((item?.link && item.link.includes('sale')) || 
            (item?.app_url && item.app_url.includes('sale'))) {
          return navigate('AllBrands', {
            saleUrl: item.link || item.app_url,
          });
        } else if (item.link && item.link.includes('membership')) {
          return navigate('MyMembership');
        } else if (item?.link && item?.link !== '#') {
          return navigate('UrlResolver', {urlKey: item.link});
        } else {
          navigate('UrlResolver', {urlKey: item.link});
          return false;
        }
      } else if (
        item?.landing_page_entity?.link ||
        item?.landing_page_entity?.url
      ) {
        let link =
          item?.landing_page_entity?.link ?? item?.landing_page_entity?.url;
        return Linking.openURL(link ?? '#');
      }
    },
    [onCardPress, urlResolverKey, userInfo, isLoggedIn, screenName, sectionType],
  );

  // Optimize snap callback
  const onSnapToItem = useCallback(
    index => {
      setCurrentIndex(index);
      progressValue.value = withSpring(index, {damping: 10, stiffness: 90});
      onChangeIndex?.(index);
    },
    [onChangeIndex, progressValue],
  );

  // Optimize progress change handler
  const onProgressChange = useCallback(
    (_, absoluteProgress) => {
      const clampedProgress = Math.min(
        Math.max(absoluteProgress, 0),
        data.length - 1,
      );

      progressValue.value = absoluteProgress;
      setCurrentIndex(Math.round(clampedProgress));
      scrollX.setValue(clampedProgress * PAGE_WIDTH);
    },
    [progressValue, scrollX, data.length],
  );

  // Optimize card style
  const cardItemStyle = useMemo(
    () => [{height: adjustedHeight}, carouselCardItemStyle],
    [adjustedHeight, carouselCardItemStyle],
  );

  // Optimize card prop
  const cardProp = useMemo(() => !fullScreen, [fullScreen]);

  // Use a ref to cache item press handlers
  const itemPressHandlersRef = useRef(new Map());
  
  // Get or create item press handler
  const getItemPressHandler = useCallback(
    item => {
      const itemId = item?.id || item?.landing_page_entity?.id || JSON.stringify(item);
      
      if (!itemPressHandlersRef.current.has(itemId)) {
        itemPressHandlersRef.current.set(itemId, () => navigateRoute(item));
      }
      
      return itemPressHandlersRef.current.get(itemId);
    },
    [navigateRoute],
  );

  // Optimize render item function
  const memoizedRenderItem = useCallback(
    ({item, index}) => {
      return (
        <CarouselCardItem
          style={cardItemStyle}
          bannerImgStyle={bannerImgStyle}
          index={index}
          card={cardProp}
          item={item}
          onPress={getItemPressHandler(item)}
          mode={mode}
        />
      );
    },
    [cardItemStyle, bannerImgStyle, cardProp, mode, getItemPressHandler],
  );

  // Optimize pan gesture handler props
  const panGestureHandlerProps = useMemo(() => ({
    activeOffsetX: [-10, 10],
  }), []);

  return (
    <View style={containerStyle}>
      <Carousel
        {...baseOptions}
        autoPlayInterval={autoplayInterval}
        enableMomentum={false}
        decelerationRate={0.3}
        scrollAnimationDuration={1000}
        pagingEnabled
        ref={ref || carouselRef}
        windowSize={3} // Reduced from 5
        panGestureHandlerProps={panGestureHandlerProps}
        style={computedCarouselStyle}
        loop={loop}
        mode={mode}
        modeConfig={modeConfig}
        autoPlay={autoPlay}
        snapEnabled={snapEnabled}
        onSnapToItem={onSnapToItem}
        onProgressChange={onProgressChange}
        data={data}
        renderItem={renderItem || memoizedRenderItem}
        {...props}
        removeClippedSubviews={true}
        maxToRenderPerBatch={3} // Reduced from 5
        updateCellsBatchingPeriod={100} // Increased from 50
        initialNumToRender={1} // Add this to render only what's needed initially
      />
      {pagination ? (
        <View style={carouselContainerStyle}>
          {paginationSpacer && <Spacer size="xm" />}
          <View style={showDotsOnImage ? styles.paginationContainer : {}}>
            <ReanimatedCarouselPagination
              activeIndex={currentIndex}
              dotColor={customColor}
              dotsLength={data.length}
              paginationType={paginationType}
              progress={progressValue.value}
              paginationSize={paginationSize}
            />
          </View>
        </View>
      ) : null}
    </View>
  );
};

export default memo(ReanimatedCarousel);
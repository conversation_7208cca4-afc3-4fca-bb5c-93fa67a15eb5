import React, {useCallback, useEffect, useRef, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from '../../routes';
import {Header, PhoneInputText} from 'components/molecules';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {TouchableOpacity, View, SectionList} from 'react-native';
import {getCountries} from 'api';
import {useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {useMemo} from 'react';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
};

const ChooseCountryScene = ({navigation}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [search, setSearch] = useState('');
  const [filteredDataSource, setFilteredDataSource] = useState<
    Country[] | null
  >(null);
  const [masterDataSource, setMasterDataSource] = useState<Country[] | null>(
    null,
  );
  const searchRef = useRef();
  const searchFilterFunction = async () => {
    if (search) {
      const newData = masterDataSource?.filter(function (item) {
        const itemData = item?.full_name_english
          ? item?.full_name_english.toUpperCase()
          : '';
        const textData = new RegExp(search.toUpperCase());
        return textData.test(itemData);
      });
      let a = await sortAndGrouped(newData);
      setFilteredDataSource(a);
    } else {
      setFilteredDataSource(a);
    }
  };
  const productReviews = useCallback(async () => {
    const {data} = await getCountries();
    if (data?.countries) {
      setMasterDataSource(data?.countries);
      let a = await sortAndGrouped(data?.countries);
      setFilteredDataSource(a);
    }
  }, []);

  const sortAndGrouped = useCallback(async (countries: Country[]) => {
    let data = countries
      .sort((a, b) =>
        a.two_letter_abbreviation.localeCompare(
          b.two_letter_abbreviation,
          'es',
          {sensitivity: 'base'},
        ),
      )
      .reduce((r, e) => {
        let title: string = e.two_letter_abbreviation[0];
        if (!r[title]) r[title] = {data: [e], title};
        else r[title].data.push(e);
        return r;
      }, {});
    let result = Object.values(data);
    return result;
  }, []);

  useEffect(() => {
    searchFilterFunction();
    productReviews();
  }, [search]);

  return (
    <SafeAreaView>
      <Header
        text={t('chooseCountry.chooseCountry')}
        backButton={true}
        navigation={navigation}
      />

      <View style={styles.marginSpace}>
        <View style={styles.searchCautioner}>
          <PhoneInputText
            testID="txtChooseCountrySearch"
            onFocus={() => {}}
            onChangeText={text => setSearch(text)}
            icon="searchIcon"
            style={styles.searchBarStyle}
            value={search}
            placeholder={t('chooseCountry.search')}
          />
          {search && (
            <TouchableOpacity
              onPress={() => {
                searchRef?.current?.clear();
                setSearch('');
              }}
              style={styles.closeIcon}>
              <ImageIcon icon="closeIcons" />
            </TouchableOpacity>
          )}
        </View>
        <Spacer type="Vertical" size="l" />
        <View>
          {filteredDataSource && (
            <SectionList
              sections={filteredDataSource}
              extraData={filteredDataSource}
              keyExtractor={(item, index) => item?.id + index}
              renderItem={({item}) => (
                <>
                  <Spacer type="Vertical" size="m" />
                  <Label
                    size="l"
                    color="textLight"
                    weight="400"
                    text={item?.full_name_english}
                  />
                </>
              )}
              renderSectionHeader={({section: {title}}) => (
                <>
                  <Spacer type="Vertical" size="m" />
                  <View style={styles.headerBox}>
                    <Label
                      color="textLight"
                      size="m"
                      weight="400"
                      text={title}
                    />
                  </View>
                </>
              )}
            />
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ChooseCountryScene;

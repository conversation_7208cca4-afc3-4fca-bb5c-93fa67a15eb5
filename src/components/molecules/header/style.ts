import {StyleSheet} from 'react-native';
import {Sizes} from 'common';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      height: Sizes.x8l,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: colors.background,
    },
    shadowParent:{overflow: "visible", zIndex: 100},
    shadow: {
      shadowColor: 'black',
      shadowOffset: { width: 0, height: 5 },
      shadowOpacity: 0.05,
      shadowRadius: 5,
      elevation: Sizes.xm
    },
    leftContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    optional: {
      marginBottom: Sizes.l,
    },
    goBackImage: {
      width: Sizes.xxxl,
      height: Sizes.xxxl,
    },
    dentalIcon: {
      tintColor: colors.text,
      width: Sizes.xxxl,
      height: Sizes.xxxl,
    },
    iconsHeader: {
      flexDirection: 'row',
    },
    icons1: {
      height: Sizes.xxl,
      alignItems: 'center',
    },
    icons: {
      marginLeft: Sizes.mx,
      paddingHorizontal: Sizes.m,
      width: Sizes.xxl,
      height: Sizes.xxl,
      marginHorizontal: Sizes.s,
      alignItems: 'center',
    },
    cartBag: {
      backgroundColor: colors.cartCountColor,
      width: Sizes.xl,
      height: Sizes.xl,
      borderRadius: Sizes.xl,
      position: 'absolute',
      zIndex: Sizes.xs,
      left: Sizes.m,
      alignItems: 'center',
      justifyContent: 'center',
      top: -Sizes.s,
    },
    searchBarStyle: {
      width: '80%',
      height: Sizes.xx4l,
      borderRadius: Sizes.xxl,
      borderColor: colors.border,
    },
    headerLogo: {height: Sizes.xl},
    headerTextContainer: {
      marginLeft: Sizes.sx,
      marginBottom: -Sizes.xs,
      flexDirection: 'row',
      alignItems: 'center',
    },
    dentalIconView: {
      marginTop: Sizes.sx,
    },
    extraItems: {width: '68%'},
    goBackView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    goBackButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingLeft: Sizes.m,
      alignSelf: 'stretch',
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    userView: {
      width: Sizes.xxl,
      height: Sizes.xxl,
      borderRadius: Sizes.l,
      backgroundColor: colors.categoryTitle,
      alignItems: 'center',
      justifyContent: 'center',
    },
    logoView: {
      width: Sizes.ex0,
      height: Sizes.xxl,
    },
    logoMainView: {flex: Sizes.x, paddingLeft: Sizes.m},
    borderRadiusStyle: {
      borderBottomLeftRadius: Sizes.mx,
      borderBottomRightRadius: Sizes.mx,
    },
    profileTxt: {
      marginBottom: -Sizes.xs,
    },
    profileTxt1: {
      alignSelf:'center',
      marginLeft:5
    },
    pressable: {
      borderRadius: Sizes.xms,
    },
    pressedStyle: {
      opacity: 0.7,
    },
    flex: {
      flex: Sizes.x,
    },
  });

export default styles;

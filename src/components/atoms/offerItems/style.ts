import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    offerList: {
      width: '30%',
      alignItems: 'center',
      borderRadius: Sizes.xm,
    },
    offerText: {
      color: colors.offerTitleBlue,
      fontSize: Sizes.xl,
      fontWeight: '700',
    },
    offerLeb: {
      color: colors.offerSubTitleBlue,
      fontSize: Sizes.m,
      fontWeight: '400',
    },
    lableView: {
      paddingTop: Sizes.xm,
    },
    imageContioner: {
      backgroundColor: colors.topBrandsIconBg,
      width: '100%',
      borderBottomEndRadius: Sizes.xm,
      borderBottomStartRadius: Sizes.xm,
      top: Sizes.s,
      alignItems: 'center',
    },
    mapView: {
      flexDirection: 'row',
      justifyContent: 'space-around',

      paddingVertical: Sizes.xl,
      width: '100%',
    },
  });

export default styles;

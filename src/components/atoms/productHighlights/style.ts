import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    highlightView: {
      flex: Sizes.x,
      borderColor: colors.darkPink,
      borderWidth: Sizes.x,
      borderRadius: Sizes.l,
    },
    highlightSubView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingTop: Sizes.m,
    },
    featureView: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xm,
    },
    productDescriptionView: {
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    productDesView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    arrowBottomView: {
      borderBottomLeftRadius: Sizes.l,
      borderBottomRightRadius: Sizes.l,
      flex: Sizes.x,
      paddingBottom: Sizes.xm,
      height: Sizes.x6l,
      justifyContent: 'flex-end',
      alignItems: 'flex-end',
      paddingHorizontal: Sizes.m,
    },
    tagStyle: {
      body: {
        whiteSpace: 'normal',
        color: colors.textLight,
      },
      a: {
        color: colors.linkText,
      },
    },
    detailsView: {
      paddingHorizontal: Sizes.m,
      paddingVertical: Sizes.xm,
    },
  });

export default styles;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    mainContinuer: {
      backgroundColor: colors.background,
      alignItems: 'center',
      padding: Sizes.xm,
    },
    image: {
      borderRadius: Sizes.xm,
      width: Sizes.ex0 + Sizes.m,
      height: Sizes.ex1 - Sizes.s,
    },
    body: {marginHorizontal: Sizes.xm, width: '63%'},
    directions: {flexDirection: 'row', alignItems: 'center'},
    subContainer: {width: '100%'},
    separator: {
      width: '100%',
      height: Sizes.xs,
      backgroundColor: colors.lightGray,
      marginHorizontal: Sizes.m,
    },
    flex: {flex: 1},
  });

export default styles;

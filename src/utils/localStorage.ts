import AsyncStorage from '@react-native-async-storage/async-storage';
import { debugLog } from './debugLog';

class _localStorage {
  get = async key => {
    try {
      const token = await AsyncStorage.getItem(key);
      if (token !== null) {
        return JSON.parse(token);
      } else {
        return false;
      }
    } catch (error) {
      debugLog(error);
      return false;
    }
  };

  set = async (key, value) => {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      debugLog(error);
      return false;
    }
  };

  clearAll = async () => {
    try {
      await AsyncStorage.clear();
      return true;
    } catch (error) {
      debugLog(error);
      return false;
    }
  };

  remove = async (key: any) => {
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (error) {
      debugLog(error);
      return false;
    }
  };
}

const localStorage = new _localStorage();

export default localStorage;

import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    maincontainer: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
    },
    or: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    couponValidView: {
      height: Sizes.ex148,
      resizeMode: 'stretch',
    },
    couponValidSubView: {
      height: Sizes.ex148,
    },
    couponCardContentContainer: {
      height: Sizes.ex148,
      flexDirection: 'row',
      padding: Sizes.xm,
      alignItems: 'center',
    },
    cardSection: {
      paddingVertical: Sizes.xms,
      marginHorizontal: Sizes.xxl,
      flex: Sizes.x,
      height: Sizes.ex148,
      justifyContent: 'center',
    },
    couponRenderView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.mx,
    },
    flexView: {
      flex: Sizes.x,
    },
    discountView: {
      transform: [{rotate: '270deg'}],
      marginRight: -Sizes.xxl,
    },
    discountLabelView: {
      flexWrap: 'wrap',
      width: '70%',
    },
  });

export default styles;

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.READ_CALL_LOG" tools:node="remove"/>
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>

  <permission android:name="${permissionName}"
    android:protectionLevel="signature" />

  <uses-permission android:name="${permissionName}" />
  <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>

  <application
    android:name=".MainApplication"
    android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher"
    android:roundIcon="@mipmap/ic_launcher_round"
    android:allowBackup="false"
    android:launchMode="singleTask"
    android:usesCleartextTraffic="true"
    android:requestLegacyExternalStorage="true"
    android:theme="@style/AppTheme">
    <profileable android:shell="true" />
    <activity
      android:name=".MainActivity"
      android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
      android:launchMode="singleTask"
      android:windowSoftInputMode="adjustResize"
      android:exported="true">
      <intent-filter>
        <!-- <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" /> -->
        <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
      </intent-filter>
      <!-- <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="dentalkart.onelink.me" />
        <data android:scheme="https" android:host="www.dentalkart.com" />
      </intent-filter> -->
      <!-- For dentalkart.onelink.me -->
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="https" android:host="dentalkart.onelink.me" />
</intent-filter>

<!-- For www.dentalkart.com -->
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <!-- <data android:scheme="https" android:host="www.dentalkart.com" /> -->
      <data android:scheme="https" android:host="www.dentalkart.com" android:pathPattern=".*\\.html"/>
            <data android:scheme="https" android:host="www.dentalkart.com" android:pathPattern="/"/>
    <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/shorts" />
    <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/cart" />
    <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/membership" />
    <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/categories" />
        <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/brands" />
    <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/track-page" />
    <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/account" />
        <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/help-center" />
        <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/dental-equipment" />
        <data android:scheme="https" android:host="www.dentalkart.com" android:pathPrefix="/sale" />
</intent-filter>

    </activity>
<activity-alias
            android:name="${applicationId}.MainActivityDefault"
            android:enabled="true"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

      <!-- Black Icon -->
      <activity-alias
            android:name="${applicationId}.MainActivityPremium"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_launcher_premium"
            android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>


    <service
      android:name="com.xiaomi.push.service.XMPushService"
      android:enabled="true"
      android:exported="false"
      android:process=":pushservice" />
    <service
      android:name="com.xiaomi.push.service.XMJobService"
      android:enabled="true"
      android:exported="false"
      android:permission="android.permission.BIND_JOB_SERVICE"
      android:process=":pushservice" />
    <service
      android:name="com.xiaomi.mipush.sdk.PushMessageHandler"
      android:enabled="true"
      android:exported="true" />
    <service
      android:name="com.xiaomi.mipush.sdk.MessageHandleService"
      android:enabled="true"
      android:exported="false" />
    <receiver
      android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver"
      android:exported="true">
      <intent-filter>
        <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
        <category android:name="android.intent.category.DEFAULT" />
      </intent-filter>
    </receiver>
    <receiver
      android:name="com.xiaomi.push.service.receivers.PingReceiver"
      android:exported="false"
      android:process=":pushservice">
      <intent-filter>
        <action android:name="com.xiaomi.push.PING_TIMER" />
      </intent-filter>
    </receiver>
    <receiver android:name="com.vasadental.dentalkart.MIPushReceiver"
      android:exported="true">
      <intent-filter>
        <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
      </intent-filter>
      <intent-filter>
        <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
      </intent-filter>
      <intent-filter>
        <action android:name="com.xiaomi.mipush.ERROR" />
      </intent-filter>
    </receiver>


    <!--Moengage-->
    <service
        android:name="com.moengage.firebase.MoEFireBaseMessagingService"
        android:exported="false">
      <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
      </intent-filter>
    </service>
    <!---->
    <meta-data
     android:name="com.google.android.geo.API_KEY"
     android:value="AIzaSyCud5-OguWrMmC95SpA0TV2Iaz2wUXz7h8"/>

     <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
     <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
     <meta-data android:name="com.facebook.sdk.AutoInitEnabled" android:value="true"/>
     <meta-data android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
           android:value="true"/>
  </application>
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:mimeType="*/*" />
    </intent>

    <package android:name="com.instagram.android" />
  </queries>
  <queries>
  <intent>
    <action android:name="android.intent.action.VIEW" />
    <data android:scheme="https" />
  </intent>
</queries>
  <!-- <uses-permission android:name="android.permission.RECEIVE_SMS" /> -->
  <uses-permission android:name="android.permission.READ_SMS" tools:node="remove"/>
  <uses-permission android:name="android.permission.RECEIVE_SMS" tools:node="remove" />
  <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" tools:node="remove"/>

</manifest>
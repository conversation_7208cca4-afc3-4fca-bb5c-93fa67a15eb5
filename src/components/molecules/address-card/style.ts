import {Platform, StyleSheet} from 'react-native';
import {Fonts, Sizes} from 'common';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      marginHorizontal: Sizes.m,
    },
    cardView: {
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.xm,
      backgroundColor: colors.background,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    iconBg: {
      width: '20%',
      backgroundColor: colors.modelCategoryBg,
      height: Sizes.xl,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Sizes.x4l - Sizes.xs,
    },
    textAddress: {
      fontWeight: '400',
      width: '75%',
    },
    userName: {
      fontWeight: '600',
      fontSize: Sizes.l,
    },
    row: {
      flexDirection: 'row',
    },
    subContainerView: {
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.background,
      paddingVertical: Sizes.xms,
      paddingHorizontal: Sizes.m,
      borderRadius: Sizes.xm,
      backgroundColor: colors.background,
    },

    borderTop: {
      borderTopWidth: Sizes.x,
      // paddingHorizontal: Sizes.xl,
      borderTopColor: colors.grey2,
      borderTopStyle: 'solid',
    },
    pinCodeView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.s,
      justifyContent: 'space-between',
    },
    flagIcon: {
      width: Sizes.xxl,
      height: Sizes.xxl,
    },
    onSelectView: {
      flex: Sizes.x,
      borderColor: colors.grey2,
      borderRadius: Sizes.sx,
      borderWidth: Sizes.x,
      paddingHorizontal: Sizes.mx,
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
      height: Sizes.x46,
    },
    shippingView: {
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
      paddingBottom: Sizes.xm,
    },
    defaultView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Sizes.s,
      justifyContent: 'space-between',
    },
    selectedView: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.s,
    },
    checkoutView: {
      flexDirection: 'row',
      paddingLeft: Sizes.x5l,
      marginBottom: Sizes.x,
    },
    buttonView: {
      width: Sizes.ex,
      height: Sizes.x3l,
      borderWidth: Sizes.x,
      borderRadius: Sizes.sx,
      borderColor: colors.text2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    defaultSubView: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: Sizes.x,
      flexWrap: 'wrap',
    },
    checkoutSubView: {
      width: Sizes.x4l,
      height: Sizes.x4l,
      borderWidth: Sizes.x,
      borderRadius: Sizes.xms,
      borderColor: colors.grey2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    addressView: {
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      padding: Sizes.xms,
      borderRadius: Sizes.xm,
      paddingHorizontal: Sizes.xm,
      backgroundColor: colors.background,
      marginTop: Sizes.xms,
    },
    styleDropDown: {
      height: Sizes.x7l,
      color: colors.grey2,
      borderWidth: 0,
    },
    defaultCardView: {
      backgroundColor: colors.blue,
      borderRadius: Sizes.s,
      width: Sizes.x70,
      height: Sizes.xl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    fOne: {
      flex: Sizes.x,
    },
    addressCard: {
      flex: Sizes.x,
      borderRadius: Sizes.xm,
      backgroundColor: colors.background,
      marginBottom: Sizes.xm,
      padding: Sizes.xm,
      marginHorizontal: Sizes.m,
    },
    selectedAdd: {
      borderColor: colors.text2,
      borderWidth: Sizes.x,
    },
    editBtn: {
      // height: Sizes.x26,
      width: Sizes.x9l,
      flexDirection: 'row',
      // justifyContent: 'center',
      alignItems: 'center',
      // borderColor: colors.vividOrange,
      // borderWidth: Sizes.x,
      borderRadius: Sizes.xm,
    },
    postCodeStyle: {
      flex: Sizes.x,
      color: colors.text,
    },
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    fRow: {
      flex: Sizes.x,
      flexDirection: 'row',
    },
    awayView: {
      backgroundColor: colors.limeGreen,
      paddingHorizontal: Sizes.xm,
      height: Sizes.xl,
      borderRadius: Sizes.s,
      justifyContent: 'center',
      alignItems: 'center',
    },
    defaultGif: {
      width: Sizes.x70,
      height: Sizes.xl,
      borderRadius: Sizes.s,
      overflow: 'hidden',
    },
    tagView: {
      backgroundColor: colors.aliceBlue,
      height: Sizes.xxl,
      width: Sizes.xxl,
      borderRadius: Sizes.sx,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tick: {
      position: 'absolute',
      right: -Sizes.m,
      top: -Sizes.m,
    },
    shippingTag: {
      backgroundColor: colors.grey2,
      paddingHorizontal: Sizes.s,
      borderRadius: Sizes.s,
      height: Sizes.xl,
      justifyContent: 'center',
      alignItems: 'center',
    },
    alignCenter: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkboxStyle: {
      height: Sizes.mx,
      width: Sizes.mx,
    },
    bottomView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    button: {
      borderRadius: Sizes.sx,
      height: Sizes.x4l,
      width: Sizes.ex114,
      color: colors.whiteColor,
    },
    saveButton: {
      fontSize: Sizes.m,
      fontWeight: '600',
      color: colors.whiteColor,
      fontFamily: Fonts.SemiBold,
    },
  });

export default styles;

import AsyncStorage from '@react-native-async-storage/async-storage';
import { debugLog } from './debugLog';

class _tokenClass {
  loginStatus = async () => {
    try {
      const token = await AsyncStorage.getItem('token');
      if (token !== null) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      debugLog(error);
      return false;
    }
  };

  getToken = async () => {
    try {
      const token = await AsyncStorage.getItem('token');
      if (token !== null) {
        return token;
      } else {
        return false;
      }
    } catch (error) {
      debugLog(error);
      return false;
    }
  };

  setToken = async (token: string) => {
    try {
      await AsyncStorage.setItem('token', token);
      return true;
    } catch (error) {
      debugLog(error);
      return false;
    }
  };

  removeToken = async () => {
    try {
      await AsyncStorage.removeItem('token');
      return true;
    } catch (error) {
      debugLog(error);
      return false;
    }
  };
}

const tokenClass = new _tokenClass();

export default tokenClass;

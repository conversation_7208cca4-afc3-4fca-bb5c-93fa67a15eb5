import React, {useEffect, useRef} from 'react';
import {TouchableOpacity, View, TextInput, Pressable} from 'react-native';
import {useTheme} from '@react-navigation/native';
import DashedLine from 'react-native-dashed-line';
import {t} from 'i18next';
import {
  ImageIcon,
  Label,
  Separator,
  Spacer,
  WithBackground,
} from 'components/atoms';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import <PERSON><PERSON>r<PERSON>and<PERSON> from 'utils/ErrorHandler';
import {useMemo} from 'react';

type Props = {
  isCouponOpen: boolean;
  openCoupon: () => void;
  couponList?: couponItem[];
  couponCode: string;
  changeCouponCode: (code: string) => void;
  onApplyDiscount: (code: string) => void;
  openCouponListModal: () => void;
  alwaysShow?: boolean;
  appliedCoupon?: boolean;
  saveNote?: string;
  savedAmount?: number;
  openKeyboard?: boolean;
  showNo?: boolean;
};

const CouponCart = (props: Props) => {
  const {
    isCouponOpen,
    openCoupon,
    couponList,
    couponCode,
    changeCouponCode, 
    onApplyDiscount,
    openCouponListModal,
    alwaysShow,
    savedAmount,
    appliedCoupon,
    saveNote,
    openKeyboard,
    showNo = false,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const couponRef = useRef(null);
  const TAG = 'CouponCart';
  useEffect(() => {
    if (openKeyboard) {
      couponRef?.current?.focus();
    }
  }, [openKeyboard]);
  return (
    <View style={styles.applyCouponView}>
      {!isCouponOpen ? (
        <TouchableOpacity 
        testID='tOCouponCart'
        onPress={() => openCoupon()}>
          <View style={styles.subApplyCouponView}>
            <View style={styles.brandTagView}>
              <ImageIcon icon="brandTag" size="xxl" />
              <Spacer size="xm" type="Horizontal" />
              <Label
                text={t('cart.applyCoupons')}
                size="m"
                fontFamily="SemiBold"
                color="text"
              />
            </View>
            <ImageIcon icon="arrowRight" size="xxl" />
          </View>
        </TouchableOpacity>
      ) : (
        <>
          <View style={styles.couponMainView}>
            <View style={styles.couponView}>
              <View style={styles.brandTagView}>
                <View style={styles.brandView}>
                  {!showNo && <ImageIcon icon="couponPercent" size="xxl" />}
                  <Label
                    text={`${showNo ? '3. ' : ''}${t('cart.bestCoupon')}`}
                    size="mx"
                    weight="600"
                    color="text"
                  />
                  <Spacer size="s" type="Horizontal" />
                  {showNo && <ImageIcon icon="couponPercent" size="xxl" />}
                </View>
                <ErrorHandler
                  componentName={`${TAG} Button`}
                  onErrorComponent={<View />}>
                  <Button
                    onPress={() => openCouponListModal()}
                    size="zero-height"
                    borderColor="categoryTitle"
                    iconRight="arrowRight"
                    iconSize="l"
                    labelSize="m"
                    text={t('buttons.ViewAllCoupon')}
                    labelColor='categoryTitle'
                    weight="500"
                    // tintColor={appliedCoupon ? 'grey' : 'categoryTitle'}
                    tintColor='categoryTitle'
                    // disabled={appliedCoupon ? true : false}
                  />
                </ErrorHandler>
              </View>
              {!alwaysShow && (
                <TouchableOpacity
                testID='touchabelOpacityOpenCoupon'
                onPress={() => openCoupon()}>
                  <ImageIcon icon="arrowUp" size="xxl" />
                </TouchableOpacity>
              )}
            </View>
            {appliedCoupon ? <Spacer size="m" /> : <Spacer size="sx" />}
            <View style={styles.fRow}>
              <View style={styles.couponApplied}>
                {appliedCoupon && (
                  <View style={styles.couponAppliedLabelContainer}>
                    <Label
                      text={t('cart.1CouponApplied')}
                      size="xms"
                      fontFamily="Medium"
                      color="categoryTitle"
                      // style={styles.couponAppliedLabel}
                    />
                  </View>
                )}
                <View style={styles.couponCodeView}>
                  {appliedCoupon ? (
                    <View style={styles.selectCoupon}>
                      <ErrorHandler
                        componentName={`${TAG} WithBackground`}
                        onErrorComponent={<View />}>
                        <WithBackground
                          image="couponCodeApplied"
                          imageStyle={styles.couponCodeMainViewApplied}
                          style={styles.couponCodeSubViewApplied}>
                          <Label
                            text={couponCode}
                            size="m"
                            fontFamily="Medium"
                            color="whiteColor"
                          />
                        </WithBackground>
                      </ErrorHandler>
                      <Spacer size="m" type="Horizontal" />
                      <View style={{ flex: 1 }}>
                        <Label
                          text={saveNote}
                          size="xms"
                          fontFamily="Medium"
                          color="categoryTitle"
                        />
                      </View>
                    </View>
                  ) : (
                    <TextInput testID="txtCouponCartCouponCode"
                      ref={couponRef}
                      value={couponCode}
                      onChangeText={text => changeCouponCode(text)}
                      placeholder={t('cart.couponPlaceholder')}
                      placeholderTextColor={colors.text2}
                      style={styles.couponInput}
                      allowFontScaling={false}
                    />
                  )}
                </View>
              </View>
              <Spacer type="Horizontal" size="xms" />
              <ErrorHandler
                componentName={`${TAG} Button`}
                onErrorComponent={<View />}>
                <Button
                  onPress={() => onApplyDiscount(couponCode)}
                  text={
                    appliedCoupon ? t('buttons.remove') : t('buttons.apply')
                  }
                  disabled={appliedCoupon ? false : !couponCode.trim()}
                  type={
                    appliedCoupon || !!couponCode.trim()
                      ? 'secondary'
                      : 'disabled'
                  }
                  size="extra-small"
                  labelColor={
                    appliedCoupon
                      ? 'whiteColor'
                      : couponCode.trim()
                      ? 'background'
                      : 'whiteColor'
                  }
                  labelSize="mx"
                  weight="500"
                  radius="sx"
                  selfAlign="stretch"
                  paddingHorizontal="xxxl"
                  paddingVertical="xm"
                  labelStyle={styles.btnLabel}
                />
              </ErrorHandler>
            </View>
          </View>

          <View style={styles.couponMainView}>
            <Spacer size="xm" />
            <View style={styles.extraView}>
              {couponList[0]?.data?.length > 0 && !appliedCoupon ? (
                <View style={styles.flexView}>
                  {savedAmount && savedAmount > 0 && (
                    <Label
                      text={
                        t('cart.extra') +
                        '₹' +
                        savedAmount +
                        t('cart.extraSavedOff')
                      }
                      size="mx"
                      fontFamily="Medium"
                      color="categoryTitle"
                    />
                  )}
                  <Pressable
                    onPress={() => {
                      changeCouponCode(couponList[0]?.data[0].coupon_code);
                      onApplyDiscount(couponList[0]?.data[0].coupon_code);
                    }}>
                    <ErrorHandler
                      componentName={`${TAG} WithBackground`}
                      onErrorComponent={<View />}>
                      <WithBackground
                        image="couponCode"
                        imageStyle={styles.couponCodeMainView}
                        style={styles.couponCodeSubView}>
                        <Spacer size="xm" />
                        <Label
                          // text={t('cart.codeHere')}
                          text={couponList[0]?.data[0].coupon_code}
                          size="m"
                          fontFamily="Medium"
                          color="whiteColor"
                        />
                      </WithBackground>
                    </ErrorHandler>
                  </Pressable>
                </View>
              ) : (
                <View style={styles.flexView} />
              )}
            </View>
          </View>
        </>
      )}
    </View>
  );
};

export default React.memo(CouponCart);

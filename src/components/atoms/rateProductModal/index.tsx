import React, {useState, useEffect, useCallback} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useTheme} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {Label, ImageIcon, Spacer, Tag, DropDown} from 'components/atoms';
import {InputBox} from 'components/molecules';
import stylesWithOutColor from './style';
import {Formik} from 'formik';
import {t} from 'i18next';
import {rateAndReviews, checkCanReviewForProduct} from 'services/productDetail';
import {useSelector} from 'react-redux';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {validateRatingSchema} from 'utils/validationError';
import FastImage from 'react-native-fast-image';
import getImageUrl from 'utils/imageUrlHelper';
import LinearGradient from 'react-native-linear-gradient';
import DynamicHeightModal from 'components/organisms/DynamicHeightModal';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {useMemo} from 'react';
import useToastConfig from '../toastConfig/toastConfig';
import Toast from 'react-native-toast-message';
import { debugError } from 'utils/debugLog';

type Props = {
  visible: boolean;
  onClose: () => void;
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  product: ProductData | null;
  review: ProductReviewResponse | any;
  groupProduct: ChildProduct[];
  setRateProductModal: boolean;
};

const RateProductModal = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const insets = useSafeAreaInsets();
  const {isLoggedIn} = useSelector((state: RootState) => state.app);
  const {
    visible,
    onClose,
    navigation,
    product,
    review,
    groupProduct,
    setRateProductModal,
  } = props;
  const [dropDownError, setDropDownError] = useState('');
  const [showGoodReview, setShowGoodReview] = useState(false);
  const [checkInReview, setCheckInReview] = useState<any>(false);
  const [loader, setLoader] = useState<any>(false);
  const [selectedProductForRating, setSelectedProductForRating] =
    useState(null);

  const reviewTextMap = {
    1: 'Poor',
    2: 'Bad',
    3: 'Good',
    4: 'Very Good',
    5: 'Excellent',
  };

  const canReviewForProduct = async (id: any) => {
    let data: any = await checkCanReviewForProduct(id);
    return !!data?.data?.can_submit;
  };

  const reviewProduct = async (values: ProductReviewForm) => {
    const {data, status} = await rateAndReviews(
      values,
      product?.type === 'simple'
        ? product?.product_id
        : selectedProductForRating,
    );
    if (data && status) {
      setRateProductModal(false);
      showSuccessMessage(data?.message);
    } else {
      setRateProductModal(false);
      showErrorMessage(data?.message);
    }
  };
  const {toastConfig} = useToastConfig();
  useEffect(() => {
    const check = async () => {
      setLoader(true);
      let result = await canReviewForProduct(product?.product_id);
      setLoader(false);
      setCheckInReview(!!result);
    };
    if (product?.type === 'simple' && isLoggedIn) {
      check();
    }
  }, [product?.product_id, isLoggedIn, product?.type]);

  const StarRating = React.memo(({ value, onChange, reviewTextMap }) => {
    const { colors } = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
    
    return (
      <View style={styles.starRating}>
        {[...Array(5)].map((_, index) => {
          const starNumber = index + 1;
          return (
            <View key={index}>
              <TouchableOpacity 
                onPress={() => onChange(starNumber)}
                hitSlop={{ top: 10, bottom: 10 }}
                // style={{ padding: 10 }} 
              >
                <ImageIcon
                  icon={starNumber <= value ? 'filledStar' : 'emptyStar'}
                  style={styles.starItem}
                  size="xx"
                />
              </TouchableOpacity>
              <Spacer type="Horizontal" size="s" />
            </View>
          );
        })}
        <Spacer type="Horizontal" size="s" />
        <View style={styles.flexShrink}>
          {value === 0 ? null : (
            <Label
              color="green2"
              weight="500"
              size="mx"
              text={`${value === 0 ? '' : reviewTextMap[value]}`}
            />
          )}
        </View>
      </View>
    );
  });

  const handleProductChange = useCallback(async (item) => {
    const productId = item?.product_id;
    setSelectedProductForRating(productId);
  
    try {
      const { data } = await checkCanReviewForProduct(productId);
  
      const canSubmit = !!data?.can_submit;
      setCheckInReview(canSubmit);
  
      if (!canSubmit && data?.errors?.length > 0) {
        const errorMessages = data.errors.map(err => err?.message);
        setDropDownError(errorMessages);
      } else {
        setDropDownError('');
      }
    } catch (error) {
      debugError("Error checking review eligibility:", error);
    }
  }, [setSelectedProductForRating, setCheckInReview, setDropDownError]);

  return (
    <DynamicHeightModal
      useInsets
      visible={visible}
      onClose={() => setRateProductModal(false)}
      bounceFalse
      maxHeight={0.86}
      content={
        <View style={styles.viewProduct}>
          <Formik
            initialValues={{
              rating: 0,
              title: '',
              content: '',
              media: [
                {
                  type: 'image',
                  url: 'https://image.url',
                },
              ],
            }}
            validationSchema={validateRatingSchema}
            onSubmit={(values: ProductReviewForm) => {
              reviewProduct(values);
            }}>
            {({
              setFieldTouched,
              setFieldValue,
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
              isValid,
            }) => (
              <>
                <View
                  style={[
                    styles.offerZoneBannerView,
                    {paddingBottom: insets.bottom},
                  ]}>
                  <View style={styles.rateReviewView}>
                    <Label
                      text={t('PDP.ratingReviews')}
                      size="l"
                      fontFamily="SemiBold"
                      color="text2"
                    />
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.rateReviewView}>
                    <View style={styles.fRow}>
                      <FastImage
                        source={{
                          uri: getImageUrl(
                            product?.media?.find(
                              item => item.media_type === 'image',
                            )?.file,
                          ),
                        }}
                        style={styles.ratingImage}
                      />
                      <Spacer size="xm" type="Horizontal" />
                      <View style={styles.flex}>
                        <Label
                          text={product?.name}
                          size="mx"
                          fontFamily="Medium"
                          color="text2"
                        />
                        <Spacer size="s" />

                        <View style={styles.rowCenter}>
                          {review?.review_meta?.average_rating > 0 && (
                            <Tag
                              style={styles.starView}
                              label={(review?.review_meta?.average_rating).toFixed(
                                1,
                              )}
                              icon="starIcon"
                              color="green2"
                              isRightIcon
                            />
                          )}

                          <Spacer size="xm" type="Horizontal" />
                          {review?.review_meta?.total_reviews > 0 && (
                            <Label
                              text={`(${review?.review_meta?.total_reviews})`}
                              size="mx"
                              fontFamily="Medium"
                              color="text2"
                            />
                          )}
                        </View>
                      </View>
                    </View>
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.accordionView}>
                    <TouchableOpacity
                      onPress={() => setShowGoodReview(!showGoodReview)}
                      style={styles.fRow}>
                      <View style={styles.flex}>
                        <Label
                          text={'What makes a good review'}
                          size="mx"
                          fontFamily="Medium"
                          color="text2"
                        />
                      </View>
                      <ImageIcon
                        icon={showGoodReview ? 'arrowUp' : 'downArrowIcon'}
                        tintColor="grey"
                        size="xxl"
                      />
                    </TouchableOpacity>
                    {showGoodReview ? (
                      <>
                        <Spacer size="m" />
                        <View style={styles.accordionItem}>
                          <Label
                            text={'Have you used this product?'}
                            size="mx"
                            fontFamily="Medium"
                            color="text2"
                          />
                          <Spacer size="xm" />
                          <Label
                            text={
                              'Your review should be about your experience with the product.'
                            }
                            size="mx"
                            fontFamily="Regular"
                            color="text2"
                          />
                        </View>
                        <View style={styles.accordionItem}>
                          <Label
                            text={'Why review a product?'}
                            size="mx"
                            fontFamily="Medium"
                            color="text2"
                          />
                          <Spacer size="xm" />
                          <Label
                            text={
                              'Your valuable feedback will help fellow shoppers decide.'
                            }
                            size="mx"
                            fontFamily="Regular"
                            color="text2"
                          />
                        </View>
                        <View
                          style={[styles.accordionItem, {paddingBottom: 0}]}>
                          <Label
                            text={'How to review a product?'}
                            size="mx"
                            fontFamily="Medium"
                            color="text2"
                          />
                          <Spacer size="xm" />
                          <Label
                            text={
                              'Your review should include facts. An honest opinion is always appreciated. If you have an issue with the product or service please contact us from the '
                            }
                            children={
                              <Label
                                text={`${t('PDP.helpCenter')}.`}
                                size="mx"
                                fontFamily="Regular"
                                color="blueOffer"
                                textDecorationLine="underline"
                                onPress={() => {
                                  onClose();
                                  navigation.navigate('HelpCenter');
                                }}
                              />
                            }
                            size="mx"
                            fontFamily="Regular"
                            color="text2"
                          />
                        </View>
                      </>
                    ) : null}
                  </View>
                  <Spacer size="xm" />
                  <View style={styles.selectView}>
                    {product?.type == 'grouped' ? (
                      <>
                        <Label
                          text={t('PDP.selectBought')}
                          size="l"
                          fontFamily="Medium"
                          color="categoryTitle"
                        />

                        <Spacer size="s" />
                        <DropDown
                          placeholderStyle={styles.dropDown}
                          selectedTextStyle={styles.selectedInputView}
                          styleDropDown={styles.styleDropView}
                          iconStyle={styles.styleDropSubView}
                          itemContainerStyle={styles.itemContainerView}
                          placeholder={t('otherText.product')}
                          data={groupProduct}
                          labelField="name"
                          valueField="product_id"
                          value={selectedProductForRating}
                          search={false}
                          onChange={handleProductChange}
                          renderItem={item => (
                            <View
                              key={item?._index?.toString()}
                              style={styles.rateView}>
                              <Label
                                text={item?.name}
                                size="mx"
                                fontFamily="Medium"
                                color="text2"
                              />
                            </View>
                          )}
                        />
                        {dropDownError && (
                          <>
                            <Spacer size="s" />
                            <Label
                              color="textError"
                              text={dropDownError}
                              size="m"
                              weight="400"
                            />
                          </>
                        )}
                      </>
                    ) : null}
                    <Spacer size="xms" />
                    <View style={styles.rateSubView}>
                      <Label
                        text={t('PDP.productRate')}
                        size="l"
                        fontFamily="Medium"
                        color="categoryTitle"
                      />
                      <Spacer size="xx" type="Horizontal" />
                        <StarRating 
                            value={values.rating} 
                            onChange={(rating) => setFieldValue('rating', rating)}
                            reviewTextMap={reviewTextMap}
                          />
                    
                    </View>
                    <Spacer size="sx" />
                    {touched.rating && errors.rating && (
                      <Label
                        text={t(errors?.rating)}
                        size="m"
                        color="textError"
                      />
                    )}
                    <Spacer size="xms" />
                    <InputBox
                      keyboardType="default"
                      style={styles.inputBox}
                      placeholder={t('PDP.rateThisProduct')}
                      placeholderTextColor={colors.text2}
                      onChangeText={handleChange('title')}
                      onBlur={handleBlur('title')}
                      value={values.title}
                      error={String(
                        t(touched.title ? (errors.title as string) : ''),
                      )}
                    />
                    <Spacer size="xms" />
                    <Label
                      text={t('PDP.reviewThisProduct')}
                      size="l"
                      fontFamily="Medium"
                      color="categoryTitle"
                    />
                    <Spacer size="s" />
                    <InputBox
                      style={styles.inputBox}
                      keyboardType="default"
                      numberOfLines={4}
                      multiline
                      placeholder={t('PDP.description')}
                      placeholderTextColor={colors.text2}
                      onChangeText={handleChange('content')}
                      onBlur={handleBlur('content')}
                      value={values.content}
                      error={String(
                        t(touched.content ? (errors.content as string) : ''),
                      )}
                    />
                    <Spacer size="xms" />
                    {!loader && (
                      <>
                        {checkInReview ? (
                          <View style={styles.submitView}>
                            <TouchableOpacity
                              onPress={() => {
                                setFieldTouched('rating', true);
                                setFieldTouched('title', true);
                                setFieldTouched('content', true);

                                if (isValid) {
                                  handleSubmit();
                                }
                              }}>
                              <LinearGradient
                                colors={[colors.coral, colors.persimmon]}
                                start={{x: 0, y: 0}}
                                end={{x: 1, y: 1}}
                                style={styles.ratingButton}>
                                <Label
                                  size="mx"
                                  color="whiteColor"
                                  fontFamily="Medium"
                                  text={t('buttons.submit')}
                                />
                              </LinearGradient>
                            </TouchableOpacity>
                          </View>
                        ) : (
                          <Label
                            text={
                              isLoggedIn
                                ? !checkInReview
                                  ? t('PDP.rateNot')
                                  : ''
                                : t('PDP.rateLogin')
                            }
                            size="l"
                            fontFamily="Medium"
                            color="textError"
                          />
                        )}
                      </>
                    )}
                    <Spacer size="l" />
                  </View>
                </View>
              </>
            )}
          </Formik>
          <Toast config={toastConfig} swipeable={false} />
        </View>
      }
    />
  );
};

export default React.memo(RateProductModal);

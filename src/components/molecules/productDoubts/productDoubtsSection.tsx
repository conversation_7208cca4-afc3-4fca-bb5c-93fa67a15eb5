import { Label, Spacer } from 'components/atoms';
import { t } from 'i18next';
import React, { useMemo } from 'react';
import { View } from 'react-native';
import ErrorHandler from 'utils/ErrorHandler';
import Button from '../button';
import stylesWithOutColor from '../../../scenes/productDetail/style';
import { useTheme } from '@react-navigation/native';

const TAG = 'PoductDetailScreen';
type Props = {
    postQuestionVerify:  () => void;
}
const ProductDoubtsSection = ({
  postQuestionVerify,
}: Props) => {
    const {colors} = useTheme();
    const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
      <View style={styles?.alignCenter}>
        <Label
          text={t('PDP.doubtsProduct')}
          size="mx"
          textTransform="capitalize"
          weight="500"
          color="text"
        />
        <Spacer size="l" />
        <ErrorHandler
          componentName={`${TAG} Button`}
          onErrorComponent={<View />}>
          <Button
            borderColor="categoryTitle"
            type="bordered"
            onPress={postQuestionVerify}
            text={t('PDP.postQuestions') as string}
            labelSize="mx"
            paddingHorizontal="m"
            radius="xm"
            weight="500"
            labelColor="categoryTitle"
            size="extra-small"
          />
        </ErrorHandler>
      </View>

  );
};

export default React.memo(ProductDoubtsSection);
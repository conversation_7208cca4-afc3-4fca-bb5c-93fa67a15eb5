/**
 * @format
 */

import {AppRegistry, LogBox} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';
import {startNetworkLogging} from 'react-native-network-logger';
import { debugLog } from 'utils/debugLog';

LogBox.ignoreLogs(['Require cycle']);

messaging().setBackgroundMessageHandler(async remoteMessage => {
  debugLog('Message is handled in the background!', remoteMessage);
});
startNetworkLogging();

AppRegistry.registerComponent(appName, () => App);

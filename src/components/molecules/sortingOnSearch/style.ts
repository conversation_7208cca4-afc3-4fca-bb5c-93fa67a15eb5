import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    optionsStyle: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Sizes.xm,
    },
    modelText: {marginLeft: Sizes.xm},
    modelStyleIos: {
      borderTopLeftRadius: Sizes.s,
      borderTopRightRadius: Sizes.s,
      paddingVertical: Sizes.s,
      backgroundColor: colors.background,
      maxHeight: '95%',
    },
    closeButton: {
      alignItems: 'flex-end',
      padding: Sizes.s,
    },
  });

export default styles;

import React from 'react';
import {View, TouchableOpacity, ImageBackground} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {Label, ImageIcon, Spacer, Separator, Tag} from 'components/atoms';
import {t} from 'i18next';
import stylesWithOutColor from './style';
import getImageUrl from 'utils/imageUrlHelper';
import {useMemo} from 'react';

type Props = {
  product: ProductData | null;
  item: SliderPdp;
  scrollToSpecificOffset: () => void;
  onFullScreen: () => void;
  viewProduct: () => void;
};

const PDPSliderItem = (props: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const {product, item, scrollToSpecificOffset, onFullScreen, viewProduct} =
    props;

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.visibleFullScreenView}
      onPress={() => onFullScreen()}>
      <ImageBackground
        source={{uri: getImageUrl(item?.file)}}
        style={styles.visibleSubScreenView}>
        <View style={styles.fullScreenImgView}>
          <ImageIcon icon="bestSeller" style={styles.bestSellerTag} />
          <View style={styles.heartView}>
            <ImageIcon icon="heart" size="xxl" tintColor="text" />
          </View>
        </View>
        {product?.type === 'simple' && (
          <View style={styles.shareView}>
            <ImageIcon icon="shareNewIcon" size="xl" tintColor="text" />
          </View>
        )}
        <View style={styles.deliveryView}>
          <Tag
            style={styles.tagView}
            labelStyle={styles.tagLabel}
            label={t('productCardVertical.expressDelivery')}
            color="skyBlue2"
          />
          <TouchableOpacity
            onPress={() => scrollToSpecificOffset()}
            style={styles.scrollToView}>
            <Label
              text={product?.rating}
              fontFamily="Regular"
              size="mx"
              color="text"
            />
            <Spacer size="sx" type="Horizontal" />
            <ImageIcon
              icon="starFill"
              size="mx"
              tintColor="green2"
              style={styles.rateFillStar}
            />
            <Spacer size="sx" type="Horizontal" />
            <Separator thickness="x" color="grey2" Vertical={true} height="l" />
            <Spacer size="sx" type="Horizontal" />
            <Label
              text={product?.reviews_count}
              fontFamily="Regular"
              size="mx"
              color="text"
            />
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          onPress={() => viewProduct()}
          style={styles.productImgView}>
          <ImageIcon icon="viewSimilarProduct" size="x4l" />
        </TouchableOpacity>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default PDPSliderItem;

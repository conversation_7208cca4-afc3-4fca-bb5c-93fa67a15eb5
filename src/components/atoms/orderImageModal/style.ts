import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    modalStyle: {
      margin: 0,
    },
    imageModelMain: {
      backgroundColor: colors.blueLagoon,
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    subImageView: {
      backgroundColor: colors.whiteColor,
      width: '100%',
      marginTop: '40%',
      paddingHorizontal: Sizes.xms,
      borderTopLeftRadius: Sizes.m,
      borderTopRightRadius: Sizes.m,
    },
    modalCloseBtnContainer: {
      flex: Sizes.x,
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    modalCloseButton: {
      paddingBottom: Sizes.x60,
    },
    itemListMain: {
      flexDirection: 'row',
      paddingHorizontal: Sizes.xm,
      borderBottomWidth: Sizes.x,
      borderBottomColor: colors.grey2,
      paddingVertical: Sizes.l,
    },
    subItemList: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      padding: Sizes.x,
      justifyContent: 'center',
      width: Sizes.ex88,
      height: Sizes.ex88,
      marginRight: Sizes.xm,
    },
    subImageSize: {
      width: Sizes.ex84,
      height: Sizes.ex84,
    },
    itemStyle: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    sourceImage: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    coinImage: {
      width: Sizes.mx,
      height: Sizes.mx,
    },
    flexOne: {
      flex: Sizes.x,
    },
    fRow: {
      flexDirection: 'row',
    },
    centerItem: {
      flex: Sizes.x,
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    bottomView: {
      alignItems: 'flex-end',
      paddingVertical: Sizes.xms,
    },
    earnView: {
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
    },
    orderView: {
      marginLeft: Sizes.s,
    },
    statusView: {
      backgroundColor: colors.green4,
      alignItems: 'center',
      paddingHorizontal: Sizes.xms,
      paddingVertical: Sizes.xs,
      borderRadius: Sizes.s,
    },
    free: {
      backgroundColor: colors.greenLight,
      borderRadius: Sizes.s,
      height: Sizes.xsl,
      width: Sizes.x7l,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

export default styles;

import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {RouteProp, useTheme} from '@react-navigation/native';
import {<PERSON><PERSON>, <PERSON>er, TextInputBox} from 'components/molecules';
import stylesWithOutColor from './style';
import {Label, Spacer} from 'components/atoms';
import {t} from 'i18next';
import {useDispatch} from 'react-redux';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {showErrorMessage, showSuccessMessage} from 'utils/show_messages';
import {View} from 'react-native';
import {userLogin} from 'services/auth';
import {changeEmailValidationSchema} from 'utils/validationError';
import Icons from 'common/icons';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'ChangeEmailOrMobile'>;
};

const ChangeEmailOrMobile = ({navigation, route}: Props) => {
  const {isEditType} = route.params;
  const dispatch = useDispatch();
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  const [error, setError] = useState({});

  const [values, setValues] = useState({
    emailOrPhone: '',
    type: isEditType,
  });

  const validationSchema = useMemo(() => {
    return changeEmailValidationSchema();
  }, [isEditType]);

  const validate = useCallback(
    async formValues => {
      try {
        const valid = await validationSchema.validateSync(formValues, {
          abortEarly: false,
        });

        setError({});
      } catch (err) {
        const errorsMessages = {};
        err?.inner?.forEach(element => {
          const path = element.path;
          errorsMessages[path]
            ? errorsMessages[path].push(t(element.message))
            : (errorsMessages[path] = [t(element.message)]);
        });

        setError(errorsMessages);
      }
    },
    [validationSchema],
  );

  const sendOtpMobileEmail = async () => {
    if (Object.keys(error).length > 0 || values) {
      dispatch(setLoading(true));
      const {data, status} = await userLogin({
        recipient: values.emailOrPhone?.trim(),
        action: values.type === 'email' ? 'email_update' : 'mobile_update',
        authentication_type: values.type,
      });
      dispatch(setLoading(false));
      if (status) {
        showSuccessMessage(data?.message);
        navigation.navigate('ChangeEmailOrMobileOtp', {
          isEditType: isEditType,
          EmailOrMobileValue: values,
        });
      } else {
        showErrorMessage(data.message);
        setError(data.message);
      }
    }
  };

  useEffect(() => {
    if (values?.emailOrPhone?.length > 0) {
      validate(values);
    }
  }, [validate, values]);

  return (
    <SafeAreaView style={styles.container}>
      <Header
        backButton
        navigation={navigation}
        text={t('profile.profileHeader')}
      />

      <Spacer size="l" />

      <View style={styles.updateEmailLabel}>
        <Label
          text={`${t('profile.updateYour')} ${
            isEditType === 'email'
              ? t('profile.emailID')
              : t('profile.mobileNumber')
          }`}
          size="l"
          fontFamily="Regular"
          lineHeight="xxl"
          color="categoryTitle"
        />
        <Spacer size="xm" type="Horizontal" />
        <Label
          text={`${t('profile.steps')}`}
          size="m"
          fontFamily="Regular"
          color="text2"
          textTransform="capitalize"
        />
      </View>

      <Spacer size="l" />
      <View style={styles.subContainer}>
        <TextInputBox
          testID="txtChangeEmailPhone"
          label={
            isEditType === 'email'
              ? t('profile.emailNewPlace')
              : t('profile.numberNewPlace')
          }
          value={values.emailOrPhone}
          containerStyle={{color: colors.text}}
          onChangeText={text =>
            setValues(prev => ({...prev, emailOrPhone: text.trimStart()}))
          }
          maxLength={isEditType === 'email' ? 320 : 10}
          error={!!error?.emailOrPhone?.[0]}
          errorText={String(error?.emailOrPhone?.[0] || '')}
          leftIcon={isEditType === 'email' ? Icons.emailIcon : Icons.newphone}
          keyboardType={isEditType === 'email' ? 'email-address' : 'phone-pad'}
        />

        <Spacer size="l" />

        <Button
          style={styles.subCautionerBox}
          text={t('profileDetails.verify')}
          onPress={() =>
            Object.keys(error).length > 0 || values?.emailOrPhone?.length === 0
              ? {}
              : sendOtpMobileEmail()
          }
          labelSize="l"
          type={
            Object.keys(error).length > 0 || values?.emailOrPhone?.length === 0
              ? 'disabled'
              : 'secondary'
          }
          radius="sx"
          labelColor="whiteColor"
          weight="500"
        />
      </View>
    </SafeAreaView>
  );
};

export default ChangeEmailOrMobile;

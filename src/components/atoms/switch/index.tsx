import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {View, ViewStyle, Switch} from 'react-native';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {ToggleSwitch} from 'toggle-switch-react-native';
import {useMemo} from 'react';

type Props = {
  selected?: boolean;
  style?: ViewStyle['style'];
  onValueChange: (value: any) => void;
  value?: string | null;
};

const SwitchButton = ({onValueChange, value}: Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <View style={styles.container}>
      {/* <Switch
        trackColor={{false: colors.switch, true: colors.grey}}
        thumbColor={colors.text2}
        ios_backgroundColor={colors.text2}
        onValueChange={onValueChange}
        value={value}
      /> */}
      <ToggleSwitch
        isOn={false}
        onColor="green"
        offColor="red"
        labelStyle={{color: 'black', fontWeight: '900'}}
        size="large"
        onToggle={onValueChange}
        // onValueChange={onValueChange}
        // value={value}
      />
    </View>
  );
};

export default SwitchButton;

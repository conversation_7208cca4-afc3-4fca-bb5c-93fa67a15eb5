import {Fonts, Sizes} from 'common';
import {StyleSheet, Dimensions} from 'react-native';

const {width} = Dimensions.get('screen');

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    container: {
      flex: Sizes.x,
      backgroundColor: colors.background,
    },
    nameInput: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
      paddingHorizontal: Sizes.xms,
      height: Sizes.x46,
    },
    labelView: {
      flexDirection: 'row',
    },
    changePassword: {
      textDecorationLine: 'underline',
      textAlign: 'right',
    },
    nameInputText: {
      color: colors.text,
      flex: Sizes.x,
      fontFamily: Fonts.Medium,
      height: Sizes.x46,
    },
    saveButton: {
      fontSize: Sizes.mx,
      fontWeight: '400',
      color: colors.whiteColor,
    },
    button: {
      borderRadius: Sizes.sx,
      height: Sizes.x7l,
      width: width - Sizes.x4l,
      color: colors.whiteColor,
      marginHorizontal: Sizes.l,
    },
    horizontalSpace: {paddingHorizontal: Sizes.l},
    styleDropDown: {
      height: Sizes.x7l,
      color: colors.grey2,
      borderColor: colors.grey2,
      borderRadius: Sizes.xm,
      fontFamily: Sizes.mx,
    },
    icon: {
      marginRight: Sizes.mx,
      marginLeft: Sizes.sx,
    },
    buttonView: {
      shadowColor: colors.black,
      shadowOffset: {
        width: Sizes.xs,
        height: Sizes.xs,
      },
      elevation: Sizes.s,
      backgroundColor: colors.whiteColor,
      paddingVertical: Sizes.m,
      paddingHorizontal: Sizes.xxxl,
      borderTopColor: colors.grey2,
      borderTopWidth: Sizes.x,
    },
    inputView: {
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    dropDownItem: {
      paddingHorizontal: Sizes.l,
      paddingVertical: Sizes.sx,
      flexDirection: 'row',
      alignItems: 'center',
    },
    inputTextView: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
      marginBottom: -Sizes.xs,
    },
    dropDownTextStyle: {
      color: colors.text2,
      fontSize: Sizes.mx,
      fontFamily: Fonts.Medium,
    },
    placeTextStyle: {
      color: colors.grey,
      fontSize: Sizes.m,
      fontFamily: Fonts.Medium,
      marginBottom: -Sizes.s,
    },
    disableColor: {
      color: colors.grey2,
    },
    lineView: {
      height: Sizes.xms,
      backgroundColor: colors.grey7,
    },
    profileSection: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    profileView: {
      backgroundColor: colors.background3,
      borderRadius: Sizes.xms,
      height: 141,
      width: 143,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: Sizes.xm,
    },
    profile: {
      height: Sizes.ex0,
      width: Sizes.ex0,
    },
    cameraView: {
      height: Sizes.xxl,
      width: Sizes.xxl,
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      right: Sizes.s,
      bottom: Sizes.s,
    },
    cameraIcon: {
      height: 15,
      width: Sizes.xx,
    },
    flexOne: {
      flex: Sizes.x,
    },
    emailAddView: {
      justifyContent: 'space-between',
      flexDirection: 'row',
    },
    tabSaveBtn: {
      width: Sizes.ex250,
      alignSelf: 'flex-end',
      marginHorizontal: 0,
    },
    icons: {
      marginHorizontal: Sizes.s,
      width: Sizes.xx,
      height: Sizes.xx,
    },
    inputSubView: {
      paddingHorizontal: Sizes.xms,
      flexDirection: 'row',
      height: Sizes.x46,
      flex: Sizes.x,
      alignItems: 'center',
      borderRadius: Sizes.xm,
      borderWidth: Sizes.x,
      borderColor: colors.grey2,
    },
    downImg: {
      transform: [{rotate: '180deg'}],
    },
    errorStyle: {
      marginTop: Sizes.s,
    },
    headerView: {
      shadowColor: colors.black,
      shadowOffset: {
        width: Sizes.xs,
        height: Sizes.xs,
      },
      borderBottomColor: colors.grey2,
      borderBottomWidth: Sizes.x,
      marginBottom: Sizes.xm,
    },
    leftIcon: {
      tintColor: colors.text,
    },
    dropDownItemView: {
      marginBottom: Sizes.xms,
      overflow: 'hidden',
    },
    itemSelected: {
      backgroundColor: colors.text,
    },
    labelStyle: {
      top: -Sizes.xm,
    },
    rightIcon: {
      marginRight: -Sizes.xms,
    },
    errorBorder: {
      borderColor: colors.red3,
      borderWidth: Sizes.x,
    },
    placeStyle: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    scrollStyle: {
      flexGrow: 1,
    },
  });

export default styles;

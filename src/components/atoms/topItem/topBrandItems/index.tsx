import Icons from 'common/icons';
import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {Alert, FlatList, TouchableOpacity, View, ViewProps} from 'react-native';
import Label from '../../label';
import Spacer from '../../spacer';
import stylesWithOutColor from './style';
import {useTheme} from '@react-navigation/native';
import {useSelector} from 'react-redux';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {RootStackParamsList} from 'routes';
import {navigate} from 'utils/navigationRef';
import Separator from 'components/atoms/separator';
import {Sizes} from 'common';
import {AnalyticsEvents} from 'components/organisms';
import {useMemo} from 'react';

type Props = {
  icon?: keyof typeof Icons;
  text?: string;
  seeMore?: string;
  data: Array<homePageTopCategoryItemProps | gethomepageBrandsItemProps>;
  // navigation?: NativeStackNavigationProp<RootStackParamsList>;
  style: ViewProps['style'];
  viewAllBox: ViewProps['style'];
  viewAllTextStyle: ViewProps['style'];
  numColumns?: number | undefined;
  seeMoreBrands?: boolean;
  horizontal?: boolean;
  latterKey?: boolean;
  ViewHeading?: boolean;
  heading?: boolean;
  renderItem: ({
    item,
    index,
  }: {
    item: homePageTopCategoryItemProps | gethomepageBrandsItemProps;
    index: number;
  }) => React.ReactElement | null;
  viewAllPress?: () => void;
};

const TopBrandsItems = ({
  text,
  seeMore,
  data,
  style,
  numColumns,
  seeMoreBrands,
  horizontal,
  latterKey = false,
  ViewHeading = false,
  heading = true,
  renderItem,
  viewAllPress,
  viewAllBox,
  viewAllTextStyle,
}: // navigation,
Props) => {
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);

  return (
    <View style={[style, styles.topBrand]}>
      {heading && (
        <>
          <Spacer size="m" />
          <View style={styles.headingTop}>
            <Label size="l" lineHeight="l" text={text} />
            {seeMoreBrands && (
              <TouchableOpacity
                activeOpacity={0.8}
                style={styles.paddingSpace}
                onPress={() => {
                  navigate('AllBrands');
                  // AnalyticsEvents('VIEW_ALL', 'All Click', {});
                }}>
                <Label color="linkText" weight="400" text={seeMore} />
              </TouchableOpacity>
            )}
          </View>
          <Spacer size="m" />
        </>
      )}
      <View style={[styles.listAlign, latterKey && styles.deration]}>
        {latterKey && <Label text={'A'} />}

        <FlatList
          showsHorizontalScrollIndicator={false}
          horizontal={horizontal}
          numColumns={numColumns}
          data={data}
          renderItem={renderItem}
          keyExtractor={(item, index) => item?.__typename + index.toString()}
          ListHeaderComponent={
            ViewHeading && (
              <>
                <TouchableOpacity
                  onPress={viewAllPress}
                  style={(styles.topBrandsAlign, viewAllBox)}>
                  <Label style={[styles.subCategoriesList]} text={'View All'} />
                </TouchableOpacity>
                <Separator style={styles.separates} />
              </>
            )
          }
        />
      </View>
    </View>
  );
};

export default TopBrandsItems;

import React from 'react';
import {
  TouchableOpacity,
  View,
  SectionList,
  TouchableWithoutFeedback,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import Modal from 'react-native-modal';
import {ImageIcon, Label, Spacer, Radio} from 'components/atoms';
import {Button} from 'components/molecules';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import {checkDevice} from 'utils/utils';
import {useMemo} from 'react';

type Props = {
  visible: boolean;
  onClose: () => void;
  clearFilter: () => void;
  applyFilter: () => void;
  onSelectFilter: (title: string, item: OrderFilterOption) => void;
  selectedFilters: OrderFilterOption;
  data: OrderFilterOption[];
};

const OrderFilter = (props: Props) => {
  const {
    visible,
    onClose,
    clearFilter,
    applyFilter,
    onSelectFilter,
    selectedFilters,
    data,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <View
        style={checkDevice() ? styles.imageModelMain1 : styles.imageModelMain}>
        <View style={[styles.subImageView, checkDevice() && styles.tabModal]}>
          <TouchableWithoutFeedback onPress={onClose}>
            <View style={styles.modalCloseBtnContainer}>
              <TouchableOpacity
                onPress={onClose}
                style={styles.modalCloseButton}>
                <ImageIcon icon="close" size="x5l" />
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
          <View style={styles.filterListView}>
            <Label
              text={t('orderListing.filterTitle')}
              size="xxl"
              color="text"
              fontFamily="SemiBold"
              style={styles.titleStyle}
            />
            <SectionList
              sections={data}
              keyExtractor={(item, index) => item + index}
              renderItem={({item, section}) => (
                <TouchableOpacity
                  style={styles.fRow}
                  onPress={() => onSelectFilter(section.title, item)}>
                  <Radio
                    onPress={() => onSelectFilter(section.title, item)}
                    selected={selectedFilters[section.title]?.includes(item)}
                    fillColor="sunnyOrange7"
                    borderColor="sunnyOrange7"
                  />
                  <Spacer type="Horizontal" size="m" />
                  <Label
                    text={item}
                    size="mx"
                    color="grey"
                    fontFamily="Medium"
                  />
                </TouchableOpacity>
              )}
              renderSectionHeader={({section: {title}}) => (
                <View style={styles.sectionView}>
                  <Label
                    text={title}
                    size="xl"
                    color="text"
                    fontFamily="SemiBold"
                    textTransform="capitalize"
                  />
                </View>
              )}
              ItemSeparatorComponent={() => <Spacer size="l" />}
            />
          </View>
          <View style={styles.btnView}>
            <Button
              text={t('buttons.clearFilter')}
              style={[styles.btnStyle, checkDevice() && styles.cleatBtn]}
              onPress={clearFilter}
              labelSize="mx"
              type="secondary"
              labelColor="whiteColor"
              radius="sx"
            />
            <Spacer size={checkDevice() ? 'x3l' : 'xms'} type="Horizontal" />
            <Button
              text={t('buttons.apply')}
              onPress={applyFilter}
              labelSize="mx"
              labelColor="whiteColor"
              radius="sx"
              withGradient
              gradientColors={[colors.coral, colors.persimmon]}
              selfAlign="stretch"
              style={[styles.btnStyle, checkDevice() && styles.applyBtn]}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default OrderFilter;

import { Sizes } from 'common';
import { StyleSheet } from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    acceptView: {
      flexDirection: 'row',
      alignSelf: 'center',
      alignItems: 'center',
    },
    securePaymentView: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignSelf: 'center',
      alignItems: 'center',
      paddingBottom: Sizes.xl,
    },
    paymentSubView: {
      alignSelf: 'center',
      alignItems: 'center',
      paddingHorizontal: Sizes.m,
    },
    easyReturnView: {
      alignSelf: 'center',
      alignItems: 'center',
      paddingHorizontal: Sizes.m,
    },
    paymentOption: {
      width: Sizes.ex2l
    }
  });

export default styles;

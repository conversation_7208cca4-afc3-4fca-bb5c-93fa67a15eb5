import React from 'react';
import {TouchableOpacity, View} from 'react-native';

import useStyle from './style';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Label, Separator, Spacer, Tag} from 'components/atoms';
import FastImage from 'react-native-fast-image';
import getImageUrl, { productDummyImage } from 'utils/imageUrlHelper';
import {Button} from 'components/molecules';

import {addToCart} from 'app-redux-store/slice/appSlice';
import {useDispatch} from 'react-redux';

type Props = {
  item: Partial<ProductData>;
  onPress?: () => void;
  onCartPress?: () => void;
  onPressWishlist?: () => void;
  onPressShare?: () => void;
  freeProducts?: FreeProducts[];
  deleteWishListItem?: () => void;

  moveWishListItem?: () => void;
  onPressMoreButton?: () => void;
};

const WishListCard = ({
  item,
  onPress,
  freeProducts = [],
  onPressMoreButton,
}: Props) => {
  const {colors} = useTheme();
  const styles = useStyle(colors);
  const dispatch = useDispatch();

  const addToCartButton = async products => {
    dispatch(addToCart([{data: {quantity: 1, sku: products?.sku}}]));
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <View style={styles.innerContainer}>
        <View style={styles.imageSection}>
          <Spacer size="s" />

          <FastImage
            resizeMode="contain"
            style={styles.image}
            onError={e => {
              e.target.uri = productDummyImage
            }}
            source={{
              uri: getImageUrl(item?.thumbnail_url),
            }}
          />
        </View>
        <Spacer size="sx" />
        <View style={styles.detailSection}>
          <Spacer size="xxl" />
          <View style={styles.productDetailRatingRow}>
            <View style={styles.rowCentered}>
              <ImageIcon icon="coin" size="m" />
              <Spacer size="s" type="Horizontal" />
              <Label
                text={item?.reward_point_product || '0'}
                size="m"
                weight="500"
                color="orange"
              />
              <Spacer size="s" type="Horizontal" />
              <Separator Vertical color="grey2" />
              <Spacer size="s" type="Horizontal" />
              <Tag
                style={styles.tagView}
                label={(item?.rating_count === 'null'
                  ? 0
                  : Number(item?.rating_count)
                ).toFixed(1)}
                icon="starIcon"
                color="green2"
              />
              <Spacer size="xm" type="Horizontal" />
              <Label
                weight="400"
                size="m"
                color="grey"
                text={!!item?.rating_count ? `(${item?.rating_count})` : '(0)'}
              />
            </View>
            <Button
              tintColor="textLight"
              style={styles.moreButton}
              onPress={onPressMoreButton}
              ghost
              iconCenter="moreIcon"
            />
          </View>
          <Spacer size="sx" />
          <View style={styles.PH8}>
            <Label
              text={item?.name}
              color="text"
              size="m"
              weight="500"
              numberOfLines={2}
              ellipsizeMode={'tail'}
            />
          </View>

          {!item?.msrp ? (
            <View style={styles.pricingRow}>
              {item.price?.regularPrice?.amount?.value !==
                item?.price?.minimalPrice?.amount?.value &&
              item.price?.regularPrice?.amount?.value > 0 ? (
                <>
                  <Label
                    weight="500"
                    size="m"
                    color="grey3"
                    textDecorationLine="line-through"
                    text={
                      item.price?.regularPrice.amount.currency_symbol +
                      '' +
                      item.price?.regularPrice?.amount?.value
                    }
                  />
                  <Spacer size="s" type="Horizontal" />
                </>
              ) : null}
              <Label
                color="text"
                weight="600"
                size="m"
                text={
                  item?.price?.minimalPrice?.amount?.currency_symbol +
                  '' +
                  item?.price?.minimalPrice?.amount?.value
                }
              />
            </View>
          ) : null}
          <View style={styles.freeTag}>
            {freeProducts?.some(
              e => e?.product_sku === item?.sku || e?.parent_sku === item?.sku,
            ) ? (
              <Label
                color="green2"
                weight="500"
                size="m"
                text={'Enjoy Free Product'}
              />
            ) : null}
          </View>
          <View style={styles.deliveryOfferRow}>
            <>
              <Button
                type="Secondary"
                onPress={() => {
                  addToCartButton(item);
                }}
                text="Add to Cart"
                borderColor="categoryTitle"
                style={styles.cartButton}
                labelSize="l"
              />
            </>
          </View>
        </View>
        <Spacer size="sx" />
      </View>
    </TouchableOpacity>
  );
};

export default WishListCard;

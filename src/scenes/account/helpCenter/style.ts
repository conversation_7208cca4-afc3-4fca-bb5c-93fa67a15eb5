import {Sizes} from 'common';
import {StyleSheet} from 'react-native';

const styles = (colors: Theme['colors']) =>
  StyleSheet.create({
    flex: {
      flex: Sizes.x,
    },
    container: {
      flex: Sizes.x,
      paddingHorizontal: Sizes.m,
      backgroundColor: colors.grey7,
    },
    container1: {
      height: Sizes.exl, // Limit the height for the view
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
    },
    contactView: {
      padding: Sizes.xm,
      flexDirection: 'column',
      backgroundColor: colors.whiteColor,
      borderRadius: Sizes.m,
    },
    contactOptions: {
      flexDirection: 'row',
      width: '100%',
      padding: Sizes.m,
    },
    textWrapper: {
      position: 'absolute',
    },
    talkToUs: {
      alignItems: 'center',
      padding: Sizes.xm,
      flex: Sizes.x,
      flexDirection: 'row',
      borderRadius: Sizes.xm,
      shadowColor: colors.tabInActive,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,

      elevation: Sizes.x,
      backgroundColor: colors.whiteColor,
    },
    talkToUsImageView: {
      width: Sizes.xx4l,
      height: Sizes.xx4l,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.lightPink,
      borderRadius: Sizes.x6l + Sizes.x,
    },
    chatWithUsImageView: {
      width: Sizes.xx4l,
      height: Sizes.xx4l,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.paleGreen,
      borderRadius: Sizes.x6l + Sizes.x,
    },
    chatWithUs: {
      alignItems: 'center',
      padding: Sizes.xm,
      flexDirection: 'row',
      flex: Sizes.x,
      borderRadius: Sizes.xm,
      shadowColor: colors.tabInActive,
      shadowOffset: {
        width: 0,
        height: Sizes.xs,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      backgroundColor: colors.whiteColor,
      elevation: Sizes.x,
    },
    feqMainView: {
      flex: Sizes.x,
      paddingVertical: Sizes.xms,
    },
    feqContainer: {
      flex: Sizes.x,
      width: '100%',
      flexDirection: 'row',
      backgroundColor: colors.whiteColor,
      color: colors.text2,
      borderRadius: Sizes.xm,
      justifyContent: 'space-between',
      fontSize: Sizes.m,
      marginBottom: Sizes.s,
    },

    gradientLabelView: {
      marginVertical: Sizes.x,
      backgroundColor: colors.grey7,
      alignItems: 'center',
      justifyContent: 'center',
      flex: Sizes.x,
    },
    navigationOption: {
      flexDirection: 'row',
      backgroundColor: colors.whiteColor,
      justifyContent: 'space-between',
      alignItems: 'center',
      borderRadius: Sizes.m,
      padding: Sizes.xm,
    },
    iconView: {
      height: Sizes.x9l,
      width: Sizes.x9l,
      paddingHorizontal: Sizes.sx,
      borderRadius: Sizes.xm,
      alignItems: 'center',
      borderColor: colors.placeholderColor,
      borderWidth: Sizes.x,
    },
    imageCoupon: {
      height: Sizes.s + Sizes.ex,
      width: '100%',
      borderRadius: Sizes.m,
    },
    giftImage: {
      height: Sizes.x8l,
      width: Sizes.x8l,
    },
    navigationTextMainView: {
      flex: Sizes.xs + Sizes.x,
    },
    navigationTextView: {
      flexDirection: 'column',
    },
    linearGradientView: {
      flex: Sizes.x,
      padding: Sizes.xms,
    },
  });

export default styles;

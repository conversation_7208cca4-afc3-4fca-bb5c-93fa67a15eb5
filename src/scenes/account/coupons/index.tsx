import React, {useCallback, useEffect, useState} from 'react';
import {
  SafeAreaView,
  View,
  ScrollView,
  SectionList,
  TouchableOpacity,
  BackHandler,
} from 'react-native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Header} from 'components/molecules';
import stylesWithOutColor from './style';
import {RouteProp, useTheme} from '@react-navigation/native';
import {t} from 'i18next';
import {RootStackParamsList} from 'routes';
import {
  Label,
  Separator,
  Spacer,
  WithBackground,
  EmptyView,
} from 'components/atoms';
import {useDispatch, useSelector} from 'react-redux';
import {getCouponList} from 'services/cart';
import {CardListLoader} from 'skeletonLoader';
import Clipboard from '@react-native-community/clipboard';
import {showSuccessMessage} from 'utils/show_messages';
import ErrorHandler from 'utils/ErrorHandler';
import Icons from 'common/icons';
import {useMemo} from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamsList>;
  route: RouteProp<RootStackParamsList, 'CouponsScene'>;
};

const CouponsScene = ({navigation, route}: Props) => {
  const TAG = 'CouponsScreen';
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const [couponList, setCouponList] = useState([]);
  const dispatch = useDispatch();
  const {cartId} = useSelector((state: RootState) => state.app);
  const [loading, setLoading] = useState(false);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    getCouponListData();
  }, []);

  const getCouponListData = useCallback(async () => {
    setLoading(true);
    const {data, status} = await getCouponList(cartId);
    if (status) {
      const groupedData = data?.data?.reduce((acc, coupon) => {
        const expiryDate = coupon.expiry_date;
        if (!acc[expiryDate]) {
          acc[expiryDate] = [];
        }
        acc[expiryDate].push(coupon);
        return acc;
      }, {});
      const result = Object.keys(groupedData).map(date => {
        return {
          title: `Valid till ${new Date(date).toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })}`,
          data: groupedData[date],
        };
      });
      setCouponList(result);
    }
    setLoading(false);
  }, [cartId]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = async () => {
    navigation.goBack();
    if (route.params?.goBack) {
      route.params?.goBack();
    }
    return true;
  };

  const copyToClipboard = (code: string) => {
    Clipboard.setString(code);
    showSuccessMessage(t('PDP.CopiedSuccessfully'));
  };

  const handleNavigation = () => {
    navigation.navigate('CategoryDetail', {categoryId: 2566});
  }
  const couponRenderItem = useCallback(({item}: {item: couponItem}) => {
    return (
      <WithBackground
        image="couponValid"
        imageStyle={styles.couponValidView}
        style={styles.couponValidSubView}>
        <TouchableOpacity
          style={styles.couponCardContentContainer}
          onPress={() => copyToClipboard(item?.coupon_code)}>
          <View style={styles.cardSection}>
            {/* <Spacer size="xl" />
            <Label
              text={t('cart.couponHere')}
              color="background"
              size="mx"
              fontFamily="SemiBold"
            /> */}
            {/* <Spacer size="xm" /> */}
            <Label
              text={item?.coupon_code}
              color="background"
              size="xxxl"
              fontFamily="Bold"
            />
            <Spacer size="xms" />
            <Label
              text={item?.description}
              color="background"
              size="mx"
              fontFamily="Regular"
              textTransform="capitalize"
              style={styles.discountLabelView}
            />
          </View>
          <View>
            <Label
              text={t('otherText.expireDate')}
              color="background"
              size="xms"
              align="center"
              fontFamily="Medium"
            />
            <Label
              text={item.expiry_date}
              color="background"
              size="xms"
              align="center"
              fontFamily="Medium"
            />
          </View>
          <Spacer size="sx" type="Horizontal" />
        </TouchableOpacity>
      </WithBackground>
    );
  }, []);

  const couponRenderHeader = useCallback(
    ({section: {title}}: {section: {title: string}}) => {
      return (
        <View style={styles.couponRenderView}>
          <View style={styles.flexView}>
            <Separator thickness="xs" color="smoothGrey" />
          </View>
          <Spacer size="l" type="Horizontal" />
          <Label
            size="m"
            color="grey"
            text={title}
            fontFamily="Regular"
            textTransform="capitalize"
          />
          <Spacer size="l" type="Horizontal" />
          <View style={styles.flexView}>
            <Separator thickness="xs" color="smoothGrey" />
          </View>
        </View>
      );
    },
    [],
  );

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <ErrorHandler componentName={`${TAG} Header`} onErrorComponent={<View />}>
        <Header
          navigation={navigation}
          backButton
          searchIcon={true}
          bagIcon={true}
          text={t('profile.coupons')}
          onPressNavigation={() => {
            navigation.goBack();
            if (route.params?.goBack) {
              route.params?.goBack();
            }
          }}
        />
      </ErrorHandler>
      {loading ? (
        <CardListLoader />
      ) : couponList?.length > 0 ? (
        <ScrollView style={styles.maincontainer}>
          <Label
            text={t('cart.couponTitle')}
            fontFamily="SemiBold"
            color="text"
            size="m"
            align="center"
          />
          <View style={styles.flexView}>
            <ErrorHandler
              componentName={`${TAG} CouponList`}
              onErrorComponent={<View />}>
              <SectionList
                keyExtractor={(_, index) => index.toString()}
                sections={couponList}
                renderSectionHeader={couponRenderHeader}
                renderItem={couponRenderItem}
                showsVerticalScrollIndicator={false}
                ItemSeparatorComponent={() => <Spacer size="l" />}
              />
            </ErrorHandler>
          </View>
          <Spacer size="l" />
          <Label
            text={t('cart.couponEnd')}
            size="m"
            color="grey"
            fontFamily="Medium"
            textTransform="capitalize"
          />
          <Spacer size="xl" />
        </ScrollView>
      ) : (
        <EmptyView
          text={t('buttons.exploreNow')}
          title={t('profile.couponEmptyTxt')}
          imageName={Icons.noCouponAvailableGif}
          bgImg={Icons.couponBg}
          onPress={handleNavigation}
        />
      )}
    </SafeAreaView>
  );
};

export default CouponsScene;

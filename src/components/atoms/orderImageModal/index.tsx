import React, {useMemo} from 'react';
import {
  TouchableOpacity,
  View,
  FlatList,
  TouchableWithoutFeedback,
  SafeAreaView,
} from 'react-native';
import {useTheme} from '@react-navigation/native';
import {ImageIcon, Label, Spacer} from 'components/atoms';
import stylesWithOutColor from './style';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image';
import Icons from 'common/icons';
import {orderStatusColor} from 'utils/utils';
import getImageUrl from 'utils/imageUrlHelper';
import Modal from 'react-native-modal';
import {getProductDetail} from 'services/productDetail';
import {setLoading} from 'app-redux-store/slice/appSlice';
import {useDispatch} from 'react-redux';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamsList} from 'routes';
import {urlResolver} from 'services/home';
import ErrorHandler from 'utils/ErrorHandler';

type Props = {
  visible?: boolean;
  onClose: () => void;
  data?: OrderItems[];
  item?: OrderItems;
  totalPrice?: number;
  totalEarnedCoin?: number;
  type?: string;
  spentPoint: number;
  navigation?: NativeStackNavigationProp<RootStackParamsList>;
};

const OrderImageModal = (props: Props) => {
  const {
    visible,
    onClose,
    item,
    totalEarnedCoin,
    totalPrice,
    data,
    type,
    spentPoint,
    navigation,
  } = props;
  const {colors} = useTheme();
  const styles = useMemo(() => stylesWithOutColor(colors), [colors]);
  const dispatch = useDispatch();
  const TAG = 'OrderImageModal';

  const navigationAction = (productId, image) => {
    navigation.navigate('ProductDetail', {
      productId: productId,
      ProductItems: {
        media: {
          mobile_image: image,
        },
      },
    });
  };

  const onNavigate = async (renderItems: Product) => {
    const parentId = renderItems?.parent_id;
    const image = renderItems?.thumbnail;
    if (parentId) {
      onClose();
      navigationAction(parentId, image);
    } else {
      dispatch(setLoading(true));
      const {status, data} = await urlResolver(renderItems?.url_key + '.html');
      if (status && data?.status !== 404) {
        onClose();
        navigationAction(data?.id, image);
        dispatch(setLoading(false));
      } else {
        const {status, data} = await getProductDetail(renderItems?.product_id);
        dispatch(setLoading(false));
        if (status && data?.statusCode !== 404) {
          onClose();
          navigationAction(renderItems?.product_id, image);
        } else {
          onClose();
          const prevRoutes = navigation?.getState().routes;
          navigation.reset({
            index: 0,
            routes: [
              ...prevRoutes.slice(0, prevRoutes.length - 1),
              {
                name: 'ItemNotFound',
              },
            ],
          });
        }
      }
    }
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={75}
      animationOutTiming={75}
      backdropOpacity={0.01}
      style={styles.modalStyle}>
      <SafeAreaView style={styles.flexOne}>
        <View style={styles.imageModelMain}>
          <View style={styles.subImageView}>
            <TouchableWithoutFeedback onPress={onClose}>
              <View style={styles.modalCloseBtnContainer}>
                <TouchableOpacity
                  onPress={() => onClose()}
                  style={styles.modalCloseButton}>
                  <ImageIcon icon="close" size="x6l" />
                </TouchableOpacity>
              </View>
            </TouchableWithoutFeedback>
            <ErrorHandler
              componentName={`${TAG} OrderImageModal`}
              onErrorComponent={<View />}>
              <FlatList
                data={data}
                bounces={false}
                renderItem={({item: renderItems, index}) => {
                  const free = renderItems?.is_free_product;
                  const selectType = type !== 'orderDetails' ? true : false;
                  const imageUrl =
                    type === 'orderDetails'
                      ? renderItems?.image
                      : renderItems?.thumbnail;
                  const qty =
                    type === 'orderDetails'
                      ? renderItems?.qty_ordered
                      : renderItems?.ordered_qty;
                  const btnClick = [
                    'Plus Membership Plan-II',
                    'Plus Membership Plan-I',
                  ].includes(renderItems?.name?.trim());
                  const statusObj = orderStatusColor(
                    item?.data?.status?.label || renderItems?.status,
                    colors,
                  );
                  return (
                    <TouchableOpacity
                      key={index}
                      style={styles.itemListMain}
                      activeOpacity={1}
                      onPress={() => {
                        if (!btnClick) {
                          onNavigate(renderItems);
                        }
                      }}>
                      <View style={styles.subItemList}>
                        <ImageIcon
                          resizeMode="contain"
                          style={styles.subImageSize}
                          sourceType="url"
                          source={getImageUrl(imageUrl, 'product')}
                        />
                      </View>
                      <View style={styles.flexOne}>
                        <View style={styles.fRow}>
                          <Label
                            color="text"
                            text={renderItems?.name}
                            fontFamily="Medium"
                            size="m"
                            numberOfLines={2}
                            style={styles.flexOne}
                          />
                          {/* {selectType && (
                            <View style={styles.orderView}>
                              {item?.data?.can_return ? (
                                <TouchableOpacity>
                                  <Label
                                    color="text"
                                    text={t('orderTrack.return')}
                                    size="m"
                                    fontFamily="SemiBold"
                                  />
                                </TouchableOpacity>
                              ) : (
                                <View />
                                // <TouchableOpacity disabled={true}>
                                //   <Label
                                //     color="text"
                                //     text={t('buttons.cancel')}
                                //     size="m"
                                //     fontFamily="SemiBold"
                                //   />
                                // </TouchableOpacity>
                              )}
                            </View>
                          )} */}
                          {free && (
                            <View style={styles.free}>
                              <Label
                                color="crusoeGreen"
                                text={t('cart.free')}
                                size="m"
                                fontFamily="Medium"
                              />
                            </View>
                          )}
                        </View>
                        <Spacer size="xs" />
                        <View style={styles.centerItem}>
                          <Label
                            color="text"
                            text={`${t('cart.quantity')} ${parseInt(qty)} `}
                            fontFamily="Medium"
                            size="m"
                            numberOfLines={2}
                            style={styles.flexOne}
                          />
                          <Label
                            color="text"
                            text={t(
                              free
                                ? 'orderReturn.amount'
                                : 'orderListing.orderAmount',
                            )}
                            fontFamily="Medium"
                            size="m">
                            <Label
                              color="text"
                              text={`${free ? t('PDP.rupee') : ''}${parseFloat(
                                renderItems?.row_total
                                  ? renderItems?.row_total
                                  : renderItems?.price * qty,
                              )?.toFixed(2)}`}
                              fontFamily="Medium"
                              size="m"
                              textDecorationLine={
                                free ? 'line-through' : 'none'
                              }
                            />
                          </Label>
                        </View>
                        <Spacer size="xs" />
                        <View style={styles.centerItem}>
                          {/* <View
                            style={[
                              styles.statusView,
                              {
                                backgroundColor: statusObj?.bgColor,
                              },
                            ]}>
                            <Label
                              color={statusObj?.color}
                              text={
                                statusObj?.status
                                  ? statusObj?.status
                                  : item?.data?.status?.label
                              }
                              fontFamily="Medium"
                              size="mx"
                            />
                          </View> */}
                          <View style={styles.flexOne} />
                          {renderItems?.reward_earned_coins > 0 && (
                            <View style={styles.itemStyle}>
                              <FastImage
                                resizeMode="contain"
                                style={styles.coinImage}
                                source={Icons?.coin}
                              />
                              <Spacer size="sx" type="Horizontal" />
                              <Label
                                color="text"
                                text={`${t('orderListing.earnedCoins')} - ${
                                  renderItems?.reward_earned_coins *
                                  renderItems?.ordered_qty
                                }`}
                                fontFamily="Medium"
                                size="m"
                              />
                            </View>
                          )}
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                }}
                ListFooterComponent={() => (
                  <>
                    {item?.data?.items?.length > 1 ? (
                      <>
                        <View style={styles.bottomView}>
                          <Label
                            color="text"
                            text={`${t(
                              'orderListing.orderAmount',
                            )} ${parseFloat(totalPrice)?.toFixed(2)}`}
                            fontFamily="Medium"
                            size="mx"
                          />
                          {item?.data?.order_summary?.total_savings > 0 && (
                            <Label
                              color="green2"
                              text={`${t(
                                'PDP.rupee',
                              )}${item?.data?.order_summary?.total_savings?.toFixed(
                                2,
                              )}`}
                              fontFamily="Medium"
                              size="mx"
                            />
                          )}
                          <View style={styles.earnView}>
                            {totalEarnedCoin > 0 && (
                              <>
                                <FastImage
                                  resizeMode="contain"
                                  style={styles.coinImage}
                                  source={Icons.coin}
                                />
                                <Spacer size="sx" type="Horizontal" />
                                <Label
                                  color="text2"
                                  text={`${t(
                                    'orderListing.earned',
                                  )} - ${totalEarnedCoin} `}
                                  fontFamily="Medium"
                                  size="mx"
                                />
                                <Spacer size="xs" type="Horizontal" />
                              </>
                            )}
                            {spentPoint > 0 && (
                              <>
                                <FastImage
                                  resizeMode="contain"
                                  style={styles.coinImage}
                                  source={Icons.coin}
                                />
                                <Spacer size="sx" type="Horizontal" />
                                <Label
                                  color="text2"
                                  text={`${t(
                                    'myRewords.spent',
                                  )} - ${spentPoint}`}
                                  fontFamily="Medium"
                                  size="mx"
                                />
                              </>
                            )}
                          </View>
                        </View>
                      </>
                    ) : null}
                  </>
                )}
              />
            </ErrorHandler>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default React.memo(OrderImageModal);
